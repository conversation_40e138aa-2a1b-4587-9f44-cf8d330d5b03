import React, { useState, useCallback, useEffect } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
} from 'reactflow';
import 'reactflow/dist/style.css';

import CustomNode from './components/CustomNode';
import { parseMermaidToReactFlow } from './utils/mermaidToReactFlow';

const nodeTypes = {
  custom: CustomNode,
};

// Demo initial nodes with test statuses
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'custom',
    position: { x: 250, y: 0 },
    data: { 
      label: 'Prompt Generator',
      testStatus: 'pass',
      subNodes: ['generatePrompt()', 'validateTokens()', 'applyConstraints()']
    }
  },
  {
    id: '2',
    type: 'custom',
    position: { x: 100, y: 100 },
    data: { 
      label: 'Test Case Generator',
      testStatus: 'fail',
      subNodes: ['createTestCategories()', 'generateSyntheticData()', 'validateTests()']
    }
  },
  {
    id: '3',
    type: 'custom',
    position: { x: 400, y: 100 },
    data: { 
      label: 'Requirements Doc',
      testStatus: 'pending',
      subNodes: ['parseRequirements()', 'extractMetrics()', 'buildWorkflow()']
    }
  },
  {
    id: '4',
    type: 'custom',
    position: { x: 250, y: 200 },
    data: { 
      label: 'Test Executor',
      testStatus: 'none',
      subNodes: ['runTests()', 'collectResults()', 'gradeOutputs()']
    }
  },
];

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', type: 'smoothstep' },
  { id: 'e1-3', source: '1', target: '3', type: 'smoothstep' },
  { id: 'e2-4', source: '2', target: '4', type: 'smoothstep' },
  { id: 'e3-4', source: '3', target: '4', type: 'smoothstep' },
];

function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [mermaidInput, setMermaidInput] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  const onConnect = useCallback(
    (params: Edge | Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const importMermaid = async (mermaidCode?: string) => {
    const code = mermaidCode || mermaidInput;
    if (!code) return;
    
    try {
      const { nodes: newNodes, edges: newEdges } = await parseMermaidToReactFlow(code);
      setNodes(newNodes);
      setEdges(newEdges);
    } catch (error) {
      console.error('Error parsing mermaid:', error);
    }
  };

  const toggleNodeExpansion = (nodeId: string) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              expanded: !node.data.expanded,
            },
          };
        }
        return node;
      })
    );
  };

  const toggleTestStatus = (nodeId: string) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const statuses: Array<'none' | 'pass' | 'fail' | 'pending'> = ['none', 'pass', 'fail', 'pending'];
          const currentIndex = statuses.indexOf(node.data.testStatus || 'none');
          const nextStatus = statuses[(currentIndex + 1) % statuses.length];
          
          return {
            ...node,
            data: {
              ...node.data,
              testStatus: nextStatus,
            },
          };
        }
        return node;
      })
    );
  };

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (event.shiftKey) {
      toggleTestStatus(node.id);
    } else {
      toggleNodeExpansion(node.id);
    }
  }, []);

  const toggleAllNodes = () => {
    setIsExpanded(!isExpanded);
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: {
          ...node.data,
          expanded: !isExpanded,
        },
      }))
    );
  };

  const resetDemo = () => {
    setNodes(initialNodes);
    setEdges(initialEdges);
  };

  return (
    <div style={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '10px', background: '#f3f4f6', borderBottom: '1px solid #e5e7eb' }}>
        <h1 style={{ margin: '0 0 10px 0', fontSize: '24px' }}>Auto-promptgen Flow Visualizer</h1>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <button onClick={resetDemo} style={{ padding: '8px 16px' }}>
            Reset Demo
          </button>
          <button onClick={toggleAllNodes} style={{ padding: '8px 16px' }}>
            {isExpanded ? 'Collapse All' : 'Expand All'}
          </button>
          <span style={{ marginLeft: '20px', fontSize: '14px', color: '#6b7280' }}>
            Click node to expand/collapse | Shift+Click to toggle test status
          </span>
        </div>
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#6b7280' }}>
          Test Status: 🟢 Pass | 🔴 Fail | 🟡 Pending | ⚪ None
        </div>
      </div>
      
      <div style={{ flex: 1 }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          fitView
        >
          <Controls />
          <MiniMap />
          <Background variant={"dots" as any} gap={12} size={1} />
        </ReactFlow>
      </div>
    </div>
  );
}

export default App;