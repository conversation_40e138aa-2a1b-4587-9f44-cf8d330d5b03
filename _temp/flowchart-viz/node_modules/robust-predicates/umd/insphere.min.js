!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).predicates={})}(this,(function(t){"use strict";const n=11102230246251565e-32,e=134217729,r=(3+8*n)*n;function o(t,n,e,r,o){let a,s,f,u,i=n[0],c=r[0],h=0,b=0;c>i==c>-i?(a=i,i=n[++h]):(a=c,c=r[++b]);let l=0;if(h<t&&b<e)for(c>i==c>-i?(s=i+a,f=a-(s-i),i=n[++h]):(s=c+a,f=a-(s-c),c=r[++b]),a=s,0!==f&&(o[l++]=f);h<t&&b<e;)c>i==c>-i?(s=a+i,u=s-a,f=a-(s-u)+(i-u),i=n[++h]):(s=a+c,u=s-a,f=a-(s-u)+(c-u),c=r[++b]),a=s,0!==f&&(o[l++]=f);for(;h<t;)s=a+i,u=s-a,f=a-(s-u)+(i-u),i=n[++h],a=s,0!==f&&(o[l++]=f);for(;b<e;)s=a+c,u=s-a,f=a-(s-u)+(c-u),c=r[++b],a=s,0!==f&&(o[l++]=f);return 0===a&&0!==l||(o[l++]=a),l}function a(t,n,e,r,a,s,f,u){return o(o(t,n,e,r,f),f,a,s,u)}function s(t,n,r,o){let a,s,f,u,i,c,h,b,l,M,d;h=e*r,M=h-(h-r),d=r-M;let p=n[0];a=p*r,h=e*p,b=h-(h-p),l=p-b,f=l*d-(a-b*M-l*M-b*d);let y=0;0!==f&&(o[y++]=f);for(let x=1;x<t;x++)p=n[x],u=p*r,h=e*p,b=h-(h-p),l=p-b,i=l*d-(u-b*M-l*M-b*d),s=a+i,c=s-a,f=a-(s-c)+(i-c),0!==f&&(o[y++]=f),a=u+s,f=s-(a-u),0!==f&&(o[y++]=f);return 0===a&&0!==y||(o[y++]=a),y}function f(t,n){for(let e=0;e<t;e++)n[e]=-n[e];return t}function u(t){return new Float64Array(t)}const i=5551115123125792e-31,c=8751425667295619e-46,h=u(4),b=u(4),l=u(4),M=u(4),d=u(4),p=u(4),y=u(4),x=u(4),g=u(4),m=u(4),T=u(24),j=u(24),w=u(24),A=u(24),F=u(24),k=u(24),q=u(24),v=u(24),z=u(24),B=u(24),C=u(1152),D=u(1152),E=u(1152),G=u(1152),H=u(1152),I=u(2304),J=u(2304),K=u(3456),L=u(5760),N=u(8),O=u(8),P=u(8),Q=u(16),R=u(24),S=u(48),U=u(48),V=u(96),W=u(192),X=u(384),Y=u(384),Z=u(384),$=u(768);function _(t,n,e,r,o,f,u){return a(s(4,t,r,N),N,s(4,n,o,O),O,s(4,e,f,P),P,Q,u)}function tt(t,n,e,r,u,i,c,h,b,l,M,d){const p=o(o(t,n,e,r,S),S,f(o(u,i,c,h,U),U),U,V);return a(s(s(p,V,b,W),W,b,X),X,s(s(p,V,l,W),W,l,Y),Y,s(s(p,V,M,W),W,M,Z),Z,$,d)}const nt=u(96),et=u(96),rt=u(96),ot=u(1152);function at(t,n,e,r,o,f,u,i,c,h){const b=_(t,n,e,r,o,f,R);return a(s(s(b,R,u,S),S,u,nt),nt,s(s(b,R,i,S),S,i,et),et,s(s(b,R,c,S),S,c,rt),rt,W,h)}function st(t,n,s,u,N,O,P,Q,R,S,U,V,W,X,Y,Z){let $,nt,et,rt,st,ft,ut,it,ct,ht,bt,lt,Mt,dt,pt,yt,xt,gt,mt,Tt,jt,wt,At,Ft,kt,qt,vt,zt,Bt,Ct,Dt;const Et=t-W,Gt=u-W,Ht=P-W,It=S-W,Jt=n-X,Kt=N-X,Lt=Q-X,Nt=U-X,Ot=s-Y,Pt=O-Y,Qt=R-Y,Rt=V-Y;zt=Et*Kt,Tt=e*Et,jt=Tt-(Tt-Et),wt=Et-jt,Tt=e*Kt,At=Tt-(Tt-Kt),Ft=Kt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=Gt*Jt,Tt=e*Gt,jt=Tt-(Tt-Gt),wt=Gt-jt,Tt=e*Jt,At=Tt-(Tt-Jt),Ft=Jt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,h[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,h[1]=vt-(kt+mt)+(mt-Ct),$=qt+kt,mt=$-qt,h[2]=qt-($-mt)+(kt-mt),h[3]=$,zt=Gt*Lt,Tt=e*Gt,jt=Tt-(Tt-Gt),wt=Gt-jt,Tt=e*Lt,At=Tt-(Tt-Lt),Ft=Lt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=Ht*Kt,Tt=e*Ht,jt=Tt-(Tt-Ht),wt=Ht-jt,Tt=e*Kt,At=Tt-(Tt-Kt),Ft=Kt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,b[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,b[1]=vt-(kt+mt)+(mt-Ct),nt=qt+kt,mt=nt-qt,b[2]=qt-(nt-mt)+(kt-mt),b[3]=nt,zt=Ht*Nt,Tt=e*Ht,jt=Tt-(Tt-Ht),wt=Ht-jt,Tt=e*Nt,At=Tt-(Tt-Nt),Ft=Nt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=It*Lt,Tt=e*It,jt=Tt-(Tt-It),wt=It-jt,Tt=e*Lt,At=Tt-(Tt-Lt),Ft=Lt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,l[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,l[1]=vt-(kt+mt)+(mt-Ct),et=qt+kt,mt=et-qt,l[2]=qt-(et-mt)+(kt-mt),l[3]=et,zt=It*Jt,Tt=e*It,jt=Tt-(Tt-It),wt=It-jt,Tt=e*Jt,At=Tt-(Tt-Jt),Ft=Jt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=Et*Nt,Tt=e*Et,jt=Tt-(Tt-Et),wt=Et-jt,Tt=e*Nt,At=Tt-(Tt-Nt),Ft=Nt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,g[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,g[1]=vt-(kt+mt)+(mt-Ct),rt=qt+kt,mt=rt-qt,g[2]=qt-(rt-mt)+(kt-mt),g[3]=rt,zt=Et*Lt,Tt=e*Et,jt=Tt-(Tt-Et),wt=Et-jt,Tt=e*Lt,At=Tt-(Tt-Lt),Ft=Lt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=Ht*Jt,Tt=e*Ht,jt=Tt-(Tt-Ht),wt=Ht-jt,Tt=e*Jt,At=Tt-(Tt-Jt),Ft=Jt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,p[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,p[1]=vt-(kt+mt)+(mt-Ct),st=qt+kt,mt=st-qt,p[2]=qt-(st-mt)+(kt-mt),p[3]=st,zt=Gt*Nt,Tt=e*Gt,jt=Tt-(Tt-Gt),wt=Gt-jt,Tt=e*Nt,At=Tt-(Tt-Nt),Ft=Nt-At,Bt=wt*Ft-(zt-jt*At-wt*At-jt*Ft),Ct=It*Kt,Tt=e*It,jt=Tt-(Tt-It),wt=It-jt,Tt=e*Kt,At=Tt-(Tt-Kt),Ft=Kt-At,Dt=wt*Ft-(Ct-jt*At-wt*At-jt*Ft),kt=Bt-Dt,mt=Bt-kt,y[0]=Bt-(kt+mt)+(mt-Dt),qt=zt+kt,mt=qt-zt,vt=zt-(qt-mt)+(kt-mt),kt=vt-Ct,mt=vt-kt,y[1]=vt-(kt+mt)+(mt-Ct),ft=qt+kt,mt=ft-qt,y[2]=qt-(ft-mt)+(kt-mt),y[3]=ft;let St=function(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}(o(o(f(at(b,l,y,Rt,Pt,-Qt,Et,Jt,Ot,C),C),C,at(l,g,p,Ot,Qt,Rt,Gt,Kt,Pt,D),D,I),I,o(f(at(g,h,y,Pt,Rt,Ot,Ht,Lt,Qt,E),E),E,at(h,b,p,Qt,Ot,-Pt,It,Nt,Rt,G),G,J),J,ot),ot),Ut=i*Z;if(St>=Ut||-St>=Ut)return St;if(mt=t-Et,ut=t-(Et+mt)+(mt-W),mt=n-Jt,bt=n-(Jt+mt)+(mt-X),mt=s-Ot,pt=s-(Ot+mt)+(mt-Y),mt=u-Gt,it=u-(Gt+mt)+(mt-W),mt=N-Kt,lt=N-(Kt+mt)+(mt-X),mt=O-Pt,yt=O-(Pt+mt)+(mt-Y),mt=P-Ht,ct=P-(Ht+mt)+(mt-W),mt=Q-Lt,Mt=Q-(Lt+mt)+(mt-X),mt=R-Qt,xt=R-(Qt+mt)+(mt-Y),mt=S-It,ht=S-(It+mt)+(mt-W),mt=U-Nt,dt=U-(Nt+mt)+(mt-X),mt=V-Rt,gt=V-(Rt+mt)+(mt-Y),0===ut&&0===bt&&0===pt&&0===it&&0===lt&&0===yt&&0===ct&&0===Mt&&0===xt&&0===ht&&0===dt&&0===gt)return St;Ut=c*Z+r*Math.abs(St);const Vt=Et*lt+Kt*ut-(Jt*it+Gt*bt),Wt=Gt*Mt+Lt*it-(Kt*ct+Ht*lt),Xt=Ht*dt+Nt*ct-(Lt*ht+It*Mt),Yt=It*bt+Jt*ht-(Nt*ut+Et*dt),Zt=Et*Mt+Lt*ut-(Jt*ct+Ht*bt),$t=Gt*dt+Nt*it-(Kt*ht+It*lt);return St+=(Gt*Gt+Kt*Kt+Pt*Pt)*(Qt*Yt+Rt*Zt+Ot*Xt+(xt*rt+gt*st+pt*et))+(It*It+Nt*Nt+Rt*Rt)*(Ot*Wt-Pt*Zt+Qt*Vt+(pt*nt-yt*st+xt*$))-((Et*Et+Jt*Jt+Ot*Ot)*(Pt*Xt-Qt*$t+Rt*Wt+(yt*et-xt*ft+gt*nt))+(Ht*Ht+Lt*Lt+Qt*Qt)*(Rt*Vt+Ot*$t+Pt*Yt+(gt*$+pt*ft+yt*rt)))+2*((Gt*it+Kt*lt+Pt*yt)*(Qt*rt+Rt*st+Ot*et)+(It*ht+Nt*dt+Rt*gt)*(Ot*nt-Pt*st+Qt*$)-((Et*ut+Jt*bt+Ot*pt)*(Pt*et-Qt*ft+Rt*nt)+(Ht*ct+Lt*Mt+Qt*xt)*(Rt*$+Ot*ft+Pt*rt))),St>=Ut||-St>=Ut?St:function(t,n,r,o,s,f,u,i,c,N,O,P,Q,R,S){let U,V,W,X,Y,Z,$,nt,et,rt,ot,at,st,ft;rt=t*s,V=e*t,W=V-(V-t),X=t-W,V=e*s,Y=V-(V-s),Z=s-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=o*n,V=e*o,W=V-(V-o),X=o-W,V=e*n,Y=V-(V-n),Z=n-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,h[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,h[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,h[2]=nt-(ft-U)+($-U),h[3]=ft,rt=o*i,V=e*o,W=V-(V-o),X=o-W,V=e*i,Y=V-(V-i),Z=i-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=u*s,V=e*u,W=V-(V-u),X=u-W,V=e*s,Y=V-(V-s),Z=s-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,b[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,b[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,b[2]=nt-(ft-U)+($-U),b[3]=ft,rt=u*O,V=e*u,W=V-(V-u),X=u-W,V=e*O,Y=V-(V-O),Z=O-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=N*i,V=e*N,W=V-(V-N),X=N-W,V=e*i,Y=V-(V-i),Z=i-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,l[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,l[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,l[2]=nt-(ft-U)+($-U),l[3]=ft,rt=N*R,V=e*N,W=V-(V-N),X=N-W,V=e*R,Y=V-(V-R),Z=R-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=Q*O,V=e*Q,W=V-(V-Q),X=Q-W,V=e*O,Y=V-(V-O),Z=O-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,M[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,M[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,M[2]=nt-(ft-U)+($-U),M[3]=ft,rt=Q*n,V=e*Q,W=V-(V-Q),X=Q-W,V=e*n,Y=V-(V-n),Z=n-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=t*R,V=e*t,W=V-(V-t),X=t-W,V=e*R,Y=V-(V-R),Z=R-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,d[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,d[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,d[2]=nt-(ft-U)+($-U),d[3]=ft,rt=t*i,V=e*t,W=V-(V-t),X=t-W,V=e*i,Y=V-(V-i),Z=i-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=u*n,V=e*u,W=V-(V-u),X=u-W,V=e*n,Y=V-(V-n),Z=n-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,p[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,p[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,p[2]=nt-(ft-U)+($-U),p[3]=ft,rt=o*O,V=e*o,W=V-(V-o),X=o-W,V=e*O,Y=V-(V-O),Z=O-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=N*s,V=e*N,W=V-(V-N),X=N-W,V=e*s,Y=V-(V-s),Z=s-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,y[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,y[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,y[2]=nt-(ft-U)+($-U),y[3]=ft,rt=u*R,V=e*u,W=V-(V-u),X=u-W,V=e*R,Y=V-(V-R),Z=R-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=Q*i,V=e*Q,W=V-(V-Q),X=Q-W,V=e*i,Y=V-(V-i),Z=i-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,x[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,x[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,x[2]=nt-(ft-U)+($-U),x[3]=ft,rt=N*n,V=e*N,W=V-(V-N),X=N-W,V=e*n,Y=V-(V-n),Z=n-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=t*O,V=e*t,W=V-(V-t),X=t-W,V=e*O,Y=V-(V-O),Z=O-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,g[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,g[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,g[2]=nt-(ft-U)+($-U),g[3]=ft,rt=Q*s,V=e*Q,W=V-(V-Q),X=Q-W,V=e*s,Y=V-(V-s),Z=s-Y,ot=X*Z-(rt-W*Y-X*Y-W*Z),at=o*R,V=e*o,W=V-(V-o),X=o-W,V=e*R,Y=V-(V-R),Z=R-Y,st=X*Z-(at-W*Y-X*Y-W*Z),$=ot-st,U=ot-$,m[0]=ot-($+U)+(U-st),nt=rt+$,U=nt-rt,et=rt-(nt-U)+($-U),$=et-at,U=et-$,m[1]=et-($+U)+(U-at),ft=nt+$,U=ft-nt,m[2]=nt-(ft-U)+($-U),m[3]=ft;const ut=_(h,b,p,c,r,-f,T),it=_(b,l,y,P,f,-c,j),ct=_(l,M,x,S,c,-P,w),ht=_(M,d,g,r,P,-S,A),bt=_(d,h,m,f,S,-r,F),lt=_(h,y,g,P,r,f,k),Mt=_(b,x,m,S,f,c,q),dt=_(l,g,p,r,c,P,v),pt=_(M,m,y,f,P,S,z),yt=_(d,p,x,c,S,r,B),xt=a(tt(ct,w,Mt,q,pt,z,it,j,t,n,r,C),C,tt(ht,A,dt,v,yt,B,ct,w,o,s,f,D),D,a(tt(bt,F,pt,z,lt,k,ht,A,u,i,c,E),E,tt(ut,T,yt,B,Mt,q,bt,F,N,O,P,G),G,tt(it,j,lt,k,dt,v,ut,T,Q,R,S,H),H,J,K),K,I,L);return L[xt-1]}(t,n,s,u,N,O,P,Q,R,S,U,V,W,X,Y)}t.insphere=function(t,n,e,r,o,a,s,f,u,i,c,h,b,l,M){const d=t-b,p=r-b,y=s-b,x=i-b,g=n-l,m=o-l,T=f-l,j=c-l,w=e-M,A=a-M,F=u-M,k=h-M,q=d*m,v=p*g,z=q-v,B=p*T,C=y*m,D=B-C,E=y*j,G=x*T,H=E-G,I=x*g,J=d*j,K=I-J,L=d*T,N=y*g,O=L-N,P=p*j,Q=x*m,R=P-Q,S=d*d+g*g+w*w,U=p*p+m*m+A*A,V=y*y+T*T+F*F,W=x*x+j*j+k*k,X=V*(k*z+w*R+A*K)-W*(w*D-A*O+F*z)+(S*(A*H-F*R+k*D)-U*(F*K+k*O+w*H)),Y=Math.abs(w),Z=Math.abs(A),$=Math.abs(F),_=Math.abs(k),tt=Math.abs(q)+Math.abs(v),nt=Math.abs(B)+Math.abs(C),et=Math.abs(E)+Math.abs(G),rt=Math.abs(I)+Math.abs(J),ot=Math.abs(L)+Math.abs(N),at=Math.abs(P)+Math.abs(Q),ft=(et*Z+at*$+nt*_)*S+(rt*$+ot*_+et*Y)*U+(tt*_+at*Y+rt*Z)*V+(nt*Y+ot*Z+tt*$)*W,ut=17763568394002532e-31*ft;return X>ut||-X>ut?X:-st(t,n,e,r,o,a,s,f,u,i,c,h,b,l,M,ft)},t.inspherefast=function(t,n,e,r,o,a,s,f,u,i,c,h,b,l,M){const d=t-b,p=r-b,y=s-b,x=i-b,g=n-l,m=o-l,T=f-l,j=c-l,w=e-M,A=a-M,F=u-M,k=h-M,q=d*m-p*g,v=p*T-y*m,z=y*j-x*T,B=x*g-d*j,C=d*T-y*g,D=p*j-x*m;return(y*y+T*T+F*F)*(k*q+w*D+A*B)-(x*x+j*j+k*k)*(w*v-A*C+F*q)+((d*d+g*g+w*w)*(A*z-F*D+k*v)-(p*p+m*m+A*A)*(F*B+k*C+w*z))}}));
