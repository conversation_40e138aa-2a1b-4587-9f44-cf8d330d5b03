{"name": "@apideck/better-ajv-errors", "description": "Human-friendly JSON Schema validation for APIs", "version": "0.3.6", "author": "Apideck <<EMAIL>> (https://apideck.com/)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/apideck-libraries/better-ajv-errors"}, "bugs": {"url": "https://github.com/apideck-libraries/better-ajv-errors/issues"}, "contributors": ["<PERSON> <<EMAIL>>"], "main": "dist/index.js", "module": "dist/better-ajv-errors.esm.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test", "lint": "tsdx lint", "prepare": "tsdx build", "size": "size-limit", "release": "np --no-publish && npm publish --access public --registry https://registry.npmjs.org", "analyze": "size-limit --why"}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 120, "singleQuote": true, "trailingComma": "es5"}, "size-limit": [{"path": "dist/better-ajv-errors.cjs.production.min.js", "limit": "2 KB"}, {"path": "dist/better-ajv-errors.esm.js", "limit": "2.5 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^7.0.8", "ajv": "^8.11.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.1", "np": "^7.6.1", "size-limit": "^7.0.8", "tsdx": "^0.14.1", "tslib": "^2.4.0", "typescript": "^4.7.2"}, "peerDependencies": {"ajv": ">=8"}, "dependencies": {"json-schema": "^0.4.0", "jsonpointer": "^5.0.0", "leven": "^3.1.0"}, "resolutions": {"prettier": "^2.3.0"}, "keywords": ["apideck", "ajv", "json", "schema", "json-schema", "errors", "human"]}