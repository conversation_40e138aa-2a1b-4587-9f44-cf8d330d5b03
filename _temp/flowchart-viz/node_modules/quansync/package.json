{"name": "quansync", "type": "module", "version": "0.2.10", "description": "Create sync/async APIs with usable logic", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "三咲智子 <PERSON>g", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "individual", "url": "https://github.com/sponsors/antfu"}, {"type": "individual", "url": "https://github.com/sponsors/sxzz"}], "homepage": "https://github.com/quansync-dev/quansync#readme", "repository": {"type": "git", "url": "git+https://github.com/quansync-dev/quansync.git"}, "bugs": "https://github.com/quansync-dev/quansync/issues", "keywords": ["async", "sync", "generator"], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./macro": {"import": "./dist/macro.mjs", "require": "./dist/macro.cjs"}, "./types": {"import": "./dist/types.mjs", "require": "./dist/types.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@types/node": "^22.13.10", "bumpp": "^10.1.0", "eslint": "^9.22.0", "gensync": "1.0.0-beta.2", "lint-staged": "^15.5.0", "mitata": "^1.0.34", "simple-git-hooks": "^2.11.1", "tsx": "^4.19.3", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vite": "^6.2.2", "vitest": "^3.0.9"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "start": "tsx src/index.ts", "benchmark": "node scripts/benchmark.js", "test": "vitest", "typecheck": "tsc --noEmit"}}