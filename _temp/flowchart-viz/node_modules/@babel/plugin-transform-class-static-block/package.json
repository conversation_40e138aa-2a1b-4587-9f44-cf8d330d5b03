{"name": "@babel/plugin-transform-class-static-block", "version": "7.27.1", "description": "Transform class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-class-static-block"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}