#!/bin/bash

FILE=test/input.txt

rm -f -- $FILE
for i in {1..10000}; do printf '%09X\n' $RANDOM >> $FILE; done

function test()
{
   if [[ $1 -eq 0 ]]
   then
      echo -e "\x1B[1;32m✓ $2\x1B[0m"
   else
      echo -e "\x1B[1;31m✗ $2\x1B[0m"
   fi
}

test/encoding-sync; test $? "encoding-sync applies the specified encodings"
test/encoding-async; test $? "encoding-async applies the specified encodings"
[ "$(test/wc-async $FILE)" = "100000" ]; test $? "wc-async reads an entire file"
[ "$(test/wc-sync $FILE)" = "100000" ]; test $? "wc-sync reads an entire file"
[ "$(test/wc-async < $FILE)" = "100000" ]; test $? "wc-async reads an entire file from stdin"
[ "$(test/wc-sync < $FILE)" = "100000" ]; test $? "wc-sync reads an entire file from stdin"
[ "$(cat $FILE | test/wc-async)" = "100000" ]; test $? "wc-async reads an entire file from a pipe"
[ "$(cat $FILE | test/wc-sync)" = "100000" ]; test $? "wc-sync reads an entire file from a pipe"
[ "$(test/cat-async $FILE | wc -c | tr -d ' ')" = "100000" ]; test $? "cat-async reads an entire file and writes it to a pipe"
[ "$(test/cat-sync $FILE | wc -c | tr -d ' ')" = "100000" ]; test $? "cat-sync reads an entire file and writes it to a pipe"
[ "$(test/cat-async $FILE | test/wc-async)" = "100000" ]; test $? "cat-async reads an entire file and writes it to a pipe to wc-async "
[ "$(test/cat-async $FILE | test/wc-sync)" = "100000" ]; test $? "cat-async reads an entire file and writes it to a pipe to wc-sync "
[ "$(test/cat-sync $FILE | test/wc-async)" = "100000" ]; test $? "cat-sync reads an entire file and writes it to a pipe to wc-async "
[ "$(test/cat-sync $FILE | test/wc-sync)" = "100000" ]; test $? "cat-sync reads an entire file and writes it to a pipe to wc-sync "
[ "$(test/cat-async < $FILE | wc -c | tr -d ' ')" = "100000" ]; test $? "cat-async reads an entire file from stdin and writes it to a pipe"
[ "$(test/cat-sync < $FILE | wc -c | tr -d ' ')" = "100000" ]; test $? "cat-sync reads an entire file from stdin and writes it to a pipe"
[ "$(test/cat-async < $FILE | test/wc-async)" = "100000" ]; test $? "cat-async reads an entire file from stdin and writes it to a pipe to wc-async"
[ "$(test/cat-async < $FILE | test/wc-sync)" = "100000" ]; test $? "cat-async reads an entire file from stdin and writes it to a pipe to wc-sync"
[ "$(test/cat-sync < $FILE | test/wc-async)" = "100000" ]; test $? "cat-sync reads an entire file from stdin and writes it to a pipe to wc-async"
[ "$(test/cat-sync < $FILE | test/wc-sync)" = "100000" ]; test $? "cat-sync reads an entire file from stdin and writes it to a pipe to wc-sync"
[ "$(cat $FILE | test/cat-async | test/wc-async)" = "100000" ]; test $? "cat-async reads an entire file from a pipe and writes it to a pipe to wc-async"
[ "$(cat $FILE | test/cat-async | test/wc-sync)" = "100000" ]; test $? "cat-async reads an entire file from a pipe and writes it to a pipe to wc-sync"
[ "$(cat $FILE | test/cat-sync | test/wc-async)" = "100000" ]; test $? "cat-sync reads an entire file from a pipe and writes it to a pipe to wc-async"
[ "$(cat $FILE | test/cat-sync | test/wc-sync)" = "100000" ]; test $? "cat-sync reads an entire file from a pipe and writes it to a pipe to wc-sync"
[ "$(cat $FILE | test/cat-async | head -n 100 | test/wc-async)" = "1000" ]; test $? "cat-async reads an entire file from a pipe and writes it to a pipe to head to wc-async"
[ "$(cat $FILE | test/cat-async | head -n 100 | test/wc-sync)" = "1000" ]; test $? "cat-async reads an entire file from a pipe and writes it to a pipe to head to wc-sync"
[ "$(cat $FILE | test/cat-sync | head -n 100 | test/wc-async)" = "1000" ]; test $? "cat-sync reads an entire file from a pipe and writes it to a pipe to head to wc-async"
[ "$(cat $FILE | test/cat-sync | head -n 100 | test/wc-sync)" = "1000" ]; test $? "cat-sync reads an entire file from a pipe and writes it to a pipe to head to wc-sync"
[ "$(cat $FILE 2> /dev/null | head -n 100 | test/cat-async | test/wc-async)" = "1000" ]; test $? "cat-async reads the head of a file from a pipe and writes it to wc-async"
[ "$(cat $FILE 2> /dev/null | head -n 100 | test/cat-async | test/wc-sync)" = "1000" ]; test $? "cat-async reads the head of a file from a pipe and writes it to wc-sync"
[ "$(cat $FILE 2> /dev/null | head -n 100 | test/cat-sync | test/wc-async)" = "1000" ]; test $? "cat-sync reads the head of a file from a pipe and writes it to wc-async"
[ "$(cat $FILE 2> /dev/null | head -n 100 | test/cat-sync | test/wc-sync)" = "1000" ]; test $? "cat-sync reads the head of a file from a pipe and writes it to wc-sync"
[ "$(test/write-async test/write.out && cat test/write.out)" = "Hello, world!" ]; test $? "write-async writes an entire file"
[ "$(test/write-sync test/write.out && cat test/write.out)" = "Hello, world!" ]; test $? "write-sync writes an entire file"

rm -f -- $FILE test/write.out test/encoding-sync.out test/encoding-async.out
