[{"/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/index.tsx": "1", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/App.tsx": "3", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/utils/mermaidToReactFlow.ts": "4", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/components/CustomNode.tsx": "5"}, {"size": 556, "mtime": 1752047562721, "results": "6", "hashOfConfig": "7"}, {"size": 425, "mtime": 1752047562723, "results": "8", "hashOfConfig": "7"}, {"size": 4699, "mtime": 1752047630074, "results": "9", "hashOfConfig": "7"}, {"size": 4535, "mtime": 1752047562734, "results": "10", "hashOfConfig": "7"}, {"size": 1737, "mtime": 1752047562719, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "64hdyn", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/index.tsx", [], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/App.tsx", ["27", "28"], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/utils/mermaidToReactFlow.ts", ["29", "30"], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/components/CustomNode.tsx", [], [], {"ruleId": "31", "severity": 1, "message": "32", "line": 37, "column": 6, "nodeType": "33", "endLine": 37, "endColumn": 8, "suggestions": "34"}, {"ruleId": "31", "severity": 1, "message": "35", "line": 101, "column": 6, "nodeType": "33", "endLine": 101, "endColumn": 8, "suggestions": "36"}, {"ruleId": "37", "severity": 1, "message": "38", "line": 4, "column": 11, "nodeType": "39", "messageId": "40", "endLine": 4, "endColumn": 22}, {"ruleId": "37", "severity": 1, "message": "41", "line": 11, "column": 11, "nodeType": "39", "messageId": "40", "endLine": 11, "endColumn": 22}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'importMermaid'. Either include it or remove the dependency array.", "ArrayExpression", ["42"], "React Hook useCallback has missing dependencies: 'toggleNodeExpansion' and 'toggleTestStatus'. Either include them or remove the dependency array.", ["43"], "@typescript-eslint/no-unused-vars", "'MermaidNode' is defined but never used.", "Identifier", "unusedVar", "'MermaidEdge' is defined but never used.", {"desc": "44", "fix": "45"}, {"desc": "46", "fix": "47"}, "Update the dependencies array to be: [importMermaid]", {"range": "48", "text": "49"}, "Update the dependencies array to be: [toggleNodeExpansion, toggleTestStatus]", {"range": "50", "text": "51"}, [979, 981], "[importMermaid]", [2645, 2647], "[toggleNodeExpansion, toggleTestStatus]"]