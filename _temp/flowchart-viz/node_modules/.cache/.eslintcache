[{"/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/index.tsx": "1", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/App.tsx": "3", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/utils/mermaidToReactFlow.ts": "4", "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/components/CustomNode.tsx": "5"}, {"size": 556, "mtime": 1752047562721, "results": "6", "hashOfConfig": "7"}, {"size": 425, "mtime": 1752047562723, "results": "8", "hashOfConfig": "7"}, {"size": 5507, "mtime": 1752047922862, "results": "9", "hashOfConfig": "7"}, {"size": 4535, "mtime": 1752047562734, "results": "10", "hashOfConfig": "7"}, {"size": 1737, "mtime": 1752047562719, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "64hdyn", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/index.tsx", [], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/App.tsx", ["27", "28", "29", "30"], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/utils/mermaidToReactFlow.ts", ["31", "32"], [], "/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/src/components/CustomNode.tsx", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 1, "column": 40, "nodeType": "35", "messageId": "36", "endLine": 1, "endColumn": 49}, {"ruleId": "33", "severity": 1, "message": "37", "line": 76, "column": 24, "nodeType": "35", "messageId": "36", "endLine": 76, "endColumn": 39}, {"ruleId": "33", "severity": 1, "message": "38", "line": 84, "column": 9, "nodeType": "35", "messageId": "36", "endLine": 84, "endColumn": 22}, {"ruleId": "39", "severity": 1, "message": "40", "line": 141, "column": 6, "nodeType": "41", "endLine": 141, "endColumn": 8, "suggestions": "42"}, {"ruleId": "33", "severity": 1, "message": "43", "line": 4, "column": 11, "nodeType": "35", "messageId": "36", "endLine": 4, "endColumn": 22}, {"ruleId": "33", "severity": 1, "message": "44", "line": 11, "column": 11, "nodeType": "35", "messageId": "36", "endLine": 11, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setMermaidInput' is assigned a value but never used.", "'importMermaid' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'toggleNodeExpansion' and 'toggleTestStatus'. Either include them or remove the dependency array.", "ArrayExpression", ["45"], "'MermaidNode' is defined but never used.", "'MermaidEdge' is defined but never used.", {"desc": "46", "fix": "47"}, "Update the dependencies array to be: [toggleNodeExpansion, toggleTestStatus]", {"range": "48", "text": "49"}, [3653, 3655], "[toggleNodeExpansion, toggleTestStatus]"]