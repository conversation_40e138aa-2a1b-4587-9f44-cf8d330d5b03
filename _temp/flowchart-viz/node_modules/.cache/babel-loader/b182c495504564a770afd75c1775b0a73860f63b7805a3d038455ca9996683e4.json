{"ast": null, "code": "import { clone, every, flatten, has, isArray, isEmpty, isObject, reduce, uniq, values } from \"lodash-es\";\nimport { AT_LEAST_ONE_IDX, AT_LEAST_ONE_SEP_IDX, BITS_FOR_METHOD_TYPE, BITS_FOR_OCCURRENCE_IDX, MANY_IDX, MANY_SEP_IDX, OPTION_IDX, OR_IDX } from \"../../grammar/keys.js\";\nimport { isRecognitionException, MismatchedTokenException, NotAllInputParsedException } from \"../../exceptions_public.js\";\nimport { PROD_TYPE } from \"../../grammar/lookahead.js\";\nimport { NextTerminalAfterAtLeastOneSepWalker, NextTerminalAfterAtLeastOneWalker, NextTerminalAfterManySepWalker, NextTerminalAfterManyWalker } from \"../../grammar/interpreter.js\";\nimport { DEFAULT_RULE_CONFIG } from \"../parser.js\";\nimport { IN_RULE_RECOVERY_EXCEPTION } from \"./recoverable.js\";\nimport { EOF } from \"../../../scan/tokens_public.js\";\nimport { augmentTokenTypes, isTokenType, tokenStructuredMatcher, tokenStructuredMatcherNoCategories } from \"../../../scan/tokens.js\";\n/**\n * This trait is responsible for the runtime parsing engine\n * Used by the official API (recognizer_api.ts)\n */\nexport class RecognizerEngine {\n  initRecognizerEngine(tokenVocabulary, config) {\n    this.className = this.constructor.name;\n    // TODO: would using an ES6 Map or plain object be faster (CST building scenario)\n    this.shortRuleNameToFull = {};\n    this.fullRuleNameToShort = {};\n    this.ruleShortNameIdx = 256;\n    this.tokenMatcher = tokenStructuredMatcherNoCategories;\n    this.subruleIdx = 0;\n    this.definedRulesNames = [];\n    this.tokensMap = {};\n    this.isBackTrackingStack = [];\n    this.RULE_STACK = [];\n    this.RULE_OCCURRENCE_STACK = [];\n    this.gastProductionsCache = {};\n    if (has(config, \"serializedGrammar\")) {\n      throw Error(\"The Parser's configuration can no longer contain a <serializedGrammar> property.\\n\" + \"\\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\\n\" + \"\\tFor Further details.\");\n    }\n    if (isArray(tokenVocabulary)) {\n      // This only checks for Token vocabularies provided as arrays.\n      // That is good enough because the main objective is to detect users of pre-V4.0 APIs\n      // rather than all edge cases of empty Token vocabularies.\n      if (isEmpty(tokenVocabulary)) {\n        throw Error(\"A Token Vocabulary cannot be empty.\\n\" + \"\\tNote that the first argument for the parser constructor\\n\" + \"\\tis no longer a Token vector (since v4.0).\");\n      }\n      if (typeof tokenVocabulary[0].startOffset === \"number\") {\n        throw Error(\"The Parser constructor no longer accepts a token vector as the first argument.\\n\" + \"\\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\\n\" + \"\\tFor Further details.\");\n      }\n    }\n    if (isArray(tokenVocabulary)) {\n      this.tokensMap = reduce(tokenVocabulary, (acc, tokType) => {\n        acc[tokType.name] = tokType;\n        return acc;\n      }, {});\n    } else if (has(tokenVocabulary, \"modes\") && every(flatten(values(tokenVocabulary.modes)), isTokenType)) {\n      const allTokenTypes = flatten(values(tokenVocabulary.modes));\n      const uniqueTokens = uniq(allTokenTypes);\n      this.tokensMap = reduce(uniqueTokens, (acc, tokType) => {\n        acc[tokType.name] = tokType;\n        return acc;\n      }, {});\n    } else if (isObject(tokenVocabulary)) {\n      this.tokensMap = clone(tokenVocabulary);\n    } else {\n      throw new Error(\"<tokensDictionary> argument must be An Array of Token constructors,\" + \" A dictionary of Token constructors or an IMultiModeLexerDefinition\");\n    }\n    // always add EOF to the tokenNames -> constructors map. it is useful to assure all the input has been\n    // parsed with a clear error message (\"expecting EOF but found ...\")\n    this.tokensMap[\"EOF\"] = EOF;\n    const allTokenTypes = has(tokenVocabulary, \"modes\") ? flatten(values(tokenVocabulary.modes)) : values(tokenVocabulary);\n    const noTokenCategoriesUsed = every(allTokenTypes, tokenConstructor => isEmpty(tokenConstructor.categoryMatches));\n    this.tokenMatcher = noTokenCategoriesUsed ? tokenStructuredMatcherNoCategories : tokenStructuredMatcher;\n    // Because ES2015+ syntax should be supported for creating Token classes\n    // We cannot assume that the Token classes were created using the \"extendToken\" utilities\n    // Therefore we must augment the Token classes both on Lexer initialization and on Parser initialization\n    augmentTokenTypes(values(this.tokensMap));\n  }\n  defineRule(ruleName, impl, config) {\n    if (this.selfAnalysisDone) {\n      throw Error(`Grammar rule <${ruleName}> may not be defined after the 'performSelfAnalysis' method has been called'\\n` + `Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);\n    }\n    const resyncEnabled = has(config, \"resyncEnabled\") ? config.resyncEnabled // assumes end user provides the correct config value/type\n    : DEFAULT_RULE_CONFIG.resyncEnabled;\n    const recoveryValueFunc = has(config, \"recoveryValueFunc\") ? config.recoveryValueFunc // assumes end user provides the correct config value/type\n    : DEFAULT_RULE_CONFIG.recoveryValueFunc;\n    // performance optimization: Use small integers as keys for the longer human readable \"full\" rule names.\n    // this greatly improves Map access time (as much as 8% for some performance benchmarks).\n    const shortName = this.ruleShortNameIdx << BITS_FOR_METHOD_TYPE + BITS_FOR_OCCURRENCE_IDX;\n    this.ruleShortNameIdx++;\n    this.shortRuleNameToFull[shortName] = ruleName;\n    this.fullRuleNameToShort[ruleName] = shortName;\n    let invokeRuleWithTry;\n    // Micro optimization, only check the condition **once** on rule definition\n    // instead of **every single** rule invocation.\n    if (this.outputCst === true) {\n      invokeRuleWithTry = function invokeRuleWithTry(...args) {\n        try {\n          this.ruleInvocationStateUpdate(shortName, ruleName, this.subruleIdx);\n          impl.apply(this, args);\n          const cst = this.CST_STACK[this.CST_STACK.length - 1];\n          this.cstPostRule(cst);\n          return cst;\n        } catch (e) {\n          return this.invokeRuleCatch(e, resyncEnabled, recoveryValueFunc);\n        } finally {\n          this.ruleFinallyStateUpdate();\n        }\n      };\n    } else {\n      invokeRuleWithTry = function invokeRuleWithTryCst(...args) {\n        try {\n          this.ruleInvocationStateUpdate(shortName, ruleName, this.subruleIdx);\n          return impl.apply(this, args);\n        } catch (e) {\n          return this.invokeRuleCatch(e, resyncEnabled, recoveryValueFunc);\n        } finally {\n          this.ruleFinallyStateUpdate();\n        }\n      };\n    }\n    const wrappedGrammarRule = Object.assign(invokeRuleWithTry, {\n      ruleName,\n      originalGrammarAction: impl\n    });\n    return wrappedGrammarRule;\n  }\n  invokeRuleCatch(e, resyncEnabledConfig, recoveryValueFunc) {\n    const isFirstInvokedRule = this.RULE_STACK.length === 1;\n    // note the reSync is always enabled for the first rule invocation, because we must always be able to\n    // reSync with EOF and just output some INVALID ParseTree\n    // during backtracking reSync recovery is disabled, otherwise we can't be certain the backtracking\n    // path is really the most valid one\n    const reSyncEnabled = resyncEnabledConfig && !this.isBackTracking() && this.recoveryEnabled;\n    if (isRecognitionException(e)) {\n      const recogError = e;\n      if (reSyncEnabled) {\n        const reSyncTokType = this.findReSyncTokenType();\n        if (this.isInCurrentRuleReSyncSet(reSyncTokType)) {\n          recogError.resyncedTokens = this.reSyncTo(reSyncTokType);\n          if (this.outputCst) {\n            const partialCstResult = this.CST_STACK[this.CST_STACK.length - 1];\n            partialCstResult.recoveredNode = true;\n            return partialCstResult;\n          } else {\n            return recoveryValueFunc(e);\n          }\n        } else {\n          if (this.outputCst) {\n            const partialCstResult = this.CST_STACK[this.CST_STACK.length - 1];\n            partialCstResult.recoveredNode = true;\n            recogError.partialCstResult = partialCstResult;\n          }\n          // to be handled Further up the call stack\n          throw recogError;\n        }\n      } else if (isFirstInvokedRule) {\n        // otherwise a Redundant input error will be created as well and we cannot guarantee that this is indeed the case\n        this.moveToTerminatedState();\n        // the parser should never throw one of its own errors outside its flow.\n        // even if error recovery is disabled\n        return recoveryValueFunc(e);\n      } else {\n        // to be recovered Further up the call stack\n        throw recogError;\n      }\n    } else {\n      // some other Error type which we don't know how to handle (for example a built in JavaScript Error)\n      throw e;\n    }\n  }\n  // Implementation of parsing DSL\n  optionInternal(actionORMethodDef, occurrence) {\n    const key = this.getKeyForAutomaticLookahead(OPTION_IDX, occurrence);\n    return this.optionInternalLogic(actionORMethodDef, occurrence, key);\n  }\n  optionInternalLogic(actionORMethodDef, occurrence, key) {\n    let lookAheadFunc = this.getLaFuncFromCache(key);\n    let action;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookAheadFunc;\n        lookAheadFunc = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n    if (lookAheadFunc.call(this) === true) {\n      return action.call(this);\n    }\n    return undefined;\n  }\n  atLeastOneInternal(prodOccurrence, actionORMethodDef) {\n    const laKey = this.getKeyForAutomaticLookahead(AT_LEAST_ONE_IDX, prodOccurrence);\n    return this.atLeastOneInternalLogic(prodOccurrence, actionORMethodDef, laKey);\n  }\n  atLeastOneInternalLogic(prodOccurrence, actionORMethodDef, key) {\n    let lookAheadFunc = this.getLaFuncFromCache(key);\n    let action;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookAheadFunc;\n        lookAheadFunc = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n    if (lookAheadFunc.call(this) === true) {\n      let notStuck = this.doSingleRepetition(action);\n      while (lookAheadFunc.call(this) === true && notStuck === true) {\n        notStuck = this.doSingleRepetition(action);\n      }\n    } else {\n      throw this.raiseEarlyExitException(prodOccurrence, PROD_TYPE.REPETITION_MANDATORY, actionORMethodDef.ERR_MSG);\n    }\n    // note that while it may seem that this can cause an error because by using a recursive call to\n    // AT_LEAST_ONE we change the grammar to AT_LEAST_TWO, AT_LEAST_THREE ... , the possible recursive call\n    // from the tryInRepetitionRecovery(...) will only happen IFF there really are TWO/THREE/.... items.\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    this.attemptInRepetitionRecovery(this.atLeastOneInternal, [prodOccurrence, actionORMethodDef], lookAheadFunc, AT_LEAST_ONE_IDX, prodOccurrence, NextTerminalAfterAtLeastOneWalker);\n  }\n  atLeastOneSepFirstInternal(prodOccurrence, options) {\n    const laKey = this.getKeyForAutomaticLookahead(AT_LEAST_ONE_SEP_IDX, prodOccurrence);\n    this.atLeastOneSepFirstInternalLogic(prodOccurrence, options, laKey);\n  }\n  atLeastOneSepFirstInternalLogic(prodOccurrence, options, key) {\n    const action = options.DEF;\n    const separator = options.SEP;\n    const firstIterationLookaheadFunc = this.getLaFuncFromCache(key);\n    // 1st iteration\n    if (firstIterationLookaheadFunc.call(this) === true) {\n      action.call(this);\n      //  TODO: Optimization can move this function construction into \"attemptInRepetitionRecovery\"\n      //  because it is only needed in error recovery scenarios.\n      const separatorLookAheadFunc = () => {\n        return this.tokenMatcher(this.LA(1), separator);\n      };\n      // 2nd..nth iterations\n      while (this.tokenMatcher(this.LA(1), separator) === true) {\n        // note that this CONSUME will never enter recovery because\n        // the separatorLookAheadFunc checks that the separator really does exist.\n        this.CONSUME(separator);\n        // No need for checking infinite loop here due to consuming the separator.\n        action.call(this);\n      }\n      // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n      this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal, [prodOccurrence, separator, separatorLookAheadFunc, action, NextTerminalAfterAtLeastOneSepWalker], separatorLookAheadFunc, AT_LEAST_ONE_SEP_IDX, prodOccurrence, NextTerminalAfterAtLeastOneSepWalker);\n    } else {\n      throw this.raiseEarlyExitException(prodOccurrence, PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR, options.ERR_MSG);\n    }\n  }\n  manyInternal(prodOccurrence, actionORMethodDef) {\n    const laKey = this.getKeyForAutomaticLookahead(MANY_IDX, prodOccurrence);\n    return this.manyInternalLogic(prodOccurrence, actionORMethodDef, laKey);\n  }\n  manyInternalLogic(prodOccurrence, actionORMethodDef, key) {\n    let lookaheadFunction = this.getLaFuncFromCache(key);\n    let action;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookaheadFunction;\n        lookaheadFunction = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n    let notStuck = true;\n    while (lookaheadFunction.call(this) === true && notStuck === true) {\n      notStuck = this.doSingleRepetition(action);\n    }\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    this.attemptInRepetitionRecovery(this.manyInternal, [prodOccurrence, actionORMethodDef], lookaheadFunction, MANY_IDX, prodOccurrence, NextTerminalAfterManyWalker,\n    // The notStuck parameter is only relevant when \"attemptInRepetitionRecovery\"\n    // is invoked from manyInternal, in the MANY_SEP case and AT_LEAST_ONE[_SEP]\n    // An infinite loop cannot occur as:\n    // - Either the lookahead is guaranteed to consume something (Single Token Separator)\n    // - AT_LEAST_ONE by definition is guaranteed to consume something (or error out).\n    notStuck);\n  }\n  manySepFirstInternal(prodOccurrence, options) {\n    const laKey = this.getKeyForAutomaticLookahead(MANY_SEP_IDX, prodOccurrence);\n    this.manySepFirstInternalLogic(prodOccurrence, options, laKey);\n  }\n  manySepFirstInternalLogic(prodOccurrence, options, key) {\n    const action = options.DEF;\n    const separator = options.SEP;\n    const firstIterationLaFunc = this.getLaFuncFromCache(key);\n    // 1st iteration\n    if (firstIterationLaFunc.call(this) === true) {\n      action.call(this);\n      const separatorLookAheadFunc = () => {\n        return this.tokenMatcher(this.LA(1), separator);\n      };\n      // 2nd..nth iterations\n      while (this.tokenMatcher(this.LA(1), separator) === true) {\n        // note that this CONSUME will never enter recovery because\n        // the separatorLookAheadFunc checks that the separator really does exist.\n        this.CONSUME(separator);\n        // No need for checking infinite loop here due to consuming the separator.\n        action.call(this);\n      }\n      // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n      this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal, [prodOccurrence, separator, separatorLookAheadFunc, action, NextTerminalAfterManySepWalker], separatorLookAheadFunc, MANY_SEP_IDX, prodOccurrence, NextTerminalAfterManySepWalker);\n    }\n  }\n  repetitionSepSecondInternal(prodOccurrence, separator, separatorLookAheadFunc, action, nextTerminalAfterWalker) {\n    while (separatorLookAheadFunc()) {\n      // note that this CONSUME will never enter recovery because\n      // the separatorLookAheadFunc checks that the separator really does exist.\n      this.CONSUME(separator);\n      action.call(this);\n    }\n    // we can only arrive to this function after an error\n    // has occurred (hence the name 'second') so the following\n    // IF will always be entered, its possible to remove it...\n    // however it is kept to avoid confusion and be consistent.\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    /* istanbul ignore else */\n    this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal, [prodOccurrence, separator, separatorLookAheadFunc, action, nextTerminalAfterWalker], separatorLookAheadFunc, AT_LEAST_ONE_SEP_IDX, prodOccurrence, nextTerminalAfterWalker);\n  }\n  doSingleRepetition(action) {\n    const beforeIteration = this.getLexerPosition();\n    action.call(this);\n    const afterIteration = this.getLexerPosition();\n    // This boolean will indicate if this repetition progressed\n    // or if we are \"stuck\" (potential infinite loop in the repetition).\n    return afterIteration > beforeIteration;\n  }\n  orInternal(altsOrOpts, occurrence) {\n    const laKey = this.getKeyForAutomaticLookahead(OR_IDX, occurrence);\n    const alts = isArray(altsOrOpts) ? altsOrOpts : altsOrOpts.DEF;\n    const laFunc = this.getLaFuncFromCache(laKey);\n    const altIdxToTake = laFunc.call(this, alts);\n    if (altIdxToTake !== undefined) {\n      const chosenAlternative = alts[altIdxToTake];\n      return chosenAlternative.ALT.call(this);\n    }\n    this.raiseNoAltException(occurrence, altsOrOpts.ERR_MSG);\n  }\n  ruleFinallyStateUpdate() {\n    this.RULE_STACK.pop();\n    this.RULE_OCCURRENCE_STACK.pop();\n    // NOOP when cst is disabled\n    this.cstFinallyStateUpdate();\n    if (this.RULE_STACK.length === 0 && this.isAtEndOfInput() === false) {\n      const firstRedundantTok = this.LA(1);\n      const errMsg = this.errorMessageProvider.buildNotAllInputParsedMessage({\n        firstRedundant: firstRedundantTok,\n        ruleName: this.getCurrRuleFullName()\n      });\n      this.SAVE_ERROR(new NotAllInputParsedException(errMsg, firstRedundantTok));\n    }\n  }\n  subruleInternal(ruleToCall, idx, options) {\n    let ruleResult;\n    try {\n      const args = options !== undefined ? options.ARGS : undefined;\n      this.subruleIdx = idx;\n      ruleResult = ruleToCall.apply(this, args);\n      this.cstPostNonTerminal(ruleResult, options !== undefined && options.LABEL !== undefined ? options.LABEL : ruleToCall.ruleName);\n      return ruleResult;\n    } catch (e) {\n      throw this.subruleInternalError(e, options, ruleToCall.ruleName);\n    }\n  }\n  subruleInternalError(e, options, ruleName) {\n    if (isRecognitionException(e) && e.partialCstResult !== undefined) {\n      this.cstPostNonTerminal(e.partialCstResult, options !== undefined && options.LABEL !== undefined ? options.LABEL : ruleName);\n      delete e.partialCstResult;\n    }\n    throw e;\n  }\n  consumeInternal(tokType, idx, options) {\n    let consumedToken;\n    try {\n      const nextToken = this.LA(1);\n      if (this.tokenMatcher(nextToken, tokType) === true) {\n        this.consumeToken();\n        consumedToken = nextToken;\n      } else {\n        this.consumeInternalError(tokType, nextToken, options);\n      }\n    } catch (eFromConsumption) {\n      consumedToken = this.consumeInternalRecovery(tokType, idx, eFromConsumption);\n    }\n    this.cstPostTerminal(options !== undefined && options.LABEL !== undefined ? options.LABEL : tokType.name, consumedToken);\n    return consumedToken;\n  }\n  consumeInternalError(tokType, nextToken, options) {\n    let msg;\n    const previousToken = this.LA(0);\n    if (options !== undefined && options.ERR_MSG) {\n      msg = options.ERR_MSG;\n    } else {\n      msg = this.errorMessageProvider.buildMismatchTokenMessage({\n        expected: tokType,\n        actual: nextToken,\n        previous: previousToken,\n        ruleName: this.getCurrRuleFullName()\n      });\n    }\n    throw this.SAVE_ERROR(new MismatchedTokenException(msg, nextToken, previousToken));\n  }\n  consumeInternalRecovery(tokType, idx, eFromConsumption) {\n    // no recovery allowed during backtracking, otherwise backtracking may recover invalid syntax and accept it\n    // but the original syntax could have been parsed successfully without any backtracking + recovery\n    if (this.recoveryEnabled &&\n    // TODO: more robust checking of the exception type. Perhaps Typescript extending expressions?\n    eFromConsumption.name === \"MismatchedTokenException\" && !this.isBackTracking()) {\n      const follows = this.getFollowsForInRuleRecovery(tokType, idx);\n      try {\n        return this.tryInRuleRecovery(tokType, follows);\n      } catch (eFromInRuleRecovery) {\n        if (eFromInRuleRecovery.name === IN_RULE_RECOVERY_EXCEPTION) {\n          // failed in RuleRecovery.\n          // throw the original error in order to trigger reSync error recovery\n          throw eFromConsumption;\n        } else {\n          throw eFromInRuleRecovery;\n        }\n      }\n    } else {\n      throw eFromConsumption;\n    }\n  }\n  saveRecogState() {\n    // errors is a getter which will clone the errors array\n    const savedErrors = this.errors;\n    const savedRuleStack = clone(this.RULE_STACK);\n    return {\n      errors: savedErrors,\n      lexerState: this.exportLexerState(),\n      RULE_STACK: savedRuleStack,\n      CST_STACK: this.CST_STACK\n    };\n  }\n  reloadRecogState(newState) {\n    this.errors = newState.errors;\n    this.importLexerState(newState.lexerState);\n    this.RULE_STACK = newState.RULE_STACK;\n  }\n  ruleInvocationStateUpdate(shortName, fullName, idxInCallingRule) {\n    this.RULE_OCCURRENCE_STACK.push(idxInCallingRule);\n    this.RULE_STACK.push(shortName);\n    // NOOP when cst is disabled\n    this.cstInvocationStateUpdate(fullName);\n  }\n  isBackTracking() {\n    return this.isBackTrackingStack.length !== 0;\n  }\n  getCurrRuleFullName() {\n    const shortName = this.getLastExplicitRuleShortName();\n    return this.shortRuleNameToFull[shortName];\n  }\n  shortRuleNameToFullName(shortName) {\n    return this.shortRuleNameToFull[shortName];\n  }\n  isAtEndOfInput() {\n    return this.tokenMatcher(this.LA(1), EOF);\n  }\n  reset() {\n    this.resetLexerState();\n    this.subruleIdx = 0;\n    this.isBackTrackingStack = [];\n    this.errors = [];\n    this.RULE_STACK = [];\n    // TODO: extract a specific reset for TreeBuilder trait\n    this.CST_STACK = [];\n    this.RULE_OCCURRENCE_STACK = [];\n  }\n}", "map": {"version": 3, "names": ["clone", "every", "flatten", "has", "isArray", "isEmpty", "isObject", "reduce", "uniq", "values", "AT_LEAST_ONE_IDX", "AT_LEAST_ONE_SEP_IDX", "BITS_FOR_METHOD_TYPE", "BITS_FOR_OCCURRENCE_IDX", "MANY_IDX", "MANY_SEP_IDX", "OPTION_IDX", "OR_IDX", "isRecognitionException", "MismatchedTokenException", "NotAllInputParsedException", "PROD_TYPE", "NextTerminalAfterAtLeastOneSepWalker", "NextTerminalAfterAtLeastOneWalker", "NextTerminalAfterManySepWalker", "NextTerminalAfterManyWalker", "DEFAULT_RULE_CONFIG", "IN_RULE_RECOVERY_EXCEPTION", "EOF", "augmentTokenTypes", "isTokenType", "tokenStructuredMatcher", "tokenStructuredMatcherNoCategories", "RecognizerEngine", "initRecognizerEngine", "tokenVocabulary", "config", "className", "constructor", "name", "shortRuleNameToFull", "fullRuleNameToShort", "ruleShortNameIdx", "tokenMatcher", "subruleIdx", "definedRulesNames", "tokensMap", "isBackTrackingStack", "RULE_STACK", "RULE_OCCURRENCE_STACK", "gastProductionsCache", "Error", "startOffset", "acc", "tokType", "modes", "allTokenTypes", "uniqueTokens", "noTokenCategoriesUsed", "tokenConstructor", "categoryMatches", "defineRule", "ruleName", "impl", "selfAnalysisDone", "resyncEnabled", "recoveryValueFunc", "shortName", "invokeRuleWithTry", "outputCst", "args", "ruleInvocationStateUpdate", "apply", "cst", "CST_STACK", "length", "cstPostRule", "e", "invokeRuleCatch", "ruleFinallyStateUpdate", "invokeRuleWithTryCst", "wrappedGrammarRule", "Object", "assign", "originalGrammarAction", "resyncEnabledConfig", "isFirstInvokedRule", "reSyncEnabled", "isBackTracking", "recoveryEnabled", "recogError", "reSyncTokType", "findReSyncTokenType", "isInCurrentRuleReSyncSet", "resyncedTokens", "reSyncTo", "partialCstResult", "recoveredNode", "moveToTerminatedState", "optionInternal", "actionORMethodDef", "occurrence", "key", "getKeyForAutomaticLookahead", "optionInternalLogic", "lookAheadFunc", "getLaFuncFromCache", "action", "DEF", "predicate", "GATE", "undefined", "orgLookaheadFunction", "call", "atLeastOneInternal", "prodOccurrence", "<PERSON><PERSON><PERSON>", "atLeastOneInternalLogic", "notStuck", "doSingleRepetition", "raiseEarlyExitException", "REPETITION_MANDATORY", "ERR_MSG", "attemptInRepetitionRecovery", "atLeastOneSepFirstInternal", "options", "atLeastOneSepFirstInternalLogic", "separator", "SEP", "firstIterationLookaheadFunc", "separatorLookAheadFunc", "LA", "CONSUME", "repetitionSepSecondInternal", "REPETITION_MANDATORY_WITH_SEPARATOR", "manyInternal", "manyInternalLogic", "lookaheadFunction", "manySepFirstInternal", "manySepFirstInternalLogic", "firstIterationLaFunc", "nextTerminalAfterWalker", "beforeIteration", "getLexerPosition", "afterIteration", "orInternal", "altsOrOpts", "alts", "laFunc", "altIdxToTake", "chosenAlternative", "ALT", "raiseNoAltException", "pop", "cstFinallyStateUpdate", "isAtEndOfInput", "firstRedundantTok", "errMsg", "errorMessageProvider", "buildNotAllInputParsedMessage", "firstRedundant", "getCurrRuleFullName", "SAVE_ERROR", "subruleInternal", "ruleToCall", "idx", "ruleResult", "ARGS", "cstPostNonTerminal", "LABEL", "subruleInternalError", "consumeInternal", "consumedToken", "nextToken", "consumeToken", "consumeInternalError", "eFromConsumption", "consumeInternalRecovery", "cstPostTerminal", "msg", "previousToken", "buildMismatchTokenMessage", "expected", "actual", "previous", "follows", "getFollowsForInRuleRecovery", "tryInRuleRecovery", "eFromInRuleRecovery", "saveRecogState", "savedErrors", "errors", "savedRuleStack", "lexerState", "exportLexerState", "reloadRecogState", "newState", "importLexerState", "fullName", "idxInCallingRule", "push", "cstInvocationStateUpdate", "getLastExplicitRuleShortName", "shortRuleNameToFullName", "reset", "resetLexerState"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/recognizer_engine.ts"], "sourcesContent": ["import {\n  AtLeastOneSepMethodOpts,\n  ConsumeMethodOpts,\n  DSLMethodOpts,\n  DSLMethodOptsWithErr,\n  GrammarAction,\n  IOrAlt,\n  IParserConfig,\n  IRuleConfig,\n  IToken,\n  ManySepMethodOpts,\n  OrMethodOpts,\n  ParserMethod,\n  SubruleMethodOpts,\n  TokenType,\n  TokenTypeDictionary,\n  TokenVocabulary,\n} from \"@chevrotain/types\";\nimport {\n  clone,\n  every,\n  flatten,\n  has,\n  isArray,\n  isEmpty,\n  isObject,\n  reduce,\n  uniq,\n  values,\n} from \"lodash-es\";\nimport {\n  AT_LEAST_ONE_IDX,\n  AT_LEAST_ONE_SEP_IDX,\n  BITS_FOR_METHOD_TYPE,\n  BITS_FOR_OCCURRENCE_IDX,\n  MANY_IDX,\n  MANY_SEP_IDX,\n  OPTION_IDX,\n  OR_IDX,\n} from \"../../grammar/keys.js\";\nimport {\n  isRecognitionException,\n  MismatchedTokenException,\n  NotAllInputParsedException,\n} from \"../../exceptions_public.js\";\nimport { PROD_TYPE } from \"../../grammar/lookahead.js\";\nimport {\n  AbstractNextTerminalAfterProductionWalker,\n  NextTerminalAfterAtLeastOneSepWalker,\n  NextTerminalAfterAtLeastOneWalker,\n  NextTerminalAfterManySepWalker,\n  NextTerminalAfterManyWalker,\n} from \"../../grammar/interpreter.js\";\nimport { DEFAULT_RULE_CONFIG, IParserState, TokenMatcher } from \"../parser.js\";\nimport { IN_RULE_RECOVERY_EXCEPTION } from \"./recoverable.js\";\nimport { EOF } from \"../../../scan/tokens_public.js\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport {\n  augmentTokenTypes,\n  isTokenType,\n  tokenStructuredMatcher,\n  tokenStructuredMatcherNoCategories,\n} from \"../../../scan/tokens.js\";\nimport { Rule } from \"@chevrotain/gast\";\nimport { ParserMethodInternal } from \"../types.js\";\n\n/**\n * This trait is responsible for the runtime parsing engine\n * Used by the official API (recognizer_api.ts)\n */\nexport class RecognizerEngine {\n  isBackTrackingStack: boolean[];\n  className: string;\n  RULE_STACK: number[];\n  RULE_OCCURRENCE_STACK: number[];\n  definedRulesNames: string[];\n  tokensMap: { [fqn: string]: TokenType };\n  gastProductionsCache: Record<string, Rule>;\n  shortRuleNameToFull: Record<string, string>;\n  fullRuleNameToShort: Record<string, number>;\n  // The shortName Index must be coded \"after\" the first 8bits to enable building unique lookahead keys\n  ruleShortNameIdx: number;\n  tokenMatcher: TokenMatcher;\n  subruleIdx: number;\n\n  initRecognizerEngine(\n    tokenVocabulary: TokenVocabulary,\n    config: IParserConfig,\n  ) {\n    this.className = this.constructor.name;\n    // TODO: would using an ES6 Map or plain object be faster (CST building scenario)\n    this.shortRuleNameToFull = {};\n    this.fullRuleNameToShort = {};\n    this.ruleShortNameIdx = 256;\n    this.tokenMatcher = tokenStructuredMatcherNoCategories;\n    this.subruleIdx = 0;\n\n    this.definedRulesNames = [];\n    this.tokensMap = {};\n    this.isBackTrackingStack = [];\n    this.RULE_STACK = [];\n    this.RULE_OCCURRENCE_STACK = [];\n    this.gastProductionsCache = {};\n\n    if (has(config, \"serializedGrammar\")) {\n      throw Error(\n        \"The Parser's configuration can no longer contain a <serializedGrammar> property.\\n\" +\n          \"\\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\\n\" +\n          \"\\tFor Further details.\",\n      );\n    }\n\n    if (isArray(tokenVocabulary)) {\n      // This only checks for Token vocabularies provided as arrays.\n      // That is good enough because the main objective is to detect users of pre-V4.0 APIs\n      // rather than all edge cases of empty Token vocabularies.\n      if (isEmpty(tokenVocabulary as any[])) {\n        throw Error(\n          \"A Token Vocabulary cannot be empty.\\n\" +\n            \"\\tNote that the first argument for the parser constructor\\n\" +\n            \"\\tis no longer a Token vector (since v4.0).\",\n        );\n      }\n\n      if (typeof (tokenVocabulary as any[])[0].startOffset === \"number\") {\n        throw Error(\n          \"The Parser constructor no longer accepts a token vector as the first argument.\\n\" +\n            \"\\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\\n\" +\n            \"\\tFor Further details.\",\n        );\n      }\n    }\n\n    if (isArray(tokenVocabulary)) {\n      this.tokensMap = reduce(\n        tokenVocabulary,\n        (acc, tokType: TokenType) => {\n          acc[tokType.name] = tokType;\n          return acc;\n        },\n        {} as { [tokenName: string]: TokenType },\n      );\n    } else if (\n      has(tokenVocabulary, \"modes\") &&\n      every(flatten(values((<any>tokenVocabulary).modes)), isTokenType)\n    ) {\n      const allTokenTypes = flatten(values((<any>tokenVocabulary).modes));\n      const uniqueTokens = uniq(allTokenTypes);\n      this.tokensMap = <any>reduce(\n        uniqueTokens,\n        (acc, tokType: TokenType) => {\n          acc[tokType.name] = tokType;\n          return acc;\n        },\n        {} as { [tokenName: string]: TokenType },\n      );\n    } else if (isObject(tokenVocabulary)) {\n      this.tokensMap = clone(tokenVocabulary as TokenTypeDictionary);\n    } else {\n      throw new Error(\n        \"<tokensDictionary> argument must be An Array of Token constructors,\" +\n          \" A dictionary of Token constructors or an IMultiModeLexerDefinition\",\n      );\n    }\n\n    // always add EOF to the tokenNames -> constructors map. it is useful to assure all the input has been\n    // parsed with a clear error message (\"expecting EOF but found ...\")\n    this.tokensMap[\"EOF\"] = EOF;\n\n    const allTokenTypes = has(tokenVocabulary, \"modes\")\n      ? flatten(values((<any>tokenVocabulary).modes))\n      : values(tokenVocabulary);\n    const noTokenCategoriesUsed = every(allTokenTypes, (tokenConstructor) =>\n      isEmpty(tokenConstructor.categoryMatches),\n    );\n\n    this.tokenMatcher = noTokenCategoriesUsed\n      ? tokenStructuredMatcherNoCategories\n      : tokenStructuredMatcher;\n\n    // Because ES2015+ syntax should be supported for creating Token classes\n    // We cannot assume that the Token classes were created using the \"extendToken\" utilities\n    // Therefore we must augment the Token classes both on Lexer initialization and on Parser initialization\n    augmentTokenTypes(values(this.tokensMap));\n  }\n\n  defineRule<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleName: string,\n    impl: (...args: ARGS) => R,\n    config: IRuleConfig<R>,\n  ): ParserMethodInternal<ARGS, R> {\n    if (this.selfAnalysisDone) {\n      throw Error(\n        `Grammar rule <${ruleName}> may not be defined after the 'performSelfAnalysis' method has been called'\\n` +\n          `Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`,\n      );\n    }\n    const resyncEnabled: boolean = has(config, \"resyncEnabled\")\n      ? (config.resyncEnabled as boolean) // assumes end user provides the correct config value/type\n      : DEFAULT_RULE_CONFIG.resyncEnabled;\n    const recoveryValueFunc = has(config, \"recoveryValueFunc\")\n      ? (config.recoveryValueFunc as () => R) // assumes end user provides the correct config value/type\n      : DEFAULT_RULE_CONFIG.recoveryValueFunc;\n\n    // performance optimization: Use small integers as keys for the longer human readable \"full\" rule names.\n    // this greatly improves Map access time (as much as 8% for some performance benchmarks).\n    const shortName =\n      this.ruleShortNameIdx << (BITS_FOR_METHOD_TYPE + BITS_FOR_OCCURRENCE_IDX);\n\n    this.ruleShortNameIdx++;\n    this.shortRuleNameToFull[shortName] = ruleName;\n    this.fullRuleNameToShort[ruleName] = shortName;\n\n    let invokeRuleWithTry: ParserMethod<ARGS, R>;\n\n    // Micro optimization, only check the condition **once** on rule definition\n    // instead of **every single** rule invocation.\n    if (this.outputCst === true) {\n      invokeRuleWithTry = function invokeRuleWithTry(\n        this: MixedInParser,\n        ...args: ARGS\n      ): R {\n        try {\n          this.ruleInvocationStateUpdate(shortName, ruleName, this.subruleIdx);\n          impl.apply(this, args);\n          const cst = this.CST_STACK[this.CST_STACK.length - 1];\n          this.cstPostRule(cst);\n          return cst as unknown as R;\n        } catch (e) {\n          return this.invokeRuleCatch(e, resyncEnabled, recoveryValueFunc) as R;\n        } finally {\n          this.ruleFinallyStateUpdate();\n        }\n      };\n    } else {\n      invokeRuleWithTry = function invokeRuleWithTryCst(\n        this: MixedInParser,\n        ...args: ARGS\n      ): R {\n        try {\n          this.ruleInvocationStateUpdate(shortName, ruleName, this.subruleIdx);\n          return impl.apply(this, args);\n        } catch (e) {\n          return this.invokeRuleCatch(e, resyncEnabled, recoveryValueFunc) as R;\n        } finally {\n          this.ruleFinallyStateUpdate();\n        }\n      };\n    }\n\n    const wrappedGrammarRule: ParserMethodInternal<ARGS, R> = Object.assign(\n      invokeRuleWithTry as any,\n      { ruleName, originalGrammarAction: impl },\n    );\n\n    return wrappedGrammarRule;\n  }\n\n  invokeRuleCatch(\n    this: MixedInParser,\n    e: Error,\n    resyncEnabledConfig: boolean,\n    recoveryValueFunc: Function,\n  ): unknown {\n    const isFirstInvokedRule = this.RULE_STACK.length === 1;\n    // note the reSync is always enabled for the first rule invocation, because we must always be able to\n    // reSync with EOF and just output some INVALID ParseTree\n    // during backtracking reSync recovery is disabled, otherwise we can't be certain the backtracking\n    // path is really the most valid one\n    const reSyncEnabled =\n      resyncEnabledConfig && !this.isBackTracking() && this.recoveryEnabled;\n\n    if (isRecognitionException(e)) {\n      const recogError: any = e;\n      if (reSyncEnabled) {\n        const reSyncTokType = this.findReSyncTokenType();\n        if (this.isInCurrentRuleReSyncSet(reSyncTokType)) {\n          recogError.resyncedTokens = this.reSyncTo(reSyncTokType);\n          if (this.outputCst) {\n            const partialCstResult: any =\n              this.CST_STACK[this.CST_STACK.length - 1];\n            partialCstResult.recoveredNode = true;\n            return partialCstResult;\n          } else {\n            return recoveryValueFunc(e);\n          }\n        } else {\n          if (this.outputCst) {\n            const partialCstResult: any =\n              this.CST_STACK[this.CST_STACK.length - 1];\n            partialCstResult.recoveredNode = true;\n            recogError.partialCstResult = partialCstResult;\n          }\n          // to be handled Further up the call stack\n          throw recogError;\n        }\n      } else if (isFirstInvokedRule) {\n        // otherwise a Redundant input error will be created as well and we cannot guarantee that this is indeed the case\n        this.moveToTerminatedState();\n        // the parser should never throw one of its own errors outside its flow.\n        // even if error recovery is disabled\n        return recoveryValueFunc(e);\n      } else {\n        // to be recovered Further up the call stack\n        throw recogError;\n      }\n    } else {\n      // some other Error type which we don't know how to handle (for example a built in JavaScript Error)\n      throw e;\n    }\n  }\n\n  // Implementation of parsing DSL\n  optionInternal<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n    occurrence: number,\n  ): OUT | undefined {\n    const key = this.getKeyForAutomaticLookahead(OPTION_IDX, occurrence);\n    return this.optionInternalLogic(actionORMethodDef, occurrence, key);\n  }\n\n  optionInternalLogic<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n    occurrence: number,\n    key: number,\n  ): OUT | undefined {\n    let lookAheadFunc = this.getLaFuncFromCache(key);\n    let action: GrammarAction<OUT>;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookAheadFunc;\n        lookAheadFunc = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n\n    if (lookAheadFunc.call(this) === true) {\n      return action.call(this);\n    }\n    return undefined;\n  }\n\n  atLeastOneInternal<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    const laKey = this.getKeyForAutomaticLookahead(\n      AT_LEAST_ONE_IDX,\n      prodOccurrence,\n    );\n    return this.atLeastOneInternalLogic(\n      prodOccurrence,\n      actionORMethodDef,\n      laKey,\n    );\n  }\n\n  atLeastOneInternalLogic<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n    key: number,\n  ): void {\n    let lookAheadFunc = this.getLaFuncFromCache(key);\n    let action;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookAheadFunc;\n        lookAheadFunc = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n\n    if ((<Function>lookAheadFunc).call(this) === true) {\n      let notStuck = this.doSingleRepetition(action);\n      while (\n        (<Function>lookAheadFunc).call(this) === true &&\n        notStuck === true\n      ) {\n        notStuck = this.doSingleRepetition(action);\n      }\n    } else {\n      throw this.raiseEarlyExitException(\n        prodOccurrence,\n        PROD_TYPE.REPETITION_MANDATORY,\n        (<DSLMethodOptsWithErr<OUT>>actionORMethodDef).ERR_MSG,\n      );\n    }\n\n    // note that while it may seem that this can cause an error because by using a recursive call to\n    // AT_LEAST_ONE we change the grammar to AT_LEAST_TWO, AT_LEAST_THREE ... , the possible recursive call\n    // from the tryInRepetitionRecovery(...) will only happen IFF there really are TWO/THREE/.... items.\n\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    this.attemptInRepetitionRecovery(\n      this.atLeastOneInternal,\n      [prodOccurrence, actionORMethodDef],\n      <any>lookAheadFunc,\n      AT_LEAST_ONE_IDX,\n      prodOccurrence,\n      NextTerminalAfterAtLeastOneWalker,\n    );\n  }\n\n  atLeastOneSepFirstInternal<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    const laKey = this.getKeyForAutomaticLookahead(\n      AT_LEAST_ONE_SEP_IDX,\n      prodOccurrence,\n    );\n    this.atLeastOneSepFirstInternalLogic(prodOccurrence, options, laKey);\n  }\n\n  atLeastOneSepFirstInternalLogic<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    options: AtLeastOneSepMethodOpts<OUT>,\n    key: number,\n  ): void {\n    const action = options.DEF;\n    const separator = options.SEP;\n\n    const firstIterationLookaheadFunc = this.getLaFuncFromCache(key);\n\n    // 1st iteration\n    if (firstIterationLookaheadFunc.call(this) === true) {\n      (<GrammarAction<OUT>>action).call(this);\n\n      //  TODO: Optimization can move this function construction into \"attemptInRepetitionRecovery\"\n      //  because it is only needed in error recovery scenarios.\n      const separatorLookAheadFunc = () => {\n        return this.tokenMatcher(this.LA(1), separator);\n      };\n\n      // 2nd..nth iterations\n      while (this.tokenMatcher(this.LA(1), separator) === true) {\n        // note that this CONSUME will never enter recovery because\n        // the separatorLookAheadFunc checks that the separator really does exist.\n        this.CONSUME(separator);\n        // No need for checking infinite loop here due to consuming the separator.\n        (<GrammarAction<OUT>>action).call(this);\n      }\n\n      // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n      this.attemptInRepetitionRecovery(\n        this.repetitionSepSecondInternal,\n        [\n          prodOccurrence,\n          separator,\n          separatorLookAheadFunc,\n          action,\n          NextTerminalAfterAtLeastOneSepWalker,\n        ],\n        separatorLookAheadFunc,\n        AT_LEAST_ONE_SEP_IDX,\n        prodOccurrence,\n        NextTerminalAfterAtLeastOneSepWalker,\n      );\n    } else {\n      throw this.raiseEarlyExitException(\n        prodOccurrence,\n        PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR,\n        options.ERR_MSG,\n      );\n    }\n  }\n\n  manyInternal<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    const laKey = this.getKeyForAutomaticLookahead(MANY_IDX, prodOccurrence);\n    return this.manyInternalLogic(prodOccurrence, actionORMethodDef, laKey);\n  }\n\n  manyInternalLogic<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n    key: number,\n  ) {\n    let lookaheadFunction = this.getLaFuncFromCache(key);\n    let action;\n    if (typeof actionORMethodDef !== \"function\") {\n      action = actionORMethodDef.DEF;\n      const predicate = actionORMethodDef.GATE;\n      // predicate present\n      if (predicate !== undefined) {\n        const orgLookaheadFunction = lookaheadFunction;\n        lookaheadFunction = () => {\n          return predicate.call(this) && orgLookaheadFunction.call(this);\n        };\n      }\n    } else {\n      action = actionORMethodDef;\n    }\n\n    let notStuck = true;\n    while (lookaheadFunction.call(this) === true && notStuck === true) {\n      notStuck = this.doSingleRepetition(action);\n    }\n\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    this.attemptInRepetitionRecovery(\n      this.manyInternal,\n      [prodOccurrence, actionORMethodDef],\n      <any>lookaheadFunction,\n      MANY_IDX,\n      prodOccurrence,\n      NextTerminalAfterManyWalker,\n      // The notStuck parameter is only relevant when \"attemptInRepetitionRecovery\"\n      // is invoked from manyInternal, in the MANY_SEP case and AT_LEAST_ONE[_SEP]\n      // An infinite loop cannot occur as:\n      // - Either the lookahead is guaranteed to consume something (Single Token Separator)\n      // - AT_LEAST_ONE by definition is guaranteed to consume something (or error out).\n      notStuck,\n    );\n  }\n\n  manySepFirstInternal<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    options: ManySepMethodOpts<OUT>,\n  ): void {\n    const laKey = this.getKeyForAutomaticLookahead(\n      MANY_SEP_IDX,\n      prodOccurrence,\n    );\n    this.manySepFirstInternalLogic(prodOccurrence, options, laKey);\n  }\n\n  manySepFirstInternalLogic<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    options: ManySepMethodOpts<OUT>,\n    key: number,\n  ): void {\n    const action = options.DEF;\n    const separator = options.SEP;\n    const firstIterationLaFunc = this.getLaFuncFromCache(key);\n\n    // 1st iteration\n    if (firstIterationLaFunc.call(this) === true) {\n      action.call(this);\n\n      const separatorLookAheadFunc = () => {\n        return this.tokenMatcher(this.LA(1), separator);\n      };\n      // 2nd..nth iterations\n      while (this.tokenMatcher(this.LA(1), separator) === true) {\n        // note that this CONSUME will never enter recovery because\n        // the separatorLookAheadFunc checks that the separator really does exist.\n        this.CONSUME(separator);\n        // No need for checking infinite loop here due to consuming the separator.\n        action.call(this);\n      }\n\n      // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n      this.attemptInRepetitionRecovery(\n        this.repetitionSepSecondInternal,\n        [\n          prodOccurrence,\n          separator,\n          separatorLookAheadFunc,\n          action,\n          NextTerminalAfterManySepWalker,\n        ],\n        separatorLookAheadFunc,\n        MANY_SEP_IDX,\n        prodOccurrence,\n        NextTerminalAfterManySepWalker,\n      );\n    }\n  }\n\n  repetitionSepSecondInternal<OUT>(\n    this: MixedInParser,\n    prodOccurrence: number,\n    separator: TokenType,\n    separatorLookAheadFunc: () => boolean,\n    action: GrammarAction<OUT>,\n    nextTerminalAfterWalker: typeof AbstractNextTerminalAfterProductionWalker,\n  ): void {\n    while (separatorLookAheadFunc()) {\n      // note that this CONSUME will never enter recovery because\n      // the separatorLookAheadFunc checks that the separator really does exist.\n      this.CONSUME(separator);\n      action.call(this);\n    }\n\n    // we can only arrive to this function after an error\n    // has occurred (hence the name 'second') so the following\n    // IF will always be entered, its possible to remove it...\n    // however it is kept to avoid confusion and be consistent.\n    // Performance optimization: \"attemptInRepetitionRecovery\" will be defined as NOOP unless recovery is enabled\n    /* istanbul ignore else */\n    this.attemptInRepetitionRecovery(\n      this.repetitionSepSecondInternal,\n      [\n        prodOccurrence,\n        separator,\n        separatorLookAheadFunc,\n        action,\n        nextTerminalAfterWalker,\n      ],\n      separatorLookAheadFunc,\n      AT_LEAST_ONE_SEP_IDX,\n      prodOccurrence,\n      nextTerminalAfterWalker,\n    );\n  }\n\n  doSingleRepetition(this: MixedInParser, action: Function): any {\n    const beforeIteration = this.getLexerPosition();\n    action.call(this);\n    const afterIteration = this.getLexerPosition();\n\n    // This boolean will indicate if this repetition progressed\n    // or if we are \"stuck\" (potential infinite loop in the repetition).\n    return afterIteration > beforeIteration;\n  }\n\n  orInternal<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n    occurrence: number,\n  ): T {\n    const laKey = this.getKeyForAutomaticLookahead(OR_IDX, occurrence);\n    const alts = isArray(altsOrOpts) ? altsOrOpts : altsOrOpts.DEF;\n\n    const laFunc = this.getLaFuncFromCache(laKey);\n    const altIdxToTake = laFunc.call(this, alts);\n    if (altIdxToTake !== undefined) {\n      const chosenAlternative: any = alts[altIdxToTake];\n      return chosenAlternative.ALT.call(this);\n    }\n    this.raiseNoAltException(\n      occurrence,\n      (altsOrOpts as OrMethodOpts<unknown>).ERR_MSG,\n    );\n  }\n\n  ruleFinallyStateUpdate(this: MixedInParser): void {\n    this.RULE_STACK.pop();\n    this.RULE_OCCURRENCE_STACK.pop();\n\n    // NOOP when cst is disabled\n    this.cstFinallyStateUpdate();\n\n    if (this.RULE_STACK.length === 0 && this.isAtEndOfInput() === false) {\n      const firstRedundantTok = this.LA(1);\n      const errMsg = this.errorMessageProvider.buildNotAllInputParsedMessage({\n        firstRedundant: firstRedundantTok,\n        ruleName: this.getCurrRuleFullName(),\n      });\n      this.SAVE_ERROR(\n        new NotAllInputParsedException(errMsg, firstRedundantTok),\n      );\n    }\n  }\n\n  subruleInternal<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    idx: number,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    let ruleResult;\n    try {\n      const args = options !== undefined ? options.ARGS : undefined;\n      this.subruleIdx = idx;\n      ruleResult = ruleToCall.apply(this, args);\n      this.cstPostNonTerminal(\n        ruleResult,\n        options !== undefined && options.LABEL !== undefined\n          ? options.LABEL\n          : ruleToCall.ruleName,\n      );\n      return ruleResult;\n    } catch (e) {\n      throw this.subruleInternalError(e, options, ruleToCall.ruleName);\n    }\n  }\n\n  subruleInternalError(\n    this: MixedInParser,\n    e: any,\n    options: SubruleMethodOpts<unknown[]> | undefined,\n    ruleName: string,\n  ): void {\n    if (isRecognitionException(e) && e.partialCstResult !== undefined) {\n      this.cstPostNonTerminal(\n        e.partialCstResult,\n        options !== undefined && options.LABEL !== undefined\n          ? options.LABEL\n          : ruleName,\n      );\n\n      delete e.partialCstResult;\n    }\n    throw e;\n  }\n\n  consumeInternal(\n    this: MixedInParser,\n    tokType: TokenType,\n    idx: number,\n    options: ConsumeMethodOpts | undefined,\n  ): IToken {\n    let consumedToken!: IToken;\n    try {\n      const nextToken = this.LA(1);\n      if (this.tokenMatcher(nextToken, tokType) === true) {\n        this.consumeToken();\n        consumedToken = nextToken;\n      } else {\n        this.consumeInternalError(tokType, nextToken, options);\n      }\n    } catch (eFromConsumption) {\n      consumedToken = this.consumeInternalRecovery(\n        tokType,\n        idx,\n        eFromConsumption,\n      );\n    }\n\n    this.cstPostTerminal(\n      options !== undefined && options.LABEL !== undefined\n        ? options.LABEL\n        : tokType.name,\n      consumedToken,\n    );\n    return consumedToken;\n  }\n\n  consumeInternalError(\n    this: MixedInParser,\n    tokType: TokenType,\n    nextToken: IToken,\n    options: ConsumeMethodOpts | undefined,\n  ): void {\n    let msg;\n    const previousToken = this.LA(0);\n    if (options !== undefined && options.ERR_MSG) {\n      msg = options.ERR_MSG;\n    } else {\n      msg = this.errorMessageProvider.buildMismatchTokenMessage({\n        expected: tokType,\n        actual: nextToken,\n        previous: previousToken,\n        ruleName: this.getCurrRuleFullName(),\n      });\n    }\n    throw this.SAVE_ERROR(\n      new MismatchedTokenException(msg, nextToken, previousToken),\n    );\n  }\n\n  consumeInternalRecovery(\n    this: MixedInParser,\n    tokType: TokenType,\n    idx: number,\n    eFromConsumption: Error,\n  ): IToken {\n    // no recovery allowed during backtracking, otherwise backtracking may recover invalid syntax and accept it\n    // but the original syntax could have been parsed successfully without any backtracking + recovery\n    if (\n      this.recoveryEnabled &&\n      // TODO: more robust checking of the exception type. Perhaps Typescript extending expressions?\n      eFromConsumption.name === \"MismatchedTokenException\" &&\n      !this.isBackTracking()\n    ) {\n      const follows = this.getFollowsForInRuleRecovery(<any>tokType, idx);\n      try {\n        return this.tryInRuleRecovery(<any>tokType, follows);\n      } catch (eFromInRuleRecovery) {\n        if (eFromInRuleRecovery.name === IN_RULE_RECOVERY_EXCEPTION) {\n          // failed in RuleRecovery.\n          // throw the original error in order to trigger reSync error recovery\n          throw eFromConsumption;\n        } else {\n          throw eFromInRuleRecovery;\n        }\n      }\n    } else {\n      throw eFromConsumption;\n    }\n  }\n\n  saveRecogState(this: MixedInParser): IParserState {\n    // errors is a getter which will clone the errors array\n    const savedErrors = this.errors;\n    const savedRuleStack = clone(this.RULE_STACK);\n    return {\n      errors: savedErrors,\n      lexerState: this.exportLexerState(),\n      RULE_STACK: savedRuleStack,\n      CST_STACK: this.CST_STACK,\n    };\n  }\n\n  reloadRecogState(this: MixedInParser, newState: IParserState) {\n    this.errors = newState.errors;\n    this.importLexerState(newState.lexerState);\n    this.RULE_STACK = newState.RULE_STACK;\n  }\n\n  ruleInvocationStateUpdate(\n    this: MixedInParser,\n    shortName: number,\n    fullName: string,\n    idxInCallingRule: number,\n  ): void {\n    this.RULE_OCCURRENCE_STACK.push(idxInCallingRule);\n    this.RULE_STACK.push(shortName);\n    // NOOP when cst is disabled\n    this.cstInvocationStateUpdate(fullName);\n  }\n\n  isBackTracking(this: MixedInParser): boolean {\n    return this.isBackTrackingStack.length !== 0;\n  }\n\n  getCurrRuleFullName(this: MixedInParser): string {\n    const shortName = this.getLastExplicitRuleShortName();\n    return this.shortRuleNameToFull[shortName];\n  }\n\n  shortRuleNameToFullName(this: MixedInParser, shortName: number) {\n    return this.shortRuleNameToFull[shortName];\n  }\n\n  public isAtEndOfInput(this: MixedInParser): boolean {\n    return this.tokenMatcher(this.LA(1), EOF);\n  }\n\n  public reset(this: MixedInParser): void {\n    this.resetLexerState();\n    this.subruleIdx = 0;\n    this.isBackTrackingStack = [];\n    this.errors = [];\n    this.RULE_STACK = [];\n    // TODO: extract a specific reset for TreeBuilder trait\n    this.CST_STACK = [];\n    this.RULE_OCCURRENCE_STACK = [];\n  }\n}\n"], "mappings": "AAkBA,SACEA,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,MAAM,QACD,WAAW;AAClB,SACEC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,uBAAuB,EACvBC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,MAAM,QACD,uBAAuB;AAC9B,SACEC,sBAAsB,EACtBC,wBAAwB,EACxBC,0BAA0B,QACrB,4BAA4B;AACnC,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAEEC,oCAAoC,EACpCC,iCAAiC,EACjCC,8BAA8B,EAC9BC,2BAA2B,QACtB,8BAA8B;AACrC,SAASC,mBAAmB,QAAoC,cAAc;AAC9E,SAASC,0BAA0B,QAAQ,kBAAkB;AAC7D,SAASC,GAAG,QAAQ,gCAAgC;AAEpD,SACEC,iBAAiB,EACjBC,WAAW,EACXC,sBAAsB,EACtBC,kCAAkC,QAC7B,yBAAyB;AAIhC;;;;AAIA,OAAM,MAAOC,gBAAgB;EAe3BC,oBAAoBA,CAClBC,eAAgC,EAChCC,MAAqB;IAErB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,WAAW,CAACC,IAAI;IACtC;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,gBAAgB,GAAG,GAAG;IAC3B,IAAI,CAACC,YAAY,GAAGX,kCAAkC;IACtD,IAAI,CAACY,UAAU,GAAG,CAAC;IAEnB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAE9B,IAAI/C,GAAG,CAACiC,MAAM,EAAE,mBAAmB,CAAC,EAAE;MACpC,MAAMe,KAAK,CACT,oFAAoF,GAClF,0EAA0E,GAC1E,wBAAwB,CAC3B;;IAGH,IAAI/C,OAAO,CAAC+B,eAAe,CAAC,EAAE;MAC5B;MACA;MACA;MACA,IAAI9B,OAAO,CAAC8B,eAAwB,CAAC,EAAE;QACrC,MAAMgB,KAAK,CACT,uCAAuC,GACrC,6DAA6D,GAC7D,6CAA6C,CAChD;;MAGH,IAAI,OAAQhB,eAAyB,CAAC,CAAC,CAAC,CAACiB,WAAW,KAAK,QAAQ,EAAE;QACjE,MAAMD,KAAK,CACT,kFAAkF,GAChF,0EAA0E,GAC1E,wBAAwB,CAC3B;;;IAIL,IAAI/C,OAAO,CAAC+B,eAAe,CAAC,EAAE;MAC5B,IAAI,CAACW,SAAS,GAAGvC,MAAM,CACrB4B,eAAe,EACf,CAACkB,GAAG,EAAEC,OAAkB,KAAI;QAC1BD,GAAG,CAACC,OAAO,CAACf,IAAI,CAAC,GAAGe,OAAO;QAC3B,OAAOD,GAAG;MACZ,CAAC,EACD,EAAwC,CACzC;KACF,MAAM,IACLlD,GAAG,CAACgC,eAAe,EAAE,OAAO,CAAC,IAC7BlC,KAAK,CAACC,OAAO,CAACO,MAAM,CAAO0B,eAAgB,CAACoB,KAAK,CAAC,CAAC,EAAEzB,WAAW,CAAC,EACjE;MACA,MAAM0B,aAAa,GAAGtD,OAAO,CAACO,MAAM,CAAO0B,eAAgB,CAACoB,KAAK,CAAC,CAAC;MACnE,MAAME,YAAY,GAAGjD,IAAI,CAACgD,aAAa,CAAC;MACxC,IAAI,CAACV,SAAS,GAAQvC,MAAM,CAC1BkD,YAAY,EACZ,CAACJ,GAAG,EAAEC,OAAkB,KAAI;QAC1BD,GAAG,CAACC,OAAO,CAACf,IAAI,CAAC,GAAGe,OAAO;QAC3B,OAAOD,GAAG;MACZ,CAAC,EACD,EAAwC,CACzC;KACF,MAAM,IAAI/C,QAAQ,CAAC6B,eAAe,CAAC,EAAE;MACpC,IAAI,CAACW,SAAS,GAAG9C,KAAK,CAACmC,eAAsC,CAAC;KAC/D,MAAM;MACL,MAAM,IAAIgB,KAAK,CACb,qEAAqE,GACnE,qEAAqE,CACxE;;IAGH;IACA;IACA,IAAI,CAACL,SAAS,CAAC,KAAK,CAAC,GAAGlB,GAAG;IAE3B,MAAM4B,aAAa,GAAGrD,GAAG,CAACgC,eAAe,EAAE,OAAO,CAAC,GAC/CjC,OAAO,CAACO,MAAM,CAAO0B,eAAgB,CAACoB,KAAK,CAAC,CAAC,GAC7C9C,MAAM,CAAC0B,eAAe,CAAC;IAC3B,MAAMuB,qBAAqB,GAAGzD,KAAK,CAACuD,aAAa,EAAGG,gBAAgB,IAClEtD,OAAO,CAACsD,gBAAgB,CAACC,eAAe,CAAC,CAC1C;IAED,IAAI,CAACjB,YAAY,GAAGe,qBAAqB,GACrC1B,kCAAkC,GAClCD,sBAAsB;IAE1B;IACA;IACA;IACAF,iBAAiB,CAACpB,MAAM,CAAC,IAAI,CAACqC,SAAS,CAAC,CAAC;EAC3C;EAEAe,UAAUA,CAERC,QAAgB,EAChBC,IAA0B,EAC1B3B,MAAsB;IAEtB,IAAI,IAAI,CAAC4B,gBAAgB,EAAE;MACzB,MAAMb,KAAK,CACT,iBAAiBW,QAAQ,gFAAgF,GACvG,8FAA8F,CACjG;;IAEH,MAAMG,aAAa,GAAY9D,GAAG,CAACiC,MAAM,EAAE,eAAe,CAAC,GACtDA,MAAM,CAAC6B,aAAyB,CAAC;IAAA,EAClCvC,mBAAmB,CAACuC,aAAa;IACrC,MAAMC,iBAAiB,GAAG/D,GAAG,CAACiC,MAAM,EAAE,mBAAmB,CAAC,GACrDA,MAAM,CAAC8B,iBAA6B,CAAC;IAAA,EACtCxC,mBAAmB,CAACwC,iBAAiB;IAEzC;IACA;IACA,MAAMC,SAAS,GACb,IAAI,CAACzB,gBAAgB,IAAK9B,oBAAoB,GAAGC,uBAAwB;IAE3E,IAAI,CAAC6B,gBAAgB,EAAE;IACvB,IAAI,CAACF,mBAAmB,CAAC2B,SAAS,CAAC,GAAGL,QAAQ;IAC9C,IAAI,CAACrB,mBAAmB,CAACqB,QAAQ,CAAC,GAAGK,SAAS;IAE9C,IAAIC,iBAAwC;IAE5C;IACA;IACA,IAAI,IAAI,CAACC,SAAS,KAAK,IAAI,EAAE;MAC3BD,iBAAiB,GAAG,SAASA,iBAAiBA,CAE5C,GAAGE,IAAU;QAEb,IAAI;UACF,IAAI,CAACC,yBAAyB,CAACJ,SAAS,EAAEL,QAAQ,EAAE,IAAI,CAAClB,UAAU,CAAC;UACpEmB,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;UACtB,MAAMG,GAAG,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACA,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;UACrD,IAAI,CAACC,WAAW,CAACH,GAAG,CAAC;UACrB,OAAOA,GAAmB;SAC3B,CAAC,OAAOI,CAAC,EAAE;UACV,OAAO,IAAI,CAACC,eAAe,CAACD,CAAC,EAAEZ,aAAa,EAAEC,iBAAiB,CAAM;SACtE,SAAS;UACR,IAAI,CAACa,sBAAsB,EAAE;;MAEjC,CAAC;KACF,MAAM;MACLX,iBAAiB,GAAG,SAASY,oBAAoBA,CAE/C,GAAGV,IAAU;QAEb,IAAI;UACF,IAAI,CAACC,yBAAyB,CAACJ,SAAS,EAAEL,QAAQ,EAAE,IAAI,CAAClB,UAAU,CAAC;UACpE,OAAOmB,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;SAC9B,CAAC,OAAOO,CAAC,EAAE;UACV,OAAO,IAAI,CAACC,eAAe,CAACD,CAAC,EAAEZ,aAAa,EAAEC,iBAAiB,CAAM;SACtE,SAAS;UACR,IAAI,CAACa,sBAAsB,EAAE;;MAEjC,CAAC;;IAGH,MAAME,kBAAkB,GAAkCC,MAAM,CAACC,MAAM,CACrEf,iBAAwB,EACxB;MAAEN,QAAQ;MAAEsB,qBAAqB,EAAErB;IAAI,CAAE,CAC1C;IAED,OAAOkB,kBAAkB;EAC3B;EAEAH,eAAeA,CAEbD,CAAQ,EACRQ,mBAA4B,EAC5BnB,iBAA2B;IAE3B,MAAMoB,kBAAkB,GAAG,IAAI,CAACtC,UAAU,CAAC2B,MAAM,KAAK,CAAC;IACvD;IACA;IACA;IACA;IACA,MAAMY,aAAa,GACjBF,mBAAmB,IAAI,CAAC,IAAI,CAACG,cAAc,EAAE,IAAI,IAAI,CAACC,eAAe;IAEvE,IAAIvE,sBAAsB,CAAC2D,CAAC,CAAC,EAAE;MAC7B,MAAMa,UAAU,GAAQb,CAAC;MACzB,IAAIU,aAAa,EAAE;QACjB,MAAMI,aAAa,GAAG,IAAI,CAACC,mBAAmB,EAAE;QAChD,IAAI,IAAI,CAACC,wBAAwB,CAACF,aAAa,CAAC,EAAE;UAChDD,UAAU,CAACI,cAAc,GAAG,IAAI,CAACC,QAAQ,CAACJ,aAAa,CAAC;UACxD,IAAI,IAAI,CAACtB,SAAS,EAAE;YAClB,MAAM2B,gBAAgB,GACpB,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACA,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;YAC3CqB,gBAAgB,CAACC,aAAa,GAAG,IAAI;YACrC,OAAOD,gBAAgB;WACxB,MAAM;YACL,OAAO9B,iBAAiB,CAACW,CAAC,CAAC;;SAE9B,MAAM;UACL,IAAI,IAAI,CAACR,SAAS,EAAE;YAClB,MAAM2B,gBAAgB,GACpB,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACA,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;YAC3CqB,gBAAgB,CAACC,aAAa,GAAG,IAAI;YACrCP,UAAU,CAACM,gBAAgB,GAAGA,gBAAgB;;UAEhD;UACA,MAAMN,UAAU;;OAEnB,MAAM,IAAIJ,kBAAkB,EAAE;QAC7B;QACA,IAAI,CAACY,qBAAqB,EAAE;QAC5B;QACA;QACA,OAAOhC,iBAAiB,CAACW,CAAC,CAAC;OAC5B,MAAM;QACL;QACA,MAAMa,UAAU;;KAEnB,MAAM;MACL;MACA,MAAMb,CAAC;;EAEX;EAEA;EACAsB,cAAcA,CAEZC,iBAA0D,EAC1DC,UAAkB;IAElB,MAAMC,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAACvF,UAAU,EAAEqF,UAAU,CAAC;IACpE,OAAO,IAAI,CAACG,mBAAmB,CAACJ,iBAAiB,EAAEC,UAAU,EAAEC,GAAG,CAAC;EACrE;EAEAE,mBAAmBA,CAEjBJ,iBAA0D,EAC1DC,UAAkB,EAClBC,GAAW;IAEX,IAAIG,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACJ,GAAG,CAAC;IAChD,IAAIK,MAA0B;IAC9B,IAAI,OAAOP,iBAAiB,KAAK,UAAU,EAAE;MAC3CO,MAAM,GAAGP,iBAAiB,CAACQ,GAAG;MAC9B,MAAMC,SAAS,GAAGT,iBAAiB,CAACU,IAAI;MACxC;MACA,IAAID,SAAS,KAAKE,SAAS,EAAE;QAC3B,MAAMC,oBAAoB,GAAGP,aAAa;QAC1CA,aAAa,GAAGA,CAAA,KAAK;UACnB,OAAOI,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC,IAAID,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC;QAChE,CAAC;;KAEJ,MAAM;MACLN,MAAM,GAAGP,iBAAiB;;IAG5B,IAAIK,aAAa,CAACQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;MACrC,OAAON,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC;;IAE1B,OAAOF,SAAS;EAClB;EAEAG,kBAAkBA,CAEhBC,cAAsB,EACtBf,iBAAiE;IAEjE,MAAMgB,KAAK,GAAG,IAAI,CAACb,2BAA2B,CAC5C7F,gBAAgB,EAChByG,cAAc,CACf;IACD,OAAO,IAAI,CAACE,uBAAuB,CACjCF,cAAc,EACdf,iBAAiB,EACjBgB,KAAK,CACN;EACH;EAEAC,uBAAuBA,CAErBF,cAAsB,EACtBf,iBAAiE,EACjEE,GAAW;IAEX,IAAIG,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACJ,GAAG,CAAC;IAChD,IAAIK,MAAM;IACV,IAAI,OAAOP,iBAAiB,KAAK,UAAU,EAAE;MAC3CO,MAAM,GAAGP,iBAAiB,CAACQ,GAAG;MAC9B,MAAMC,SAAS,GAAGT,iBAAiB,CAACU,IAAI;MACxC;MACA,IAAID,SAAS,KAAKE,SAAS,EAAE;QAC3B,MAAMC,oBAAoB,GAAGP,aAAa;QAC1CA,aAAa,GAAGA,CAAA,KAAK;UACnB,OAAOI,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC,IAAID,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC;QAChE,CAAC;;KAEJ,MAAM;MACLN,MAAM,GAAGP,iBAAiB;;IAG5B,IAAeK,aAAc,CAACQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;MACjD,IAAIK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACZ,MAAM,CAAC;MAC9C,OACaF,aAAc,CAACQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAC7CK,QAAQ,KAAK,IAAI,EACjB;QACAA,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACZ,MAAM,CAAC;;KAE7C,MAAM;MACL,MAAM,IAAI,CAACa,uBAAuB,CAChCL,cAAc,EACd9F,SAAS,CAACoG,oBAAoB,EACFrB,iBAAkB,CAACsB,OAAO,CACvD;;IAGH;IACA;IACA;IAEA;IACA,IAAI,CAACC,2BAA2B,CAC9B,IAAI,CAACT,kBAAkB,EACvB,CAACC,cAAc,EAAEf,iBAAiB,CAAC,EAC9BK,aAAa,EAClB/F,gBAAgB,EAChByG,cAAc,EACd5F,iCAAiC,CAClC;EACH;EAEAqG,0BAA0BA,CAExBT,cAAsB,EACtBU,OAAqC;IAErC,MAAMT,KAAK,GAAG,IAAI,CAACb,2BAA2B,CAC5C5F,oBAAoB,EACpBwG,cAAc,CACf;IACD,IAAI,CAACW,+BAA+B,CAACX,cAAc,EAAEU,OAAO,EAAET,KAAK,CAAC;EACtE;EAEAU,+BAA+BA,CAE7BX,cAAsB,EACtBU,OAAqC,EACrCvB,GAAW;IAEX,MAAMK,MAAM,GAAGkB,OAAO,CAACjB,GAAG;IAC1B,MAAMmB,SAAS,GAAGF,OAAO,CAACG,GAAG;IAE7B,MAAMC,2BAA2B,GAAG,IAAI,CAACvB,kBAAkB,CAACJ,GAAG,CAAC;IAEhE;IACA,IAAI2B,2BAA2B,CAAChB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;MAC9BN,MAAO,CAACM,IAAI,CAAC,IAAI,CAAC;MAEvC;MACA;MACA,MAAMiB,sBAAsB,GAAGA,CAAA,KAAK;QAClC,OAAO,IAAI,CAACvF,YAAY,CAAC,IAAI,CAACwF,EAAE,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC;MACjD,CAAC;MAED;MACA,OAAO,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACwF,EAAE,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,KAAK,IAAI,EAAE;QACxD;QACA;QACA,IAAI,CAACK,OAAO,CAACL,SAAS,CAAC;QACvB;QACqBpB,MAAO,CAACM,IAAI,CAAC,IAAI,CAAC;;MAGzC;MACA,IAAI,CAACU,2BAA2B,CAC9B,IAAI,CAACU,2BAA2B,EAChC,CACElB,cAAc,EACdY,SAAS,EACTG,sBAAsB,EACtBvB,MAAM,EACNrF,oCAAoC,CACrC,EACD4G,sBAAsB,EACtBvH,oBAAoB,EACpBwG,cAAc,EACd7F,oCAAoC,CACrC;KACF,MAAM;MACL,MAAM,IAAI,CAACkG,uBAAuB,CAChCL,cAAc,EACd9F,SAAS,CAACiH,mCAAmC,EAC7CT,OAAO,CAACH,OAAO,CAChB;;EAEL;EAEAa,YAAYA,CAEVpB,cAAsB,EACtBf,iBAA0D;IAE1D,MAAMgB,KAAK,GAAG,IAAI,CAACb,2BAA2B,CAACzF,QAAQ,EAAEqG,cAAc,CAAC;IACxE,OAAO,IAAI,CAACqB,iBAAiB,CAACrB,cAAc,EAAEf,iBAAiB,EAAEgB,KAAK,CAAC;EACzE;EAEAoB,iBAAiBA,CAEfrB,cAAsB,EACtBf,iBAA0D,EAC1DE,GAAW;IAEX,IAAImC,iBAAiB,GAAG,IAAI,CAAC/B,kBAAkB,CAACJ,GAAG,CAAC;IACpD,IAAIK,MAAM;IACV,IAAI,OAAOP,iBAAiB,KAAK,UAAU,EAAE;MAC3CO,MAAM,GAAGP,iBAAiB,CAACQ,GAAG;MAC9B,MAAMC,SAAS,GAAGT,iBAAiB,CAACU,IAAI;MACxC;MACA,IAAID,SAAS,KAAKE,SAAS,EAAE;QAC3B,MAAMC,oBAAoB,GAAGyB,iBAAiB;QAC9CA,iBAAiB,GAAGA,CAAA,KAAK;UACvB,OAAO5B,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC,IAAID,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC;QAChE,CAAC;;KAEJ,MAAM;MACLN,MAAM,GAAGP,iBAAiB;;IAG5B,IAAIkB,QAAQ,GAAG,IAAI;IACnB,OAAOmB,iBAAiB,CAACxB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAIK,QAAQ,KAAK,IAAI,EAAE;MACjEA,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACZ,MAAM,CAAC;;IAG5C;IACA,IAAI,CAACgB,2BAA2B,CAC9B,IAAI,CAACY,YAAY,EACjB,CAACpB,cAAc,EAAEf,iBAAiB,CAAC,EAC9BqC,iBAAiB,EACtB3H,QAAQ,EACRqG,cAAc,EACd1F,2BAA2B;IAC3B;IACA;IACA;IACA;IACA;IACA6F,QAAQ,CACT;EACH;EAEAoB,oBAAoBA,CAElBvB,cAAsB,EACtBU,OAA+B;IAE/B,MAAMT,KAAK,GAAG,IAAI,CAACb,2BAA2B,CAC5CxF,YAAY,EACZoG,cAAc,CACf;IACD,IAAI,CAACwB,yBAAyB,CAACxB,cAAc,EAAEU,OAAO,EAAET,KAAK,CAAC;EAChE;EAEAuB,yBAAyBA,CAEvBxB,cAAsB,EACtBU,OAA+B,EAC/BvB,GAAW;IAEX,MAAMK,MAAM,GAAGkB,OAAO,CAACjB,GAAG;IAC1B,MAAMmB,SAAS,GAAGF,OAAO,CAACG,GAAG;IAC7B,MAAMY,oBAAoB,GAAG,IAAI,CAAClC,kBAAkB,CAACJ,GAAG,CAAC;IAEzD;IACA,IAAIsC,oBAAoB,CAAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;MAC5CN,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC;MAEjB,MAAMiB,sBAAsB,GAAGA,CAAA,KAAK;QAClC,OAAO,IAAI,CAACvF,YAAY,CAAC,IAAI,CAACwF,EAAE,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC;MACjD,CAAC;MACD;MACA,OAAO,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACwF,EAAE,CAAC,CAAC,CAAC,EAAEJ,SAAS,CAAC,KAAK,IAAI,EAAE;QACxD;QACA;QACA,IAAI,CAACK,OAAO,CAACL,SAAS,CAAC;QACvB;QACApB,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC;;MAGnB;MACA,IAAI,CAACU,2BAA2B,CAC9B,IAAI,CAACU,2BAA2B,EAChC,CACElB,cAAc,EACdY,SAAS,EACTG,sBAAsB,EACtBvB,MAAM,EACNnF,8BAA8B,CAC/B,EACD0G,sBAAsB,EACtBnH,YAAY,EACZoG,cAAc,EACd3F,8BAA8B,CAC/B;;EAEL;EAEA6G,2BAA2BA,CAEzBlB,cAAsB,EACtBY,SAAoB,EACpBG,sBAAqC,EACrCvB,MAA0B,EAC1BkC,uBAAyE;IAEzE,OAAOX,sBAAsB,EAAE,EAAE;MAC/B;MACA;MACA,IAAI,CAACE,OAAO,CAACL,SAAS,CAAC;MACvBpB,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC;;IAGnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACU,2BAA2B,CAC9B,IAAI,CAACU,2BAA2B,EAChC,CACElB,cAAc,EACdY,SAAS,EACTG,sBAAsB,EACtBvB,MAAM,EACNkC,uBAAuB,CACxB,EACDX,sBAAsB,EACtBvH,oBAAoB,EACpBwG,cAAc,EACd0B,uBAAuB,CACxB;EACH;EAEAtB,kBAAkBA,CAAsBZ,MAAgB;IACtD,MAAMmC,eAAe,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC/CpC,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC;IACjB,MAAM+B,cAAc,GAAG,IAAI,CAACD,gBAAgB,EAAE;IAE9C;IACA;IACA,OAAOC,cAAc,GAAGF,eAAe;EACzC;EAEAG,UAAUA,CAERC,UAAiD,EACjD7C,UAAkB;IAElB,MAAMe,KAAK,GAAG,IAAI,CAACb,2BAA2B,CAACtF,MAAM,EAAEoF,UAAU,CAAC;IAClE,MAAM8C,IAAI,GAAG/I,OAAO,CAAC8I,UAAU,CAAC,GAAGA,UAAU,GAAGA,UAAU,CAACtC,GAAG;IAE9D,MAAMwC,MAAM,GAAG,IAAI,CAAC1C,kBAAkB,CAACU,KAAK,CAAC;IAC7C,MAAMiC,YAAY,GAAGD,MAAM,CAACnC,IAAI,CAAC,IAAI,EAAEkC,IAAI,CAAC;IAC5C,IAAIE,YAAY,KAAKtC,SAAS,EAAE;MAC9B,MAAMuC,iBAAiB,GAAQH,IAAI,CAACE,YAAY,CAAC;MACjD,OAAOC,iBAAiB,CAACC,GAAG,CAACtC,IAAI,CAAC,IAAI,CAAC;;IAEzC,IAAI,CAACuC,mBAAmB,CACtBnD,UAAU,EACT6C,UAAoC,CAACxB,OAAO,CAC9C;EACH;EAEA3C,sBAAsBA,CAAA;IACpB,IAAI,CAAC/B,UAAU,CAACyG,GAAG,EAAE;IACrB,IAAI,CAACxG,qBAAqB,CAACwG,GAAG,EAAE;IAEhC;IACA,IAAI,CAACC,qBAAqB,EAAE;IAE5B,IAAI,IAAI,CAAC1G,UAAU,CAAC2B,MAAM,KAAK,CAAC,IAAI,IAAI,CAACgF,cAAc,EAAE,KAAK,KAAK,EAAE;MACnE,MAAMC,iBAAiB,GAAG,IAAI,CAACzB,EAAE,CAAC,CAAC,CAAC;MACpC,MAAM0B,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACC,6BAA6B,CAAC;QACrEC,cAAc,EAAEJ,iBAAiB;QACjC9F,QAAQ,EAAE,IAAI,CAACmG,mBAAmB;OACnC,CAAC;MACF,IAAI,CAACC,UAAU,CACb,IAAI9I,0BAA0B,CAACyI,MAAM,EAAED,iBAAiB,CAAC,CAC1D;;EAEL;EAEAO,eAAeA,CAEbC,UAAyC,EACzCC,GAAW,EACXxC,OAAiC;IAEjC,IAAIyC,UAAU;IACd,IAAI;MACF,MAAMhG,IAAI,GAAGuD,OAAO,KAAKd,SAAS,GAAGc,OAAO,CAAC0C,IAAI,GAAGxD,SAAS;MAC7D,IAAI,CAACnE,UAAU,GAAGyH,GAAG;MACrBC,UAAU,GAAGF,UAAU,CAAC5F,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;MACzC,IAAI,CAACkG,kBAAkB,CACrBF,UAAU,EACVzC,OAAO,KAAKd,SAAS,IAAIc,OAAO,CAAC4C,KAAK,KAAK1D,SAAS,GAChDc,OAAO,CAAC4C,KAAK,GACbL,UAAU,CAACtG,QAAQ,CACxB;MACD,OAAOwG,UAAU;KAClB,CAAC,OAAOzF,CAAC,EAAE;MACV,MAAM,IAAI,CAAC6F,oBAAoB,CAAC7F,CAAC,EAAEgD,OAAO,EAAEuC,UAAU,CAACtG,QAAQ,CAAC;;EAEpE;EAEA4G,oBAAoBA,CAElB7F,CAAM,EACNgD,OAAiD,EACjD/D,QAAgB;IAEhB,IAAI5C,sBAAsB,CAAC2D,CAAC,CAAC,IAAIA,CAAC,CAACmB,gBAAgB,KAAKe,SAAS,EAAE;MACjE,IAAI,CAACyD,kBAAkB,CACrB3F,CAAC,CAACmB,gBAAgB,EAClB6B,OAAO,KAAKd,SAAS,IAAIc,OAAO,CAAC4C,KAAK,KAAK1D,SAAS,GAChDc,OAAO,CAAC4C,KAAK,GACb3G,QAAQ,CACb;MAED,OAAOe,CAAC,CAACmB,gBAAgB;;IAE3B,MAAMnB,CAAC;EACT;EAEA8F,eAAeA,CAEbrH,OAAkB,EAClB+G,GAAW,EACXxC,OAAsC;IAEtC,IAAI+C,aAAsB;IAC1B,IAAI;MACF,MAAMC,SAAS,GAAG,IAAI,CAAC1C,EAAE,CAAC,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACxF,YAAY,CAACkI,SAAS,EAAEvH,OAAO,CAAC,KAAK,IAAI,EAAE;QAClD,IAAI,CAACwH,YAAY,EAAE;QACnBF,aAAa,GAAGC,SAAS;OAC1B,MAAM;QACL,IAAI,CAACE,oBAAoB,CAACzH,OAAO,EAAEuH,SAAS,EAAEhD,OAAO,CAAC;;KAEzD,CAAC,OAAOmD,gBAAgB,EAAE;MACzBJ,aAAa,GAAG,IAAI,CAACK,uBAAuB,CAC1C3H,OAAO,EACP+G,GAAG,EACHW,gBAAgB,CACjB;;IAGH,IAAI,CAACE,eAAe,CAClBrD,OAAO,KAAKd,SAAS,IAAIc,OAAO,CAAC4C,KAAK,KAAK1D,SAAS,GAChDc,OAAO,CAAC4C,KAAK,GACbnH,OAAO,CAACf,IAAI,EAChBqI,aAAa,CACd;IACD,OAAOA,aAAa;EACtB;EAEAG,oBAAoBA,CAElBzH,OAAkB,EAClBuH,SAAiB,EACjBhD,OAAsC;IAEtC,IAAIsD,GAAG;IACP,MAAMC,aAAa,GAAG,IAAI,CAACjD,EAAE,CAAC,CAAC,CAAC;IAChC,IAAIN,OAAO,KAAKd,SAAS,IAAIc,OAAO,CAACH,OAAO,EAAE;MAC5CyD,GAAG,GAAGtD,OAAO,CAACH,OAAO;KACtB,MAAM;MACLyD,GAAG,GAAG,IAAI,CAACrB,oBAAoB,CAACuB,yBAAyB,CAAC;QACxDC,QAAQ,EAAEhI,OAAO;QACjBiI,MAAM,EAAEV,SAAS;QACjBW,QAAQ,EAAEJ,aAAa;QACvBtH,QAAQ,EAAE,IAAI,CAACmG,mBAAmB;OACnC,CAAC;;IAEJ,MAAM,IAAI,CAACC,UAAU,CACnB,IAAI/I,wBAAwB,CAACgK,GAAG,EAAEN,SAAS,EAAEO,aAAa,CAAC,CAC5D;EACH;EAEAH,uBAAuBA,CAErB3H,OAAkB,EAClB+G,GAAW,EACXW,gBAAuB;IAEvB;IACA;IACA,IACE,IAAI,CAACvF,eAAe;IACpB;IACAuF,gBAAgB,CAACzI,IAAI,KAAK,0BAA0B,IACpD,CAAC,IAAI,CAACiD,cAAc,EAAE,EACtB;MACA,MAAMiG,OAAO,GAAG,IAAI,CAACC,2BAA2B,CAAMpI,OAAO,EAAE+G,GAAG,CAAC;MACnE,IAAI;QACF,OAAO,IAAI,CAACsB,iBAAiB,CAAMrI,OAAO,EAAEmI,OAAO,CAAC;OACrD,CAAC,OAAOG,mBAAmB,EAAE;QAC5B,IAAIA,mBAAmB,CAACrJ,IAAI,KAAKZ,0BAA0B,EAAE;UAC3D;UACA;UACA,MAAMqJ,gBAAgB;SACvB,MAAM;UACL,MAAMY,mBAAmB;;;KAG9B,MAAM;MACL,MAAMZ,gBAAgB;;EAE1B;EAEAa,cAAcA,CAAA;IACZ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,MAAM;IAC/B,MAAMC,cAAc,GAAGhM,KAAK,CAAC,IAAI,CAACgD,UAAU,CAAC;IAC7C,OAAO;MACL+I,MAAM,EAAED,WAAW;MACnBG,UAAU,EAAE,IAAI,CAACC,gBAAgB,EAAE;MACnClJ,UAAU,EAAEgJ,cAAc;MAC1BtH,SAAS,EAAE,IAAI,CAACA;KACjB;EACH;EAEAyH,gBAAgBA,CAAsBC,QAAsB;IAC1D,IAAI,CAACL,MAAM,GAAGK,QAAQ,CAACL,MAAM;IAC7B,IAAI,CAACM,gBAAgB,CAACD,QAAQ,CAACH,UAAU,CAAC;IAC1C,IAAI,CAACjJ,UAAU,GAAGoJ,QAAQ,CAACpJ,UAAU;EACvC;EAEAuB,yBAAyBA,CAEvBJ,SAAiB,EACjBmI,QAAgB,EAChBC,gBAAwB;IAExB,IAAI,CAACtJ,qBAAqB,CAACuJ,IAAI,CAACD,gBAAgB,CAAC;IACjD,IAAI,CAACvJ,UAAU,CAACwJ,IAAI,CAACrI,SAAS,CAAC;IAC/B;IACA,IAAI,CAACsI,wBAAwB,CAACH,QAAQ,CAAC;EACzC;EAEA9G,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACzC,mBAAmB,CAAC4B,MAAM,KAAK,CAAC;EAC9C;EAEAsF,mBAAmBA,CAAA;IACjB,MAAM9F,SAAS,GAAG,IAAI,CAACuI,4BAA4B,EAAE;IACrD,OAAO,IAAI,CAAClK,mBAAmB,CAAC2B,SAAS,CAAC;EAC5C;EAEAwI,uBAAuBA,CAAsBxI,SAAiB;IAC5D,OAAO,IAAI,CAAC3B,mBAAmB,CAAC2B,SAAS,CAAC;EAC5C;EAEOwF,cAAcA,CAAA;IACnB,OAAO,IAAI,CAAChH,YAAY,CAAC,IAAI,CAACwF,EAAE,CAAC,CAAC,CAAC,EAAEvG,GAAG,CAAC;EAC3C;EAEOgL,KAAKA,CAAA;IACV,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACjK,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACgJ,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC/I,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAAC0B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACzB,qBAAqB,GAAG,EAAE;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}