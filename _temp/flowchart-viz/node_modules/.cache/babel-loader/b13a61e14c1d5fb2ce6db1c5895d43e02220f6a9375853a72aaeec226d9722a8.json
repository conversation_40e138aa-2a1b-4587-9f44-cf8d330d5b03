{"ast": null, "code": "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function (value, path) {\n    return hasIn(object, path);\n  });\n}\nexport default basePick;", "map": {"version": 3, "names": ["basePickBy", "hasIn", "base<PERSON>ick", "object", "paths", "value", "path"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_basePick.js"], "sourcesContent": ["import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,KAAK,MAAM,YAAY;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,OAAOJ,UAAU,CAACG,MAAM,EAAEC,KAAK,EAAE,UAASC,KAAK,EAAEC,IAAI,EAAE;IACrD,OAAOL,KAAK,CAACE,MAAM,EAAEG,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}