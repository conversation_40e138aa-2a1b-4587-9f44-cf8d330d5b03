{"ast": null, "code": "import { addNoneTerminalToCst, addTerminalToCst, setNodeLocationFull, setNodeLocationOnlyOffset } from \"../../cst/cst.js\";\nimport { has, isUndefined, keys, noop } from \"lodash-es\";\nimport { createBaseSemanticVisitorConstructor, createBaseVisitorConstructorWithDefaults } from \"../../cst/cst_visitor.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n/**\n * This trait is responsible for the CST building logic.\n */\nexport class TreeBuilder {\n  initTreeBuilder(config) {\n    this.CST_STACK = [];\n    // outputCst is no longer exposed/defined in the pubic API\n    this.outputCst = config.outputCst;\n    this.nodeLocationTracking = has(config, \"nodeLocationTracking\") ? config.nodeLocationTracking // assumes end user provides the correct config value/type\n    : DEFAULT_PARSER_CONFIG.nodeLocationTracking;\n    if (!this.outputCst) {\n      this.cstInvocationStateUpdate = noop;\n      this.cstFinallyStateUpdate = noop;\n      this.cstPostTerminal = noop;\n      this.cstPostNonTerminal = noop;\n      this.cstPostRule = noop;\n    } else {\n      if (/full/i.test(this.nodeLocationTracking)) {\n        if (this.recoveryEnabled) {\n          this.setNodeLocationFromToken = setNodeLocationFull;\n          this.setNodeLocationFromNode = setNodeLocationFull;\n          this.cstPostRule = noop;\n          this.setInitialNodeLocation = this.setInitialNodeLocationFullRecovery;\n        } else {\n          this.setNodeLocationFromToken = noop;\n          this.setNodeLocationFromNode = noop;\n          this.cstPostRule = this.cstPostRuleFull;\n          this.setInitialNodeLocation = this.setInitialNodeLocationFullRegular;\n        }\n      } else if (/onlyOffset/i.test(this.nodeLocationTracking)) {\n        if (this.recoveryEnabled) {\n          this.setNodeLocationFromToken = setNodeLocationOnlyOffset;\n          this.setNodeLocationFromNode = setNodeLocationOnlyOffset;\n          this.cstPostRule = noop;\n          this.setInitialNodeLocation = this.setInitialNodeLocationOnlyOffsetRecovery;\n        } else {\n          this.setNodeLocationFromToken = noop;\n          this.setNodeLocationFromNode = noop;\n          this.cstPostRule = this.cstPostRuleOnlyOffset;\n          this.setInitialNodeLocation = this.setInitialNodeLocationOnlyOffsetRegular;\n        }\n      } else if (/none/i.test(this.nodeLocationTracking)) {\n        this.setNodeLocationFromToken = noop;\n        this.setNodeLocationFromNode = noop;\n        this.cstPostRule = noop;\n        this.setInitialNodeLocation = noop;\n      } else {\n        throw Error(`Invalid <nodeLocationTracking> config option: \"${config.nodeLocationTracking}\"`);\n      }\n    }\n  }\n  setInitialNodeLocationOnlyOffsetRecovery(cstNode) {\n    cstNode.location = {\n      startOffset: NaN,\n      endOffset: NaN\n    };\n  }\n  setInitialNodeLocationOnlyOffsetRegular(cstNode) {\n    cstNode.location = {\n      // without error recovery the starting Location of a new CstNode is guaranteed\n      // To be the next Token's startOffset (for valid inputs).\n      // For invalid inputs there won't be any CSTOutput so this potential\n      // inaccuracy does not matter\n      startOffset: this.LA(1).startOffset,\n      endOffset: NaN\n    };\n  }\n  setInitialNodeLocationFullRecovery(cstNode) {\n    cstNode.location = {\n      startOffset: NaN,\n      startLine: NaN,\n      startColumn: NaN,\n      endOffset: NaN,\n      endLine: NaN,\n      endColumn: NaN\n    };\n  }\n  /**\n     *  @see setInitialNodeLocationOnlyOffsetRegular for explanation why this work\n        * @param cstNode\n     */\n  setInitialNodeLocationFullRegular(cstNode) {\n    const nextToken = this.LA(1);\n    cstNode.location = {\n      startOffset: nextToken.startOffset,\n      startLine: nextToken.startLine,\n      startColumn: nextToken.startColumn,\n      endOffset: NaN,\n      endLine: NaN,\n      endColumn: NaN\n    };\n  }\n  cstInvocationStateUpdate(fullRuleName) {\n    const cstNode = {\n      name: fullRuleName,\n      children: Object.create(null)\n    };\n    this.setInitialNodeLocation(cstNode);\n    this.CST_STACK.push(cstNode);\n  }\n  cstFinallyStateUpdate() {\n    this.CST_STACK.pop();\n  }\n  cstPostRuleFull(ruleCstNode) {\n    // casts to `required<CstNodeLocation>` are safe because `cstPostRuleFull` should only be invoked when full location is enabled\n    const prevToken = this.LA(0);\n    const loc = ruleCstNode.location;\n    // If this condition is true it means we consumed at least one Token\n    // In this CstNode.\n    if (loc.startOffset <= prevToken.startOffset === true) {\n      loc.endOffset = prevToken.endOffset;\n      loc.endLine = prevToken.endLine;\n      loc.endColumn = prevToken.endColumn;\n    }\n    // \"empty\" CstNode edge case\n    else {\n      loc.startOffset = NaN;\n      loc.startLine = NaN;\n      loc.startColumn = NaN;\n    }\n  }\n  cstPostRuleOnlyOffset(ruleCstNode) {\n    const prevToken = this.LA(0);\n    // `location' is not null because `cstPostRuleOnlyOffset` will only be invoked when location tracking is enabled.\n    const loc = ruleCstNode.location;\n    // If this condition is true it means we consumed at least one Token\n    // In this CstNode.\n    if (loc.startOffset <= prevToken.startOffset === true) {\n      loc.endOffset = prevToken.endOffset;\n    }\n    // \"empty\" CstNode edge case\n    else {\n      loc.startOffset = NaN;\n    }\n  }\n  cstPostTerminal(key, consumedToken) {\n    const rootCst = this.CST_STACK[this.CST_STACK.length - 1];\n    addTerminalToCst(rootCst, consumedToken, key);\n    // This is only used when **both** error recovery and CST Output are enabled.\n    this.setNodeLocationFromToken(rootCst.location, consumedToken);\n  }\n  cstPostNonTerminal(ruleCstResult, ruleName) {\n    const preCstNode = this.CST_STACK[this.CST_STACK.length - 1];\n    addNoneTerminalToCst(preCstNode, ruleName, ruleCstResult);\n    // This is only used when **both** error recovery and CST Output are enabled.\n    this.setNodeLocationFromNode(preCstNode.location, ruleCstResult.location);\n  }\n  getBaseCstVisitorConstructor() {\n    if (isUndefined(this.baseCstVisitorConstructor)) {\n      const newBaseCstVisitorConstructor = createBaseSemanticVisitorConstructor(this.className, keys(this.gastProductionsCache));\n      this.baseCstVisitorConstructor = newBaseCstVisitorConstructor;\n      return newBaseCstVisitorConstructor;\n    }\n    return this.baseCstVisitorConstructor;\n  }\n  getBaseCstVisitorConstructorWithDefaults() {\n    if (isUndefined(this.baseCstVisitorWithDefaultsConstructor)) {\n      const newConstructor = createBaseVisitorConstructorWithDefaults(this.className, keys(this.gastProductionsCache), this.getBaseCstVisitorConstructor());\n      this.baseCstVisitorWithDefaultsConstructor = newConstructor;\n      return newConstructor;\n    }\n    return this.baseCstVisitorWithDefaultsConstructor;\n  }\n  getLastExplicitRuleShortName() {\n    const ruleStack = this.RULE_STACK;\n    return ruleStack[ruleStack.length - 1];\n  }\n  getPreviousExplicitRuleShortName() {\n    const ruleStack = this.RULE_STACK;\n    return ruleStack[ruleStack.length - 2];\n  }\n  getLastExplicitRuleOccurrenceIndex() {\n    const occurrenceStack = this.RULE_OCCURRENCE_STACK;\n    return occurrenceStack[occurrenceStack.length - 1];\n  }\n}", "map": {"version": 3, "names": ["addNoneTerminalToCst", "addTerminalToCst", "setNodeLocationFull", "setNodeLocationOnlyOffset", "has", "isUndefined", "keys", "noop", "createBaseSemanticVisitorConstructor", "createBaseVisitorConstructorWithDefaults", "DEFAULT_PARSER_CONFIG", "TreeBuilder", "initTreeBuilder", "config", "CST_STACK", "outputCst", "nodeLocationTracking", "cstInvocationStateUpdate", "cstFinallyStateUpdate", "cstPostTerminal", "cstPostNonTerminal", "cstPostRule", "test", "recoveryEnabled", "setNodeLocationFromToken", "setNodeLocationFromNode", "setInitialNodeLocation", "setInitialNodeLocationFullRecovery", "cstPostRuleFull", "setInitialNodeLocationFullRegular", "setInitialNodeLocationOnlyOffsetRecovery", "cstPostRuleOnlyOffset", "setInitialNodeLocationOnlyOffsetRegular", "Error", "cstNode", "location", "startOffset", "NaN", "endOffset", "LA", "startLine", "startColumn", "endLine", "endColumn", "nextToken", "fullRuleName", "name", "children", "Object", "create", "push", "pop", "ruleCstNode", "prevToken", "loc", "key", "consumedToken", "rootCst", "length", "ruleCstResult", "ruleName", "preCstNode", "getBaseCstVisitorConstructor", "baseCstVisitorConstructor", "newBaseCstVisitorConstructor", "className", "gastProductionsCache", "getBaseCstVisitorConstructorWithDefaults", "baseCstVisitorWithDefaultsConstructor", "newConstructor", "getLastExplicitRuleShortName", "ruleStack", "RULE_STACK", "getPreviousExplicitRuleShortName", "getLastExplicitRuleOccurrenceIndex", "occurrenceStack", "RULE_OCCURRENCE_STACK"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/tree_builder.ts"], "sourcesContent": ["import {\n  addNoneTerminalToCst,\n  addTerminalToCst,\n  setNodeLocationFull,\n  setNodeLocationOnlyOffset,\n} from \"../../cst/cst.js\";\nimport { has, isUndefined, keys, noop } from \"lodash-es\";\nimport {\n  createBaseSemanticVisitorConstructor,\n  createBaseVisitorConstructorWithDefaults,\n} from \"../../cst/cst_visitor.js\";\nimport {\n  CstNode,\n  CstNodeLocation,\n  ICstVisitor,\n  IParserConfig,\n  IToken,\n  nodeLocationTrackingOptions,\n} from \"@chevrotain/types\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n\n/**\n * This trait is responsible for the CST building logic.\n */\nexport class TreeBuilder {\n  outputCst: boolean;\n  CST_STACK: CstNode[];\n  baseCstVisitorConstructor: Function;\n  baseCstVisitorWithDefaultsConstructor: Function;\n\n  // dynamically assigned Methods\n  setNodeLocationFromNode: (\n    nodeLocation: CstNodeLocation,\n    locationInformation: CstNodeLocation,\n  ) => void;\n  setNodeLocationFromToken: (\n    nodeLocation: CstNodeLocation,\n    locationInformation: CstNodeLocation,\n  ) => void;\n  cstPostRule: (this: MixedInParser, ruleCstNode: CstNode) => void;\n\n  setInitialNodeLocation: (cstNode: CstNode) => void;\n  nodeLocationTracking: nodeLocationTrackingOptions;\n\n  initTreeBuilder(this: MixedInParser, config: IParserConfig) {\n    this.CST_STACK = [];\n\n    // outputCst is no longer exposed/defined in the pubic API\n    this.outputCst = (config as any).outputCst;\n\n    this.nodeLocationTracking = has(config, \"nodeLocationTracking\")\n      ? (config.nodeLocationTracking as nodeLocationTrackingOptions) // assumes end user provides the correct config value/type\n      : DEFAULT_PARSER_CONFIG.nodeLocationTracking;\n\n    if (!this.outputCst) {\n      this.cstInvocationStateUpdate = noop;\n      this.cstFinallyStateUpdate = noop;\n      this.cstPostTerminal = noop;\n      this.cstPostNonTerminal = noop;\n      this.cstPostRule = noop;\n    } else {\n      if (/full/i.test(this.nodeLocationTracking)) {\n        if (this.recoveryEnabled) {\n          this.setNodeLocationFromToken = setNodeLocationFull;\n          this.setNodeLocationFromNode = setNodeLocationFull;\n          this.cstPostRule = noop;\n          this.setInitialNodeLocation = this.setInitialNodeLocationFullRecovery;\n        } else {\n          this.setNodeLocationFromToken = noop;\n          this.setNodeLocationFromNode = noop;\n          this.cstPostRule = this.cstPostRuleFull;\n          this.setInitialNodeLocation = this.setInitialNodeLocationFullRegular;\n        }\n      } else if (/onlyOffset/i.test(this.nodeLocationTracking)) {\n        if (this.recoveryEnabled) {\n          this.setNodeLocationFromToken = <any>setNodeLocationOnlyOffset;\n          this.setNodeLocationFromNode = <any>setNodeLocationOnlyOffset;\n          this.cstPostRule = noop;\n          this.setInitialNodeLocation =\n            this.setInitialNodeLocationOnlyOffsetRecovery;\n        } else {\n          this.setNodeLocationFromToken = noop;\n          this.setNodeLocationFromNode = noop;\n          this.cstPostRule = this.cstPostRuleOnlyOffset;\n          this.setInitialNodeLocation =\n            this.setInitialNodeLocationOnlyOffsetRegular;\n        }\n      } else if (/none/i.test(this.nodeLocationTracking)) {\n        this.setNodeLocationFromToken = noop;\n        this.setNodeLocationFromNode = noop;\n        this.cstPostRule = noop;\n        this.setInitialNodeLocation = noop;\n      } else {\n        throw Error(\n          `Invalid <nodeLocationTracking> config option: \"${config.nodeLocationTracking}\"`,\n        );\n      }\n    }\n  }\n\n  setInitialNodeLocationOnlyOffsetRecovery(\n    this: MixedInParser,\n    cstNode: any,\n  ): void {\n    cstNode.location = {\n      startOffset: NaN,\n      endOffset: NaN,\n    };\n  }\n\n  setInitialNodeLocationOnlyOffsetRegular(\n    this: MixedInParser,\n    cstNode: any,\n  ): void {\n    cstNode.location = {\n      // without error recovery the starting Location of a new CstNode is guaranteed\n      // To be the next Token's startOffset (for valid inputs).\n      // For invalid inputs there won't be any CSTOutput so this potential\n      // inaccuracy does not matter\n      startOffset: this.LA(1).startOffset,\n      endOffset: NaN,\n    };\n  }\n\n  setInitialNodeLocationFullRecovery(this: MixedInParser, cstNode: any): void {\n    cstNode.location = {\n      startOffset: NaN,\n      startLine: NaN,\n      startColumn: NaN,\n      endOffset: NaN,\n      endLine: NaN,\n      endColumn: NaN,\n    };\n  }\n\n  /**\n     *  @see setInitialNodeLocationOnlyOffsetRegular for explanation why this work\n\n     * @param cstNode\n     */\n  setInitialNodeLocationFullRegular(this: MixedInParser, cstNode: any): void {\n    const nextToken = this.LA(1);\n    cstNode.location = {\n      startOffset: nextToken.startOffset,\n      startLine: nextToken.startLine,\n      startColumn: nextToken.startColumn,\n      endOffset: NaN,\n      endLine: NaN,\n      endColumn: NaN,\n    };\n  }\n\n  cstInvocationStateUpdate(this: MixedInParser, fullRuleName: string): void {\n    const cstNode: CstNode = {\n      name: fullRuleName,\n      children: Object.create(null),\n    };\n\n    this.setInitialNodeLocation(cstNode);\n    this.CST_STACK.push(cstNode);\n  }\n\n  cstFinallyStateUpdate(this: MixedInParser): void {\n    this.CST_STACK.pop();\n  }\n\n  cstPostRuleFull(this: MixedInParser, ruleCstNode: CstNode): void {\n    // casts to `required<CstNodeLocation>` are safe because `cstPostRuleFull` should only be invoked when full location is enabled\n    const prevToken = this.LA(0) as Required<CstNodeLocation>;\n    const loc = ruleCstNode.location as Required<CstNodeLocation>;\n\n    // If this condition is true it means we consumed at least one Token\n    // In this CstNode.\n    if (loc.startOffset <= prevToken.startOffset === true) {\n      loc.endOffset = prevToken.endOffset;\n      loc.endLine = prevToken.endLine;\n      loc.endColumn = prevToken.endColumn;\n    }\n    // \"empty\" CstNode edge case\n    else {\n      loc.startOffset = NaN;\n      loc.startLine = NaN;\n      loc.startColumn = NaN;\n    }\n  }\n\n  cstPostRuleOnlyOffset(this: MixedInParser, ruleCstNode: CstNode): void {\n    const prevToken = this.LA(0);\n    // `location' is not null because `cstPostRuleOnlyOffset` will only be invoked when location tracking is enabled.\n    const loc = ruleCstNode.location!;\n\n    // If this condition is true it means we consumed at least one Token\n    // In this CstNode.\n    if (loc.startOffset <= prevToken.startOffset === true) {\n      loc.endOffset = prevToken.endOffset;\n    }\n    // \"empty\" CstNode edge case\n    else {\n      loc.startOffset = NaN;\n    }\n  }\n\n  cstPostTerminal(\n    this: MixedInParser,\n    key: string,\n    consumedToken: IToken,\n  ): void {\n    const rootCst = this.CST_STACK[this.CST_STACK.length - 1];\n    addTerminalToCst(rootCst, consumedToken, key);\n    // This is only used when **both** error recovery and CST Output are enabled.\n    this.setNodeLocationFromToken(rootCst.location!, <any>consumedToken);\n  }\n\n  cstPostNonTerminal(\n    this: MixedInParser,\n    ruleCstResult: CstNode,\n    ruleName: string,\n  ): void {\n    const preCstNode = this.CST_STACK[this.CST_STACK.length - 1];\n    addNoneTerminalToCst(preCstNode, ruleName, ruleCstResult);\n    // This is only used when **both** error recovery and CST Output are enabled.\n    this.setNodeLocationFromNode(preCstNode.location!, ruleCstResult.location!);\n  }\n\n  getBaseCstVisitorConstructor<IN = any, OUT = any>(\n    this: MixedInParser,\n  ): {\n    new (...args: any[]): ICstVisitor<IN, OUT>;\n  } {\n    if (isUndefined(this.baseCstVisitorConstructor)) {\n      const newBaseCstVisitorConstructor = createBaseSemanticVisitorConstructor(\n        this.className,\n        keys(this.gastProductionsCache),\n      );\n      this.baseCstVisitorConstructor = newBaseCstVisitorConstructor;\n      return newBaseCstVisitorConstructor;\n    }\n\n    return <any>this.baseCstVisitorConstructor;\n  }\n\n  getBaseCstVisitorConstructorWithDefaults<IN = any, OUT = any>(\n    this: MixedInParser,\n  ): {\n    new (...args: any[]): ICstVisitor<IN, OUT>;\n  } {\n    if (isUndefined(this.baseCstVisitorWithDefaultsConstructor)) {\n      const newConstructor = createBaseVisitorConstructorWithDefaults(\n        this.className,\n        keys(this.gastProductionsCache),\n        this.getBaseCstVisitorConstructor(),\n      );\n      this.baseCstVisitorWithDefaultsConstructor = newConstructor;\n      return newConstructor;\n    }\n\n    return <any>this.baseCstVisitorWithDefaultsConstructor;\n  }\n\n  getLastExplicitRuleShortName(this: MixedInParser): number {\n    const ruleStack = this.RULE_STACK;\n    return ruleStack[ruleStack.length - 1];\n  }\n\n  getPreviousExplicitRuleShortName(this: MixedInParser): number {\n    const ruleStack = this.RULE_STACK;\n    return ruleStack[ruleStack.length - 2];\n  }\n\n  getLastExplicitRuleOccurrenceIndex(this: MixedInParser): number {\n    const occurrenceStack = this.RULE_OCCURRENCE_STACK;\n    return occurrenceStack[occurrenceStack.length - 1];\n  }\n}\n"], "mappings": "AAAA,SACEA,oBAAoB,EACpBC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,QACpB,kBAAkB;AACzB,SAASC,GAAG,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,QAAQ,WAAW;AACxD,SACEC,oCAAoC,EACpCC,wCAAwC,QACnC,0BAA0B;AAUjC,SAASC,qBAAqB,QAAQ,cAAc;AAEpD;;;AAGA,OAAM,MAAOC,WAAW;EAoBtBC,eAAeA,CAAsBC,MAAqB;IACxD,IAAI,CAACC,SAAS,GAAG,EAAE;IAEnB;IACA,IAAI,CAACC,SAAS,GAAIF,MAAc,CAACE,SAAS;IAE1C,IAAI,CAACC,oBAAoB,GAAGZ,GAAG,CAACS,MAAM,EAAE,sBAAsB,CAAC,GAC1DA,MAAM,CAACG,oBAAoD,CAAC;IAAA,EAC7DN,qBAAqB,CAACM,oBAAoB;IAE9C,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACnB,IAAI,CAACE,wBAAwB,GAAGV,IAAI;MACpC,IAAI,CAACW,qBAAqB,GAAGX,IAAI;MACjC,IAAI,CAACY,eAAe,GAAGZ,IAAI;MAC3B,IAAI,CAACa,kBAAkB,GAAGb,IAAI;MAC9B,IAAI,CAACc,WAAW,GAAGd,IAAI;KACxB,MAAM;MACL,IAAI,OAAO,CAACe,IAAI,CAAC,IAAI,CAACN,oBAAoB,CAAC,EAAE;QAC3C,IAAI,IAAI,CAACO,eAAe,EAAE;UACxB,IAAI,CAACC,wBAAwB,GAAGtB,mBAAmB;UACnD,IAAI,CAACuB,uBAAuB,GAAGvB,mBAAmB;UAClD,IAAI,CAACmB,WAAW,GAAGd,IAAI;UACvB,IAAI,CAACmB,sBAAsB,GAAG,IAAI,CAACC,kCAAkC;SACtE,MAAM;UACL,IAAI,CAACH,wBAAwB,GAAGjB,IAAI;UACpC,IAAI,CAACkB,uBAAuB,GAAGlB,IAAI;UACnC,IAAI,CAACc,WAAW,GAAG,IAAI,CAACO,eAAe;UACvC,IAAI,CAACF,sBAAsB,GAAG,IAAI,CAACG,iCAAiC;;OAEvE,MAAM,IAAI,aAAa,CAACP,IAAI,CAAC,IAAI,CAACN,oBAAoB,CAAC,EAAE;QACxD,IAAI,IAAI,CAACO,eAAe,EAAE;UACxB,IAAI,CAACC,wBAAwB,GAAQrB,yBAAyB;UAC9D,IAAI,CAACsB,uBAAuB,GAAQtB,yBAAyB;UAC7D,IAAI,CAACkB,WAAW,GAAGd,IAAI;UACvB,IAAI,CAACmB,sBAAsB,GACzB,IAAI,CAACI,wCAAwC;SAChD,MAAM;UACL,IAAI,CAACN,wBAAwB,GAAGjB,IAAI;UACpC,IAAI,CAACkB,uBAAuB,GAAGlB,IAAI;UACnC,IAAI,CAACc,WAAW,GAAG,IAAI,CAACU,qBAAqB;UAC7C,IAAI,CAACL,sBAAsB,GACzB,IAAI,CAACM,uCAAuC;;OAEjD,MAAM,IAAI,OAAO,CAACV,IAAI,CAAC,IAAI,CAACN,oBAAoB,CAAC,EAAE;QAClD,IAAI,CAACQ,wBAAwB,GAAGjB,IAAI;QACpC,IAAI,CAACkB,uBAAuB,GAAGlB,IAAI;QACnC,IAAI,CAACc,WAAW,GAAGd,IAAI;QACvB,IAAI,CAACmB,sBAAsB,GAAGnB,IAAI;OACnC,MAAM;QACL,MAAM0B,KAAK,CACT,kDAAkDpB,MAAM,CAACG,oBAAoB,GAAG,CACjF;;;EAGP;EAEAc,wCAAwCA,CAEtCI,OAAY;IAEZA,OAAO,CAACC,QAAQ,GAAG;MACjBC,WAAW,EAAEC,GAAG;MAChBC,SAAS,EAAED;KACZ;EACH;EAEAL,uCAAuCA,CAErCE,OAAY;IAEZA,OAAO,CAACC,QAAQ,GAAG;MACjB;MACA;MACA;MACA;MACAC,WAAW,EAAE,IAAI,CAACG,EAAE,CAAC,CAAC,CAAC,CAACH,WAAW;MACnCE,SAAS,EAAED;KACZ;EACH;EAEAV,kCAAkCA,CAAsBO,OAAY;IAClEA,OAAO,CAACC,QAAQ,GAAG;MACjBC,WAAW,EAAEC,GAAG;MAChBG,SAAS,EAAEH,GAAG;MACdI,WAAW,EAAEJ,GAAG;MAChBC,SAAS,EAAED,GAAG;MACdK,OAAO,EAAEL,GAAG;MACZM,SAAS,EAAEN;KACZ;EACH;EAEA;;;;EAKAR,iCAAiCA,CAAsBK,OAAY;IACjE,MAAMU,SAAS,GAAG,IAAI,CAACL,EAAE,CAAC,CAAC,CAAC;IAC5BL,OAAO,CAACC,QAAQ,GAAG;MACjBC,WAAW,EAAEQ,SAAS,CAACR,WAAW;MAClCI,SAAS,EAAEI,SAAS,CAACJ,SAAS;MAC9BC,WAAW,EAAEG,SAAS,CAACH,WAAW;MAClCH,SAAS,EAAED,GAAG;MACdK,OAAO,EAAEL,GAAG;MACZM,SAAS,EAAEN;KACZ;EACH;EAEApB,wBAAwBA,CAAsB4B,YAAoB;IAChE,MAAMX,OAAO,GAAY;MACvBY,IAAI,EAAED,YAAY;MAClBE,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI;KAC7B;IAED,IAAI,CAACvB,sBAAsB,CAACQ,OAAO,CAAC;IACpC,IAAI,CAACpB,SAAS,CAACoC,IAAI,CAAChB,OAAO,CAAC;EAC9B;EAEAhB,qBAAqBA,CAAA;IACnB,IAAI,CAACJ,SAAS,CAACqC,GAAG,EAAE;EACtB;EAEAvB,eAAeA,CAAsBwB,WAAoB;IACvD;IACA,MAAMC,SAAS,GAAG,IAAI,CAACd,EAAE,CAAC,CAAC,CAA8B;IACzD,MAAMe,GAAG,GAAGF,WAAW,CAACjB,QAAqC;IAE7D;IACA;IACA,IAAImB,GAAG,CAAClB,WAAW,IAAIiB,SAAS,CAACjB,WAAW,KAAK,IAAI,EAAE;MACrDkB,GAAG,CAAChB,SAAS,GAAGe,SAAS,CAACf,SAAS;MACnCgB,GAAG,CAACZ,OAAO,GAAGW,SAAS,CAACX,OAAO;MAC/BY,GAAG,CAACX,SAAS,GAAGU,SAAS,CAACV,SAAS;;IAErC;IAAA,KACK;MACHW,GAAG,CAAClB,WAAW,GAAGC,GAAG;MACrBiB,GAAG,CAACd,SAAS,GAAGH,GAAG;MACnBiB,GAAG,CAACb,WAAW,GAAGJ,GAAG;;EAEzB;EAEAN,qBAAqBA,CAAsBqB,WAAoB;IAC7D,MAAMC,SAAS,GAAG,IAAI,CAACd,EAAE,CAAC,CAAC,CAAC;IAC5B;IACA,MAAMe,GAAG,GAAGF,WAAW,CAACjB,QAAS;IAEjC;IACA;IACA,IAAImB,GAAG,CAAClB,WAAW,IAAIiB,SAAS,CAACjB,WAAW,KAAK,IAAI,EAAE;MACrDkB,GAAG,CAAChB,SAAS,GAAGe,SAAS,CAACf,SAAS;;IAErC;IAAA,KACK;MACHgB,GAAG,CAAClB,WAAW,GAAGC,GAAG;;EAEzB;EAEAlB,eAAeA,CAEboC,GAAW,EACXC,aAAqB;IAErB,MAAMC,OAAO,GAAG,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC4C,MAAM,GAAG,CAAC,CAAC;IACzDzD,gBAAgB,CAACwD,OAAO,EAAED,aAAa,EAAED,GAAG,CAAC;IAC7C;IACA,IAAI,CAAC/B,wBAAwB,CAACiC,OAAO,CAACtB,QAAS,EAAOqB,aAAa,CAAC;EACtE;EAEApC,kBAAkBA,CAEhBuC,aAAsB,EACtBC,QAAgB;IAEhB,MAAMC,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC4C,MAAM,GAAG,CAAC,CAAC;IAC5D1D,oBAAoB,CAAC6D,UAAU,EAAED,QAAQ,EAAED,aAAa,CAAC;IACzD;IACA,IAAI,CAAClC,uBAAuB,CAACoC,UAAU,CAAC1B,QAAS,EAAEwB,aAAa,CAACxB,QAAS,CAAC;EAC7E;EAEA2B,4BAA4BA,CAAA;IAK1B,IAAIzD,WAAW,CAAC,IAAI,CAAC0D,yBAAyB,CAAC,EAAE;MAC/C,MAAMC,4BAA4B,GAAGxD,oCAAoC,CACvE,IAAI,CAACyD,SAAS,EACd3D,IAAI,CAAC,IAAI,CAAC4D,oBAAoB,CAAC,CAChC;MACD,IAAI,CAACH,yBAAyB,GAAGC,4BAA4B;MAC7D,OAAOA,4BAA4B;;IAGrC,OAAY,IAAI,CAACD,yBAAyB;EAC5C;EAEAI,wCAAwCA,CAAA;IAKtC,IAAI9D,WAAW,CAAC,IAAI,CAAC+D,qCAAqC,CAAC,EAAE;MAC3D,MAAMC,cAAc,GAAG5D,wCAAwC,CAC7D,IAAI,CAACwD,SAAS,EACd3D,IAAI,CAAC,IAAI,CAAC4D,oBAAoB,CAAC,EAC/B,IAAI,CAACJ,4BAA4B,EAAE,CACpC;MACD,IAAI,CAACM,qCAAqC,GAAGC,cAAc;MAC3D,OAAOA,cAAc;;IAGvB,OAAY,IAAI,CAACD,qCAAqC;EACxD;EAEAE,4BAA4BA,CAAA;IAC1B,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU;IACjC,OAAOD,SAAS,CAACA,SAAS,CAACb,MAAM,GAAG,CAAC,CAAC;EACxC;EAEAe,gCAAgCA,CAAA;IAC9B,MAAMF,SAAS,GAAG,IAAI,CAACC,UAAU;IACjC,OAAOD,SAAS,CAACA,SAAS,CAACb,MAAM,GAAG,CAAC,CAAC;EACxC;EAEAgB,kCAAkCA,CAAA;IAChC,MAAMC,eAAe,GAAG,IAAI,CAACC,qBAAqB;IAClD,OAAOD,eAAe,CAACA,eAAe,CAACjB,MAAM,GAAG,CAAC,CAAC;EACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}