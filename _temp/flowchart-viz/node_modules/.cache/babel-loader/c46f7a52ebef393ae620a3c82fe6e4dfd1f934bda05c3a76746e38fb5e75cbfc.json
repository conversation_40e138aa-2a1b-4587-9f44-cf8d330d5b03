{"ast": null, "code": "\"use strict\";\n\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CancellationTokenSource = exports.CancellationToken = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst events_1 = require(\"./events\");\nvar CancellationToken;\n(function (CancellationToken) {\n  CancellationToken.None = Object.freeze({\n    isCancellationRequested: false,\n    onCancellationRequested: events_1.Event.None\n  });\n  CancellationToken.Cancelled = Object.freeze({\n    isCancellationRequested: true,\n    onCancellationRequested: events_1.Event.None\n  });\n  function is(value) {\n    const candidate = value;\n    return candidate && (candidate === CancellationToken.None || candidate === CancellationToken.Cancelled || Is.boolean(candidate.isCancellationRequested) && !!candidate.onCancellationRequested);\n  }\n  CancellationToken.is = is;\n})(CancellationToken || (exports.CancellationToken = CancellationToken = {}));\nconst shortcutEvent = Object.freeze(function (callback, context) {\n  const handle = (0, ral_1.default)().timer.setTimeout(callback.bind(context), 0);\n  return {\n    dispose() {\n      handle.dispose();\n    }\n  };\n});\nclass MutableToken {\n  constructor() {\n    this._isCancelled = false;\n  }\n  cancel() {\n    if (!this._isCancelled) {\n      this._isCancelled = true;\n      if (this._emitter) {\n        this._emitter.fire(undefined);\n        this.dispose();\n      }\n    }\n  }\n  get isCancellationRequested() {\n    return this._isCancelled;\n  }\n  get onCancellationRequested() {\n    if (this._isCancelled) {\n      return shortcutEvent;\n    }\n    if (!this._emitter) {\n      this._emitter = new events_1.Emitter();\n    }\n    return this._emitter.event;\n  }\n  dispose() {\n    if (this._emitter) {\n      this._emitter.dispose();\n      this._emitter = undefined;\n    }\n  }\n}\nclass CancellationTokenSource {\n  get token() {\n    if (!this._token) {\n      // be lazy and create the token only when\n      // actually needed\n      this._token = new MutableToken();\n    }\n    return this._token;\n  }\n  cancel() {\n    if (!this._token) {\n      // save an object by returning the default\n      // cancelled token when cancellation happens\n      // before someone asks for the token\n      this._token = CancellationToken.Cancelled;\n    } else {\n      this._token.cancel();\n    }\n  }\n  dispose() {\n    if (!this._token) {\n      // ensure to initialize with an empty token if we had none\n      this._token = CancellationToken.None;\n    } else if (this._token instanceof MutableToken) {\n      // actually dispose\n      this._token.dispose();\n    }\n  }\n}\nexports.CancellationTokenSource = CancellationTokenSource;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "CancellationTokenSource", "CancellationToken", "ral_1", "require", "Is", "events_1", "None", "freeze", "isCancellationRequested", "onCancellationRequested", "Event", "Cancelled", "is", "candidate", "boolean", "shortcutEvent", "callback", "context", "handle", "default", "timer", "setTimeout", "bind", "dispose", "MutableToken", "constructor", "_isCancelled", "cancel", "_emitter", "fire", "undefined", "Emitter", "event", "token", "_token"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-jsonrpc/lib/common/cancellation.js"], "sourcesContent": ["\"use strict\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CancellationTokenSource = exports.CancellationToken = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst events_1 = require(\"./events\");\nvar CancellationToken;\n(function (CancellationToken) {\n    CancellationToken.None = Object.freeze({\n        isCancellationRequested: false,\n        onCancellationRequested: events_1.Event.None\n    });\n    CancellationToken.Cancelled = Object.freeze({\n        isCancellationRequested: true,\n        onCancellationRequested: events_1.Event.None\n    });\n    function is(value) {\n        const candidate = value;\n        return candidate && (candidate === CancellationToken.None\n            || candidate === CancellationToken.Cancelled\n            || (Is.boolean(candidate.isCancellationRequested) && !!candidate.onCancellationRequested));\n    }\n    CancellationToken.is = is;\n})(CancellationToken || (exports.CancellationToken = CancellationToken = {}));\nconst shortcutEvent = Object.freeze(function (callback, context) {\n    const handle = (0, ral_1.default)().timer.setTimeout(callback.bind(context), 0);\n    return { dispose() { handle.dispose(); } };\n});\nclass MutableToken {\n    constructor() {\n        this._isCancelled = false;\n    }\n    cancel() {\n        if (!this._isCancelled) {\n            this._isCancelled = true;\n            if (this._emitter) {\n                this._emitter.fire(undefined);\n                this.dispose();\n            }\n        }\n    }\n    get isCancellationRequested() {\n        return this._isCancelled;\n    }\n    get onCancellationRequested() {\n        if (this._isCancelled) {\n            return shortcutEvent;\n        }\n        if (!this._emitter) {\n            this._emitter = new events_1.Emitter();\n        }\n        return this._emitter.event;\n    }\n    dispose() {\n        if (this._emitter) {\n            this._emitter.dispose();\n            this._emitter = undefined;\n        }\n    }\n}\nclass CancellationTokenSource {\n    get token() {\n        if (!this._token) {\n            // be lazy and create the token only when\n            // actually needed\n            this._token = new MutableToken();\n        }\n        return this._token;\n    }\n    cancel() {\n        if (!this._token) {\n            // save an object by returning the default\n            // cancelled token when cancellation happens\n            // before someone asks for the token\n            this._token = CancellationToken.Cancelled;\n        }\n        else {\n            this._token.cancel();\n        }\n    }\n    dispose() {\n        if (!this._token) {\n            // ensure to initialize with an empty token if we had none\n            this._token = CancellationToken.None;\n        }\n        else if (this._token instanceof MutableToken) {\n            // actually dispose\n            this._token.dispose();\n        }\n    }\n}\nexports.CancellationTokenSource = CancellationTokenSource;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACAA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,uBAAuB,GAAGF,OAAO,CAACG,iBAAiB,GAAG,KAAK,CAAC;AACpE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAMC,EAAE,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC1B,MAAME,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;AACpC,IAAIF,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACK,IAAI,GAAGV,MAAM,CAACW,MAAM,CAAC;IACnCC,uBAAuB,EAAE,KAAK;IAC9BC,uBAAuB,EAAEJ,QAAQ,CAACK,KAAK,CAACJ;EAC5C,CAAC,CAAC;EACFL,iBAAiB,CAACU,SAAS,GAAGf,MAAM,CAACW,MAAM,CAAC;IACxCC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAEJ,QAAQ,CAACK,KAAK,CAACJ;EAC5C,CAAC,CAAC;EACF,SAASM,EAAEA,CAACb,KAAK,EAAE;IACf,MAAMc,SAAS,GAAGd,KAAK;IACvB,OAAOc,SAAS,KAAKA,SAAS,KAAKZ,iBAAiB,CAACK,IAAI,IAClDO,SAAS,KAAKZ,iBAAiB,CAACU,SAAS,IACxCP,EAAE,CAACU,OAAO,CAACD,SAAS,CAACL,uBAAuB,CAAC,IAAI,CAAC,CAACK,SAAS,CAACJ,uBAAwB,CAAC;EAClG;EACAR,iBAAiB,CAACW,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAEX,iBAAiB,KAAKH,OAAO,CAACG,iBAAiB,GAAGA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,MAAMc,aAAa,GAAGnB,MAAM,CAACW,MAAM,CAAC,UAAUS,QAAQ,EAAEC,OAAO,EAAE;EAC7D,MAAMC,MAAM,GAAG,CAAC,CAAC,EAAEhB,KAAK,CAACiB,OAAO,EAAE,CAAC,CAACC,KAAK,CAACC,UAAU,CAACL,QAAQ,CAACM,IAAI,CAACL,OAAO,CAAC,EAAE,CAAC,CAAC;EAC/E,OAAO;IAAEM,OAAOA,CAAA,EAAG;MAAEL,MAAM,CAACK,OAAO,CAAC,CAAC;IAAE;EAAE,CAAC;AAC9C,CAAC,CAAC;AACF,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACD,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,IAAI,CAACE,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC;QAC7B,IAAI,CAACP,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA,IAAIf,uBAAuBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACkB,YAAY;EAC5B;EACA,IAAIjB,uBAAuBA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACiB,YAAY,EAAE;MACnB,OAAOX,aAAa;IACxB;IACA,IAAI,CAAC,IAAI,CAACa,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAIvB,QAAQ,CAAC0B,OAAO,CAAC,CAAC;IAC1C;IACA,OAAO,IAAI,CAACH,QAAQ,CAACI,KAAK;EAC9B;EACAT,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACK,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACL,OAAO,CAAC,CAAC;MACvB,IAAI,CAACK,QAAQ,GAAGE,SAAS;IAC7B;EACJ;AACJ;AACA,MAAM9B,uBAAuB,CAAC;EAC1B,IAAIiC,KAAKA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd;MACA;MACA,IAAI,CAACA,MAAM,GAAG,IAAIV,YAAY,CAAC,CAAC;IACpC;IACA,OAAO,IAAI,CAACU,MAAM;EACtB;EACAP,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACO,MAAM,EAAE;MACd;MACA;MACA;MACA,IAAI,CAACA,MAAM,GAAGjC,iBAAiB,CAACU,SAAS;IAC7C,CAAC,MACI;MACD,IAAI,CAACuB,MAAM,CAACP,MAAM,CAAC,CAAC;IACxB;EACJ;EACAJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACW,MAAM,EAAE;MACd;MACA,IAAI,CAACA,MAAM,GAAGjC,iBAAiB,CAACK,IAAI;IACxC,CAAC,MACI,IAAI,IAAI,CAAC4B,MAAM,YAAYV,YAAY,EAAE;MAC1C;MACA,IAAI,CAACU,MAAM,CAACX,OAAO,CAAC,CAAC;IACzB;EACJ;AACJ;AACAzB,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}