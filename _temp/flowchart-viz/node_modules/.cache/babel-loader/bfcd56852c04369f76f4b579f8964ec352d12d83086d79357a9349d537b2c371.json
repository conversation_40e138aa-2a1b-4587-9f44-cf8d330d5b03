{"ast": null, "code": "/* IMPORT */\nimport _ from '../utils/index.js';\nimport Type from './type.js';\nimport { TYPE } from '../constants.js';\n/* MAIN */\nclass Channels {\n  /* CONSTRUCTOR */\n  constructor(data, color) {\n    this.color = color;\n    this.changed = false;\n    this.data = data; //TSC\n    this.type = new Type();\n  }\n  /* API */\n  set(data, color) {\n    this.color = color;\n    this.changed = false;\n    this.data = data; //TSC\n    this.type.type = TYPE.ALL;\n    return this;\n  }\n  /* HELPERS */\n  _ensureHSL() {\n    const data = this.data;\n    const {\n      h,\n      s,\n      l\n    } = data;\n    if (h === undefined) data.h = _.channel.rgb2hsl(data, 'h');\n    if (s === undefined) data.s = _.channel.rgb2hsl(data, 's');\n    if (l === undefined) data.l = _.channel.rgb2hsl(data, 'l');\n  }\n  _ensureRGB() {\n    const data = this.data;\n    const {\n      r,\n      g,\n      b\n    } = data;\n    if (r === undefined) data.r = _.channel.hsl2rgb(data, 'r');\n    if (g === undefined) data.g = _.channel.hsl2rgb(data, 'g');\n    if (b === undefined) data.b = _.channel.hsl2rgb(data, 'b');\n  }\n  /* GETTERS */\n  get r() {\n    const data = this.data;\n    const r = data.r;\n    if (!this.type.is(TYPE.HSL) && r !== undefined) return r;\n    this._ensureHSL();\n    return _.channel.hsl2rgb(data, 'r');\n  }\n  get g() {\n    const data = this.data;\n    const g = data.g;\n    if (!this.type.is(TYPE.HSL) && g !== undefined) return g;\n    this._ensureHSL();\n    return _.channel.hsl2rgb(data, 'g');\n  }\n  get b() {\n    const data = this.data;\n    const b = data.b;\n    if (!this.type.is(TYPE.HSL) && b !== undefined) return b;\n    this._ensureHSL();\n    return _.channel.hsl2rgb(data, 'b');\n  }\n  get h() {\n    const data = this.data;\n    const h = data.h;\n    if (!this.type.is(TYPE.RGB) && h !== undefined) return h;\n    this._ensureRGB();\n    return _.channel.rgb2hsl(data, 'h');\n  }\n  get s() {\n    const data = this.data;\n    const s = data.s;\n    if (!this.type.is(TYPE.RGB) && s !== undefined) return s;\n    this._ensureRGB();\n    return _.channel.rgb2hsl(data, 's');\n  }\n  get l() {\n    const data = this.data;\n    const l = data.l;\n    if (!this.type.is(TYPE.RGB) && l !== undefined) return l;\n    this._ensureRGB();\n    return _.channel.rgb2hsl(data, 'l');\n  }\n  get a() {\n    return this.data.a;\n  }\n  /* SETTERS */\n  set r(r) {\n    this.type.set(TYPE.RGB);\n    this.changed = true;\n    this.data.r = r;\n  }\n  set g(g) {\n    this.type.set(TYPE.RGB);\n    this.changed = true;\n    this.data.g = g;\n  }\n  set b(b) {\n    this.type.set(TYPE.RGB);\n    this.changed = true;\n    this.data.b = b;\n  }\n  set h(h) {\n    this.type.set(TYPE.HSL);\n    this.changed = true;\n    this.data.h = h;\n  }\n  set s(s) {\n    this.type.set(TYPE.HSL);\n    this.changed = true;\n    this.data.s = s;\n  }\n  set l(l) {\n    this.type.set(TYPE.HSL);\n    this.changed = true;\n    this.data.l = l;\n  }\n  set a(a) {\n    this.changed = true;\n    this.data.a = a;\n  }\n}\n/* EXPORT */\nexport default Channels;", "map": {"version": 3, "names": ["_", "Type", "TYPE", "Channels", "constructor", "data", "color", "changed", "type", "set", "ALL", "_ensureHSL", "h", "s", "l", "undefined", "channel", "rgb2hsl", "_ensureRGB", "r", "g", "b", "hsl2rgb", "is", "HSL", "RGB", "a"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/channels/index.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Type from './type.js';\nimport { TYPE } from '../constants.js';\n/* MAIN */\nclass Channels {\n    /* CONSTRUCTOR */\n    constructor(data, color) {\n        this.color = color;\n        this.changed = false;\n        this.data = data; //TSC\n        this.type = new Type();\n    }\n    /* API */\n    set(data, color) {\n        this.color = color;\n        this.changed = false;\n        this.data = data; //TSC\n        this.type.type = TYPE.ALL;\n        return this;\n    }\n    /* HELPERS */\n    _ensureHSL() {\n        const data = this.data;\n        const { h, s, l } = data;\n        if (h === undefined)\n            data.h = _.channel.rgb2hsl(data, 'h');\n        if (s === undefined)\n            data.s = _.channel.rgb2hsl(data, 's');\n        if (l === undefined)\n            data.l = _.channel.rgb2hsl(data, 'l');\n    }\n    _ensureRGB() {\n        const data = this.data;\n        const { r, g, b } = data;\n        if (r === undefined)\n            data.r = _.channel.hsl2rgb(data, 'r');\n        if (g === undefined)\n            data.g = _.channel.hsl2rgb(data, 'g');\n        if (b === undefined)\n            data.b = _.channel.hsl2rgb(data, 'b');\n    }\n    /* GETTERS */\n    get r() {\n        const data = this.data;\n        const r = data.r;\n        if (!this.type.is(TYPE.HSL) && r !== undefined)\n            return r;\n        this._ensureHSL();\n        return _.channel.hsl2rgb(data, 'r');\n    }\n    get g() {\n        const data = this.data;\n        const g = data.g;\n        if (!this.type.is(TYPE.HSL) && g !== undefined)\n            return g;\n        this._ensureHSL();\n        return _.channel.hsl2rgb(data, 'g');\n    }\n    get b() {\n        const data = this.data;\n        const b = data.b;\n        if (!this.type.is(TYPE.HSL) && b !== undefined)\n            return b;\n        this._ensureHSL();\n        return _.channel.hsl2rgb(data, 'b');\n    }\n    get h() {\n        const data = this.data;\n        const h = data.h;\n        if (!this.type.is(TYPE.RGB) && h !== undefined)\n            return h;\n        this._ensureRGB();\n        return _.channel.rgb2hsl(data, 'h');\n    }\n    get s() {\n        const data = this.data;\n        const s = data.s;\n        if (!this.type.is(TYPE.RGB) && s !== undefined)\n            return s;\n        this._ensureRGB();\n        return _.channel.rgb2hsl(data, 's');\n    }\n    get l() {\n        const data = this.data;\n        const l = data.l;\n        if (!this.type.is(TYPE.RGB) && l !== undefined)\n            return l;\n        this._ensureRGB();\n        return _.channel.rgb2hsl(data, 'l');\n    }\n    get a() {\n        return this.data.a;\n    }\n    /* SETTERS */\n    set r(r) {\n        this.type.set(TYPE.RGB);\n        this.changed = true;\n        this.data.r = r;\n    }\n    set g(g) {\n        this.type.set(TYPE.RGB);\n        this.changed = true;\n        this.data.g = g;\n    }\n    set b(b) {\n        this.type.set(TYPE.RGB);\n        this.changed = true;\n        this.data.b = b;\n    }\n    set h(h) {\n        this.type.set(TYPE.HSL);\n        this.changed = true;\n        this.data.h = h;\n    }\n    set s(s) {\n        this.type.set(TYPE.HSL);\n        this.changed = true;\n        this.data.s = s;\n    }\n    set l(l) {\n        this.type.set(TYPE.HSL);\n        this.changed = true;\n        this.data.l = l;\n    }\n    set a(a) {\n        this.changed = true;\n        this.data.a = a;\n    }\n}\n/* EXPORT */\nexport default Channels;\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,mBAAmB;AACjC,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAASC,IAAI,QAAQ,iBAAiB;AACtC;AACA,MAAMC,QAAQ,CAAC;EACX;EACAC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACF,IAAI,GAAGA,IAAI,CAAC,CAAC;IAClB,IAAI,CAACG,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC;EAC1B;EACA;EACAQ,GAAGA,CAACJ,IAAI,EAAEC,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACF,IAAI,GAAGA,IAAI,CAAC,CAAC;IAClB,IAAI,CAACG,IAAI,CAACA,IAAI,GAAGN,IAAI,CAACQ,GAAG;IACzB,OAAO,IAAI;EACf;EACA;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMN,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM;MAAEO,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGT,IAAI;IACxB,IAAIO,CAAC,KAAKG,SAAS,EACfV,IAAI,CAACO,CAAC,GAAGZ,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;IACzC,IAAIQ,CAAC,KAAKE,SAAS,EACfV,IAAI,CAACQ,CAAC,GAAGb,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;IACzC,IAAIS,CAAC,KAAKC,SAAS,EACfV,IAAI,CAACS,CAAC,GAAGd,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;EAC7C;EACAa,UAAUA,CAAA,EAAG;IACT,MAAMb,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM;MAAEc,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGhB,IAAI;IACxB,IAAIc,CAAC,KAAKJ,SAAS,EACfV,IAAI,CAACc,CAAC,GAAGnB,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;IACzC,IAAIe,CAAC,KAAKL,SAAS,EACfV,IAAI,CAACe,CAAC,GAAGpB,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;IACzC,IAAIgB,CAAC,KAAKN,SAAS,EACfV,IAAI,CAACgB,CAAC,GAAGrB,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;EAC7C;EACA;EACA,IAAIc,CAACA,CAAA,EAAG;IACJ,MAAMd,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMc,CAAC,GAAGd,IAAI,CAACc,CAAC;IAChB,IAAI,CAAC,IAAI,CAACX,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACsB,GAAG,CAAC,IAAIL,CAAC,KAAKJ,SAAS,EAC1C,OAAOI,CAAC;IACZ,IAAI,CAACR,UAAU,CAAC,CAAC;IACjB,OAAOX,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIe,CAACA,CAAA,EAAG;IACJ,MAAMf,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMe,CAAC,GAAGf,IAAI,CAACe,CAAC;IAChB,IAAI,CAAC,IAAI,CAACZ,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACsB,GAAG,CAAC,IAAIJ,CAAC,KAAKL,SAAS,EAC1C,OAAOK,CAAC;IACZ,IAAI,CAACT,UAAU,CAAC,CAAC;IACjB,OAAOX,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIgB,CAACA,CAAA,EAAG;IACJ,MAAMhB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMgB,CAAC,GAAGhB,IAAI,CAACgB,CAAC;IAChB,IAAI,CAAC,IAAI,CAACb,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACsB,GAAG,CAAC,IAAIH,CAAC,KAAKN,SAAS,EAC1C,OAAOM,CAAC;IACZ,IAAI,CAACV,UAAU,CAAC,CAAC;IACjB,OAAOX,CAAC,CAACgB,OAAO,CAACM,OAAO,CAACjB,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIO,CAACA,CAAA,EAAG;IACJ,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMO,CAAC,GAAGP,IAAI,CAACO,CAAC;IAChB,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACuB,GAAG,CAAC,IAAIb,CAAC,KAAKG,SAAS,EAC1C,OAAOH,CAAC;IACZ,IAAI,CAACM,UAAU,CAAC,CAAC;IACjB,OAAOlB,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIQ,CAACA,CAAA,EAAG;IACJ,MAAMR,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMQ,CAAC,GAAGR,IAAI,CAACQ,CAAC;IAChB,IAAI,CAAC,IAAI,CAACL,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACuB,GAAG,CAAC,IAAIZ,CAAC,KAAKE,SAAS,EAC1C,OAAOF,CAAC;IACZ,IAAI,CAACK,UAAU,CAAC,CAAC;IACjB,OAAOlB,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIS,CAACA,CAAA,EAAG;IACJ,MAAMT,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMS,CAAC,GAAGT,IAAI,CAACS,CAAC;IAChB,IAAI,CAAC,IAAI,CAACN,IAAI,CAACe,EAAE,CAACrB,IAAI,CAACuB,GAAG,CAAC,IAAIX,CAAC,KAAKC,SAAS,EAC1C,OAAOD,CAAC;IACZ,IAAI,CAACI,UAAU,CAAC,CAAC;IACjB,OAAOlB,CAAC,CAACgB,OAAO,CAACC,OAAO,CAACZ,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,IAAIqB,CAACA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACrB,IAAI,CAACqB,CAAC;EACtB;EACA;EACA,IAAIP,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACX,IAAI,CAACC,GAAG,CAACP,IAAI,CAACuB,GAAG,CAAC;IACvB,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACc,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIC,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACZ,IAAI,CAACC,GAAG,CAACP,IAAI,CAACuB,GAAG,CAAC;IACvB,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACe,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIC,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACb,IAAI,CAACC,GAAG,CAACP,IAAI,CAACuB,GAAG,CAAC;IACvB,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACgB,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIT,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACJ,IAAI,CAACC,GAAG,CAACP,IAAI,CAACsB,GAAG,CAAC;IACvB,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACO,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIC,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACL,IAAI,CAACC,GAAG,CAACP,IAAI,CAACsB,GAAG,CAAC;IACvB,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACQ,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIC,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACN,IAAI,CAACC,GAAG,CAACP,IAAI,CAACsB,GAAG,CAAC;IACvB,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACS,CAAC,GAAGA,CAAC;EACnB;EACA,IAAIY,CAACA,CAACA,CAAC,EAAE;IACL,IAAI,CAACnB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,IAAI,CAACqB,CAAC,GAAGA,CAAC;EACnB;AACJ;AACA;AACA,eAAevB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}