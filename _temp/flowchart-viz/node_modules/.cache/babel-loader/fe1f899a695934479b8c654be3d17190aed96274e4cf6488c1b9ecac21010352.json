{"ast": null, "code": "/* IMPORT */\nimport channel from './channel.js';\n/* MAIN */\nconst blue = color => {\n  return channel(color, 'b');\n};\n/* EXPORT */\nexport default blue;", "map": {"version": 3, "names": ["channel", "blue", "color"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/blue.js"], "sourcesContent": ["/* IMPORT */\nimport channel from './channel.js';\n/* MAIN */\nconst blue = (color) => {\n    return channel(color, 'b');\n};\n/* EXPORT */\nexport default blue;\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,cAAc;AAClC;AACA,MAAMC,IAAI,GAAIC,KAAK,IAAK;EACpB,OAAOF,OAAO,CAACE,KAAK,EAAE,GAAG,CAAC;AAC9B,CAAC;AACD;AACA,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}