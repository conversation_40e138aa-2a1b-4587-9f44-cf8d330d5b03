{"ast": null, "code": "import { includes, values } from \"lodash-es\";\nimport { isRecognitionException } from \"../../exceptions_public.js\";\nimport { DEFAULT_RULE_CONFIG, ParserDefinitionErrorType } from \"../parser.js\";\nimport { defaultGrammarValidatorErrorProvider } from \"../../errors_public.js\";\nimport { validateRuleIsOverridden } from \"../../grammar/checks.js\";\nimport { serializeGrammar } from \"@chevrotain/gast\";\n/**\n * This trait is responsible for implementing the public API\n * for defining Chevrotain parsers, i.e:\n * - CONSUME\n * - RULE\n * - OPTION\n * - ...\n */\nexport class RecognizerApi {\n  ACTION(impl) {\n    return impl.call(this);\n  }\n  consume(idx, tokType, options) {\n    return this.consumeInternal(tokType, idx, options);\n  }\n  subrule(idx, ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, idx, options);\n  }\n  option(idx, actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, idx);\n  }\n  or(idx, altsOrOpts) {\n    return this.orInternal(altsOrOpts, idx);\n  }\n  many(idx, actionORMethodDef) {\n    return this.manyInternal(idx, actionORMethodDef);\n  }\n  atLeastOne(idx, actionORMethodDef) {\n    return this.atLeastOneInternal(idx, actionORMethodDef);\n  }\n  CONSUME(tokType, options) {\n    return this.consumeInternal(tokType, 0, options);\n  }\n  CONSUME1(tokType, options) {\n    return this.consumeInternal(tokType, 1, options);\n  }\n  CONSUME2(tokType, options) {\n    return this.consumeInternal(tokType, 2, options);\n  }\n  CONSUME3(tokType, options) {\n    return this.consumeInternal(tokType, 3, options);\n  }\n  CONSUME4(tokType, options) {\n    return this.consumeInternal(tokType, 4, options);\n  }\n  CONSUME5(tokType, options) {\n    return this.consumeInternal(tokType, 5, options);\n  }\n  CONSUME6(tokType, options) {\n    return this.consumeInternal(tokType, 6, options);\n  }\n  CONSUME7(tokType, options) {\n    return this.consumeInternal(tokType, 7, options);\n  }\n  CONSUME8(tokType, options) {\n    return this.consumeInternal(tokType, 8, options);\n  }\n  CONSUME9(tokType, options) {\n    return this.consumeInternal(tokType, 9, options);\n  }\n  SUBRULE(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 0, options);\n  }\n  SUBRULE1(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 1, options);\n  }\n  SUBRULE2(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 2, options);\n  }\n  SUBRULE3(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 3, options);\n  }\n  SUBRULE4(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 4, options);\n  }\n  SUBRULE5(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 5, options);\n  }\n  SUBRULE6(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 6, options);\n  }\n  SUBRULE7(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 7, options);\n  }\n  SUBRULE8(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 8, options);\n  }\n  SUBRULE9(ruleToCall, options) {\n    return this.subruleInternal(ruleToCall, 9, options);\n  }\n  OPTION(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 0);\n  }\n  OPTION1(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 1);\n  }\n  OPTION2(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 2);\n  }\n  OPTION3(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 3);\n  }\n  OPTION4(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 4);\n  }\n  OPTION5(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 5);\n  }\n  OPTION6(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 6);\n  }\n  OPTION7(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 7);\n  }\n  OPTION8(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 8);\n  }\n  OPTION9(actionORMethodDef) {\n    return this.optionInternal(actionORMethodDef, 9);\n  }\n  OR(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 0);\n  }\n  OR1(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 1);\n  }\n  OR2(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 2);\n  }\n  OR3(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 3);\n  }\n  OR4(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 4);\n  }\n  OR5(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 5);\n  }\n  OR6(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 6);\n  }\n  OR7(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 7);\n  }\n  OR8(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 8);\n  }\n  OR9(altsOrOpts) {\n    return this.orInternal(altsOrOpts, 9);\n  }\n  MANY(actionORMethodDef) {\n    this.manyInternal(0, actionORMethodDef);\n  }\n  MANY1(actionORMethodDef) {\n    this.manyInternal(1, actionORMethodDef);\n  }\n  MANY2(actionORMethodDef) {\n    this.manyInternal(2, actionORMethodDef);\n  }\n  MANY3(actionORMethodDef) {\n    this.manyInternal(3, actionORMethodDef);\n  }\n  MANY4(actionORMethodDef) {\n    this.manyInternal(4, actionORMethodDef);\n  }\n  MANY5(actionORMethodDef) {\n    this.manyInternal(5, actionORMethodDef);\n  }\n  MANY6(actionORMethodDef) {\n    this.manyInternal(6, actionORMethodDef);\n  }\n  MANY7(actionORMethodDef) {\n    this.manyInternal(7, actionORMethodDef);\n  }\n  MANY8(actionORMethodDef) {\n    this.manyInternal(8, actionORMethodDef);\n  }\n  MANY9(actionORMethodDef) {\n    this.manyInternal(9, actionORMethodDef);\n  }\n  MANY_SEP(options) {\n    this.manySepFirstInternal(0, options);\n  }\n  MANY_SEP1(options) {\n    this.manySepFirstInternal(1, options);\n  }\n  MANY_SEP2(options) {\n    this.manySepFirstInternal(2, options);\n  }\n  MANY_SEP3(options) {\n    this.manySepFirstInternal(3, options);\n  }\n  MANY_SEP4(options) {\n    this.manySepFirstInternal(4, options);\n  }\n  MANY_SEP5(options) {\n    this.manySepFirstInternal(5, options);\n  }\n  MANY_SEP6(options) {\n    this.manySepFirstInternal(6, options);\n  }\n  MANY_SEP7(options) {\n    this.manySepFirstInternal(7, options);\n  }\n  MANY_SEP8(options) {\n    this.manySepFirstInternal(8, options);\n  }\n  MANY_SEP9(options) {\n    this.manySepFirstInternal(9, options);\n  }\n  AT_LEAST_ONE(actionORMethodDef) {\n    this.atLeastOneInternal(0, actionORMethodDef);\n  }\n  AT_LEAST_ONE1(actionORMethodDef) {\n    return this.atLeastOneInternal(1, actionORMethodDef);\n  }\n  AT_LEAST_ONE2(actionORMethodDef) {\n    this.atLeastOneInternal(2, actionORMethodDef);\n  }\n  AT_LEAST_ONE3(actionORMethodDef) {\n    this.atLeastOneInternal(3, actionORMethodDef);\n  }\n  AT_LEAST_ONE4(actionORMethodDef) {\n    this.atLeastOneInternal(4, actionORMethodDef);\n  }\n  AT_LEAST_ONE5(actionORMethodDef) {\n    this.atLeastOneInternal(5, actionORMethodDef);\n  }\n  AT_LEAST_ONE6(actionORMethodDef) {\n    this.atLeastOneInternal(6, actionORMethodDef);\n  }\n  AT_LEAST_ONE7(actionORMethodDef) {\n    this.atLeastOneInternal(7, actionORMethodDef);\n  }\n  AT_LEAST_ONE8(actionORMethodDef) {\n    this.atLeastOneInternal(8, actionORMethodDef);\n  }\n  AT_LEAST_ONE9(actionORMethodDef) {\n    this.atLeastOneInternal(9, actionORMethodDef);\n  }\n  AT_LEAST_ONE_SEP(options) {\n    this.atLeastOneSepFirstInternal(0, options);\n  }\n  AT_LEAST_ONE_SEP1(options) {\n    this.atLeastOneSepFirstInternal(1, options);\n  }\n  AT_LEAST_ONE_SEP2(options) {\n    this.atLeastOneSepFirstInternal(2, options);\n  }\n  AT_LEAST_ONE_SEP3(options) {\n    this.atLeastOneSepFirstInternal(3, options);\n  }\n  AT_LEAST_ONE_SEP4(options) {\n    this.atLeastOneSepFirstInternal(4, options);\n  }\n  AT_LEAST_ONE_SEP5(options) {\n    this.atLeastOneSepFirstInternal(5, options);\n  }\n  AT_LEAST_ONE_SEP6(options) {\n    this.atLeastOneSepFirstInternal(6, options);\n  }\n  AT_LEAST_ONE_SEP7(options) {\n    this.atLeastOneSepFirstInternal(7, options);\n  }\n  AT_LEAST_ONE_SEP8(options) {\n    this.atLeastOneSepFirstInternal(8, options);\n  }\n  AT_LEAST_ONE_SEP9(options) {\n    this.atLeastOneSepFirstInternal(9, options);\n  }\n  RULE(name, implementation, config = DEFAULT_RULE_CONFIG) {\n    if (includes(this.definedRulesNames, name)) {\n      const errMsg = defaultGrammarValidatorErrorProvider.buildDuplicateRuleNameError({\n        topLevelRule: name,\n        grammarName: this.className\n      });\n      const error = {\n        message: errMsg,\n        type: ParserDefinitionErrorType.DUPLICATE_RULE_NAME,\n        ruleName: name\n      };\n      this.definitionErrors.push(error);\n    }\n    this.definedRulesNames.push(name);\n    const ruleImplementation = this.defineRule(name, implementation, config);\n    this[name] = ruleImplementation;\n    return ruleImplementation;\n  }\n  OVERRIDE_RULE(name, impl, config = DEFAULT_RULE_CONFIG) {\n    const ruleErrors = validateRuleIsOverridden(name, this.definedRulesNames, this.className);\n    this.definitionErrors = this.definitionErrors.concat(ruleErrors);\n    const ruleImplementation = this.defineRule(name, impl, config);\n    this[name] = ruleImplementation;\n    return ruleImplementation;\n  }\n  BACKTRACK(grammarRule, args) {\n    return function () {\n      // save org state\n      this.isBackTrackingStack.push(1);\n      const orgState = this.saveRecogState();\n      try {\n        grammarRule.apply(this, args);\n        // if no exception was thrown we have succeed parsing the rule.\n        return true;\n      } catch (e) {\n        if (isRecognitionException(e)) {\n          return false;\n        } else {\n          throw e;\n        }\n      } finally {\n        this.reloadRecogState(orgState);\n        this.isBackTrackingStack.pop();\n      }\n    };\n  }\n  // GAST export APIs\n  getGAstProductions() {\n    return this.gastProductionsCache;\n  }\n  getSerializedGastProductions() {\n    return serializeGrammar(values(this.gastProductionsCache));\n  }\n}", "map": {"version": 3, "names": ["includes", "values", "isRecognitionException", "DEFAULT_RULE_CONFIG", "ParserDefinitionErrorType", "defaultGrammarValidatorErrorProvider", "validateRuleIsOverridden", "serializeGrammar", "RecognizerApi", "ACTION", "impl", "call", "consume", "idx", "tokType", "options", "consumeInternal", "subrule", "ruleToCall", "subruleInternal", "option", "actionORMethodDef", "optionInternal", "or", "altsOrOpts", "orInternal", "many", "manyInternal", "atLeastOne", "atLeastOneInternal", "CONSUME", "CONSUME1", "CONSUME2", "CONSUME3", "CONSUME4", "CONSUME5", "CONSUME6", "CONSUME7", "CONSUME8", "CONSUME9", "SUBRULE", "SUBRULE1", "SUBRULE2", "SUBRULE3", "SUBRULE4", "SUBRULE5", "SUBRULE6", "SUBRULE7", "SUBRULE8", "SUBRULE9", "OPTION", "OPTION1", "OPTION2", "OPTION3", "OPTION4", "OPTION5", "OPTION6", "OPTION7", "OPTION8", "OPTION9", "OR", "OR1", "OR2", "OR3", "OR4", "OR5", "OR6", "OR7", "OR8", "OR9", "MANY", "MANY1", "MANY2", "MANY3", "MANY4", "MANY5", "MANY6", "MANY7", "MANY8", "MANY9", "MANY_SEP", "manySepFirstInternal", "MANY_SEP1", "MANY_SEP2", "MANY_SEP3", "MANY_SEP4", "MANY_SEP5", "MANY_SEP6", "MANY_SEP7", "MANY_SEP8", "MANY_SEP9", "AT_LEAST_ONE", "AT_LEAST_ONE1", "AT_LEAST_ONE2", "AT_LEAST_ONE3", "AT_LEAST_ONE4", "AT_LEAST_ONE5", "AT_LEAST_ONE6", "AT_LEAST_ONE7", "AT_LEAST_ONE8", "AT_LEAST_ONE9", "AT_LEAST_ONE_SEP", "atLeastOneSepFirstInternal", "AT_LEAST_ONE_SEP1", "AT_LEAST_ONE_SEP2", "AT_LEAST_ONE_SEP3", "AT_LEAST_ONE_SEP4", "AT_LEAST_ONE_SEP5", "AT_LEAST_ONE_SEP6", "AT_LEAST_ONE_SEP7", "AT_LEAST_ONE_SEP8", "AT_LEAST_ONE_SEP9", "RULE", "name", "implementation", "config", "definedRulesNames", "errMsg", "buildDuplicateRuleNameError", "topLevelRule", "grammarName", "className", "error", "message", "type", "DUPLICATE_RULE_NAME", "ruleName", "definitionErrors", "push", "ruleImplementation", "defineRule", "OVERRIDE_RULE", "ruleErrors", "concat", "BACKTRACK", "grammarRule", "args", "isBackTrackingStack", "orgState", "saveRecogState", "apply", "e", "reloadRecogState", "pop", "getGAstProductions", "gastProductionsCache", "getSerializedGastProductions"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/recognizer_api.ts"], "sourcesContent": ["import {\n  AtLeastOneSepMethodOpts,\n  ConsumeMethodOpts,\n  DSLMethodOpts,\n  DSLMethodOptsWithErr,\n  GrammarAction,\n  IOrAlt,\n  IRuleConfig,\n  ISerializedGast,\n  IToken,\n  ManySepMethodOpts,\n  OrMethodOpts,\n  SubruleMethodOpts,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { includes, values } from \"lodash-es\";\nimport { isRecognitionException } from \"../../exceptions_public.js\";\nimport { DEFAULT_RULE_CONFIG, ParserDefinitionErrorType } from \"../parser.js\";\nimport { defaultGrammarValidatorErrorProvider } from \"../../errors_public.js\";\nimport { validateRuleIsOverridden } from \"../../grammar/checks.js\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport { Rule, serializeGrammar } from \"@chevrotain/gast\";\nimport { IParserDefinitionError } from \"../../grammar/types.js\";\nimport { ParserMethodInternal } from \"../types.js\";\n\n/**\n * This trait is responsible for implementing the public API\n * for defining Chevrotain parsers, i.e:\n * - CONSUME\n * - RULE\n * - OPTION\n * - ...\n */\nexport class RecognizerApi {\n  ACTION<T>(this: MixedInParser, impl: () => T): T {\n    return impl.call(this);\n  }\n\n  consume(\n    this: MixedInParser,\n    idx: number,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, idx, options);\n  }\n\n  subrule<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    idx: number,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, idx, options);\n  }\n\n  option<OUT>(\n    this: MixedInParser,\n    idx: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, idx);\n  }\n\n  or(\n    this: MixedInParser,\n    idx: number,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<any>,\n  ): any {\n    return this.orInternal(altsOrOpts, idx);\n  }\n\n  many(\n    this: MixedInParser,\n    idx: number,\n    actionORMethodDef: GrammarAction<any> | DSLMethodOpts<any>,\n  ): void {\n    return this.manyInternal(idx, actionORMethodDef);\n  }\n\n  atLeastOne(\n    this: MixedInParser,\n    idx: number,\n    actionORMethodDef: GrammarAction<any> | DSLMethodOptsWithErr<any>,\n  ): void {\n    return this.atLeastOneInternal(idx, actionORMethodDef);\n  }\n\n  CONSUME(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 0, options);\n  }\n\n  CONSUME1(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 1, options);\n  }\n\n  CONSUME2(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 2, options);\n  }\n\n  CONSUME3(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 3, options);\n  }\n\n  CONSUME4(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 4, options);\n  }\n\n  CONSUME5(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 5, options);\n  }\n\n  CONSUME6(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 6, options);\n  }\n\n  CONSUME7(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 7, options);\n  }\n\n  CONSUME8(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 8, options);\n  }\n\n  CONSUME9(\n    this: MixedInParser,\n    tokType: TokenType,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    return this.consumeInternal(tokType, 9, options);\n  }\n\n  SUBRULE<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 0, options);\n  }\n\n  SUBRULE1<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 1, options);\n  }\n\n  SUBRULE2<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 2, options);\n  }\n\n  SUBRULE3<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 3, options);\n  }\n\n  SUBRULE4<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 4, options);\n  }\n\n  SUBRULE5<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 5, options);\n  }\n\n  SUBRULE6<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 6, options);\n  }\n\n  SUBRULE7<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 7, options);\n  }\n\n  SUBRULE8<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 8, options);\n  }\n\n  SUBRULE9<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R {\n    return this.subruleInternal(ruleToCall, 9, options);\n  }\n\n  OPTION<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 0);\n  }\n\n  OPTION1<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 1);\n  }\n\n  OPTION2<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 2);\n  }\n\n  OPTION3<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 3);\n  }\n\n  OPTION4<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 4);\n  }\n\n  OPTION5<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 5);\n  }\n\n  OPTION6<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 6);\n  }\n\n  OPTION7<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 7);\n  }\n\n  OPTION8<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 8);\n  }\n\n  OPTION9<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): OUT | undefined {\n    return this.optionInternal(actionORMethodDef, 9);\n  }\n\n  OR<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 0);\n  }\n\n  OR1<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 1);\n  }\n\n  OR2<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 2);\n  }\n\n  OR3<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 3);\n  }\n\n  OR4<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 4);\n  }\n\n  OR5<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 5);\n  }\n\n  OR6<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 6);\n  }\n\n  OR7<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 7);\n  }\n\n  OR8<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 8);\n  }\n\n  OR9<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n  ): T {\n    return this.orInternal(altsOrOpts, 9);\n  }\n\n  MANY<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(0, actionORMethodDef);\n  }\n\n  MANY1<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(1, actionORMethodDef);\n  }\n\n  MANY2<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(2, actionORMethodDef);\n  }\n\n  MANY3<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(3, actionORMethodDef);\n  }\n\n  MANY4<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(4, actionORMethodDef);\n  }\n\n  MANY5<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(5, actionORMethodDef);\n  }\n\n  MANY6<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(6, actionORMethodDef);\n  }\n\n  MANY7<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(7, actionORMethodDef);\n  }\n\n  MANY8<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(8, actionORMethodDef);\n  }\n\n  MANY9<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    this.manyInternal(9, actionORMethodDef);\n  }\n\n  MANY_SEP<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(0, options);\n  }\n\n  MANY_SEP1<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(1, options);\n  }\n\n  MANY_SEP2<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(2, options);\n  }\n\n  MANY_SEP3<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(3, options);\n  }\n\n  MANY_SEP4<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(4, options);\n  }\n\n  MANY_SEP5<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(5, options);\n  }\n\n  MANY_SEP6<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(6, options);\n  }\n\n  MANY_SEP7<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(7, options);\n  }\n\n  MANY_SEP8<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(8, options);\n  }\n\n  MANY_SEP9<OUT>(this: MixedInParser, options: ManySepMethodOpts<OUT>): void {\n    this.manySepFirstInternal(9, options);\n  }\n\n  AT_LEAST_ONE<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(0, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE1<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    return this.atLeastOneInternal(1, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE2<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(2, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE3<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(3, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE4<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(4, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE5<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(5, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE6<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(6, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE7<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(7, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE8<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(8, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE9<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    this.atLeastOneInternal(9, actionORMethodDef);\n  }\n\n  AT_LEAST_ONE_SEP<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(0, options);\n  }\n\n  AT_LEAST_ONE_SEP1<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(1, options);\n  }\n\n  AT_LEAST_ONE_SEP2<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(2, options);\n  }\n\n  AT_LEAST_ONE_SEP3<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(3, options);\n  }\n\n  AT_LEAST_ONE_SEP4<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(4, options);\n  }\n\n  AT_LEAST_ONE_SEP5<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(5, options);\n  }\n\n  AT_LEAST_ONE_SEP6<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(6, options);\n  }\n\n  AT_LEAST_ONE_SEP7<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(7, options);\n  }\n\n  AT_LEAST_ONE_SEP8<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(8, options);\n  }\n\n  AT_LEAST_ONE_SEP9<OUT>(\n    this: MixedInParser,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    this.atLeastOneSepFirstInternal(9, options);\n  }\n\n  RULE<T>(\n    this: MixedInParser,\n    name: string,\n    implementation: (...implArgs: any[]) => T,\n    config: IRuleConfig<T> = DEFAULT_RULE_CONFIG,\n  ): (idxInCallingRule?: number, ...args: any[]) => T | any {\n    if (includes(this.definedRulesNames, name)) {\n      const errMsg =\n        defaultGrammarValidatorErrorProvider.buildDuplicateRuleNameError({\n          topLevelRule: name,\n          grammarName: this.className,\n        });\n\n      const error = {\n        message: errMsg,\n        type: ParserDefinitionErrorType.DUPLICATE_RULE_NAME,\n        ruleName: name,\n      };\n      this.definitionErrors.push(error);\n    }\n\n    this.definedRulesNames.push(name);\n\n    const ruleImplementation = this.defineRule(name, implementation, config);\n    (this as any)[name] = ruleImplementation;\n    return ruleImplementation;\n  }\n\n  OVERRIDE_RULE<T>(\n    this: MixedInParser,\n    name: string,\n    impl: (...implArgs: any[]) => T,\n    config: IRuleConfig<T> = DEFAULT_RULE_CONFIG,\n  ): (idxInCallingRule?: number, ...args: any[]) => T {\n    const ruleErrors: IParserDefinitionError[] = validateRuleIsOverridden(\n      name,\n      this.definedRulesNames,\n      this.className,\n    );\n    this.definitionErrors = this.definitionErrors.concat(ruleErrors);\n\n    const ruleImplementation = this.defineRule(name, impl, config);\n    (this as any)[name] = ruleImplementation;\n    return ruleImplementation;\n  }\n\n  BACKTRACK<T>(\n    this: MixedInParser,\n    grammarRule: (...args: any[]) => T,\n    args?: any[],\n  ): () => boolean {\n    return function () {\n      // save org state\n      this.isBackTrackingStack.push(1);\n      const orgState = this.saveRecogState();\n      try {\n        grammarRule.apply(this, args);\n        // if no exception was thrown we have succeed parsing the rule.\n        return true;\n      } catch (e) {\n        if (isRecognitionException(e)) {\n          return false;\n        } else {\n          throw e;\n        }\n      } finally {\n        this.reloadRecogState(orgState);\n        this.isBackTrackingStack.pop();\n      }\n    };\n  }\n\n  // GAST export APIs\n  public getGAstProductions(this: MixedInParser): Record<string, Rule> {\n    return this.gastProductionsCache;\n  }\n\n  public getSerializedGastProductions(this: MixedInParser): ISerializedGast[] {\n    return serializeGrammar(values(this.gastProductionsCache));\n  }\n}\n"], "mappings": "AAeA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,WAAW;AAC5C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,mBAAmB,EAAEC,yBAAyB,QAAQ,cAAc;AAC7E,SAASC,oCAAoC,QAAQ,wBAAwB;AAC7E,SAASC,wBAAwB,QAAQ,yBAAyB;AAElE,SAAeC,gBAAgB,QAAQ,kBAAkB;AAIzD;;;;;;;;AAQA,OAAM,MAAOC,aAAa;EACxBC,MAAMA,CAAyBC,IAAa;IAC1C,OAAOA,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACxB;EAEAC,OAAOA,CAELC,GAAW,EACXC,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAED,GAAG,EAAEE,OAAO,CAAC;EACpD;EAEAE,OAAOA,CAELJ,GAAW,EACXK,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAEL,GAAG,EAAEE,OAAO,CAAC;EACvD;EAEAK,MAAMA,CAEJP,GAAW,EACXQ,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAER,GAAG,CAAC;EACpD;EAEAU,EAAEA,CAEAV,GAAW,EACXW,UAA6C;IAE7C,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAEX,GAAG,CAAC;EACzC;EAEAa,IAAIA,CAEFb,GAAW,EACXQ,iBAA0D;IAE1D,OAAO,IAAI,CAACM,YAAY,CAACd,GAAG,EAAEQ,iBAAiB,CAAC;EAClD;EAEAO,UAAUA,CAERf,GAAW,EACXQ,iBAAiE;IAEjE,OAAO,IAAI,CAACQ,kBAAkB,CAAChB,GAAG,EAAEQ,iBAAiB,CAAC;EACxD;EAEAS,OAAOA,CAELhB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAgB,QAAQA,CAENjB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAiB,QAAQA,CAENlB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAkB,QAAQA,CAENnB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAmB,QAAQA,CAENpB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAoB,QAAQA,CAENrB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAqB,QAAQA,CAENtB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAsB,QAAQA,CAENvB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAuB,QAAQA,CAENxB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAwB,QAAQA,CAENzB,OAAkB,EAClBC,OAA2B;IAE3B,OAAO,IAAI,CAACC,eAAe,CAACF,OAAO,EAAE,CAAC,EAAEC,OAAO,CAAC;EAClD;EAEAyB,OAAOA,CAELtB,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA0B,QAAQA,CAENvB,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA2B,QAAQA,CAENxB,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA4B,QAAQA,CAENzB,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA6B,QAAQA,CAEN1B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA8B,QAAQA,CAEN3B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEA+B,QAAQA,CAEN5B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEAgC,QAAQA,CAEN7B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEAiC,QAAQA,CAEN9B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEAkC,QAAQA,CAEN/B,UAAyC,EACzCH,OAAiC;IAEjC,OAAO,IAAI,CAACI,eAAe,CAACD,UAAU,EAAE,CAAC,EAAEH,OAAO,CAAC;EACrD;EAEAmC,MAAMA,CAEJ7B,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEA8B,OAAOA,CAEL9B,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEA+B,OAAOA,CAEL/B,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAgC,OAAOA,CAELhC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAiC,OAAOA,CAELjC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAkC,OAAOA,CAELlC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAmC,OAAOA,CAELnC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAoC,OAAOA,CAELpC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAqC,OAAOA,CAELrC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAsC,OAAOA,CAELtC,iBAA0D;IAE1D,OAAO,IAAI,CAACC,cAAc,CAACD,iBAAiB,EAAE,CAAC,CAAC;EAClD;EAEAuC,EAAEA,CAEApC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEAqC,GAAGA,CAEDrC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEAsC,GAAGA,CAEDtC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEAuC,GAAGA,CAEDvC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEAwC,GAAGA,CAEDxC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEAyC,GAAGA,CAEDzC,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEA0C,GAAGA,CAED1C,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEA2C,GAAGA,CAED3C,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEA4C,GAAGA,CAED5C,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEA6C,GAAGA,CAED7C,UAAiD;IAEjD,OAAO,IAAI,CAACC,UAAU,CAACD,UAAU,EAAE,CAAC,CAAC;EACvC;EAEA8C,IAAIA,CAEFjD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAkD,KAAKA,CAEHlD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAmD,KAAKA,CAEHnD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAoD,KAAKA,CAEHpD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAqD,KAAKA,CAEHrD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAsD,KAAKA,CAEHtD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAuD,KAAKA,CAEHvD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAwD,KAAKA,CAEHxD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEAyD,KAAKA,CAEHzD,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEA0D,KAAKA,CAEH1D,iBAA0D;IAE1D,IAAI,CAACM,YAAY,CAAC,CAAC,EAAEN,iBAAiB,CAAC;EACzC;EAEA2D,QAAQA,CAA2BjE,OAA+B;IAChE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAmE,SAASA,CAA2BnE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAoE,SAASA,CAA2BpE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAqE,SAASA,CAA2BrE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAsE,SAASA,CAA2BtE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAuE,SAASA,CAA2BvE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAwE,SAASA,CAA2BxE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEAyE,SAASA,CAA2BzE,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEA0E,SAASA,CAA2B1E,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEA2E,SAASA,CAA2B3E,OAA+B;IACjE,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAElE,OAAO,CAAC;EACvC;EAEA4E,YAAYA,CAEVtE,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEAuE,aAAaA,CAEXvE,iBAAiE;IAEjE,OAAO,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EACtD;EAEAwE,aAAaA,CAEXxE,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEAyE,aAAaA,CAEXzE,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA0E,aAAaA,CAEX1E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA2E,aAAaA,CAEX3E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA4E,aAAaA,CAEX5E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA6E,aAAaA,CAEX7E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA8E,aAAaA,CAEX9E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEA+E,aAAaA,CAEX/E,iBAAiE;IAEjE,IAAI,CAACQ,kBAAkB,CAAC,CAAC,EAAER,iBAAiB,CAAC;EAC/C;EAEAgF,gBAAgBA,CAEdtF,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEAwF,iBAAiBA,CAEfxF,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEAyF,iBAAiBA,CAEfzF,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA0F,iBAAiBA,CAEf1F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA2F,iBAAiBA,CAEf3F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA4F,iBAAiBA,CAEf5F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA6F,iBAAiBA,CAEf7F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA8F,iBAAiBA,CAEf9F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEA+F,iBAAiBA,CAEf/F,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEAgG,iBAAiBA,CAEfhG,OAAqC;IAErC,IAAI,CAACuF,0BAA0B,CAAC,CAAC,EAAEvF,OAAO,CAAC;EAC7C;EAEAiG,IAAIA,CAEFC,IAAY,EACZC,cAAyC,EACzCC,MAAA,GAAyBhH,mBAAmB;IAE5C,IAAIH,QAAQ,CAAC,IAAI,CAACoH,iBAAiB,EAAEH,IAAI,CAAC,EAAE;MAC1C,MAAMI,MAAM,GACVhH,oCAAoC,CAACiH,2BAA2B,CAAC;QAC/DC,YAAY,EAAEN,IAAI;QAClBO,WAAW,EAAE,IAAI,CAACC;OACnB,CAAC;MAEJ,MAAMC,KAAK,GAAG;QACZC,OAAO,EAAEN,MAAM;QACfO,IAAI,EAAExH,yBAAyB,CAACyH,mBAAmB;QACnDC,QAAQ,EAAEb;OACX;MACD,IAAI,CAACc,gBAAgB,CAACC,IAAI,CAACN,KAAK,CAAC;;IAGnC,IAAI,CAACN,iBAAiB,CAACY,IAAI,CAACf,IAAI,CAAC;IAEjC,MAAMgB,kBAAkB,GAAG,IAAI,CAACC,UAAU,CAACjB,IAAI,EAAEC,cAAc,EAAEC,MAAM,CAAC;IACvE,IAAY,CAACF,IAAI,CAAC,GAAGgB,kBAAkB;IACxC,OAAOA,kBAAkB;EAC3B;EAEAE,aAAaA,CAEXlB,IAAY,EACZvG,IAA+B,EAC/ByG,MAAA,GAAyBhH,mBAAmB;IAE5C,MAAMiI,UAAU,GAA6B9H,wBAAwB,CACnE2G,IAAI,EACJ,IAAI,CAACG,iBAAiB,EACtB,IAAI,CAACK,SAAS,CACf;IACD,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACM,MAAM,CAACD,UAAU,CAAC;IAEhE,MAAMH,kBAAkB,GAAG,IAAI,CAACC,UAAU,CAACjB,IAAI,EAAEvG,IAAI,EAAEyG,MAAM,CAAC;IAC7D,IAAY,CAACF,IAAI,CAAC,GAAGgB,kBAAkB;IACxC,OAAOA,kBAAkB;EAC3B;EAEAK,SAASA,CAEPC,WAAkC,EAClCC,IAAY;IAEZ,OAAO;MACL;MACA,IAAI,CAACC,mBAAmB,CAACT,IAAI,CAAC,CAAC,CAAC;MAChC,MAAMU,QAAQ,GAAG,IAAI,CAACC,cAAc,EAAE;MACtC,IAAI;QACFJ,WAAW,CAACK,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;QAC7B;QACA,OAAO,IAAI;OACZ,CAAC,OAAOK,CAAC,EAAE;QACV,IAAI3I,sBAAsB,CAAC2I,CAAC,CAAC,EAAE;UAC7B,OAAO,KAAK;SACb,MAAM;UACL,MAAMA,CAAC;;OAEV,SAAS;QACR,IAAI,CAACC,gBAAgB,CAACJ,QAAQ,CAAC;QAC/B,IAAI,CAACD,mBAAmB,CAACM,GAAG,EAAE;;IAElC,CAAC;EACH;EAEA;EACOC,kBAAkBA,CAAA;IACvB,OAAO,IAAI,CAACC,oBAAoB;EAClC;EAEOC,4BAA4BA,CAAA;IACjC,OAAO3I,gBAAgB,CAACN,MAAM,CAAC,IAAI,CAACgJ,oBAAoB,CAAC,CAAC;EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}