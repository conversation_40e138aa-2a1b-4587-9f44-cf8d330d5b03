{"ast": null, "code": "import { epsilon } from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport { fitExtent, fitSize, fitWidth, fitHeight } from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function (x, y) {\n      var i = -1;\n      while (++i < n) streams[i].point(x, y);\n    },\n    sphere: function () {\n      var i = -1;\n      while (++i < n) streams[i].sphere();\n    },\n    lineStart: function () {\n      var i = -1;\n      while (++i < n) streams[i].lineStart();\n    },\n    lineEnd: function () {\n      var i = -1;\n      while (++i < n) streams[i].lineEnd();\n    },\n    polygonStart: function () {\n      var i = -1;\n      while (++i < n) streams[i].polygonStart();\n    },\n    polygonEnd: function () {\n      var i = -1;\n      while (++i < n) streams[i].polygonEnd();\n    }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function () {\n  var cache,\n    cacheStream,\n    lower48 = albers(),\n    lower48Point,\n    alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]),\n    alaskaPoint,\n    // EPSG:3338\n    hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]),\n    hawaiiPoint,\n    // ESRI:102007\n    point,\n    pointStream = {\n      point: function (x, y) {\n        point = [x, y];\n      }\n    };\n  function albersUsa(coordinates) {\n    var x = coordinates[0],\n      y = coordinates[1];\n    return point = null, (lower48Point.point(x, y), point) || (alaskaPoint.point(x, y), point) || (hawaiiPoint.point(x, y), point);\n  }\n  albersUsa.invert = function (coordinates) {\n    var k = lower48.scale(),\n      t = lower48.translate(),\n      x = (coordinates[0] - t[0]) / k,\n      y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii : lower48).invert(coordinates);\n  };\n  albersUsa.stream = function (stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n  albersUsa.precision = function (_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n  albersUsa.scale = function (_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n  albersUsa.translate = function (_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(),\n      x = +_[0],\n      y = +_[1];\n    lower48Point = lower48.translate(_).clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]]).stream(pointStream);\n    alaskaPoint = alaska.translate([x - 0.307 * k, y + 0.201 * k]).clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]]).stream(pointStream);\n    hawaiiPoint = hawaii.translate([x - 0.205 * k, y + 0.212 * k]).clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]]).stream(pointStream);\n    return reset();\n  };\n  albersUsa.fitExtent = function (extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n  albersUsa.fitSize = function (size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n  albersUsa.fitWidth = function (width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n  albersUsa.fitHeight = function (height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n  return albersUsa.scale(1070);\n}", "map": {"version": 3, "names": ["epsilon", "albers", "conicEqualArea", "fitExtent", "fitSize", "fit<PERSON><PERSON><PERSON>", "fitHeight", "multiplex", "streams", "n", "length", "point", "x", "y", "i", "sphere", "lineStart", "lineEnd", "polygonStart", "polygonEnd", "cache", "cacheStream", "lower48", "lower48Point", "alaska", "rotate", "center", "parallels", "alaskaPoint", "hawaii", "hawaiiPoint", "pointStream", "albersUsa", "coordinates", "invert", "k", "scale", "t", "translate", "stream", "precision", "_", "arguments", "reset", "clipExtent", "extent", "object", "size", "width", "height"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/albersUsa.js"], "sourcesContent": ["import {epsilon} from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function() {\n  var cache,\n      cacheStream,\n      lower48 = albers(), lower48Point,\n      alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,QAAO,YAAY;AAClC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAAQC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAO,UAAU;;AAEhE;AACA;AACA,SAASC,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAIC,CAAC,GAAGD,OAAO,CAACE,MAAM;EACtB,OAAO;IACLC,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;MAAE,IAAIC,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACH,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC;IAAE,CAAC;IAC7EE,MAAM,EAAE,SAAAA,CAAA,EAAW;MAAE,IAAID,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAAE,CAAC;IACvEC,SAAS,EAAE,SAAAA,CAAA,EAAW;MAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IAAE,CAAC;IAC7EC,OAAO,EAAE,SAAAA,CAAA,EAAW;MAAE,IAAIH,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;IAAE,CAAC;IACzEC,YAAY,EAAE,SAAAA,CAAA,EAAW;MAAE,IAAIJ,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACI,YAAY,CAAC,CAAC;IAAE,CAAC;IACnFC,UAAU,EAAE,SAAAA,CAAA,EAAW;MAAE,IAAIL,CAAC,GAAG,CAAC,CAAC;MAAE,OAAO,EAAEA,CAAC,GAAGL,CAAC,EAAED,OAAO,CAACM,CAAC,CAAC,CAACK,UAAU,CAAC,CAAC;IAAE;EAChF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,YAAW;EACxB,IAAIC,KAAK;IACLC,WAAW;IACXC,OAAO,GAAGrB,MAAM,CAAC,CAAC;IAAEsB,YAAY;IAChCC,MAAM,GAAGtB,cAAc,CAAC,CAAC,CAACuB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAAEC,WAAW;IAAE;IAChGC,MAAM,GAAG3B,cAAc,CAAC,CAAC,CAACuB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAAEG,WAAW;IAAE;IAC/FnB,KAAK;IAAEoB,WAAW,GAAG;MAACpB,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;QAAEF,KAAK,GAAG,CAACC,CAAC,EAAEC,CAAC,CAAC;MAAE;IAAC,CAAC;EAEpE,SAASmB,SAASA,CAACC,WAAW,EAAE;IAC9B,IAAIrB,CAAC,GAAGqB,WAAW,CAAC,CAAC,CAAC;MAAEpB,CAAC,GAAGoB,WAAW,CAAC,CAAC,CAAC;IAC1C,OAAOtB,KAAK,GAAG,IAAI,EACf,CAACY,YAAY,CAACZ,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAEF,KAAK,MAC5BiB,WAAW,CAACjB,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAEF,KAAK,CAAC,KAC/BmB,WAAW,CAACnB,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAEF,KAAK,CAAC;EACzC;EAEAqB,SAAS,CAACE,MAAM,GAAG,UAASD,WAAW,EAAE;IACvC,IAAIE,CAAC,GAAGb,OAAO,CAACc,KAAK,CAAC,CAAC;MACnBC,CAAC,GAAGf,OAAO,CAACgB,SAAS,CAAC,CAAC;MACvB1B,CAAC,GAAG,CAACqB,WAAW,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,IAAIF,CAAC;MAC/BtB,CAAC,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,IAAIF,CAAC;IACnC,OAAO,CAACtB,CAAC,IAAI,KAAK,IAAIA,CAAC,GAAG,KAAK,IAAID,CAAC,IAAI,CAAC,KAAK,IAAIA,CAAC,GAAG,CAAC,KAAK,GAAGY,MAAM,GAC/DX,CAAC,IAAI,KAAK,IAAIA,CAAC,GAAG,KAAK,IAAID,CAAC,IAAI,CAAC,KAAK,IAAIA,CAAC,GAAG,CAAC,KAAK,GAAGiB,MAAM,GAC7DP,OAAO,EAAEY,MAAM,CAACD,WAAW,CAAC;EACpC,CAAC;EAEDD,SAAS,CAACO,MAAM,GAAG,UAASA,MAAM,EAAE;IAClC,OAAOnB,KAAK,IAAIC,WAAW,KAAKkB,MAAM,GAAGnB,KAAK,GAAGA,KAAK,GAAGb,SAAS,CAAC,CAACe,OAAO,CAACiB,MAAM,CAAClB,WAAW,GAAGkB,MAAM,CAAC,EAAEf,MAAM,CAACe,MAAM,CAACA,MAAM,CAAC,EAAEV,MAAM,CAACU,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC;EAC1J,CAAC;EAEDP,SAAS,CAACQ,SAAS,GAAG,UAASC,CAAC,EAAE;IAChC,IAAI,CAACC,SAAS,CAAChC,MAAM,EAAE,OAAOY,OAAO,CAACkB,SAAS,CAAC,CAAC;IACjDlB,OAAO,CAACkB,SAAS,CAACC,CAAC,CAAC,EAAEjB,MAAM,CAACgB,SAAS,CAACC,CAAC,CAAC,EAAEZ,MAAM,CAACW,SAAS,CAACC,CAAC,CAAC;IAC9D,OAAOE,KAAK,CAAC,CAAC;EAChB,CAAC;EAEDX,SAAS,CAACI,KAAK,GAAG,UAASK,CAAC,EAAE;IAC5B,IAAI,CAACC,SAAS,CAAChC,MAAM,EAAE,OAAOY,OAAO,CAACc,KAAK,CAAC,CAAC;IAC7Cd,OAAO,CAACc,KAAK,CAACK,CAAC,CAAC,EAAEjB,MAAM,CAACY,KAAK,CAACK,CAAC,GAAG,IAAI,CAAC,EAAEZ,MAAM,CAACO,KAAK,CAACK,CAAC,CAAC;IACzD,OAAOT,SAAS,CAACM,SAAS,CAAChB,OAAO,CAACgB,SAAS,CAAC,CAAC,CAAC;EACjD,CAAC;EAEDN,SAAS,CAACM,SAAS,GAAG,UAASG,CAAC,EAAE;IAChC,IAAI,CAACC,SAAS,CAAChC,MAAM,EAAE,OAAOY,OAAO,CAACgB,SAAS,CAAC,CAAC;IACjD,IAAIH,CAAC,GAAGb,OAAO,CAACc,KAAK,CAAC,CAAC;MAAExB,CAAC,GAAG,CAAC6B,CAAC,CAAC,CAAC,CAAC;MAAE5B,CAAC,GAAG,CAAC4B,CAAC,CAAC,CAAC,CAAC;IAE7ClB,YAAY,GAAGD,OAAO,CACjBgB,SAAS,CAACG,CAAC,CAAC,CACZG,UAAU,CAAC,CAAC,CAAChC,CAAC,GAAG,KAAK,GAAGuB,CAAC,EAAEtB,CAAC,GAAG,KAAK,GAAGsB,CAAC,CAAC,EAAE,CAACvB,CAAC,GAAG,KAAK,GAAGuB,CAAC,EAAEtB,CAAC,GAAG,KAAK,GAAGsB,CAAC,CAAC,CAAC,CAAC,CAC5EI,MAAM,CAACR,WAAW,CAAC;IAExBH,WAAW,GAAGJ,MAAM,CACfc,SAAS,CAAC,CAAC1B,CAAC,GAAG,KAAK,GAAGuB,CAAC,EAAEtB,CAAC,GAAG,KAAK,GAAGsB,CAAC,CAAC,CAAC,CACzCS,UAAU,CAAC,CAAC,CAAChC,CAAC,GAAG,KAAK,GAAGuB,CAAC,GAAGnC,OAAO,EAAEa,CAAC,GAAG,KAAK,GAAGsB,CAAC,GAAGnC,OAAO,CAAC,EAAE,CAACY,CAAC,GAAG,KAAK,GAAGuB,CAAC,GAAGnC,OAAO,EAAEa,CAAC,GAAG,KAAK,GAAGsB,CAAC,GAAGnC,OAAO,CAAC,CAAC,CAAC,CACpHuC,MAAM,CAACR,WAAW,CAAC;IAExBD,WAAW,GAAGD,MAAM,CACfS,SAAS,CAAC,CAAC1B,CAAC,GAAG,KAAK,GAAGuB,CAAC,EAAEtB,CAAC,GAAG,KAAK,GAAGsB,CAAC,CAAC,CAAC,CACzCS,UAAU,CAAC,CAAC,CAAChC,CAAC,GAAG,KAAK,GAAGuB,CAAC,GAAGnC,OAAO,EAAEa,CAAC,GAAG,KAAK,GAAGsB,CAAC,GAAGnC,OAAO,CAAC,EAAE,CAACY,CAAC,GAAG,KAAK,GAAGuB,CAAC,GAAGnC,OAAO,EAAEa,CAAC,GAAG,KAAK,GAAGsB,CAAC,GAAGnC,OAAO,CAAC,CAAC,CAAC,CACpHuC,MAAM,CAACR,WAAW,CAAC;IAExB,OAAOY,KAAK,CAAC,CAAC;EAChB,CAAC;EAEDX,SAAS,CAAC7B,SAAS,GAAG,UAAS0C,MAAM,EAAEC,MAAM,EAAE;IAC7C,OAAO3C,SAAS,CAAC6B,SAAS,EAAEa,MAAM,EAAEC,MAAM,CAAC;EAC7C,CAAC;EAEDd,SAAS,CAAC5B,OAAO,GAAG,UAAS2C,IAAI,EAAED,MAAM,EAAE;IACzC,OAAO1C,OAAO,CAAC4B,SAAS,EAAEe,IAAI,EAAED,MAAM,CAAC;EACzC,CAAC;EAEDd,SAAS,CAAC3B,QAAQ,GAAG,UAAS2C,KAAK,EAAEF,MAAM,EAAE;IAC3C,OAAOzC,QAAQ,CAAC2B,SAAS,EAAEgB,KAAK,EAAEF,MAAM,CAAC;EAC3C,CAAC;EAEDd,SAAS,CAAC1B,SAAS,GAAG,UAAS2C,MAAM,EAAEH,MAAM,EAAE;IAC7C,OAAOxC,SAAS,CAAC0B,SAAS,EAAEiB,MAAM,EAAEH,MAAM,CAAC;EAC7C,CAAC;EAED,SAASH,KAAKA,CAAA,EAAG;IACfvB,KAAK,GAAGC,WAAW,GAAG,IAAI;IAC1B,OAAOW,SAAS;EAClB;EAEA,OAAOA,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}