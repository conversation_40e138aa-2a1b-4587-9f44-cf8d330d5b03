{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { findAssignment } from '../utils/grammar-utils.js';\nimport { isReference } from '../syntax-tree.js';\nimport { getDocument } from '../utils/ast-utils.js';\nimport { isChildNode, toDocumentSegment } from '../utils/cst-utils.js';\nimport { stream } from '../utils/stream.js';\nimport { UriUtils } from '../utils/uri-utils.js';\nexport class DefaultReferences {\n  constructor(services) {\n    this.nameProvider = services.references.NameProvider;\n    this.index = services.shared.workspace.IndexManager;\n    this.nodeLocator = services.workspace.AstNodeLocator;\n  }\n  findDeclaration(sourceCstNode) {\n    if (sourceCstNode) {\n      const assignment = findAssignment(sourceCstNode);\n      const nodeElem = sourceCstNode.astNode;\n      if (assignment && nodeElem) {\n        const reference = nodeElem[assignment.feature];\n        if (isReference(reference)) {\n          return reference.ref;\n        } else if (Array.isArray(reference)) {\n          for (const ref of reference) {\n            if (isReference(ref) && ref.$refNode && ref.$refNode.offset <= sourceCstNode.offset && ref.$refNode.end >= sourceCstNode.end) {\n              return ref.ref;\n            }\n          }\n        }\n      }\n      if (nodeElem) {\n        const nameNode = this.nameProvider.getNameNode(nodeElem);\n        // Only return the targeted node in case the targeted cst node is the name node or part of it\n        if (nameNode && (nameNode === sourceCstNode || isChildNode(sourceCstNode, nameNode))) {\n          return nodeElem;\n        }\n      }\n    }\n    return undefined;\n  }\n  findDeclarationNode(sourceCstNode) {\n    const astNode = this.findDeclaration(sourceCstNode);\n    if (astNode === null || astNode === void 0 ? void 0 : astNode.$cstNode) {\n      const targetNode = this.nameProvider.getNameNode(astNode);\n      return targetNode !== null && targetNode !== void 0 ? targetNode : astNode.$cstNode;\n    }\n    return undefined;\n  }\n  findReferences(targetNode, options) {\n    const refs = [];\n    if (options.includeDeclaration) {\n      const ref = this.getReferenceToSelf(targetNode);\n      if (ref) {\n        refs.push(ref);\n      }\n    }\n    let indexReferences = this.index.findAllReferences(targetNode, this.nodeLocator.getAstNodePath(targetNode));\n    if (options.documentUri) {\n      indexReferences = indexReferences.filter(ref => UriUtils.equals(ref.sourceUri, options.documentUri));\n    }\n    refs.push(...indexReferences);\n    return stream(refs);\n  }\n  getReferenceToSelf(targetNode) {\n    const nameNode = this.nameProvider.getNameNode(targetNode);\n    if (nameNode) {\n      const doc = getDocument(targetNode);\n      const path = this.nodeLocator.getAstNodePath(targetNode);\n      return {\n        sourceUri: doc.uri,\n        sourcePath: path,\n        targetUri: doc.uri,\n        targetPath: path,\n        segment: toDocumentSegment(nameNode),\n        local: true\n      };\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["findAssignment", "isReference", "getDocument", "isChildNode", "toDocumentSegment", "stream", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultReferences", "constructor", "services", "nameProvider", "references", "Name<PERSON>rovider", "index", "shared", "workspace", "IndexManager", "nodeLocator", "AstNodeLocator", "findDeclaration", "sourceCstNode", "assignment", "nodeElem", "astNode", "reference", "feature", "ref", "Array", "isArray", "$refNode", "offset", "end", "nameNode", "getNameNode", "undefined", "findDeclarationNode", "$cstNode", "targetNode", "findReferences", "options", "refs", "includeDeclaration", "getReferenceToSelf", "push", "indexReferences", "findAllReferences", "getAstNodePath", "documentUri", "filter", "equals", "sourceUri", "doc", "path", "uri", "sourcePath", "targetUri", "targetPath", "segment", "local"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/references/references.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, CstNode, GenericAstNode } from '../syntax-tree.js';\r\nimport type { Stream } from '../utils/stream.js';\r\nimport type { ReferenceDescription } from '../workspace/ast-descriptions.js';\r\nimport type { AstNodeLocator } from '../workspace/ast-node-locator.js';\r\nimport type { IndexManager } from '../workspace/index-manager.js';\r\nimport type { NameProvider } from './name-provider.js';\r\nimport type { URI } from '../utils/uri-utils.js';\r\nimport { findAssignment } from '../utils/grammar-utils.js';\r\nimport { isReference } from '../syntax-tree.js';\r\nimport { getDocument } from '../utils/ast-utils.js';\r\nimport { isChildNode, toDocumentSegment } from '../utils/cst-utils.js';\r\nimport { stream } from '../utils/stream.js';\r\nimport { UriUtils } from '../utils/uri-utils.js';\r\n\r\n/**\r\n * Language-specific service for finding references and declaration of a given `CstNode`.\r\n */\r\nexport interface References {\r\n\r\n    /**\r\n     * If the CstNode is a reference node the target CstNode will be returned.\r\n     * If the CstNode is a significant node of the CstNode this CstNode will be returned.\r\n     *\r\n     * @param sourceCstNode CstNode that points to a AstNode\r\n     */\r\n    findDeclaration(sourceCstNode: CstNode): AstNode | undefined;\r\n\r\n    /**\r\n     * If the CstNode is a reference node the target CstNode will be returned.\r\n     * If the CstNode is a significant node of the CstNode this CstNode will be returned.\r\n     *\r\n     * @param sourceCstNode CstNode that points to a AstNode\r\n     */\r\n    findDeclarationNode(sourceCstNode: CstNode): CstNode | undefined;\r\n\r\n    /**\r\n     * Finds all references to the target node as references (local references) or reference descriptions.\r\n     *\r\n     * @param targetNode Specified target node whose references should be returned\r\n     */\r\n    findReferences(targetNode: AstNode, options: FindReferencesOptions): Stream<ReferenceDescription>;\r\n}\r\n\r\nexport interface FindReferencesOptions {\r\n    /**\r\n     * @deprecated Since v1.2.0. Please use `documentUri` instead.\r\n     */\r\n    onlyLocal?: boolean;\r\n    /**\r\n     * When set, the `findReferences` method will only return references/declarations from the specified document.\r\n     */\r\n    documentUri?: URI;\r\n    /**\r\n     * Whether the returned list of references should include the declaration.\r\n     */\r\n    includeDeclaration?: boolean;\r\n}\r\n\r\nexport class DefaultReferences implements References {\r\n    protected readonly nameProvider: NameProvider;\r\n    protected readonly index: IndexManager;\r\n    protected readonly nodeLocator: AstNodeLocator;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.nameProvider = services.references.NameProvider;\r\n        this.index = services.shared.workspace.IndexManager;\r\n        this.nodeLocator = services.workspace.AstNodeLocator;\r\n    }\r\n\r\n    findDeclaration(sourceCstNode: CstNode): AstNode | undefined {\r\n        if (sourceCstNode) {\r\n            const assignment = findAssignment(sourceCstNode);\r\n            const nodeElem = sourceCstNode.astNode;\r\n            if (assignment && nodeElem) {\r\n                const reference = (nodeElem as GenericAstNode)[assignment.feature];\r\n\r\n                if (isReference(reference)) {\r\n                    return reference.ref;\r\n                } else if (Array.isArray(reference)) {\r\n                    for (const ref of reference) {\r\n                        if (isReference(ref) && ref.$refNode\r\n                            && ref.$refNode.offset <= sourceCstNode.offset\r\n                            && ref.$refNode.end >= sourceCstNode.end) {\r\n                            return ref.ref;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            if (nodeElem) {\r\n                const nameNode = this.nameProvider.getNameNode(nodeElem);\r\n                // Only return the targeted node in case the targeted cst node is the name node or part of it\r\n                if (nameNode && (nameNode === sourceCstNode || isChildNode(sourceCstNode, nameNode))) {\r\n                    return nodeElem;\r\n                }\r\n            }\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    findDeclarationNode(sourceCstNode: CstNode): CstNode | undefined {\r\n        const astNode = this.findDeclaration(sourceCstNode);\r\n        if (astNode?.$cstNode) {\r\n            const targetNode = this.nameProvider.getNameNode(astNode);\r\n            return targetNode ?? astNode.$cstNode;\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    findReferences(targetNode: AstNode, options: FindReferencesOptions): Stream<ReferenceDescription> {\r\n        const refs: ReferenceDescription[] = [];\r\n        if (options.includeDeclaration) {\r\n            const ref = this.getReferenceToSelf(targetNode);\r\n            if (ref) {\r\n                refs.push(ref);\r\n            }\r\n        }\r\n        let indexReferences = this.index.findAllReferences(targetNode, this.nodeLocator.getAstNodePath(targetNode));\r\n        if (options.documentUri) {\r\n            indexReferences = indexReferences.filter(ref => UriUtils.equals(ref.sourceUri, options.documentUri));\r\n        }\r\n        refs.push(...indexReferences);\r\n        return stream(refs);\r\n    }\r\n\r\n    protected getReferenceToSelf(targetNode: AstNode): ReferenceDescription | undefined {\r\n        const nameNode = this.nameProvider.getNameNode(targetNode);\r\n        if (nameNode) {\r\n            const doc = getDocument(targetNode);\r\n            const path = this.nodeLocator.getAstNodePath(targetNode);\r\n            return {\r\n                sourceUri: doc.uri,\r\n                sourcePath: path,\r\n                targetUri: doc.uri,\r\n                targetPath: path,\r\n                segment: toDocumentSegment(nameNode),\r\n                local: true\r\n            };\r\n        }\r\n        return undefined;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAcA,SAASA,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,uBAAuB;AACtE,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,uBAAuB;AA8ChD,OAAM,MAAOC,iBAAiB;EAK1BC,YAAYC,QAA6B;IACrC,IAAI,CAACC,YAAY,GAAGD,QAAQ,CAACE,UAAU,CAACC,YAAY;IACpD,IAAI,CAACC,KAAK,GAAGJ,QAAQ,CAACK,MAAM,CAACC,SAAS,CAACC,YAAY;IACnD,IAAI,CAACC,WAAW,GAAGR,QAAQ,CAACM,SAAS,CAACG,cAAc;EACxD;EAEAC,eAAeA,CAACC,aAAsB;IAClC,IAAIA,aAAa,EAAE;MACf,MAAMC,UAAU,GAAGrB,cAAc,CAACoB,aAAa,CAAC;MAChD,MAAME,QAAQ,GAAGF,aAAa,CAACG,OAAO;MACtC,IAAIF,UAAU,IAAIC,QAAQ,EAAE;QACxB,MAAME,SAAS,GAAIF,QAA2B,CAACD,UAAU,CAACI,OAAO,CAAC;QAElE,IAAIxB,WAAW,CAACuB,SAAS,CAAC,EAAE;UACxB,OAAOA,SAAS,CAACE,GAAG;QACxB,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;UACjC,KAAK,MAAME,GAAG,IAAIF,SAAS,EAAE;YACzB,IAAIvB,WAAW,CAACyB,GAAG,CAAC,IAAIA,GAAG,CAACG,QAAQ,IAC7BH,GAAG,CAACG,QAAQ,CAACC,MAAM,IAAIV,aAAa,CAACU,MAAM,IAC3CJ,GAAG,CAACG,QAAQ,CAACE,GAAG,IAAIX,aAAa,CAACW,GAAG,EAAE;cAC1C,OAAOL,GAAG,CAACA,GAAG;YAClB;UACJ;QACJ;MACJ;MACA,IAAIJ,QAAQ,EAAE;QACV,MAAMU,QAAQ,GAAG,IAAI,CAACtB,YAAY,CAACuB,WAAW,CAACX,QAAQ,CAAC;QACxD;QACA,IAAIU,QAAQ,KAAKA,QAAQ,KAAKZ,aAAa,IAAIjB,WAAW,CAACiB,aAAa,EAAEY,QAAQ,CAAC,CAAC,EAAE;UAClF,OAAOV,QAAQ;QACnB;MACJ;IACJ;IACA,OAAOY,SAAS;EACpB;EAEAC,mBAAmBA,CAACf,aAAsB;IACtC,MAAMG,OAAO,GAAG,IAAI,CAACJ,eAAe,CAACC,aAAa,CAAC;IACnD,IAAIG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,QAAQ,EAAE;MACnB,MAAMC,UAAU,GAAG,IAAI,CAAC3B,YAAY,CAACuB,WAAW,CAACV,OAAO,CAAC;MACzD,OAAOc,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAId,OAAO,CAACa,QAAQ;IACzC;IACA,OAAOF,SAAS;EACpB;EAEAI,cAAcA,CAACD,UAAmB,EAAEE,OAA8B;IAC9D,MAAMC,IAAI,GAA2B,EAAE;IACvC,IAAID,OAAO,CAACE,kBAAkB,EAAE;MAC5B,MAAMf,GAAG,GAAG,IAAI,CAACgB,kBAAkB,CAACL,UAAU,CAAC;MAC/C,IAAIX,GAAG,EAAE;QACLc,IAAI,CAACG,IAAI,CAACjB,GAAG,CAAC;MAClB;IACJ;IACA,IAAIkB,eAAe,GAAG,IAAI,CAAC/B,KAAK,CAACgC,iBAAiB,CAACR,UAAU,EAAE,IAAI,CAACpB,WAAW,CAAC6B,cAAc,CAACT,UAAU,CAAC,CAAC;IAC3G,IAAIE,OAAO,CAACQ,WAAW,EAAE;MACrBH,eAAe,GAAGA,eAAe,CAACI,MAAM,CAACtB,GAAG,IAAIpB,QAAQ,CAAC2C,MAAM,CAACvB,GAAG,CAACwB,SAAS,EAAEX,OAAO,CAACQ,WAAW,CAAC,CAAC;IACxG;IACAP,IAAI,CAACG,IAAI,CAAC,GAAGC,eAAe,CAAC;IAC7B,OAAOvC,MAAM,CAACmC,IAAI,CAAC;EACvB;EAEUE,kBAAkBA,CAACL,UAAmB;IAC5C,MAAML,QAAQ,GAAG,IAAI,CAACtB,YAAY,CAACuB,WAAW,CAACI,UAAU,CAAC;IAC1D,IAAIL,QAAQ,EAAE;MACV,MAAMmB,GAAG,GAAGjD,WAAW,CAACmC,UAAU,CAAC;MACnC,MAAMe,IAAI,GAAG,IAAI,CAACnC,WAAW,CAAC6B,cAAc,CAACT,UAAU,CAAC;MACxD,OAAO;QACHa,SAAS,EAAEC,GAAG,CAACE,GAAG;QAClBC,UAAU,EAAEF,IAAI;QAChBG,SAAS,EAAEJ,GAAG,CAACE,GAAG;QAClBG,UAAU,EAAEJ,IAAI;QAChBK,OAAO,EAAErD,iBAAiB,CAAC4B,QAAQ,CAAC;QACpC0B,KAAK,EAAE;OACV;IACL;IACA,OAAOxB,SAAS;EACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}