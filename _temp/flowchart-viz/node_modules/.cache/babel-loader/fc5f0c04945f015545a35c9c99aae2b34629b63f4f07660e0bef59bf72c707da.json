{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arraySome from './_arraySome.js';\nimport cacheHas from './_cacheHas.js';\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n  COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n    arrLength = array.length,\n    othLength = other.length;\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  // Check that cyclic values are equal.\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1,\n    result = true,\n    seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache() : undefined;\n  stack.set(array, other);\n  stack.set(other, array);\n\n  // Ignore non-index properties.\n  while (++index < arrLength) {\n    var arrValue = array[index],\n      othValue = other[index];\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    // Recursively compare arrays (susceptible to call stack limits).\n    if (seen) {\n      if (!arraySome(other, function (othValue, othIndex) {\n        if (!cacheHas(seen, othIndex) && (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n          return seen.push(othIndex);\n        }\n      })) {\n        result = false;\n        break;\n      }\n    } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n      result = false;\n      break;\n    }\n  }\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\nexport default equalArrays;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "arraySome", "cacheHas", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "equalArrays", "array", "other", "bitmask", "customizer", "equalFunc", "stack", "isPartial", "arr<PERSON><PERSON><PERSON>", "length", "oth<PERSON><PERSON><PERSON>", "arrStacked", "get", "othStacked", "index", "result", "seen", "undefined", "set", "arrV<PERSON>ue", "othValue", "compared", "othIndex", "push"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_equalArrays.js"], "sourcesContent": ["import SetCache from './_SetCache.js';\nimport arraySome from './_arraySome.js';\nimport cacheHas from './_cacheHas.js';\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  // Check that cyclic values are equal.\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1,\n      result = true,\n      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;\n\n  stack.set(array, other);\n  stack.set(other, array);\n\n  // Ignore non-index properties.\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, arrValue, index, other, array, stack)\n        : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    // Recursively compare arrays (susceptible to call stack limits).\n    if (seen) {\n      if (!arraySome(other, function(othValue, othIndex) {\n            if (!cacheHas(seen, othIndex) &&\n                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n              return seen.push(othIndex);\n            }\n          })) {\n        result = false;\n        break;\n      }\n    } else if (!(\n          arrValue === othValue ||\n            equalFunc(arrValue, othValue, bitmask, customizer, stack)\n        )) {\n      result = false;\n      break;\n    }\n  }\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nexport default equalArrays;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,oBAAoB,GAAG,CAAC;EACxBC,sBAAsB,GAAG,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACxE,IAAIC,SAAS,GAAGJ,OAAO,GAAGL,oBAAoB;IAC1CU,SAAS,GAAGP,KAAK,CAACQ,MAAM;IACxBC,SAAS,GAAGR,KAAK,CAACO,MAAM;EAE5B,IAAID,SAAS,IAAIE,SAAS,IAAI,EAAEH,SAAS,IAAIG,SAAS,GAAGF,SAAS,CAAC,EAAE;IACnE,OAAO,KAAK;EACd;EACA;EACA,IAAIG,UAAU,GAAGL,KAAK,CAACM,GAAG,CAACX,KAAK,CAAC;EACjC,IAAIY,UAAU,GAAGP,KAAK,CAACM,GAAG,CAACV,KAAK,CAAC;EACjC,IAAIS,UAAU,IAAIE,UAAU,EAAE;IAC5B,OAAOF,UAAU,IAAIT,KAAK,IAAIW,UAAU,IAAIZ,KAAK;EACnD;EACA,IAAIa,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAG,IAAI;IACbC,IAAI,GAAIb,OAAO,GAAGJ,sBAAsB,GAAI,IAAIJ,QAAQ,CAAD,CAAC,GAAGsB,SAAS;EAExEX,KAAK,CAACY,GAAG,CAACjB,KAAK,EAAEC,KAAK,CAAC;EACvBI,KAAK,CAACY,GAAG,CAAChB,KAAK,EAAED,KAAK,CAAC;;EAEvB;EACA,OAAO,EAAEa,KAAK,GAAGN,SAAS,EAAE;IAC1B,IAAIW,QAAQ,GAAGlB,KAAK,CAACa,KAAK,CAAC;MACvBM,QAAQ,GAAGlB,KAAK,CAACY,KAAK,CAAC;IAE3B,IAAIV,UAAU,EAAE;MACd,IAAIiB,QAAQ,GAAGd,SAAS,GACpBH,UAAU,CAACgB,QAAQ,EAAED,QAAQ,EAAEL,KAAK,EAAEZ,KAAK,EAAED,KAAK,EAAEK,KAAK,CAAC,GAC1DF,UAAU,CAACe,QAAQ,EAAEC,QAAQ,EAAEN,KAAK,EAAEb,KAAK,EAAEC,KAAK,EAAEI,KAAK,CAAC;IAChE;IACA,IAAIe,QAAQ,KAAKJ,SAAS,EAAE;MAC1B,IAAII,QAAQ,EAAE;QACZ;MACF;MACAN,MAAM,GAAG,KAAK;MACd;IACF;IACA;IACA,IAAIC,IAAI,EAAE;MACR,IAAI,CAACpB,SAAS,CAACM,KAAK,EAAE,UAASkB,QAAQ,EAAEE,QAAQ,EAAE;QAC7C,IAAI,CAACzB,QAAQ,CAACmB,IAAI,EAAEM,QAAQ,CAAC,KACxBH,QAAQ,KAAKC,QAAQ,IAAIf,SAAS,CAACc,QAAQ,EAAEC,QAAQ,EAAEjB,OAAO,EAAEC,UAAU,EAAEE,KAAK,CAAC,CAAC,EAAE;UACxF,OAAOU,IAAI,CAACO,IAAI,CAACD,QAAQ,CAAC;QAC5B;MACF,CAAC,CAAC,EAAE;QACNP,MAAM,GAAG,KAAK;QACd;MACF;IACF,CAAC,MAAM,IAAI,EACLI,QAAQ,KAAKC,QAAQ,IACnBf,SAAS,CAACc,QAAQ,EAAEC,QAAQ,EAAEjB,OAAO,EAAEC,UAAU,EAAEE,KAAK,CAAC,CAC5D,EAAE;MACLS,MAAM,GAAG,KAAK;MACd;IACF;EACF;EACAT,KAAK,CAAC,QAAQ,CAAC,CAACL,KAAK,CAAC;EACtBK,KAAK,CAAC,QAAQ,CAAC,CAACJ,KAAK,CAAC;EACtB,OAAOa,MAAM;AACf;AAEA,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}