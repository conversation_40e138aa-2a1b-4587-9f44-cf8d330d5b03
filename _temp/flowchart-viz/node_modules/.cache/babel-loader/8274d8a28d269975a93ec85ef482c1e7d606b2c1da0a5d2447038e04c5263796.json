{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport map from \"lodash-es/map.js\";\nexport const DFA_ERROR = {};\nexport class ATNConfigSet {\n  constructor() {\n    this.map = {};\n    this.configs = [];\n  }\n  get size() {\n    return this.configs.length;\n  }\n  finalize() {\n    // Empties the map to free up memory\n    this.map = {};\n  }\n  add(config) {\n    const key = getATNConfigKey(config);\n    // Only add configs which don't exist in our map already\n    // While this does not influence the actual algorithm, adding them anyway would massively increase memory consumption\n    if (!(key in this.map)) {\n      this.map[key] = this.configs.length;\n      this.configs.push(config);\n    }\n  }\n  get elements() {\n    return this.configs;\n  }\n  get alts() {\n    return map(this.configs, e => e.alt);\n  }\n  get key() {\n    let value = \"\";\n    for (const k in this.map) {\n      value += k + \":\";\n    }\n    return value;\n  }\n}\nexport function getATNConfigKey(config, alt = true) {\n  return `${alt ? `a${config.alt}` : \"\"}s${config.state.stateNumber}:${config.stack.map(e => e.stateNumber.toString()).join(\"_\")}`;\n}", "map": {"version": 3, "names": ["map", "DFA_ERROR", "ATNConfigSet", "constructor", "configs", "size", "length", "finalize", "add", "config", "key", "getATNConfigKey", "push", "elements", "alts", "e", "alt", "value", "k", "state", "stateNumber", "stack", "toString", "join"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain-allstar/src/dfa.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport map from \"lodash-es/map.js\"\r\nimport { ATNState, DecisionState } from \"./atn.js\"\r\n\r\nexport interface DFA {\r\n  start?: DFAState\r\n  states: Record<string, DFAState>\r\n  decision: number\r\n  atnStartState: DecisionState\r\n}\r\n\r\nexport interface DFAState {\r\n  configs: ATNConfigSet\r\n  edges: Record<number, DFAState>\r\n  isAcceptState: boolean\r\n  prediction: number\r\n}\r\n\r\nexport const DFA_ERROR = {} as DFAState\r\n\r\nexport interface ATNConfig {\r\n  state: ATNState\r\n  alt: number\r\n  stack: ATNState[]\r\n}\r\n\r\nexport class ATNConfigSet {\r\n  private map: Record<string, number> = {}\r\n  private configs: ATNConfig[] = []\r\n\r\n  uniqueAlt: number | undefined\r\n\r\n  get size(): number {\r\n    return this.configs.length\r\n  }\r\n\r\n  finalize(): void {\r\n    // Empties the map to free up memory\r\n    this.map = {}\r\n  }\r\n\r\n  add(config: ATNConfig): void {\r\n    const key = getATNConfigKey(config)\r\n    // Only add configs which don't exist in our map already\r\n    // While this does not influence the actual algorithm, adding them anyway would massively increase memory consumption\r\n    if (!(key in this.map)) {\r\n      this.map[key] = this.configs.length\r\n      this.configs.push(config)\r\n    }\r\n  }\r\n\r\n  get elements(): readonly ATNConfig[] {\r\n    return this.configs\r\n  }\r\n\r\n  get alts(): number[] {\r\n    return map(this.configs, (e) => e.alt)\r\n  }\r\n\r\n  get key(): string {\r\n    let value = \"\"\r\n    for (const k in this.map) {\r\n      value += k + \":\"\r\n    }\r\n    return value\r\n  }\r\n}\r\n\r\nexport function getATNConfigKey(config: ATNConfig, alt = true) {\r\n  return `${alt ? `a${config.alt}` : \"\"}s${\r\n    config.state.stateNumber\r\n  }:${config.stack.map((e) => e.stateNumber.toString()).join(\"_\")}`\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,OAAOA,GAAG,MAAM,kBAAkB;AAiBlC,OAAO,MAAMC,SAAS,GAAG,EAAc;AAQvC,OAAM,MAAOC,YAAY;EAAzBC,YAAA;IACU,KAAAH,GAAG,GAA2B,EAAE;IAChC,KAAAI,OAAO,GAAgB,EAAE;EAsCnC;EAlCE,IAAIC,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,OAAO,CAACE,MAAM;EAC5B;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,GAAG,GAAG,EAAE;EACf;EAEAQ,GAAGA,CAACC,MAAiB;IACnB,MAAMC,GAAG,GAAGC,eAAe,CAACF,MAAM,CAAC;IACnC;IACA;IACA,IAAI,EAAEC,GAAG,IAAI,IAAI,CAACV,GAAG,CAAC,EAAE;MACtB,IAAI,CAACA,GAAG,CAACU,GAAG,CAAC,GAAG,IAAI,CAACN,OAAO,CAACE,MAAM;MACnC,IAAI,CAACF,OAAO,CAACQ,IAAI,CAACH,MAAM,CAAC;;EAE7B;EAEA,IAAII,QAAQA,CAAA;IACV,OAAO,IAAI,CAACT,OAAO;EACrB;EAEA,IAAIU,IAAIA,CAAA;IACN,OAAOd,GAAG,CAAC,IAAI,CAACI,OAAO,EAAGW,CAAC,IAAKA,CAAC,CAACC,GAAG,CAAC;EACxC;EAEA,IAAIN,GAAGA,CAAA;IACL,IAAIO,KAAK,GAAG,EAAE;IACd,KAAK,MAAMC,CAAC,IAAI,IAAI,CAAClB,GAAG,EAAE;MACxBiB,KAAK,IAAIC,CAAC,GAAG,GAAG;;IAElB,OAAOD,KAAK;EACd;;AAGF,OAAM,SAAUN,eAAeA,CAACF,MAAiB,EAAEO,GAAG,GAAG,IAAI;EAC3D,OAAO,GAAGA,GAAG,GAAG,IAAIP,MAAM,CAACO,GAAG,EAAE,GAAG,EAAE,IACnCP,MAAM,CAACU,KAAK,CAACC,WACf,IAAIX,MAAM,CAACY,KAAK,CAACrB,GAAG,CAAEe,CAAC,IAAKA,CAAC,CAACK,WAAW,CAACE,QAAQ,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}