{"ast": null, "code": "import { cc } from \"./utils.js\";\nexport const digitsCharCodes = [];\nfor (let i = cc(\"0\"); i <= cc(\"9\"); i++) {\n  digitsCharCodes.push(i);\n}\nexport const wordCharCodes = [cc(\"_\")].concat(digitsCharCodes);\nfor (let i = cc(\"a\"); i <= cc(\"z\"); i++) {\n  wordCharCodes.push(i);\n}\nfor (let i = cc(\"A\"); i <= cc(\"Z\"); i++) {\n  wordCharCodes.push(i);\n}\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#character-classes\nexport const whitespaceCodes = [cc(\" \"), cc(\"\\f\"), cc(\"\\n\"), cc(\"\\r\"), cc(\"\\t\"), cc(\"\\v\"), cc(\"\\t\"), cc(\"\\u00a0\"), cc(\"\\u1680\"), cc(\"\\u2000\"), cc(\"\\u2001\"), cc(\"\\u2002\"), cc(\"\\u2003\"), cc(\"\\u2004\"), cc(\"\\u2005\"), cc(\"\\u2006\"), cc(\"\\u2007\"), cc(\"\\u2008\"), cc(\"\\u2009\"), cc(\"\\u200a\"), cc(\"\\u2028\"), cc(\"\\u2029\"), cc(\"\\u202f\"), cc(\"\\u205f\"), cc(\"\\u3000\"), cc(\"\\ufeff\")];", "map": {"version": 3, "names": ["cc", "digitsCharCodes", "i", "push", "wordCharCodes", "concat", "whitespaceCodes"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/regexp-to-ast/src/character-classes.ts"], "sourcesContent": ["import { cc } from \"./utils.js\";\n\nexport const digitsCharCodes: number[] = [];\nfor (let i = cc(\"0\"); i <= cc(\"9\"); i++) {\n  digitsCharCodes.push(i);\n}\n\nexport const wordCharCodes: number[] = [cc(\"_\")].concat(digitsCharCodes);\nfor (let i = cc(\"a\"); i <= cc(\"z\"); i++) {\n  wordCharCodes.push(i);\n}\n\nfor (let i = cc(\"A\"); i <= cc(\"Z\"); i++) {\n  wordCharCodes.push(i);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#character-classes\nexport const whitespaceCodes: number[] = [\n  cc(\" \"),\n  cc(\"\\f\"),\n  cc(\"\\n\"),\n  cc(\"\\r\"),\n  cc(\"\\t\"),\n  cc(\"\\v\"),\n  cc(\"\\t\"),\n  cc(\"\\u00a0\"),\n  cc(\"\\u1680\"),\n  cc(\"\\u2000\"),\n  cc(\"\\u2001\"),\n  cc(\"\\u2002\"),\n  cc(\"\\u2003\"),\n  cc(\"\\u2004\"),\n  cc(\"\\u2005\"),\n  cc(\"\\u2006\"),\n  cc(\"\\u2007\"),\n  cc(\"\\u2008\"),\n  cc(\"\\u2009\"),\n  cc(\"\\u200a\"),\n  cc(\"\\u2028\"),\n  cc(\"\\u2029\"),\n  cc(\"\\u202f\"),\n  cc(\"\\u205f\"),\n  cc(\"\\u3000\"),\n  cc(\"\\ufeff\"),\n];\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,YAAY;AAE/B,OAAO,MAAMC,eAAe,GAAa,EAAE;AAC3C,KAAK,IAAIC,CAAC,GAAGF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAIF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;EACvCD,eAAe,CAACE,IAAI,CAACD,CAAC,CAAC;;AAGzB,OAAO,MAAME,aAAa,GAAa,CAACJ,EAAE,CAAC,GAAG,CAAC,CAAC,CAACK,MAAM,CAACJ,eAAe,CAAC;AACxE,KAAK,IAAIC,CAAC,GAAGF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAIF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;EACvCE,aAAa,CAACD,IAAI,CAACD,CAAC,CAAC;;AAGvB,KAAK,IAAIA,CAAC,GAAGF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAIF,EAAE,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;EACvCE,aAAa,CAACD,IAAI,CAACD,CAAC,CAAC;;AAGvB;AACA,OAAO,MAAMI,eAAe,GAAa,CACvCN,EAAE,CAAC,GAAG,CAAC,EACPA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,EACZA,EAAE,CAAC,QAAQ,CAAC,CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}