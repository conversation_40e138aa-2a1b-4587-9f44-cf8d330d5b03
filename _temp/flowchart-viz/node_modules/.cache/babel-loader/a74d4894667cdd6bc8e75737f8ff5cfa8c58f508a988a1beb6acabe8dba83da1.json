{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport class DefaultAstNodeLocator {\n  constructor() {\n    this.segmentSeparator = '/';\n    this.indexSeparator = '@';\n  }\n  getAstNodePath(node) {\n    if (node.$container) {\n      const containerPath = this.getAstNodePath(node.$container);\n      const newSegment = this.getPathSegment(node);\n      const nodePath = containerPath + this.segmentSeparator + newSegment;\n      return nodePath;\n    }\n    return '';\n  }\n  getPathSegment({\n    $containerProperty,\n    $containerIndex\n  }) {\n    if (!$containerProperty) {\n      throw new Error(\"Missing '$containerProperty' in AST node.\");\n    }\n    if ($containerIndex !== undefined) {\n      return $containerProperty + this.indexSeparator + $containerIndex;\n    }\n    return $containerProperty;\n  }\n  getAstNode(node, path) {\n    const segments = path.split(this.segmentSeparator);\n    return segments.reduce((previousValue, currentValue) => {\n      if (!previousValue || currentValue.length === 0) {\n        return previousValue;\n      }\n      const propertyIndex = currentValue.indexOf(this.indexSeparator);\n      if (propertyIndex > 0) {\n        const property = currentValue.substring(0, propertyIndex);\n        const arrayIndex = parseInt(currentValue.substring(propertyIndex + 1));\n        const array = previousValue[property];\n        return array === null || array === void 0 ? void 0 : array[arrayIndex];\n      }\n      return previousValue[currentValue];\n    }, node);\n  }\n}", "map": {"version": 3, "names": ["DefaultAstNodeLocator", "constructor", "segmentSeparator", "indexSeparator", "getAstNodePath", "node", "$container", "containerPath", "newSegment", "getPathSegment", "nodePath", "$containerProperty", "$containerIndex", "Error", "undefined", "getAstNode", "path", "segments", "split", "reduce", "previousValue", "currentValue", "length", "propertyIndex", "indexOf", "property", "substring", "arrayIndex", "parseInt", "array"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/ast-node-locator.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { AstNode } from '../syntax-tree.js';\r\n\r\n/**\r\n * Language-specific service for locating an `AstNode` in a document.\r\n */\r\nexport interface AstNodeLocator {\r\n\r\n    /**\r\n     * Creates a path represented by a `string` that identifies an `AstNode` inside its document.\r\n     * It must be possible to retrieve exactly the same `AstNode` from the document using this path.\r\n     *\r\n     * @param node The `AstNode` for which to create the path.\r\n     * @returns a path represented by a `string` that identifies `node` inside its document.\r\n     * @see AstNodeLocator.getAstNode\r\n     */\r\n    getAstNodePath(node: AstNode): string;\r\n\r\n    /**\r\n     * Locates an `AstNode` inside another node by following the given path.\r\n     *\r\n     * @param node Parent element.\r\n     * @param path Describes how to locate the `AstNode` inside the given `node`.\r\n     * @returns The `AstNode` located under the given path, or `undefined` if the path cannot be resolved.\r\n     * @see AstNodeLocator.getAstNodePath\r\n     */\r\n    getAstNode<T extends AstNode = AstNode>(node: AstNode, path: string): T | undefined;\r\n\r\n}\r\n\r\nexport class DefaultAstNodeLocator implements AstNodeLocator {\r\n    protected segmentSeparator = '/';\r\n    protected indexSeparator = '@';\r\n\r\n    getAstNodePath(node: AstNode): string {\r\n        if (node.$container) {\r\n            const containerPath = this.getAstNodePath(node.$container);\r\n            const newSegment = this.getPathSegment(node);\r\n            const nodePath = containerPath + this.segmentSeparator + newSegment;\r\n            return nodePath;\r\n        }\r\n        return '';\r\n    }\r\n\r\n    protected getPathSegment({ $containerProperty, $containerIndex }: AstNode): string {\r\n        if (!$containerProperty) {\r\n            throw new Error(\"Missing '$containerProperty' in AST node.\");\r\n        }\r\n        if ($containerIndex !== undefined) {\r\n            return $containerProperty + this.indexSeparator + $containerIndex;\r\n        }\r\n        return $containerProperty;\r\n    }\r\n\r\n    getAstNode<T extends AstNode = AstNode>(node: AstNode, path: string): T | undefined {\r\n        const segments = path.split(this.segmentSeparator);\r\n        return segments.reduce((previousValue, currentValue) => {\r\n            if (!previousValue || currentValue.length === 0) {\r\n                return previousValue;\r\n            }\r\n            const propertyIndex = currentValue.indexOf(this.indexSeparator);\r\n            if (propertyIndex > 0) {\r\n                const property = currentValue.substring(0, propertyIndex);\r\n                const arrayIndex = parseInt(currentValue.substring(propertyIndex + 1));\r\n                const array = (previousValue as unknown as Record<string, AstNode[]>)[property];\r\n                return array?.[arrayIndex];\r\n            }\r\n            return (previousValue as unknown as Record<string, AstNode>)[currentValue];\r\n        }, node) as T;\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAmCA,OAAM,MAAOA,qBAAqB;EAAlCC,YAAA;IACc,KAAAC,gBAAgB,GAAG,GAAG;IACtB,KAAAC,cAAc,GAAG,GAAG;EAuClC;EArCIC,cAAcA,CAACC,IAAa;IACxB,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjB,MAAMC,aAAa,GAAG,IAAI,CAACH,cAAc,CAACC,IAAI,CAACC,UAAU,CAAC;MAC1D,MAAME,UAAU,GAAG,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC;MAC5C,MAAMK,QAAQ,GAAGH,aAAa,GAAG,IAAI,CAACL,gBAAgB,GAAGM,UAAU;MACnE,OAAOE,QAAQ;IACnB;IACA,OAAO,EAAE;EACb;EAEUD,cAAcA,CAAC;IAAEE,kBAAkB;IAAEC;EAAe,CAAW;IACrE,IAAI,CAACD,kBAAkB,EAAE;MACrB,MAAM,IAAIE,KAAK,CAAC,2CAA2C,CAAC;IAChE;IACA,IAAID,eAAe,KAAKE,SAAS,EAAE;MAC/B,OAAOH,kBAAkB,GAAG,IAAI,CAACR,cAAc,GAAGS,eAAe;IACrE;IACA,OAAOD,kBAAkB;EAC7B;EAEAI,UAAUA,CAA8BV,IAAa,EAAEW,IAAY;IAC/D,MAAMC,QAAQ,GAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAChB,gBAAgB,CAAC;IAClD,OAAOe,QAAQ,CAACE,MAAM,CAAC,CAACC,aAAa,EAAEC,YAAY,KAAI;MACnD,IAAI,CAACD,aAAa,IAAIC,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAOF,aAAa;MACxB;MACA,MAAMG,aAAa,GAAGF,YAAY,CAACG,OAAO,CAAC,IAAI,CAACrB,cAAc,CAAC;MAC/D,IAAIoB,aAAa,GAAG,CAAC,EAAE;QACnB,MAAME,QAAQ,GAAGJ,YAAY,CAACK,SAAS,CAAC,CAAC,EAAEH,aAAa,CAAC;QACzD,MAAMI,UAAU,GAAGC,QAAQ,CAACP,YAAY,CAACK,SAAS,CAACH,aAAa,GAAG,CAAC,CAAC,CAAC;QACtE,MAAMM,KAAK,GAAIT,aAAsD,CAACK,QAAQ,CAAC;QAC/E,OAAOI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAGF,UAAU,CAAC;MAC9B;MACA,OAAQP,aAAoD,CAACC,YAAY,CAAC;IAC9E,CAAC,EAAEhB,IAAI,CAAM;EACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}