{"ast": null, "code": "import noop from \"../noop.js\";\nexport default function () {\n  var lines = [],\n    line;\n  return {\n    point: function (x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function () {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function () {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function () {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}", "map": {"version": 3, "names": ["noop", "lines", "line", "point", "x", "y", "m", "push", "lineStart", "lineEnd", "rejoin", "length", "pop", "concat", "shift", "result"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/clip/buffer.js"], "sourcesContent": ["import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAE7B,eAAe,YAAW;EACxB,IAAIC,KAAK,GAAG,EAAE;IACVC,IAAI;EACR,OAAO;IACLC,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;MACvBJ,IAAI,CAACK,IAAI,CAAC,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAC;IACtB,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAA,EAAW;MACpBP,KAAK,CAACM,IAAI,CAACL,IAAI,GAAG,EAAE,CAAC;IACvB,CAAC;IACDO,OAAO,EAAET,IAAI;IACbU,MAAM,EAAE,SAAAA,CAAA,EAAW;MACjB,IAAIT,KAAK,CAACU,MAAM,GAAG,CAAC,EAAEV,KAAK,CAACM,IAAI,CAACN,KAAK,CAACW,GAAG,CAAC,CAAC,CAACC,MAAM,CAACZ,KAAK,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;IACDC,MAAM,EAAE,SAAAA,CAAA,EAAW;MACjB,IAAIA,MAAM,GAAGd,KAAK;MAClBA,KAAK,GAAG,EAAE;MACVC,IAAI,GAAG,IAAI;MACX,OAAOa,MAAM;IACf;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}