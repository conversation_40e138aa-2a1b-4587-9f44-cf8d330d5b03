{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport class EmptyFileSystemProvider {\n  readFile() {\n    throw new Error('No file system is available.');\n  }\n  async readDirectory() {\n    return [];\n  }\n}\nexport const EmptyFileSystem = {\n  fileSystemProvider: () => new EmptyFileSystemProvider()\n};", "map": {"version": 3, "names": ["EmptyFileSystemProvider", "readFile", "Error", "readDirectory", "EmptyFileSystem", "fileSystemProvider"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/file-system-provider.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { URI } from '../utils/uri-utils.js';\r\n\r\nexport interface FileSystemNode {\r\n    readonly isFile: boolean;\r\n    readonly isDirectory: boolean;\r\n    readonly uri: URI;\r\n}\r\n\r\nexport type FileSystemFilter = (node: FileSystemNode) => boolean;\r\n\r\n/**\r\n * Provides methods to interact with an abstract file system. The default implementation is based on the node.js `fs` API.\r\n */\r\nexport interface FileSystemProvider {\r\n    /**\r\n     * Reads a document asynchronously from a given URI.\r\n     * @returns The string content of the file with the specified URI.\r\n     */\r\n    readFile(uri: URI): Promise<string>;\r\n    /**\r\n     * Reads the directory information for the given URI.\r\n     * @returns The list of file system entries that are contained within the specified directory.\r\n     */\r\n    readDirectory(uri: URI): Promise<FileSystemNode[]>;\r\n}\r\n\r\nexport class EmptyFileSystemProvider implements FileSystemProvider {\r\n\r\n    readFile(): Promise<string> {\r\n        throw new Error('No file system is available.');\r\n    }\r\n\r\n    async readDirectory(): Promise<FileSystemNode[]> {\r\n        return [];\r\n    }\r\n\r\n}\r\n\r\nexport const EmptyFileSystem = {\r\n    fileSystemProvider: () => new EmptyFileSystemProvider()\r\n};\r\n"], "mappings": "AAAA;;;;;AAgCA,OAAM,MAAOA,uBAAuB;EAEhCC,QAAQA,CAAA;IACJ,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;EACnD;EAEA,MAAMC,aAAaA,CAAA;IACf,OAAO,EAAE;EACb;;AAIJ,OAAO,MAAMC,eAAe,GAAG;EAC3BC,kBAAkB,EAAEA,CAAA,KAAM,IAAIL,uBAAuB;CACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}