{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nexport default (function sourceRandomExponential(source) {\n  function randomExponential(lambda) {\n    return function () {\n      return -Math.log1p(-source()) / lambda;\n    };\n  }\n  randomExponential.source = sourceRandomExponential;\n  return randomExponential;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "sourceRandomExponential", "source", "randomExponential", "lambda", "Math", "log1p"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/exponential.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomExponential(source) {\n  function randomExponential(lambda) {\n    return function() {\n      return -Math.log1p(-source()) / lambda;\n    };\n  }\n\n  randomExponential.source = sourceRandomExponential;\n\n  return randomExponential;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAE9C,eAAe,CAAC,SAASC,uBAAuBA,CAACC,MAAM,EAAE;EACvD,SAASC,iBAAiBA,CAACC,MAAM,EAAE;IACjC,OAAO,YAAW;MAChB,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,GAAGE,MAAM;IACxC,CAAC;EACH;EAEAD,iBAAiB,CAACD,MAAM,GAAGD,uBAAuB;EAElD,OAAOE,iBAAiB;AAC1B,CAAC,EAAEH,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}