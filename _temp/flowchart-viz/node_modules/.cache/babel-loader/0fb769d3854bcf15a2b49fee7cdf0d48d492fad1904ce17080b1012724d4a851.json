{"ast": null, "code": "import { assign, forEach, isRegExp, isString, map, pickBy } from \"lodash-es\";\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction tokenLabel(tokType) {\n  if (hasTokenLabel(tokType)) {\n    return tokType.LABEL;\n  } else {\n    return tokType.name;\n  }\n}\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction hasTokenLabel(obj) {\n  return isString(obj.LABEL) && obj.LABEL !== \"\";\n}\nexport class AbstractProduction {\n  get definition() {\n    return this._definition;\n  }\n  set definition(value) {\n    this._definition = value;\n  }\n  constructor(_definition) {\n    this._definition = _definition;\n  }\n  accept(visitor) {\n    visitor.visit(this);\n    forEach(this.definition, prod => {\n      prod.accept(visitor);\n    });\n  }\n}\nexport class NonTerminal extends AbstractProduction {\n  constructor(options) {\n    super([]);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n  set definition(definition) {\n    // immutable\n  }\n  get definition() {\n    if (this.referencedRule !== undefined) {\n      return this.referencedRule.definition;\n    }\n    return [];\n  }\n  accept(visitor) {\n    visitor.visit(this);\n    // don't visit children of a reference, we will get cyclic infinite loops if we do so\n  }\n}\nexport class Rule extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.orgText = \"\";\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class Alternative extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.ignoreAmbiguities = false;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class Option extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class RepetitionMandatory extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class RepetitionMandatoryWithSeparator extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class Repetition extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class RepetitionWithSeparator extends AbstractProduction {\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class Alternation extends AbstractProduction {\n  get definition() {\n    return this._definition;\n  }\n  set definition(value) {\n    this._definition = value;\n  }\n  constructor(options) {\n    super(options.definition);\n    this.idx = 1;\n    this.ignoreAmbiguities = false;\n    this.hasPredicates = false;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n}\nexport class Terminal {\n  constructor(options) {\n    this.idx = 1;\n    assign(this, pickBy(options, v => v !== undefined));\n  }\n  accept(visitor) {\n    visitor.visit(this);\n  }\n}\nexport function serializeGrammar(topRules) {\n  return map(topRules, serializeProduction);\n}\nexport function serializeProduction(node) {\n  function convertDefinition(definition) {\n    return map(definition, serializeProduction);\n  }\n  /* istanbul ignore else */\n  if (node instanceof NonTerminal) {\n    const serializedNonTerminal = {\n      type: \"NonTerminal\",\n      name: node.nonTerminalName,\n      idx: node.idx\n    };\n    if (isString(node.label)) {\n      serializedNonTerminal.label = node.label;\n    }\n    return serializedNonTerminal;\n  } else if (node instanceof Alternative) {\n    return {\n      type: \"Alternative\",\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof Option) {\n    return {\n      type: \"Option\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof RepetitionMandatory) {\n    return {\n      type: \"RepetitionMandatory\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof RepetitionMandatoryWithSeparator) {\n    return {\n      type: \"RepetitionMandatoryWithSeparator\",\n      idx: node.idx,\n      separator: serializeProduction(new Terminal({\n        terminalType: node.separator\n      })),\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof RepetitionWithSeparator) {\n    return {\n      type: \"RepetitionWithSeparator\",\n      idx: node.idx,\n      separator: serializeProduction(new Terminal({\n        terminalType: node.separator\n      })),\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof Repetition) {\n    return {\n      type: \"Repetition\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof Alternation) {\n    return {\n      type: \"Alternation\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition)\n    };\n  } else if (node instanceof Terminal) {\n    const serializedTerminal = {\n      type: \"Terminal\",\n      name: node.terminalType.name,\n      label: tokenLabel(node.terminalType),\n      idx: node.idx\n    };\n    if (isString(node.label)) {\n      serializedTerminal.terminalLabel = node.label;\n    }\n    const pattern = node.terminalType.PATTERN;\n    if (node.terminalType.PATTERN) {\n      serializedTerminal.pattern = isRegExp(pattern) ? pattern.source : pattern;\n    }\n    return serializedTerminal;\n  } else if (node instanceof Rule) {\n    return {\n      type: \"Rule\",\n      name: node.name,\n      orgText: node.orgText,\n      definition: convertDefinition(node.definition)\n    };\n    /* c8 ignore next 3 */\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}", "map": {"version": 3, "names": ["assign", "for<PERSON>ach", "isRegExp", "isString", "map", "pickBy", "tokenLabel", "tokType", "hasTokenLabel", "LABEL", "name", "obj", "AbstractProduction", "definition", "_definition", "value", "constructor", "accept", "visitor", "visit", "prod", "NonTerminal", "options", "idx", "v", "undefined", "referencedRule", "Rule", "orgText", "Alternative", "ignoreAmbiguities", "Option", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "Repetition", "RepetitionWithSeparator", "Alternation", "hasPredicates", "Terminal", "serializeGrammar", "topRules", "serializeProduction", "node", "convertDefinition", "serializedNonTerminal", "type", "nonTerminalName", "label", "separator", "terminalType", "serializedTerminal", "terminalLabel", "pattern", "PATTERN", "source", "Error"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/gast/src/model.ts"], "sourcesContent": ["import { assign, forEach, isRegExp, isString, map, pickBy } from \"lodash-es\";\nimport type {\n  IGASTVisitor,\n  IProduction,\n  IProductionWithOccurrence,\n  ISerializedGast,\n  TokenType,\n} from \"@chevrotain/types\";\n\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction tokenLabel(tokType: TokenType): string {\n  if (hasTokenLabel(tokType)) {\n    return tokType.LABEL;\n  } else {\n    return tokType.name;\n  }\n}\n\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction hasTokenLabel(\n  obj: TokenType,\n): obj is TokenType & Pick<Required<TokenType>, \"LABEL\"> {\n  return isString(obj.LABEL) && obj.LABEL !== \"\";\n}\n\nexport abstract class AbstractProduction<T extends IProduction = IProduction>\n  implements IProduction\n{\n  public get definition(): T[] {\n    return this._definition;\n  }\n  public set definition(value: T[]) {\n    this._definition = value;\n  }\n\n  constructor(protected _definition: T[]) {}\n\n  accept(visitor: IGASTVisitor): void {\n    visitor.visit(this);\n    forEach(this.definition, (prod) => {\n      prod.accept(visitor);\n    });\n  }\n}\n\nexport class NonTerminal\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public nonTerminalName!: string;\n  public label?: string;\n  public referencedRule!: Rule;\n  public idx: number = 1;\n\n  constructor(options: {\n    nonTerminalName: string;\n    label?: string;\n    referencedRule?: Rule;\n    idx?: number;\n  }) {\n    super([]);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n\n  set definition(definition: IProduction[]) {\n    // immutable\n  }\n\n  get definition(): IProduction[] {\n    if (this.referencedRule !== undefined) {\n      return this.referencedRule.definition;\n    }\n    return [];\n  }\n\n  accept(visitor: IGASTVisitor): void {\n    visitor.visit(this);\n    // don't visit children of a reference, we will get cyclic infinite loops if we do so\n  }\n}\n\nexport class Rule extends AbstractProduction {\n  public name!: string;\n  public orgText: string = \"\";\n\n  constructor(options: {\n    name: string;\n    definition: IProduction[];\n    orgText?: string;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class Alternative extends AbstractProduction {\n  public ignoreAmbiguities: boolean = false;\n\n  constructor(options: {\n    definition: IProduction[];\n    ignoreAmbiguities?: boolean;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class Option\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public idx: number = 1;\n  public maxLookahead?: number;\n\n  constructor(options: {\n    definition: IProduction[];\n    idx?: number;\n    maxLookahead?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class RepetitionMandatory\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public idx: number = 1;\n  public maxLookahead?: number;\n\n  constructor(options: {\n    definition: IProduction[];\n    idx?: number;\n    maxLookahead?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class RepetitionMandatoryWithSeparator\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public separator!: TokenType;\n  public idx: number = 1;\n  public maxLookahead?: number;\n\n  constructor(options: {\n    definition: IProduction[];\n    separator: TokenType;\n    idx?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class Repetition\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public separator!: TokenType;\n  public idx: number = 1;\n  public maxLookahead?: number;\n\n  constructor(options: {\n    definition: IProduction[];\n    idx?: number;\n    maxLookahead?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class RepetitionWithSeparator\n  extends AbstractProduction\n  implements IProductionWithOccurrence\n{\n  public separator!: TokenType;\n  public idx: number = 1;\n  public maxLookahead?: number;\n\n  constructor(options: {\n    definition: IProduction[];\n    separator: TokenType;\n    idx?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class Alternation\n  extends AbstractProduction<Alternative>\n  implements IProductionWithOccurrence\n{\n  public idx: number = 1;\n  public ignoreAmbiguities: boolean = false;\n  public hasPredicates: boolean = false;\n  public maxLookahead?: number;\n\n  public get definition(): Alternative[] {\n    return this._definition;\n  }\n  public set definition(value: Alternative[]) {\n    this._definition = value;\n  }\n\n  constructor(options: {\n    definition: Alternative[];\n    idx?: number;\n    ignoreAmbiguities?: boolean;\n    hasPredicates?: boolean;\n    maxLookahead?: number;\n  }) {\n    super(options.definition);\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n}\n\nexport class Terminal implements IProductionWithOccurrence {\n  public terminalType!: TokenType;\n  public label?: string;\n  public idx: number = 1;\n\n  constructor(options: {\n    terminalType: TokenType;\n    label?: string;\n    idx?: number;\n  }) {\n    assign(\n      this,\n      pickBy(options, (v) => v !== undefined),\n    );\n  }\n\n  accept(visitor: IGASTVisitor): void {\n    visitor.visit(this);\n  }\n}\n\nexport interface ISerializedBasic extends ISerializedGast {\n  type:\n    | \"Alternative\"\n    | \"Option\"\n    | \"RepetitionMandatory\"\n    | \"Repetition\"\n    | \"Alternation\";\n  idx?: number;\n}\n\nexport interface ISerializedGastRule extends ISerializedGast {\n  type: \"Rule\";\n  name: string;\n  orgText: string;\n}\n\nexport interface ISerializedNonTerminal extends ISerializedGast {\n  type: \"NonTerminal\";\n  name: string;\n  label?: string;\n  idx: number;\n}\n\nexport interface ISerializedTerminal extends ISerializedGast {\n  type: \"Terminal\";\n  name: string;\n  terminalLabel?: string;\n  label?: string;\n  pattern?: string;\n  idx: number;\n}\n\nexport interface ISerializedTerminalWithSeparator extends ISerializedGast {\n  type: \"RepetitionMandatoryWithSeparator\" | \"RepetitionWithSeparator\";\n  idx: number;\n  separator: ISerializedTerminal;\n}\n\nexport type ISerializedGastAny =\n  | ISerializedBasic\n  | ISerializedGastRule\n  | ISerializedNonTerminal\n  | ISerializedTerminal\n  | ISerializedTerminalWithSeparator;\n\nexport function serializeGrammar(topRules: Rule[]): ISerializedGast[] {\n  return map(topRules, serializeProduction);\n}\n\nexport function serializeProduction(node: IProduction): ISerializedGast {\n  function convertDefinition(definition: IProduction[]): ISerializedGast[] {\n    return map(definition, serializeProduction);\n  }\n  /* istanbul ignore else */\n  if (node instanceof NonTerminal) {\n    const serializedNonTerminal: ISerializedNonTerminal = {\n      type: \"NonTerminal\",\n      name: node.nonTerminalName,\n      idx: node.idx,\n    };\n\n    if (isString(node.label)) {\n      serializedNonTerminal.label = node.label;\n    }\n\n    return serializedNonTerminal;\n  } else if (node instanceof Alternative) {\n    return <ISerializedBasic>{\n      type: \"Alternative\",\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof Option) {\n    return <ISerializedBasic>{\n      type: \"Option\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof RepetitionMandatory) {\n    return <ISerializedBasic>{\n      type: \"RepetitionMandatory\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof RepetitionMandatoryWithSeparator) {\n    return <ISerializedTerminalWithSeparator>{\n      type: \"RepetitionMandatoryWithSeparator\",\n      idx: node.idx,\n      separator: <ISerializedTerminal>(\n        serializeProduction(new Terminal({ terminalType: node.separator }))\n      ),\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof RepetitionWithSeparator) {\n    return <ISerializedTerminalWithSeparator>{\n      type: \"RepetitionWithSeparator\",\n      idx: node.idx,\n      separator: <ISerializedTerminal>(\n        serializeProduction(new Terminal({ terminalType: node.separator }))\n      ),\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof Repetition) {\n    return <ISerializedBasic>{\n      type: \"Repetition\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof Alternation) {\n    return <ISerializedBasic>{\n      type: \"Alternation\",\n      idx: node.idx,\n      definition: convertDefinition(node.definition),\n    };\n  } else if (node instanceof Terminal) {\n    const serializedTerminal = <ISerializedTerminal>{\n      type: \"Terminal\",\n      name: node.terminalType.name,\n      label: tokenLabel(node.terminalType),\n      idx: node.idx,\n    };\n\n    if (isString(node.label)) {\n      serializedTerminal.terminalLabel = node.label;\n    }\n\n    const pattern = node.terminalType.PATTERN;\n    if (node.terminalType.PATTERN) {\n      serializedTerminal.pattern = isRegExp(pattern)\n        ? (<any>pattern).source\n        : pattern;\n    }\n\n    return serializedTerminal;\n  } else if (node instanceof Rule) {\n    return <ISerializedGastRule>{\n      type: \"Rule\",\n      name: node.name,\n      orgText: node.orgText,\n      definition: convertDefinition(node.definition),\n    };\n    /* c8 ignore next 3 */\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,QAAQ,WAAW;AAS5E;AACA,SAASC,UAAUA,CAACC,OAAkB;EACpC,IAAIC,aAAa,CAACD,OAAO,CAAC,EAAE;IAC1B,OAAOA,OAAO,CAACE,KAAK;GACrB,MAAM;IACL,OAAOF,OAAO,CAACG,IAAI;;AAEvB;AAEA;AACA,SAASF,aAAaA,CACpBG,GAAc;EAEd,OAAOR,QAAQ,CAACQ,GAAG,CAACF,KAAK,CAAC,IAAIE,GAAG,CAACF,KAAK,KAAK,EAAE;AAChD;AAEA,OAAM,MAAgBG,kBAAkB;EAGtC,IAAWC,UAAUA,CAAA;IACnB,OAAO,IAAI,CAACC,WAAW;EACzB;EACA,IAAWD,UAAUA,CAACE,KAAU;IAC9B,IAAI,CAACD,WAAW,GAAGC,KAAK;EAC1B;EAEAC,YAAsBF,WAAgB;IAAhB,KAAAA,WAAW,GAAXA,WAAW;EAAQ;EAEzCG,MAAMA,CAACC,OAAqB;IAC1BA,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC;IACnBlB,OAAO,CAAC,IAAI,CAACY,UAAU,EAAGO,IAAI,IAAI;MAChCA,IAAI,CAACH,MAAM,CAACC,OAAO,CAAC;IACtB,CAAC,CAAC;EACJ;;AAGF,OAAM,MAAOG,WACX,SAAQT,kBAAkB;EAQ1BI,YAAYM,OAKX;IACC,KAAK,CAAC,EAAE,CAAC;IARJ,KAAAC,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;EAEA,IAAIZ,UAAUA,CAACA,UAAyB;IACtC;EAAA;EAGF,IAAIA,UAAUA,CAAA;IACZ,IAAI,IAAI,CAACa,cAAc,KAAKD,SAAS,EAAE;MACrC,OAAO,IAAI,CAACC,cAAc,CAACb,UAAU;;IAEvC,OAAO,EAAE;EACX;EAEAI,MAAMA,CAACC,OAAqB;IAC1BA,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC;IACnB;EACF;;AAGF,OAAM,MAAOQ,IAAK,SAAQf,kBAAkB;EAI1CI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IAPpB,KAAAe,OAAO,GAAW,EAAE;IAQzB5B,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOI,WAAY,SAAQjB,kBAAkB;EAGjDI,YAAYM,OAGX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IANpB,KAAAiB,iBAAiB,GAAY,KAAK;IAOvC9B,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOM,MACX,SAAQnB,kBAAkB;EAM1BI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IARpB,KAAAU,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOO,mBACX,SAAQpB,kBAAkB;EAM1BI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IARpB,KAAAU,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOQ,gCACX,SAAQrB,kBAAkB;EAO1BI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IARpB,KAAAU,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOS,UACX,SAAQtB,kBAAkB;EAO1BI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IARpB,KAAAU,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOU,uBACX,SAAQvB,kBAAkB;EAO1BI,YAAYM,OAIX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IARpB,KAAAU,GAAG,GAAW,CAAC;IASpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOW,WACX,SAAQxB,kBAA+B;EAQvC,IAAWC,UAAUA,CAAA;IACnB,OAAO,IAAI,CAACC,WAAW;EACzB;EACA,IAAWD,UAAUA,CAACE,KAAoB;IACxC,IAAI,CAACD,WAAW,GAAGC,KAAK;EAC1B;EAEAC,YAAYM,OAMX;IACC,KAAK,CAACA,OAAO,CAACT,UAAU,CAAC;IAnBpB,KAAAU,GAAG,GAAW,CAAC;IACf,KAAAO,iBAAiB,GAAY,KAAK;IAClC,KAAAO,aAAa,GAAY,KAAK;IAkBnCrC,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;;AAGF,OAAM,MAAOa,QAAQ;EAKnBtB,YAAYM,OAIX;IANM,KAAAC,GAAG,GAAW,CAAC;IAOpBvB,MAAM,CACJ,IAAI,EACJK,MAAM,CAACiB,OAAO,EAAGE,CAAC,IAAKA,CAAC,KAAKC,SAAS,CAAC,CACxC;EACH;EAEAR,MAAMA,CAACC,OAAqB;IAC1BA,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC;EACrB;;AAgDF,OAAM,SAAUoB,gBAAgBA,CAACC,QAAgB;EAC/C,OAAOpC,GAAG,CAACoC,QAAQ,EAAEC,mBAAmB,CAAC;AAC3C;AAEA,OAAM,SAAUA,mBAAmBA,CAACC,IAAiB;EACnD,SAASC,iBAAiBA,CAAC9B,UAAyB;IAClD,OAAOT,GAAG,CAACS,UAAU,EAAE4B,mBAAmB,CAAC;EAC7C;EACA;EACA,IAAIC,IAAI,YAAYrB,WAAW,EAAE;IAC/B,MAAMuB,qBAAqB,GAA2B;MACpDC,IAAI,EAAE,aAAa;MACnBnC,IAAI,EAAEgC,IAAI,CAACI,eAAe;MAC1BvB,GAAG,EAAEmB,IAAI,CAACnB;KACX;IAED,IAAIpB,QAAQ,CAACuC,IAAI,CAACK,KAAK,CAAC,EAAE;MACxBH,qBAAqB,CAACG,KAAK,GAAGL,IAAI,CAACK,KAAK;;IAG1C,OAAOH,qBAAqB;GAC7B,MAAM,IAAIF,IAAI,YAAYb,WAAW,EAAE;IACtC,OAAyB;MACvBgB,IAAI,EAAE,aAAa;MACnBhC,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYX,MAAM,EAAE;IACjC,OAAyB;MACvBc,IAAI,EAAE,QAAQ;MACdtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbV,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYV,mBAAmB,EAAE;IAC9C,OAAyB;MACvBa,IAAI,EAAE,qBAAqB;MAC3BtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbV,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYT,gCAAgC,EAAE;IAC3D,OAAyC;MACvCY,IAAI,EAAE,kCAAkC;MACxCtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbyB,SAAS,EACPP,mBAAmB,CAAC,IAAIH,QAAQ,CAAC;QAAEW,YAAY,EAAEP,IAAI,CAACM;MAAS,CAAE,CAAC,CACnE;MACDnC,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYP,uBAAuB,EAAE;IAClD,OAAyC;MACvCU,IAAI,EAAE,yBAAyB;MAC/BtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbyB,SAAS,EACPP,mBAAmB,CAAC,IAAIH,QAAQ,CAAC;QAAEW,YAAY,EAAEP,IAAI,CAACM;MAAS,CAAE,CAAC,CACnE;MACDnC,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYR,UAAU,EAAE;IACrC,OAAyB;MACvBW,IAAI,EAAE,YAAY;MAClBtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbV,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYN,WAAW,EAAE;IACtC,OAAyB;MACvBS,IAAI,EAAE,aAAa;MACnBtB,GAAG,EAAEmB,IAAI,CAACnB,GAAG;MACbV,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;GACF,MAAM,IAAI6B,IAAI,YAAYJ,QAAQ,EAAE;IACnC,MAAMY,kBAAkB,GAAwB;MAC9CL,IAAI,EAAE,UAAU;MAChBnC,IAAI,EAAEgC,IAAI,CAACO,YAAY,CAACvC,IAAI;MAC5BqC,KAAK,EAAEzC,UAAU,CAACoC,IAAI,CAACO,YAAY,CAAC;MACpC1B,GAAG,EAAEmB,IAAI,CAACnB;KACX;IAED,IAAIpB,QAAQ,CAACuC,IAAI,CAACK,KAAK,CAAC,EAAE;MACxBG,kBAAkB,CAACC,aAAa,GAAGT,IAAI,CAACK,KAAK;;IAG/C,MAAMK,OAAO,GAAGV,IAAI,CAACO,YAAY,CAACI,OAAO;IACzC,IAAIX,IAAI,CAACO,YAAY,CAACI,OAAO,EAAE;MAC7BH,kBAAkB,CAACE,OAAO,GAAGlD,QAAQ,CAACkD,OAAO,CAAC,GACpCA,OAAQ,CAACE,MAAM,GACrBF,OAAO;;IAGb,OAAOF,kBAAkB;GAC1B,MAAM,IAAIR,IAAI,YAAYf,IAAI,EAAE;IAC/B,OAA4B;MAC1BkB,IAAI,EAAE,MAAM;MACZnC,IAAI,EAAEgC,IAAI,CAAChC,IAAI;MACfkB,OAAO,EAAEc,IAAI,CAACd,OAAO;MACrBf,UAAU,EAAE8B,iBAAiB,CAACD,IAAI,CAAC7B,UAAU;KAC9C;IACD;GACD,MAAM;IACL,MAAM0C,KAAK,CAAC,sBAAsB,CAAC;;AAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}