{"ast": null, "code": "import quantile from \"./quantile.js\";\nexport default function (values, valueof) {\n  return quantile(values, 0.5, valueof);\n}", "map": {"version": 3, "names": ["quantile", "values", "valueof"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/median.js"], "sourcesContent": ["import quantile from \"./quantile.js\";\n\nexport default function(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,MAAM,EAAEC,OAAO,EAAE;EACvC,OAAOF,QAAQ,CAACC,MAAM,EAAE,GAAG,EAAEC,OAAO,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}