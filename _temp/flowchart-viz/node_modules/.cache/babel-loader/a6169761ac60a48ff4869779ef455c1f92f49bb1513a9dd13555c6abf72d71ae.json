{"ast": null, "code": "import { GAstVisitor, NonTerminal } from \"@chevrotain/gast\";\nimport { assign, flatten, groupBy, map, some, values } from \"lodash-es\";\nexport function buildModel(productions) {\n  const generator = new CstNodeDefinitionGenerator();\n  const allRules = values(productions);\n  return map(allRules, rule => generator.visitRule(rule));\n}\nclass CstNodeDefinitionGenerator extends GAstVisitor {\n  visitRule(node) {\n    const rawElements = this.visitEach(node.definition);\n    const grouped = groupBy(rawElements, el => el.propertyName);\n    const properties = map(grouped, (group, propertyName) => {\n      const allNullable = !some(group, el => !el.canBeNull);\n      // In an alternation with a label a property name can have\n      // multiple types.\n      let propertyType = group[0].type;\n      if (group.length > 1) {\n        propertyType = map(group, g => g.type);\n      }\n      return {\n        name: propertyName,\n        type: propertyType,\n        optional: allNullable\n      };\n    });\n    return {\n      name: node.name,\n      properties: properties\n    };\n  }\n  visitAlternative(node) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true\n    });\n  }\n  visitOption(node) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true\n    });\n  }\n  visitRepetition(node) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true\n    });\n  }\n  visitRepetitionMandatory(node) {\n    return this.visitEach(node.definition);\n  }\n  visitRepetitionMandatoryWithSeparator(node) {\n    return this.visitEach(node.definition).concat({\n      propertyName: node.separator.name,\n      canBeNull: true,\n      type: getType(node.separator)\n    });\n  }\n  visitRepetitionWithSeparator(node) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true\n    }).concat({\n      propertyName: node.separator.name,\n      canBeNull: true,\n      type: getType(node.separator)\n    });\n  }\n  visitAlternation(node) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true\n    });\n  }\n  visitTerminal(node) {\n    return [{\n      propertyName: node.label || node.terminalType.name,\n      canBeNull: false,\n      type: getType(node)\n    }];\n  }\n  visitNonTerminal(node) {\n    return [{\n      propertyName: node.label || node.nonTerminalName,\n      canBeNull: false,\n      type: getType(node)\n    }];\n  }\n  visitEachAndOverrideWith(definition, override) {\n    return map(this.visitEach(definition), definition => assign({}, definition, override));\n  }\n  visitEach(definition) {\n    return flatten(map(definition, definition => this.visit(definition)));\n  }\n}\nfunction getType(production) {\n  if (production instanceof NonTerminal) {\n    return {\n      kind: \"rule\",\n      name: production.referencedRule.name\n    };\n  }\n  return {\n    kind: \"token\"\n  };\n}", "map": {"version": 3, "names": ["GAstVisitor", "NonTerminal", "assign", "flatten", "groupBy", "map", "some", "values", "buildModel", "productions", "generator", "CstNodeDefinitionGenerator", "allRules", "rule", "visitRule", "node", "rawElements", "visitEach", "definition", "grouped", "el", "propertyName", "properties", "group", "allNullable", "canBeNull", "propertyType", "type", "length", "g", "name", "optional", "visitAlternative", "visitEachAndOverrideWith", "visitOption", "visitRepetition", "visitRepetitionMandatory", "visitRepetitionMandatoryWithSeparator", "concat", "separator", "getType", "visitRepetitionWithSeparator", "visitAlternation", "visitTerminal", "label", "terminalType", "visitNonTerminal", "nonTerminalName", "override", "visit", "production", "kind", "referencedRule"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/cst-dts-gen/src/model.ts"], "sourcesContent": ["import type {\n  Alternation,\n  Alternative,\n  IProduction,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { GAstVisitor, NonTerminal } from \"@chevrotain/gast\";\nimport { assign, flatten, groupBy, map, some, values } from \"lodash-es\";\n\nexport function buildModel(\n  productions: Record<string, Rule>,\n): CstNodeTypeDefinition[] {\n  const generator = new CstNodeDefinitionGenerator();\n  const allRules = values(productions);\n  return map(allRules, (rule) => generator.visitRule(rule));\n}\n\nexport type CstNodeTypeDefinition = {\n  name: string;\n  properties: PropertyTypeDefinition[];\n};\n\nexport type PropertyTypeDefinition = {\n  name: string;\n  type: PropertyArrayType;\n  optional: boolean;\n};\n\nexport type PropertyArrayType =\n  | TokenArrayType\n  | RuleArrayType\n  | (TokenArrayType | RuleArrayType)[];\n\nexport type TokenArrayType = { kind: \"token\" };\nexport type RuleArrayType = {\n  kind: \"rule\";\n  name: string;\n};\n\nclass CstNodeDefinitionGenerator extends GAstVisitor {\n  visitRule(node: Rule): CstNodeTypeDefinition {\n    const rawElements = this.visitEach(node.definition);\n\n    const grouped = groupBy(rawElements, (el) => el.propertyName);\n    const properties = map(grouped, (group, propertyName) => {\n      const allNullable = !some(group, (el) => !el.canBeNull);\n\n      // In an alternation with a label a property name can have\n      // multiple types.\n      let propertyType: PropertyArrayType = group[0].type;\n      if (group.length > 1) {\n        propertyType = map(group, (g) => g.type);\n      }\n\n      return {\n        name: propertyName,\n        type: propertyType,\n        optional: allNullable,\n      } as PropertyTypeDefinition;\n    });\n\n    return {\n      name: node.name,\n      properties: properties,\n    };\n  }\n\n  visitAlternative(node: Alternative) {\n    return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n  }\n\n  visitOption(node: Option) {\n    return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n  }\n\n  visitRepetition(node: Repetition) {\n    return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n  }\n\n  visitRepetitionMandatory(node: RepetitionMandatory) {\n    return this.visitEach(node.definition);\n  }\n\n  visitRepetitionMandatoryWithSeparator(\n    node: RepetitionMandatoryWithSeparator,\n  ) {\n    return this.visitEach(node.definition).concat({\n      propertyName: node.separator.name,\n      canBeNull: true,\n      type: getType(node.separator),\n    });\n  }\n\n  visitRepetitionWithSeparator(node: RepetitionWithSeparator) {\n    return this.visitEachAndOverrideWith(node.definition, {\n      canBeNull: true,\n    }).concat({\n      propertyName: node.separator.name,\n      canBeNull: true,\n      type: getType(node.separator),\n    });\n  }\n\n  visitAlternation(node: Alternation) {\n    return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n  }\n\n  visitTerminal(node: Terminal): PropertyTupleElement[] {\n    return [\n      {\n        propertyName: node.label || node.terminalType.name,\n        canBeNull: false,\n        type: getType(node),\n      },\n    ];\n  }\n\n  visitNonTerminal(node: NonTerminal): PropertyTupleElement[] {\n    return [\n      {\n        propertyName: node.label || node.nonTerminalName,\n        canBeNull: false,\n        type: getType(node),\n      },\n    ];\n  }\n\n  private visitEachAndOverrideWith(\n    definition: IProduction[],\n    override: Partial<PropertyTupleElement>,\n  ) {\n    return map(\n      this.visitEach(definition),\n      (definition) => assign({}, definition, override) as PropertyTupleElement,\n    );\n  }\n\n  private visitEach(definition: IProduction[]) {\n    return flatten<PropertyTupleElement>(\n      map(\n        definition,\n        (definition) => this.visit(definition) as PropertyTupleElement[],\n      ),\n    );\n  }\n}\n\ntype PropertyTupleElement = {\n  propertyName: string;\n  canBeNull: boolean;\n  type: TokenArrayType | RuleArrayType;\n};\n\nfunction getType(\n  production: Terminal | NonTerminal | TokenType,\n): TokenArrayType | RuleArrayType {\n  if (production instanceof NonTerminal) {\n    return {\n      kind: \"rule\",\n      name: production.referencedRule.name,\n    };\n  }\n\n  return { kind: \"token\" };\n}\n"], "mappings": "AAaA,SAASA,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,WAAW;AAEvE,OAAM,SAAUC,UAAUA,CACxBC,WAAiC;EAEjC,MAAMC,SAAS,GAAG,IAAIC,0BAA0B,EAAE;EAClD,MAAMC,QAAQ,GAAGL,MAAM,CAACE,WAAW,CAAC;EACpC,OAAOJ,GAAG,CAACO,QAAQ,EAAGC,IAAI,IAAKH,SAAS,CAACI,SAAS,CAACD,IAAI,CAAC,CAAC;AAC3D;AAwBA,MAAMF,0BAA2B,SAAQX,WAAW;EAClDc,SAASA,CAACC,IAAU;IAClB,MAAMC,WAAW,GAAG,IAAI,CAACC,SAAS,CAACF,IAAI,CAACG,UAAU,CAAC;IAEnD,MAAMC,OAAO,GAAGf,OAAO,CAACY,WAAW,EAAGI,EAAE,IAAKA,EAAE,CAACC,YAAY,CAAC;IAC7D,MAAMC,UAAU,GAAGjB,GAAG,CAACc,OAAO,EAAE,CAACI,KAAK,EAAEF,YAAY,KAAI;MACtD,MAAMG,WAAW,GAAG,CAAClB,IAAI,CAACiB,KAAK,EAAGH,EAAE,IAAK,CAACA,EAAE,CAACK,SAAS,CAAC;MAEvD;MACA;MACA,IAAIC,YAAY,GAAsBH,KAAK,CAAC,CAAC,CAAC,CAACI,IAAI;MACnD,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;QACpBF,YAAY,GAAGrB,GAAG,CAACkB,KAAK,EAAGM,CAAC,IAAKA,CAAC,CAACF,IAAI,CAAC;;MAG1C,OAAO;QACLG,IAAI,EAAET,YAAY;QAClBM,IAAI,EAAED,YAAY;QAClBK,QAAQ,EAAEP;OACe;IAC7B,CAAC,CAAC;IAEF,OAAO;MACLM,IAAI,EAAEf,IAAI,CAACe,IAAI;MACfR,UAAU,EAAEA;KACb;EACH;EAEAU,gBAAgBA,CAACjB,IAAiB;IAChC,OAAO,IAAI,CAACkB,wBAAwB,CAAClB,IAAI,CAACG,UAAU,EAAE;MAAEO,SAAS,EAAE;IAAI,CAAE,CAAC;EAC5E;EAEAS,WAAWA,CAACnB,IAAY;IACtB,OAAO,IAAI,CAACkB,wBAAwB,CAAClB,IAAI,CAACG,UAAU,EAAE;MAAEO,SAAS,EAAE;IAAI,CAAE,CAAC;EAC5E;EAEAU,eAAeA,CAACpB,IAAgB;IAC9B,OAAO,IAAI,CAACkB,wBAAwB,CAAClB,IAAI,CAACG,UAAU,EAAE;MAAEO,SAAS,EAAE;IAAI,CAAE,CAAC;EAC5E;EAEAW,wBAAwBA,CAACrB,IAAyB;IAChD,OAAO,IAAI,CAACE,SAAS,CAACF,IAAI,CAACG,UAAU,CAAC;EACxC;EAEAmB,qCAAqCA,CACnCtB,IAAsC;IAEtC,OAAO,IAAI,CAACE,SAAS,CAACF,IAAI,CAACG,UAAU,CAAC,CAACoB,MAAM,CAAC;MAC5CjB,YAAY,EAAEN,IAAI,CAACwB,SAAS,CAACT,IAAI;MACjCL,SAAS,EAAE,IAAI;MACfE,IAAI,EAAEa,OAAO,CAACzB,IAAI,CAACwB,SAAS;KAC7B,CAAC;EACJ;EAEAE,4BAA4BA,CAAC1B,IAA6B;IACxD,OAAO,IAAI,CAACkB,wBAAwB,CAAClB,IAAI,CAACG,UAAU,EAAE;MACpDO,SAAS,EAAE;KACZ,CAAC,CAACa,MAAM,CAAC;MACRjB,YAAY,EAAEN,IAAI,CAACwB,SAAS,CAACT,IAAI;MACjCL,SAAS,EAAE,IAAI;MACfE,IAAI,EAAEa,OAAO,CAACzB,IAAI,CAACwB,SAAS;KAC7B,CAAC;EACJ;EAEAG,gBAAgBA,CAAC3B,IAAiB;IAChC,OAAO,IAAI,CAACkB,wBAAwB,CAAClB,IAAI,CAACG,UAAU,EAAE;MAAEO,SAAS,EAAE;IAAI,CAAE,CAAC;EAC5E;EAEAkB,aAAaA,CAAC5B,IAAc;IAC1B,OAAO,CACL;MACEM,YAAY,EAAEN,IAAI,CAAC6B,KAAK,IAAI7B,IAAI,CAAC8B,YAAY,CAACf,IAAI;MAClDL,SAAS,EAAE,KAAK;MAChBE,IAAI,EAAEa,OAAO,CAACzB,IAAI;KACnB,CACF;EACH;EAEA+B,gBAAgBA,CAAC/B,IAAiB;IAChC,OAAO,CACL;MACEM,YAAY,EAAEN,IAAI,CAAC6B,KAAK,IAAI7B,IAAI,CAACgC,eAAe;MAChDtB,SAAS,EAAE,KAAK;MAChBE,IAAI,EAAEa,OAAO,CAACzB,IAAI;KACnB,CACF;EACH;EAEQkB,wBAAwBA,CAC9Bf,UAAyB,EACzB8B,QAAuC;IAEvC,OAAO3C,GAAG,CACR,IAAI,CAACY,SAAS,CAACC,UAAU,CAAC,EACzBA,UAAU,IAAKhB,MAAM,CAAC,EAAE,EAAEgB,UAAU,EAAE8B,QAAQ,CAAyB,CACzE;EACH;EAEQ/B,SAASA,CAACC,UAAyB;IACzC,OAAOf,OAAO,CACZE,GAAG,CACDa,UAAU,EACTA,UAAU,IAAK,IAAI,CAAC+B,KAAK,CAAC/B,UAAU,CAA2B,CACjE,CACF;EACH;;AASF,SAASsB,OAAOA,CACdU,UAA8C;EAE9C,IAAIA,UAAU,YAAYjD,WAAW,EAAE;IACrC,OAAO;MACLkD,IAAI,EAAE,MAAM;MACZrB,IAAI,EAAEoB,UAAU,CAACE,cAAc,CAACtB;KACjC;;EAGH,OAAO;IAAEqB,IAAI,EAAE;EAAO,CAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}