{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport * from './comment-provider.js';\nexport * from './documentation-provider.js';\nexport * from './jsdoc.js';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/documentation/index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport * from './comment-provider.js';\r\nexport * from './documentation-provider.js';\r\nexport * from './jsdoc.js';\r\n"], "mappings": "AAAA;;;;;AAMA,cAAc,uBAAuB;AACrC,cAAc,6BAA6B;AAC3C,cAAc,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}