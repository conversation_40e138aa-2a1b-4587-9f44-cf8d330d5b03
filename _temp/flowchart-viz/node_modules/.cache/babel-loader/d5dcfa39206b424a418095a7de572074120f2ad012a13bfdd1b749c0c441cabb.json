{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport isArrayLike from './isArrayLike.js';\nimport keys from './keys.js';\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function (collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function (key) {\n        return iteratee(iterable[key], key, iterable);\n      };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\nexport default createFind;", "map": {"version": 3, "names": ["baseIteratee", "isArrayLike", "keys", "createFind", "findIndexFunc", "collection", "predicate", "fromIndex", "iterable", "Object", "iteratee", "key", "index", "undefined"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_createFind.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport isArrayLike from './isArrayLike.js';\nimport keys from './keys.js';\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function(collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function(key) { return iteratee(iterable[key], key, iterable); };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nexport default createFind;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,aAAa,EAAE;EACjC,OAAO,UAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAChD,IAAIC,QAAQ,GAAGC,MAAM,CAACJ,UAAU,CAAC;IACjC,IAAI,CAACJ,WAAW,CAACI,UAAU,CAAC,EAAE;MAC5B,IAAIK,QAAQ,GAAGV,YAAY,CAACM,SAAS,EAAE,CAAC,CAAC;MACzCD,UAAU,GAAGH,IAAI,CAACG,UAAU,CAAC;MAC7BC,SAAS,GAAG,SAAAA,CAASK,GAAG,EAAE;QAAE,OAAOD,QAAQ,CAACF,QAAQ,CAACG,GAAG,CAAC,EAAEA,GAAG,EAAEH,QAAQ,CAAC;MAAE,CAAC;IAC9E;IACA,IAAII,KAAK,GAAGR,aAAa,CAACC,UAAU,EAAEC,SAAS,EAAEC,SAAS,CAAC;IAC3D,OAAOK,KAAK,GAAG,CAAC,CAAC,GAAGJ,QAAQ,CAACE,QAAQ,GAAGL,UAAU,CAACO,KAAK,CAAC,GAAGA,KAAK,CAAC,GAAGC,SAAS;EAChF,CAAC;AACH;AAEA,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}