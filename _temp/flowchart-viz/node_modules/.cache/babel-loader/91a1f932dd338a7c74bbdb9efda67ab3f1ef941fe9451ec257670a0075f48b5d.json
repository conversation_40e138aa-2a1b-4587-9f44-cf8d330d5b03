{"ast": null, "code": "import ascending from \"./ascending.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = array.length - 1, compare = ascending) {\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}", "map": {"version": 3, "names": ["ascending", "quickselect", "array", "k", "left", "right", "length", "compare", "n", "m", "z", "Math", "log", "s", "exp", "sd", "sqrt", "newLeft", "max", "floor", "newRight", "min", "t", "i", "j", "swap"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/quickselect.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = array.length - 1, compare = ascending) {\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAGH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGP,SAAS,EAAE;EACrG,OAAOK,KAAK,GAAGD,IAAI,EAAE;IACnB,IAAIC,KAAK,GAAGD,IAAI,GAAG,GAAG,EAAE;MACtB,MAAMI,CAAC,GAAGH,KAAK,GAAGD,IAAI,GAAG,CAAC;MAC1B,MAAMK,CAAC,GAAGN,CAAC,GAAGC,IAAI,GAAG,CAAC;MACtB,MAAMM,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,CAAC;MACrB,MAAMK,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAGJ,CAAC,GAAG,CAAC,CAAC;MACnC,MAAMK,EAAE,GAAG,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACN,CAAC,GAAGG,CAAC,IAAIL,CAAC,GAAGK,CAAC,CAAC,GAAGL,CAAC,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,MAAMS,OAAO,GAAGN,IAAI,CAACO,GAAG,CAACd,IAAI,EAAEO,IAAI,CAACQ,KAAK,CAAChB,CAAC,GAAGM,CAAC,GAAGI,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MAC9D,MAAMK,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAAChB,KAAK,EAAEM,IAAI,CAACQ,KAAK,CAAChB,CAAC,GAAG,CAACK,CAAC,GAAGC,CAAC,IAAII,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MACtEd,WAAW,CAACC,KAAK,EAAEC,CAAC,EAAEc,OAAO,EAAEG,QAAQ,EAAEb,OAAO,CAAC;IACnD;IAEA,MAAMe,CAAC,GAAGpB,KAAK,CAACC,CAAC,CAAC;IAClB,IAAIoB,CAAC,GAAGnB,IAAI;IACZ,IAAIoB,CAAC,GAAGnB,KAAK;IAEboB,IAAI,CAACvB,KAAK,EAAEE,IAAI,EAAED,CAAC,CAAC;IACpB,IAAII,OAAO,CAACL,KAAK,CAACG,KAAK,CAAC,EAAEiB,CAAC,CAAC,GAAG,CAAC,EAAEG,IAAI,CAACvB,KAAK,EAAEE,IAAI,EAAEC,KAAK,CAAC;IAE1D,OAAOkB,CAAC,GAAGC,CAAC,EAAE;MACZC,IAAI,CAACvB,KAAK,EAAEqB,CAAC,EAAEC,CAAC,CAAC,EAAE,EAAED,CAAC,EAAE,EAAEC,CAAC;MAC3B,OAAOjB,OAAO,CAACL,KAAK,CAACqB,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;MACpC,OAAOhB,OAAO,CAACL,KAAK,CAACsB,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEE,CAAC;IACtC;IAEA,IAAIjB,OAAO,CAACL,KAAK,CAACE,IAAI,CAAC,EAAEkB,CAAC,CAAC,KAAK,CAAC,EAAEG,IAAI,CAACvB,KAAK,EAAEE,IAAI,EAAEoB,CAAC,CAAC,CAAC,KACnD,EAAEA,CAAC,EAAEC,IAAI,CAACvB,KAAK,EAAEsB,CAAC,EAAEnB,KAAK,CAAC;IAE/B,IAAImB,CAAC,IAAIrB,CAAC,EAAEC,IAAI,GAAGoB,CAAC,GAAG,CAAC;IACxB,IAAIrB,CAAC,IAAIqB,CAAC,EAAEnB,KAAK,GAAGmB,CAAC,GAAG,CAAC;EAC3B;EACA,OAAOtB,KAAK;AACd;AAEA,SAASuB,IAAIA,CAACvB,KAAK,EAAEqB,CAAC,EAAEC,CAAC,EAAE;EACzB,MAAMF,CAAC,GAAGpB,KAAK,CAACqB,CAAC,CAAC;EAClBrB,KAAK,CAACqB,CAAC,CAAC,GAAGrB,KAAK,CAACsB,CAAC,CAAC;EACnBtB,KAAK,CAACsB,CAAC,CAAC,GAAGF,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}