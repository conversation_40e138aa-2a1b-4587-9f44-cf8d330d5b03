{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { Deferred, OperationCancelled } from '../utils/promise-utils.js';\nimport { Emitter } from '../utils/event.js';\n/**\n * Default implementation of the async parser which simply wraps the sync parser in a promise.\n *\n * @remarks\n * A real implementation would create worker threads or web workers to offload the parsing work.\n */\nexport class DefaultAsyncParser {\n  constructor(services) {\n    this.syncParser = services.parser.LangiumParser;\n  }\n  parse(text, _cancelToken) {\n    return Promise.resolve(this.syncParser.parse(text));\n  }\n}\nexport class AbstractThreadedAsyncParser {\n  constructor(services) {\n    /**\n     * The thread count determines how many threads are used to parse files in parallel.\n     * The default value is 8. Decreasing this value increases startup performance, but decreases parallel parsing performance.\n     */\n    this.threadCount = 8;\n    /**\n     * The termination delay determines how long the parser waits for a thread to finish after a cancellation request.\n     * The default value is 200(ms).\n     */\n    this.terminationDelay = 200;\n    this.workerPool = [];\n    this.queue = [];\n    this.hydrator = services.serializer.Hydrator;\n  }\n  initializeWorkers() {\n    while (this.workerPool.length < this.threadCount) {\n      const worker = this.createWorker();\n      worker.onReady(() => {\n        if (this.queue.length > 0) {\n          const deferred = this.queue.shift();\n          if (deferred) {\n            worker.lock();\n            deferred.resolve(worker);\n          }\n        }\n      });\n      this.workerPool.push(worker);\n    }\n  }\n  async parse(text, cancelToken) {\n    const worker = await this.acquireParserWorker(cancelToken);\n    const deferred = new Deferred();\n    let timeout;\n    // If the cancellation token is requested, we wait for a certain time before terminating the worker.\n    // Since the cancellation token lives longer than the parsing process, we need to dispose the event listener.\n    // Otherwise, we might accidentally terminate the worker after the parsing process has finished.\n    const cancellation = cancelToken.onCancellationRequested(() => {\n      timeout = setTimeout(() => {\n        this.terminateWorker(worker);\n      }, this.terminationDelay);\n    });\n    worker.parse(text).then(result => {\n      const hydrated = this.hydrator.hydrate(result);\n      deferred.resolve(hydrated);\n    }).catch(err => {\n      deferred.reject(err);\n    }).finally(() => {\n      cancellation.dispose();\n      clearTimeout(timeout);\n    });\n    return deferred.promise;\n  }\n  terminateWorker(worker) {\n    worker.terminate();\n    const index = this.workerPool.indexOf(worker);\n    if (index >= 0) {\n      this.workerPool.splice(index, 1);\n    }\n  }\n  async acquireParserWorker(cancelToken) {\n    this.initializeWorkers();\n    for (const worker of this.workerPool) {\n      if (worker.ready) {\n        worker.lock();\n        return worker;\n      }\n    }\n    const deferred = new Deferred();\n    cancelToken.onCancellationRequested(() => {\n      const index = this.queue.indexOf(deferred);\n      if (index >= 0) {\n        this.queue.splice(index, 1);\n      }\n      deferred.reject(OperationCancelled);\n    });\n    this.queue.push(deferred);\n    return deferred.promise;\n  }\n}\nexport class ParserWorker {\n  get ready() {\n    return this._ready;\n  }\n  get onReady() {\n    return this.onReadyEmitter.event;\n  }\n  constructor(sendMessage, onMessage, onError, terminate) {\n    this.onReadyEmitter = new Emitter();\n    this.deferred = new Deferred();\n    this._ready = true;\n    this._parsing = false;\n    this.sendMessage = sendMessage;\n    this._terminate = terminate;\n    onMessage(result => {\n      const parseResult = result;\n      this.deferred.resolve(parseResult);\n      this.unlock();\n    });\n    onError(error => {\n      this.deferred.reject(error);\n      this.unlock();\n    });\n  }\n  terminate() {\n    this.deferred.reject(OperationCancelled);\n    this._terminate();\n  }\n  lock() {\n    this._ready = false;\n  }\n  unlock() {\n    this._parsing = false;\n    this._ready = true;\n    this.onReadyEmitter.fire();\n  }\n  parse(text) {\n    if (this._parsing) {\n      throw new Error('Parser worker is busy');\n    }\n    this._parsing = true;\n    this.deferred = new Deferred();\n    this.sendMessage(text);\n    return this.deferred.promise;\n  }\n}", "map": {"version": 3, "names": ["Deferred", "OperationCancelled", "Emitter", "<PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "services", "syncP<PERSON>er", "parser", "LangiumParser", "parse", "text", "_cancelToken", "Promise", "resolve", "AbstractThreadedAsyncParser", "threadCount", "terminationDelay", "workerPool", "queue", "hydrator", "serializer", "Hydrator", "initializeWorkers", "length", "worker", "createWorker", "onReady", "deferred", "shift", "lock", "push", "cancelToken", "acquireParserWorker", "timeout", "cancellation", "onCancellationRequested", "setTimeout", "terminateWorker", "then", "result", "hydrated", "hydrate", "catch", "err", "reject", "finally", "dispose", "clearTimeout", "promise", "terminate", "index", "indexOf", "splice", "ready", "ParserWorker", "_ready", "onReadyEmitter", "event", "sendMessage", "onMessage", "onError", "_parsing", "_terminate", "parseResult", "unlock", "error", "fire", "Error"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/parser/async-parser.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { CancellationToken } from '../utils/cancellation.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode } from '../syntax-tree.js';\r\nimport type { LangiumParser, ParseResult } from './langium-parser.js';\r\nimport type { Hydrator } from '../serializer/hydrator.js';\r\nimport type { Event } from '../utils/event.js';\r\nimport { Deferred, OperationCancelled } from '../utils/promise-utils.js';\r\nimport { Emitter } from '../utils/event.js';\r\n\r\n/**\r\n * Async parser that allows cancellation of the current parsing process.\r\n *\r\n * @remarks\r\n * The sync parser implementation is blocking the event loop, which can become quite problematic for large files.\r\n * The default implementation is not actually async. It just wraps the sync parser in a promise. A real implementation would create worker threads or web workers to offload the parsing work.\r\n */\r\nexport interface AsyncParser {\r\n    /**\r\n     * Parses the given text and returns the parse result.\r\n     *\r\n     * @param text The text to parse.\r\n     * @param cancelToken A cancellation token that can be used to cancel the parsing process.\r\n     * @returns A promise that resolves to the parse result.\r\n     *\r\n     * @throws `OperationCancelled` if the parsing process is cancelled.\r\n     */\r\n    parse<T extends AstNode>(text: string, cancelToken: CancellationToken): Promise<ParseResult<T>>;\r\n}\r\n\r\n/**\r\n * Default implementation of the async parser which simply wraps the sync parser in a promise.\r\n *\r\n * @remarks\r\n * A real implementation would create worker threads or web workers to offload the parsing work.\r\n */\r\nexport class DefaultAsyncParser implements AsyncParser {\r\n\r\n    protected readonly syncParser: LangiumParser;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.syncParser = services.parser.LangiumParser;\r\n    }\r\n\r\n    parse<T extends AstNode>(text: string, _cancelToken: CancellationToken): Promise<ParseResult<T>> {\r\n        return Promise.resolve(this.syncParser.parse<T>(text));\r\n    }\r\n}\r\n\r\nexport abstract class AbstractThreadedAsyncParser implements AsyncParser {\r\n\r\n    /**\r\n     * The thread count determines how many threads are used to parse files in parallel.\r\n     * The default value is 8. Decreasing this value increases startup performance, but decreases parallel parsing performance.\r\n     */\r\n    protected threadCount = 8;\r\n    /**\r\n     * The termination delay determines how long the parser waits for a thread to finish after a cancellation request.\r\n     * The default value is 200(ms).\r\n     */\r\n    protected terminationDelay = 200;\r\n    protected workerPool: ParserWorker[] = [];\r\n    protected queue: Array<Deferred<ParserWorker>> = [];\r\n\r\n    protected readonly hydrator: Hydrator;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.hydrator = services.serializer.Hydrator;\r\n    }\r\n\r\n    protected initializeWorkers(): void {\r\n        while (this.workerPool.length < this.threadCount) {\r\n            const worker = this.createWorker();\r\n            worker.onReady(() => {\r\n                if (this.queue.length > 0) {\r\n                    const deferred = this.queue.shift();\r\n                    if (deferred) {\r\n                        worker.lock();\r\n                        deferred.resolve(worker);\r\n                    }\r\n                }\r\n            });\r\n            this.workerPool.push(worker);\r\n        }\r\n    }\r\n\r\n    async parse<T extends AstNode>(text: string, cancelToken: CancellationToken): Promise<ParseResult<T>> {\r\n        const worker = await this.acquireParserWorker(cancelToken);\r\n        const deferred = new Deferred<ParseResult<T>>();\r\n        let timeout: NodeJS.Timeout | undefined;\r\n        // If the cancellation token is requested, we wait for a certain time before terminating the worker.\r\n        // Since the cancellation token lives longer than the parsing process, we need to dispose the event listener.\r\n        // Otherwise, we might accidentally terminate the worker after the parsing process has finished.\r\n        const cancellation = cancelToken.onCancellationRequested(() => {\r\n            timeout = setTimeout(() => {\r\n                this.terminateWorker(worker);\r\n            }, this.terminationDelay);\r\n        });\r\n        worker.parse(text).then(result => {\r\n            const hydrated = this.hydrator.hydrate<T>(result);\r\n            deferred.resolve(hydrated);\r\n        }).catch(err => {\r\n            deferred.reject(err);\r\n        }).finally(() => {\r\n            cancellation.dispose();\r\n            clearTimeout(timeout);\r\n        });\r\n        return deferred.promise;\r\n    }\r\n\r\n    protected terminateWorker(worker: ParserWorker): void {\r\n        worker.terminate();\r\n        const index = this.workerPool.indexOf(worker);\r\n        if (index >= 0) {\r\n            this.workerPool.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    protected async acquireParserWorker(cancelToken: CancellationToken): Promise<ParserWorker> {\r\n        this.initializeWorkers();\r\n        for (const worker of this.workerPool) {\r\n            if (worker.ready) {\r\n                worker.lock();\r\n                return worker;\r\n            }\r\n        }\r\n        const deferred = new Deferred<ParserWorker>();\r\n        cancelToken.onCancellationRequested(() => {\r\n            const index = this.queue.indexOf(deferred);\r\n            if (index >= 0) {\r\n                this.queue.splice(index, 1);\r\n            }\r\n            deferred.reject(OperationCancelled);\r\n        });\r\n        this.queue.push(deferred);\r\n        return deferred.promise;\r\n    }\r\n\r\n    protected abstract createWorker(): ParserWorker;\r\n}\r\n\r\nexport type WorkerMessagePost = (message: unknown) => void;\r\nexport type WorkerMessageCallback = (cb: (message: unknown) => void) => void;\r\n\r\nexport class ParserWorker {\r\n\r\n    protected readonly sendMessage: WorkerMessagePost;\r\n    protected readonly _terminate: () => void;\r\n    protected readonly onReadyEmitter = new Emitter<void>();\r\n\r\n    protected deferred = new Deferred<ParseResult>();\r\n    protected _ready = true;\r\n    protected _parsing = false;\r\n\r\n    get ready(): boolean {\r\n        return this._ready;\r\n    }\r\n\r\n    get onReady(): Event<void> {\r\n        return this.onReadyEmitter.event;\r\n    }\r\n\r\n    constructor(sendMessage: WorkerMessagePost, onMessage: WorkerMessageCallback, onError: WorkerMessageCallback, terminate: () => void) {\r\n        this.sendMessage = sendMessage;\r\n        this._terminate = terminate;\r\n        onMessage(result => {\r\n            const parseResult = result as ParseResult;\r\n            this.deferred.resolve(parseResult);\r\n            this.unlock();\r\n        });\r\n        onError(error => {\r\n            this.deferred.reject(error);\r\n            this.unlock();\r\n        });\r\n    }\r\n\r\n    terminate(): void {\r\n        this.deferred.reject(OperationCancelled);\r\n        this._terminate();\r\n    }\r\n\r\n    lock(): void {\r\n        this._ready = false;\r\n    }\r\n\r\n    unlock(): void {\r\n        this._parsing = false;\r\n        this._ready = true;\r\n        this.onReadyEmitter.fire();\r\n    }\r\n\r\n    parse(text: string): Promise<ParseResult> {\r\n        if (this._parsing) {\r\n            throw new Error('Parser worker is busy');\r\n        }\r\n        this._parsing = true;\r\n        this.deferred = new Deferred();\r\n        this.sendMessage(text);\r\n        return this.deferred.promise;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAYA,SAASA,QAAQ,EAAEC,kBAAkB,QAAQ,2BAA2B;AACxE,SAASC,OAAO,QAAQ,mBAAmB;AAsB3C;;;;;;AAMA,OAAM,MAAOC,kBAAkB;EAI3BC,YAAYC,QAA6B;IACrC,IAAI,CAACC,UAAU,GAAGD,QAAQ,CAACE,MAAM,CAACC,aAAa;EACnD;EAEAC,KAAKA,CAAoBC,IAAY,EAAEC,YAA+B;IAClE,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACP,UAAU,CAACG,KAAK,CAAIC,IAAI,CAAC,CAAC;EAC1D;;AAGJ,OAAM,MAAgBI,2BAA2B;EAiB7CV,YAAYC,QAA6B;IAfzC;;;;IAIU,KAAAU,WAAW,GAAG,CAAC;IACzB;;;;IAIU,KAAAC,gBAAgB,GAAG,GAAG;IACtB,KAAAC,UAAU,GAAmB,EAAE;IAC/B,KAAAC,KAAK,GAAkC,EAAE;IAK/C,IAAI,CAACC,QAAQ,GAAGd,QAAQ,CAACe,UAAU,CAACC,QAAQ;EAChD;EAEUC,iBAAiBA,CAAA;IACvB,OAAO,IAAI,CAACL,UAAU,CAACM,MAAM,GAAG,IAAI,CAACR,WAAW,EAAE;MAC9C,MAAMS,MAAM,GAAG,IAAI,CAACC,YAAY,EAAE;MAClCD,MAAM,CAACE,OAAO,CAAC,MAAK;QAChB,IAAI,IAAI,CAACR,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;UACvB,MAAMI,QAAQ,GAAG,IAAI,CAACT,KAAK,CAACU,KAAK,EAAE;UACnC,IAAID,QAAQ,EAAE;YACVH,MAAM,CAACK,IAAI,EAAE;YACbF,QAAQ,CAACd,OAAO,CAACW,MAAM,CAAC;UAC5B;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,CAACP,UAAU,CAACa,IAAI,CAACN,MAAM,CAAC;IAChC;EACJ;EAEA,MAAMf,KAAKA,CAAoBC,IAAY,EAAEqB,WAA8B;IACvE,MAAMP,MAAM,GAAG,MAAM,IAAI,CAACQ,mBAAmB,CAACD,WAAW,CAAC;IAC1D,MAAMJ,QAAQ,GAAG,IAAI3B,QAAQ,EAAkB;IAC/C,IAAIiC,OAAmC;IACvC;IACA;IACA;IACA,MAAMC,YAAY,GAAGH,WAAW,CAACI,uBAAuB,CAAC,MAAK;MAC1DF,OAAO,GAAGG,UAAU,CAAC,MAAK;QACtB,IAAI,CAACC,eAAe,CAACb,MAAM,CAAC;MAChC,CAAC,EAAE,IAAI,CAACR,gBAAgB,CAAC;IAC7B,CAAC,CAAC;IACFQ,MAAM,CAACf,KAAK,CAACC,IAAI,CAAC,CAAC4B,IAAI,CAACC,MAAM,IAAG;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACrB,QAAQ,CAACsB,OAAO,CAAIF,MAAM,CAAC;MACjDZ,QAAQ,CAACd,OAAO,CAAC2B,QAAQ,CAAC;IAC9B,CAAC,CAAC,CAACE,KAAK,CAACC,GAAG,IAAG;MACXhB,QAAQ,CAACiB,MAAM,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC,CAACE,OAAO,CAAC,MAAK;MACZX,YAAY,CAACY,OAAO,EAAE;MACtBC,YAAY,CAACd,OAAO,CAAC;IACzB,CAAC,CAAC;IACF,OAAON,QAAQ,CAACqB,OAAO;EAC3B;EAEUX,eAAeA,CAACb,MAAoB;IAC1CA,MAAM,CAACyB,SAAS,EAAE;IAClB,MAAMC,KAAK,GAAG,IAAI,CAACjC,UAAU,CAACkC,OAAO,CAAC3B,MAAM,CAAC;IAC7C,IAAI0B,KAAK,IAAI,CAAC,EAAE;MACZ,IAAI,CAACjC,UAAU,CAACmC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACpC;EACJ;EAEU,MAAMlB,mBAAmBA,CAACD,WAA8B;IAC9D,IAAI,CAACT,iBAAiB,EAAE;IACxB,KAAK,MAAME,MAAM,IAAI,IAAI,CAACP,UAAU,EAAE;MAClC,IAAIO,MAAM,CAAC6B,KAAK,EAAE;QACd7B,MAAM,CAACK,IAAI,EAAE;QACb,OAAOL,MAAM;MACjB;IACJ;IACA,MAAMG,QAAQ,GAAG,IAAI3B,QAAQ,EAAgB;IAC7C+B,WAAW,CAACI,uBAAuB,CAAC,MAAK;MACrC,MAAMe,KAAK,GAAG,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAACxB,QAAQ,CAAC;MAC1C,IAAIuB,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAAChC,KAAK,CAACkC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC/B;MACAvB,QAAQ,CAACiB,MAAM,CAAC3C,kBAAkB,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACiB,KAAK,CAACY,IAAI,CAACH,QAAQ,CAAC;IACzB,OAAOA,QAAQ,CAACqB,OAAO;EAC3B;;AAQJ,OAAM,MAAOM,YAAY;EAUrB,IAAID,KAAKA,CAAA;IACL,OAAO,IAAI,CAACE,MAAM;EACtB;EAEA,IAAI7B,OAAOA,CAAA;IACP,OAAO,IAAI,CAAC8B,cAAc,CAACC,KAAK;EACpC;EAEArD,YAAYsD,WAA8B,EAAEC,SAAgC,EAAEC,OAA8B,EAAEX,SAAqB;IAdhH,KAAAO,cAAc,GAAG,IAAItD,OAAO,EAAQ;IAE7C,KAAAyB,QAAQ,GAAG,IAAI3B,QAAQ,EAAe;IACtC,KAAAuD,MAAM,GAAG,IAAI;IACb,KAAAM,QAAQ,GAAG,KAAK;IAWtB,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACI,UAAU,GAAGb,SAAS;IAC3BU,SAAS,CAACpB,MAAM,IAAG;MACf,MAAMwB,WAAW,GAAGxB,MAAqB;MACzC,IAAI,CAACZ,QAAQ,CAACd,OAAO,CAACkD,WAAW,CAAC;MAClC,IAAI,CAACC,MAAM,EAAE;IACjB,CAAC,CAAC;IACFJ,OAAO,CAACK,KAAK,IAAG;MACZ,IAAI,CAACtC,QAAQ,CAACiB,MAAM,CAACqB,KAAK,CAAC;MAC3B,IAAI,CAACD,MAAM,EAAE;IACjB,CAAC,CAAC;EACN;EAEAf,SAASA,CAAA;IACL,IAAI,CAACtB,QAAQ,CAACiB,MAAM,CAAC3C,kBAAkB,CAAC;IACxC,IAAI,CAAC6D,UAAU,EAAE;EACrB;EAEAjC,IAAIA,CAAA;IACA,IAAI,CAAC0B,MAAM,GAAG,KAAK;EACvB;EAEAS,MAAMA,CAAA;IACF,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACN,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,cAAc,CAACU,IAAI,EAAE;EAC9B;EAEAzD,KAAKA,CAACC,IAAY;IACd,IAAI,IAAI,CAACmD,QAAQ,EAAE;MACf,MAAM,IAAIM,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,IAAI,CAACN,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAClC,QAAQ,GAAG,IAAI3B,QAAQ,EAAE;IAC9B,IAAI,CAAC0D,WAAW,CAAChD,IAAI,CAAC;IACtB,OAAO,IAAI,CAACiB,QAAQ,CAACqB,OAAO;EAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}