{"ast": null, "code": "import { hasTokenLabel, tokenLabel } from \"../scan/tokens_public.js\";\nimport { first, map, reduce } from \"lodash-es\";\nimport { getProductionDslName, NonTerminal, Rule, Terminal } from \"@chevrotain/gast\";\nexport const defaultParserErrorProvider = {\n  buildMismatchTokenMessage({\n    expected,\n    actual,\n    previous,\n    ruleName\n  }) {\n    const hasLabel = hasTokenLabel(expected);\n    const expectedMsg = hasLabel ? `--> ${tokenLabel(expected)} <--` : `token of type --> ${expected.name} <--`;\n    const msg = `Expecting ${expectedMsg} but found --> '${actual.image}' <--`;\n    return msg;\n  },\n  buildNotAllInputParsedMessage({\n    firstRedundant,\n    ruleName\n  }) {\n    return \"Redundant input, expecting EOF but found: \" + firstRedundant.image;\n  },\n  buildNoViableAltMessage({\n    expectedPathsPerAlt,\n    actual,\n    previous,\n    customUserDescription,\n    ruleName\n  }) {\n    const errPrefix = \"Expecting: \";\n    // TODO: issue: No Viable Alternative Error may have incomplete details. #502\n    const actualText = first(actual).image;\n    const errSuffix = \"\\nbut found: '\" + actualText + \"'\";\n    if (customUserDescription) {\n      return errPrefix + customUserDescription + errSuffix;\n    } else {\n      const allLookAheadPaths = reduce(expectedPathsPerAlt, (result, currAltPaths) => result.concat(currAltPaths), []);\n      const nextValidTokenSequences = map(allLookAheadPaths, currPath => `[${map(currPath, currTokenType => tokenLabel(currTokenType)).join(\", \")}]`);\n      const nextValidSequenceItems = map(nextValidTokenSequences, (itemMsg, idx) => `  ${idx + 1}. ${itemMsg}`);\n      const calculatedDescription = `one of these possible Token sequences:\\n${nextValidSequenceItems.join(\"\\n\")}`;\n      return errPrefix + calculatedDescription + errSuffix;\n    }\n  },\n  buildEarlyExitMessage({\n    expectedIterationPaths,\n    actual,\n    customUserDescription,\n    ruleName\n  }) {\n    const errPrefix = \"Expecting: \";\n    // TODO: issue: No Viable Alternative Error may have incomplete details. #502\n    const actualText = first(actual).image;\n    const errSuffix = \"\\nbut found: '\" + actualText + \"'\";\n    if (customUserDescription) {\n      return errPrefix + customUserDescription + errSuffix;\n    } else {\n      const nextValidTokenSequences = map(expectedIterationPaths, currPath => `[${map(currPath, currTokenType => tokenLabel(currTokenType)).join(\",\")}]`);\n      const calculatedDescription = `expecting at least one iteration which starts with one of these possible Token sequences::\\n  ` + `<${nextValidTokenSequences.join(\" ,\")}>`;\n      return errPrefix + calculatedDescription + errSuffix;\n    }\n  }\n};\nObject.freeze(defaultParserErrorProvider);\nexport const defaultGrammarResolverErrorProvider = {\n  buildRuleNotFoundError(topLevelRule, undefinedRule) {\n    const msg = \"Invalid grammar, reference to a rule which is not defined: ->\" + undefinedRule.nonTerminalName + \"<-\\n\" + \"inside top level rule: ->\" + topLevelRule.name + \"<-\";\n    return msg;\n  }\n};\nexport const defaultGrammarValidatorErrorProvider = {\n  buildDuplicateFoundError(topLevelRule, duplicateProds) {\n    function getExtraProductionArgument(prod) {\n      if (prod instanceof Terminal) {\n        return prod.terminalType.name;\n      } else if (prod instanceof NonTerminal) {\n        return prod.nonTerminalName;\n      } else {\n        return \"\";\n      }\n    }\n    const topLevelName = topLevelRule.name;\n    const duplicateProd = first(duplicateProds);\n    const index = duplicateProd.idx;\n    const dslName = getProductionDslName(duplicateProd);\n    const extraArgument = getExtraProductionArgument(duplicateProd);\n    const hasExplicitIndex = index > 0;\n    let msg = `->${dslName}${hasExplicitIndex ? index : \"\"}<- ${extraArgument ? `with argument: ->${extraArgument}<-` : \"\"}\n                  appears more than once (${duplicateProds.length} times) in the top level rule: ->${topLevelName}<-.                  \n                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES \n                  `;\n    // white space trimming time! better to trim afterwards as it allows to use WELL formatted multi line template strings...\n    msg = msg.replace(/[ \\t]+/g, \" \");\n    msg = msg.replace(/\\s\\s+/g, \"\\n\");\n    return msg;\n  },\n  buildNamespaceConflictError(rule) {\n    const errMsg = `Namespace conflict found in grammar.\\n` + `The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${rule.name}>.\\n` + `To resolve this make sure each Terminal and Non-Terminal names are unique\\n` + `This is easy to accomplish by using the convention that Terminal names start with an uppercase letter\\n` + `and Non-Terminal names start with a lower case letter.`;\n    return errMsg;\n  },\n  buildAlternationPrefixAmbiguityError(options) {\n    const pathMsg = map(options.prefixPath, currTok => tokenLabel(currTok)).join(\", \");\n    const occurrence = options.alternation.idx === 0 ? \"\" : options.alternation.idx;\n    const errMsg = `Ambiguous alternatives: <${options.ambiguityIndices.join(\" ,\")}> due to common lookahead prefix\\n` + `in <OR${occurrence}> inside <${options.topLevelRule.name}> Rule,\\n` + `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n` + `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX\\n` + `For Further details.`;\n    return errMsg;\n  },\n  buildAlternationAmbiguityError(options) {\n    const pathMsg = map(options.prefixPath, currtok => tokenLabel(currtok)).join(\", \");\n    const occurrence = options.alternation.idx === 0 ? \"\" : options.alternation.idx;\n    let currMessage = `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\" ,\")}> in <OR${occurrence}>` + ` inside <${options.topLevelRule.name}> Rule,\\n` + `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`;\n    currMessage = currMessage + `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` + `For Further details.`;\n    return currMessage;\n  },\n  buildEmptyRepetitionError(options) {\n    let dslName = getProductionDslName(options.repetition);\n    if (options.repetition.idx !== 0) {\n      dslName += options.repetition.idx;\n    }\n    const errMsg = `The repetition <${dslName}> within Rule <${options.topLevelRule.name}> can never consume any tokens.\\n` + `This could lead to an infinite loop.`;\n    return errMsg;\n  },\n  // TODO: remove - `errors_public` from nyc.config.js exclude\n  //       once this method is fully removed from this file\n  buildTokenNameError(options) {\n    /* istanbul ignore next */\n    return \"deprecated\";\n  },\n  buildEmptyAlternationError(options) {\n    const errMsg = `Ambiguous empty alternative: <${options.emptyChoiceIdx + 1}>` + ` in <OR${options.alternation.idx}> inside <${options.topLevelRule.name}> Rule.\\n` + `Only the last alternative may be an empty alternative.`;\n    return errMsg;\n  },\n  buildTooManyAlternativesError(options) {\n    const errMsg = `An Alternation cannot have more than 256 alternatives:\\n` + `<OR${options.alternation.idx}> inside <${options.topLevelRule.name}> Rule.\\n has ${options.alternation.definition.length + 1} alternatives.`;\n    return errMsg;\n  },\n  buildLeftRecursionError(options) {\n    const ruleName = options.topLevelRule.name;\n    const pathNames = map(options.leftRecursionPath, currRule => currRule.name);\n    const leftRecursivePath = `${ruleName} --> ${pathNames.concat([ruleName]).join(\" --> \")}`;\n    const errMsg = `Left Recursion found in grammar.\\n` + `rule: <${ruleName}> can be invoked from itself (directly or indirectly)\\n` + `without consuming any Tokens. The grammar path that causes this is: \\n ${leftRecursivePath}\\n` + ` To fix this refactor your grammar to remove the left recursion.\\n` + `see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`;\n    return errMsg;\n  },\n  // TODO: remove - `errors_public` from nyc.config.js exclude\n  //       once this method is fully removed from this file\n  buildInvalidRuleNameError(options) {\n    /* istanbul ignore next */\n    return \"deprecated\";\n  },\n  buildDuplicateRuleNameError(options) {\n    let ruleName;\n    if (options.topLevelRule instanceof Rule) {\n      ruleName = options.topLevelRule.name;\n    } else {\n      ruleName = options.topLevelRule;\n    }\n    const errMsg = `Duplicate definition, rule: ->${ruleName}<- is already defined in the grammar: ->${options.grammarName}<-`;\n    return errMsg;\n  }\n};", "map": {"version": 3, "names": ["hasTokenLabel", "tokenLabel", "first", "map", "reduce", "getProductionDslName", "NonTerminal", "Rule", "Terminal", "defaultParserErrorProvider", "buildMismatchTokenMessage", "expected", "actual", "previous", "ruleName", "<PERSON><PERSON><PERSON><PERSON>", "expectedMsg", "name", "msg", "image", "buildNotAllInputParsedMessage", "firstRedundant", "buildNoViableAltMessage", "expectedPathsPerAlt", "customUserDescription", "errPrefix", "actualText", "err<PERSON><PERSON><PERSON>", "allLookAheadPaths", "result", "currAltPaths", "concat", "nextValidTokenSequences", "currPath", "currTokenType", "join", "nextValidSequenceItems", "itemMsg", "idx", "calculatedDescription", "buildEarlyExitMessage", "expectedIterationPaths", "Object", "freeze", "defaultGrammarResolverErrorProvider", "buildRuleNotFoundError", "topLevelRule", "undefinedRule", "nonTerminalName", "defaultGrammarValidatorErrorProvider", "buildDuplicateFoundError", "duplicateProds", "getExtraProductionArgument", "prod", "terminalType", "topLevelName", "duplicateProd", "index", "dslName", "extraArgument", "hasExplicitIndex", "length", "replace", "buildNamespaceConflictError", "rule", "errMsg", "buildAlternationPrefixAmbiguityError", "options", "pathMsg", "prefixPath", "currTok", "occurrence", "alternation", "ambiguityIndices", "buildAlternationAmbiguityError", "currtok", "currMessage", "buildEmptyRepetitionError", "repetition", "buildTokenNameError", "buildEmptyAlternationError", "emptyChoiceIdx", "buildTooManyAlternativesError", "definition", "buildLeftRecursionError", "pathNames", "leftRecursionPath", "currRule", "leftRecursivePath", "buildInvalidRuleNameError", "buildDuplicateRuleNameError", "grammarName"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/errors_public.ts"], "sourcesContent": ["import { hasTokenLabel, tokenLabel } from \"../scan/tokens_public.js\";\nimport { first, map, reduce } from \"lodash-es\";\nimport {\n  Alternation,\n  getProductionDslName,\n  NonTerminal,\n  Rule,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport {\n  IParserErrorMessageProvider,\n  IProductionWithOccurrence,\n  TokenType,\n} from \"@chevrotain/types\";\nimport {\n  IGrammarResolverErrorMessageProvider,\n  IGrammarValidatorErrorMessageProvider,\n} from \"./grammar/types.js\";\n\nexport const defaultParserErrorProvider: IParserErrorMessageProvider = {\n  buildMismatchTokenMessage({ expected, actual, previous, ruleName }): string {\n    const hasLabel = hasTokenLabel(expected);\n    const expectedMsg = hasLabel\n      ? `--> ${tokenLabel(expected)} <--`\n      : `token of type --> ${expected.name} <--`;\n\n    const msg = `Expecting ${expectedMsg} but found --> '${actual.image}' <--`;\n\n    return msg;\n  },\n\n  buildNotAllInputParsedMessage({ firstRedundant, ruleName }): string {\n    return \"Redundant input, expecting EOF but found: \" + firstRedundant.image;\n  },\n\n  buildNoViableAltMessage({\n    expectedPathsPerAlt,\n    actual,\n    previous,\n    customUserDescription,\n    ruleName,\n  }): string {\n    const errPrefix = \"Expecting: \";\n    // TODO: issue: No Viable Alternative Error may have incomplete details. #502\n    const actualText = first(actual)!.image;\n    const errSuffix = \"\\nbut found: '\" + actualText + \"'\";\n\n    if (customUserDescription) {\n      return errPrefix + customUserDescription + errSuffix;\n    } else {\n      const allLookAheadPaths = reduce(\n        expectedPathsPerAlt,\n        (result, currAltPaths) => result.concat(currAltPaths),\n        [] as TokenType[][],\n      );\n      const nextValidTokenSequences = map(\n        allLookAheadPaths,\n        (currPath) =>\n          `[${map(currPath, (currTokenType) => tokenLabel(currTokenType)).join(\n            \", \",\n          )}]`,\n      );\n      const nextValidSequenceItems = map(\n        nextValidTokenSequences,\n        (itemMsg, idx) => `  ${idx + 1}. ${itemMsg}`,\n      );\n      const calculatedDescription = `one of these possible Token sequences:\\n${nextValidSequenceItems.join(\n        \"\\n\",\n      )}`;\n\n      return errPrefix + calculatedDescription + errSuffix;\n    }\n  },\n\n  buildEarlyExitMessage({\n    expectedIterationPaths,\n    actual,\n    customUserDescription,\n    ruleName,\n  }): string {\n    const errPrefix = \"Expecting: \";\n    // TODO: issue: No Viable Alternative Error may have incomplete details. #502\n    const actualText = first(actual)!.image;\n    const errSuffix = \"\\nbut found: '\" + actualText + \"'\";\n\n    if (customUserDescription) {\n      return errPrefix + customUserDescription + errSuffix;\n    } else {\n      const nextValidTokenSequences = map(\n        expectedIterationPaths,\n        (currPath) =>\n          `[${map(currPath, (currTokenType) => tokenLabel(currTokenType)).join(\n            \",\",\n          )}]`,\n      );\n      const calculatedDescription =\n        `expecting at least one iteration which starts with one of these possible Token sequences::\\n  ` +\n        `<${nextValidTokenSequences.join(\" ,\")}>`;\n\n      return errPrefix + calculatedDescription + errSuffix;\n    }\n  },\n};\n\nObject.freeze(defaultParserErrorProvider);\n\nexport const defaultGrammarResolverErrorProvider: IGrammarResolverErrorMessageProvider =\n  {\n    buildRuleNotFoundError(\n      topLevelRule: Rule,\n      undefinedRule: NonTerminal,\n    ): string {\n      const msg =\n        \"Invalid grammar, reference to a rule which is not defined: ->\" +\n        undefinedRule.nonTerminalName +\n        \"<-\\n\" +\n        \"inside top level rule: ->\" +\n        topLevelRule.name +\n        \"<-\";\n      return msg;\n    },\n  };\n\nexport const defaultGrammarValidatorErrorProvider: IGrammarValidatorErrorMessageProvider =\n  {\n    buildDuplicateFoundError(\n      topLevelRule: Rule,\n      duplicateProds: IProductionWithOccurrence[],\n    ): string {\n      function getExtraProductionArgument(\n        prod: IProductionWithOccurrence,\n      ): string {\n        if (prod instanceof Terminal) {\n          return prod.terminalType.name;\n        } else if (prod instanceof NonTerminal) {\n          return prod.nonTerminalName;\n        } else {\n          return \"\";\n        }\n      }\n\n      const topLevelName = topLevelRule.name;\n      const duplicateProd = first(duplicateProds)!;\n      const index = duplicateProd.idx;\n      const dslName = getProductionDslName(duplicateProd);\n      const extraArgument = getExtraProductionArgument(duplicateProd);\n\n      const hasExplicitIndex = index > 0;\n      let msg = `->${dslName}${hasExplicitIndex ? index : \"\"}<- ${\n        extraArgument ? `with argument: ->${extraArgument}<-` : \"\"\n      }\n                  appears more than once (${\n                    duplicateProds.length\n                  } times) in the top level rule: ->${topLevelName}<-.                  \n                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES \n                  `;\n\n      // white space trimming time! better to trim afterwards as it allows to use WELL formatted multi line template strings...\n      msg = msg.replace(/[ \\t]+/g, \" \");\n      msg = msg.replace(/\\s\\s+/g, \"\\n\");\n\n      return msg;\n    },\n\n    buildNamespaceConflictError(rule: Rule): string {\n      const errMsg =\n        `Namespace conflict found in grammar.\\n` +\n        `The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${rule.name}>.\\n` +\n        `To resolve this make sure each Terminal and Non-Terminal names are unique\\n` +\n        `This is easy to accomplish by using the convention that Terminal names start with an uppercase letter\\n` +\n        `and Non-Terminal names start with a lower case letter.`;\n\n      return errMsg;\n    },\n\n    buildAlternationPrefixAmbiguityError(options: {\n      topLevelRule: Rule;\n      prefixPath: TokenType[];\n      ambiguityIndices: number[];\n      alternation: Alternation;\n    }): string {\n      const pathMsg = map(options.prefixPath, (currTok) =>\n        tokenLabel(currTok),\n      ).join(\", \");\n      const occurrence =\n        options.alternation.idx === 0 ? \"\" : options.alternation.idx;\n      const errMsg =\n        `Ambiguous alternatives: <${options.ambiguityIndices.join(\n          \" ,\",\n        )}> due to common lookahead prefix\\n` +\n        `in <OR${occurrence}> inside <${options.topLevelRule.name}> Rule,\\n` +\n        `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n` +\n        `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX\\n` +\n        `For Further details.`;\n\n      return errMsg;\n    },\n\n    buildAlternationAmbiguityError(options: {\n      topLevelRule: Rule;\n      prefixPath: TokenType[];\n      ambiguityIndices: number[];\n      alternation: Alternation;\n    }): string {\n      const pathMsg = map(options.prefixPath, (currtok) =>\n        tokenLabel(currtok),\n      ).join(\", \");\n      const occurrence =\n        options.alternation.idx === 0 ? \"\" : options.alternation.idx;\n      let currMessage =\n        `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\n          \" ,\",\n        )}> in <OR${occurrence}>` +\n        ` inside <${options.topLevelRule.name}> Rule,\\n` +\n        `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`;\n\n      currMessage =\n        currMessage +\n        `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` +\n        `For Further details.`;\n      return currMessage;\n    },\n\n    buildEmptyRepetitionError(options: {\n      topLevelRule: Rule;\n      repetition: IProductionWithOccurrence;\n    }): string {\n      let dslName = getProductionDslName(options.repetition);\n      if (options.repetition.idx !== 0) {\n        dslName += options.repetition.idx;\n      }\n\n      const errMsg =\n        `The repetition <${dslName}> within Rule <${options.topLevelRule.name}> can never consume any tokens.\\n` +\n        `This could lead to an infinite loop.`;\n\n      return errMsg;\n    },\n\n    // TODO: remove - `errors_public` from nyc.config.js exclude\n    //       once this method is fully removed from this file\n    buildTokenNameError(options: {\n      tokenType: TokenType;\n      expectedPattern: RegExp;\n    }): string {\n      /* istanbul ignore next */\n      return \"deprecated\";\n    },\n\n    buildEmptyAlternationError(options: {\n      topLevelRule: Rule;\n      alternation: Alternation;\n      emptyChoiceIdx: number;\n    }): string {\n      const errMsg =\n        `Ambiguous empty alternative: <${options.emptyChoiceIdx + 1}>` +\n        ` in <OR${options.alternation.idx}> inside <${options.topLevelRule.name}> Rule.\\n` +\n        `Only the last alternative may be an empty alternative.`;\n\n      return errMsg;\n    },\n\n    buildTooManyAlternativesError(options: {\n      topLevelRule: Rule;\n      alternation: Alternation;\n    }): string {\n      const errMsg =\n        `An Alternation cannot have more than 256 alternatives:\\n` +\n        `<OR${options.alternation.idx}> inside <${\n          options.topLevelRule.name\n        }> Rule.\\n has ${\n          options.alternation.definition.length + 1\n        } alternatives.`;\n\n      return errMsg;\n    },\n\n    buildLeftRecursionError(options: {\n      topLevelRule: Rule;\n      leftRecursionPath: Rule[];\n    }): string {\n      const ruleName = options.topLevelRule.name;\n      const pathNames = map(\n        options.leftRecursionPath,\n        (currRule) => currRule.name,\n      );\n      const leftRecursivePath = `${ruleName} --> ${pathNames\n        .concat([ruleName])\n        .join(\" --> \")}`;\n      const errMsg =\n        `Left Recursion found in grammar.\\n` +\n        `rule: <${ruleName}> can be invoked from itself (directly or indirectly)\\n` +\n        `without consuming any Tokens. The grammar path that causes this is: \\n ${leftRecursivePath}\\n` +\n        ` To fix this refactor your grammar to remove the left recursion.\\n` +\n        `see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`;\n\n      return errMsg;\n    },\n\n    // TODO: remove - `errors_public` from nyc.config.js exclude\n    //       once this method is fully removed from this file\n    buildInvalidRuleNameError(options: {\n      topLevelRule: Rule;\n      expectedPattern: RegExp;\n    }): string {\n      /* istanbul ignore next */\n      return \"deprecated\";\n    },\n\n    buildDuplicateRuleNameError(options: {\n      topLevelRule: Rule | string;\n      grammarName: string;\n    }): string {\n      let ruleName;\n      if (options.topLevelRule instanceof Rule) {\n        ruleName = options.topLevelRule.name;\n      } else {\n        ruleName = options.topLevelRule;\n      }\n\n      const errMsg = `Duplicate definition, rule: ->${ruleName}<- is already defined in the grammar: ->${options.grammarName}<-`;\n\n      return errMsg;\n    },\n  };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,0BAA0B;AACpE,SAASC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,WAAW;AAC9C,SAEEC,oBAAoB,EACpBC,WAAW,EACXC,IAAI,EACJC,QAAQ,QACH,kBAAkB;AAWzB,OAAO,MAAMC,0BAA0B,GAAgC;EACrEC,yBAAyBA,CAAC;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAQ,CAAE;IAChE,MAAMC,QAAQ,GAAGf,aAAa,CAACW,QAAQ,CAAC;IACxC,MAAMK,WAAW,GAAGD,QAAQ,GACxB,OAAOd,UAAU,CAACU,QAAQ,CAAC,MAAM,GACjC,qBAAqBA,QAAQ,CAACM,IAAI,MAAM;IAE5C,MAAMC,GAAG,GAAG,aAAaF,WAAW,mBAAmBJ,MAAM,CAACO,KAAK,OAAO;IAE1E,OAAOD,GAAG;EACZ,CAAC;EAEDE,6BAA6BA,CAAC;IAAEC,cAAc;IAAEP;EAAQ,CAAE;IACxD,OAAO,4CAA4C,GAAGO,cAAc,CAACF,KAAK;EAC5E,CAAC;EAEDG,uBAAuBA,CAAC;IACtBC,mBAAmB;IACnBX,MAAM;IACNC,QAAQ;IACRW,qBAAqB;IACrBV;EAAQ,CACT;IACC,MAAMW,SAAS,GAAG,aAAa;IAC/B;IACA,MAAMC,UAAU,GAAGxB,KAAK,CAACU,MAAM,CAAE,CAACO,KAAK;IACvC,MAAMQ,SAAS,GAAG,gBAAgB,GAAGD,UAAU,GAAG,GAAG;IAErD,IAAIF,qBAAqB,EAAE;MACzB,OAAOC,SAAS,GAAGD,qBAAqB,GAAGG,SAAS;KACrD,MAAM;MACL,MAAMC,iBAAiB,GAAGxB,MAAM,CAC9BmB,mBAAmB,EACnB,CAACM,MAAM,EAAEC,YAAY,KAAKD,MAAM,CAACE,MAAM,CAACD,YAAY,CAAC,EACrD,EAAmB,CACpB;MACD,MAAME,uBAAuB,GAAG7B,GAAG,CACjCyB,iBAAiB,EAChBK,QAAQ,IACP,IAAI9B,GAAG,CAAC8B,QAAQ,EAAGC,aAAa,IAAKjC,UAAU,CAACiC,aAAa,CAAC,CAAC,CAACC,IAAI,CAClE,IAAI,CACL,GAAG,CACP;MACD,MAAMC,sBAAsB,GAAGjC,GAAG,CAChC6B,uBAAuB,EACvB,CAACK,OAAO,EAAEC,GAAG,KAAK,KAAKA,GAAG,GAAG,CAAC,KAAKD,OAAO,EAAE,CAC7C;MACD,MAAME,qBAAqB,GAAG,2CAA2CH,sBAAsB,CAACD,IAAI,CAClG,IAAI,CACL,EAAE;MAEH,OAAOV,SAAS,GAAGc,qBAAqB,GAAGZ,SAAS;;EAExD,CAAC;EAEDa,qBAAqBA,CAAC;IACpBC,sBAAsB;IACtB7B,MAAM;IACNY,qBAAqB;IACrBV;EAAQ,CACT;IACC,MAAMW,SAAS,GAAG,aAAa;IAC/B;IACA,MAAMC,UAAU,GAAGxB,KAAK,CAACU,MAAM,CAAE,CAACO,KAAK;IACvC,MAAMQ,SAAS,GAAG,gBAAgB,GAAGD,UAAU,GAAG,GAAG;IAErD,IAAIF,qBAAqB,EAAE;MACzB,OAAOC,SAAS,GAAGD,qBAAqB,GAAGG,SAAS;KACrD,MAAM;MACL,MAAMK,uBAAuB,GAAG7B,GAAG,CACjCsC,sBAAsB,EACrBR,QAAQ,IACP,IAAI9B,GAAG,CAAC8B,QAAQ,EAAGC,aAAa,IAAKjC,UAAU,CAACiC,aAAa,CAAC,CAAC,CAACC,IAAI,CAClE,GAAG,CACJ,GAAG,CACP;MACD,MAAMI,qBAAqB,GACzB,gGAAgG,GAChG,IAAIP,uBAAuB,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG;MAE3C,OAAOV,SAAS,GAAGc,qBAAqB,GAAGZ,SAAS;;EAExD;CACD;AAEDe,MAAM,CAACC,MAAM,CAAClC,0BAA0B,CAAC;AAEzC,OAAO,MAAMmC,mCAAmC,GAC9C;EACEC,sBAAsBA,CACpBC,YAAkB,EAClBC,aAA0B;IAE1B,MAAM7B,GAAG,GACP,+DAA+D,GAC/D6B,aAAa,CAACC,eAAe,GAC7B,MAAM,GACN,2BAA2B,GAC3BF,YAAY,CAAC7B,IAAI,GACjB,IAAI;IACN,OAAOC,GAAG;EACZ;CACD;AAEH,OAAO,MAAM+B,oCAAoC,GAC/C;EACEC,wBAAwBA,CACtBJ,YAAkB,EAClBK,cAA2C;IAE3C,SAASC,0BAA0BA,CACjCC,IAA+B;MAE/B,IAAIA,IAAI,YAAY7C,QAAQ,EAAE;QAC5B,OAAO6C,IAAI,CAACC,YAAY,CAACrC,IAAI;OAC9B,MAAM,IAAIoC,IAAI,YAAY/C,WAAW,EAAE;QACtC,OAAO+C,IAAI,CAACL,eAAe;OAC5B,MAAM;QACL,OAAO,EAAE;;IAEb;IAEA,MAAMO,YAAY,GAAGT,YAAY,CAAC7B,IAAI;IACtC,MAAMuC,aAAa,GAAGtD,KAAK,CAACiD,cAAc,CAAE;IAC5C,MAAMM,KAAK,GAAGD,aAAa,CAAClB,GAAG;IAC/B,MAAMoB,OAAO,GAAGrD,oBAAoB,CAACmD,aAAa,CAAC;IACnD,MAAMG,aAAa,GAAGP,0BAA0B,CAACI,aAAa,CAAC;IAE/D,MAAMI,gBAAgB,GAAGH,KAAK,GAAG,CAAC;IAClC,IAAIvC,GAAG,GAAG,KAAKwC,OAAO,GAAGE,gBAAgB,GAAGH,KAAK,GAAG,EAAE,MACpDE,aAAa,GAAG,oBAAoBA,aAAa,IAAI,GAAG,EAC1D;4CAEcR,cAAc,CAACU,MACjB,oCAAoCN,YAAY;;mBAE/C;IAEb;IACArC,GAAG,GAAGA,GAAG,CAAC4C,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IACjC5C,GAAG,GAAGA,GAAG,CAAC4C,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;IAEjC,OAAO5C,GAAG;EACZ,CAAC;EAED6C,2BAA2BA,CAACC,IAAU;IACpC,MAAMC,MAAM,GACV,wCAAwC,GACxC,2EAA2ED,IAAI,CAAC/C,IAAI,MAAM,GAC1F,6EAA6E,GAC7E,yGAAyG,GACzG,wDAAwD;IAE1D,OAAOgD,MAAM;EACf,CAAC;EAEDC,oCAAoCA,CAACC,OAKpC;IACC,MAAMC,OAAO,GAAGjE,GAAG,CAACgE,OAAO,CAACE,UAAU,EAAGC,OAAO,IAC9CrE,UAAU,CAACqE,OAAO,CAAC,CACpB,CAACnC,IAAI,CAAC,IAAI,CAAC;IACZ,MAAMoC,UAAU,GACdJ,OAAO,CAACK,WAAW,CAAClC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG6B,OAAO,CAACK,WAAW,CAAClC,GAAG;IAC9D,MAAM2B,MAAM,GACV,4BAA4BE,OAAO,CAACM,gBAAgB,CAACtC,IAAI,CACvD,IAAI,CACL,oCAAoC,GACrC,SAASoC,UAAU,aAAaJ,OAAO,CAACrB,YAAY,CAAC7B,IAAI,WAAW,GACpE,IAAImD,OAAO,6DAA6D,GACxE,qFAAqF,GACrF,sBAAsB;IAExB,OAAOH,MAAM;EACf,CAAC;EAEDS,8BAA8BA,CAACP,OAK9B;IACC,MAAMC,OAAO,GAAGjE,GAAG,CAACgE,OAAO,CAACE,UAAU,EAAGM,OAAO,IAC9C1E,UAAU,CAAC0E,OAAO,CAAC,CACpB,CAACxC,IAAI,CAAC,IAAI,CAAC;IACZ,MAAMoC,UAAU,GACdJ,OAAO,CAACK,WAAW,CAAClC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG6B,OAAO,CAACK,WAAW,CAAClC,GAAG;IAC9D,IAAIsC,WAAW,GACb,qCAAqCT,OAAO,CAACM,gBAAgB,CAACtC,IAAI,CAChE,IAAI,CACL,WAAWoC,UAAU,GAAG,GACzB,YAAYJ,OAAO,CAACrB,YAAY,CAAC7B,IAAI,WAAW,GAChD,IAAImD,OAAO,6DAA6D;IAE1EQ,WAAW,GACTA,WAAW,GACX,8FAA8F,GAC9F,sBAAsB;IACxB,OAAOA,WAAW;EACpB,CAAC;EAEDC,yBAAyBA,CAACV,OAGzB;IACC,IAAIT,OAAO,GAAGrD,oBAAoB,CAAC8D,OAAO,CAACW,UAAU,CAAC;IACtD,IAAIX,OAAO,CAACW,UAAU,CAACxC,GAAG,KAAK,CAAC,EAAE;MAChCoB,OAAO,IAAIS,OAAO,CAACW,UAAU,CAACxC,GAAG;;IAGnC,MAAM2B,MAAM,GACV,mBAAmBP,OAAO,kBAAkBS,OAAO,CAACrB,YAAY,CAAC7B,IAAI,mCAAmC,GACxG,sCAAsC;IAExC,OAAOgD,MAAM;EACf,CAAC;EAED;EACA;EACAc,mBAAmBA,CAACZ,OAGnB;IACC;IACA,OAAO,YAAY;EACrB,CAAC;EAEDa,0BAA0BA,CAACb,OAI1B;IACC,MAAMF,MAAM,GACV,iCAAiCE,OAAO,CAACc,cAAc,GAAG,CAAC,GAAG,GAC9D,UAAUd,OAAO,CAACK,WAAW,CAAClC,GAAG,aAAa6B,OAAO,CAACrB,YAAY,CAAC7B,IAAI,WAAW,GAClF,wDAAwD;IAE1D,OAAOgD,MAAM;EACf,CAAC;EAEDiB,6BAA6BA,CAACf,OAG7B;IACC,MAAMF,MAAM,GACV,0DAA0D,GAC1D,MAAME,OAAO,CAACK,WAAW,CAAClC,GAAG,aAC3B6B,OAAO,CAACrB,YAAY,CAAC7B,IACvB,iBACEkD,OAAO,CAACK,WAAW,CAACW,UAAU,CAACtB,MAAM,GAAG,CAC1C,gBAAgB;IAElB,OAAOI,MAAM;EACf,CAAC;EAEDmB,uBAAuBA,CAACjB,OAGvB;IACC,MAAMrD,QAAQ,GAAGqD,OAAO,CAACrB,YAAY,CAAC7B,IAAI;IAC1C,MAAMoE,SAAS,GAAGlF,GAAG,CACnBgE,OAAO,CAACmB,iBAAiB,EACxBC,QAAQ,IAAKA,QAAQ,CAACtE,IAAI,CAC5B;IACD,MAAMuE,iBAAiB,GAAG,GAAG1E,QAAQ,QAAQuE,SAAS,CACnDtD,MAAM,CAAC,CAACjB,QAAQ,CAAC,CAAC,CAClBqB,IAAI,CAAC,OAAO,CAAC,EAAE;IAClB,MAAM8B,MAAM,GACV,oCAAoC,GACpC,UAAUnD,QAAQ,yDAAyD,GAC3E,0EAA0E0E,iBAAiB,IAAI,GAC/F,oEAAoE,GACpE,8DAA8D;IAEhE,OAAOvB,MAAM;EACf,CAAC;EAED;EACA;EACAwB,yBAAyBA,CAACtB,OAGzB;IACC;IACA,OAAO,YAAY;EACrB,CAAC;EAEDuB,2BAA2BA,CAACvB,OAG3B;IACC,IAAIrD,QAAQ;IACZ,IAAIqD,OAAO,CAACrB,YAAY,YAAYvC,IAAI,EAAE;MACxCO,QAAQ,GAAGqD,OAAO,CAACrB,YAAY,CAAC7B,IAAI;KACrC,MAAM;MACLH,QAAQ,GAAGqD,OAAO,CAACrB,YAAY;;IAGjC,MAAMmB,MAAM,GAAG,iCAAiCnD,QAAQ,2CAA2CqD,OAAO,CAACwB,WAAW,IAAI;IAE1H,OAAO1B,MAAM;EACf;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}