{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\nimport { Graph } from '../graph.js';\nexport { prim };\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n  _.each(g.nodes(), function (v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (Object.prototype.hasOwnProperty.call(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error('Input graph is not connected: ' + g);\n    } else {\n      init = true;\n    }\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["_", "PriorityQueue", "Graph", "prim", "g", "weightFunc", "result", "parents", "pq", "v", "updateNeighbors", "edge", "w", "pri", "priority", "undefined", "edgeWeight", "decrease", "nodeCount", "each", "nodes", "add", "Number", "POSITIVE_INFINITY", "setNode", "init", "size", "removeMin", "Object", "prototype", "hasOwnProperty", "call", "setEdge", "Error", "nodeEdges", "for<PERSON>ach"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/graphlib/alg/prim.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\nimport { Graph } from '../graph.js';\n\nexport { prim };\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  _.each(g.nodes(), function (v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (Object.prototype.hasOwnProperty.call(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error('Input graph is not connected: ' + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,IAAI;AAEb,SAASA,IAAIA,CAACC,CAAC,EAAEC,UAAU,EAAE;EAC3B,IAAIC,MAAM,GAAG,IAAIJ,KAAK,CAAC,CAAC;EACxB,IAAIK,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,EAAE,GAAG,IAAIP,aAAa,CAAC,CAAC;EAC5B,IAAIQ,CAAC;EAEL,SAASC,eAAeA,CAACC,IAAI,EAAE;IAC7B,IAAIC,CAAC,GAAGD,IAAI,CAACF,CAAC,KAAKA,CAAC,GAAGE,IAAI,CAACC,CAAC,GAAGD,IAAI,CAACF,CAAC;IACtC,IAAII,GAAG,GAAGL,EAAE,CAACM,QAAQ,CAACF,CAAC,CAAC;IACxB,IAAIC,GAAG,KAAKE,SAAS,EAAE;MACrB,IAAIC,UAAU,GAAGX,UAAU,CAACM,IAAI,CAAC;MACjC,IAAIK,UAAU,GAAGH,GAAG,EAAE;QACpBN,OAAO,CAACK,CAAC,CAAC,GAAGH,CAAC;QACdD,EAAE,CAACS,QAAQ,CAACL,CAAC,EAAEI,UAAU,CAAC;MAC5B;IACF;EACF;EAEA,IAAIZ,CAAC,CAACc,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;IACvB,OAAOZ,MAAM;EACf;EAEAN,CAAC,CAACmB,IAAI,CAACf,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE,UAAUX,CAAC,EAAE;IAC7BD,EAAE,CAACa,GAAG,CAACZ,CAAC,EAAEa,MAAM,CAACC,iBAAiB,CAAC;IACnCjB,MAAM,CAACkB,OAAO,CAACf,CAAC,CAAC;EACnB,CAAC,CAAC;;EAEF;EACAD,EAAE,CAACS,QAAQ,CAACb,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAE5B,IAAIK,IAAI,GAAG,KAAK;EAChB,OAAOjB,EAAE,CAACkB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACpBjB,CAAC,GAAGD,EAAE,CAACmB,SAAS,CAAC,CAAC;IAClB,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACxB,OAAO,EAAEE,CAAC,CAAC,EAAE;MACpDH,MAAM,CAAC0B,OAAO,CAACvB,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIgB,IAAI,EAAE;MACf,MAAM,IAAIQ,KAAK,CAAC,gCAAgC,GAAG7B,CAAC,CAAC;IACvD,CAAC,MAAM;MACLqB,IAAI,GAAG,IAAI;IACb;IAEArB,CAAC,CAAC8B,SAAS,CAACzB,CAAC,CAAC,CAAC0B,OAAO,CAACzB,eAAe,CAAC;EACzC;EAEA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}