{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { DefaultNameRegexp } from '../utils/cst-utils.js';\nimport { isCommentTerminal, terminalRegex } from '../utils/grammar-utils.js';\nimport { isMultilineComment } from '../utils/regexp-utils.js';\nimport { isTerminalRule } from './generated/ast.js';\n/**\n * Create the default grammar configuration (used by `createDefaultModule`). This can be overridden in a\n * language-specific module.\n */\nexport function createGrammarConfig(services) {\n  const rules = [];\n  const grammar = services.Grammar;\n  for (const rule of grammar.rules) {\n    if (isTerminalRule(rule) && isCommentTerminal(rule) && isMultilineComment(terminalRegex(rule))) {\n      rules.push(rule.name);\n    }\n  }\n  return {\n    multilineCommentRules: rules,\n    nameRegexp: DefaultNameRegexp\n  };\n}", "map": {"version": 3, "names": ["DefaultNameRegexp", "isCommentTerminal", "terminalRegex", "isMultilineComment", "isTerminalRule", "createGrammarConfig", "services", "rules", "grammar", "Grammar", "rule", "push", "name", "multilineCommentRules", "nameRegexp"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/languages/grammar-config.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport { DefaultNameRegexp } from '../utils/cst-utils.js';\r\nimport { isCommentTerminal, terminalRegex } from '../utils/grammar-utils.js';\r\nimport { isMultilineComment } from '../utils/regexp-utils.js';\r\nimport { isTerminalRule } from './generated/ast.js';\r\n\r\nexport interface GrammarConfig {\r\n    /**\r\n     * Lists all rule names which are classified as multiline comment rules\r\n     */\r\n    multilineCommentRules: string[]\r\n    /**\r\n     * A regular expression which matches characters of names\r\n     */\r\n    nameRegexp: RegExp\r\n}\r\n\r\n/**\r\n * Create the default grammar configuration (used by `createDefaultModule`). This can be overridden in a\r\n * language-specific module.\r\n */\r\nexport function createGrammarConfig(services: LangiumCoreServices): GrammarConfig {\r\n    const rules: string[] = [];\r\n    const grammar = services.Grammar;\r\n    for (const rule of grammar.rules) {\r\n        if (isTerminalRule(rule) && isCommentTerminal(rule) && isMultilineComment(terminalRegex(rule))) {\r\n            rules.push(rule.name);\r\n        }\r\n    }\r\n    return {\r\n        multilineCommentRules: rules,\r\n        nameRegexp: DefaultNameRegexp\r\n    };\r\n}\r\n"], "mappings": "AAAA;;;;;AAOA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,2BAA2B;AAC5E,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,cAAc,QAAQ,oBAAoB;AAanD;;;;AAIA,OAAM,SAAUC,mBAAmBA,CAACC,QAA6B;EAC7D,MAAMC,KAAK,GAAa,EAAE;EAC1B,MAAMC,OAAO,GAAGF,QAAQ,CAACG,OAAO;EAChC,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACD,KAAK,EAAE;IAC9B,IAAIH,cAAc,CAACM,IAAI,CAAC,IAAIT,iBAAiB,CAACS,IAAI,CAAC,IAAIP,kBAAkB,CAACD,aAAa,CAACQ,IAAI,CAAC,CAAC,EAAE;MAC5FH,KAAK,CAACI,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC;IACzB;EACJ;EACA,OAAO;IACHC,qBAAqB,EAAEN,KAAK;IAC5BO,UAAU,EAAEd;GACf;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}