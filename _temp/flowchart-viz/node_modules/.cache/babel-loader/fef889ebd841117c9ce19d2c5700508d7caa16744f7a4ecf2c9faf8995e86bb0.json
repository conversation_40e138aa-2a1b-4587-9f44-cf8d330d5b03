{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { URI } from 'vscode-uri';\nimport { isAstNode, isReference } from '../syntax-tree.js';\nimport { getDocument } from '../utils/ast-utils.js';\nimport { findNodesForProperty } from '../utils/grammar-utils.js';\nexport function isAstNodeWithComment(node) {\n  return typeof node.$comment === 'string';\n}\nfunction isIntermediateReference(obj) {\n  return typeof obj === 'object' && !!obj && ('$ref' in obj || '$error' in obj);\n}\nexport class DefaultJsonSerializer {\n  constructor(services) {\n    /** The set of AstNode properties to be ignored by the serializer. */\n    this.ignoreProperties = new Set(['$container', '$containerProperty', '$containerIndex', '$document', '$cstNode']);\n    this.langiumDocuments = services.shared.workspace.LangiumDocuments;\n    this.astNodeLocator = services.workspace.AstNodeLocator;\n    this.nameProvider = services.references.NameProvider;\n    this.commentProvider = services.documentation.CommentProvider;\n  }\n  serialize(node, options) {\n    const serializeOptions = options !== null && options !== void 0 ? options : {};\n    const specificReplacer = options === null || options === void 0 ? void 0 : options.replacer;\n    const defaultReplacer = (key, value) => this.replacer(key, value, serializeOptions);\n    const replacer = specificReplacer ? (key, value) => specificReplacer(key, value, defaultReplacer) : defaultReplacer;\n    try {\n      this.currentDocument = getDocument(node);\n      return JSON.stringify(node, replacer, options === null || options === void 0 ? void 0 : options.space);\n    } finally {\n      this.currentDocument = undefined;\n    }\n  }\n  deserialize(content, options) {\n    const deserializeOptions = options !== null && options !== void 0 ? options : {};\n    const root = JSON.parse(content);\n    this.linkNode(root, root, deserializeOptions);\n    return root;\n  }\n  replacer(key, value, {\n    refText,\n    sourceText,\n    textRegions,\n    comments,\n    uriConverter\n  }) {\n    var _a, _b, _c, _d;\n    if (this.ignoreProperties.has(key)) {\n      return undefined;\n    } else if (isReference(value)) {\n      const refValue = value.ref;\n      const $refText = refText ? value.$refText : undefined;\n      if (refValue) {\n        const targetDocument = getDocument(refValue);\n        let targetUri = '';\n        if (this.currentDocument && this.currentDocument !== targetDocument) {\n          if (uriConverter) {\n            targetUri = uriConverter(targetDocument.uri, value);\n          } else {\n            targetUri = targetDocument.uri.toString();\n          }\n        }\n        const targetPath = this.astNodeLocator.getAstNodePath(refValue);\n        return {\n          $ref: `${targetUri}#${targetPath}`,\n          $refText\n        };\n      } else {\n        return {\n          $error: (_b = (_a = value.error) === null || _a === void 0 ? void 0 : _a.message) !== null && _b !== void 0 ? _b : 'Could not resolve reference',\n          $refText\n        };\n      }\n    } else if (isAstNode(value)) {\n      let astNode = undefined;\n      if (textRegions) {\n        astNode = this.addAstNodeRegionWithAssignmentsTo(Object.assign({}, value));\n        if ((!key || value.$document) && (astNode === null || astNode === void 0 ? void 0 : astNode.$textRegion)) {\n          // The document URI is added to the root node of the resulting JSON tree\n          astNode.$textRegion.documentURI = (_c = this.currentDocument) === null || _c === void 0 ? void 0 : _c.uri.toString();\n        }\n      }\n      if (sourceText && !key) {\n        astNode !== null && astNode !== void 0 ? astNode : astNode = Object.assign({}, value);\n        astNode.$sourceText = (_d = value.$cstNode) === null || _d === void 0 ? void 0 : _d.text;\n      }\n      if (comments) {\n        astNode !== null && astNode !== void 0 ? astNode : astNode = Object.assign({}, value);\n        const comment = this.commentProvider.getComment(value);\n        if (comment) {\n          astNode.$comment = comment.replace(/\\r/g, '');\n        }\n      }\n      return astNode !== null && astNode !== void 0 ? astNode : value;\n    } else {\n      return value;\n    }\n  }\n  addAstNodeRegionWithAssignmentsTo(node) {\n    const createDocumentSegment = cstNode => ({\n      offset: cstNode.offset,\n      end: cstNode.end,\n      length: cstNode.length,\n      range: cstNode.range\n    });\n    if (node.$cstNode) {\n      const textRegion = node.$textRegion = createDocumentSegment(node.$cstNode);\n      const assignments = textRegion.assignments = {};\n      Object.keys(node).filter(key => !key.startsWith('$')).forEach(key => {\n        const propertyAssignments = findNodesForProperty(node.$cstNode, key).map(createDocumentSegment);\n        if (propertyAssignments.length !== 0) {\n          assignments[key] = propertyAssignments;\n        }\n      });\n      return node;\n    }\n    return undefined;\n  }\n  linkNode(node, root, options, container, containerProperty, containerIndex) {\n    for (const [propertyName, item] of Object.entries(node)) {\n      if (Array.isArray(item)) {\n        for (let index = 0; index < item.length; index++) {\n          const element = item[index];\n          if (isIntermediateReference(element)) {\n            item[index] = this.reviveReference(node, propertyName, root, element, options);\n          } else if (isAstNode(element)) {\n            this.linkNode(element, root, options, node, propertyName, index);\n          }\n        }\n      } else if (isIntermediateReference(item)) {\n        node[propertyName] = this.reviveReference(node, propertyName, root, item, options);\n      } else if (isAstNode(item)) {\n        this.linkNode(item, root, options, node, propertyName);\n      }\n    }\n    const mutable = node;\n    mutable.$container = container;\n    mutable.$containerProperty = containerProperty;\n    mutable.$containerIndex = containerIndex;\n  }\n  reviveReference(container, property, root, reference, options) {\n    let refText = reference.$refText;\n    let error = reference.$error;\n    if (reference.$ref) {\n      const ref = this.getRefNode(root, reference.$ref, options.uriConverter);\n      if (isAstNode(ref)) {\n        if (!refText) {\n          refText = this.nameProvider.getName(ref);\n        }\n        return {\n          $refText: refText !== null && refText !== void 0 ? refText : '',\n          ref\n        };\n      } else {\n        error = ref;\n      }\n    }\n    if (error) {\n      const ref = {\n        $refText: refText !== null && refText !== void 0 ? refText : ''\n      };\n      ref.error = {\n        container,\n        property,\n        message: error,\n        reference: ref\n      };\n      return ref;\n    } else {\n      return undefined;\n    }\n  }\n  getRefNode(root, uri, uriConverter) {\n    try {\n      const fragmentIndex = uri.indexOf('#');\n      if (fragmentIndex === 0) {\n        const node = this.astNodeLocator.getAstNode(root, uri.substring(1));\n        if (!node) {\n          return 'Could not resolve path: ' + uri;\n        }\n        return node;\n      }\n      if (fragmentIndex < 0) {\n        const documentUri = uriConverter ? uriConverter(uri) : URI.parse(uri);\n        const document = this.langiumDocuments.getDocument(documentUri);\n        if (!document) {\n          return 'Could not find document for URI: ' + uri;\n        }\n        return document.parseResult.value;\n      }\n      const documentUri = uriConverter ? uriConverter(uri.substring(0, fragmentIndex)) : URI.parse(uri.substring(0, fragmentIndex));\n      const document = this.langiumDocuments.getDocument(documentUri);\n      if (!document) {\n        return 'Could not find document for URI: ' + uri;\n      }\n      if (fragmentIndex === uri.length - 1) {\n        return document.parseResult.value;\n      }\n      const node = this.astNodeLocator.getAstNode(document.parseResult.value, uri.substring(fragmentIndex + 1));\n      if (!node) {\n        return 'Could not resolve URI: ' + uri;\n      }\n      return node;\n    } catch (err) {\n      return String(err);\n    }\n  }\n}", "map": {"version": 3, "names": ["URI", "isAstNode", "isReference", "getDocument", "findNodesForProperty", "isAstNodeWithComment", "node", "$comment", "isIntermediateReference", "obj", "DefaultJsonSerializer", "constructor", "services", "ignoreProperties", "Set", "langiumDocuments", "shared", "workspace", "LangiumDocuments", "astNodeLocator", "AstNodeLocator", "nameProvider", "references", "Name<PERSON>rovider", "comment<PERSON><PERSON><PERSON>", "documentation", "CommentProvider", "serialize", "options", "serializeOptions", "specificReplacer", "replacer", "defaultReplacer", "key", "value", "currentDocument", "JSON", "stringify", "space", "undefined", "deserialize", "content", "deserializeOptions", "root", "parse", "linkNode", "refText", "sourceText", "textRegions", "comments", "uriConverter", "has", "refValue", "ref", "$refText", "targetDocument", "targetUri", "uri", "toString", "targetPath", "getAstNodePath", "$ref", "$error", "_b", "_a", "error", "message", "astNode", "addAstNodeRegionWithAssignmentsTo", "Object", "assign", "$document", "$textRegion", "documentURI", "_c", "$sourceText", "_d", "$cstNode", "text", "comment", "getComment", "replace", "createDocumentSegment", "cstNode", "offset", "end", "length", "range", "textRegion", "assignments", "keys", "filter", "startsWith", "for<PERSON>ach", "propertyAssignments", "map", "container", "containerProperty", "containerIndex", "propertyName", "item", "entries", "Array", "isArray", "index", "element", "reviveReference", "mutable", "$container", "$containerProperty", "$containerIndex", "property", "reference", "getRefNode", "getName", "fragmentIndex", "indexOf", "getAstNode", "substring", "documentUri", "document", "parseResult", "err", "String"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/serializer/json-serializer.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { URI } from 'vscode-uri';\r\nimport type { CommentProvider } from '../documentation/comment-provider.js';\r\nimport type { NameProvider } from '../references/name-provider.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, CstNode, GenericAstNode, Mutable, Reference } from '../syntax-tree.js';\r\nimport { isAstNode, isReference } from '../syntax-tree.js';\r\nimport { getDocument } from '../utils/ast-utils.js';\r\nimport { findNodesForProperty } from '../utils/grammar-utils.js';\r\nimport type { AstNodeLocator } from '../workspace/ast-node-locator.js';\r\nimport type { DocumentSegment, LangiumDocument, LangiumDocuments } from '../workspace/documents.js';\r\n\r\nexport interface JsonSerializeOptions {\r\n    /** The space parameter for `JSON.stringify`, controlling whether and how to pretty-print the output. */\r\n    space?: string | number;\r\n    /** Whether to include the `$refText` property for references (the name used to identify the target node). */\r\n    refText?: boolean;\r\n    /** Whether to include the `$sourceText` property, which holds the full source text from which an AST node was parsed. */\r\n    sourceText?: boolean;\r\n    /** Whether to include the `$textRegion` property, which holds information to trace AST node properties to their respective source text regions. */\r\n    textRegions?: boolean;\r\n    /** Whether to include the `$comment` property, which holds comments according to the CommentProvider service. */\r\n    comments?: boolean;\r\n    /** The replacer parameter for `JSON.stringify`; the default replacer given as parameter should be used to apply basic replacements. */\r\n    replacer?: (key: string, value: unknown, defaultReplacer: (key: string, value: unknown) => unknown) => unknown\r\n    /** Used to convert and serialize URIs when the target of a cross-reference is in a different document. */\r\n    uriConverter?: (uri: URI, reference: Reference) => string\r\n}\r\n\r\nexport interface JsonDeserializeOptions {\r\n    /** Used to parse and convert URIs when the target of a cross-reference is in a different document. */\r\n    uriConverter?: (uri: string) => URI\r\n}\r\n\r\n/**\r\n * {@link AstNode}s that may carry information on their definition area within the DSL text.\r\n */\r\nexport interface AstNodeWithTextRegion extends AstNode {\r\n    $sourceText?: string;\r\n    $textRegion?: AstNodeRegionWithAssignments;\r\n}\r\n\r\n/**\r\n * {@link AstNode}s that may carry a semantically relevant comment.\r\n */\r\nexport interface AstNodeWithComment extends AstNode {\r\n    $comment?: string;\r\n}\r\n\r\nexport function isAstNodeWithComment(node: AstNode): node is AstNodeWithComment {\r\n    return typeof (node as AstNodeWithComment).$comment === 'string';\r\n}\r\n\r\n/**\r\n * A {@link DocumentSegment} representing the definition area of an AstNode within the DSL text.\r\n * Usually contains text region information on all assigned property values of the AstNode,\r\n * and may contain the defining file's URI as string.\r\n */\r\nexport interface AstNodeRegionWithAssignments extends DocumentSegment {\r\n    /**\r\n     * A record containing an entry for each assigned property of the AstNode.\r\n     * The key is equal to the property name and the value is an array of the property values'\r\n     * text regions, regardless of whether the property is a single value or list property.\r\n     */\r\n    assignments?: Record<string, DocumentSegment[]>;\r\n    /**\r\n     * The AstNode defining file's URI as string\r\n     */\r\n    documentURI?: string;\r\n}\r\n\r\n/**\r\n * Utility service for transforming an `AstNode` into a JSON string and vice versa.\r\n */\r\nexport interface JsonSerializer {\r\n    /**\r\n     * Serialize an `AstNode` into a JSON `string`.\r\n     * @param node The `AstNode` to be serialized.\r\n     * @param options Serialization options\r\n     */\r\n    serialize(node: AstNode, options?: JsonSerializeOptions): string;\r\n    /**\r\n     * Deserialize (parse) a JSON `string` into an `AstNode`.\r\n     */\r\n    deserialize<T extends AstNode = AstNode>(content: string, options?: JsonDeserializeOptions): T;\r\n}\r\n\r\n/**\r\n * A cross-reference in the serialized JSON representation of an AstNode.\r\n */\r\ninterface IntermediateReference {\r\n    /** URI pointing to the target element. This is either `#${path}` if the target is in the same document, or `${documentURI}#${path}` otherwise. */\r\n    $ref?: string\r\n    /** The actual text used to look up the reference target in the surrounding scope. */\r\n    $refText?: string\r\n    /** If any problem occurred while resolving the reference, it is described by this property. */\r\n    $error?: string\r\n}\r\n\r\nfunction isIntermediateReference(obj: unknown): obj is IntermediateReference {\r\n    return typeof obj === 'object' && !!obj && ('$ref' in obj || '$error' in obj);\r\n}\r\n\r\nexport class DefaultJsonSerializer implements JsonSerializer {\r\n\r\n    /** The set of AstNode properties to be ignored by the serializer. */\r\n    ignoreProperties = new Set(['$container', '$containerProperty', '$containerIndex', '$document', '$cstNode']);\r\n\r\n    /** The document that is currently processed by the serializer; this is used by the replacer function.  */\r\n    protected currentDocument: LangiumDocument | undefined;\r\n\r\n    protected readonly langiumDocuments: LangiumDocuments;\r\n    protected readonly astNodeLocator: AstNodeLocator;\r\n    protected readonly nameProvider: NameProvider;\r\n    protected readonly commentProvider: CommentProvider;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.langiumDocuments = services.shared.workspace.LangiumDocuments;\r\n        this.astNodeLocator = services.workspace.AstNodeLocator;\r\n        this.nameProvider = services.references.NameProvider;\r\n        this.commentProvider = services.documentation.CommentProvider;\r\n    }\r\n\r\n    serialize(node: AstNode, options?: JsonSerializeOptions): string {\r\n        const serializeOptions = options ?? {};\r\n        const specificReplacer = options?.replacer;\r\n        const defaultReplacer = (key: string, value: unknown) => this.replacer(key, value, serializeOptions);\r\n        const replacer = specificReplacer ? (key: string, value: unknown) => specificReplacer(key, value, defaultReplacer) : defaultReplacer;\r\n\r\n        try {\r\n            this.currentDocument = getDocument(node);\r\n            return JSON.stringify(node, replacer, options?.space);\r\n        } finally {\r\n            this.currentDocument = undefined;\r\n        }\r\n    }\r\n\r\n    deserialize<T extends AstNode = AstNode>(content: string, options?: JsonDeserializeOptions): T {\r\n        const deserializeOptions = options ?? {};\r\n        const root = JSON.parse(content);\r\n        this.linkNode(root, root, deserializeOptions);\r\n        return root;\r\n    }\r\n\r\n    protected replacer(key: string, value: unknown, { refText, sourceText, textRegions, comments, uriConverter }: JsonSerializeOptions): unknown {\r\n        if (this.ignoreProperties.has(key)) {\r\n            return undefined;\r\n        } else if (isReference(value)) {\r\n            const refValue = value.ref;\r\n            const $refText = refText ? value.$refText : undefined;\r\n            if (refValue) {\r\n                const targetDocument = getDocument(refValue);\r\n                let targetUri = '';\r\n                if (this.currentDocument && this.currentDocument !== targetDocument) {\r\n                    if (uriConverter) {\r\n                        targetUri = uriConverter(targetDocument.uri, value);\r\n                    } else {\r\n                        targetUri = targetDocument.uri.toString();\r\n                    }\r\n                }\r\n                const targetPath = this.astNodeLocator.getAstNodePath(refValue);\r\n                return {\r\n                    $ref: `${targetUri}#${targetPath}`,\r\n                    $refText\r\n                } satisfies IntermediateReference;\r\n            } else {\r\n                return {\r\n                    $error: value.error?.message ?? 'Could not resolve reference',\r\n                    $refText\r\n                } satisfies IntermediateReference;\r\n            }\r\n        } else if (isAstNode(value)) {\r\n            let astNode: AstNodeWithTextRegion | undefined = undefined;\r\n            if (textRegions) {\r\n                astNode = this.addAstNodeRegionWithAssignmentsTo({ ...value });\r\n                if ((!key || value.$document) && astNode?.$textRegion) {\r\n                    // The document URI is added to the root node of the resulting JSON tree\r\n                    astNode.$textRegion.documentURI = this.currentDocument?.uri.toString();\r\n                }\r\n            }\r\n            if (sourceText && !key) {\r\n                astNode ??= { ...value };\r\n                astNode.$sourceText = value.$cstNode?.text;\r\n            }\r\n            if (comments) {\r\n                astNode ??= { ...value };\r\n                const comment = this.commentProvider.getComment(value);\r\n                if (comment) {\r\n                    (astNode as AstNodeWithComment).$comment = comment.replace(/\\r/g, '');\r\n                }\r\n            }\r\n            return astNode ?? value;\r\n        } else {\r\n            return value;\r\n        }\r\n    }\r\n\r\n    protected addAstNodeRegionWithAssignmentsTo(node: AstNodeWithTextRegion) {\r\n        const createDocumentSegment: (cstNode: CstNode) => AstNodeRegionWithAssignments = cstNode => <DocumentSegment>{\r\n            offset: cstNode.offset,\r\n            end: cstNode.end,\r\n            length: cstNode.length,\r\n            range: cstNode.range,\r\n        };\r\n\r\n        if (node.$cstNode) {\r\n            const textRegion = node.$textRegion = createDocumentSegment(node.$cstNode);\r\n            const assignments: Record<string, DocumentSegment[]> = textRegion.assignments = {};\r\n\r\n            Object.keys(node).filter(key => !key.startsWith('$')).forEach(key => {\r\n                const propertyAssignments = findNodesForProperty(node.$cstNode, key).map(createDocumentSegment);\r\n                if (propertyAssignments.length !== 0) {\r\n                    assignments[key] = propertyAssignments;\r\n                }\r\n            });\r\n\r\n            return node;\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    protected linkNode(node: GenericAstNode, root: AstNode, options: JsonDeserializeOptions, container?: AstNode, containerProperty?: string, containerIndex?: number) {\r\n        for (const [propertyName, item] of Object.entries(node)) {\r\n            if (Array.isArray(item)) {\r\n                for (let index = 0; index < item.length; index++) {\r\n                    const element = item[index];\r\n                    if (isIntermediateReference(element)) {\r\n                        item[index] = this.reviveReference(node, propertyName, root, element, options);\r\n                    } else if (isAstNode(element)) {\r\n                        this.linkNode(element as GenericAstNode, root, options, node, propertyName, index);\r\n                    }\r\n                }\r\n            } else if (isIntermediateReference(item)) {\r\n                node[propertyName] = this.reviveReference(node, propertyName, root, item, options);\r\n            } else if (isAstNode(item)) {\r\n                this.linkNode(item as GenericAstNode, root, options, node, propertyName);\r\n            }\r\n        }\r\n        const mutable = node as Mutable<AstNode>;\r\n        mutable.$container = container;\r\n        mutable.$containerProperty = containerProperty;\r\n        mutable.$containerIndex = containerIndex;\r\n    }\r\n\r\n    protected reviveReference(container: AstNode, property: string, root: AstNode, reference: IntermediateReference, options: JsonDeserializeOptions): Reference | undefined {\r\n        let refText = reference.$refText;\r\n        let error = reference.$error;\r\n        if (reference.$ref) {\r\n            const ref = this.getRefNode(root, reference.$ref, options.uriConverter);\r\n            if (isAstNode(ref)) {\r\n                if (!refText) {\r\n                    refText = this.nameProvider.getName(ref);\r\n                }\r\n                return {\r\n                    $refText: refText ?? '',\r\n                    ref\r\n                };\r\n            } else {\r\n                error = ref;\r\n            }\r\n        }\r\n        if (error) {\r\n            const ref: Mutable<Reference> = {\r\n                $refText: refText ?? ''\r\n            };\r\n            ref.error = {\r\n                container,\r\n                property,\r\n                message: error,\r\n                reference: ref\r\n            };\r\n            return ref;\r\n        } else {\r\n            return undefined;\r\n        }\r\n    }\r\n\r\n    protected getRefNode(root: AstNode, uri: string, uriConverter?: (uri: string) => URI): AstNode | string {\r\n        try {\r\n            const fragmentIndex = uri.indexOf('#');\r\n            if (fragmentIndex === 0) {\r\n                const node = this.astNodeLocator.getAstNode(root, uri.substring(1));\r\n                if (!node) {\r\n                    return 'Could not resolve path: ' + uri;\r\n                }\r\n                return node;\r\n            }\r\n            if (fragmentIndex < 0) {\r\n                const documentUri = uriConverter ? uriConverter(uri) : URI.parse(uri);\r\n                const document = this.langiumDocuments.getDocument(documentUri);\r\n                if (!document) {\r\n                    return 'Could not find document for URI: ' + uri;\r\n                }\r\n                return document.parseResult.value;\r\n            }\r\n            const documentUri = uriConverter ? uriConverter(uri.substring(0, fragmentIndex)) : URI.parse(uri.substring(0, fragmentIndex));\r\n            const document = this.langiumDocuments.getDocument(documentUri);\r\n            if (!document) {\r\n                return 'Could not find document for URI: ' + uri;\r\n            }\r\n            if (fragmentIndex === uri.length - 1) {\r\n                return document.parseResult.value;\r\n            }\r\n            const node = this.astNodeLocator.getAstNode(document.parseResult.value, uri.substring(fragmentIndex + 1));\r\n            if (!node) {\r\n                return 'Could not resolve URI: ' + uri;\r\n            }\r\n            return node;\r\n        } catch (err) {\r\n            return String(err);\r\n        }\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,GAAG,QAAQ,YAAY;AAKhC,SAASC,SAAS,EAAEC,WAAW,QAAQ,mBAAmB;AAC1D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,oBAAoB,QAAQ,2BAA2B;AAyChE,OAAM,SAAUC,oBAAoBA,CAACC,IAAa;EAC9C,OAAO,OAAQA,IAA2B,CAACC,QAAQ,KAAK,QAAQ;AACpE;AAgDA,SAASC,uBAAuBA,CAACC,GAAY;EACzC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAAC,CAACA,GAAG,KAAK,MAAM,IAAIA,GAAG,IAAI,QAAQ,IAAIA,GAAG,CAAC;AACjF;AAEA,OAAM,MAAOC,qBAAqB;EAa9BC,YAAYC,QAA6B;IAXzC;IACA,KAAAC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAWxG,IAAI,CAACC,gBAAgB,GAAGH,QAAQ,CAACI,MAAM,CAACC,SAAS,CAACC,gBAAgB;IAClE,IAAI,CAACC,cAAc,GAAGP,QAAQ,CAACK,SAAS,CAACG,cAAc;IACvD,IAAI,CAACC,YAAY,GAAGT,QAAQ,CAACU,UAAU,CAACC,YAAY;IACpD,IAAI,CAACC,eAAe,GAAGZ,QAAQ,CAACa,aAAa,CAACC,eAAe;EACjE;EAEAC,SAASA,CAACrB,IAAa,EAAEsB,OAA8B;IACnD,MAAMC,gBAAgB,GAAGD,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,EAAE;IACtC,MAAME,gBAAgB,GAAGF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,QAAQ;IAC1C,MAAMC,eAAe,GAAGA,CAACC,GAAW,EAAEC,KAAc,KAAK,IAAI,CAACH,QAAQ,CAACE,GAAG,EAAEC,KAAK,EAAEL,gBAAgB,CAAC;IACpG,MAAME,QAAQ,GAAGD,gBAAgB,GAAG,CAACG,GAAW,EAAEC,KAAc,KAAKJ,gBAAgB,CAACG,GAAG,EAAEC,KAAK,EAAEF,eAAe,CAAC,GAAGA,eAAe;IAEpI,IAAI;MACA,IAAI,CAACG,eAAe,GAAGhC,WAAW,CAACG,IAAI,CAAC;MACxC,OAAO8B,IAAI,CAACC,SAAS,CAAC/B,IAAI,EAAEyB,QAAQ,EAAEH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,CAAC;IACzD,CAAC,SAAS;MACN,IAAI,CAACH,eAAe,GAAGI,SAAS;IACpC;EACJ;EAEAC,WAAWA,CAA8BC,OAAe,EAAEb,OAAgC;IACtF,MAAMc,kBAAkB,GAAGd,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,EAAE;IACxC,MAAMe,IAAI,GAAGP,IAAI,CAACQ,KAAK,CAACH,OAAO,CAAC;IAChC,IAAI,CAACI,QAAQ,CAACF,IAAI,EAAEA,IAAI,EAAED,kBAAkB,CAAC;IAC7C,OAAOC,IAAI;EACf;EAEUZ,QAAQA,CAACE,GAAW,EAAEC,KAAc,EAAE;IAAEY,OAAO;IAAEC,UAAU;IAAEC,WAAW;IAAEC,QAAQ;IAAEC;EAAY,CAAwB;;IAC9H,IAAI,IAAI,CAACrC,gBAAgB,CAACsC,GAAG,CAAClB,GAAG,CAAC,EAAE;MAChC,OAAOM,SAAS;IACpB,CAAC,MAAM,IAAIrC,WAAW,CAACgC,KAAK,CAAC,EAAE;MAC3B,MAAMkB,QAAQ,GAAGlB,KAAK,CAACmB,GAAG;MAC1B,MAAMC,QAAQ,GAAGR,OAAO,GAAGZ,KAAK,CAACoB,QAAQ,GAAGf,SAAS;MACrD,IAAIa,QAAQ,EAAE;QACV,MAAMG,cAAc,GAAGpD,WAAW,CAACiD,QAAQ,CAAC;QAC5C,IAAII,SAAS,GAAG,EAAE;QAClB,IAAI,IAAI,CAACrB,eAAe,IAAI,IAAI,CAACA,eAAe,KAAKoB,cAAc,EAAE;UACjE,IAAIL,YAAY,EAAE;YACdM,SAAS,GAAGN,YAAY,CAACK,cAAc,CAACE,GAAG,EAAEvB,KAAK,CAAC;UACvD,CAAC,MAAM;YACHsB,SAAS,GAAGD,cAAc,CAACE,GAAG,CAACC,QAAQ,EAAE;UAC7C;QACJ;QACA,MAAMC,UAAU,GAAG,IAAI,CAACxC,cAAc,CAACyC,cAAc,CAACR,QAAQ,CAAC;QAC/D,OAAO;UACHS,IAAI,EAAE,GAAGL,SAAS,IAAIG,UAAU,EAAE;UAClCL;SAC6B;MACrC,CAAC,MAAM;QACH,OAAO;UACHQ,MAAM,EAAE,CAAAC,EAAA,IAAAC,EAAA,GAAA9B,KAAK,CAAC+B,KAAK,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,OAAO,cAAAH,EAAA,cAAAA,EAAA,GAAI,6BAA6B;UAC7DT;SAC6B;MACrC;IACJ,CAAC,MAAM,IAAIrD,SAAS,CAACiC,KAAK,CAAC,EAAE;MACzB,IAAIiC,OAAO,GAAsC5B,SAAS;MAC1D,IAAIS,WAAW,EAAE;QACbmB,OAAO,GAAG,IAAI,CAACC,iCAAiC,CAAAC,MAAA,CAAAC,MAAA,KAAMpC,KAAK,EAAG;QAC9D,IAAI,CAAC,CAACD,GAAG,IAAIC,KAAK,CAACqC,SAAS,MAAKJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,WAAW,GAAE;UACnD;UACAL,OAAO,CAACK,WAAW,CAACC,WAAW,GAAG,CAAAC,EAAA,OAAI,CAACvC,eAAe,cAAAuC,EAAA,uBAAAA,EAAA,CAAEjB,GAAG,CAACC,QAAQ,EAAE;QAC1E;MACJ;MACA,IAAIX,UAAU,IAAI,CAACd,GAAG,EAAE;QACpBkC,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAPA,OAAO,GAAAE,MAAA,CAAAC,MAAA,KAAUpC,KAAK;QACtBiC,OAAO,CAACQ,WAAW,GAAG,CAAAC,EAAA,GAAA1C,KAAK,CAAC2C,QAAQ,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI;MAC9C;MACA,IAAI7B,QAAQ,EAAE;QACVkB,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAPA,OAAO,GAAAE,MAAA,CAAAC,MAAA,KAAUpC,KAAK;QACtB,MAAM6C,OAAO,GAAG,IAAI,CAACvD,eAAe,CAACwD,UAAU,CAAC9C,KAAK,CAAC;QACtD,IAAI6C,OAAO,EAAE;UACRZ,OAA8B,CAAC5D,QAAQ,GAAGwE,OAAO,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QACzE;MACJ;MACA,OAAOd,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAIjC,KAAK;IAC3B,CAAC,MAAM;MACH,OAAOA,KAAK;IAChB;EACJ;EAEUkC,iCAAiCA,CAAC9D,IAA2B;IACnE,MAAM4E,qBAAqB,GAAuDC,OAAO,KAAqB;MAC1GC,MAAM,EAAED,OAAO,CAACC,MAAM;MACtBC,GAAG,EAAEF,OAAO,CAACE,GAAG;MAChBC,MAAM,EAAEH,OAAO,CAACG,MAAM;MACtBC,KAAK,EAAEJ,OAAO,CAACI;KAClB;IAED,IAAIjF,IAAI,CAACuE,QAAQ,EAAE;MACf,MAAMW,UAAU,GAAGlF,IAAI,CAACkE,WAAW,GAAGU,qBAAqB,CAAC5E,IAAI,CAACuE,QAAQ,CAAC;MAC1E,MAAMY,WAAW,GAAsCD,UAAU,CAACC,WAAW,GAAG,EAAE;MAElFpB,MAAM,CAACqB,IAAI,CAACpF,IAAI,CAAC,CAACqF,MAAM,CAAC1D,GAAG,IAAI,CAACA,GAAG,CAAC2D,UAAU,CAAC,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC5D,GAAG,IAAG;QAChE,MAAM6D,mBAAmB,GAAG1F,oBAAoB,CAACE,IAAI,CAACuE,QAAQ,EAAE5C,GAAG,CAAC,CAAC8D,GAAG,CAACb,qBAAqB,CAAC;QAC/F,IAAIY,mBAAmB,CAACR,MAAM,KAAK,CAAC,EAAE;UAClCG,WAAW,CAACxD,GAAG,CAAC,GAAG6D,mBAAmB;QAC1C;MACJ,CAAC,CAAC;MAEF,OAAOxF,IAAI;IACf;IACA,OAAOiC,SAAS;EACpB;EAEUM,QAAQA,CAACvC,IAAoB,EAAEqC,IAAa,EAAEf,OAA+B,EAAEoE,SAAmB,EAAEC,iBAA0B,EAAEC,cAAuB;IAC7J,KAAK,MAAM,CAACC,YAAY,EAAEC,IAAI,CAAC,IAAI/B,MAAM,CAACgC,OAAO,CAAC/F,IAAI,CAAC,EAAE;MACrD,IAAIgG,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;QACrB,KAAK,IAAII,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGJ,IAAI,CAACd,MAAM,EAAEkB,KAAK,EAAE,EAAE;UAC9C,MAAMC,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAC;UAC3B,IAAIhG,uBAAuB,CAACiG,OAAO,CAAC,EAAE;YAClCL,IAAI,CAACI,KAAK,CAAC,GAAG,IAAI,CAACE,eAAe,CAACpG,IAAI,EAAE6F,YAAY,EAAExD,IAAI,EAAE8D,OAAO,EAAE7E,OAAO,CAAC;UAClF,CAAC,MAAM,IAAI3B,SAAS,CAACwG,OAAO,CAAC,EAAE;YAC3B,IAAI,CAAC5D,QAAQ,CAAC4D,OAAyB,EAAE9D,IAAI,EAAEf,OAAO,EAAEtB,IAAI,EAAE6F,YAAY,EAAEK,KAAK,CAAC;UACtF;QACJ;MACJ,CAAC,MAAM,IAAIhG,uBAAuB,CAAC4F,IAAI,CAAC,EAAE;QACtC9F,IAAI,CAAC6F,YAAY,CAAC,GAAG,IAAI,CAACO,eAAe,CAACpG,IAAI,EAAE6F,YAAY,EAAExD,IAAI,EAAEyD,IAAI,EAAExE,OAAO,CAAC;MACtF,CAAC,MAAM,IAAI3B,SAAS,CAACmG,IAAI,CAAC,EAAE;QACxB,IAAI,CAACvD,QAAQ,CAACuD,IAAsB,EAAEzD,IAAI,EAAEf,OAAO,EAAEtB,IAAI,EAAE6F,YAAY,CAAC;MAC5E;IACJ;IACA,MAAMQ,OAAO,GAAGrG,IAAwB;IACxCqG,OAAO,CAACC,UAAU,GAAGZ,SAAS;IAC9BW,OAAO,CAACE,kBAAkB,GAAGZ,iBAAiB;IAC9CU,OAAO,CAACG,eAAe,GAAGZ,cAAc;EAC5C;EAEUQ,eAAeA,CAACV,SAAkB,EAAEe,QAAgB,EAAEpE,IAAa,EAAEqE,SAAgC,EAAEpF,OAA+B;IAC5I,IAAIkB,OAAO,GAAGkE,SAAS,CAAC1D,QAAQ;IAChC,IAAIW,KAAK,GAAG+C,SAAS,CAAClD,MAAM;IAC5B,IAAIkD,SAAS,CAACnD,IAAI,EAAE;MAChB,MAAMR,GAAG,GAAG,IAAI,CAAC4D,UAAU,CAACtE,IAAI,EAAEqE,SAAS,CAACnD,IAAI,EAAEjC,OAAO,CAACsB,YAAY,CAAC;MACvE,IAAIjD,SAAS,CAACoD,GAAG,CAAC,EAAE;QAChB,IAAI,CAACP,OAAO,EAAE;UACVA,OAAO,GAAG,IAAI,CAACzB,YAAY,CAAC6F,OAAO,CAAC7D,GAAG,CAAC;QAC5C;QACA,OAAO;UACHC,QAAQ,EAAER,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,EAAE;UACvBO;SACH;MACL,CAAC,MAAM;QACHY,KAAK,GAAGZ,GAAG;MACf;IACJ;IACA,IAAIY,KAAK,EAAE;MACP,MAAMZ,GAAG,GAAuB;QAC5BC,QAAQ,EAAER,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI;OACxB;MACDO,GAAG,CAACY,KAAK,GAAG;QACR+B,SAAS;QACTe,QAAQ;QACR7C,OAAO,EAAED,KAAK;QACd+C,SAAS,EAAE3D;OACd;MACD,OAAOA,GAAG;IACd,CAAC,MAAM;MACH,OAAOd,SAAS;IACpB;EACJ;EAEU0E,UAAUA,CAACtE,IAAa,EAAEc,GAAW,EAAEP,YAAmC;IAChF,IAAI;MACA,MAAMiE,aAAa,GAAG1D,GAAG,CAAC2D,OAAO,CAAC,GAAG,CAAC;MACtC,IAAID,aAAa,KAAK,CAAC,EAAE;QACrB,MAAM7G,IAAI,GAAG,IAAI,CAACa,cAAc,CAACkG,UAAU,CAAC1E,IAAI,EAAEc,GAAG,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAAC;QACnE,IAAI,CAAChH,IAAI,EAAE;UACP,OAAO,0BAA0B,GAAGmD,GAAG;QAC3C;QACA,OAAOnD,IAAI;MACf;MACA,IAAI6G,aAAa,GAAG,CAAC,EAAE;QACnB,MAAMI,WAAW,GAAGrE,YAAY,GAAGA,YAAY,CAACO,GAAG,CAAC,GAAGzD,GAAG,CAAC4C,KAAK,CAACa,GAAG,CAAC;QACrE,MAAM+D,QAAQ,GAAG,IAAI,CAACzG,gBAAgB,CAACZ,WAAW,CAACoH,WAAW,CAAC;QAC/D,IAAI,CAACC,QAAQ,EAAE;UACX,OAAO,mCAAmC,GAAG/D,GAAG;QACpD;QACA,OAAO+D,QAAQ,CAACC,WAAW,CAACvF,KAAK;MACrC;MACA,MAAMqF,WAAW,GAAGrE,YAAY,GAAGA,YAAY,CAACO,GAAG,CAAC6D,SAAS,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAC,GAAGnH,GAAG,CAAC4C,KAAK,CAACa,GAAG,CAAC6D,SAAS,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAC;MAC7H,MAAMK,QAAQ,GAAG,IAAI,CAACzG,gBAAgB,CAACZ,WAAW,CAACoH,WAAW,CAAC;MAC/D,IAAI,CAACC,QAAQ,EAAE;QACX,OAAO,mCAAmC,GAAG/D,GAAG;MACpD;MACA,IAAI0D,aAAa,KAAK1D,GAAG,CAAC6B,MAAM,GAAG,CAAC,EAAE;QAClC,OAAOkC,QAAQ,CAACC,WAAW,CAACvF,KAAK;MACrC;MACA,MAAM5B,IAAI,GAAG,IAAI,CAACa,cAAc,CAACkG,UAAU,CAACG,QAAQ,CAACC,WAAW,CAACvF,KAAK,EAAEuB,GAAG,CAAC6D,SAAS,CAACH,aAAa,GAAG,CAAC,CAAC,CAAC;MACzG,IAAI,CAAC7G,IAAI,EAAE;QACP,OAAO,yBAAyB,GAAGmD,GAAG;MAC1C;MACA,OAAOnD,IAAI;IACf,CAAC,CAAC,OAAOoH,GAAG,EAAE;MACV,OAAOC,MAAM,CAACD,GAAG,CAAC;IACtB;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}