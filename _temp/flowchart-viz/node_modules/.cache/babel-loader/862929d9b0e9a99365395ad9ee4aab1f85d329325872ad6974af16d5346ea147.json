{"ast": null, "code": "export default function (callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}", "map": {"version": 3, "names": ["callback", "that", "index", "node", "call"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-hierarchy/src/hierarchy/find.js"], "sourcesContent": ["export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAEC,IAAI,EAAE;EACtC,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,IAAI,IAAI,IAAI,EAAE;IACvB,IAAIH,QAAQ,CAACI,IAAI,CAACH,IAAI,EAAEE,IAAI,EAAE,EAAED,KAAK,EAAE,IAAI,CAAC,EAAE;MAC5C,OAAOC,IAAI;IACb;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}