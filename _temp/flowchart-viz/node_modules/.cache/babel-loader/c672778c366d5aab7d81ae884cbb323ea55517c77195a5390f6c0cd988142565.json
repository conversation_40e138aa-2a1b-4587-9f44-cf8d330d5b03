{"ast": null, "code": "import { atan, exp, halfPi, log, tan } from \"../math.js\";\nimport { mercatorProjection } from \"./mercator.js\";\nexport function transverseMercatorRaw(lambda, phi) {\n  return [log(tan((halfPi + phi) / 2)), -lambda];\n}\ntransverseMercatorRaw.invert = function (x, y) {\n  return [-y, 2 * atan(exp(x)) - halfPi];\n};\nexport default function () {\n  var m = mercatorProjection(transverseMercatorRaw),\n    center = m.center,\n    rotate = m.rotate;\n  m.center = function (_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n  m.rotate = function (_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n  return rotate([0, 0, 90]).scale(159.155);\n}", "map": {"version": 3, "names": ["atan", "exp", "halfPi", "log", "tan", "mercatorProjection", "transverseMercatorRaw", "lambda", "phi", "invert", "x", "y", "m", "center", "rotate", "_", "arguments", "length", "scale"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/transverseMercator.js"], "sourcesContent": ["import {atan, exp, halfPi, log, tan} from \"../math.js\";\nimport {mercatorProjection} from \"./mercator.js\";\n\nexport function transverseMercatorRaw(lambda, phi) {\n  return [log(tan((halfPi + phi) / 2)), -lambda];\n}\n\ntransverseMercatorRaw.invert = function(x, y) {\n  return [-y, 2 * atan(exp(x)) - halfPi];\n};\n\nexport default function() {\n  var m = mercatorProjection(transverseMercatorRaw),\n      center = m.center,\n      rotate = m.rotate;\n\n  m.center = function(_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n\n  m.rotate = function(_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n\n  return rotate([0, 0, 90])\n      .scale(159.155);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,QAAO,YAAY;AACtD,SAAQC,kBAAkB,QAAO,eAAe;AAEhD,OAAO,SAASC,qBAAqBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACjD,OAAO,CAACL,GAAG,CAACC,GAAG,CAAC,CAACF,MAAM,GAAGM,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAACD,MAAM,CAAC;AAChD;AAEAD,qBAAqB,CAACG,MAAM,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5C,OAAO,CAAC,CAACA,CAAC,EAAE,CAAC,GAAGX,IAAI,CAACC,GAAG,CAACS,CAAC,CAAC,CAAC,GAAGR,MAAM,CAAC;AACxC,CAAC;AAED,eAAe,YAAW;EACxB,IAAIU,CAAC,GAAGP,kBAAkB,CAACC,qBAAqB,CAAC;IAC7CO,MAAM,GAAGD,CAAC,CAACC,MAAM;IACjBC,MAAM,GAAGF,CAAC,CAACE,MAAM;EAErBF,CAAC,CAACC,MAAM,GAAG,UAASE,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACC,MAAM,GAAGJ,MAAM,CAAC,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAGF,MAAM,CAAC,CAAC,EAAE,CAACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjF,CAAC;EAEDH,CAAC,CAACE,MAAM,GAAG,UAASC,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACC,MAAM,GAAGH,MAAM,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACE,MAAM,GAAG,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,IAAIA,CAAC,GAAGD,MAAM,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;EACzH,CAAC;EAED,OAAOD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CACpBI,KAAK,CAAC,OAAO,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}