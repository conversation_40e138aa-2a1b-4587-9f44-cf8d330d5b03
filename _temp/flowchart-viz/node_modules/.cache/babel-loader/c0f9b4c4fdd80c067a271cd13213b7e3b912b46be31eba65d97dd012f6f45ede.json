{"ast": null, "code": "import { clone, forEach, has, isEmpty, map, values } from \"lodash-es\";\nimport { toFastProperties } from \"@chevrotain/utils\";\nimport { computeAllProdsFollows } from \"../grammar/follow.js\";\nimport { createTokenInstance, EOF } from \"../../scan/tokens_public.js\";\nimport { defaultGrammarValidatorErrorProvider, defaultParserErrorProvider } from \"../errors_public.js\";\nimport { resolveGrammar, validateGrammar } from \"../grammar/gast/gast_resolver_public.js\";\nimport { Recoverable } from \"./traits/recoverable.js\";\nimport { LooksAhead } from \"./traits/looksahead.js\";\nimport { TreeBuilder } from \"./traits/tree_builder.js\";\nimport { LexerAdapter } from \"./traits/lexer_adapter.js\";\nimport { RecognizerApi } from \"./traits/recognizer_api.js\";\nimport { RecognizerEngine } from \"./traits/recognizer_engine.js\";\nimport { Error<PERSON>and<PERSON> } from \"./traits/error_handler.js\";\nimport { ContentAssist } from \"./traits/context_assist.js\";\nimport { GastRecorder } from \"./traits/gast_recorder.js\";\nimport { PerformanceTracer } from \"./traits/perf_tracer.js\";\nimport { applyMixins } from \"./utils/apply_mixins.js\";\nimport { validateLookahead } from \"../grammar/checks.js\";\nexport const END_OF_FILE = createTokenInstance(EOF, \"\", NaN, NaN, NaN, NaN, NaN, NaN);\nObject.freeze(END_OF_FILE);\nexport const DEFAULT_PARSER_CONFIG = Object.freeze({\n  recoveryEnabled: false,\n  maxLookahead: 3,\n  dynamicTokensEnabled: false,\n  outputCst: true,\n  errorMessageProvider: defaultParserErrorProvider,\n  nodeLocationTracking: \"none\",\n  traceInitPerf: false,\n  skipValidations: false\n});\nexport const DEFAULT_RULE_CONFIG = Object.freeze({\n  recoveryValueFunc: () => undefined,\n  resyncEnabled: true\n});\nexport var ParserDefinitionErrorType;\n(function (ParserDefinitionErrorType) {\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"INVALID_RULE_NAME\"] = 0] = \"INVALID_RULE_NAME\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"DUPLICATE_RULE_NAME\"] = 1] = \"DUPLICATE_RULE_NAME\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"INVALID_RULE_OVERRIDE\"] = 2] = \"INVALID_RULE_OVERRIDE\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"DUPLICATE_PRODUCTIONS\"] = 3] = \"DUPLICATE_PRODUCTIONS\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"UNRESOLVED_SUBRULE_REF\"] = 4] = \"UNRESOLVED_SUBRULE_REF\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"LEFT_RECURSION\"] = 5] = \"LEFT_RECURSION\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"NONE_LAST_EMPTY_ALT\"] = 6] = \"NONE_LAST_EMPTY_ALT\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"AMBIGUOUS_ALTS\"] = 7] = \"AMBIGUOUS_ALTS\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"CONFLICT_TOKENS_RULES_NAMESPACE\"] = 8] = \"CONFLICT_TOKENS_RULES_NAMESPACE\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"INVALID_TOKEN_NAME\"] = 9] = \"INVALID_TOKEN_NAME\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"NO_NON_EMPTY_LOOKAHEAD\"] = 10] = \"NO_NON_EMPTY_LOOKAHEAD\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"AMBIGUOUS_PREFIX_ALTS\"] = 11] = \"AMBIGUOUS_PREFIX_ALTS\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"TOO_MANY_ALTS\"] = 12] = \"TOO_MANY_ALTS\";\n  ParserDefinitionErrorType[ParserDefinitionErrorType[\"CUSTOM_LOOKAHEAD_VALIDATION\"] = 13] = \"CUSTOM_LOOKAHEAD_VALIDATION\";\n})(ParserDefinitionErrorType || (ParserDefinitionErrorType = {}));\nexport function EMPTY_ALT(value = undefined) {\n  return function () {\n    return value;\n  };\n}\nexport class Parser {\n  /**\n   *  @deprecated use the **instance** method with the same name instead\n   */\n  static performSelfAnalysis(parserInstance) {\n    throw Error(\"The **static** `performSelfAnalysis` method has been deprecated.\" + \"\\t\\nUse the **instance** method with the same name instead.\");\n  }\n  performSelfAnalysis() {\n    this.TRACE_INIT(\"performSelfAnalysis\", () => {\n      let defErrorsMsgs;\n      this.selfAnalysisDone = true;\n      const className = this.className;\n      this.TRACE_INIT(\"toFastProps\", () => {\n        // Without this voodoo magic the parser would be x3-x4 slower\n        // It seems it is better to invoke `toFastProperties` **before**\n        // Any manipulations of the `this` object done during the recording phase.\n        toFastProperties(this);\n      });\n      this.TRACE_INIT(\"Grammar Recording\", () => {\n        try {\n          this.enableRecording();\n          // Building the GAST\n          forEach(this.definedRulesNames, currRuleName => {\n            const wrappedRule = this[currRuleName];\n            const originalGrammarAction = wrappedRule[\"originalGrammarAction\"];\n            let recordedRuleGast;\n            this.TRACE_INIT(`${currRuleName} Rule`, () => {\n              recordedRuleGast = this.topLevelRuleRecord(currRuleName, originalGrammarAction);\n            });\n            this.gastProductionsCache[currRuleName] = recordedRuleGast;\n          });\n        } finally {\n          this.disableRecording();\n        }\n      });\n      let resolverErrors = [];\n      this.TRACE_INIT(\"Grammar Resolving\", () => {\n        resolverErrors = resolveGrammar({\n          rules: values(this.gastProductionsCache)\n        });\n        this.definitionErrors = this.definitionErrors.concat(resolverErrors);\n      });\n      this.TRACE_INIT(\"Grammar Validations\", () => {\n        // only perform additional grammar validations IFF no resolving errors have occurred.\n        // as unresolved grammar may lead to unhandled runtime exceptions in the follow up validations.\n        if (isEmpty(resolverErrors) && this.skipValidations === false) {\n          const validationErrors = validateGrammar({\n            rules: values(this.gastProductionsCache),\n            tokenTypes: values(this.tokensMap),\n            errMsgProvider: defaultGrammarValidatorErrorProvider,\n            grammarName: className\n          });\n          const lookaheadValidationErrors = validateLookahead({\n            lookaheadStrategy: this.lookaheadStrategy,\n            rules: values(this.gastProductionsCache),\n            tokenTypes: values(this.tokensMap),\n            grammarName: className\n          });\n          this.definitionErrors = this.definitionErrors.concat(validationErrors, lookaheadValidationErrors);\n        }\n      });\n      // this analysis may fail if the grammar is not perfectly valid\n      if (isEmpty(this.definitionErrors)) {\n        // The results of these computations are not needed unless error recovery is enabled.\n        if (this.recoveryEnabled) {\n          this.TRACE_INIT(\"computeAllProdsFollows\", () => {\n            const allFollows = computeAllProdsFollows(values(this.gastProductionsCache));\n            this.resyncFollows = allFollows;\n          });\n        }\n        this.TRACE_INIT(\"ComputeLookaheadFunctions\", () => {\n          var _a, _b;\n          (_b = (_a = this.lookaheadStrategy).initialize) === null || _b === void 0 ? void 0 : _b.call(_a, {\n            rules: values(this.gastProductionsCache)\n          });\n          this.preComputeLookaheadFunctions(values(this.gastProductionsCache));\n        });\n      }\n      if (!Parser.DEFER_DEFINITION_ERRORS_HANDLING && !isEmpty(this.definitionErrors)) {\n        defErrorsMsgs = map(this.definitionErrors, defError => defError.message);\n        throw new Error(`Parser Definition Errors detected:\\n ${defErrorsMsgs.join(\"\\n-------------------------------\\n\")}`);\n      }\n    });\n  }\n  constructor(tokenVocabulary, config) {\n    this.definitionErrors = [];\n    this.selfAnalysisDone = false;\n    const that = this;\n    that.initErrorHandler(config);\n    that.initLexerAdapter();\n    that.initLooksAhead(config);\n    that.initRecognizerEngine(tokenVocabulary, config);\n    that.initRecoverable(config);\n    that.initTreeBuilder(config);\n    that.initContentAssist();\n    that.initGastRecorder(config);\n    that.initPerformanceTracer(config);\n    if (has(config, \"ignoredIssues\")) {\n      throw new Error(\"The <ignoredIssues> IParserConfig property has been deprecated.\\n\\t\" + \"Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\\n\\t\" + \"See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\\n\\t\" + \"For further details.\");\n    }\n    this.skipValidations = has(config, \"skipValidations\") ? config.skipValidations // casting assumes the end user passing the correct type\n    : DEFAULT_PARSER_CONFIG.skipValidations;\n  }\n}\n// Set this flag to true if you don't want the Parser to throw error when problems in it's definition are detected.\n// (normally during the parser's constructor).\n// This is a design time flag, it will not affect the runtime error handling of the parser, just design time errors,\n// for example: duplicate rule names, referencing an unresolved subrule, ect...\n// This flag should not be enabled during normal usage, it is used in special situations, for example when\n// needing to display the parser definition errors in some GUI(online playground).\nParser.DEFER_DEFINITION_ERRORS_HANDLING = false;\napplyMixins(Parser, [Recoverable, LooksAhead, TreeBuilder, LexerAdapter, RecognizerEngine, RecognizerApi, ErrorHandler, ContentAssist, GastRecorder, PerformanceTracer]);\nexport class CstParser extends Parser {\n  constructor(tokenVocabulary, config = DEFAULT_PARSER_CONFIG) {\n    const configClone = clone(config);\n    configClone.outputCst = true;\n    super(tokenVocabulary, configClone);\n  }\n}\nexport class EmbeddedActionsParser extends Parser {\n  constructor(tokenVocabulary, config = DEFAULT_PARSER_CONFIG) {\n    const configClone = clone(config);\n    configClone.outputCst = false;\n    super(tokenVocabulary, configClone);\n  }\n}", "map": {"version": 3, "names": ["clone", "for<PERSON>ach", "has", "isEmpty", "map", "values", "toFastProperties", "computeAllProdsFollows", "createTokenInstance", "EOF", "defaultGrammarValidatorErrorProvider", "defaultParserErrorProvider", "resolveGrammar", "validate<PERSON>rammar", "Recoverable", "LooksAhead", "TreeBuilder", "LexerAdapter", "RecognizerApi", "RecognizerEngine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentAssist", "GastRecorder", "PerformanceTracer", "applyMixins", "validate<PERSON><PERSON><PERSON><PERSON>", "END_OF_FILE", "NaN", "Object", "freeze", "DEFAULT_PARSER_CONFIG", "recoveryEnabled", "max<PERSON><PERSON><PERSON><PERSON>", "dynamicTokensEnabled", "outputCst", "errorMessageProvider", "nodeLocationTracking", "traceInitPerf", "skipValidations", "DEFAULT_RULE_CONFIG", "recoveryValueFunc", "undefined", "resyncEnabled", "ParserDefinitionErrorType", "EMPTY_ALT", "value", "<PERSON><PERSON><PERSON>", "performSelfAnalysis", "parserInstance", "Error", "TRACE_INIT", "defErrorsMsgs", "selfAnalysisDone", "className", "enableRecording", "definedRulesNames", "currRuleName", "wrappedRule", "originalGrammarAction", "recordedRuleGast", "topLevelRuleRecord", "gastProductionsCache", "disableRecording", "resolverErrors", "rules", "definitionErrors", "concat", "validationErrors", "tokenTypes", "tokensMap", "err<PERSON><PERSON><PERSON><PERSON><PERSON>", "grammarName", "lookaheadValidationErrors", "lookaheadStrategy", "allFollows", "resyncFollows", "_b", "_a", "initialize", "call", "preComputeLookaheadFunctions", "DEFER_DEFINITION_ERRORS_HANDLING", "defError", "message", "join", "constructor", "tokenVocabulary", "config", "that", "initErrorHandler", "initLexerAdapter", "initLooksAhead", "initRecognizerEngine", "initRecoverable", "initTreeBuilder", "initContentAssist", "initGastRecorder", "initPerformanceTracer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON>", "EmbeddedActions<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/parser.ts"], "sourcesContent": ["import { clone, forEach, has, isEmpty, map, values } from \"lodash-es\";\nimport { toFastProperties } from \"@chevrotain/utils\";\nimport { computeAllProdsFollows } from \"../grammar/follow.js\";\nimport { createTokenInstance, EOF } from \"../../scan/tokens_public.js\";\nimport {\n  defaultGrammarValidatorErrorProvider,\n  defaultParserErrorProvider,\n} from \"../errors_public.js\";\nimport {\n  resolveGrammar,\n  validateGrammar,\n} from \"../grammar/gast/gast_resolver_public.js\";\nimport {\n  CstNode,\n  IParserConfig,\n  IRecognitionException,\n  IRuleConfig,\n  IToken,\n  TokenType,\n  TokenVocabulary,\n} from \"@chevrotain/types\";\nimport { Recoverable } from \"./traits/recoverable.js\";\nimport { LooksAhead } from \"./traits/looksahead.js\";\nimport { TreeBuilder } from \"./traits/tree_builder.js\";\nimport { LexerAdapter } from \"./traits/lexer_adapter.js\";\nimport { RecognizerApi } from \"./traits/recognizer_api.js\";\nimport { RecognizerEngine } from \"./traits/recognizer_engine.js\";\n\nimport { ErrorHandler } from \"./traits/error_handler.js\";\nimport { MixedInParser } from \"./traits/parser_traits.js\";\nimport { ContentAssist } from \"./traits/context_assist.js\";\nimport { GastRecorder } from \"./traits/gast_recorder.js\";\nimport { PerformanceTracer } from \"./traits/perf_tracer.js\";\nimport { applyMixins } from \"./utils/apply_mixins.js\";\nimport { IParserDefinitionError } from \"../grammar/types.js\";\nimport { Rule } from \"@chevrotain/gast\";\nimport { IParserConfigInternal, ParserMethodInternal } from \"./types.js\";\nimport { validateLookahead } from \"../grammar/checks.js\";\n\nexport const END_OF_FILE = createTokenInstance(\n  EOF,\n  \"\",\n  NaN,\n  NaN,\n  NaN,\n  NaN,\n  NaN,\n  NaN,\n);\nObject.freeze(END_OF_FILE);\n\nexport type TokenMatcher = (token: IToken, tokType: TokenType) => boolean;\n\nexport const DEFAULT_PARSER_CONFIG: Required<\n  Omit<IParserConfigInternal, \"lookaheadStrategy\">\n> = Object.freeze({\n  recoveryEnabled: false,\n  maxLookahead: 3,\n  dynamicTokensEnabled: false,\n  outputCst: true,\n  errorMessageProvider: defaultParserErrorProvider,\n  nodeLocationTracking: \"none\",\n  traceInitPerf: false,\n  skipValidations: false,\n});\n\nexport const DEFAULT_RULE_CONFIG: Required<IRuleConfig<any>> = Object.freeze({\n  recoveryValueFunc: () => undefined,\n  resyncEnabled: true,\n});\n\nexport enum ParserDefinitionErrorType {\n  INVALID_RULE_NAME = 0,\n  DUPLICATE_RULE_NAME = 1,\n  INVALID_RULE_OVERRIDE = 2,\n  DUPLICATE_PRODUCTIONS = 3,\n  UNRESOLVED_SUBRULE_REF = 4,\n  LEFT_RECURSION = 5,\n  NONE_LAST_EMPTY_ALT = 6,\n  AMBIGUOUS_ALTS = 7,\n  CONFLICT_TOKENS_RULES_NAMESPACE = 8,\n  INVALID_TOKEN_NAME = 9,\n  NO_NON_EMPTY_LOOKAHEAD = 10,\n  AMBIGUOUS_PREFIX_ALTS = 11,\n  TOO_MANY_ALTS = 12,\n  CUSTOM_LOOKAHEAD_VALIDATION = 13,\n}\n\nexport interface IParserDuplicatesDefinitionError\n  extends IParserDefinitionError {\n  dslName: string;\n  occurrence: number;\n  parameter?: string;\n}\n\nexport interface IParserEmptyAlternativeDefinitionError\n  extends IParserDefinitionError {\n  occurrence: number;\n  alternative: number;\n}\n\nexport interface IParserAmbiguousAlternativesDefinitionError\n  extends IParserDefinitionError {\n  occurrence: number | string;\n  alternatives: number[];\n}\n\nexport interface IParserUnresolvedRefDefinitionError\n  extends IParserDefinitionError {\n  unresolvedRefName: string;\n}\n\nexport interface IParserState {\n  errors: IRecognitionException[];\n  lexerState: any;\n  RULE_STACK: number[];\n  CST_STACK: CstNode[];\n}\n\nexport type Predicate = () => boolean;\n\nexport function EMPTY_ALT(): () => undefined;\nexport function EMPTY_ALT<T>(value: T): () => T;\nexport function EMPTY_ALT(value: any = undefined) {\n  return function () {\n    return value;\n  };\n}\n\nexport class Parser {\n  // Set this flag to true if you don't want the Parser to throw error when problems in it's definition are detected.\n  // (normally during the parser's constructor).\n  // This is a design time flag, it will not affect the runtime error handling of the parser, just design time errors,\n  // for example: duplicate rule names, referencing an unresolved subrule, ect...\n  // This flag should not be enabled during normal usage, it is used in special situations, for example when\n  // needing to display the parser definition errors in some GUI(online playground).\n  static DEFER_DEFINITION_ERRORS_HANDLING: boolean = false;\n\n  /**\n   *  @deprecated use the **instance** method with the same name instead\n   */\n  static performSelfAnalysis(parserInstance: Parser): void {\n    throw Error(\n      \"The **static** `performSelfAnalysis` method has been deprecated.\" +\n        \"\\t\\nUse the **instance** method with the same name instead.\",\n    );\n  }\n\n  public performSelfAnalysis(this: MixedInParser): void {\n    this.TRACE_INIT(\"performSelfAnalysis\", () => {\n      let defErrorsMsgs;\n\n      this.selfAnalysisDone = true;\n      const className = this.className;\n\n      this.TRACE_INIT(\"toFastProps\", () => {\n        // Without this voodoo magic the parser would be x3-x4 slower\n        // It seems it is better to invoke `toFastProperties` **before**\n        // Any manipulations of the `this` object done during the recording phase.\n        toFastProperties(this);\n      });\n\n      this.TRACE_INIT(\"Grammar Recording\", () => {\n        try {\n          this.enableRecording();\n          // Building the GAST\n          forEach(this.definedRulesNames, (currRuleName) => {\n            const wrappedRule = (this as any)[\n              currRuleName\n            ] as ParserMethodInternal<unknown[], unknown>;\n            const originalGrammarAction = wrappedRule[\"originalGrammarAction\"];\n            let recordedRuleGast!: Rule;\n            this.TRACE_INIT(`${currRuleName} Rule`, () => {\n              recordedRuleGast = this.topLevelRuleRecord(\n                currRuleName,\n                originalGrammarAction,\n              );\n            });\n            this.gastProductionsCache[currRuleName] = recordedRuleGast;\n          });\n        } finally {\n          this.disableRecording();\n        }\n      });\n\n      let resolverErrors: IParserDefinitionError[] = [];\n      this.TRACE_INIT(\"Grammar Resolving\", () => {\n        resolverErrors = resolveGrammar({\n          rules: values(this.gastProductionsCache),\n        });\n        this.definitionErrors = this.definitionErrors.concat(resolverErrors);\n      });\n\n      this.TRACE_INIT(\"Grammar Validations\", () => {\n        // only perform additional grammar validations IFF no resolving errors have occurred.\n        // as unresolved grammar may lead to unhandled runtime exceptions in the follow up validations.\n        if (isEmpty(resolverErrors) && this.skipValidations === false) {\n          const validationErrors = validateGrammar({\n            rules: values(this.gastProductionsCache),\n            tokenTypes: values(this.tokensMap),\n            errMsgProvider: defaultGrammarValidatorErrorProvider,\n            grammarName: className,\n          });\n          const lookaheadValidationErrors = validateLookahead({\n            lookaheadStrategy: this.lookaheadStrategy,\n            rules: values(this.gastProductionsCache),\n            tokenTypes: values(this.tokensMap),\n            grammarName: className,\n          });\n          this.definitionErrors = this.definitionErrors.concat(\n            validationErrors,\n            lookaheadValidationErrors,\n          );\n        }\n      });\n\n      // this analysis may fail if the grammar is not perfectly valid\n      if (isEmpty(this.definitionErrors)) {\n        // The results of these computations are not needed unless error recovery is enabled.\n        if (this.recoveryEnabled) {\n          this.TRACE_INIT(\"computeAllProdsFollows\", () => {\n            const allFollows = computeAllProdsFollows(\n              values(this.gastProductionsCache),\n            );\n            this.resyncFollows = allFollows;\n          });\n        }\n\n        this.TRACE_INIT(\"ComputeLookaheadFunctions\", () => {\n          this.lookaheadStrategy.initialize?.({\n            rules: values(this.gastProductionsCache),\n          });\n          this.preComputeLookaheadFunctions(values(this.gastProductionsCache));\n        });\n      }\n\n      if (\n        !Parser.DEFER_DEFINITION_ERRORS_HANDLING &&\n        !isEmpty(this.definitionErrors)\n      ) {\n        defErrorsMsgs = map(\n          this.definitionErrors,\n          (defError) => defError.message,\n        );\n        throw new Error(\n          `Parser Definition Errors detected:\\n ${defErrorsMsgs.join(\n            \"\\n-------------------------------\\n\",\n          )}`,\n        );\n      }\n    });\n  }\n\n  definitionErrors: IParserDefinitionError[] = [];\n  selfAnalysisDone = false;\n  protected skipValidations: boolean;\n\n  constructor(tokenVocabulary: TokenVocabulary, config: IParserConfig) {\n    const that: MixedInParser = this as any;\n    that.initErrorHandler(config);\n    that.initLexerAdapter();\n    that.initLooksAhead(config);\n    that.initRecognizerEngine(tokenVocabulary, config);\n    that.initRecoverable(config);\n    that.initTreeBuilder(config);\n    that.initContentAssist();\n    that.initGastRecorder(config);\n    that.initPerformanceTracer(config);\n\n    if (has(config, \"ignoredIssues\")) {\n      throw new Error(\n        \"The <ignoredIssues> IParserConfig property has been deprecated.\\n\\t\" +\n          \"Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\\n\\t\" +\n          \"See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\\n\\t\" +\n          \"For further details.\",\n      );\n    }\n\n    this.skipValidations = has(config, \"skipValidations\")\n      ? (config.skipValidations as boolean) // casting assumes the end user passing the correct type\n      : DEFAULT_PARSER_CONFIG.skipValidations;\n  }\n}\n\napplyMixins(Parser, [\n  Recoverable,\n  LooksAhead,\n  TreeBuilder,\n  LexerAdapter,\n  RecognizerEngine,\n  RecognizerApi,\n  ErrorHandler,\n  ContentAssist,\n  GastRecorder,\n  PerformanceTracer,\n]);\n\nexport class CstParser extends Parser {\n  constructor(\n    tokenVocabulary: TokenVocabulary,\n    config: IParserConfigInternal = DEFAULT_PARSER_CONFIG,\n  ) {\n    const configClone = clone(config);\n    configClone.outputCst = true;\n    super(tokenVocabulary, configClone);\n  }\n}\n\nexport class EmbeddedActionsParser extends Parser {\n  constructor(\n    tokenVocabulary: TokenVocabulary,\n    config: IParserConfigInternal = DEFAULT_PARSER_CONFIG,\n  ) {\n    const configClone = clone(config);\n    configClone.outputCst = false;\n    super(tokenVocabulary, configClone);\n  }\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,WAAW;AACrE,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,mBAAmB,EAAEC,GAAG,QAAQ,6BAA6B;AACtE,SACEC,oCAAoC,EACpCC,0BAA0B,QACrB,qBAAqB;AAC5B,SACEC,cAAc,EACdC,eAAe,QACV,yCAAyC;AAUhD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,gBAAgB,QAAQ,+BAA+B;AAEhE,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,WAAW,QAAQ,yBAAyB;AAIrD,SAASC,iBAAiB,QAAQ,sBAAsB;AAExD,OAAO,MAAMC,WAAW,GAAGlB,mBAAmB,CAC5CC,GAAG,EACH,EAAE,EACFkB,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,CACJ;AACDC,MAAM,CAACC,MAAM,CAACH,WAAW,CAAC;AAI1B,OAAO,MAAMI,qBAAqB,GAE9BF,MAAM,CAACC,MAAM,CAAC;EAChBE,eAAe,EAAE,KAAK;EACtBC,YAAY,EAAE,CAAC;EACfC,oBAAoB,EAAE,KAAK;EAC3BC,SAAS,EAAE,IAAI;EACfC,oBAAoB,EAAExB,0BAA0B;EAChDyB,oBAAoB,EAAE,MAAM;EAC5BC,aAAa,EAAE,KAAK;EACpBC,eAAe,EAAE;CAClB,CAAC;AAEF,OAAO,MAAMC,mBAAmB,GAA+BX,MAAM,CAACC,MAAM,CAAC;EAC3EW,iBAAiB,EAAEA,CAAA,KAAMC,SAAS;EAClCC,aAAa,EAAE;CAChB,CAAC;AAEF,WAAYC,yBAeX;AAfD,WAAYA,yBAAyB;EACnCA,yBAAA,CAAAA,yBAAA,gDAAqB;EACrBA,yBAAA,CAAAA,yBAAA,oDAAuB;EACvBA,yBAAA,CAAAA,yBAAA,wDAAyB;EACzBA,yBAAA,CAAAA,yBAAA,wDAAyB;EACzBA,yBAAA,CAAAA,yBAAA,0DAA0B;EAC1BA,yBAAA,CAAAA,yBAAA,0CAAkB;EAClBA,yBAAA,CAAAA,yBAAA,oDAAuB;EACvBA,yBAAA,CAAAA,yBAAA,0CAAkB;EAClBA,yBAAA,CAAAA,yBAAA,4EAAmC;EACnCA,yBAAA,CAAAA,yBAAA,kDAAsB;EACtBA,yBAAA,CAAAA,yBAAA,2DAA2B;EAC3BA,yBAAA,CAAAA,yBAAA,yDAA0B;EAC1BA,yBAAA,CAAAA,yBAAA,yCAAkB;EAClBA,yBAAA,CAAAA,yBAAA,qEAAgC;AAClC,CAAC,EAfWA,yBAAyB,KAAzBA,yBAAyB;AAoDrC,OAAM,SAAUC,SAASA,CAACC,KAAA,GAAaJ,SAAS;EAC9C,OAAO;IACL,OAAOI,KAAK;EACd,CAAC;AACH;AAEA,OAAM,MAAOC,MAAM;EASjB;;;EAGA,OAAOC,mBAAmBA,CAACC,cAAsB;IAC/C,MAAMC,KAAK,CACT,kEAAkE,GAChE,6DAA6D,CAChE;EACH;EAEOF,mBAAmBA,CAAA;IACxB,IAAI,CAACG,UAAU,CAAC,qBAAqB,EAAE,MAAK;MAC1C,IAAIC,aAAa;MAEjB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS;MAEhC,IAAI,CAACH,UAAU,CAAC,aAAa,EAAE,MAAK;QAClC;QACA;QACA;QACA5C,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,CAAC;MAEF,IAAI,CAAC4C,UAAU,CAAC,mBAAmB,EAAE,MAAK;QACxC,IAAI;UACF,IAAI,CAACI,eAAe,EAAE;UACtB;UACArD,OAAO,CAAC,IAAI,CAACsD,iBAAiB,EAAGC,YAAY,IAAI;YAC/C,MAAMC,WAAW,GAAI,IAAY,CAC/BD,YAAY,CAC+B;YAC7C,MAAME,qBAAqB,GAAGD,WAAW,CAAC,uBAAuB,CAAC;YAClE,IAAIE,gBAAuB;YAC3B,IAAI,CAACT,UAAU,CAAC,GAAGM,YAAY,OAAO,EAAE,MAAK;cAC3CG,gBAAgB,GAAG,IAAI,CAACC,kBAAkB,CACxCJ,YAAY,EACZE,qBAAqB,CACtB;YACH,CAAC,CAAC;YACF,IAAI,CAACG,oBAAoB,CAACL,YAAY,CAAC,GAAGG,gBAAgB;UAC5D,CAAC,CAAC;SACH,SAAS;UACR,IAAI,CAACG,gBAAgB,EAAE;;MAE3B,CAAC,CAAC;MAEF,IAAIC,cAAc,GAA6B,EAAE;MACjD,IAAI,CAACb,UAAU,CAAC,mBAAmB,EAAE,MAAK;QACxCa,cAAc,GAAGnD,cAAc,CAAC;UAC9BoD,KAAK,EAAE3D,MAAM,CAAC,IAAI,CAACwD,oBAAoB;SACxC,CAAC;QACF,IAAI,CAACI,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACC,MAAM,CAACH,cAAc,CAAC;MACtE,CAAC,CAAC;MAEF,IAAI,CAACb,UAAU,CAAC,qBAAqB,EAAE,MAAK;QAC1C;QACA;QACA,IAAI/C,OAAO,CAAC4D,cAAc,CAAC,IAAI,IAAI,CAACzB,eAAe,KAAK,KAAK,EAAE;UAC7D,MAAM6B,gBAAgB,GAAGtD,eAAe,CAAC;YACvCmD,KAAK,EAAE3D,MAAM,CAAC,IAAI,CAACwD,oBAAoB,CAAC;YACxCO,UAAU,EAAE/D,MAAM,CAAC,IAAI,CAACgE,SAAS,CAAC;YAClCC,cAAc,EAAE5D,oCAAoC;YACpD6D,WAAW,EAAElB;WACd,CAAC;UACF,MAAMmB,yBAAyB,GAAG/C,iBAAiB,CAAC;YAClDgD,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;YACzCT,KAAK,EAAE3D,MAAM,CAAC,IAAI,CAACwD,oBAAoB,CAAC;YACxCO,UAAU,EAAE/D,MAAM,CAAC,IAAI,CAACgE,SAAS,CAAC;YAClCE,WAAW,EAAElB;WACd,CAAC;UACF,IAAI,CAACY,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACC,MAAM,CAClDC,gBAAgB,EAChBK,yBAAyB,CAC1B;;MAEL,CAAC,CAAC;MAEF;MACA,IAAIrE,OAAO,CAAC,IAAI,CAAC8D,gBAAgB,CAAC,EAAE;QAClC;QACA,IAAI,IAAI,CAAClC,eAAe,EAAE;UACxB,IAAI,CAACmB,UAAU,CAAC,wBAAwB,EAAE,MAAK;YAC7C,MAAMwB,UAAU,GAAGnE,sBAAsB,CACvCF,MAAM,CAAC,IAAI,CAACwD,oBAAoB,CAAC,CAClC;YACD,IAAI,CAACc,aAAa,GAAGD,UAAU;UACjC,CAAC,CAAC;;QAGJ,IAAI,CAACxB,UAAU,CAAC,2BAA2B,EAAE,MAAK;;UAChD,CAAA0B,EAAA,IAAAC,EAAA,OAAI,CAACJ,iBAAiB,EAACK,UAAU,cAAAF,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAG;YAClCb,KAAK,EAAE3D,MAAM,CAAC,IAAI,CAACwD,oBAAoB;WACxC,CAAC;UACF,IAAI,CAACmB,4BAA4B,CAAC3E,MAAM,CAAC,IAAI,CAACwD,oBAAoB,CAAC,CAAC;QACtE,CAAC,CAAC;;MAGJ,IACE,CAACf,MAAM,CAACmC,gCAAgC,IACxC,CAAC9E,OAAO,CAAC,IAAI,CAAC8D,gBAAgB,CAAC,EAC/B;QACAd,aAAa,GAAG/C,GAAG,CACjB,IAAI,CAAC6D,gBAAgB,EACpBiB,QAAQ,IAAKA,QAAQ,CAACC,OAAO,CAC/B;QACD,MAAM,IAAIlC,KAAK,CACb,wCAAwCE,aAAa,CAACiC,IAAI,CACxD,qCAAqC,CACtC,EAAE,CACJ;;IAEL,CAAC,CAAC;EACJ;EAMAC,YAAYC,eAAgC,EAAEC,MAAqB;IAJnE,KAAAtB,gBAAgB,GAA6B,EAAE;IAC/C,KAAAb,gBAAgB,GAAG,KAAK;IAItB,MAAMoC,IAAI,GAAkB,IAAW;IACvCA,IAAI,CAACC,gBAAgB,CAACF,MAAM,CAAC;IAC7BC,IAAI,CAACE,gBAAgB,EAAE;IACvBF,IAAI,CAACG,cAAc,CAACJ,MAAM,CAAC;IAC3BC,IAAI,CAACI,oBAAoB,CAACN,eAAe,EAAEC,MAAM,CAAC;IAClDC,IAAI,CAACK,eAAe,CAACN,MAAM,CAAC;IAC5BC,IAAI,CAACM,eAAe,CAACP,MAAM,CAAC;IAC5BC,IAAI,CAACO,iBAAiB,EAAE;IACxBP,IAAI,CAACQ,gBAAgB,CAACT,MAAM,CAAC;IAC7BC,IAAI,CAACS,qBAAqB,CAACV,MAAM,CAAC;IAElC,IAAIrF,GAAG,CAACqF,MAAM,EAAE,eAAe,CAAC,EAAE;MAChC,MAAM,IAAItC,KAAK,CACb,qEAAqE,GACnE,kFAAkF,GAClF,8FAA8F,GAC9F,sBAAsB,CACzB;;IAGH,IAAI,CAACX,eAAe,GAAGpC,GAAG,CAACqF,MAAM,EAAE,iBAAiB,CAAC,GAChDA,MAAM,CAACjD,eAA2B,CAAC;IAAA,EACpCR,qBAAqB,CAACQ,eAAe;EAC3C;;AAvJA;AACA;AACA;AACA;AACA;AACA;AACOQ,MAAA,CAAAmC,gCAAgC,GAAY,KAAK;AAoJ1DzD,WAAW,CAACsB,MAAM,EAAE,CAClBhC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZE,gBAAgB,EAChBD,aAAa,EACbE,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,iBAAiB,CAClB,CAAC;AAEF,OAAM,MAAO2E,SAAU,SAAQpD,MAAM;EACnCuC,YACEC,eAAgC,EAChCC,MAAA,GAAgCzD,qBAAqB;IAErD,MAAMqE,WAAW,GAAGnG,KAAK,CAACuF,MAAM,CAAC;IACjCY,WAAW,CAACjE,SAAS,GAAG,IAAI;IAC5B,KAAK,CAACoD,eAAe,EAAEa,WAAW,CAAC;EACrC;;AAGF,OAAM,MAAOC,qBAAsB,SAAQtD,MAAM;EAC/CuC,YACEC,eAAgC,EAChCC,MAAA,GAAgCzD,qBAAqB;IAErD,MAAMqE,WAAW,GAAGnG,KAAK,CAACuF,MAAM,CAAC;IACjCY,WAAW,CAACjE,SAAS,GAAG,KAAK;IAC7B,KAAK,CAACoD,eAAe,EAAEa,WAAW,CAAC;EACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}