{"ast": null, "code": "export var abs = Math.abs;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var max = Math.max;\nexport var min = Math.min;\nexport var sin = Math.sin;\nexport var sqrt = Math.sqrt;\nexport var epsilon = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = 2 * pi;\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}", "map": {"version": 3, "names": ["abs", "Math", "atan2", "cos", "max", "min", "sin", "sqrt", "epsilon", "pi", "PI", "halfPi", "tau", "acos", "x", "asin"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/math.js"], "sourcesContent": ["export var abs = Math.abs;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var max = Math.max;\nexport var min = Math.min;\nexport var sin = Math.sin;\nexport var sqrt = Math.sqrt;\n\nexport var epsilon = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAG;AACzB,OAAO,IAAIE,KAAK,GAAGD,IAAI,CAACC,KAAK;AAC7B,OAAO,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGH,IAAI,CAACG,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGJ,IAAI,CAACI,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGL,IAAI,CAACK,GAAG;AACzB,OAAO,IAAIC,IAAI,GAAGN,IAAI,CAACM,IAAI;AAE3B,OAAO,IAAIC,OAAO,GAAG,KAAK;AAC1B,OAAO,IAAIC,EAAE,GAAGR,IAAI,CAACS,EAAE;AACvB,OAAO,IAAIC,MAAM,GAAGF,EAAE,GAAG,CAAC;AAC1B,OAAO,IAAIG,GAAG,GAAG,CAAC,GAAGH,EAAE;AAEvB,OAAO,SAASI,IAAIA,CAACC,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGL,EAAE,GAAGR,IAAI,CAACY,IAAI,CAACC,CAAC,CAAC;AAC/C;AAEA,OAAO,SAASC,IAAIA,CAACD,CAAC,EAAE;EACtB,OAAOA,CAAC,IAAI,CAAC,GAAGH,MAAM,GAAGG,CAAC,IAAI,CAAC,CAAC,GAAG,CAACH,MAAM,GAAGV,IAAI,CAACc,IAAI,CAACD,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}