{"ast": null, "code": "import { buildModel } from \"./model.js\";\nimport { genDts } from \"./generate.js\";\nconst defaultOptions = {\n  includeVisitorInterface: true,\n  visitorInterfaceName: \"ICstNodeVisitor\"\n};\nexport function generateCstDts(productions, options) {\n  const effectiveOptions = Object.assign(Object.assign({}, defaultOptions), options);\n  const model = buildModel(productions);\n  return genDts(model, effectiveOptions);\n}", "map": {"version": 3, "names": ["buildModel", "genDts", "defaultOptions", "includeVisitorInterface", "visitorInterfaceName", "generateCstDts", "productions", "options", "effectiveOptions", "Object", "assign", "model"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/cst-dts-gen/src/api.ts"], "sourcesContent": ["import { GenerateDtsOptions, Rule } from \"@chevrotain/types\";\nimport { buildModel } from \"./model.js\";\nimport { genDts } from \"./generate.js\";\n\nconst defaultOptions: Required<GenerateDtsOptions> = {\n  includeVisitorInterface: true,\n  visitorInterfaceName: \"ICstNodeVisitor\",\n};\n\nexport function generateCstDts(\n  productions: Record<string, Rule>,\n  options?: GenerateDtsOptions,\n): string {\n  const effectiveOptions = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const model = buildModel(productions);\n\n  return genDts(model, effectiveOptions);\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,YAAY;AACvC,SAASC,MAAM,QAAQ,eAAe;AAEtC,MAAMC,cAAc,GAAiC;EACnDC,uBAAuB,EAAE,IAAI;EAC7BC,oBAAoB,EAAE;CACvB;AAED,OAAM,SAAUC,cAAcA,CAC5BC,WAAiC,EACjCC,OAA4B;EAE5B,MAAMC,gBAAgB,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACjBR,cAAc,GACdK,OAAO,CACX;EAED,MAAMI,KAAK,GAAGX,UAAU,CAACM,WAAW,CAAC;EAErC,OAAOL,MAAM,CAACU,KAAK,EAAEH,gBAAgB,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}