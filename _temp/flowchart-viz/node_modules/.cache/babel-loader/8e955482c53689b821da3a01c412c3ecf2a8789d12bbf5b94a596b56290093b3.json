{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n/**\n * Re-export 'TextDocument' from 'vscode-languageserver-textdocument' for convenience,\n *  including both type _and_ symbol (namespace), as we here and there also refer to the symbol,\n *  the overhead is very small, just a few kilobytes.\n * Everything else of that package (at the time contributing) is also defined\n *  in 'vscode-languageserver-protocol' or 'vscode-languageserver-types'.\n */\nexport { TextDocument } from 'vscode-languageserver-textdocument';\nimport { TextDocument } from './documents.js';\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { stream } from '../utils/stream.js';\nimport { URI } from '../utils/uri-utils.js';\n/**\n * A document is subject to several phases that are run in predefined order. Any state value implies that\n * smaller state values are finished as well.\n */\nexport var DocumentState;\n(function (DocumentState) {\n  /**\n   * The text content has changed and needs to be parsed again. The AST held by this outdated\n   * document instance is no longer valid.\n   */\n  DocumentState[DocumentState[\"Changed\"] = 0] = \"Changed\";\n  /**\n   * An AST has been created from the text content. The document structure can be traversed,\n   * but cross-references cannot be resolved yet. If necessary, the structure can be manipulated\n   * at this stage as a preprocessing step.\n   */\n  DocumentState[DocumentState[\"Parsed\"] = 1] = \"Parsed\";\n  /**\n   * The `IndexManager` service has processed AST nodes of this document. This means the\n   * exported symbols are available in the global scope and can be resolved from other documents.\n   */\n  DocumentState[DocumentState[\"IndexedContent\"] = 2] = \"IndexedContent\";\n  /**\n   * The `ScopeComputation` service has processed this document. This means the local symbols\n   * are stored in a MultiMap so they can be looked up by the `ScopeProvider` service.\n   * Once a document has reached this state, you may follow every reference - it will lazily\n   * resolve its `ref` property and yield either the target AST node or `undefined` in case\n   * the target is not in scope.\n   */\n  DocumentState[DocumentState[\"ComputedScopes\"] = 3] = \"ComputedScopes\";\n  /**\n   * The `Linker` service has processed this document. All outgoing references have been\n   * resolved or marked as erroneous.\n   */\n  DocumentState[DocumentState[\"Linked\"] = 4] = \"Linked\";\n  /**\n   * The `IndexManager` service has processed AST node references of this document. This is\n   * necessary to determine which documents are affected by a change in one of the workspace\n   * documents.\n   */\n  DocumentState[DocumentState[\"IndexedReferences\"] = 5] = \"IndexedReferences\";\n  /**\n   * The `DocumentValidator` service has processed this document. The language server listens\n   * to the results of this phase and sends diagnostics to the client.\n   */\n  DocumentState[DocumentState[\"Validated\"] = 6] = \"Validated\";\n})(DocumentState || (DocumentState = {}));\nexport class DefaultLangiumDocumentFactory {\n  constructor(services) {\n    this.serviceRegistry = services.ServiceRegistry;\n    this.textDocuments = services.workspace.TextDocuments;\n    this.fileSystemProvider = services.workspace.FileSystemProvider;\n  }\n  async fromUri(uri, cancellationToken = CancellationToken.None) {\n    const content = await this.fileSystemProvider.readFile(uri);\n    return this.createAsync(uri, content, cancellationToken);\n  }\n  fromTextDocument(textDocument, uri, token) {\n    uri = uri !== null && uri !== void 0 ? uri : URI.parse(textDocument.uri);\n    if (CancellationToken.is(token)) {\n      return this.createAsync(uri, textDocument, token);\n    } else {\n      return this.create(uri, textDocument, token);\n    }\n  }\n  fromString(text, uri, token) {\n    if (CancellationToken.is(token)) {\n      return this.createAsync(uri, text, token);\n    } else {\n      return this.create(uri, text, token);\n    }\n  }\n  fromModel(model, uri) {\n    return this.create(uri, {\n      $model: model\n    });\n  }\n  create(uri, content, options) {\n    if (typeof content === 'string') {\n      const parseResult = this.parse(uri, content, options);\n      return this.createLangiumDocument(parseResult, uri, undefined, content);\n    } else if ('$model' in content) {\n      const parseResult = {\n        value: content.$model,\n        parserErrors: [],\n        lexerErrors: []\n      };\n      return this.createLangiumDocument(parseResult, uri);\n    } else {\n      const parseResult = this.parse(uri, content.getText(), options);\n      return this.createLangiumDocument(parseResult, uri, content);\n    }\n  }\n  async createAsync(uri, content, cancelToken) {\n    if (typeof content === 'string') {\n      const parseResult = await this.parseAsync(uri, content, cancelToken);\n      return this.createLangiumDocument(parseResult, uri, undefined, content);\n    } else {\n      const parseResult = await this.parseAsync(uri, content.getText(), cancelToken);\n      return this.createLangiumDocument(parseResult, uri, content);\n    }\n  }\n  /**\n   * Create a LangiumDocument from a given parse result.\n   *\n   * A TextDocument is created on demand if it is not provided as argument here. Usually this\n   * should not be necessary because the main purpose of the TextDocument is to convert between\n   * text ranges and offsets, which is done solely in LSP request handling.\n   *\n   * With the introduction of {@link update} below this method is supposed to be mainly called\n   * during workspace initialization and on addition/recognition of new files, while changes in\n   * existing documents are processed via {@link update}.\n   */\n  createLangiumDocument(parseResult, uri, textDocument, text) {\n    let document;\n    if (textDocument) {\n      document = {\n        parseResult,\n        uri,\n        state: DocumentState.Parsed,\n        references: [],\n        textDocument\n      };\n    } else {\n      const textDocumentGetter = this.createTextDocumentGetter(uri, text);\n      document = {\n        parseResult,\n        uri,\n        state: DocumentState.Parsed,\n        references: [],\n        get textDocument() {\n          return textDocumentGetter();\n        }\n      };\n    }\n    parseResult.value.$document = document;\n    return document;\n  }\n  async update(document, cancellationToken) {\n    var _a, _b;\n    // The CST full text property contains the original text that was used to create the AST.\n    const oldText = (_a = document.parseResult.value.$cstNode) === null || _a === void 0 ? void 0 : _a.root.fullText;\n    const textDocument = (_b = this.textDocuments) === null || _b === void 0 ? void 0 : _b.get(document.uri.toString());\n    const text = textDocument ? textDocument.getText() : await this.fileSystemProvider.readFile(document.uri);\n    if (textDocument) {\n      Object.defineProperty(document, 'textDocument', {\n        value: textDocument\n      });\n    } else {\n      const textDocumentGetter = this.createTextDocumentGetter(document.uri, text);\n      Object.defineProperty(document, 'textDocument', {\n        get: textDocumentGetter\n      });\n    }\n    // Some of these documents can be pretty large, so parsing them again can be quite expensive.\n    // Therefore, we only parse if the text has actually changed.\n    if (oldText !== text) {\n      document.parseResult = await this.parseAsync(document.uri, text, cancellationToken);\n      document.parseResult.value.$document = document;\n    }\n    document.state = DocumentState.Parsed;\n    return document;\n  }\n  parse(uri, text, options) {\n    const services = this.serviceRegistry.getServices(uri);\n    return services.parser.LangiumParser.parse(text, options);\n  }\n  parseAsync(uri, text, cancellationToken) {\n    const services = this.serviceRegistry.getServices(uri);\n    return services.parser.AsyncParser.parse(text, cancellationToken);\n  }\n  createTextDocumentGetter(uri, text) {\n    const serviceRegistry = this.serviceRegistry;\n    let textDoc = undefined;\n    return () => {\n      return textDoc !== null && textDoc !== void 0 ? textDoc : textDoc = TextDocument.create(uri.toString(), serviceRegistry.getServices(uri).LanguageMetaData.languageId, 0, text !== null && text !== void 0 ? text : '');\n    };\n  }\n}\nexport class DefaultLangiumDocuments {\n  constructor(services) {\n    this.documentMap = new Map();\n    this.langiumDocumentFactory = services.workspace.LangiumDocumentFactory;\n    this.serviceRegistry = services.ServiceRegistry;\n  }\n  get all() {\n    return stream(this.documentMap.values());\n  }\n  addDocument(document) {\n    const uriString = document.uri.toString();\n    if (this.documentMap.has(uriString)) {\n      throw new Error(`A document with the URI '${uriString}' is already present.`);\n    }\n    this.documentMap.set(uriString, document);\n  }\n  getDocument(uri) {\n    const uriString = uri.toString();\n    return this.documentMap.get(uriString);\n  }\n  async getOrCreateDocument(uri, cancellationToken) {\n    let document = this.getDocument(uri);\n    if (document) {\n      return document;\n    }\n    document = await this.langiumDocumentFactory.fromUri(uri, cancellationToken);\n    this.addDocument(document);\n    return document;\n  }\n  createDocument(uri, text, cancellationToken) {\n    if (cancellationToken) {\n      return this.langiumDocumentFactory.fromString(text, uri, cancellationToken).then(document => {\n        this.addDocument(document);\n        return document;\n      });\n    } else {\n      const document = this.langiumDocumentFactory.fromString(text, uri);\n      this.addDocument(document);\n      return document;\n    }\n  }\n  hasDocument(uri) {\n    return this.documentMap.has(uri.toString());\n  }\n  invalidateDocument(uri) {\n    const uriString = uri.toString();\n    const langiumDoc = this.documentMap.get(uriString);\n    if (langiumDoc) {\n      const linker = this.serviceRegistry.getServices(uri).references.Linker;\n      linker.unlink(langiumDoc);\n      langiumDoc.state = DocumentState.Changed;\n      langiumDoc.precomputedScopes = undefined;\n      langiumDoc.diagnostics = undefined;\n    }\n    return langiumDoc;\n  }\n  deleteDocument(uri) {\n    const uriString = uri.toString();\n    const langiumDoc = this.documentMap.get(uriString);\n    if (langiumDoc) {\n      langiumDoc.state = DocumentState.Changed;\n      this.documentMap.delete(uriString);\n    }\n    return langiumDoc;\n  }\n}", "map": {"version": 3, "names": ["TextDocument", "CancellationToken", "stream", "URI", "DocumentState", "DefaultLangiumDocumentFactory", "constructor", "services", "serviceRegistry", "ServiceRegistry", "textDocuments", "workspace", "TextDocuments", "fileSystemProvider", "FileSystemProvider", "fromUri", "uri", "cancellationToken", "None", "content", "readFile", "createAsync", "fromTextDocument", "textDocument", "token", "parse", "is", "create", "fromString", "text", "fromModel", "model", "$model", "options", "parseResult", "createLangiumDocument", "undefined", "value", "parserErrors", "lexerErrors", "getText", "cancelToken", "parseAsync", "document", "state", "Parsed", "references", "textDocumentGetter", "createTextDocumentGetter", "$document", "update", "oldText", "_a", "$cstNode", "root", "fullText", "_b", "get", "toString", "Object", "defineProperty", "getServices", "parser", "LangiumParser", "As<PERSON><PERSON><PERSON><PERSON>", "textDoc", "LanguageMetaData", "languageId", "DefaultLangiumDocuments", "documentMap", "Map", "langiumDocumentFactory", "LangiumDocumentFactory", "all", "values", "addDocument", "uriString", "has", "Error", "set", "getDocument", "getOrCreateDocument", "createDocument", "then", "hasDocument", "invalidateDocument", "langiumDoc", "linker", "<PERSON><PERSON>", "unlink", "Changed", "precomputedScopes", "diagnostics", "deleteDocument", "delete"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/documents.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/**\r\n * Re-export 'TextDocument' from 'vscode-languageserver-textdocument' for convenience,\r\n *  including both type _and_ symbol (namespace), as we here and there also refer to the symbol,\r\n *  the overhead is very small, just a few kilobytes.\r\n * Everything else of that package (at the time contributing) is also defined\r\n *  in 'vscode-languageserver-protocol' or 'vscode-languageserver-types'.\r\n */\r\nexport { TextDocument } from 'vscode-languageserver-textdocument';\r\n\r\nimport type { Diagnostic, Range } from 'vscode-languageserver-types';\r\nimport type { FileSystemProvider } from './file-system-provider.js';\r\nimport type { ParseResult, ParserOptions } from '../parser/langium-parser.js';\r\nimport type { ServiceRegistry } from '../service-registry.js';\r\nimport type { LangiumSharedCoreServices } from '../services.js';\r\nimport type { AstNode, AstNodeDescription, Mutable, Reference } from '../syntax-tree.js';\r\nimport type { MultiMap } from '../utils/collections.js';\r\nimport type { Stream } from '../utils/stream.js';\r\nimport { TextDocument } from './documents.js';\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { stream } from '../utils/stream.js';\r\nimport { URI } from '../utils/uri-utils.js';\r\n\r\n/**\r\n * A Langium document holds the parse result (AST and CST) and any additional state that is derived\r\n * from the AST, e.g. the result of scope precomputation.\r\n */\r\nexport interface LangiumDocument<T extends AstNode = AstNode> {\r\n    /** The Uniform Resource Identifier (URI) of the document */\r\n    readonly uri: URI;\r\n    /** The text document used to convert between offsets and positions */\r\n    readonly textDocument: TextDocument;\r\n    /** The current state of the document */\r\n    state: DocumentState;\r\n    /** The parse result holds the Abstract Syntax Tree (AST) and potentially also parser / lexer errors */\r\n    parseResult: ParseResult<T>;\r\n    /** Result of the scope precomputation phase */\r\n    precomputedScopes?: PrecomputedScopes;\r\n    /** An array of all cross-references found in the AST while linking */\r\n    references: Reference[];\r\n    /** Result of the validation phase */\r\n    diagnostics?: Diagnostic[]\r\n}\r\n\r\n/**\r\n * A document is subject to several phases that are run in predefined order. Any state value implies that\r\n * smaller state values are finished as well.\r\n */\r\nexport enum DocumentState {\r\n    /**\r\n     * The text content has changed and needs to be parsed again. The AST held by this outdated\r\n     * document instance is no longer valid.\r\n     */\r\n    Changed = 0,\r\n    /**\r\n     * An AST has been created from the text content. The document structure can be traversed,\r\n     * but cross-references cannot be resolved yet. If necessary, the structure can be manipulated\r\n     * at this stage as a preprocessing step.\r\n     */\r\n    Parsed = 1,\r\n    /**\r\n     * The `IndexManager` service has processed AST nodes of this document. This means the\r\n     * exported symbols are available in the global scope and can be resolved from other documents.\r\n     */\r\n    IndexedContent = 2,\r\n    /**\r\n     * The `ScopeComputation` service has processed this document. This means the local symbols\r\n     * are stored in a MultiMap so they can be looked up by the `ScopeProvider` service.\r\n     * Once a document has reached this state, you may follow every reference - it will lazily\r\n     * resolve its `ref` property and yield either the target AST node or `undefined` in case\r\n     * the target is not in scope.\r\n     */\r\n    ComputedScopes = 3,\r\n    /**\r\n     * The `Linker` service has processed this document. All outgoing references have been\r\n     * resolved or marked as erroneous.\r\n     */\r\n    Linked = 4,\r\n    /**\r\n     * The `IndexManager` service has processed AST node references of this document. This is\r\n     * necessary to determine which documents are affected by a change in one of the workspace\r\n     * documents.\r\n     */\r\n    IndexedReferences = 5,\r\n    /**\r\n     * The `DocumentValidator` service has processed this document. The language server listens\r\n     * to the results of this phase and sends diagnostics to the client.\r\n     */\r\n    Validated = 6\r\n}\r\n\r\n/**\r\n * Result of the scope precomputation phase (`ScopeComputation` service).\r\n * It maps every AST node to the set of symbols that are visible in the subtree of that node.\r\n */\r\nexport type PrecomputedScopes = MultiMap<AstNode, AstNodeDescription>\r\n\r\nexport interface DocumentSegment {\r\n    readonly range: Range\r\n    readonly offset: number\r\n    readonly length: number\r\n    readonly end: number\r\n}\r\n\r\n/**\r\n * Surrogate definition of the `TextDocuments` interface from the `vscode-languageserver` package.\r\n * No implementation object is expected to be offered by `LangiumCoreServices`, but only by `LangiumLSPServices`.\r\n */\r\nexport type TextDocumentProvider = {\r\n    get(uri: string | URI): TextDocument | undefined\r\n}\r\n\r\n/**\r\n * Shared service for creating `LangiumDocument` instances.\r\n *\r\n * Register a custom implementation if special (additional) behavior is required for your language(s).\r\n * Note: If you specialize {@link fromString} or {@link fromTextDocument} you probably might want to\r\n * specialize {@link update}, too!\r\n */\r\nexport interface LangiumDocumentFactory {\r\n    /**\r\n     * Create a Langium document from a `TextDocument` (usually associated with a file).\r\n     */\r\n    fromTextDocument<T extends AstNode = AstNode>(textDocument: TextDocument, uri?: URI, options?: ParserOptions): LangiumDocument<T>;\r\n    /**\r\n     * Create a Langium document from a `TextDocument` asynchronously. This action can be cancelled if a cancellable parser implementation has been provided.\r\n     */\r\n    fromTextDocument<T extends AstNode = AstNode>(textDocument: TextDocument, uri: URI | undefined, cancellationToken: CancellationToken): Promise<LangiumDocument<T>>;\r\n\r\n    /**\r\n     * Create an Langium document from an in-memory string.\r\n     */\r\n    fromString<T extends AstNode = AstNode>(text: string, uri: URI, options?: ParserOptions): LangiumDocument<T>;\r\n    /**\r\n     * Create a Langium document from an in-memory string asynchronously. This action can be cancelled if a cancellable parser implementation has been provided.\r\n     */\r\n    fromString<T extends AstNode = AstNode>(text: string, uri: URI, cancellationToken: CancellationToken): Promise<LangiumDocument<T>>;\r\n\r\n    /**\r\n     * Create an Langium document from a model that has been constructed in memory.\r\n     */\r\n    fromModel<T extends AstNode = AstNode>(model: T, uri: URI): LangiumDocument<T>;\r\n\r\n    /**\r\n     * Create an Langium document from a specified `URI`. The factory will use the `FileSystemAccess` service to read the file.\r\n     */\r\n    fromUri<T extends AstNode = AstNode>(uri: URI, cancellationToken?: CancellationToken): Promise<LangiumDocument<T>>;\r\n\r\n    /**\r\n     * Update the given document after changes in the corresponding textual representation.\r\n     * Method is called by the document builder after it has been requested to build an existing\r\n     * document and the document's state is {@link DocumentState.Changed}.\r\n     * The text parsing is expected to be done the same way as in {@link fromTextDocument}\r\n     * and {@link fromString}.\r\n     */\r\n    update<T extends AstNode = AstNode>(document: LangiumDocument<T>, cancellationToken: CancellationToken): Promise<LangiumDocument<T>>\r\n}\r\n\r\nexport class DefaultLangiumDocumentFactory implements LangiumDocumentFactory {\r\n\r\n    protected readonly serviceRegistry: ServiceRegistry;\r\n    protected readonly textDocuments?: TextDocumentProvider;\r\n    protected readonly fileSystemProvider: FileSystemProvider;\r\n\r\n    constructor(services: LangiumSharedCoreServices) {\r\n        this.serviceRegistry = services.ServiceRegistry;\r\n        this.textDocuments = services.workspace.TextDocuments;\r\n        this.fileSystemProvider = services.workspace.FileSystemProvider;\r\n    }\r\n\r\n    async fromUri<T extends AstNode = AstNode>(uri: URI, cancellationToken = CancellationToken.None): Promise<LangiumDocument<T>> {\r\n        const content = await this.fileSystemProvider.readFile(uri);\r\n        return this.createAsync<T>(uri, content, cancellationToken);\r\n    }\r\n\r\n    fromTextDocument<T extends AstNode = AstNode>(textDocument: TextDocument, uri?: URI, options?: ParserOptions): LangiumDocument<T>;\r\n    fromTextDocument<T extends AstNode = AstNode>(textDocument: TextDocument, uri: URI | undefined, cancellationToken: CancellationToken): Promise<LangiumDocument<T>>;\r\n    fromTextDocument<T extends AstNode = AstNode>(textDocument: TextDocument, uri?: URI, token?: CancellationToken | ParserOptions): LangiumDocument<T> | Promise<LangiumDocument<T>> {\r\n        uri = uri ?? URI.parse(textDocument.uri);\r\n        if (CancellationToken.is(token)) {\r\n            return this.createAsync<T>(uri, textDocument, token);\r\n        } else {\r\n            return this.create<T>(uri, textDocument, token);\r\n        }\r\n    }\r\n\r\n    fromString<T extends AstNode = AstNode>(text: string, uri: URI, options?: ParserOptions): LangiumDocument<T>;\r\n    fromString<T extends AstNode = AstNode>(text: string, uri: URI, cancellationToken: CancellationToken): Promise<LangiumDocument<T>>;\r\n    fromString<T extends AstNode = AstNode>(text: string, uri: URI, token?: CancellationToken | ParserOptions): LangiumDocument<T> | Promise<LangiumDocument<T>> {\r\n        if (CancellationToken.is(token)) {\r\n            return this.createAsync<T>(uri, text, token);\r\n        } else {\r\n            return this.create<T>(uri, text, token);\r\n        }\r\n    }\r\n\r\n    fromModel<T extends AstNode = AstNode>(model: T, uri: URI): LangiumDocument<T> {\r\n        return this.create<T>(uri, { $model: model });\r\n    }\r\n\r\n    protected create<T extends AstNode = AstNode>(uri: URI, content: string | TextDocument | { $model: T }, options?: ParserOptions): LangiumDocument<T> {\r\n        if (typeof content === 'string') {\r\n            const parseResult = this.parse<T>(uri, content, options);\r\n            return this.createLangiumDocument<T>(parseResult, uri, undefined, content);\r\n\r\n        } else if ('$model' in content) {\r\n            const parseResult = { value: content.$model, parserErrors: [], lexerErrors: [] };\r\n            return this.createLangiumDocument<T>(parseResult, uri);\r\n\r\n        } else {\r\n            const parseResult = this.parse<T>(uri, content.getText(), options);\r\n            return this.createLangiumDocument(parseResult, uri, content);\r\n        }\r\n    }\r\n\r\n    protected async createAsync<T extends AstNode = AstNode>(uri: URI, content: string | TextDocument, cancelToken: CancellationToken): Promise<LangiumDocument<T>> {\r\n        if (typeof content === 'string') {\r\n            const parseResult = await this.parseAsync<T>(uri, content, cancelToken);\r\n            return this.createLangiumDocument<T>(parseResult, uri, undefined, content);\r\n        } else {\r\n            const parseResult = await this.parseAsync<T>(uri, content.getText(), cancelToken);\r\n            return this.createLangiumDocument(parseResult, uri, content);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a LangiumDocument from a given parse result.\r\n     *\r\n     * A TextDocument is created on demand if it is not provided as argument here. Usually this\r\n     * should not be necessary because the main purpose of the TextDocument is to convert between\r\n     * text ranges and offsets, which is done solely in LSP request handling.\r\n     *\r\n     * With the introduction of {@link update} below this method is supposed to be mainly called\r\n     * during workspace initialization and on addition/recognition of new files, while changes in\r\n     * existing documents are processed via {@link update}.\r\n     */\r\n    protected createLangiumDocument<T extends AstNode = AstNode>(parseResult: ParseResult<T>, uri: URI, textDocument?: TextDocument, text?: string): LangiumDocument<T> {\r\n        let document: LangiumDocument<T>;\r\n        if (textDocument) {\r\n            document = {\r\n                parseResult,\r\n                uri,\r\n                state: DocumentState.Parsed,\r\n                references: [],\r\n                textDocument\r\n            };\r\n        } else {\r\n            const textDocumentGetter = this.createTextDocumentGetter(uri, text);\r\n            document = {\r\n                parseResult,\r\n                uri,\r\n                state: DocumentState.Parsed,\r\n                references: [],\r\n                get textDocument() {\r\n                    return textDocumentGetter();\r\n                }\r\n            };\r\n        }\r\n        (parseResult.value as Mutable<AstNode>).$document = document;\r\n        return document;\r\n    }\r\n\r\n    async update<T extends AstNode = AstNode>(document: Mutable<LangiumDocument<T>>, cancellationToken: CancellationToken): Promise<LangiumDocument<T>> {\r\n        // The CST full text property contains the original text that was used to create the AST.\r\n        const oldText = document.parseResult.value.$cstNode?.root.fullText;\r\n        const textDocument = this.textDocuments?.get(document.uri.toString());\r\n        const text = textDocument ? textDocument.getText() : await this.fileSystemProvider.readFile(document.uri);\r\n\r\n        if (textDocument) {\r\n            Object.defineProperty(\r\n                document,\r\n                'textDocument',\r\n                {\r\n                    value: textDocument\r\n                }\r\n            );\r\n        } else {\r\n            const textDocumentGetter = this.createTextDocumentGetter(document.uri, text);\r\n            Object.defineProperty(\r\n                document,\r\n                'textDocument',\r\n                {\r\n                    get: textDocumentGetter\r\n                }\r\n            );\r\n        }\r\n\r\n        // Some of these documents can be pretty large, so parsing them again can be quite expensive.\r\n        // Therefore, we only parse if the text has actually changed.\r\n        if (oldText !== text) {\r\n            document.parseResult = await this.parseAsync(document.uri, text, cancellationToken);\r\n            (document.parseResult.value as Mutable<AstNode>).$document = document;\r\n        }\r\n        document.state = DocumentState.Parsed;\r\n        return document;\r\n    }\r\n\r\n    protected parse<T extends AstNode>(uri: URI, text: string, options?: ParserOptions): ParseResult<T> {\r\n        const services = this.serviceRegistry.getServices(uri);\r\n        return services.parser.LangiumParser.parse<T>(text, options);\r\n    }\r\n\r\n    protected parseAsync<T extends AstNode>(uri: URI, text: string, cancellationToken: CancellationToken): Promise<ParseResult<T>> {\r\n        const services = this.serviceRegistry.getServices(uri);\r\n        return services.parser.AsyncParser.parse<T>(text, cancellationToken);\r\n    }\r\n\r\n    protected createTextDocumentGetter(uri: URI, text?: string): () => TextDocument {\r\n        const serviceRegistry = this.serviceRegistry;\r\n        let textDoc: TextDocument | undefined = undefined;\r\n        return () => {\r\n            return textDoc ??= TextDocument.create(\r\n                uri.toString(), serviceRegistry.getServices(uri).LanguageMetaData.languageId, 0, text ?? ''\r\n            );\r\n        };\r\n    }\r\n}\r\n\r\n/**\r\n * Shared service for managing Langium documents.\r\n */\r\nexport interface LangiumDocuments {\r\n\r\n    /**\r\n     * A stream of all documents managed under this service.\r\n     */\r\n    readonly all: Stream<LangiumDocument>\r\n\r\n    /**\r\n     * Manage a new document under this service.\r\n     * @throws an error if a document with the same URI is already present.\r\n     */\r\n    addDocument(document: LangiumDocument): void;\r\n\r\n    /**\r\n     * Retrieve the document with the given URI, if present. Otherwise returns `undefined`.\r\n     */\r\n    getDocument(uri: URI): LangiumDocument | undefined;\r\n\r\n    /**\r\n     * Retrieve the document with the given URI. If not present, a new one will be created using the file system access.\r\n     * The new document will be added to the list of documents managed under this service.\r\n     */\r\n    getOrCreateDocument(uri: URI, cancellationToken?: CancellationToken): Promise<LangiumDocument>;\r\n\r\n    /**\r\n     * Creates a new document with the given URI and text content.\r\n     * The new document is automatically added to this service and can be retrieved using {@link getDocument}.\r\n     *\r\n     * @throws an error if a document with the same URI is already present.\r\n     */\r\n    createDocument(uri: URI, text: string): LangiumDocument;\r\n\r\n    /**\r\n     * Creates a new document with the given URI and text content asynchronously.\r\n     * The process can be interrupted with a cancellation token.\r\n     * The new document is automatically added to this service and can be retrieved using {@link getDocument}.\r\n     *\r\n     * @throws an error if a document with the same URI is already present.\r\n     */\r\n    createDocument(uri: URI, text: string, cancellationToken: CancellationToken): Promise<LangiumDocument>;\r\n\r\n    /**\r\n     * Returns `true` if a document with the given URI is managed under this service.\r\n     */\r\n    hasDocument(uri: URI): boolean;\r\n\r\n    /**\r\n     * Flag the document with the given URI as `Changed`, if present, meaning that its content\r\n     * is no longer valid. The content (parseResult) stays untouched, while internal data may\r\n     * be dropped to reduce memory footprint.\r\n     *\r\n     * @returns the affected {@link LangiumDocument} if existing for convenience\r\n     */\r\n    invalidateDocument(uri: URI): LangiumDocument | undefined;\r\n\r\n    /**\r\n     * Remove the document with the given URI, if present, and mark it as `Changed`, meaning\r\n     * that its content is no longer valid. The next call to `getOrCreateDocument` with the same\r\n     * URI will create a new document instance.\r\n     *\r\n     * @returns the affected {@link LangiumDocument} if existing for convenience\r\n     */\r\n    deleteDocument(uri: URI): LangiumDocument | undefined;\r\n}\r\n\r\nexport class DefaultLangiumDocuments implements LangiumDocuments {\r\n\r\n    protected readonly langiumDocumentFactory: LangiumDocumentFactory;\r\n    protected readonly serviceRegistry: ServiceRegistry;\r\n\r\n    protected readonly documentMap: Map<string, LangiumDocument> = new Map();\r\n\r\n    constructor(services: LangiumSharedCoreServices) {\r\n        this.langiumDocumentFactory = services.workspace.LangiumDocumentFactory;\r\n        this.serviceRegistry = services.ServiceRegistry;\r\n    }\r\n\r\n    get all(): Stream<LangiumDocument> {\r\n        return stream(this.documentMap.values());\r\n    }\r\n\r\n    addDocument(document: LangiumDocument): void {\r\n        const uriString = document.uri.toString();\r\n        if (this.documentMap.has(uriString)) {\r\n            throw new Error(`A document with the URI '${uriString}' is already present.`);\r\n        }\r\n        this.documentMap.set(uriString, document);\r\n    }\r\n\r\n    getDocument(uri: URI): LangiumDocument | undefined {\r\n        const uriString = uri.toString();\r\n        return this.documentMap.get(uriString);\r\n    }\r\n\r\n    async getOrCreateDocument(uri: URI, cancellationToken?: CancellationToken): Promise<LangiumDocument> {\r\n        let document = this.getDocument(uri);\r\n        if (document) {\r\n            return document;\r\n        }\r\n        document = await this.langiumDocumentFactory.fromUri(uri, cancellationToken);\r\n        this.addDocument(document);\r\n        return document;\r\n    }\r\n\r\n    createDocument(uri: URI, text: string): LangiumDocument;\r\n    createDocument(uri: URI, text: string, cancellationToken: CancellationToken): Promise<LangiumDocument>;\r\n    createDocument(uri: URI, text: string, cancellationToken?: CancellationToken): LangiumDocument | Promise<LangiumDocument> {\r\n        if (cancellationToken) {\r\n            return this.langiumDocumentFactory.fromString(text, uri, cancellationToken).then(document => {\r\n                this.addDocument(document);\r\n                return document;\r\n            });\r\n        } else {\r\n            const document = this.langiumDocumentFactory.fromString(text, uri);\r\n            this.addDocument(document);\r\n            return document;\r\n        }\r\n    }\r\n\r\n    hasDocument(uri: URI): boolean {\r\n        return this.documentMap.has(uri.toString());\r\n    }\r\n\r\n    invalidateDocument(uri: URI): LangiumDocument | undefined {\r\n        const uriString = uri.toString();\r\n        const langiumDoc = this.documentMap.get(uriString);\r\n        if (langiumDoc) {\r\n            const linker = this.serviceRegistry.getServices(uri).references.Linker;\r\n            linker.unlink(langiumDoc);\r\n            langiumDoc.state = DocumentState.Changed;\r\n            langiumDoc.precomputedScopes = undefined;\r\n            langiumDoc.diagnostics = undefined;\r\n        }\r\n        return langiumDoc;\r\n    }\r\n\r\n    deleteDocument(uri: URI): LangiumDocument | undefined {\r\n        const uriString = uri.toString();\r\n        const langiumDoc = this.documentMap.get(uriString);\r\n        if (langiumDoc) {\r\n            langiumDoc.state = DocumentState.Changed;\r\n            this.documentMap.delete(uriString);\r\n        }\r\n        return langiumDoc;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA;;;;;;;AAOA,SAASA,YAAY,QAAQ,oCAAoC;AAUjE,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,GAAG,QAAQ,uBAAuB;AAuB3C;;;;AAIA,WAAYC,aAyCX;AAzCD,WAAYA,aAAa;EACrB;;;;EAIAA,aAAA,CAAAA,aAAA,4BAAW;EACX;;;;;EAKAA,aAAA,CAAAA,aAAA,0BAAU;EACV;;;;EAIAA,aAAA,CAAAA,aAAA,0CAAkB;EAClB;;;;;;;EAOAA,aAAA,CAAAA,aAAA,0CAAkB;EAClB;;;;EAIAA,aAAA,CAAAA,aAAA,0BAAU;EACV;;;;;EAKAA,aAAA,CAAAA,aAAA,gDAAqB;EACrB;;;;EAIAA,aAAA,CAAAA,aAAA,gCAAa;AACjB,CAAC,EAzCWA,aAAa,KAAbA,aAAa;AA8GzB,OAAM,MAAOC,6BAA6B;EAMtCC,YAAYC,QAAmC;IAC3C,IAAI,CAACC,eAAe,GAAGD,QAAQ,CAACE,eAAe;IAC/C,IAAI,CAACC,aAAa,GAAGH,QAAQ,CAACI,SAAS,CAACC,aAAa;IACrD,IAAI,CAACC,kBAAkB,GAAGN,QAAQ,CAACI,SAAS,CAACG,kBAAkB;EACnE;EAEA,MAAMC,OAAOA,CAA8BC,GAAQ,EAAEC,iBAAiB,GAAGhB,iBAAiB,CAACiB,IAAI;IAC3F,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACN,kBAAkB,CAACO,QAAQ,CAACJ,GAAG,CAAC;IAC3D,OAAO,IAAI,CAACK,WAAW,CAAIL,GAAG,EAAEG,OAAO,EAAEF,iBAAiB,CAAC;EAC/D;EAIAK,gBAAgBA,CAA8BC,YAA0B,EAAEP,GAAS,EAAEQ,KAAyC;IAC1HR,GAAG,GAAGA,GAAG,aAAHA,GAAG,cAAHA,GAAG,GAAIb,GAAG,CAACsB,KAAK,CAACF,YAAY,CAACP,GAAG,CAAC;IACxC,IAAIf,iBAAiB,CAACyB,EAAE,CAACF,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACH,WAAW,CAAIL,GAAG,EAAEO,YAAY,EAAEC,KAAK,CAAC;IACxD,CAAC,MAAM;MACH,OAAO,IAAI,CAACG,MAAM,CAAIX,GAAG,EAAEO,YAAY,EAAEC,KAAK,CAAC;IACnD;EACJ;EAIAI,UAAUA,CAA8BC,IAAY,EAAEb,GAAQ,EAAEQ,KAAyC;IACrG,IAAIvB,iBAAiB,CAACyB,EAAE,CAACF,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACH,WAAW,CAAIL,GAAG,EAAEa,IAAI,EAAEL,KAAK,CAAC;IAChD,CAAC,MAAM;MACH,OAAO,IAAI,CAACG,MAAM,CAAIX,GAAG,EAAEa,IAAI,EAAEL,KAAK,CAAC;IAC3C;EACJ;EAEAM,SAASA,CAA8BC,KAAQ,EAAEf,GAAQ;IACrD,OAAO,IAAI,CAACW,MAAM,CAAIX,GAAG,EAAE;MAAEgB,MAAM,EAAED;IAAK,CAAE,CAAC;EACjD;EAEUJ,MAAMA,CAA8BX,GAAQ,EAAEG,OAA8C,EAAEc,OAAuB;IAC3H,IAAI,OAAOd,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMe,WAAW,GAAG,IAAI,CAACT,KAAK,CAAIT,GAAG,EAAEG,OAAO,EAAEc,OAAO,CAAC;MACxD,OAAO,IAAI,CAACE,qBAAqB,CAAID,WAAW,EAAElB,GAAG,EAAEoB,SAAS,EAAEjB,OAAO,CAAC;IAE9E,CAAC,MAAM,IAAI,QAAQ,IAAIA,OAAO,EAAE;MAC5B,MAAMe,WAAW,GAAG;QAAEG,KAAK,EAAElB,OAAO,CAACa,MAAM;QAAEM,YAAY,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAE,CAAE;MAChF,OAAO,IAAI,CAACJ,qBAAqB,CAAID,WAAW,EAAElB,GAAG,CAAC;IAE1D,CAAC,MAAM;MACH,MAAMkB,WAAW,GAAG,IAAI,CAACT,KAAK,CAAIT,GAAG,EAAEG,OAAO,CAACqB,OAAO,EAAE,EAAEP,OAAO,CAAC;MAClE,OAAO,IAAI,CAACE,qBAAqB,CAACD,WAAW,EAAElB,GAAG,EAAEG,OAAO,CAAC;IAChE;EACJ;EAEU,MAAME,WAAWA,CAA8BL,GAAQ,EAAEG,OAA8B,EAAEsB,WAA8B;IAC7H,IAAI,OAAOtB,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMe,WAAW,GAAG,MAAM,IAAI,CAACQ,UAAU,CAAI1B,GAAG,EAAEG,OAAO,EAAEsB,WAAW,CAAC;MACvE,OAAO,IAAI,CAACN,qBAAqB,CAAID,WAAW,EAAElB,GAAG,EAAEoB,SAAS,EAAEjB,OAAO,CAAC;IAC9E,CAAC,MAAM;MACH,MAAMe,WAAW,GAAG,MAAM,IAAI,CAACQ,UAAU,CAAI1B,GAAG,EAAEG,OAAO,CAACqB,OAAO,EAAE,EAAEC,WAAW,CAAC;MACjF,OAAO,IAAI,CAACN,qBAAqB,CAACD,WAAW,EAAElB,GAAG,EAAEG,OAAO,CAAC;IAChE;EACJ;EAEA;;;;;;;;;;;EAWUgB,qBAAqBA,CAA8BD,WAA2B,EAAElB,GAAQ,EAAEO,YAA2B,EAAEM,IAAa;IAC1I,IAAIc,QAA4B;IAChC,IAAIpB,YAAY,EAAE;MACdoB,QAAQ,GAAG;QACPT,WAAW;QACXlB,GAAG;QACH4B,KAAK,EAAExC,aAAa,CAACyC,MAAM;QAC3BC,UAAU,EAAE,EAAE;QACdvB;OACH;IACL,CAAC,MAAM;MACH,MAAMwB,kBAAkB,GAAG,IAAI,CAACC,wBAAwB,CAAChC,GAAG,EAAEa,IAAI,CAAC;MACnEc,QAAQ,GAAG;QACPT,WAAW;QACXlB,GAAG;QACH4B,KAAK,EAAExC,aAAa,CAACyC,MAAM;QAC3BC,UAAU,EAAE,EAAE;QACd,IAAIvB,YAAYA,CAAA;UACZ,OAAOwB,kBAAkB,EAAE;QAC/B;OACH;IACL;IACCb,WAAW,CAACG,KAA0B,CAACY,SAAS,GAAGN,QAAQ;IAC5D,OAAOA,QAAQ;EACnB;EAEA,MAAMO,MAAMA,CAA8BP,QAAqC,EAAE1B,iBAAoC;;IACjH;IACA,MAAMkC,OAAO,GAAG,CAAAC,EAAA,GAAAT,QAAQ,CAACT,WAAW,CAACG,KAAK,CAACgB,QAAQ,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI,CAACC,QAAQ;IAClE,MAAMhC,YAAY,GAAG,CAAAiC,EAAA,OAAI,CAAC9C,aAAa,cAAA8C,EAAA,uBAAAA,EAAA,CAAEC,GAAG,CAACd,QAAQ,CAAC3B,GAAG,CAAC0C,QAAQ,EAAE,CAAC;IACrE,MAAM7B,IAAI,GAAGN,YAAY,GAAGA,YAAY,CAACiB,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC3B,kBAAkB,CAACO,QAAQ,CAACuB,QAAQ,CAAC3B,GAAG,CAAC;IAEzG,IAAIO,YAAY,EAAE;MACdoC,MAAM,CAACC,cAAc,CACjBjB,QAAQ,EACR,cAAc,EACd;QACIN,KAAK,EAAEd;OACV,CACJ;IACL,CAAC,MAAM;MACH,MAAMwB,kBAAkB,GAAG,IAAI,CAACC,wBAAwB,CAACL,QAAQ,CAAC3B,GAAG,EAAEa,IAAI,CAAC;MAC5E8B,MAAM,CAACC,cAAc,CACjBjB,QAAQ,EACR,cAAc,EACd;QACIc,GAAG,EAAEV;OACR,CACJ;IACL;IAEA;IACA;IACA,IAAII,OAAO,KAAKtB,IAAI,EAAE;MAClBc,QAAQ,CAACT,WAAW,GAAG,MAAM,IAAI,CAACQ,UAAU,CAACC,QAAQ,CAAC3B,GAAG,EAAEa,IAAI,EAAEZ,iBAAiB,CAAC;MAClF0B,QAAQ,CAACT,WAAW,CAACG,KAA0B,CAACY,SAAS,GAAGN,QAAQ;IACzE;IACAA,QAAQ,CAACC,KAAK,GAAGxC,aAAa,CAACyC,MAAM;IACrC,OAAOF,QAAQ;EACnB;EAEUlB,KAAKA,CAAoBT,GAAQ,EAAEa,IAAY,EAAEI,OAAuB;IAC9E,MAAM1B,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACqD,WAAW,CAAC7C,GAAG,CAAC;IACtD,OAAOT,QAAQ,CAACuD,MAAM,CAACC,aAAa,CAACtC,KAAK,CAAII,IAAI,EAAEI,OAAO,CAAC;EAChE;EAEUS,UAAUA,CAAoB1B,GAAQ,EAAEa,IAAY,EAAEZ,iBAAoC;IAChG,MAAMV,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACqD,WAAW,CAAC7C,GAAG,CAAC;IACtD,OAAOT,QAAQ,CAACuD,MAAM,CAACE,WAAW,CAACvC,KAAK,CAAII,IAAI,EAAEZ,iBAAiB,CAAC;EACxE;EAEU+B,wBAAwBA,CAAChC,GAAQ,EAAEa,IAAa;IACtD,MAAMrB,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,IAAIyD,OAAO,GAA6B7B,SAAS;IACjD,OAAO,MAAK;MACR,OAAO6B,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAPA,OAAO,GAAKjE,YAAY,CAAC2B,MAAM,CAClCX,GAAG,CAAC0C,QAAQ,EAAE,EAAElD,eAAe,CAACqD,WAAW,CAAC7C,GAAG,CAAC,CAACkD,gBAAgB,CAACC,UAAU,EAAE,CAAC,EAAEtC,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAC9F;IACL,CAAC;EACL;;AAuEJ,OAAM,MAAOuC,uBAAuB;EAOhC9D,YAAYC,QAAmC;IAF5B,KAAA8D,WAAW,GAAiC,IAAIC,GAAG,EAAE;IAGpE,IAAI,CAACC,sBAAsB,GAAGhE,QAAQ,CAACI,SAAS,CAAC6D,sBAAsB;IACvE,IAAI,CAAChE,eAAe,GAAGD,QAAQ,CAACE,eAAe;EACnD;EAEA,IAAIgE,GAAGA,CAAA;IACH,OAAOvE,MAAM,CAAC,IAAI,CAACmE,WAAW,CAACK,MAAM,EAAE,CAAC;EAC5C;EAEAC,WAAWA,CAAChC,QAAyB;IACjC,MAAMiC,SAAS,GAAGjC,QAAQ,CAAC3B,GAAG,CAAC0C,QAAQ,EAAE;IACzC,IAAI,IAAI,CAACW,WAAW,CAACQ,GAAG,CAACD,SAAS,CAAC,EAAE;MACjC,MAAM,IAAIE,KAAK,CAAC,4BAA4BF,SAAS,uBAAuB,CAAC;IACjF;IACA,IAAI,CAACP,WAAW,CAACU,GAAG,CAACH,SAAS,EAAEjC,QAAQ,CAAC;EAC7C;EAEAqC,WAAWA,CAAChE,GAAQ;IAChB,MAAM4D,SAAS,GAAG5D,GAAG,CAAC0C,QAAQ,EAAE;IAChC,OAAO,IAAI,CAACW,WAAW,CAACZ,GAAG,CAACmB,SAAS,CAAC;EAC1C;EAEA,MAAMK,mBAAmBA,CAACjE,GAAQ,EAAEC,iBAAqC;IACrE,IAAI0B,QAAQ,GAAG,IAAI,CAACqC,WAAW,CAAChE,GAAG,CAAC;IACpC,IAAI2B,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB;IACAA,QAAQ,GAAG,MAAM,IAAI,CAAC4B,sBAAsB,CAACxD,OAAO,CAACC,GAAG,EAAEC,iBAAiB,CAAC;IAC5E,IAAI,CAAC0D,WAAW,CAAChC,QAAQ,CAAC;IAC1B,OAAOA,QAAQ;EACnB;EAIAuC,cAAcA,CAAClE,GAAQ,EAAEa,IAAY,EAAEZ,iBAAqC;IACxE,IAAIA,iBAAiB,EAAE;MACnB,OAAO,IAAI,CAACsD,sBAAsB,CAAC3C,UAAU,CAACC,IAAI,EAAEb,GAAG,EAAEC,iBAAiB,CAAC,CAACkE,IAAI,CAACxC,QAAQ,IAAG;QACxF,IAAI,CAACgC,WAAW,CAAChC,QAAQ,CAAC;QAC1B,OAAOA,QAAQ;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACH,MAAMA,QAAQ,GAAG,IAAI,CAAC4B,sBAAsB,CAAC3C,UAAU,CAACC,IAAI,EAAEb,GAAG,CAAC;MAClE,IAAI,CAAC2D,WAAW,CAAChC,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACnB;EACJ;EAEAyC,WAAWA,CAACpE,GAAQ;IAChB,OAAO,IAAI,CAACqD,WAAW,CAACQ,GAAG,CAAC7D,GAAG,CAAC0C,QAAQ,EAAE,CAAC;EAC/C;EAEA2B,kBAAkBA,CAACrE,GAAQ;IACvB,MAAM4D,SAAS,GAAG5D,GAAG,CAAC0C,QAAQ,EAAE;IAChC,MAAM4B,UAAU,GAAG,IAAI,CAACjB,WAAW,CAACZ,GAAG,CAACmB,SAAS,CAAC;IAClD,IAAIU,UAAU,EAAE;MACZ,MAAMC,MAAM,GAAG,IAAI,CAAC/E,eAAe,CAACqD,WAAW,CAAC7C,GAAG,CAAC,CAAC8B,UAAU,CAAC0C,MAAM;MACtED,MAAM,CAACE,MAAM,CAACH,UAAU,CAAC;MACzBA,UAAU,CAAC1C,KAAK,GAAGxC,aAAa,CAACsF,OAAO;MACxCJ,UAAU,CAACK,iBAAiB,GAAGvD,SAAS;MACxCkD,UAAU,CAACM,WAAW,GAAGxD,SAAS;IACtC;IACA,OAAOkD,UAAU;EACrB;EAEAO,cAAcA,CAAC7E,GAAQ;IACnB,MAAM4D,SAAS,GAAG5D,GAAG,CAAC0C,QAAQ,EAAE;IAChC,MAAM4B,UAAU,GAAG,IAAI,CAACjB,WAAW,CAACZ,GAAG,CAACmB,SAAS,CAAC;IAClD,IAAIU,UAAU,EAAE;MACZA,UAAU,CAAC1C,KAAK,GAAGxC,aAAa,CAACsF,OAAO;MACxC,IAAI,CAACrB,WAAW,CAACyB,MAAM,CAAClB,SAAS,CAAC;IACtC;IACA,OAAOU,UAAU;EACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}