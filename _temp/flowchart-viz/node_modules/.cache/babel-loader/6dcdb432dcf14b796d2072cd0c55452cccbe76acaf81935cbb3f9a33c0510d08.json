{"ast": null, "code": "\"use strict\";\n\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Emitter = exports.Event = void 0;\nconst ral_1 = require(\"./ral\");\nvar Event;\n(function (Event) {\n  const _disposable = {\n    dispose() {}\n  };\n  Event.None = function () {\n    return _disposable;\n  };\n})(Event || (exports.Event = Event = {}));\nclass CallbackList {\n  add(callback, context = null, bucket) {\n    if (!this._callbacks) {\n      this._callbacks = [];\n      this._contexts = [];\n    }\n    this._callbacks.push(callback);\n    this._contexts.push(context);\n    if (Array.isArray(bucket)) {\n      bucket.push({\n        dispose: () => this.remove(callback, context)\n      });\n    }\n  }\n  remove(callback, context = null) {\n    if (!this._callbacks) {\n      return;\n    }\n    let foundCallbackWithDifferentContext = false;\n    for (let i = 0, len = this._callbacks.length; i < len; i++) {\n      if (this._callbacks[i] === callback) {\n        if (this._contexts[i] === context) {\n          // callback & context match => remove it\n          this._callbacks.splice(i, 1);\n          this._contexts.splice(i, 1);\n          return;\n        } else {\n          foundCallbackWithDifferentContext = true;\n        }\n      }\n    }\n    if (foundCallbackWithDifferentContext) {\n      throw new Error('When adding a listener with a context, you should remove it with the same context');\n    }\n  }\n  invoke(...args) {\n    if (!this._callbacks) {\n      return [];\n    }\n    const ret = [],\n      callbacks = this._callbacks.slice(0),\n      contexts = this._contexts.slice(0);\n    for (let i = 0, len = callbacks.length; i < len; i++) {\n      try {\n        ret.push(callbacks[i].apply(contexts[i], args));\n      } catch (e) {\n        // eslint-disable-next-line no-console\n        (0, ral_1.default)().console.error(e);\n      }\n    }\n    return ret;\n  }\n  isEmpty() {\n    return !this._callbacks || this._callbacks.length === 0;\n  }\n  dispose() {\n    this._callbacks = undefined;\n    this._contexts = undefined;\n  }\n}\nclass Emitter {\n  constructor(_options) {\n    this._options = _options;\n  }\n  /**\n   * For the public to allow to subscribe\n   * to events from this Emitter\n   */\n  get event() {\n    if (!this._event) {\n      this._event = (listener, thisArgs, disposables) => {\n        if (!this._callbacks) {\n          this._callbacks = new CallbackList();\n        }\n        if (this._options && this._options.onFirstListenerAdd && this._callbacks.isEmpty()) {\n          this._options.onFirstListenerAdd(this);\n        }\n        this._callbacks.add(listener, thisArgs);\n        const result = {\n          dispose: () => {\n            if (!this._callbacks) {\n              // disposable is disposed after emitter is disposed.\n              return;\n            }\n            this._callbacks.remove(listener, thisArgs);\n            result.dispose = Emitter._noop;\n            if (this._options && this._options.onLastListenerRemove && this._callbacks.isEmpty()) {\n              this._options.onLastListenerRemove(this);\n            }\n          }\n        };\n        if (Array.isArray(disposables)) {\n          disposables.push(result);\n        }\n        return result;\n      };\n    }\n    return this._event;\n  }\n  /**\n   * To be kept private to fire an event to\n   * subscribers\n   */\n  fire(event) {\n    if (this._callbacks) {\n      this._callbacks.invoke.call(this._callbacks, event);\n    }\n  }\n  dispose() {\n    if (this._callbacks) {\n      this._callbacks.dispose();\n      this._callbacks = undefined;\n    }\n  }\n}\nexports.Emitter = Emitter;\nEmitter._noop = function () {};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Emitter", "Event", "ral_1", "require", "_disposable", "dispose", "None", "CallbackList", "add", "callback", "context", "bucket", "_callbacks", "_contexts", "push", "Array", "isArray", "remove", "foundCallbackWithDifferentContext", "i", "len", "length", "splice", "Error", "invoke", "args", "ret", "callbacks", "slice", "contexts", "apply", "e", "default", "console", "error", "isEmpty", "undefined", "constructor", "_options", "event", "_event", "listener", "thisArgs", "disposables", "onFirstListenerAdd", "result", "_noop", "onLastListenerRemove", "fire", "call"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-jsonrpc/lib/common/events.js"], "sourcesContent": ["\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Emitter = exports.Event = void 0;\nconst ral_1 = require(\"./ral\");\nvar Event;\n(function (Event) {\n    const _disposable = { dispose() { } };\n    Event.None = function () { return _disposable; };\n})(Event || (exports.Event = Event = {}));\nclass CallbackList {\n    add(callback, context = null, bucket) {\n        if (!this._callbacks) {\n            this._callbacks = [];\n            this._contexts = [];\n        }\n        this._callbacks.push(callback);\n        this._contexts.push(context);\n        if (Array.isArray(bucket)) {\n            bucket.push({ dispose: () => this.remove(callback, context) });\n        }\n    }\n    remove(callback, context = null) {\n        if (!this._callbacks) {\n            return;\n        }\n        let foundCallbackWithDifferentContext = false;\n        for (let i = 0, len = this._callbacks.length; i < len; i++) {\n            if (this._callbacks[i] === callback) {\n                if (this._contexts[i] === context) {\n                    // callback & context match => remove it\n                    this._callbacks.splice(i, 1);\n                    this._contexts.splice(i, 1);\n                    return;\n                }\n                else {\n                    foundCallbackWithDifferentContext = true;\n                }\n            }\n        }\n        if (foundCallbackWithDifferentContext) {\n            throw new Error('When adding a listener with a context, you should remove it with the same context');\n        }\n    }\n    invoke(...args) {\n        if (!this._callbacks) {\n            return [];\n        }\n        const ret = [], callbacks = this._callbacks.slice(0), contexts = this._contexts.slice(0);\n        for (let i = 0, len = callbacks.length; i < len; i++) {\n            try {\n                ret.push(callbacks[i].apply(contexts[i], args));\n            }\n            catch (e) {\n                // eslint-disable-next-line no-console\n                (0, ral_1.default)().console.error(e);\n            }\n        }\n        return ret;\n    }\n    isEmpty() {\n        return !this._callbacks || this._callbacks.length === 0;\n    }\n    dispose() {\n        this._callbacks = undefined;\n        this._contexts = undefined;\n    }\n}\nclass Emitter {\n    constructor(_options) {\n        this._options = _options;\n    }\n    /**\n     * For the public to allow to subscribe\n     * to events from this Emitter\n     */\n    get event() {\n        if (!this._event) {\n            this._event = (listener, thisArgs, disposables) => {\n                if (!this._callbacks) {\n                    this._callbacks = new CallbackList();\n                }\n                if (this._options && this._options.onFirstListenerAdd && this._callbacks.isEmpty()) {\n                    this._options.onFirstListenerAdd(this);\n                }\n                this._callbacks.add(listener, thisArgs);\n                const result = {\n                    dispose: () => {\n                        if (!this._callbacks) {\n                            // disposable is disposed after emitter is disposed.\n                            return;\n                        }\n                        this._callbacks.remove(listener, thisArgs);\n                        result.dispose = Emitter._noop;\n                        if (this._options && this._options.onLastListenerRemove && this._callbacks.isEmpty()) {\n                            this._options.onLastListenerRemove(this);\n                        }\n                    }\n                };\n                if (Array.isArray(disposables)) {\n                    disposables.push(result);\n                }\n                return result;\n            };\n        }\n        return this._event;\n    }\n    /**\n     * To be kept private to fire an event to\n     * subscribers\n     */\n    fire(event) {\n        if (this._callbacks) {\n            this._callbacks.invoke.call(this._callbacks, event);\n        }\n    }\n    dispose() {\n        if (this._callbacks) {\n            this._callbacks.dispose();\n            this._callbacks = undefined;\n        }\n    }\n}\nexports.Emitter = Emitter;\nEmitter._noop = function () { };\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACAA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,KAAK,GAAG,KAAK,CAAC;AACxC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,IAAIF,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACd,MAAMG,WAAW,GAAG;IAAEC,OAAOA,CAAA,EAAG,CAAE;EAAE,CAAC;EACrCJ,KAAK,CAACK,IAAI,GAAG,YAAY;IAAE,OAAOF,WAAW;EAAE,CAAC;AACpD,CAAC,EAAEH,KAAK,KAAKH,OAAO,CAACG,KAAK,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,MAAMM,YAAY,CAAC;EACfC,GAAGA,CAACC,QAAQ,EAAEC,OAAO,GAAG,IAAI,EAAEC,MAAM,EAAE;IAClC,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,SAAS,GAAG,EAAE;IACvB;IACA,IAAI,CAACD,UAAU,CAACE,IAAI,CAACL,QAAQ,CAAC;IAC9B,IAAI,CAACI,SAAS,CAACC,IAAI,CAACJ,OAAO,CAAC;IAC5B,IAAIK,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACvBA,MAAM,CAACG,IAAI,CAAC;QAAET,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACY,MAAM,CAACR,QAAQ,EAAEC,OAAO;MAAE,CAAC,CAAC;IAClE;EACJ;EACAO,MAAMA,CAACR,QAAQ,EAAEC,OAAO,GAAG,IAAI,EAAE;IAC7B,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;MAClB;IACJ;IACA,IAAIM,iCAAiC,GAAG,KAAK;IAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACR,UAAU,CAACS,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxD,IAAI,IAAI,CAACP,UAAU,CAACO,CAAC,CAAC,KAAKV,QAAQ,EAAE;QACjC,IAAI,IAAI,CAACI,SAAS,CAACM,CAAC,CAAC,KAAKT,OAAO,EAAE;UAC/B;UACA,IAAI,CAACE,UAAU,CAACU,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;UAC5B,IAAI,CAACN,SAAS,CAACS,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;UAC3B;QACJ,CAAC,MACI;UACDD,iCAAiC,GAAG,IAAI;QAC5C;MACJ;IACJ;IACA,IAAIA,iCAAiC,EAAE;MACnC,MAAM,IAAIK,KAAK,CAAC,mFAAmF,CAAC;IACxG;EACJ;EACAC,MAAMA,CAAC,GAAGC,IAAI,EAAE;IACZ,IAAI,CAAC,IAAI,CAACb,UAAU,EAAE;MAClB,OAAO,EAAE;IACb;IACA,MAAMc,GAAG,GAAG,EAAE;MAAEC,SAAS,GAAG,IAAI,CAACf,UAAU,CAACgB,KAAK,CAAC,CAAC,CAAC;MAAEC,QAAQ,GAAG,IAAI,CAAChB,SAAS,CAACe,KAAK,CAAC,CAAC,CAAC;IACxF,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGO,SAAS,CAACN,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,IAAI;QACAO,GAAG,CAACZ,IAAI,CAACa,SAAS,CAACR,CAAC,CAAC,CAACW,KAAK,CAACD,QAAQ,CAACV,CAAC,CAAC,EAAEM,IAAI,CAAC,CAAC;MACnD,CAAC,CACD,OAAOM,CAAC,EAAE;QACN;QACA,CAAC,CAAC,EAAE7B,KAAK,CAAC8B,OAAO,EAAE,CAAC,CAACC,OAAO,CAACC,KAAK,CAACH,CAAC,CAAC;MACzC;IACJ;IACA,OAAOL,GAAG;EACd;EACAS,OAAOA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAACvB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACS,MAAM,KAAK,CAAC;EAC3D;EACAhB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACO,UAAU,GAAGwB,SAAS;IAC3B,IAAI,CAACvB,SAAS,GAAGuB,SAAS;EAC9B;AACJ;AACA,MAAMpC,OAAO,CAAC;EACVqC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,KAAK;QAC/C,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAAE;UAClB,IAAI,CAACA,UAAU,GAAG,IAAIL,YAAY,CAAC,CAAC;QACxC;QACA,IAAI,IAAI,CAAC+B,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACM,kBAAkB,IAAI,IAAI,CAAChC,UAAU,CAACuB,OAAO,CAAC,CAAC,EAAE;UAChF,IAAI,CAACG,QAAQ,CAACM,kBAAkB,CAAC,IAAI,CAAC;QAC1C;QACA,IAAI,CAAChC,UAAU,CAACJ,GAAG,CAACiC,QAAQ,EAAEC,QAAQ,CAAC;QACvC,MAAMG,MAAM,GAAG;UACXxC,OAAO,EAAEA,CAAA,KAAM;YACX,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;cAClB;cACA;YACJ;YACA,IAAI,CAACA,UAAU,CAACK,MAAM,CAACwB,QAAQ,EAAEC,QAAQ,CAAC;YAC1CG,MAAM,CAACxC,OAAO,GAAGL,OAAO,CAAC8C,KAAK;YAC9B,IAAI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACS,oBAAoB,IAAI,IAAI,CAACnC,UAAU,CAACuB,OAAO,CAAC,CAAC,EAAE;cAClF,IAAI,CAACG,QAAQ,CAACS,oBAAoB,CAAC,IAAI,CAAC;YAC5C;UACJ;QACJ,CAAC;QACD,IAAIhC,KAAK,CAACC,OAAO,CAAC2B,WAAW,CAAC,EAAE;UAC5BA,WAAW,CAAC7B,IAAI,CAAC+B,MAAM,CAAC;QAC5B;QACA,OAAOA,MAAM;MACjB,CAAC;IACL;IACA,OAAO,IAAI,CAACL,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIQ,IAAIA,CAACT,KAAK,EAAE;IACR,IAAI,IAAI,CAAC3B,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACY,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACrC,UAAU,EAAE2B,KAAK,CAAC;IACvD;EACJ;EACAlC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACO,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACP,OAAO,CAAC,CAAC;MACzB,IAAI,CAACO,UAAU,GAAGwB,SAAS;IAC/B;EACJ;AACJ;AACAtC,OAAO,CAACE,OAAO,GAAGA,OAAO;AACzBA,OAAO,CAAC8C,KAAK,GAAG,YAAY,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}