{"ast": null, "code": "import { BaseRegExpVisitor } from \"@chevrotain/regexp-to-ast\";\nimport { Lexer, LexerDefinitionErrorType } from \"./lexer_public.js\";\nimport { compact, defaults, difference, filter, find, first, flatten, forEach, has, includes, indexOf, isArray, isEmpty, isFunction, isRegExp, isString, isUndefined, keys, map, reduce, reject, values } from \"lodash-es\";\nimport { PRINT_ERROR } from \"@chevrotain/utils\";\nimport { canMatchCharCode, failedOptimizationPrefixMsg, getOptimizedStartCodesIndices } from \"./reg_exp.js\";\nimport { getRegExpAst } from \"./reg_exp_parser.js\";\nconst PATTERN = \"PATTERN\";\nexport const DEFAULT_MODE = \"defaultMode\";\nexport const MODES = \"modes\";\nexport let SUPPORT_STICKY = typeof new RegExp(\"(?:)\").sticky === \"boolean\";\nexport function disableSticky() {\n  SUPPORT_STICKY = false;\n}\nexport function enableSticky() {\n  SUPPORT_STICKY = true;\n}\nexport function analyzeTokenTypes(tokenTypes, options) {\n  options = defaults(options, {\n    useSticky: SUPPORT_STICKY,\n    debug: false,\n    safeMode: false,\n    positionTracking: \"full\",\n    lineTerminatorCharacters: [\"\\r\", \"\\n\"],\n    tracer: (msg, action) => action()\n  });\n  const tracer = options.tracer;\n  tracer(\"initCharCodeToOptimizedIndexMap\", () => {\n    initCharCodeToOptimizedIndexMap();\n  });\n  let onlyRelevantTypes;\n  tracer(\"Reject Lexer.NA\", () => {\n    onlyRelevantTypes = reject(tokenTypes, currType => {\n      return currType[PATTERN] === Lexer.NA;\n    });\n  });\n  let hasCustom = false;\n  let allTransformedPatterns;\n  tracer(\"Transform Patterns\", () => {\n    hasCustom = false;\n    allTransformedPatterns = map(onlyRelevantTypes, currType => {\n      const currPattern = currType[PATTERN];\n      /* istanbul ignore else */\n      if (isRegExp(currPattern)) {\n        const regExpSource = currPattern.source;\n        if (regExpSource.length === 1 &&\n        // only these regExp meta characters which can appear in a length one regExp\n        regExpSource !== \"^\" && regExpSource !== \"$\" && regExpSource !== \".\" && !currPattern.ignoreCase) {\n          return regExpSource;\n        } else if (regExpSource.length === 2 && regExpSource[0] === \"\\\\\" &&\n        // not a meta character\n        !includes([\"d\", \"D\", \"s\", \"S\", \"t\", \"r\", \"n\", \"t\", \"0\", \"c\", \"b\", \"B\", \"f\", \"v\", \"w\", \"W\"], regExpSource[1])) {\n          // escaped meta Characters: /\\+/ /\\[/\n          // or redundant escaping: /\\a/\n          // without the escaping \"\\\"\n          return regExpSource[1];\n        } else {\n          return options.useSticky ? addStickyFlag(currPattern) : addStartOfInput(currPattern);\n        }\n      } else if (isFunction(currPattern)) {\n        hasCustom = true;\n        // CustomPatternMatcherFunc - custom patterns do not require any transformations, only wrapping in a RegExp Like object\n        return {\n          exec: currPattern\n        };\n      } else if (typeof currPattern === \"object\") {\n        hasCustom = true;\n        // ICustomPattern\n        return currPattern;\n      } else if (typeof currPattern === \"string\") {\n        if (currPattern.length === 1) {\n          return currPattern;\n        } else {\n          const escapedRegExpString = currPattern.replace(/[\\\\^$.*+?()[\\]{}|]/g, \"\\\\$&\");\n          const wrappedRegExp = new RegExp(escapedRegExpString);\n          return options.useSticky ? addStickyFlag(wrappedRegExp) : addStartOfInput(wrappedRegExp);\n        }\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    });\n  });\n  let patternIdxToType;\n  let patternIdxToGroup;\n  let patternIdxToLongerAltIdxArr;\n  let patternIdxToPushMode;\n  let patternIdxToPopMode;\n  tracer(\"misc mapping\", () => {\n    patternIdxToType = map(onlyRelevantTypes, currType => currType.tokenTypeIdx);\n    patternIdxToGroup = map(onlyRelevantTypes, clazz => {\n      const groupName = clazz.GROUP;\n      /* istanbul ignore next */\n      if (groupName === Lexer.SKIPPED) {\n        return undefined;\n      } else if (isString(groupName)) {\n        return groupName;\n      } else if (isUndefined(groupName)) {\n        return false;\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    });\n    patternIdxToLongerAltIdxArr = map(onlyRelevantTypes, clazz => {\n      const longerAltType = clazz.LONGER_ALT;\n      if (longerAltType) {\n        const longerAltIdxArr = isArray(longerAltType) ? map(longerAltType, type => indexOf(onlyRelevantTypes, type)) : [indexOf(onlyRelevantTypes, longerAltType)];\n        return longerAltIdxArr;\n      }\n    });\n    patternIdxToPushMode = map(onlyRelevantTypes, clazz => clazz.PUSH_MODE);\n    patternIdxToPopMode = map(onlyRelevantTypes, clazz => has(clazz, \"POP_MODE\"));\n  });\n  let patternIdxToCanLineTerminator;\n  tracer(\"Line Terminator Handling\", () => {\n    const lineTerminatorCharCodes = getCharCodes(options.lineTerminatorCharacters);\n    patternIdxToCanLineTerminator = map(onlyRelevantTypes, tokType => false);\n    if (options.positionTracking !== \"onlyOffset\") {\n      patternIdxToCanLineTerminator = map(onlyRelevantTypes, tokType => {\n        if (has(tokType, \"LINE_BREAKS\")) {\n          return !!tokType.LINE_BREAKS;\n        } else {\n          return checkLineBreaksIssues(tokType, lineTerminatorCharCodes) === false && canMatchCharCode(lineTerminatorCharCodes, tokType.PATTERN);\n        }\n      });\n    }\n  });\n  let patternIdxToIsCustom;\n  let patternIdxToShort;\n  let emptyGroups;\n  let patternIdxToConfig;\n  tracer(\"Misc Mapping #2\", () => {\n    patternIdxToIsCustom = map(onlyRelevantTypes, isCustomPattern);\n    patternIdxToShort = map(allTransformedPatterns, isShortPattern);\n    emptyGroups = reduce(onlyRelevantTypes, (acc, clazz) => {\n      const groupName = clazz.GROUP;\n      if (isString(groupName) && !(groupName === Lexer.SKIPPED)) {\n        acc[groupName] = [];\n      }\n      return acc;\n    }, {});\n    patternIdxToConfig = map(allTransformedPatterns, (x, idx) => {\n      return {\n        pattern: allTransformedPatterns[idx],\n        longerAlt: patternIdxToLongerAltIdxArr[idx],\n        canLineTerminator: patternIdxToCanLineTerminator[idx],\n        isCustom: patternIdxToIsCustom[idx],\n        short: patternIdxToShort[idx],\n        group: patternIdxToGroup[idx],\n        push: patternIdxToPushMode[idx],\n        pop: patternIdxToPopMode[idx],\n        tokenTypeIdx: patternIdxToType[idx],\n        tokenType: onlyRelevantTypes[idx]\n      };\n    });\n  });\n  let canBeOptimized = true;\n  let charCodeToPatternIdxToConfig = [];\n  if (!options.safeMode) {\n    tracer(\"First Char Optimization\", () => {\n      charCodeToPatternIdxToConfig = reduce(onlyRelevantTypes, (result, currTokType, idx) => {\n        if (typeof currTokType.PATTERN === \"string\") {\n          const charCode = currTokType.PATTERN.charCodeAt(0);\n          const optimizedIdx = charCodeToOptimizedIndex(charCode);\n          addToMapOfArrays(result, optimizedIdx, patternIdxToConfig[idx]);\n        } else if (isArray(currTokType.START_CHARS_HINT)) {\n          let lastOptimizedIdx;\n          forEach(currTokType.START_CHARS_HINT, charOrInt => {\n            const charCode = typeof charOrInt === \"string\" ? charOrInt.charCodeAt(0) : charOrInt;\n            const currOptimizedIdx = charCodeToOptimizedIndex(charCode);\n            // Avoid adding the config multiple times\n            /* istanbul ignore else */\n            // - Difficult to check this scenario effects as it is only a performance\n            //   optimization that does not change correctness\n            if (lastOptimizedIdx !== currOptimizedIdx) {\n              lastOptimizedIdx = currOptimizedIdx;\n              addToMapOfArrays(result, currOptimizedIdx, patternIdxToConfig[idx]);\n            }\n          });\n        } else if (isRegExp(currTokType.PATTERN)) {\n          if (currTokType.PATTERN.unicode) {\n            canBeOptimized = false;\n            if (options.ensureOptimizations) {\n              PRINT_ERROR(`${failedOptimizationPrefixMsg}` + `\\tUnable to analyze < ${currTokType.PATTERN.toString()} > pattern.\\n` + \"\\tThe regexp unicode flag is not currently supported by the regexp-to-ast library.\\n\" + \"\\tThis will disable the lexer's first char optimizations.\\n\" + \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE\");\n            }\n          } else {\n            const optimizedCodes = getOptimizedStartCodesIndices(currTokType.PATTERN, options.ensureOptimizations);\n            /* istanbul ignore if */\n            // start code will only be empty given an empty regExp or failure of regexp-to-ast library\n            // the first should be a different validation and the second cannot be tested.\n            if (isEmpty(optimizedCodes)) {\n              // we cannot understand what codes may start possible matches\n              // The optimization correctness requires knowing start codes for ALL patterns.\n              // Not actually sure this is an error, no debug message\n              canBeOptimized = false;\n            }\n            forEach(optimizedCodes, code => {\n              addToMapOfArrays(result, code, patternIdxToConfig[idx]);\n            });\n          }\n        } else {\n          if (options.ensureOptimizations) {\n            PRINT_ERROR(`${failedOptimizationPrefixMsg}` + `\\tTokenType: <${currTokType.name}> is using a custom token pattern without providing <start_chars_hint> parameter.\\n` + \"\\tThis will disable the lexer's first char optimizations.\\n\" + \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE\");\n          }\n          canBeOptimized = false;\n        }\n        return result;\n      }, []);\n    });\n  }\n  return {\n    emptyGroups: emptyGroups,\n    patternIdxToConfig: patternIdxToConfig,\n    charCodeToPatternIdxToConfig: charCodeToPatternIdxToConfig,\n    hasCustom: hasCustom,\n    canBeOptimized: canBeOptimized\n  };\n}\nexport function validatePatterns(tokenTypes, validModesNames) {\n  let errors = [];\n  const missingResult = findMissingPatterns(tokenTypes);\n  errors = errors.concat(missingResult.errors);\n  const invalidResult = findInvalidPatterns(missingResult.valid);\n  const validTokenTypes = invalidResult.valid;\n  errors = errors.concat(invalidResult.errors);\n  errors = errors.concat(validateRegExpPattern(validTokenTypes));\n  errors = errors.concat(findInvalidGroupType(validTokenTypes));\n  errors = errors.concat(findModesThatDoNotExist(validTokenTypes, validModesNames));\n  errors = errors.concat(findUnreachablePatterns(validTokenTypes));\n  return errors;\n}\nfunction validateRegExpPattern(tokenTypes) {\n  let errors = [];\n  const withRegExpPatterns = filter(tokenTypes, currTokType => isRegExp(currTokType[PATTERN]));\n  errors = errors.concat(findEndOfInputAnchor(withRegExpPatterns));\n  errors = errors.concat(findStartOfInputAnchor(withRegExpPatterns));\n  errors = errors.concat(findUnsupportedFlags(withRegExpPatterns));\n  errors = errors.concat(findDuplicatePatterns(withRegExpPatterns));\n  errors = errors.concat(findEmptyMatchRegExps(withRegExpPatterns));\n  return errors;\n}\nexport function findMissingPatterns(tokenTypes) {\n  const tokenTypesWithMissingPattern = filter(tokenTypes, currType => {\n    return !has(currType, PATTERN);\n  });\n  const errors = map(tokenTypesWithMissingPattern, currType => {\n    return {\n      message: \"Token Type: ->\" + currType.name + \"<- missing static 'PATTERN' property\",\n      type: LexerDefinitionErrorType.MISSING_PATTERN,\n      tokenTypes: [currType]\n    };\n  });\n  const valid = difference(tokenTypes, tokenTypesWithMissingPattern);\n  return {\n    errors,\n    valid\n  };\n}\nexport function findInvalidPatterns(tokenTypes) {\n  const tokenTypesWithInvalidPattern = filter(tokenTypes, currType => {\n    const pattern = currType[PATTERN];\n    return !isRegExp(pattern) && !isFunction(pattern) && !has(pattern, \"exec\") && !isString(pattern);\n  });\n  const errors = map(tokenTypesWithInvalidPattern, currType => {\n    return {\n      message: \"Token Type: ->\" + currType.name + \"<- static 'PATTERN' can only be a RegExp, a\" + \" Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.\",\n      type: LexerDefinitionErrorType.INVALID_PATTERN,\n      tokenTypes: [currType]\n    };\n  });\n  const valid = difference(tokenTypes, tokenTypesWithInvalidPattern);\n  return {\n    errors,\n    valid\n  };\n}\nconst end_of_input = /[^\\\\][$]/;\nexport function findEndOfInputAnchor(tokenTypes) {\n  class EndAnchorFinder extends BaseRegExpVisitor {\n    constructor() {\n      super(...arguments);\n      this.found = false;\n    }\n    visitEndAnchor(node) {\n      this.found = true;\n    }\n  }\n  const invalidRegex = filter(tokenTypes, currType => {\n    const pattern = currType.PATTERN;\n    try {\n      const regexpAst = getRegExpAst(pattern);\n      const endAnchorVisitor = new EndAnchorFinder();\n      endAnchorVisitor.visit(regexpAst);\n      return endAnchorVisitor.found;\n    } catch (e) {\n      // old behavior in case of runtime exceptions with regexp-to-ast.\n      /* istanbul ignore next - cannot ensure an error in regexp-to-ast*/\n      return end_of_input.test(pattern.source);\n    }\n  });\n  const errors = map(invalidRegex, currType => {\n    return {\n      message: \"Unexpected RegExp Anchor Error:\\n\" + \"\\tToken Type: ->\" + currType.name + \"<- static 'PATTERN' cannot contain end of input anchor '$'\\n\" + \"\\tSee chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\" + \"\\tfor details.\",\n      type: LexerDefinitionErrorType.EOI_ANCHOR_FOUND,\n      tokenTypes: [currType]\n    };\n  });\n  return errors;\n}\nexport function findEmptyMatchRegExps(tokenTypes) {\n  const matchesEmptyString = filter(tokenTypes, currType => {\n    const pattern = currType.PATTERN;\n    return pattern.test(\"\");\n  });\n  const errors = map(matchesEmptyString, currType => {\n    return {\n      message: \"Token Type: ->\" + currType.name + \"<- static 'PATTERN' must not match an empty string\",\n      type: LexerDefinitionErrorType.EMPTY_MATCH_PATTERN,\n      tokenTypes: [currType]\n    };\n  });\n  return errors;\n}\nconst start_of_input = /[^\\\\[][\\^]|^\\^/;\nexport function findStartOfInputAnchor(tokenTypes) {\n  class StartAnchorFinder extends BaseRegExpVisitor {\n    constructor() {\n      super(...arguments);\n      this.found = false;\n    }\n    visitStartAnchor(node) {\n      this.found = true;\n    }\n  }\n  const invalidRegex = filter(tokenTypes, currType => {\n    const pattern = currType.PATTERN;\n    try {\n      const regexpAst = getRegExpAst(pattern);\n      const startAnchorVisitor = new StartAnchorFinder();\n      startAnchorVisitor.visit(regexpAst);\n      return startAnchorVisitor.found;\n    } catch (e) {\n      // old behavior in case of runtime exceptions with regexp-to-ast.\n      /* istanbul ignore next - cannot ensure an error in regexp-to-ast*/\n      return start_of_input.test(pattern.source);\n    }\n  });\n  const errors = map(invalidRegex, currType => {\n    return {\n      message: \"Unexpected RegExp Anchor Error:\\n\" + \"\\tToken Type: ->\" + currType.name + \"<- static 'PATTERN' cannot contain start of input anchor '^'\\n\" + \"\\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\" + \"\\tfor details.\",\n      type: LexerDefinitionErrorType.SOI_ANCHOR_FOUND,\n      tokenTypes: [currType]\n    };\n  });\n  return errors;\n}\nexport function findUnsupportedFlags(tokenTypes) {\n  const invalidFlags = filter(tokenTypes, currType => {\n    const pattern = currType[PATTERN];\n    return pattern instanceof RegExp && (pattern.multiline || pattern.global);\n  });\n  const errors = map(invalidFlags, currType => {\n    return {\n      message: \"Token Type: ->\" + currType.name + \"<- static 'PATTERN' may NOT contain global('g') or multiline('m')\",\n      type: LexerDefinitionErrorType.UNSUPPORTED_FLAGS_FOUND,\n      tokenTypes: [currType]\n    };\n  });\n  return errors;\n}\n// This can only test for identical duplicate RegExps, not semantically equivalent ones.\nexport function findDuplicatePatterns(tokenTypes) {\n  const found = [];\n  let identicalPatterns = map(tokenTypes, outerType => {\n    return reduce(tokenTypes, (result, innerType) => {\n      if (outerType.PATTERN.source === innerType.PATTERN.source && !includes(found, innerType) && innerType.PATTERN !== Lexer.NA) {\n        // this avoids duplicates in the result, each Token Type may only appear in one \"set\"\n        // in essence we are creating Equivalence classes on equality relation.\n        found.push(innerType);\n        result.push(innerType);\n        return result;\n      }\n      return result;\n    }, []);\n  });\n  identicalPatterns = compact(identicalPatterns);\n  const duplicatePatterns = filter(identicalPatterns, currIdenticalSet => {\n    return currIdenticalSet.length > 1;\n  });\n  const errors = map(duplicatePatterns, setOfIdentical => {\n    const tokenTypeNames = map(setOfIdentical, currType => {\n      return currType.name;\n    });\n    const dupPatternSrc = first(setOfIdentical).PATTERN;\n    return {\n      message: `The same RegExp pattern ->${dupPatternSrc}<-` + `has been used in all of the following Token Types: ${tokenTypeNames.join(\", \")} <-`,\n      type: LexerDefinitionErrorType.DUPLICATE_PATTERNS_FOUND,\n      tokenTypes: setOfIdentical\n    };\n  });\n  return errors;\n}\nexport function findInvalidGroupType(tokenTypes) {\n  const invalidTypes = filter(tokenTypes, clazz => {\n    if (!has(clazz, \"GROUP\")) {\n      return false;\n    }\n    const group = clazz.GROUP;\n    return group !== Lexer.SKIPPED && group !== Lexer.NA && !isString(group);\n  });\n  const errors = map(invalidTypes, currType => {\n    return {\n      message: \"Token Type: ->\" + currType.name + \"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String\",\n      type: LexerDefinitionErrorType.INVALID_GROUP_TYPE_FOUND,\n      tokenTypes: [currType]\n    };\n  });\n  return errors;\n}\nexport function findModesThatDoNotExist(tokenTypes, validModes) {\n  const invalidModes = filter(tokenTypes, clazz => {\n    return clazz.PUSH_MODE !== undefined && !includes(validModes, clazz.PUSH_MODE);\n  });\n  const errors = map(invalidModes, tokType => {\n    const msg = `Token Type: ->${tokType.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${tokType.PUSH_MODE}<-` + `which does not exist`;\n    return {\n      message: msg,\n      type: LexerDefinitionErrorType.PUSH_MODE_DOES_NOT_EXIST,\n      tokenTypes: [tokType]\n    };\n  });\n  return errors;\n}\nexport function findUnreachablePatterns(tokenTypes) {\n  const errors = [];\n  const canBeTested = reduce(tokenTypes, (result, tokType, idx) => {\n    const pattern = tokType.PATTERN;\n    if (pattern === Lexer.NA) {\n      return result;\n    }\n    // a more comprehensive validation for all forms of regExps would require\n    // deeper regExp analysis capabilities\n    if (isString(pattern)) {\n      result.push({\n        str: pattern,\n        idx,\n        tokenType: tokType\n      });\n    } else if (isRegExp(pattern) && noMetaChar(pattern)) {\n      result.push({\n        str: pattern.source,\n        idx,\n        tokenType: tokType\n      });\n    }\n    return result;\n  }, []);\n  forEach(tokenTypes, (tokType, testIdx) => {\n    forEach(canBeTested, ({\n      str,\n      idx,\n      tokenType\n    }) => {\n      if (testIdx < idx && testTokenType(str, tokType.PATTERN)) {\n        const msg = `Token: ->${tokenType.name}<- can never be matched.\\n` + `Because it appears AFTER the Token Type ->${tokType.name}<-` + `in the lexer's definition.\\n` + `See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;\n        errors.push({\n          message: msg,\n          type: LexerDefinitionErrorType.UNREACHABLE_PATTERN,\n          tokenTypes: [tokType, tokenType]\n        });\n      }\n    });\n  });\n  return errors;\n}\nfunction testTokenType(str, pattern) {\n  /* istanbul ignore else */\n  if (isRegExp(pattern)) {\n    const regExpArray = pattern.exec(str);\n    return regExpArray !== null && regExpArray.index === 0;\n  } else if (isFunction(pattern)) {\n    // maintain the API of custom patterns\n    return pattern(str, 0, [], {});\n  } else if (has(pattern, \"exec\")) {\n    // maintain the API of custom patterns\n    return pattern.exec(str, 0, [], {});\n  } else if (typeof pattern === \"string\") {\n    return pattern === str;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nfunction noMetaChar(regExp) {\n  //https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp\n  const metaChars = [\".\", \"\\\\\", \"[\", \"]\", \"|\", \"^\", \"$\", \"(\", \")\", \"?\", \"*\", \"+\", \"{\"];\n  return find(metaChars, char => regExp.source.indexOf(char) !== -1) === undefined;\n}\nexport function addStartOfInput(pattern) {\n  const flags = pattern.ignoreCase ? \"i\" : \"\";\n  // always wrapping in a none capturing group preceded by '^' to make sure matching can only work on start of input.\n  // duplicate/redundant start of input markers have no meaning (/^^^^A/ === /^A/)\n  return new RegExp(`^(?:${pattern.source})`, flags);\n}\nexport function addStickyFlag(pattern) {\n  const flags = pattern.ignoreCase ? \"iy\" : \"y\";\n  // always wrapping in a none capturing group preceded by '^' to make sure matching can only work on start of input.\n  // duplicate/redundant start of input markers have no meaning (/^^^^A/ === /^A/)\n  return new RegExp(`${pattern.source}`, flags);\n}\nexport function performRuntimeChecks(lexerDefinition, trackLines, lineTerminatorCharacters) {\n  const errors = [];\n  // some run time checks to help the end users.\n  if (!has(lexerDefinition, DEFAULT_MODE)) {\n    errors.push({\n      message: \"A MultiMode Lexer cannot be initialized without a <\" + DEFAULT_MODE + \"> property in its definition\\n\",\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE\n    });\n  }\n  if (!has(lexerDefinition, MODES)) {\n    errors.push({\n      message: \"A MultiMode Lexer cannot be initialized without a <\" + MODES + \"> property in its definition\\n\",\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY\n    });\n  }\n  if (has(lexerDefinition, MODES) && has(lexerDefinition, DEFAULT_MODE) && !has(lexerDefinition.modes, lexerDefinition.defaultMode)) {\n    errors.push({\n      message: `A MultiMode Lexer cannot be initialized with a ${DEFAULT_MODE}: <${lexerDefinition.defaultMode}>` + `which does not exist\\n`,\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST\n    });\n  }\n  if (has(lexerDefinition, MODES)) {\n    forEach(lexerDefinition.modes, (currModeValue, currModeName) => {\n      forEach(currModeValue, (currTokType, currIdx) => {\n        if (isUndefined(currTokType)) {\n          errors.push({\n            message: `A Lexer cannot be initialized using an undefined Token Type. Mode:` + `<${currModeName}> at index: <${currIdx}>\\n`,\n            type: LexerDefinitionErrorType.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED\n          });\n        } else if (has(currTokType, \"LONGER_ALT\")) {\n          const longerAlt = isArray(currTokType.LONGER_ALT) ? currTokType.LONGER_ALT : [currTokType.LONGER_ALT];\n          forEach(longerAlt, currLongerAlt => {\n            if (!isUndefined(currLongerAlt) && !includes(currModeValue, currLongerAlt)) {\n              errors.push({\n                message: `A MultiMode Lexer cannot be initialized with a longer_alt <${currLongerAlt.name}> on token <${currTokType.name}> outside of mode <${currModeName}>\\n`,\n                type: LexerDefinitionErrorType.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE\n              });\n            }\n          });\n        }\n      });\n    });\n  }\n  return errors;\n}\nexport function performWarningRuntimeChecks(lexerDefinition, trackLines, lineTerminatorCharacters) {\n  const warnings = [];\n  let hasAnyLineBreak = false;\n  const allTokenTypes = compact(flatten(values(lexerDefinition.modes)));\n  const concreteTokenTypes = reject(allTokenTypes, currType => currType[PATTERN] === Lexer.NA);\n  const terminatorCharCodes = getCharCodes(lineTerminatorCharacters);\n  if (trackLines) {\n    forEach(concreteTokenTypes, tokType => {\n      const currIssue = checkLineBreaksIssues(tokType, terminatorCharCodes);\n      if (currIssue !== false) {\n        const message = buildLineBreakIssueMessage(tokType, currIssue);\n        const warningDescriptor = {\n          message,\n          type: currIssue.issue,\n          tokenType: tokType\n        };\n        warnings.push(warningDescriptor);\n      } else {\n        // we don't want to attempt to scan if the user explicitly specified the line_breaks option.\n        if (has(tokType, \"LINE_BREAKS\")) {\n          if (tokType.LINE_BREAKS === true) {\n            hasAnyLineBreak = true;\n          }\n        } else {\n          if (canMatchCharCode(terminatorCharCodes, tokType.PATTERN)) {\n            hasAnyLineBreak = true;\n          }\n        }\n      }\n    });\n  }\n  if (trackLines && !hasAnyLineBreak) {\n    warnings.push({\n      message: \"Warning: No LINE_BREAKS Found.\\n\" + \"\\tThis Lexer has been defined to track line and column information,\\n\" + \"\\tBut none of the Token Types can be identified as matching a line terminator.\\n\" + \"\\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \\n\" + \"\\tfor details.\",\n      type: LexerDefinitionErrorType.NO_LINE_BREAKS_FLAGS\n    });\n  }\n  return warnings;\n}\nexport function cloneEmptyGroups(emptyGroups) {\n  const clonedResult = {};\n  const groupKeys = keys(emptyGroups);\n  forEach(groupKeys, currKey => {\n    const currGroupValue = emptyGroups[currKey];\n    /* istanbul ignore else */\n    if (isArray(currGroupValue)) {\n      clonedResult[currKey] = [];\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  });\n  return clonedResult;\n}\n// TODO: refactor to avoid duplication\nexport function isCustomPattern(tokenType) {\n  const pattern = tokenType.PATTERN;\n  /* istanbul ignore else */\n  if (isRegExp(pattern)) {\n    return false;\n  } else if (isFunction(pattern)) {\n    // CustomPatternMatcherFunc - custom patterns do not require any transformations, only wrapping in a RegExp Like object\n    return true;\n  } else if (has(pattern, \"exec\")) {\n    // ICustomPattern\n    return true;\n  } else if (isString(pattern)) {\n    return false;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nexport function isShortPattern(pattern) {\n  if (isString(pattern) && pattern.length === 1) {\n    return pattern.charCodeAt(0);\n  } else {\n    return false;\n  }\n}\n/**\n * Faster than using a RegExp for default newline detection during lexing.\n */\nexport const LineTerminatorOptimizedTester = {\n  // implements /\\n|\\r\\n?/g.test\n  test: function (text) {\n    const len = text.length;\n    for (let i = this.lastIndex; i < len; i++) {\n      const c = text.charCodeAt(i);\n      if (c === 10) {\n        this.lastIndex = i + 1;\n        return true;\n      } else if (c === 13) {\n        if (text.charCodeAt(i + 1) === 10) {\n          this.lastIndex = i + 2;\n        } else {\n          this.lastIndex = i + 1;\n        }\n        return true;\n      }\n    }\n    return false;\n  },\n  lastIndex: 0\n};\nfunction checkLineBreaksIssues(tokType, lineTerminatorCharCodes) {\n  if (has(tokType, \"LINE_BREAKS\")) {\n    // if the user explicitly declared the line_breaks option we will respect their choice\n    // and assume it is correct.\n    return false;\n  } else {\n    /* istanbul ignore else */\n    if (isRegExp(tokType.PATTERN)) {\n      try {\n        // TODO: why is the casting suddenly needed?\n        canMatchCharCode(lineTerminatorCharCodes, tokType.PATTERN);\n      } catch (e) {\n        /* istanbul ignore next - to test this we would have to mock <canMatchCharCode> to throw an error */\n        return {\n          issue: LexerDefinitionErrorType.IDENTIFY_TERMINATOR,\n          errMsg: e.message\n        };\n      }\n      return false;\n    } else if (isString(tokType.PATTERN)) {\n      // string literal patterns can always be analyzed to detect line terminator usage\n      return false;\n    } else if (isCustomPattern(tokType)) {\n      // custom token types\n      return {\n        issue: LexerDefinitionErrorType.CUSTOM_LINE_BREAK\n      };\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  }\n}\nexport function buildLineBreakIssueMessage(tokType, details) {\n  /* istanbul ignore else */\n  if (details.issue === LexerDefinitionErrorType.IDENTIFY_TERMINATOR) {\n    return \"Warning: unable to identify line terminator usage in pattern.\\n\" + `\\tThe problem is in the <${tokType.name}> Token Type\\n` + `\\t Root cause: ${details.errMsg}.\\n` + \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR\";\n  } else if (details.issue === LexerDefinitionErrorType.CUSTOM_LINE_BREAK) {\n    return \"Warning: A Custom Token Pattern should specify the <line_breaks> option.\\n\" + `\\tThe problem is in the <${tokType.name}> Token Type\\n` + \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK\";\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nfunction getCharCodes(charsOrCodes) {\n  const charCodes = map(charsOrCodes, numOrString => {\n    if (isString(numOrString)) {\n      return numOrString.charCodeAt(0);\n    } else {\n      return numOrString;\n    }\n  });\n  return charCodes;\n}\nfunction addToMapOfArrays(map, key, value) {\n  if (map[key] === undefined) {\n    map[key] = [value];\n  } else {\n    map[key].push(value);\n  }\n}\nexport const minOptimizationVal = 256;\n/**\n * We are mapping charCode above ASCI (256) into buckets each in the size of 256.\n * This is because ASCI are the most common start chars so each one of those will get its own\n * possible token configs vector.\n *\n * Tokens starting with charCodes \"above\" ASCI are uncommon, so we can \"afford\"\n * to place these into buckets of possible token configs, What we gain from\n * this is avoiding the case of creating an optimization 'charCodeToPatternIdxToConfig'\n * which would contain 10,000+ arrays of small size (e.g unicode Identifiers scenario).\n * Our 'charCodeToPatternIdxToConfig' max size will now be:\n * 256 + (2^16 / 2^8) - 1 === 511\n *\n * note the hack for fast division integer part extraction\n * See: https://stackoverflow.com/a/4228528\n */\nlet charCodeToOptimizedIdxMap = [];\nexport function charCodeToOptimizedIndex(charCode) {\n  return charCode < minOptimizationVal ? charCode : charCodeToOptimizedIdxMap[charCode];\n}\n/**\n * This is a compromise between cold start / hot running performance\n * Creating this array takes ~3ms on a modern machine,\n * But if we perform the computation at runtime as needed the CSS Lexer benchmark\n * performance degrades by ~10%\n *\n * TODO: Perhaps it should be lazy initialized only if a charCode > 255 is used.\n */\nfunction initCharCodeToOptimizedIndexMap() {\n  if (isEmpty(charCodeToOptimizedIdxMap)) {\n    charCodeToOptimizedIdxMap = new Array(65536);\n    for (let i = 0; i < 65536; i++) {\n      charCodeToOptimizedIdxMap[i] = i > 255 ? 255 + ~~(i / 255) : i;\n    }\n  }\n}", "map": {"version": 3, "names": ["BaseRegExpVisitor", "<PERSON><PERSON>", "LexerDefinitionErrorType", "compact", "defaults", "difference", "filter", "find", "first", "flatten", "for<PERSON>ach", "has", "includes", "indexOf", "isArray", "isEmpty", "isFunction", "isRegExp", "isString", "isUndefined", "keys", "map", "reduce", "reject", "values", "PRINT_ERROR", "canMatchCharCode", "failedOptimizationPrefixMsg", "getOptimizedStartCodesIndices", "getRegExpAst", "PATTERN", "DEFAULT_MODE", "MODES", "SUPPORT_STICKY", "RegExp", "sticky", "disableSticky", "enableSticky", "analyzeTokenTypes", "tokenTypes", "options", "useSticky", "debug", "safeMode", "positionTracking", "lineTerminatorCharacters", "tracer", "msg", "action", "initCharCodeToOptimizedIndexMap", "onlyRelevantTypes", "currType", "NA", "hasCustom", "allTransformedPatterns", "currPattern", "regExpSource", "source", "length", "ignoreCase", "addStickyFlag", "addStartOfInput", "exec", "escapedRegExpString", "replace", "wrappedRegExp", "Error", "patternIdxToType", "patternIdxToGroup", "patternIdxToLongerAltIdxArr", "patternIdxToPushMode", "patternIdxToPopMode", "tokenTypeIdx", "clazz", "groupName", "GROUP", "SKIPPED", "undefined", "longerAltType", "LONGER_ALT", "longerAltIdxArr", "type", "PUSH_MODE", "patternIdxToCanLineTerminator", "lineTerminatorCharCodes", "getCharCodes", "tokType", "LINE_BREAKS", "checkLineBreaksIssues", "patternIdxToIsCustom", "patternIdxToShort", "emptyGroups", "patternIdxToConfig", "isCustomPattern", "isShortPattern", "acc", "x", "idx", "pattern", "longerAlt", "canLineTerminator", "isCustom", "short", "group", "push", "pop", "tokenType", "canBeOptimized", "charCodeToPatternIdxToConfig", "result", "currTokType", "charCode", "charCodeAt", "optimizedIdx", "charCodeToOptimizedIndex", "addToMapOfArrays", "START_CHARS_HINT", "lastOptimizedIdx", "charOrInt", "currOptimizedIdx", "unicode", "ensureOptimizations", "toString", "optimizedCodes", "code", "name", "validatePatterns", "validModesNames", "errors", "missingR<PERSON>ult", "findMissingPatterns", "concat", "invalidR<PERSON>ult", "findInvalidPatterns", "valid", "validTokenTypes", "validateRegExpPattern", "findInvalidGroupType", "findModesThatDoNotExist", "findUnreachablePatterns", "withRegExpPatterns", "findEndOfInputAnchor", "findStartOfInputAnchor", "findUnsupportedFlags", "findDuplicatePatterns", "findEmptyMatchRegExps", "tokenTypesWithMissingPattern", "message", "MISSING_PATTERN", "tokenTypesWithInvalidPattern", "INVALID_PATTERN", "end_of_input", "EndAnchorFinder", "constructor", "found", "visitEndAnchor", "node", "invalidRegex", "regexpAst", "endAnchorVisitor", "visit", "e", "test", "EOI_ANCHOR_FOUND", "matchesEmptyString", "EMPTY_MATCH_PATTERN", "start_of_input", "StartAnchorFinder", "visitStartAnchor", "startAnchorVisitor", "SOI_ANCHOR_FOUND", "invalidFlags", "multiline", "global", "UNSUPPORTED_FLAGS_FOUND", "identicalPatterns", "outerType", "innerType", "duplicatePatterns", "currIdenticalSet", "setOfIdentical", "tokenTypeNames", "dupPatternSrc", "join", "DUPLICATE_PATTERNS_FOUND", "invalidTypes", "INVALID_GROUP_TYPE_FOUND", "validModes", "invalidModes", "PUSH_MODE_DOES_NOT_EXIST", "canBeTested", "str", "noMetaChar", "testIdx", "testTokenType", "UNREACHABLE_PATTERN", "regExpArray", "index", "regExp", "metaChars", "char", "flags", "performRuntimeChecks", "lexerDefinition", "trackLines", "MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE", "MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY", "modes", "defaultMode", "MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST", "currModeValue", "currModeName", "currIdx", "LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED", "currLongerAlt", "MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE", "performWarningRuntimeChecks", "warnings", "hasAnyLineBreak", "allTokenTypes", "concreteTokenTypes", "terminatorCharCodes", "currIssue", "buildLineBreakIssueMessage", "warningDescriptor", "issue", "NO_LINE_BREAKS_FLAGS", "cloneEmptyGroups", "clonedResult", "groupKeys", "curr<PERSON><PERSON>", "currGroupValue", "LineTerminatorOptimizedTester", "text", "len", "i", "lastIndex", "c", "IDENTIFY_TERMINATOR", "errMsg", "CUSTOM_LINE_BREAK", "details", "charsOrCodes", "charCodes", "numOrString", "key", "value", "minOptimizationVal", "charCodeToOptimizedIdxMap", "Array"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/lexer.ts"], "sourcesContent": ["import { BaseRegExpVisitor } from \"@chevrotain/regexp-to-ast\";\nimport {\n  IRegExpExec,\n  <PERSON><PERSON>,\n  LexerDefinitionErrorType,\n} from \"./lexer_public.js\";\nimport {\n  compact,\n  defaults,\n  difference,\n  filter,\n  find,\n  first,\n  flatten,\n  forEach,\n  has,\n  includes,\n  indexOf,\n  isArray,\n  isEmpty,\n  isFunction,\n  isRegExp,\n  isString,\n  isUndefined,\n  keys,\n  map,\n  reduce,\n  reject,\n  values,\n} from \"lodash-es\";\nimport { PRINT_ERROR } from \"@chevrotain/utils\";\nimport {\n  canMatchCharCode,\n  failedOptimizationPrefixMsg,\n  getOptimizedStartCodesIndices,\n} from \"./reg_exp.js\";\nimport {\n  ILexerDefinitionError,\n  ILineTerminatorsTester,\n  IMultiModeLexerDefinition,\n  IToken,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { getRegExpAst } from \"./reg_exp_parser.js\";\n\nconst PATTERN = \"PATTERN\";\nexport const DEFAULT_MODE = \"defaultMode\";\nexport const MODES = \"modes\";\n\nexport interface IPatternConfig {\n  pattern: IRegExpExec | string;\n  longerAlt: number[] | undefined;\n  canLineTerminator: boolean;\n  isCustom: boolean;\n  short: number | false;\n  group: string | undefined | false;\n  push: string | undefined;\n  pop: boolean;\n  tokenType: TokenType;\n  tokenTypeIdx: number;\n}\n\nexport interface IAnalyzeResult {\n  patternIdxToConfig: IPatternConfig[];\n  charCodeToPatternIdxToConfig: { [charCode: number]: IPatternConfig[] };\n  emptyGroups: { [groupName: string]: IToken[] };\n  hasCustom: boolean;\n  canBeOptimized: boolean;\n}\n\nexport let SUPPORT_STICKY =\n  typeof (<any>new RegExp(\"(?:)\")).sticky === \"boolean\";\n\nexport function disableSticky() {\n  SUPPORT_STICKY = false;\n}\n\nexport function enableSticky() {\n  SUPPORT_STICKY = true;\n}\n\nexport function analyzeTokenTypes(\n  tokenTypes: TokenType[],\n  options: {\n    positionTracking?: \"full\" | \"onlyStart\" | \"onlyOffset\";\n    ensureOptimizations?: boolean;\n    lineTerminatorCharacters?: (number | string)[];\n    // TODO: should `useSticky` be an argument here?\n    useSticky?: boolean;\n    safeMode?: boolean;\n    tracer?: (msg: string, action: () => void) => void;\n  },\n): IAnalyzeResult {\n  options = defaults(options, {\n    useSticky: SUPPORT_STICKY,\n    debug: false as boolean,\n    safeMode: false as boolean,\n    positionTracking: \"full\",\n    lineTerminatorCharacters: [\"\\r\", \"\\n\"],\n    tracer: (msg: string, action: Function) => action(),\n  });\n\n  const tracer = options.tracer!;\n\n  tracer(\"initCharCodeToOptimizedIndexMap\", () => {\n    initCharCodeToOptimizedIndexMap();\n  });\n\n  let onlyRelevantTypes: TokenType[];\n  tracer(\"Reject Lexer.NA\", () => {\n    onlyRelevantTypes = reject(tokenTypes, (currType) => {\n      return currType[PATTERN] === Lexer.NA;\n    });\n  });\n\n  let hasCustom = false;\n  let allTransformedPatterns: (IRegExpExec | string)[];\n  tracer(\"Transform Patterns\", () => {\n    hasCustom = false;\n    allTransformedPatterns = map(\n      onlyRelevantTypes,\n      (currType): IRegExpExec | string => {\n        const currPattern = currType[PATTERN];\n\n        /* istanbul ignore else */\n        if (isRegExp(currPattern)) {\n          const regExpSource = currPattern.source;\n          if (\n            regExpSource.length === 1 &&\n            // only these regExp meta characters which can appear in a length one regExp\n            regExpSource !== \"^\" &&\n            regExpSource !== \"$\" &&\n            regExpSource !== \".\" &&\n            !currPattern.ignoreCase\n          ) {\n            return regExpSource;\n          } else if (\n            regExpSource.length === 2 &&\n            regExpSource[0] === \"\\\\\" &&\n            // not a meta character\n            !includes(\n              [\n                \"d\",\n                \"D\",\n                \"s\",\n                \"S\",\n                \"t\",\n                \"r\",\n                \"n\",\n                \"t\",\n                \"0\",\n                \"c\",\n                \"b\",\n                \"B\",\n                \"f\",\n                \"v\",\n                \"w\",\n                \"W\",\n              ],\n              regExpSource[1],\n            )\n          ) {\n            // escaped meta Characters: /\\+/ /\\[/\n            // or redundant escaping: /\\a/\n            // without the escaping \"\\\"\n            return regExpSource[1];\n          } else {\n            return options.useSticky\n              ? addStickyFlag(currPattern)\n              : addStartOfInput(currPattern);\n          }\n        } else if (isFunction(currPattern)) {\n          hasCustom = true;\n          // CustomPatternMatcherFunc - custom patterns do not require any transformations, only wrapping in a RegExp Like object\n          return { exec: currPattern };\n        } else if (typeof currPattern === \"object\") {\n          hasCustom = true;\n          // ICustomPattern\n          return currPattern;\n        } else if (typeof currPattern === \"string\") {\n          if (currPattern.length === 1) {\n            return currPattern;\n          } else {\n            const escapedRegExpString = currPattern.replace(\n              /[\\\\^$.*+?()[\\]{}|]/g,\n              \"\\\\$&\",\n            );\n            const wrappedRegExp = new RegExp(escapedRegExpString);\n            return options.useSticky\n              ? addStickyFlag(wrappedRegExp)\n              : addStartOfInput(wrappedRegExp);\n          }\n        } else {\n          throw Error(\"non exhaustive match\");\n        }\n      },\n    );\n  });\n\n  let patternIdxToType: number[];\n  let patternIdxToGroup: (string | undefined | false)[];\n  let patternIdxToLongerAltIdxArr: (number[] | undefined)[];\n  let patternIdxToPushMode: (string | undefined)[];\n  let patternIdxToPopMode: boolean[];\n  tracer(\"misc mapping\", () => {\n    patternIdxToType = map(\n      onlyRelevantTypes,\n      (currType) => currType.tokenTypeIdx!,\n    );\n\n    patternIdxToGroup = map(onlyRelevantTypes, (clazz: any) => {\n      const groupName = clazz.GROUP;\n      /* istanbul ignore next */\n      if (groupName === Lexer.SKIPPED) {\n        return undefined;\n      } else if (isString(groupName)) {\n        return groupName;\n      } else if (isUndefined(groupName)) {\n        return false;\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    });\n\n    patternIdxToLongerAltIdxArr = map(onlyRelevantTypes, (clazz: any) => {\n      const longerAltType = clazz.LONGER_ALT;\n\n      if (longerAltType) {\n        const longerAltIdxArr = isArray(longerAltType)\n          ? map(longerAltType, (type: any) => indexOf(onlyRelevantTypes, type))\n          : [indexOf(onlyRelevantTypes, longerAltType)];\n        return longerAltIdxArr;\n      }\n    });\n\n    patternIdxToPushMode = map(\n      onlyRelevantTypes,\n      (clazz: any) => clazz.PUSH_MODE,\n    );\n\n    patternIdxToPopMode = map(onlyRelevantTypes, (clazz: any) =>\n      has(clazz, \"POP_MODE\"),\n    );\n  });\n\n  let patternIdxToCanLineTerminator: boolean[];\n  tracer(\"Line Terminator Handling\", () => {\n    const lineTerminatorCharCodes = getCharCodes(\n      options.lineTerminatorCharacters!,\n    );\n    patternIdxToCanLineTerminator = map(onlyRelevantTypes, (tokType) => false);\n    if (options.positionTracking !== \"onlyOffset\") {\n      patternIdxToCanLineTerminator = map(onlyRelevantTypes, (tokType) => {\n        if (has(tokType, \"LINE_BREAKS\")) {\n          return !!tokType.LINE_BREAKS;\n        } else {\n          return (\n            checkLineBreaksIssues(tokType, lineTerminatorCharCodes) === false &&\n            canMatchCharCode(\n              lineTerminatorCharCodes,\n              tokType.PATTERN as RegExp | string,\n            )\n          );\n        }\n      });\n    }\n  });\n\n  let patternIdxToIsCustom: boolean[];\n  let patternIdxToShort: (number | false)[];\n  let emptyGroups!: { [groupName: string]: IToken[] };\n  let patternIdxToConfig!: IPatternConfig[];\n  tracer(\"Misc Mapping #2\", () => {\n    patternIdxToIsCustom = map(onlyRelevantTypes, isCustomPattern);\n    patternIdxToShort = map(allTransformedPatterns, isShortPattern);\n\n    emptyGroups = reduce(\n      onlyRelevantTypes,\n      (acc, clazz: any) => {\n        const groupName = clazz.GROUP;\n        if (isString(groupName) && !(groupName === Lexer.SKIPPED)) {\n          acc[groupName] = [];\n        }\n        return acc;\n      },\n      {} as { [groupName: string]: IToken[] },\n    );\n\n    patternIdxToConfig = map(\n      allTransformedPatterns,\n      (x, idx): IPatternConfig => {\n        return {\n          pattern: allTransformedPatterns[idx],\n          longerAlt: patternIdxToLongerAltIdxArr[idx],\n          canLineTerminator: patternIdxToCanLineTerminator[idx],\n          isCustom: patternIdxToIsCustom[idx],\n          short: patternIdxToShort[idx],\n          group: patternIdxToGroup[idx],\n          push: patternIdxToPushMode[idx],\n          pop: patternIdxToPopMode[idx],\n          tokenTypeIdx: patternIdxToType[idx],\n          tokenType: onlyRelevantTypes[idx],\n        };\n      },\n    );\n  });\n\n  let canBeOptimized = true;\n  let charCodeToPatternIdxToConfig: { [charCode: number]: IPatternConfig[] } =\n    [];\n\n  if (!options.safeMode) {\n    tracer(\"First Char Optimization\", () => {\n      charCodeToPatternIdxToConfig = reduce(\n        onlyRelevantTypes,\n        (result, currTokType, idx) => {\n          if (typeof currTokType.PATTERN === \"string\") {\n            const charCode = currTokType.PATTERN.charCodeAt(0);\n            const optimizedIdx = charCodeToOptimizedIndex(charCode);\n            addToMapOfArrays(result, optimizedIdx, patternIdxToConfig[idx]);\n          } else if (isArray(currTokType.START_CHARS_HINT)) {\n            let lastOptimizedIdx: number;\n            forEach(currTokType.START_CHARS_HINT, (charOrInt) => {\n              const charCode =\n                typeof charOrInt === \"string\"\n                  ? charOrInt.charCodeAt(0)\n                  : charOrInt;\n              const currOptimizedIdx = charCodeToOptimizedIndex(charCode);\n              // Avoid adding the config multiple times\n              /* istanbul ignore else */\n              // - Difficult to check this scenario effects as it is only a performance\n              //   optimization that does not change correctness\n              if (lastOptimizedIdx !== currOptimizedIdx) {\n                lastOptimizedIdx = currOptimizedIdx;\n                addToMapOfArrays(\n                  result,\n                  currOptimizedIdx,\n                  patternIdxToConfig[idx],\n                );\n              }\n            });\n          } else if (isRegExp(currTokType.PATTERN)) {\n            if (currTokType.PATTERN.unicode) {\n              canBeOptimized = false;\n              if (options.ensureOptimizations) {\n                PRINT_ERROR(\n                  `${failedOptimizationPrefixMsg}` +\n                    `\\tUnable to analyze < ${currTokType.PATTERN.toString()} > pattern.\\n` +\n                    \"\\tThe regexp unicode flag is not currently supported by the regexp-to-ast library.\\n\" +\n                    \"\\tThis will disable the lexer's first char optimizations.\\n\" +\n                    \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE\",\n                );\n              }\n            } else {\n              const optimizedCodes = getOptimizedStartCodesIndices(\n                currTokType.PATTERN,\n                options.ensureOptimizations,\n              );\n              /* istanbul ignore if */\n              // start code will only be empty given an empty regExp or failure of regexp-to-ast library\n              // the first should be a different validation and the second cannot be tested.\n              if (isEmpty(optimizedCodes)) {\n                // we cannot understand what codes may start possible matches\n                // The optimization correctness requires knowing start codes for ALL patterns.\n                // Not actually sure this is an error, no debug message\n                canBeOptimized = false;\n              }\n              forEach(optimizedCodes, (code) => {\n                addToMapOfArrays(result, code, patternIdxToConfig[idx]);\n              });\n            }\n          } else {\n            if (options.ensureOptimizations) {\n              PRINT_ERROR(\n                `${failedOptimizationPrefixMsg}` +\n                  `\\tTokenType: <${currTokType.name}> is using a custom token pattern without providing <start_chars_hint> parameter.\\n` +\n                  \"\\tThis will disable the lexer's first char optimizations.\\n\" +\n                  \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE\",\n              );\n            }\n            canBeOptimized = false;\n          }\n\n          return result;\n        },\n        [] as { [charCode: number]: IPatternConfig[] },\n      );\n    });\n  }\n\n  return {\n    emptyGroups: emptyGroups,\n    patternIdxToConfig: patternIdxToConfig,\n    charCodeToPatternIdxToConfig: charCodeToPatternIdxToConfig,\n    hasCustom: hasCustom,\n    canBeOptimized: canBeOptimized,\n  };\n}\n\nexport function validatePatterns(\n  tokenTypes: TokenType[],\n  validModesNames: string[],\n): ILexerDefinitionError[] {\n  let errors: ILexerDefinitionError[] = [];\n\n  const missingResult = findMissingPatterns(tokenTypes);\n  errors = errors.concat(missingResult.errors);\n\n  const invalidResult = findInvalidPatterns(missingResult.valid);\n  const validTokenTypes = invalidResult.valid;\n  errors = errors.concat(invalidResult.errors);\n\n  errors = errors.concat(validateRegExpPattern(validTokenTypes));\n\n  errors = errors.concat(findInvalidGroupType(validTokenTypes));\n\n  errors = errors.concat(\n    findModesThatDoNotExist(validTokenTypes, validModesNames),\n  );\n\n  errors = errors.concat(findUnreachablePatterns(validTokenTypes));\n\n  return errors;\n}\n\nfunction validateRegExpPattern(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  let errors: ILexerDefinitionError[] = [];\n  const withRegExpPatterns = filter(tokenTypes, (currTokType) =>\n    isRegExp(currTokType[PATTERN]),\n  );\n\n  errors = errors.concat(findEndOfInputAnchor(withRegExpPatterns));\n\n  errors = errors.concat(findStartOfInputAnchor(withRegExpPatterns));\n\n  errors = errors.concat(findUnsupportedFlags(withRegExpPatterns));\n\n  errors = errors.concat(findDuplicatePatterns(withRegExpPatterns));\n\n  errors = errors.concat(findEmptyMatchRegExps(withRegExpPatterns));\n\n  return errors;\n}\n\nexport interface ILexerFilterResult {\n  errors: ILexerDefinitionError[];\n  valid: TokenType[];\n}\n\nexport function findMissingPatterns(\n  tokenTypes: TokenType[],\n): ILexerFilterResult {\n  const tokenTypesWithMissingPattern = filter(tokenTypes, (currType) => {\n    return !has(currType, PATTERN);\n  });\n\n  const errors = map(tokenTypesWithMissingPattern, (currType) => {\n    return {\n      message:\n        \"Token Type: ->\" +\n        currType.name +\n        \"<- missing static 'PATTERN' property\",\n      type: LexerDefinitionErrorType.MISSING_PATTERN,\n      tokenTypes: [currType],\n    };\n  });\n\n  const valid = difference(tokenTypes, tokenTypesWithMissingPattern);\n  return { errors, valid };\n}\n\nexport function findInvalidPatterns(\n  tokenTypes: TokenType[],\n): ILexerFilterResult {\n  const tokenTypesWithInvalidPattern = filter(tokenTypes, (currType) => {\n    const pattern = currType[PATTERN];\n    return (\n      !isRegExp(pattern) &&\n      !isFunction(pattern) &&\n      !has(pattern, \"exec\") &&\n      !isString(pattern)\n    );\n  });\n\n  const errors = map(tokenTypesWithInvalidPattern, (currType) => {\n    return {\n      message:\n        \"Token Type: ->\" +\n        currType.name +\n        \"<- static 'PATTERN' can only be a RegExp, a\" +\n        \" Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.\",\n      type: LexerDefinitionErrorType.INVALID_PATTERN,\n      tokenTypes: [currType],\n    };\n  });\n\n  const valid = difference(tokenTypes, tokenTypesWithInvalidPattern);\n  return { errors, valid };\n}\n\nconst end_of_input = /[^\\\\][$]/;\n\nexport function findEndOfInputAnchor(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  class EndAnchorFinder extends BaseRegExpVisitor {\n    found = false;\n\n    visitEndAnchor(node: unknown) {\n      this.found = true;\n    }\n  }\n\n  const invalidRegex = filter(tokenTypes, (currType) => {\n    const pattern = currType.PATTERN;\n\n    try {\n      const regexpAst = getRegExpAst(pattern as RegExp);\n      const endAnchorVisitor = new EndAnchorFinder();\n      endAnchorVisitor.visit(regexpAst);\n\n      return endAnchorVisitor.found;\n    } catch (e) {\n      // old behavior in case of runtime exceptions with regexp-to-ast.\n      /* istanbul ignore next - cannot ensure an error in regexp-to-ast*/\n      return end_of_input.test((pattern as RegExp).source);\n    }\n  });\n\n  const errors = map(invalidRegex, (currType) => {\n    return {\n      message:\n        \"Unexpected RegExp Anchor Error:\\n\" +\n        \"\\tToken Type: ->\" +\n        currType.name +\n        \"<- static 'PATTERN' cannot contain end of input anchor '$'\\n\" +\n        \"\\tSee chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\" +\n        \"\\tfor details.\",\n      type: LexerDefinitionErrorType.EOI_ANCHOR_FOUND,\n      tokenTypes: [currType],\n    };\n  });\n\n  return errors;\n}\n\nexport function findEmptyMatchRegExps(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  const matchesEmptyString = filter(tokenTypes, (currType) => {\n    const pattern = currType.PATTERN as RegExp;\n    return pattern.test(\"\");\n  });\n\n  const errors = map(matchesEmptyString, (currType) => {\n    return {\n      message:\n        \"Token Type: ->\" +\n        currType.name +\n        \"<- static 'PATTERN' must not match an empty string\",\n      type: LexerDefinitionErrorType.EMPTY_MATCH_PATTERN,\n      tokenTypes: [currType],\n    };\n  });\n\n  return errors;\n}\n\nconst start_of_input = /[^\\\\[][\\^]|^\\^/;\n\nexport function findStartOfInputAnchor(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  class StartAnchorFinder extends BaseRegExpVisitor {\n    found = false;\n\n    visitStartAnchor(node: unknown) {\n      this.found = true;\n    }\n  }\n\n  const invalidRegex = filter(tokenTypes, (currType) => {\n    const pattern = currType.PATTERN as RegExp;\n    try {\n      const regexpAst = getRegExpAst(pattern);\n      const startAnchorVisitor = new StartAnchorFinder();\n      startAnchorVisitor.visit(regexpAst);\n\n      return startAnchorVisitor.found;\n    } catch (e) {\n      // old behavior in case of runtime exceptions with regexp-to-ast.\n      /* istanbul ignore next - cannot ensure an error in regexp-to-ast*/\n      return start_of_input.test(pattern.source);\n    }\n  });\n\n  const errors = map(invalidRegex, (currType) => {\n    return {\n      message:\n        \"Unexpected RegExp Anchor Error:\\n\" +\n        \"\\tToken Type: ->\" +\n        currType.name +\n        \"<- static 'PATTERN' cannot contain start of input anchor '^'\\n\" +\n        \"\\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\" +\n        \"\\tfor details.\",\n      type: LexerDefinitionErrorType.SOI_ANCHOR_FOUND,\n      tokenTypes: [currType],\n    };\n  });\n\n  return errors;\n}\n\nexport function findUnsupportedFlags(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  const invalidFlags = filter(tokenTypes, (currType) => {\n    const pattern = currType[PATTERN];\n    return pattern instanceof RegExp && (pattern.multiline || pattern.global);\n  });\n\n  const errors = map(invalidFlags, (currType) => {\n    return {\n      message:\n        \"Token Type: ->\" +\n        currType.name +\n        \"<- static 'PATTERN' may NOT contain global('g') or multiline('m')\",\n      type: LexerDefinitionErrorType.UNSUPPORTED_FLAGS_FOUND,\n      tokenTypes: [currType],\n    };\n  });\n\n  return errors;\n}\n\n// This can only test for identical duplicate RegExps, not semantically equivalent ones.\nexport function findDuplicatePatterns(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  const found: TokenType[] = [];\n  let identicalPatterns = map(tokenTypes, (outerType: any) => {\n    return reduce(\n      tokenTypes,\n      (result, innerType) => {\n        if (\n          outerType.PATTERN.source === (innerType.PATTERN as RegExp).source &&\n          !includes(found, innerType) &&\n          innerType.PATTERN !== Lexer.NA\n        ) {\n          // this avoids duplicates in the result, each Token Type may only appear in one \"set\"\n          // in essence we are creating Equivalence classes on equality relation.\n          found.push(innerType);\n          result.push(innerType);\n          return result;\n        }\n        return result;\n      },\n      [] as TokenType[],\n    );\n  });\n\n  identicalPatterns = compact(identicalPatterns);\n\n  const duplicatePatterns = filter(identicalPatterns, (currIdenticalSet) => {\n    return currIdenticalSet.length > 1;\n  });\n\n  const errors = map(duplicatePatterns, (setOfIdentical: any) => {\n    const tokenTypeNames = map(setOfIdentical, (currType: any) => {\n      return currType.name;\n    });\n\n    const dupPatternSrc = (<any>first(setOfIdentical)).PATTERN;\n    return {\n      message:\n        `The same RegExp pattern ->${dupPatternSrc}<-` +\n        `has been used in all of the following Token Types: ${tokenTypeNames.join(\n          \", \",\n        )} <-`,\n      type: LexerDefinitionErrorType.DUPLICATE_PATTERNS_FOUND,\n      tokenTypes: setOfIdentical,\n    };\n  });\n\n  return errors;\n}\n\nexport function findInvalidGroupType(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  const invalidTypes = filter(tokenTypes, (clazz: any) => {\n    if (!has(clazz, \"GROUP\")) {\n      return false;\n    }\n    const group = clazz.GROUP;\n\n    return group !== Lexer.SKIPPED && group !== Lexer.NA && !isString(group);\n  });\n\n  const errors = map(invalidTypes, (currType) => {\n    return {\n      message:\n        \"Token Type: ->\" +\n        currType.name +\n        \"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String\",\n      type: LexerDefinitionErrorType.INVALID_GROUP_TYPE_FOUND,\n      tokenTypes: [currType],\n    };\n  });\n\n  return errors;\n}\n\nexport function findModesThatDoNotExist(\n  tokenTypes: TokenType[],\n  validModes: string[],\n): ILexerDefinitionError[] {\n  const invalidModes = filter(tokenTypes, (clazz: any) => {\n    return (\n      clazz.PUSH_MODE !== undefined && !includes(validModes, clazz.PUSH_MODE)\n    );\n  });\n\n  const errors = map(invalidModes, (tokType) => {\n    const msg =\n      `Token Type: ->${tokType.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${tokType.PUSH_MODE}<-` +\n      `which does not exist`;\n    return {\n      message: msg,\n      type: LexerDefinitionErrorType.PUSH_MODE_DOES_NOT_EXIST,\n      tokenTypes: [tokType],\n    };\n  });\n\n  return errors;\n}\n\nexport function findUnreachablePatterns(\n  tokenTypes: TokenType[],\n): ILexerDefinitionError[] {\n  const errors: ILexerDefinitionError[] = [];\n\n  const canBeTested = reduce(\n    tokenTypes,\n    (result, tokType, idx) => {\n      const pattern = tokType.PATTERN;\n\n      if (pattern === Lexer.NA) {\n        return result;\n      }\n\n      // a more comprehensive validation for all forms of regExps would require\n      // deeper regExp analysis capabilities\n      if (isString(pattern)) {\n        result.push({ str: pattern, idx, tokenType: tokType });\n      } else if (isRegExp(pattern) && noMetaChar(pattern)) {\n        result.push({ str: pattern.source, idx, tokenType: tokType });\n      }\n      return result;\n    },\n    [] as { str: string; idx: number; tokenType: TokenType }[],\n  );\n\n  forEach(tokenTypes, (tokType, testIdx) => {\n    forEach(canBeTested, ({ str, idx, tokenType }) => {\n      if (testIdx < idx && testTokenType(str, tokType.PATTERN)) {\n        const msg =\n          `Token: ->${tokenType.name}<- can never be matched.\\n` +\n          `Because it appears AFTER the Token Type ->${tokType.name}<-` +\n          `in the lexer's definition.\\n` +\n          `See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;\n        errors.push({\n          message: msg,\n          type: LexerDefinitionErrorType.UNREACHABLE_PATTERN,\n          tokenTypes: [tokType, tokenType],\n        });\n      }\n    });\n  });\n\n  return errors;\n}\n\nfunction testTokenType(str: string, pattern: any): boolean {\n  /* istanbul ignore else */\n  if (isRegExp(pattern)) {\n    const regExpArray = pattern.exec(str);\n    return regExpArray !== null && regExpArray.index === 0;\n  } else if (isFunction(pattern)) {\n    // maintain the API of custom patterns\n    return pattern(str, 0, [], {});\n  } else if (has(pattern, \"exec\")) {\n    // maintain the API of custom patterns\n    return pattern.exec(str, 0, [], {});\n  } else if (typeof pattern === \"string\") {\n    return pattern === str;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n\nfunction noMetaChar(regExp: RegExp): boolean {\n  //https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp\n  const metaChars = [\n    \".\",\n    \"\\\\\",\n    \"[\",\n    \"]\",\n    \"|\",\n    \"^\",\n    \"$\",\n    \"(\",\n    \")\",\n    \"?\",\n    \"*\",\n    \"+\",\n    \"{\",\n  ];\n  return (\n    find(metaChars, (char) => regExp.source.indexOf(char) !== -1) === undefined\n  );\n}\n\nexport function addStartOfInput(pattern: RegExp): RegExp {\n  const flags = pattern.ignoreCase ? \"i\" : \"\";\n  // always wrapping in a none capturing group preceded by '^' to make sure matching can only work on start of input.\n  // duplicate/redundant start of input markers have no meaning (/^^^^A/ === /^A/)\n  return new RegExp(`^(?:${pattern.source})`, flags);\n}\n\nexport function addStickyFlag(pattern: RegExp): RegExp {\n  const flags = pattern.ignoreCase ? \"iy\" : \"y\";\n  // always wrapping in a none capturing group preceded by '^' to make sure matching can only work on start of input.\n  // duplicate/redundant start of input markers have no meaning (/^^^^A/ === /^A/)\n  return new RegExp(`${pattern.source}`, flags);\n}\n\nexport function performRuntimeChecks(\n  lexerDefinition: IMultiModeLexerDefinition,\n  trackLines: boolean,\n  lineTerminatorCharacters: (number | string)[],\n): ILexerDefinitionError[] {\n  const errors: ILexerDefinitionError[] = [];\n\n  // some run time checks to help the end users.\n  if (!has(lexerDefinition, DEFAULT_MODE)) {\n    errors.push({\n      message:\n        \"A MultiMode Lexer cannot be initialized without a <\" +\n        DEFAULT_MODE +\n        \"> property in its definition\\n\",\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE,\n    });\n  }\n  if (!has(lexerDefinition, MODES)) {\n    errors.push({\n      message:\n        \"A MultiMode Lexer cannot be initialized without a <\" +\n        MODES +\n        \"> property in its definition\\n\",\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY,\n    });\n  }\n\n  if (\n    has(lexerDefinition, MODES) &&\n    has(lexerDefinition, DEFAULT_MODE) &&\n    !has(lexerDefinition.modes, lexerDefinition.defaultMode)\n  ) {\n    errors.push({\n      message:\n        `A MultiMode Lexer cannot be initialized with a ${DEFAULT_MODE}: <${lexerDefinition.defaultMode}>` +\n        `which does not exist\\n`,\n      type: LexerDefinitionErrorType.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST,\n    });\n  }\n\n  if (has(lexerDefinition, MODES)) {\n    forEach(lexerDefinition.modes, (currModeValue, currModeName) => {\n      forEach(currModeValue, (currTokType, currIdx) => {\n        if (isUndefined(currTokType)) {\n          errors.push({\n            message:\n              `A Lexer cannot be initialized using an undefined Token Type. Mode:` +\n              `<${currModeName}> at index: <${currIdx}>\\n`,\n            type: LexerDefinitionErrorType.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED,\n          });\n        } else if (has(currTokType, \"LONGER_ALT\")) {\n          const longerAlt = isArray(currTokType.LONGER_ALT)\n            ? currTokType.LONGER_ALT\n            : [currTokType.LONGER_ALT];\n          forEach(longerAlt, (currLongerAlt) => {\n            if (\n              !isUndefined(currLongerAlt) &&\n              !includes(currModeValue, currLongerAlt)\n            ) {\n              errors.push({\n                message: `A MultiMode Lexer cannot be initialized with a longer_alt <${currLongerAlt.name}> on token <${currTokType.name}> outside of mode <${currModeName}>\\n`,\n                type: LexerDefinitionErrorType.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE,\n              });\n            }\n          });\n        }\n      });\n    });\n  }\n\n  return errors;\n}\n\nexport function performWarningRuntimeChecks(\n  lexerDefinition: IMultiModeLexerDefinition,\n  trackLines: boolean,\n  lineTerminatorCharacters: (number | string)[],\n): ILexerDefinitionError[] {\n  const warnings = [];\n  let hasAnyLineBreak = false;\n  const allTokenTypes = compact(flatten(values(lexerDefinition.modes)));\n\n  const concreteTokenTypes = reject(\n    allTokenTypes,\n    (currType) => currType[PATTERN] === Lexer.NA,\n  );\n  const terminatorCharCodes = getCharCodes(lineTerminatorCharacters);\n  if (trackLines) {\n    forEach(concreteTokenTypes, (tokType) => {\n      const currIssue = checkLineBreaksIssues(tokType, terminatorCharCodes);\n      if (currIssue !== false) {\n        const message = buildLineBreakIssueMessage(tokType, currIssue);\n        const warningDescriptor = {\n          message,\n          type: currIssue.issue,\n          tokenType: tokType,\n        };\n        warnings.push(warningDescriptor);\n      } else {\n        // we don't want to attempt to scan if the user explicitly specified the line_breaks option.\n        if (has(tokType, \"LINE_BREAKS\")) {\n          if (tokType.LINE_BREAKS === true) {\n            hasAnyLineBreak = true;\n          }\n        } else {\n          if (\n            canMatchCharCode(terminatorCharCodes, tokType.PATTERN as RegExp)\n          ) {\n            hasAnyLineBreak = true;\n          }\n        }\n      }\n    });\n  }\n\n  if (trackLines && !hasAnyLineBreak) {\n    warnings.push({\n      message:\n        \"Warning: No LINE_BREAKS Found.\\n\" +\n        \"\\tThis Lexer has been defined to track line and column information,\\n\" +\n        \"\\tBut none of the Token Types can be identified as matching a line terminator.\\n\" +\n        \"\\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \\n\" +\n        \"\\tfor details.\",\n      type: LexerDefinitionErrorType.NO_LINE_BREAKS_FLAGS,\n    });\n  }\n  return warnings;\n}\n\nexport function cloneEmptyGroups(emptyGroups: {\n  [groupName: string]: IToken;\n}): { [groupName: string]: IToken } {\n  const clonedResult: any = {};\n  const groupKeys = keys(emptyGroups);\n\n  forEach(groupKeys, (currKey) => {\n    const currGroupValue = emptyGroups[currKey];\n\n    /* istanbul ignore else */\n    if (isArray(currGroupValue)) {\n      clonedResult[currKey] = [];\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  });\n\n  return clonedResult;\n}\n\n// TODO: refactor to avoid duplication\nexport function isCustomPattern(tokenType: TokenType): boolean {\n  const pattern = tokenType.PATTERN;\n  /* istanbul ignore else */\n  if (isRegExp(pattern)) {\n    return false;\n  } else if (isFunction(pattern)) {\n    // CustomPatternMatcherFunc - custom patterns do not require any transformations, only wrapping in a RegExp Like object\n    return true;\n  } else if (has(pattern, \"exec\")) {\n    // ICustomPattern\n    return true;\n  } else if (isString(pattern)) {\n    return false;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n\nexport function isShortPattern(pattern: any): number | false {\n  if (isString(pattern) && pattern.length === 1) {\n    return pattern.charCodeAt(0);\n  } else {\n    return false;\n  }\n}\n\n/**\n * Faster than using a RegExp for default newline detection during lexing.\n */\nexport const LineTerminatorOptimizedTester: ILineTerminatorsTester = {\n  // implements /\\n|\\r\\n?/g.test\n  test: function (text) {\n    const len = text.length;\n    for (let i = this.lastIndex; i < len; i++) {\n      const c = text.charCodeAt(i);\n      if (c === 10) {\n        this.lastIndex = i + 1;\n        return true;\n      } else if (c === 13) {\n        if (text.charCodeAt(i + 1) === 10) {\n          this.lastIndex = i + 2;\n        } else {\n          this.lastIndex = i + 1;\n        }\n        return true;\n      }\n    }\n    return false;\n  },\n\n  lastIndex: 0,\n};\n\nfunction checkLineBreaksIssues(\n  tokType: TokenType,\n  lineTerminatorCharCodes: number[],\n):\n  | {\n      issue:\n        | LexerDefinitionErrorType.IDENTIFY_TERMINATOR\n        | LexerDefinitionErrorType.CUSTOM_LINE_BREAK;\n      errMsg?: string;\n    }\n  | false {\n  if (has(tokType, \"LINE_BREAKS\")) {\n    // if the user explicitly declared the line_breaks option we will respect their choice\n    // and assume it is correct.\n    return false;\n  } else {\n    /* istanbul ignore else */\n    if (isRegExp(tokType.PATTERN)) {\n      try {\n        // TODO: why is the casting suddenly needed?\n        canMatchCharCode(lineTerminatorCharCodes, tokType.PATTERN as RegExp);\n      } catch (e) {\n        /* istanbul ignore next - to test this we would have to mock <canMatchCharCode> to throw an error */\n        return {\n          issue: LexerDefinitionErrorType.IDENTIFY_TERMINATOR,\n          errMsg: (e as Error).message,\n        };\n      }\n      return false;\n    } else if (isString(tokType.PATTERN)) {\n      // string literal patterns can always be analyzed to detect line terminator usage\n      return false;\n    } else if (isCustomPattern(tokType)) {\n      // custom token types\n      return { issue: LexerDefinitionErrorType.CUSTOM_LINE_BREAK };\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  }\n}\n\nexport function buildLineBreakIssueMessage(\n  tokType: TokenType,\n  details: {\n    issue:\n      | LexerDefinitionErrorType.IDENTIFY_TERMINATOR\n      | LexerDefinitionErrorType.CUSTOM_LINE_BREAK;\n    errMsg?: string;\n  },\n): string {\n  /* istanbul ignore else */\n  if (details.issue === LexerDefinitionErrorType.IDENTIFY_TERMINATOR) {\n    return (\n      \"Warning: unable to identify line terminator usage in pattern.\\n\" +\n      `\\tThe problem is in the <${tokType.name}> Token Type\\n` +\n      `\\t Root cause: ${details.errMsg}.\\n` +\n      \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR\"\n    );\n  } else if (details.issue === LexerDefinitionErrorType.CUSTOM_LINE_BREAK) {\n    return (\n      \"Warning: A Custom Token Pattern should specify the <line_breaks> option.\\n\" +\n      `\\tThe problem is in the <${tokType.name}> Token Type\\n` +\n      \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK\"\n    );\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n\nfunction getCharCodes(charsOrCodes: (number | string)[]): number[] {\n  const charCodes = map(charsOrCodes, (numOrString) => {\n    if (isString(numOrString)) {\n      return numOrString.charCodeAt(0);\n    } else {\n      return numOrString;\n    }\n  });\n\n  return charCodes;\n}\n\nfunction addToMapOfArrays<T>(\n  map: Record<number, T[]>,\n  key: number,\n  value: T,\n): void {\n  if (map[key] === undefined) {\n    map[key] = [value];\n  } else {\n    map[key].push(value);\n  }\n}\n\nexport const minOptimizationVal = 256;\n\n/**\n * We are mapping charCode above ASCI (256) into buckets each in the size of 256.\n * This is because ASCI are the most common start chars so each one of those will get its own\n * possible token configs vector.\n *\n * Tokens starting with charCodes \"above\" ASCI are uncommon, so we can \"afford\"\n * to place these into buckets of possible token configs, What we gain from\n * this is avoiding the case of creating an optimization 'charCodeToPatternIdxToConfig'\n * which would contain 10,000+ arrays of small size (e.g unicode Identifiers scenario).\n * Our 'charCodeToPatternIdxToConfig' max size will now be:\n * 256 + (2^16 / 2^8) - 1 === 511\n *\n * note the hack for fast division integer part extraction\n * See: https://stackoverflow.com/a/4228528\n */\nlet charCodeToOptimizedIdxMap: number[] = [];\nexport function charCodeToOptimizedIndex(charCode: number): number {\n  return charCode < minOptimizationVal\n    ? charCode\n    : charCodeToOptimizedIdxMap[charCode];\n}\n\n/**\n * This is a compromise between cold start / hot running performance\n * Creating this array takes ~3ms on a modern machine,\n * But if we perform the computation at runtime as needed the CSS Lexer benchmark\n * performance degrades by ~10%\n *\n * TODO: Perhaps it should be lazy initialized only if a charCode > 255 is used.\n */\nfunction initCharCodeToOptimizedIndexMap() {\n  if (isEmpty(charCodeToOptimizedIdxMap)) {\n    charCodeToOptimizedIdxMap = new Array(65536);\n    for (let i = 0; i < 65536; i++) {\n      charCodeToOptimizedIdxMap[i] = i > 255 ? 255 + ~~(i / 255) : i;\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,2BAA2B;AAC7D,SAEEC,KAAK,EACLC,wBAAwB,QACnB,mBAAmB;AAC1B,SACEC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,QACD,WAAW;AAClB,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SACEC,gBAAgB,EAChBC,2BAA2B,EAC3BC,6BAA6B,QACxB,cAAc;AAQrB,SAASC,YAAY,QAAQ,qBAAqB;AAElD,MAAMC,OAAO,GAAG,SAAS;AACzB,OAAO,MAAMC,YAAY,GAAG,aAAa;AACzC,OAAO,MAAMC,KAAK,GAAG,OAAO;AAuB5B,OAAO,IAAIC,cAAc,GACvB,OAAa,IAAIC,MAAM,CAAC,MAAM,CAAE,CAACC,MAAM,KAAK,SAAS;AAEvD,OAAM,SAAUC,aAAaA,CAAA;EAC3BH,cAAc,GAAG,KAAK;AACxB;AAEA,OAAM,SAAUI,YAAYA,CAAA;EAC1BJ,cAAc,GAAG,IAAI;AACvB;AAEA,OAAM,SAAUK,iBAAiBA,CAC/BC,UAAuB,EACvBC,OAQC;EAEDA,OAAO,GAAGpC,QAAQ,CAACoC,OAAO,EAAE;IAC1BC,SAAS,EAAER,cAAc;IACzBS,KAAK,EAAE,KAAgB;IACvBC,QAAQ,EAAE,KAAgB;IAC1BC,gBAAgB,EAAE,MAAM;IACxBC,wBAAwB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACtCC,MAAM,EAAEA,CAACC,GAAW,EAAEC,MAAgB,KAAKA,MAAM;GAClD,CAAC;EAEF,MAAMF,MAAM,GAAGN,OAAO,CAACM,MAAO;EAE9BA,MAAM,CAAC,iCAAiC,EAAE,MAAK;IAC7CG,+BAA+B,EAAE;EACnC,CAAC,CAAC;EAEF,IAAIC,iBAA8B;EAClCJ,MAAM,CAAC,iBAAiB,EAAE,MAAK;IAC7BI,iBAAiB,GAAG3B,MAAM,CAACgB,UAAU,EAAGY,QAAQ,IAAI;MAClD,OAAOA,QAAQ,CAACrB,OAAO,CAAC,KAAK7B,KAAK,CAACmD,EAAE;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,sBAAgD;EACpDR,MAAM,CAAC,oBAAoB,EAAE,MAAK;IAChCO,SAAS,GAAG,KAAK;IACjBC,sBAAsB,GAAGjC,GAAG,CAC1B6B,iBAAiB,EAChBC,QAAQ,IAA0B;MACjC,MAAMI,WAAW,GAAGJ,QAAQ,CAACrB,OAAO,CAAC;MAErC;MACA,IAAIb,QAAQ,CAACsC,WAAW,CAAC,EAAE;QACzB,MAAMC,YAAY,GAAGD,WAAW,CAACE,MAAM;QACvC,IACED,YAAY,CAACE,MAAM,KAAK,CAAC;QACzB;QACAF,YAAY,KAAK,GAAG,IACpBA,YAAY,KAAK,GAAG,IACpBA,YAAY,KAAK,GAAG,IACpB,CAACD,WAAW,CAACI,UAAU,EACvB;UACA,OAAOH,YAAY;SACpB,MAAM,IACLA,YAAY,CAACE,MAAM,KAAK,CAAC,IACzBF,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI;QACxB;QACA,CAAC5C,QAAQ,CACP,CACE,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ,EACD4C,YAAY,CAAC,CAAC,CAAC,CAChB,EACD;UACA;UACA;UACA;UACA,OAAOA,YAAY,CAAC,CAAC,CAAC;SACvB,MAAM;UACL,OAAOhB,OAAO,CAACC,SAAS,GACpBmB,aAAa,CAACL,WAAW,CAAC,GAC1BM,eAAe,CAACN,WAAW,CAAC;;OAEnC,MAAM,IAAIvC,UAAU,CAACuC,WAAW,CAAC,EAAE;QAClCF,SAAS,GAAG,IAAI;QAChB;QACA,OAAO;UAAES,IAAI,EAAEP;QAAW,CAAE;OAC7B,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QAC1CF,SAAS,GAAG,IAAI;QAChB;QACA,OAAOE,WAAW;OACnB,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QAC1C,IAAIA,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;UAC5B,OAAOH,WAAW;SACnB,MAAM;UACL,MAAMQ,mBAAmB,GAAGR,WAAW,CAACS,OAAO,CAC7C,qBAAqB,EACrB,MAAM,CACP;UACD,MAAMC,aAAa,GAAG,IAAI/B,MAAM,CAAC6B,mBAAmB,CAAC;UACrD,OAAOvB,OAAO,CAACC,SAAS,GACpBmB,aAAa,CAACK,aAAa,CAAC,GAC5BJ,eAAe,CAACI,aAAa,CAAC;;OAErC,MAAM;QACL,MAAMC,KAAK,CAAC,sBAAsB,CAAC;;IAEvC,CAAC,CACF;EACH,CAAC,CAAC;EAEF,IAAIC,gBAA0B;EAC9B,IAAIC,iBAAiD;EACrD,IAAIC,2BAAqD;EACzD,IAAIC,oBAA4C;EAChD,IAAIC,mBAA8B;EAClCzB,MAAM,CAAC,cAAc,EAAE,MAAK;IAC1BqB,gBAAgB,GAAG9C,GAAG,CACpB6B,iBAAiB,EAChBC,QAAQ,IAAKA,QAAQ,CAACqB,YAAa,CACrC;IAEDJ,iBAAiB,GAAG/C,GAAG,CAAC6B,iBAAiB,EAAGuB,KAAU,IAAI;MACxD,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK;MAC7B;MACA,IAAID,SAAS,KAAKzE,KAAK,CAAC2E,OAAO,EAAE;QAC/B,OAAOC,SAAS;OACjB,MAAM,IAAI3D,QAAQ,CAACwD,SAAS,CAAC,EAAE;QAC9B,OAAOA,SAAS;OACjB,MAAM,IAAIvD,WAAW,CAACuD,SAAS,CAAC,EAAE;QACjC,OAAO,KAAK;OACb,MAAM;QACL,MAAMR,KAAK,CAAC,sBAAsB,CAAC;;IAEvC,CAAC,CAAC;IAEFG,2BAA2B,GAAGhD,GAAG,CAAC6B,iBAAiB,EAAGuB,KAAU,IAAI;MAClE,MAAMK,aAAa,GAAGL,KAAK,CAACM,UAAU;MAEtC,IAAID,aAAa,EAAE;QACjB,MAAME,eAAe,GAAGlE,OAAO,CAACgE,aAAa,CAAC,GAC1CzD,GAAG,CAACyD,aAAa,EAAGG,IAAS,IAAKpE,OAAO,CAACqC,iBAAiB,EAAE+B,IAAI,CAAC,CAAC,GACnE,CAACpE,OAAO,CAACqC,iBAAiB,EAAE4B,aAAa,CAAC,CAAC;QAC/C,OAAOE,eAAe;;IAE1B,CAAC,CAAC;IAEFV,oBAAoB,GAAGjD,GAAG,CACxB6B,iBAAiB,EAChBuB,KAAU,IAAKA,KAAK,CAACS,SAAS,CAChC;IAEDX,mBAAmB,GAAGlD,GAAG,CAAC6B,iBAAiB,EAAGuB,KAAU,IACtD9D,GAAG,CAAC8D,KAAK,EAAE,UAAU,CAAC,CACvB;EACH,CAAC,CAAC;EAEF,IAAIU,6BAAwC;EAC5CrC,MAAM,CAAC,0BAA0B,EAAE,MAAK;IACtC,MAAMsC,uBAAuB,GAAGC,YAAY,CAC1C7C,OAAO,CAACK,wBAAyB,CAClC;IACDsC,6BAA6B,GAAG9D,GAAG,CAAC6B,iBAAiB,EAAGoC,OAAO,IAAK,KAAK,CAAC;IAC1E,IAAI9C,OAAO,CAACI,gBAAgB,KAAK,YAAY,EAAE;MAC7CuC,6BAA6B,GAAG9D,GAAG,CAAC6B,iBAAiB,EAAGoC,OAAO,IAAI;QACjE,IAAI3E,GAAG,CAAC2E,OAAO,EAAE,aAAa,CAAC,EAAE;UAC/B,OAAO,CAAC,CAACA,OAAO,CAACC,WAAW;SAC7B,MAAM;UACL,OACEC,qBAAqB,CAACF,OAAO,EAAEF,uBAAuB,CAAC,KAAK,KAAK,IACjE1D,gBAAgB,CACd0D,uBAAuB,EACvBE,OAAO,CAACxD,OAA0B,CACnC;;MAGP,CAAC,CAAC;;EAEN,CAAC,CAAC;EAEF,IAAI2D,oBAA+B;EACnC,IAAIC,iBAAqC;EACzC,IAAIC,WAA+C;EACnD,IAAIC,kBAAqC;EACzC9C,MAAM,CAAC,iBAAiB,EAAE,MAAK;IAC7B2C,oBAAoB,GAAGpE,GAAG,CAAC6B,iBAAiB,EAAE2C,eAAe,CAAC;IAC9DH,iBAAiB,GAAGrE,GAAG,CAACiC,sBAAsB,EAAEwC,cAAc,CAAC;IAE/DH,WAAW,GAAGrE,MAAM,CAClB4B,iBAAiB,EACjB,CAAC6C,GAAG,EAAEtB,KAAU,KAAI;MAClB,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK;MAC7B,IAAIzD,QAAQ,CAACwD,SAAS,CAAC,IAAI,EAAEA,SAAS,KAAKzE,KAAK,CAAC2E,OAAO,CAAC,EAAE;QACzDmB,GAAG,CAACrB,SAAS,CAAC,GAAG,EAAE;;MAErB,OAAOqB,GAAG;IACZ,CAAC,EACD,EAAuC,CACxC;IAEDH,kBAAkB,GAAGvE,GAAG,CACtBiC,sBAAsB,EACtB,CAAC0C,CAAC,EAAEC,GAAG,KAAoB;MACzB,OAAO;QACLC,OAAO,EAAE5C,sBAAsB,CAAC2C,GAAG,CAAC;QACpCE,SAAS,EAAE9B,2BAA2B,CAAC4B,GAAG,CAAC;QAC3CG,iBAAiB,EAAEjB,6BAA6B,CAACc,GAAG,CAAC;QACrDI,QAAQ,EAAEZ,oBAAoB,CAACQ,GAAG,CAAC;QACnCK,KAAK,EAAEZ,iBAAiB,CAACO,GAAG,CAAC;QAC7BM,KAAK,EAAEnC,iBAAiB,CAAC6B,GAAG,CAAC;QAC7BO,IAAI,EAAElC,oBAAoB,CAAC2B,GAAG,CAAC;QAC/BQ,GAAG,EAAElC,mBAAmB,CAAC0B,GAAG,CAAC;QAC7BzB,YAAY,EAAEL,gBAAgB,CAAC8B,GAAG,CAAC;QACnCS,SAAS,EAAExD,iBAAiB,CAAC+C,GAAG;OACjC;IACH,CAAC,CACF;EACH,CAAC,CAAC;EAEF,IAAIU,cAAc,GAAG,IAAI;EACzB,IAAIC,4BAA4B,GAC9B,EAAE;EAEJ,IAAI,CAACpE,OAAO,CAACG,QAAQ,EAAE;IACrBG,MAAM,CAAC,yBAAyB,EAAE,MAAK;MACrC8D,4BAA4B,GAAGtF,MAAM,CACnC4B,iBAAiB,EACjB,CAAC2D,MAAM,EAAEC,WAAW,EAAEb,GAAG,KAAI;QAC3B,IAAI,OAAOa,WAAW,CAAChF,OAAO,KAAK,QAAQ,EAAE;UAC3C,MAAMiF,QAAQ,GAAGD,WAAW,CAAChF,OAAO,CAACkF,UAAU,CAAC,CAAC,CAAC;UAClD,MAAMC,YAAY,GAAGC,wBAAwB,CAACH,QAAQ,CAAC;UACvDI,gBAAgB,CAACN,MAAM,EAAEI,YAAY,EAAErB,kBAAkB,CAACK,GAAG,CAAC,CAAC;SAChE,MAAM,IAAInF,OAAO,CAACgG,WAAW,CAACM,gBAAgB,CAAC,EAAE;UAChD,IAAIC,gBAAwB;UAC5B3G,OAAO,CAACoG,WAAW,CAACM,gBAAgB,EAAGE,SAAS,IAAI;YAClD,MAAMP,QAAQ,GACZ,OAAOO,SAAS,KAAK,QAAQ,GACzBA,SAAS,CAACN,UAAU,CAAC,CAAC,CAAC,GACvBM,SAAS;YACf,MAAMC,gBAAgB,GAAGL,wBAAwB,CAACH,QAAQ,CAAC;YAC3D;YACA;YACA;YACA;YACA,IAAIM,gBAAgB,KAAKE,gBAAgB,EAAE;cACzCF,gBAAgB,GAAGE,gBAAgB;cACnCJ,gBAAgB,CACdN,MAAM,EACNU,gBAAgB,EAChB3B,kBAAkB,CAACK,GAAG,CAAC,CACxB;;UAEL,CAAC,CAAC;SACH,MAAM,IAAIhF,QAAQ,CAAC6F,WAAW,CAAChF,OAAO,CAAC,EAAE;UACxC,IAAIgF,WAAW,CAAChF,OAAO,CAAC0F,OAAO,EAAE;YAC/Bb,cAAc,GAAG,KAAK;YACtB,IAAInE,OAAO,CAACiF,mBAAmB,EAAE;cAC/BhG,WAAW,CACT,GAAGE,2BAA2B,EAAE,GAC9B,yBAAyBmF,WAAW,CAAChF,OAAO,CAAC4F,QAAQ,EAAE,eAAe,GACtE,sFAAsF,GACtF,6DAA6D,GAC7D,kGAAkG,CACrG;;WAEJ,MAAM;YACL,MAAMC,cAAc,GAAG/F,6BAA6B,CAClDkF,WAAW,CAAChF,OAAO,EACnBU,OAAO,CAACiF,mBAAmB,CAC5B;YACD;YACA;YACA;YACA,IAAI1G,OAAO,CAAC4G,cAAc,CAAC,EAAE;cAC3B;cACA;cACA;cACAhB,cAAc,GAAG,KAAK;;YAExBjG,OAAO,CAACiH,cAAc,EAAGC,IAAI,IAAI;cAC/BT,gBAAgB,CAACN,MAAM,EAAEe,IAAI,EAAEhC,kBAAkB,CAACK,GAAG,CAAC,CAAC;YACzD,CAAC,CAAC;;SAEL,MAAM;UACL,IAAIzD,OAAO,CAACiF,mBAAmB,EAAE;YAC/BhG,WAAW,CACT,GAAGE,2BAA2B,EAAE,GAC9B,iBAAiBmF,WAAW,CAACe,IAAI,qFAAqF,GACtH,6DAA6D,GAC7D,iGAAiG,CACpG;;UAEHlB,cAAc,GAAG,KAAK;;QAGxB,OAAOE,MAAM;MACf,CAAC,EACD,EAA8C,CAC/C;IACH,CAAC,CAAC;;EAGJ,OAAO;IACLlB,WAAW,EAAEA,WAAW;IACxBC,kBAAkB,EAAEA,kBAAkB;IACtCgB,4BAA4B,EAAEA,4BAA4B;IAC1DvD,SAAS,EAAEA,SAAS;IACpBsD,cAAc,EAAEA;GACjB;AACH;AAEA,OAAM,SAAUmB,gBAAgBA,CAC9BvF,UAAuB,EACvBwF,eAAyB;EAEzB,IAAIC,MAAM,GAA4B,EAAE;EAExC,MAAMC,aAAa,GAAGC,mBAAmB,CAAC3F,UAAU,CAAC;EACrDyF,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACF,aAAa,CAACD,MAAM,CAAC;EAE5C,MAAMI,aAAa,GAAGC,mBAAmB,CAACJ,aAAa,CAACK,KAAK,CAAC;EAC9D,MAAMC,eAAe,GAAGH,aAAa,CAACE,KAAK;EAC3CN,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACC,aAAa,CAACJ,MAAM,CAAC;EAE5CA,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACK,qBAAqB,CAACD,eAAe,CAAC,CAAC;EAE9DP,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACM,oBAAoB,CAACF,eAAe,CAAC,CAAC;EAE7DP,MAAM,GAAGA,MAAM,CAACG,MAAM,CACpBO,uBAAuB,CAACH,eAAe,EAAER,eAAe,CAAC,CAC1D;EAEDC,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACQ,uBAAuB,CAACJ,eAAe,CAAC,CAAC;EAEhE,OAAOP,MAAM;AACf;AAEA,SAASQ,qBAAqBA,CAC5BjG,UAAuB;EAEvB,IAAIyF,MAAM,GAA4B,EAAE;EACxC,MAAMY,kBAAkB,GAAGtI,MAAM,CAACiC,UAAU,EAAGuE,WAAW,IACxD7F,QAAQ,CAAC6F,WAAW,CAAChF,OAAO,CAAC,CAAC,CAC/B;EAEDkG,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACU,oBAAoB,CAACD,kBAAkB,CAAC,CAAC;EAEhEZ,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACW,sBAAsB,CAACF,kBAAkB,CAAC,CAAC;EAElEZ,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACY,oBAAoB,CAACH,kBAAkB,CAAC,CAAC;EAEhEZ,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACa,qBAAqB,CAACJ,kBAAkB,CAAC,CAAC;EAEjEZ,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACc,qBAAqB,CAACL,kBAAkB,CAAC,CAAC;EAEjE,OAAOZ,MAAM;AACf;AAOA,OAAM,SAAUE,mBAAmBA,CACjC3F,UAAuB;EAEvB,MAAM2G,4BAA4B,GAAG5I,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACnE,OAAO,CAACxC,GAAG,CAACwC,QAAQ,EAAErB,OAAO,CAAC;EAChC,CAAC,CAAC;EAEF,MAAMkG,MAAM,GAAG3G,GAAG,CAAC6H,4BAA4B,EAAG/F,QAAQ,IAAI;IAC5D,OAAO;MACLgG,OAAO,EACL,gBAAgB,GAChBhG,QAAQ,CAAC0E,IAAI,GACb,sCAAsC;MACxC5C,IAAI,EAAE/E,wBAAwB,CAACkJ,eAAe;MAC9C7G,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,MAAMmF,KAAK,GAAGjI,UAAU,CAACkC,UAAU,EAAE2G,4BAA4B,CAAC;EAClE,OAAO;IAAElB,MAAM;IAAEM;EAAK,CAAE;AAC1B;AAEA,OAAM,SAAUD,mBAAmBA,CACjC9F,UAAuB;EAEvB,MAAM8G,4BAA4B,GAAG/I,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACnE,MAAM+C,OAAO,GAAG/C,QAAQ,CAACrB,OAAO,CAAC;IACjC,OACE,CAACb,QAAQ,CAACiF,OAAO,CAAC,IAClB,CAAClF,UAAU,CAACkF,OAAO,CAAC,IACpB,CAACvF,GAAG,CAACuF,OAAO,EAAE,MAAM,CAAC,IACrB,CAAChF,QAAQ,CAACgF,OAAO,CAAC;EAEtB,CAAC,CAAC;EAEF,MAAM8B,MAAM,GAAG3G,GAAG,CAACgI,4BAA4B,EAAGlG,QAAQ,IAAI;IAC5D,OAAO;MACLgG,OAAO,EACL,gBAAgB,GAChBhG,QAAQ,CAAC0E,IAAI,GACb,6CAA6C,GAC7C,8GAA8G;MAChH5C,IAAI,EAAE/E,wBAAwB,CAACoJ,eAAe;MAC9C/G,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,MAAMmF,KAAK,GAAGjI,UAAU,CAACkC,UAAU,EAAE8G,4BAA4B,CAAC;EAClE,OAAO;IAAErB,MAAM;IAAEM;EAAK,CAAE;AAC1B;AAEA,MAAMiB,YAAY,GAAG,UAAU;AAE/B,OAAM,SAAUV,oBAAoBA,CAClCtG,UAAuB;EAEvB,MAAMiH,eAAgB,SAAQxJ,iBAAiB;IAA/CyJ,YAAA;;MACE,KAAAC,KAAK,GAAG,KAAK;IAKf;IAHEC,cAAcA,CAACC,IAAa;MAC1B,IAAI,CAACF,KAAK,GAAG,IAAI;IACnB;;EAGF,MAAMG,YAAY,GAAGvJ,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACnD,MAAM+C,OAAO,GAAG/C,QAAQ,CAACrB,OAAO;IAEhC,IAAI;MACF,MAAMgI,SAAS,GAAGjI,YAAY,CAACqE,OAAiB,CAAC;MACjD,MAAM6D,gBAAgB,GAAG,IAAIP,eAAe,EAAE;MAC9CO,gBAAgB,CAACC,KAAK,CAACF,SAAS,CAAC;MAEjC,OAAOC,gBAAgB,CAACL,KAAK;KAC9B,CAAC,OAAOO,CAAC,EAAE;MACV;MACA;MACA,OAAOV,YAAY,CAACW,IAAI,CAAEhE,OAAkB,CAACzC,MAAM,CAAC;;EAExD,CAAC,CAAC;EAEF,MAAMuE,MAAM,GAAG3G,GAAG,CAACwI,YAAY,EAAG1G,QAAQ,IAAI;IAC5C,OAAO;MACLgG,OAAO,EACL,mCAAmC,GACnC,kBAAkB,GAClBhG,QAAQ,CAAC0E,IAAI,GACb,8DAA8D,GAC9D,oEAAoE,GACpE,gBAAgB;MAClB5C,IAAI,EAAE/E,wBAAwB,CAACiK,gBAAgB;MAC/C5H,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,OAAO6E,MAAM;AACf;AAEA,OAAM,SAAUiB,qBAAqBA,CACnC1G,UAAuB;EAEvB,MAAM6H,kBAAkB,GAAG9J,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACzD,MAAM+C,OAAO,GAAG/C,QAAQ,CAACrB,OAAiB;IAC1C,OAAOoE,OAAO,CAACgE,IAAI,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC;EAEF,MAAMlC,MAAM,GAAG3G,GAAG,CAAC+I,kBAAkB,EAAGjH,QAAQ,IAAI;IAClD,OAAO;MACLgG,OAAO,EACL,gBAAgB,GAChBhG,QAAQ,CAAC0E,IAAI,GACb,oDAAoD;MACtD5C,IAAI,EAAE/E,wBAAwB,CAACmK,mBAAmB;MAClD9H,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,OAAO6E,MAAM;AACf;AAEA,MAAMsC,cAAc,GAAG,gBAAgB;AAEvC,OAAM,SAAUxB,sBAAsBA,CACpCvG,UAAuB;EAEvB,MAAMgI,iBAAkB,SAAQvK,iBAAiB;IAAjDyJ,YAAA;;MACE,KAAAC,KAAK,GAAG,KAAK;IAKf;IAHEc,gBAAgBA,CAACZ,IAAa;MAC5B,IAAI,CAACF,KAAK,GAAG,IAAI;IACnB;;EAGF,MAAMG,YAAY,GAAGvJ,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACnD,MAAM+C,OAAO,GAAG/C,QAAQ,CAACrB,OAAiB;IAC1C,IAAI;MACF,MAAMgI,SAAS,GAAGjI,YAAY,CAACqE,OAAO,CAAC;MACvC,MAAMuE,kBAAkB,GAAG,IAAIF,iBAAiB,EAAE;MAClDE,kBAAkB,CAACT,KAAK,CAACF,SAAS,CAAC;MAEnC,OAAOW,kBAAkB,CAACf,KAAK;KAChC,CAAC,OAAOO,CAAC,EAAE;MACV;MACA;MACA,OAAOK,cAAc,CAACJ,IAAI,CAAChE,OAAO,CAACzC,MAAM,CAAC;;EAE9C,CAAC,CAAC;EAEF,MAAMuE,MAAM,GAAG3G,GAAG,CAACwI,YAAY,EAAG1G,QAAQ,IAAI;IAC5C,OAAO;MACLgG,OAAO,EACL,mCAAmC,GACnC,kBAAkB,GAClBhG,QAAQ,CAAC0E,IAAI,GACb,gEAAgE,GAChE,4EAA4E,GAC5E,gBAAgB;MAClB5C,IAAI,EAAE/E,wBAAwB,CAACwK,gBAAgB;MAC/CnI,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,OAAO6E,MAAM;AACf;AAEA,OAAM,SAAUe,oBAAoBA,CAClCxG,UAAuB;EAEvB,MAAMoI,YAAY,GAAGrK,MAAM,CAACiC,UAAU,EAAGY,QAAQ,IAAI;IACnD,MAAM+C,OAAO,GAAG/C,QAAQ,CAACrB,OAAO,CAAC;IACjC,OAAOoE,OAAO,YAAYhE,MAAM,KAAKgE,OAAO,CAAC0E,SAAS,IAAI1E,OAAO,CAAC2E,MAAM,CAAC;EAC3E,CAAC,CAAC;EAEF,MAAM7C,MAAM,GAAG3G,GAAG,CAACsJ,YAAY,EAAGxH,QAAQ,IAAI;IAC5C,OAAO;MACLgG,OAAO,EACL,gBAAgB,GAChBhG,QAAQ,CAAC0E,IAAI,GACb,mEAAmE;MACrE5C,IAAI,EAAE/E,wBAAwB,CAAC4K,uBAAuB;MACtDvI,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,OAAO6E,MAAM;AACf;AAEA;AACA,OAAM,SAAUgB,qBAAqBA,CACnCzG,UAAuB;EAEvB,MAAMmH,KAAK,GAAgB,EAAE;EAC7B,IAAIqB,iBAAiB,GAAG1J,GAAG,CAACkB,UAAU,EAAGyI,SAAc,IAAI;IACzD,OAAO1J,MAAM,CACXiB,UAAU,EACV,CAACsE,MAAM,EAAEoE,SAAS,KAAI;MACpB,IACED,SAAS,CAAClJ,OAAO,CAAC2B,MAAM,KAAMwH,SAAS,CAACnJ,OAAkB,CAAC2B,MAAM,IACjE,CAAC7C,QAAQ,CAAC8I,KAAK,EAAEuB,SAAS,CAAC,IAC3BA,SAAS,CAACnJ,OAAO,KAAK7B,KAAK,CAACmD,EAAE,EAC9B;QACA;QACA;QACAsG,KAAK,CAAClD,IAAI,CAACyE,SAAS,CAAC;QACrBpE,MAAM,CAACL,IAAI,CAACyE,SAAS,CAAC;QACtB,OAAOpE,MAAM;;MAEf,OAAOA,MAAM;IACf,CAAC,EACD,EAAiB,CAClB;EACH,CAAC,CAAC;EAEFkE,iBAAiB,GAAG5K,OAAO,CAAC4K,iBAAiB,CAAC;EAE9C,MAAMG,iBAAiB,GAAG5K,MAAM,CAACyK,iBAAiB,EAAGI,gBAAgB,IAAI;IACvE,OAAOA,gBAAgB,CAACzH,MAAM,GAAG,CAAC;EACpC,CAAC,CAAC;EAEF,MAAMsE,MAAM,GAAG3G,GAAG,CAAC6J,iBAAiB,EAAGE,cAAmB,IAAI;IAC5D,MAAMC,cAAc,GAAGhK,GAAG,CAAC+J,cAAc,EAAGjI,QAAa,IAAI;MAC3D,OAAOA,QAAQ,CAAC0E,IAAI;IACtB,CAAC,CAAC;IAEF,MAAMyD,aAAa,GAAS9K,KAAK,CAAC4K,cAAc,CAAE,CAACtJ,OAAO;IAC1D,OAAO;MACLqH,OAAO,EACL,6BAA6BmC,aAAa,IAAI,GAC9C,sDAAsDD,cAAc,CAACE,IAAI,CACvE,IAAI,CACL,KAAK;MACRtG,IAAI,EAAE/E,wBAAwB,CAACsL,wBAAwB;MACvDjJ,UAAU,EAAE6I;KACb;EACH,CAAC,CAAC;EAEF,OAAOpD,MAAM;AACf;AAEA,OAAM,SAAUS,oBAAoBA,CAClClG,UAAuB;EAEvB,MAAMkJ,YAAY,GAAGnL,MAAM,CAACiC,UAAU,EAAGkC,KAAU,IAAI;IACrD,IAAI,CAAC9D,GAAG,CAAC8D,KAAK,EAAE,OAAO,CAAC,EAAE;MACxB,OAAO,KAAK;;IAEd,MAAM8B,KAAK,GAAG9B,KAAK,CAACE,KAAK;IAEzB,OAAO4B,KAAK,KAAKtG,KAAK,CAAC2E,OAAO,IAAI2B,KAAK,KAAKtG,KAAK,CAACmD,EAAE,IAAI,CAAClC,QAAQ,CAACqF,KAAK,CAAC;EAC1E,CAAC,CAAC;EAEF,MAAMyB,MAAM,GAAG3G,GAAG,CAACoK,YAAY,EAAGtI,QAAQ,IAAI;IAC5C,OAAO;MACLgG,OAAO,EACL,gBAAgB,GAChBhG,QAAQ,CAAC0E,IAAI,GACb,+DAA+D;MACjE5C,IAAI,EAAE/E,wBAAwB,CAACwL,wBAAwB;MACvDnJ,UAAU,EAAE,CAACY,QAAQ;KACtB;EACH,CAAC,CAAC;EAEF,OAAO6E,MAAM;AACf;AAEA,OAAM,SAAUU,uBAAuBA,CACrCnG,UAAuB,EACvBoJ,UAAoB;EAEpB,MAAMC,YAAY,GAAGtL,MAAM,CAACiC,UAAU,EAAGkC,KAAU,IAAI;IACrD,OACEA,KAAK,CAACS,SAAS,KAAKL,SAAS,IAAI,CAACjE,QAAQ,CAAC+K,UAAU,EAAElH,KAAK,CAACS,SAAS,CAAC;EAE3E,CAAC,CAAC;EAEF,MAAM8C,MAAM,GAAG3G,GAAG,CAACuK,YAAY,EAAGtG,OAAO,IAAI;IAC3C,MAAMvC,GAAG,GACP,iBAAiBuC,OAAO,CAACuC,IAAI,8DAA8DvC,OAAO,CAACJ,SAAS,IAAI,GAChH,sBAAsB;IACxB,OAAO;MACLiE,OAAO,EAAEpG,GAAG;MACZkC,IAAI,EAAE/E,wBAAwB,CAAC2L,wBAAwB;MACvDtJ,UAAU,EAAE,CAAC+C,OAAO;KACrB;EACH,CAAC,CAAC;EAEF,OAAO0C,MAAM;AACf;AAEA,OAAM,SAAUW,uBAAuBA,CACrCpG,UAAuB;EAEvB,MAAMyF,MAAM,GAA4B,EAAE;EAE1C,MAAM8D,WAAW,GAAGxK,MAAM,CACxBiB,UAAU,EACV,CAACsE,MAAM,EAAEvB,OAAO,EAAEW,GAAG,KAAI;IACvB,MAAMC,OAAO,GAAGZ,OAAO,CAACxD,OAAO;IAE/B,IAAIoE,OAAO,KAAKjG,KAAK,CAACmD,EAAE,EAAE;MACxB,OAAOyD,MAAM;;IAGf;IACA;IACA,IAAI3F,QAAQ,CAACgF,OAAO,CAAC,EAAE;MACrBW,MAAM,CAACL,IAAI,CAAC;QAAEuF,GAAG,EAAE7F,OAAO;QAAED,GAAG;QAAES,SAAS,EAAEpB;MAAO,CAAE,CAAC;KACvD,MAAM,IAAIrE,QAAQ,CAACiF,OAAO,CAAC,IAAI8F,UAAU,CAAC9F,OAAO,CAAC,EAAE;MACnDW,MAAM,CAACL,IAAI,CAAC;QAAEuF,GAAG,EAAE7F,OAAO,CAACzC,MAAM;QAAEwC,GAAG;QAAES,SAAS,EAAEpB;MAAO,CAAE,CAAC;;IAE/D,OAAOuB,MAAM;EACf,CAAC,EACD,EAA0D,CAC3D;EAEDnG,OAAO,CAAC6B,UAAU,EAAE,CAAC+C,OAAO,EAAE2G,OAAO,KAAI;IACvCvL,OAAO,CAACoL,WAAW,EAAE,CAAC;MAAEC,GAAG;MAAE9F,GAAG;MAAES;IAAS,CAAE,KAAI;MAC/C,IAAIuF,OAAO,GAAGhG,GAAG,IAAIiG,aAAa,CAACH,GAAG,EAAEzG,OAAO,CAACxD,OAAO,CAAC,EAAE;QACxD,MAAMiB,GAAG,GACP,YAAY2D,SAAS,CAACmB,IAAI,4BAA4B,GACtD,6CAA6CvC,OAAO,CAACuC,IAAI,IAAI,GAC7D,8BAA8B,GAC9B,8EAA8E;QAChFG,MAAM,CAACxB,IAAI,CAAC;UACV2C,OAAO,EAAEpG,GAAG;UACZkC,IAAI,EAAE/E,wBAAwB,CAACiM,mBAAmB;UAClD5J,UAAU,EAAE,CAAC+C,OAAO,EAAEoB,SAAS;SAChC,CAAC;;IAEN,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOsB,MAAM;AACf;AAEA,SAASkE,aAAaA,CAACH,GAAW,EAAE7F,OAAY;EAC9C;EACA,IAAIjF,QAAQ,CAACiF,OAAO,CAAC,EAAE;IACrB,MAAMkG,WAAW,GAAGlG,OAAO,CAACpC,IAAI,CAACiI,GAAG,CAAC;IACrC,OAAOK,WAAW,KAAK,IAAI,IAAIA,WAAW,CAACC,KAAK,KAAK,CAAC;GACvD,MAAM,IAAIrL,UAAU,CAACkF,OAAO,CAAC,EAAE;IAC9B;IACA,OAAOA,OAAO,CAAC6F,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;GAC/B,MAAM,IAAIpL,GAAG,CAACuF,OAAO,EAAE,MAAM,CAAC,EAAE;IAC/B;IACA,OAAOA,OAAO,CAACpC,IAAI,CAACiI,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;GACpC,MAAM,IAAI,OAAO7F,OAAO,KAAK,QAAQ,EAAE;IACtC,OAAOA,OAAO,KAAK6F,GAAG;GACvB,MAAM;IACL,MAAM7H,KAAK,CAAC,sBAAsB,CAAC;;AAEvC;AAEA,SAAS8H,UAAUA,CAACM,MAAc;EAChC;EACA,MAAMC,SAAS,GAAG,CAChB,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;EACD,OACEhM,IAAI,CAACgM,SAAS,EAAGC,IAAI,IAAKF,MAAM,CAAC7I,MAAM,CAAC5C,OAAO,CAAC2L,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK3H,SAAS;AAE/E;AAEA,OAAM,SAAUhB,eAAeA,CAACqC,OAAe;EAC7C,MAAMuG,KAAK,GAAGvG,OAAO,CAACvC,UAAU,GAAG,GAAG,GAAG,EAAE;EAC3C;EACA;EACA,OAAO,IAAIzB,MAAM,CAAC,OAAOgE,OAAO,CAACzC,MAAM,GAAG,EAAEgJ,KAAK,CAAC;AACpD;AAEA,OAAM,SAAU7I,aAAaA,CAACsC,OAAe;EAC3C,MAAMuG,KAAK,GAAGvG,OAAO,CAACvC,UAAU,GAAG,IAAI,GAAG,GAAG;EAC7C;EACA;EACA,OAAO,IAAIzB,MAAM,CAAC,GAAGgE,OAAO,CAACzC,MAAM,EAAE,EAAEgJ,KAAK,CAAC;AAC/C;AAEA,OAAM,SAAUC,oBAAoBA,CAClCC,eAA0C,EAC1CC,UAAmB,EACnB/J,wBAA6C;EAE7C,MAAMmF,MAAM,GAA4B,EAAE;EAE1C;EACA,IAAI,CAACrH,GAAG,CAACgM,eAAe,EAAE5K,YAAY,CAAC,EAAE;IACvCiG,MAAM,CAACxB,IAAI,CAAC;MACV2C,OAAO,EACL,qDAAqD,GACrDpH,YAAY,GACZ,gCAAgC;MAClCkD,IAAI,EAAE/E,wBAAwB,CAAC2M;KAChC,CAAC;;EAEJ,IAAI,CAAClM,GAAG,CAACgM,eAAe,EAAE3K,KAAK,CAAC,EAAE;IAChCgG,MAAM,CAACxB,IAAI,CAAC;MACV2C,OAAO,EACL,qDAAqD,GACrDnH,KAAK,GACL,gCAAgC;MAClCiD,IAAI,EAAE/E,wBAAwB,CAAC4M;KAChC,CAAC;;EAGJ,IACEnM,GAAG,CAACgM,eAAe,EAAE3K,KAAK,CAAC,IAC3BrB,GAAG,CAACgM,eAAe,EAAE5K,YAAY,CAAC,IAClC,CAACpB,GAAG,CAACgM,eAAe,CAACI,KAAK,EAAEJ,eAAe,CAACK,WAAW,CAAC,EACxD;IACAhF,MAAM,CAACxB,IAAI,CAAC;MACV2C,OAAO,EACL,kDAAkDpH,YAAY,MAAM4K,eAAe,CAACK,WAAW,GAAG,GAClG,wBAAwB;MAC1B/H,IAAI,EAAE/E,wBAAwB,CAAC+M;KAChC,CAAC;;EAGJ,IAAItM,GAAG,CAACgM,eAAe,EAAE3K,KAAK,CAAC,EAAE;IAC/BtB,OAAO,CAACiM,eAAe,CAACI,KAAK,EAAE,CAACG,aAAa,EAAEC,YAAY,KAAI;MAC7DzM,OAAO,CAACwM,aAAa,EAAE,CAACpG,WAAW,EAAEsG,OAAO,KAAI;QAC9C,IAAIjM,WAAW,CAAC2F,WAAW,CAAC,EAAE;UAC5BkB,MAAM,CAACxB,IAAI,CAAC;YACV2C,OAAO,EACL,oEAAoE,GACpE,IAAIgE,YAAY,gBAAgBC,OAAO,KAAK;YAC9CnI,IAAI,EAAE/E,wBAAwB,CAACmN;WAChC,CAAC;SACH,MAAM,IAAI1M,GAAG,CAACmG,WAAW,EAAE,YAAY,CAAC,EAAE;UACzC,MAAMX,SAAS,GAAGrF,OAAO,CAACgG,WAAW,CAAC/B,UAAU,CAAC,GAC7C+B,WAAW,CAAC/B,UAAU,GACtB,CAAC+B,WAAW,CAAC/B,UAAU,CAAC;UAC5BrE,OAAO,CAACyF,SAAS,EAAGmH,aAAa,IAAI;YACnC,IACE,CAACnM,WAAW,CAACmM,aAAa,CAAC,IAC3B,CAAC1M,QAAQ,CAACsM,aAAa,EAAEI,aAAa,CAAC,EACvC;cACAtF,MAAM,CAACxB,IAAI,CAAC;gBACV2C,OAAO,EAAE,8DAA8DmE,aAAa,CAACzF,IAAI,eAAef,WAAW,CAACe,IAAI,sBAAsBsF,YAAY,KAAK;gBAC/JlI,IAAI,EAAE/E,wBAAwB,CAACqN;eAChC,CAAC;;UAEN,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;;EAGJ,OAAOvF,MAAM;AACf;AAEA,OAAM,SAAUwF,2BAA2BA,CACzCb,eAA0C,EAC1CC,UAAmB,EACnB/J,wBAA6C;EAE7C,MAAM4K,QAAQ,GAAG,EAAE;EACnB,IAAIC,eAAe,GAAG,KAAK;EAC3B,MAAMC,aAAa,GAAGxN,OAAO,CAACM,OAAO,CAACe,MAAM,CAACmL,eAAe,CAACI,KAAK,CAAC,CAAC,CAAC;EAErE,MAAMa,kBAAkB,GAAGrM,MAAM,CAC/BoM,aAAa,EACZxK,QAAQ,IAAKA,QAAQ,CAACrB,OAAO,CAAC,KAAK7B,KAAK,CAACmD,EAAE,CAC7C;EACD,MAAMyK,mBAAmB,GAAGxI,YAAY,CAACxC,wBAAwB,CAAC;EAClE,IAAI+J,UAAU,EAAE;IACdlM,OAAO,CAACkN,kBAAkB,EAAGtI,OAAO,IAAI;MACtC,MAAMwI,SAAS,GAAGtI,qBAAqB,CAACF,OAAO,EAAEuI,mBAAmB,CAAC;MACrE,IAAIC,SAAS,KAAK,KAAK,EAAE;QACvB,MAAM3E,OAAO,GAAG4E,0BAA0B,CAACzI,OAAO,EAAEwI,SAAS,CAAC;QAC9D,MAAME,iBAAiB,GAAG;UACxB7E,OAAO;UACPlE,IAAI,EAAE6I,SAAS,CAACG,KAAK;UACrBvH,SAAS,EAAEpB;SACZ;QACDmI,QAAQ,CAACjH,IAAI,CAACwH,iBAAiB,CAAC;OACjC,MAAM;QACL;QACA,IAAIrN,GAAG,CAAC2E,OAAO,EAAE,aAAa,CAAC,EAAE;UAC/B,IAAIA,OAAO,CAACC,WAAW,KAAK,IAAI,EAAE;YAChCmI,eAAe,GAAG,IAAI;;SAEzB,MAAM;UACL,IACEhM,gBAAgB,CAACmM,mBAAmB,EAAEvI,OAAO,CAACxD,OAAiB,CAAC,EAChE;YACA4L,eAAe,GAAG,IAAI;;;;IAI9B,CAAC,CAAC;;EAGJ,IAAId,UAAU,IAAI,CAACc,eAAe,EAAE;IAClCD,QAAQ,CAACjH,IAAI,CAAC;MACZ2C,OAAO,EACL,kCAAkC,GAClC,uEAAuE,GACvE,kFAAkF,GAClF,mFAAmF,GACnF,gBAAgB;MAClBlE,IAAI,EAAE/E,wBAAwB,CAACgO;KAChC,CAAC;;EAEJ,OAAOT,QAAQ;AACjB;AAEA,OAAM,SAAUU,gBAAgBA,CAACxI,WAEhC;EACC,MAAMyI,YAAY,GAAQ,EAAE;EAC5B,MAAMC,SAAS,GAAGjN,IAAI,CAACuE,WAAW,CAAC;EAEnCjF,OAAO,CAAC2N,SAAS,EAAGC,OAAO,IAAI;IAC7B,MAAMC,cAAc,GAAG5I,WAAW,CAAC2I,OAAO,CAAC;IAE3C;IACA,IAAIxN,OAAO,CAACyN,cAAc,CAAC,EAAE;MAC3BH,YAAY,CAACE,OAAO,CAAC,GAAG,EAAE;KAC3B,MAAM;MACL,MAAMpK,KAAK,CAAC,sBAAsB,CAAC;;EAEvC,CAAC,CAAC;EAEF,OAAOkK,YAAY;AACrB;AAEA;AACA,OAAM,SAAUvI,eAAeA,CAACa,SAAoB;EAClD,MAAMR,OAAO,GAAGQ,SAAS,CAAC5E,OAAO;EACjC;EACA,IAAIb,QAAQ,CAACiF,OAAO,CAAC,EAAE;IACrB,OAAO,KAAK;GACb,MAAM,IAAIlF,UAAU,CAACkF,OAAO,CAAC,EAAE;IAC9B;IACA,OAAO,IAAI;GACZ,MAAM,IAAIvF,GAAG,CAACuF,OAAO,EAAE,MAAM,CAAC,EAAE;IAC/B;IACA,OAAO,IAAI;GACZ,MAAM,IAAIhF,QAAQ,CAACgF,OAAO,CAAC,EAAE;IAC5B,OAAO,KAAK;GACb,MAAM;IACL,MAAMhC,KAAK,CAAC,sBAAsB,CAAC;;AAEvC;AAEA,OAAM,SAAU4B,cAAcA,CAACI,OAAY;EACzC,IAAIhF,QAAQ,CAACgF,OAAO,CAAC,IAAIA,OAAO,CAACxC,MAAM,KAAK,CAAC,EAAE;IAC7C,OAAOwC,OAAO,CAACc,UAAU,CAAC,CAAC,CAAC;GAC7B,MAAM;IACL,OAAO,KAAK;;AAEhB;AAEA;;;AAGA,OAAO,MAAMwH,6BAA6B,GAA2B;EACnE;EACAtE,IAAI,EAAE,SAAAA,CAAUuE,IAAI;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAAC/K,MAAM;IACvB,KAAK,IAAIiL,CAAC,GAAG,IAAI,CAACC,SAAS,EAAED,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;MACzC,MAAME,CAAC,GAAGJ,IAAI,CAACzH,UAAU,CAAC2H,CAAC,CAAC;MAC5B,IAAIE,CAAC,KAAK,EAAE,EAAE;QACZ,IAAI,CAACD,SAAS,GAAGD,CAAC,GAAG,CAAC;QACtB,OAAO,IAAI;OACZ,MAAM,IAAIE,CAAC,KAAK,EAAE,EAAE;QACnB,IAAIJ,IAAI,CAACzH,UAAU,CAAC2H,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;UACjC,IAAI,CAACC,SAAS,GAAGD,CAAC,GAAG,CAAC;SACvB,MAAM;UACL,IAAI,CAACC,SAAS,GAAGD,CAAC,GAAG,CAAC;;QAExB,OAAO,IAAI;;;IAGf,OAAO,KAAK;EACd,CAAC;EAEDC,SAAS,EAAE;CACZ;AAED,SAASpJ,qBAAqBA,CAC5BF,OAAkB,EAClBF,uBAAiC;EASjC,IAAIzE,GAAG,CAAC2E,OAAO,EAAE,aAAa,CAAC,EAAE;IAC/B;IACA;IACA,OAAO,KAAK;GACb,MAAM;IACL;IACA,IAAIrE,QAAQ,CAACqE,OAAO,CAACxD,OAAO,CAAC,EAAE;MAC7B,IAAI;QACF;QACAJ,gBAAgB,CAAC0D,uBAAuB,EAAEE,OAAO,CAACxD,OAAiB,CAAC;OACrE,CAAC,OAAOmI,CAAC,EAAE;QACV;QACA,OAAO;UACLgE,KAAK,EAAE/N,wBAAwB,CAAC4O,mBAAmB;UACnDC,MAAM,EAAG9E,CAAW,CAACd;SACtB;;MAEH,OAAO,KAAK;KACb,MAAM,IAAIjI,QAAQ,CAACoE,OAAO,CAACxD,OAAO,CAAC,EAAE;MACpC;MACA,OAAO,KAAK;KACb,MAAM,IAAI+D,eAAe,CAACP,OAAO,CAAC,EAAE;MACnC;MACA,OAAO;QAAE2I,KAAK,EAAE/N,wBAAwB,CAAC8O;MAAiB,CAAE;KAC7D,MAAM;MACL,MAAM9K,KAAK,CAAC,sBAAsB,CAAC;;;AAGzC;AAEA,OAAM,SAAU6J,0BAA0BA,CACxCzI,OAAkB,EAClB2J,OAKC;EAED;EACA,IAAIA,OAAO,CAAChB,KAAK,KAAK/N,wBAAwB,CAAC4O,mBAAmB,EAAE;IAClE,OACE,iEAAiE,GACjE,4BAA4BxJ,OAAO,CAACuC,IAAI,gBAAgB,GACxD,kBAAkBoH,OAAO,CAACF,MAAM,KAAK,GACrC,qGAAqG;GAExG,MAAM,IAAIE,OAAO,CAAChB,KAAK,KAAK/N,wBAAwB,CAAC8O,iBAAiB,EAAE;IACvE,OACE,4EAA4E,GAC5E,4BAA4B1J,OAAO,CAACuC,IAAI,gBAAgB,GACxD,mGAAmG;GAEtG,MAAM;IACL,MAAM3D,KAAK,CAAC,sBAAsB,CAAC;;AAEvC;AAEA,SAASmB,YAAYA,CAAC6J,YAAiC;EACrD,MAAMC,SAAS,GAAG9N,GAAG,CAAC6N,YAAY,EAAGE,WAAW,IAAI;IAClD,IAAIlO,QAAQ,CAACkO,WAAW,CAAC,EAAE;MACzB,OAAOA,WAAW,CAACpI,UAAU,CAAC,CAAC,CAAC;KACjC,MAAM;MACL,OAAOoI,WAAW;;EAEtB,CAAC,CAAC;EAEF,OAAOD,SAAS;AAClB;AAEA,SAAShI,gBAAgBA,CACvB9F,GAAwB,EACxBgO,GAAW,EACXC,KAAQ;EAER,IAAIjO,GAAG,CAACgO,GAAG,CAAC,KAAKxK,SAAS,EAAE;IAC1BxD,GAAG,CAACgO,GAAG,CAAC,GAAG,CAACC,KAAK,CAAC;GACnB,MAAM;IACLjO,GAAG,CAACgO,GAAG,CAAC,CAAC7I,IAAI,CAAC8I,KAAK,CAAC;;AAExB;AAEA,OAAO,MAAMC,kBAAkB,GAAG,GAAG;AAErC;;;;;;;;;;;;;;;AAeA,IAAIC,yBAAyB,GAAa,EAAE;AAC5C,OAAM,SAAUtI,wBAAwBA,CAACH,QAAgB;EACvD,OAAOA,QAAQ,GAAGwI,kBAAkB,GAChCxI,QAAQ,GACRyI,yBAAyB,CAACzI,QAAQ,CAAC;AACzC;AAEA;;;;;;;;AAQA,SAAS9D,+BAA+BA,CAAA;EACtC,IAAIlC,OAAO,CAACyO,yBAAyB,CAAC,EAAE;IACtCA,yBAAyB,GAAG,IAAIC,KAAK,CAAC,KAAK,CAAC;IAC5C,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,KAAK,EAAEA,CAAC,EAAE,EAAE;MAC9Ba,yBAAyB,CAACb,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,CAAC,GAAGA,CAAC;;;AAGpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}