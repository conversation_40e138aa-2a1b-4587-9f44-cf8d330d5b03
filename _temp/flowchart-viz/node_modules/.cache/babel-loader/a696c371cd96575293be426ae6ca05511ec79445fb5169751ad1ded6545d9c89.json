{"ast": null, "code": "/* IMPORT */\nimport _ from '../utils/index.js';\nimport Hex from './hex.js';\nimport HSL from './hsl.js';\nimport Keyword from './keyword.js';\nimport RGB from './rgb.js';\nimport { TYPE } from '../constants.js';\n/* MAIN */\nconst Color = {\n  /* VARIABLES */\n  format: {\n    keyword: Keyword,\n    hex: Hex,\n    rgb: RGB,\n    rgba: RGB,\n    hsl: HSL,\n    hsla: HSL\n  },\n  /* API */\n  parse: color => {\n    if (typeof color !== 'string') return color;\n    const channels = Hex.parse(color) || RGB.parse(color) || HSL.parse(color) || Keyword.parse(color); // Color providers ordered with performance in mind\n    if (channels) return channels;\n    throw new Error(`Unsupported color format: \"${color}\"`);\n  },\n  stringify: channels => {\n    // SASS returns a keyword if possible, but we avoid doing that as it's slower and doesn't really add any value\n    if (!channels.changed && channels.color) return channels.color;\n    if (channels.type.is(TYPE.HSL) || channels.data.r === undefined) {\n      return HSL.stringify(channels);\n    } else if (channels.a < 1 || !Number.isInteger(channels.r) || !Number.isInteger(channels.g) || !Number.isInteger(channels.b)) {\n      return RGB.stringify(channels);\n    } else {\n      return Hex.stringify(channels);\n    }\n  }\n};\n/* EXPORT */\nexport default Color;", "map": {"version": 3, "names": ["_", "Hex", "HSL", "Keyword", "RGB", "TYPE", "Color", "format", "keyword", "hex", "rgb", "rgba", "hsl", "hsla", "parse", "color", "channels", "Error", "stringify", "changed", "type", "is", "data", "r", "undefined", "a", "Number", "isInteger", "g", "b"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/color/index.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Hex from './hex.js';\nimport HSL from './hsl.js';\nimport Keyword from './keyword.js';\nimport RGB from './rgb.js';\nimport { TYPE } from '../constants.js';\n/* MAIN */\nconst Color = {\n    /* VARIABLES */\n    format: {\n        keyword: Keyword,\n        hex: Hex,\n        rgb: RGB,\n        rgba: RGB,\n        hsl: HSL,\n        hsla: HSL\n    },\n    /* API */\n    parse: (color) => {\n        if (typeof color !== 'string')\n            return color;\n        const channels = Hex.parse(color) || RGB.parse(color) || HSL.parse(color) || Keyword.parse(color); // Color providers ordered with performance in mind\n        if (channels)\n            return channels;\n        throw new Error(`Unsupported color format: \"${color}\"`);\n    },\n    stringify: (channels) => {\n        // SASS returns a keyword if possible, but we avoid doing that as it's slower and doesn't really add any value\n        if (!channels.changed && channels.color)\n            return channels.color;\n        if (channels.type.is(TYPE.HSL) || channels.data.r === undefined) {\n            return HSL.stringify(channels);\n        }\n        else if (channels.a < 1 || !Number.isInteger(channels.r) || !Number.isInteger(channels.g) || !Number.isInteger(channels.b)) {\n            return RGB.stringify(channels);\n        }\n        else {\n            return Hex.stringify(channels);\n        }\n    }\n};\n/* EXPORT */\nexport default Color;\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,mBAAmB;AACjC,OAAOC,GAAG,MAAM,UAAU;AAC1B,OAAOC,GAAG,MAAM,UAAU;AAC1B,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAASC,IAAI,QAAQ,iBAAiB;AACtC;AACA,MAAMC,KAAK,GAAG;EACV;EACAC,MAAM,EAAE;IACJC,OAAO,EAAEL,OAAO;IAChBM,GAAG,EAAER,GAAG;IACRS,GAAG,EAAEN,GAAG;IACRO,IAAI,EAAEP,GAAG;IACTQ,GAAG,EAAEV,GAAG;IACRW,IAAI,EAAEX;EACV,CAAC;EACD;EACAY,KAAK,EAAGC,KAAK,IAAK;IACd,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACzB,OAAOA,KAAK;IAChB,MAAMC,QAAQ,GAAGf,GAAG,CAACa,KAAK,CAACC,KAAK,CAAC,IAAIX,GAAG,CAACU,KAAK,CAACC,KAAK,CAAC,IAAIb,GAAG,CAACY,KAAK,CAACC,KAAK,CAAC,IAAIZ,OAAO,CAACW,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;IACnG,IAAIC,QAAQ,EACR,OAAOA,QAAQ;IACnB,MAAM,IAAIC,KAAK,CAAC,8BAA8BF,KAAK,GAAG,CAAC;EAC3D,CAAC;EACDG,SAAS,EAAGF,QAAQ,IAAK;IACrB;IACA,IAAI,CAACA,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACD,KAAK,EACnC,OAAOC,QAAQ,CAACD,KAAK;IACzB,IAAIC,QAAQ,CAACI,IAAI,CAACC,EAAE,CAAChB,IAAI,CAACH,GAAG,CAAC,IAAIc,QAAQ,CAACM,IAAI,CAACC,CAAC,KAAKC,SAAS,EAAE;MAC7D,OAAOtB,GAAG,CAACgB,SAAS,CAACF,QAAQ,CAAC;IAClC,CAAC,MACI,IAAIA,QAAQ,CAACS,CAAC,GAAG,CAAC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACX,QAAQ,CAACO,CAAC,CAAC,IAAI,CAACG,MAAM,CAACC,SAAS,CAACX,QAAQ,CAACY,CAAC,CAAC,IAAI,CAACF,MAAM,CAACC,SAAS,CAACX,QAAQ,CAACa,CAAC,CAAC,EAAE;MACxH,OAAOzB,GAAG,CAACc,SAAS,CAACF,QAAQ,CAAC;IAClC,CAAC,MACI;MACD,OAAOf,GAAG,CAACiB,SAAS,CAACF,QAAQ,CAAC;IAClC;EACJ;AACJ,CAAC;AACD;AACA,eAAeV,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}