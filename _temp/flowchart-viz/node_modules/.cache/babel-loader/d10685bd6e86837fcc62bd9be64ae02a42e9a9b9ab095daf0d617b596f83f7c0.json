{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport function isAstNode(obj) {\n  return typeof obj === 'object' && obj !== null && typeof obj.$type === 'string';\n}\nexport function isReference(obj) {\n  return typeof obj === 'object' && obj !== null && typeof obj.$refText === 'string';\n}\nexport function isAstNodeDescription(obj) {\n  return typeof obj === 'object' && obj !== null && typeof obj.name === 'string' && typeof obj.type === 'string' && typeof obj.path === 'string';\n}\nexport function isLinkingError(obj) {\n  return typeof obj === 'object' && obj !== null && isAstNode(obj.container) && isReference(obj.reference) && typeof obj.message === 'string';\n}\n/**\n * An abstract implementation of the {@link AstReflection} interface.\n * Serves to cache subtype computation results to improve performance throughout different parts of Langium.\n */\nexport class AbstractAstReflection {\n  constructor() {\n    this.subtypes = {};\n    this.allSubtypes = {};\n  }\n  isInstance(node, type) {\n    return isAstNode(node) && this.isSubtype(node.$type, type);\n  }\n  isSubtype(subtype, supertype) {\n    if (subtype === supertype) {\n      return true;\n    }\n    let nested = this.subtypes[subtype];\n    if (!nested) {\n      nested = this.subtypes[subtype] = {};\n    }\n    const existing = nested[supertype];\n    if (existing !== undefined) {\n      return existing;\n    } else {\n      const result = this.computeIsSubtype(subtype, supertype);\n      nested[supertype] = result;\n      return result;\n    }\n  }\n  getAllSubTypes(type) {\n    const existing = this.allSubtypes[type];\n    if (existing) {\n      return existing;\n    } else {\n      const allTypes = this.getAllTypes();\n      const types = [];\n      for (const possibleSubType of allTypes) {\n        if (this.isSubtype(possibleSubType, type)) {\n          types.push(possibleSubType);\n        }\n      }\n      this.allSubtypes[type] = types;\n      return types;\n    }\n  }\n}\nexport function isCompositeCstNode(node) {\n  return typeof node === 'object' && node !== null && Array.isArray(node.content);\n}\nexport function isLeafCstNode(node) {\n  return typeof node === 'object' && node !== null && typeof node.tokenType === 'object';\n}\nexport function isRootCstNode(node) {\n  return isCompositeCstNode(node) && typeof node.fullText === 'string';\n}", "map": {"version": 3, "names": ["isAstNode", "obj", "$type", "isReference", "$refText", "isAstNodeDescription", "name", "type", "path", "isLinkingError", "container", "reference", "message", "AbstractAstReflection", "constructor", "subtypes", "allSubtypes", "isInstance", "node", "isSubtype", "subtype", "supertype", "nested", "existing", "undefined", "result", "computeIsSubtype", "getAllSubTypes", "allTypes", "getAllTypes", "types", "possibleSubType", "push", "isCompositeCstNode", "Array", "isArray", "content", "isLeafCstNode", "tokenType", "isRootCstNode", "fullText"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/syntax-tree.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { TokenType } from 'chevrotain';\r\nimport type { URI } from './utils/uri-utils.js';\r\nimport type { AbstractElement } from './languages/generated/ast.js';\r\nimport type { DocumentSegment, LangiumDocument } from './workspace/documents.js';\r\n\r\n/**\r\n * A node in the Abstract Syntax Tree (AST).\r\n */\r\nexport interface AstNode {\r\n    /** Every AST node has a type corresponding to what was specified in the grammar declaration. */\r\n    readonly $type: string;\r\n    /** The container node in the AST; every node except the root node has a container. */\r\n    readonly $container?: AstNode;\r\n    /** The property of the `$container` node that contains this node. This is either a direct reference or an array. */\r\n    readonly $containerProperty?: string;\r\n    /** In case `$containerProperty` is an array, the array index is stored here. */\r\n    readonly $containerIndex?: number;\r\n    /** The Concrete Syntax Tree (CST) node of the text range from which this node was parsed. */\r\n    readonly $cstNode?: CstNode;\r\n    /** The document containing the AST; only the root node has a direct reference to the document. */\r\n    readonly $document?: LangiumDocument;\r\n}\r\n\r\nexport function isAstNode(obj: unknown): obj is AstNode {\r\n    return typeof obj === 'object' && obj !== null && typeof (obj as AstNode).$type === 'string';\r\n}\r\n\r\nexport interface GenericAstNode extends AstNode {\r\n    [key: string]: unknown\r\n}\r\n\r\ntype SpecificNodeProperties<N extends AstNode> = keyof Omit<N, keyof AstNode | number | symbol>;\r\n\r\n/**\r\n * The property names of a given AST node type.\r\n */\r\nexport type Properties<N extends AstNode> = SpecificNodeProperties<N> extends never ? string : SpecificNodeProperties<N>\r\n\r\n/**\r\n * A cross-reference in the AST. Cross-references may or may not be successfully resolved.\r\n */\r\nexport interface Reference<T extends AstNode = AstNode> {\r\n    /**\r\n     * The target AST node of this reference. Accessing this property may trigger cross-reference\r\n     * resolution by the `Linker` in case it has not been done yet. If the reference cannot be resolved,\r\n     * the value is `undefined`.\r\n     */\r\n    readonly ref?: T;\r\n\r\n    /** If any problem occurred while resolving the reference, it is described by this property. */\r\n    readonly error?: LinkingError;\r\n    /** The CST node from which the reference was parsed */\r\n    readonly $refNode?: CstNode;\r\n    /** The actual text used to look up in the surrounding scope */\r\n    readonly $refText: string;\r\n    /** The node description for the AstNode returned by `ref`  */\r\n    readonly $nodeDescription?: AstNodeDescription;\r\n}\r\n\r\nexport function isReference(obj: unknown): obj is Reference {\r\n    return typeof obj === 'object' && obj !== null && typeof (obj as Reference).$refText === 'string';\r\n}\r\n\r\nexport type ResolvedReference<T extends AstNode = AstNode> = Reference<T> & {\r\n    readonly ref: T;\r\n}\r\n\r\n/**\r\n * A description of an AST node is used when constructing scopes and looking up cross-reference targets.\r\n */\r\nexport interface AstNodeDescription {\r\n    /** The target node; should be present only for local references (linking to the same document). */\r\n    node?: AstNode;\r\n    /**\r\n     * The document segment that represents the range of the name of the AST node.\r\n     */\r\n    nameSegment?: DocumentSegment;\r\n    /**\r\n     * The document segment that represents the full range of the AST node.\r\n     */\r\n    selectionSegment?: DocumentSegment;\r\n    /** `$type` property value of the AST node */\r\n    type: string;\r\n    /** Name of the AST node; this is usually determined by the `NameProvider` service. */\r\n    name: string;\r\n    /** URI to the document containing the AST node */\r\n    documentUri: URI;\r\n    /** Navigation path inside the document */\r\n    path: string;\r\n}\r\n\r\nexport function isAstNodeDescription(obj: unknown): obj is AstNodeDescription {\r\n    return typeof obj === 'object' && obj !== null\r\n        && typeof (obj as AstNodeDescription).name === 'string'\r\n        && typeof (obj as AstNodeDescription).type === 'string'\r\n        && typeof (obj as AstNodeDescription).path === 'string';\r\n}\r\n\r\n/**\r\n * Information about a cross-reference. This is used when traversing references in an AST or to describe\r\n * unresolved references.\r\n */\r\nexport interface ReferenceInfo {\r\n    reference: Reference\r\n    container: AstNode\r\n    property: string\r\n    index?: number\r\n}\r\n\r\n/**\r\n * Used to collect information when the `Linker` service fails to resolve a cross-reference.\r\n */\r\nexport interface LinkingError extends ReferenceInfo {\r\n    message: string;\r\n    targetDescription?: AstNodeDescription;\r\n}\r\n\r\nexport function isLinkingError(obj: unknown): obj is LinkingError {\r\n    return typeof obj === 'object' && obj !== null\r\n        && isAstNode((obj as LinkingError).container)\r\n        && isReference((obj as LinkingError).reference)\r\n        && typeof (obj as LinkingError).message === 'string';\r\n}\r\n\r\n/**\r\n * Service used for generic access to the structure of the AST. This service is shared between\r\n * all involved languages, so it operates on the superset of types of these languages.\r\n */\r\nexport interface AstReflection {\r\n    getAllTypes(): string[]\r\n    getAllSubTypes(type: string): string[]\r\n    getReferenceType(refInfo: ReferenceInfo): string\r\n    getTypeMetaData(type: string): TypeMetaData\r\n    isInstance(node: unknown, type: string): boolean\r\n    isSubtype(subtype: string, supertype: string): boolean\r\n}\r\n\r\n/**\r\n * An abstract implementation of the {@link AstReflection} interface.\r\n * Serves to cache subtype computation results to improve performance throughout different parts of Langium.\r\n */\r\nexport abstract class AbstractAstReflection implements AstReflection {\r\n\r\n    protected subtypes: Record<string, Record<string, boolean | undefined>> = {};\r\n    protected allSubtypes: Record<string, string[] | undefined> = {};\r\n\r\n    abstract getAllTypes(): string[];\r\n    abstract getReferenceType(refInfo: ReferenceInfo): string;\r\n    abstract getTypeMetaData(type: string): TypeMetaData;\r\n    protected abstract computeIsSubtype(subtype: string, supertype: string): boolean;\r\n\r\n    isInstance(node: unknown, type: string): boolean {\r\n        return isAstNode(node) && this.isSubtype(node.$type, type);\r\n    }\r\n\r\n    isSubtype(subtype: string, supertype: string): boolean {\r\n        if (subtype === supertype) {\r\n            return true;\r\n        }\r\n        let nested = this.subtypes[subtype];\r\n        if (!nested) {\r\n            nested = this.subtypes[subtype] = {};\r\n        }\r\n        const existing = nested[supertype];\r\n        if (existing !== undefined) {\r\n            return existing;\r\n        } else {\r\n            const result = this.computeIsSubtype(subtype, supertype);\r\n            nested[supertype] = result;\r\n            return result;\r\n        }\r\n    }\r\n\r\n    getAllSubTypes(type: string): string[] {\r\n        const existing = this.allSubtypes[type];\r\n        if (existing) {\r\n            return existing;\r\n        } else {\r\n            const allTypes = this.getAllTypes();\r\n            const types: string[] = [];\r\n            for (const possibleSubType of allTypes) {\r\n                if (this.isSubtype(possibleSubType, type)) {\r\n                    types.push(possibleSubType);\r\n                }\r\n            }\r\n            this.allSubtypes[type] = types;\r\n            return types;\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Represents runtime meta data about a meta model type.\r\n */\r\nexport interface TypeMetaData {\r\n    /** The name of this meta model type. Corresponds to the `AstNode.$type` value. */\r\n    name: string\r\n    /** A list of properties. They can contain default values for their respective property in the AST. */\r\n    properties: TypeProperty[]\r\n}\r\n\r\n/**\r\n * Describes the meta data of a property of an AST node.\r\n *\r\n * The optional `defaultValue` indicates that the property is mandatory in the AST node.\r\n * For example, if an AST node contains an array, but no elements of this array have been parsed, we still expect an empty array instead of `undefined`.\r\n */\r\nexport interface TypeProperty {\r\n    name: string\r\n    defaultValue?: PropertyType\r\n}\r\n\r\n/**\r\n * Represents a default value for an AST property.\r\n */\r\nexport type PropertyType = number | string | boolean | PropertyType[];\r\n\r\n/**\r\n * A node in the Concrete Syntax Tree (CST).\r\n */\r\nexport interface CstNode extends DocumentSegment {\r\n    /** The container node in the CST */\r\n    readonly container?: CompositeCstNode;\r\n    /** @deprecated use `container` instead. */\r\n    readonly parent?: CompositeCstNode;\r\n    /** The actual text */\r\n    readonly text: string;\r\n    /** The root CST node */\r\n    readonly root: RootCstNode;\r\n    /** The grammar element from which this node was parsed */\r\n    readonly grammarSource?: AbstractElement;\r\n    /** @deprecated use `grammarSource` instead. */\r\n    readonly feature?: AbstractElement;\r\n    /** The AST node created from this CST node */\r\n    readonly astNode: AstNode;\r\n    /** @deprecated use `astNode` instead. */\r\n    readonly element: AstNode;\r\n    /** Whether the token is hidden, i.e. not explicitly part of the containing grammar rule */\r\n    readonly hidden: boolean;\r\n}\r\n\r\n/**\r\n * A composite CST node contains other nodes, but no directly associated token.\r\n */\r\nexport interface CompositeCstNode extends CstNode {\r\n    readonly content: CstNode[];\r\n    /** @deprecated use `content` instead. */\r\n    readonly children: CstNode[];\r\n}\r\n\r\nexport function isCompositeCstNode(node: unknown): node is CompositeCstNode {\r\n    return typeof node === 'object' && node !== null && Array.isArray((node as CompositeCstNode).content);\r\n}\r\n\r\n/**\r\n * A leaf CST node corresponds to a token in the input token stream.\r\n */\r\nexport interface LeafCstNode extends CstNode {\r\n    readonly tokenType: TokenType;\r\n}\r\n\r\nexport function isLeafCstNode(node: unknown): node is LeafCstNode {\r\n    return typeof node === 'object' && node !== null && typeof (node as LeafCstNode).tokenType === 'object';\r\n}\r\n\r\nexport interface RootCstNode extends CompositeCstNode {\r\n    readonly fullText: string\r\n}\r\n\r\nexport function isRootCstNode(node: unknown): node is RootCstNode {\r\n    return isCompositeCstNode(node) && typeof (node as RootCstNode).fullText === 'string';\r\n}\r\n\r\n/**\r\n * Returns a type to have only properties names (!) of a type T whose property value is of a certain type K.\r\n */\r\ntype ExtractKeysOfValueType<T, K> = { [I in keyof T]: T[I] extends K ? I : never }[keyof T];\r\n\r\n/**\r\n * Returns the property names (!) of an AstNode that are cross-references.\r\n * Meant to be used during cross-reference resolution in combination with `assertUnreachable(context.property)`.\r\n */\r\nexport type CrossReferencesOfAstNodeType<N extends AstNode> = (\r\n    ExtractKeysOfValueType<N, Reference|undefined>\r\n    | ExtractKeysOfValueType<N, Array<Reference|undefined>|undefined>\r\n// eslint-disable-next-line @typescript-eslint/ban-types\r\n) & {};\r\n\r\n/**\r\n * Represents the enumeration-like type, that lists all AstNode types of your grammar.\r\n */\r\nexport type AstTypeList<T> = Record<keyof T, AstNode>;\r\n\r\n/**\r\n * Returns all types that contain cross-references, A is meant to be the interface `XXXAstType` fromm your generated `ast.ts` file.\r\n * Meant to be used during cross-reference resolution in combination with `assertUnreachable(context.container)`.\r\n */\r\nexport type AstNodeTypesWithCrossReferences<A extends AstTypeList<A>> = {\r\n    [T in keyof A]: CrossReferencesOfAstNodeType<A[T]> extends never ? never : A[T]\r\n}[keyof A];\r\n\r\nexport type Mutable<T> = {\r\n    -readonly [P in keyof T]: T[P]\r\n};\r\n"], "mappings": "AAAA;;;;;AA6BA,OAAM,SAAUA,SAASA,CAACC,GAAY;EAClC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAQA,GAAe,CAACC,KAAK,KAAK,QAAQ;AAChG;AAkCA,OAAM,SAAUC,WAAWA,CAACF,GAAY;EACpC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAQA,GAAiB,CAACG,QAAQ,KAAK,QAAQ;AACrG;AA8BA,OAAM,SAAUC,oBAAoBA,CAACJ,GAAY;EAC7C,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IACvC,OAAQA,GAA0B,CAACK,IAAI,KAAK,QAAQ,IACpD,OAAQL,GAA0B,CAACM,IAAI,KAAK,QAAQ,IACpD,OAAQN,GAA0B,CAACO,IAAI,KAAK,QAAQ;AAC/D;AAqBA,OAAM,SAAUC,cAAcA,CAACR,GAAY;EACvC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IACvCD,SAAS,CAAEC,GAAoB,CAACS,SAAS,CAAC,IAC1CP,WAAW,CAAEF,GAAoB,CAACU,SAAS,CAAC,IAC5C,OAAQV,GAAoB,CAACW,OAAO,KAAK,QAAQ;AAC5D;AAeA;;;;AAIA,OAAM,MAAgBC,qBAAqB;EAA3CC,YAAA;IAEc,KAAAC,QAAQ,GAAwD,EAAE;IAClE,KAAAC,WAAW,GAAyC,EAAE;EA6CpE;EAtCIC,UAAUA,CAACC,IAAa,EAAEX,IAAY;IAClC,OAAOP,SAAS,CAACkB,IAAI,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,IAAI,CAAChB,KAAK,EAAEK,IAAI,CAAC;EAC9D;EAEAY,SAASA,CAACC,OAAe,EAAEC,SAAiB;IACxC,IAAID,OAAO,KAAKC,SAAS,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAIC,MAAM,GAAG,IAAI,CAACP,QAAQ,CAACK,OAAO,CAAC;IACnC,IAAI,CAACE,MAAM,EAAE;MACTA,MAAM,GAAG,IAAI,CAACP,QAAQ,CAACK,OAAO,CAAC,GAAG,EAAE;IACxC;IACA,MAAMG,QAAQ,GAAGD,MAAM,CAACD,SAAS,CAAC;IAClC,IAAIE,QAAQ,KAAKC,SAAS,EAAE;MACxB,OAAOD,QAAQ;IACnB,CAAC,MAAM;MACH,MAAME,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACN,OAAO,EAAEC,SAAS,CAAC;MACxDC,MAAM,CAACD,SAAS,CAAC,GAAGI,MAAM;MAC1B,OAAOA,MAAM;IACjB;EACJ;EAEAE,cAAcA,CAACpB,IAAY;IACvB,MAAMgB,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACT,IAAI,CAAC;IACvC,IAAIgB,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB,CAAC,MAAM;MACH,MAAMK,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;MACnC,MAAMC,KAAK,GAAa,EAAE;MAC1B,KAAK,MAAMC,eAAe,IAAIH,QAAQ,EAAE;QACpC,IAAI,IAAI,CAACT,SAAS,CAACY,eAAe,EAAExB,IAAI,CAAC,EAAE;UACvCuB,KAAK,CAACE,IAAI,CAACD,eAAe,CAAC;QAC/B;MACJ;MACA,IAAI,CAACf,WAAW,CAACT,IAAI,CAAC,GAAGuB,KAAK;MAC9B,OAAOA,KAAK;IAChB;EACJ;;AA8DJ,OAAM,SAAUG,kBAAkBA,CAACf,IAAa;EAC5C,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAIgB,KAAK,CAACC,OAAO,CAAEjB,IAAyB,CAACkB,OAAO,CAAC;AACzG;AASA,OAAM,SAAUC,aAAaA,CAACnB,IAAa;EACvC,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAQA,IAAoB,CAACoB,SAAS,KAAK,QAAQ;AAC3G;AAMA,OAAM,SAAUC,aAAaA,CAACrB,IAAa;EACvC,OAAOe,kBAAkB,CAACf,IAAI,CAAC,IAAI,OAAQA,IAAoB,CAACsB,QAAQ,KAAK,QAAQ;AACzF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}