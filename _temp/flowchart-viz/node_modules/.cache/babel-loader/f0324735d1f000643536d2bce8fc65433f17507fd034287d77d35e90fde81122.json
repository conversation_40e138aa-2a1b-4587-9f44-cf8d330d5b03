{"ast": null, "code": "export var abs = Math.abs;\nexport var cos = Math.cos;\nexport var sin = Math.sin;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = pi * 2;\nexport var max = Math.max;\nexport var epsilon = 1e-12;", "map": {"version": 3, "names": ["abs", "Math", "cos", "sin", "pi", "PI", "halfPi", "tau", "max", "epsilon"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-chord/src/math.js"], "sourcesContent": ["export var abs = Math.abs;\nexport var cos = Math.cos;\nexport var sin = Math.sin;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = pi * 2;\nexport var max = Math.max;\nexport var epsilon = 1e-12;\n"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAG;AACzB,OAAO,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;AACzB,OAAO,IAAIC,EAAE,GAAGH,IAAI,CAACI,EAAE;AACvB,OAAO,IAAIC,MAAM,GAAGF,EAAE,GAAG,CAAC;AAC1B,OAAO,IAAIG,GAAG,GAAGH,EAAE,GAAG,CAAC;AACvB,OAAO,IAAII,GAAG,GAAGP,IAAI,CAACO,GAAG;AACzB,OAAO,IAAIC,OAAO,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}