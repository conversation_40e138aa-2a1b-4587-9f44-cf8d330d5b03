{"ast": null, "code": "import { RegExpParser } from \"@chevrotain/regexp-to-ast\";\nlet regExpAstCache = {};\nconst regExpParser = new RegExpParser();\nexport function getRegExpAst(regExp) {\n  const regExpStr = regExp.toString();\n  if (regExpAstCache.hasOwnProperty(regExpStr)) {\n    return regExpAstCache[regExpStr];\n  } else {\n    const regExpAst = regExpParser.pattern(regExpStr);\n    regExpAstCache[regExpStr] = regExpAst;\n    return regExpAst;\n  }\n}\nexport function clearRegExpParserCache() {\n  regExpAstCache = {};\n}", "map": {"version": 3, "names": ["Reg<PERSON>xp<PERSON><PERSON><PERSON>", "regExpAstCache", "regEx<PERSON><PERSON><PERSON><PERSON>", "getRegExpAst", "regExp", "regExpStr", "toString", "hasOwnProperty", "regExpAst", "pattern", "clearRegExpParserCache"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/reg_exp_parser.ts"], "sourcesContent": ["import {\n  Alternative,\n  Assertion,\n  Atom,\n  Disjunction,\n  RegExpParser,\n  RegExpPattern,\n} from \"@chevrotain/regexp-to-ast\";\n\nlet regExpAstCache: { [regex: string]: RegExpPattern } = {};\nconst regExpParser = new RegExpParser();\n\n// this should be moved to regexp-to-ast\nexport type ASTNode =\n  | RegExpPattern\n  | Disjunction\n  | Alternative\n  | Assertion\n  | Atom;\n\nexport function getRegExpAst(regExp: RegExp): RegExpPattern {\n  const regExpStr = regExp.toString();\n  if (regExpAstCache.hasOwnProperty(regExpStr)) {\n    return regExpAstCache[regExpStr];\n  } else {\n    const regExpAst = regExpParser.pattern(regExpStr);\n    regExpAstCache[regExpStr] = regExpAst;\n    return regExpAst;\n  }\n}\n\nexport function clearRegExpParserCache() {\n  regExpAstCache = {};\n}\n"], "mappings": "AAAA,SAKEA,YAAY,QAEP,2BAA2B;AAElC,IAAIC,cAAc,GAAuC,EAAE;AAC3D,MAAMC,YAAY,GAAG,IAAIF,YAAY,EAAE;AAUvC,OAAM,SAAUG,YAAYA,CAACC,MAAc;EACzC,MAAMC,SAAS,GAAGD,MAAM,CAACE,QAAQ,EAAE;EACnC,IAAIL,cAAc,CAACM,cAAc,CAACF,SAAS,CAAC,EAAE;IAC5C,OAAOJ,cAAc,CAACI,SAAS,CAAC;GACjC,MAAM;IACL,MAAMG,SAAS,GAAGN,YAAY,CAACO,OAAO,CAACJ,SAAS,CAAC;IACjDJ,cAAc,CAACI,SAAS,CAAC,GAAGG,SAAS;IACrC,OAAOA,SAAS;;AAEpB;AAEA,OAAM,SAAUE,sBAAsBA,CAAA;EACpCT,cAAc,GAAG,EAAE;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}