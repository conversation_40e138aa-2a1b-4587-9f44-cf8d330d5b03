{"ast": null, "code": "export { PriorityQueue };\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nclass PriorityQueue {\n  constructor() {\n    this._arr = [];\n    this._keyIndices = {};\n  }\n  /**\n   * Returns the number of elements in the queue. Takes `O(1)` time.\n   */\n  size() {\n    return this._arr.length;\n  }\n  /**\n   * Returns the keys that are in the queue. Takes `O(n)` time.\n   */\n  keys() {\n    return this._arr.map(function (x) {\n      return x.key;\n    });\n  }\n  /**\n   * Returns `true` if **key** is in the queue and `false` if not.\n   */\n  has(key) {\n    return Object.prototype.hasOwnProperty.call(this._keyIndices, key);\n  }\n  /**\n   * Returns the priority for **key**. If **key** is not present in the queue\n   * then this function returns `undefined`. Takes `O(1)` time.\n   *\n   * @param {Object} key\n   */\n  priority(key) {\n    var index = this._keyIndices[key];\n    if (index !== undefined) {\n      return this._arr[index].priority;\n    }\n  }\n  /**\n   * Returns the key for the minimum element in this queue. If the queue is\n   * empty this function throws an Error. Takes `O(1)` time.\n   */\n  min() {\n    if (this.size() === 0) {\n      throw new Error('Queue underflow');\n    }\n    return this._arr[0].key;\n  }\n  /**\n   * Inserts a new key into the priority queue. If the key already exists in\n   * the queue this function returns `false`; otherwise it will return `true`.\n   * Takes `O(n)` time.\n   *\n   * @param {Object} key the key to add\n   * @param {Number} priority the initial priority for the key\n   */\n  add(key, priority) {\n    var keyIndices = this._keyIndices;\n    key = String(key);\n    if (!Object.prototype.hasOwnProperty.call(keyIndices, key)) {\n      var arr = this._arr;\n      var index = arr.length;\n      keyIndices[key] = index;\n      arr.push({\n        key: key,\n        priority: priority\n      });\n      this._decrease(index);\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n   */\n  removeMin() {\n    this._swap(0, this._arr.length - 1);\n    var min = this._arr.pop();\n    delete this._keyIndices[min.key];\n    this._heapify(0);\n    return min.key;\n  }\n  /**\n   * Decreases the priority for **key** to **priority**. If the new priority is\n   * greater than the previous priority, this function will throw an Error.\n   *\n   * @param {Object} key the key for which to raise priority\n   * @param {Number} priority the new priority for the key\n   */\n  decrease(key, priority) {\n    var index = this._keyIndices[key];\n    if (priority > this._arr[index].priority) {\n      throw new Error('New priority is greater than current priority. ' + 'Key: ' + key + ' Old: ' + this._arr[index].priority + ' New: ' + priority);\n    }\n    this._arr[index].priority = priority;\n    this._decrease(index);\n  }\n  _heapify(i) {\n    var arr = this._arr;\n    var l = 2 * i;\n    var r = l + 1;\n    var largest = i;\n    if (l < arr.length) {\n      largest = arr[l].priority < arr[largest].priority ? l : largest;\n      if (r < arr.length) {\n        largest = arr[r].priority < arr[largest].priority ? r : largest;\n      }\n      if (largest !== i) {\n        this._swap(i, largest);\n        this._heapify(largest);\n      }\n    }\n  }\n  _decrease(index) {\n    var arr = this._arr;\n    var priority = arr[index].priority;\n    var parent;\n    while (index !== 0) {\n      parent = index >> 1;\n      if (arr[parent].priority < priority) {\n        break;\n      }\n      this._swap(index, parent);\n      index = parent;\n    }\n  }\n  _swap(i, j) {\n    var arr = this._arr;\n    var keyIndices = this._keyIndices;\n    var origArrI = arr[i];\n    var origArrJ = arr[j];\n    arr[i] = origArrJ;\n    arr[j] = origArrI;\n    keyIndices[origArrJ.key] = i;\n    keyIndices[origArrI.key] = j;\n  }\n}", "map": {"version": 3, "names": ["PriorityQueue", "constructor", "_arr", "_keyIndices", "size", "length", "keys", "map", "x", "key", "has", "Object", "prototype", "hasOwnProperty", "call", "priority", "index", "undefined", "min", "Error", "add", "keyIndices", "String", "arr", "push", "_decrease", "removeMin", "_swap", "pop", "_heapify", "decrease", "i", "l", "r", "largest", "parent", "j", "origArrI", "origArrJ"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/graphlib/data/priority-queue.js"], "sourcesContent": ["export { PriorityQueue };\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nclass PriorityQueue {\n  constructor() {\n    this._arr = [];\n    this._keyIndices = {};\n  }\n  /**\n   * Returns the number of elements in the queue. Takes `O(1)` time.\n   */\n  size() {\n    return this._arr.length;\n  }\n  /**\n   * Returns the keys that are in the queue. Takes `O(n)` time.\n   */\n  keys() {\n    return this._arr.map(function (x) {\n      return x.key;\n    });\n  }\n  /**\n   * Returns `true` if **key** is in the queue and `false` if not.\n   */\n  has(key) {\n    return Object.prototype.hasOwnProperty.call(this._keyIndices, key);\n  }\n  /**\n   * Returns the priority for **key**. If **key** is not present in the queue\n   * then this function returns `undefined`. Takes `O(1)` time.\n   *\n   * @param {Object} key\n   */\n  priority(key) {\n    var index = this._keyIndices[key];\n    if (index !== undefined) {\n      return this._arr[index].priority;\n    }\n  }\n  /**\n   * Returns the key for the minimum element in this queue. If the queue is\n   * empty this function throws an Error. Takes `O(1)` time.\n   */\n  min() {\n    if (this.size() === 0) {\n      throw new Error('Queue underflow');\n    }\n    return this._arr[0].key;\n  }\n  /**\n   * Inserts a new key into the priority queue. If the key already exists in\n   * the queue this function returns `false`; otherwise it will return `true`.\n   * Takes `O(n)` time.\n   *\n   * @param {Object} key the key to add\n   * @param {Number} priority the initial priority for the key\n   */\n  add(key, priority) {\n    var keyIndices = this._keyIndices;\n    key = String(key);\n    if (!Object.prototype.hasOwnProperty.call(keyIndices, key)) {\n      var arr = this._arr;\n      var index = arr.length;\n      keyIndices[key] = index;\n      arr.push({ key: key, priority: priority });\n      this._decrease(index);\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n   */\n  removeMin() {\n    this._swap(0, this._arr.length - 1);\n    var min = this._arr.pop();\n    delete this._keyIndices[min.key];\n    this._heapify(0);\n    return min.key;\n  }\n  /**\n   * Decreases the priority for **key** to **priority**. If the new priority is\n   * greater than the previous priority, this function will throw an Error.\n   *\n   * @param {Object} key the key for which to raise priority\n   * @param {Number} priority the new priority for the key\n   */\n  decrease(key, priority) {\n    var index = this._keyIndices[key];\n    if (priority > this._arr[index].priority) {\n      throw new Error(\n        'New priority is greater than current priority. ' +\n          'Key: ' +\n          key +\n          ' Old: ' +\n          this._arr[index].priority +\n          ' New: ' +\n          priority,\n      );\n    }\n    this._arr[index].priority = priority;\n    this._decrease(index);\n  }\n  _heapify(i) {\n    var arr = this._arr;\n    var l = 2 * i;\n    var r = l + 1;\n    var largest = i;\n    if (l < arr.length) {\n      largest = arr[l].priority < arr[largest].priority ? l : largest;\n      if (r < arr.length) {\n        largest = arr[r].priority < arr[largest].priority ? r : largest;\n      }\n      if (largest !== i) {\n        this._swap(i, largest);\n        this._heapify(largest);\n      }\n    }\n  }\n  _decrease(index) {\n    var arr = this._arr;\n    var priority = arr[index].priority;\n    var parent;\n    while (index !== 0) {\n      parent = index >> 1;\n      if (arr[parent].priority < priority) {\n        break;\n      }\n      this._swap(index, parent);\n      index = parent;\n    }\n  }\n  _swap(i, j) {\n    var arr = this._arr;\n    var keyIndices = this._keyIndices;\n    var origArrI = arr[i];\n    var origArrJ = arr[j];\n    arr[i] = origArrJ;\n    arr[j] = origArrI;\n    keyIndices[origArrJ.key] = i;\n    keyIndices[origArrI.key] = j;\n  }\n}\n"], "mappings": "AAAA,SAASA,aAAa;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;EACvB;EACA;AACF;AACA;EACEC,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACF,IAAI,CAACG,MAAM;EACzB;EACA;AACF;AACA;EACEC,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,UAAUC,CAAC,EAAE;MAChC,OAAOA,CAAC,CAACC,GAAG;IACd,CAAC,CAAC;EACJ;EACA;AACF;AACA;EACEC,GAAGA,CAACD,GAAG,EAAE;IACP,OAAOE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAACX,WAAW,EAAEM,GAAG,CAAC;EACpE;EACA;AACF;AACA;AACA;AACA;AACA;EACEM,QAAQA,CAACN,GAAG,EAAE;IACZ,IAAIO,KAAK,GAAG,IAAI,CAACb,WAAW,CAACM,GAAG,CAAC;IACjC,IAAIO,KAAK,KAAKC,SAAS,EAAE;MACvB,OAAO,IAAI,CAACf,IAAI,CAACc,KAAK,CAAC,CAACD,QAAQ;IAClC;EACF;EACA;AACF;AACA;AACA;EACEG,GAAGA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACd,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;MACrB,MAAM,IAAIe,KAAK,CAAC,iBAAiB,CAAC;IACpC;IACA,OAAO,IAAI,CAACjB,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG;EACzB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEW,GAAGA,CAACX,GAAG,EAAEM,QAAQ,EAAE;IACjB,IAAIM,UAAU,GAAG,IAAI,CAAClB,WAAW;IACjCM,GAAG,GAAGa,MAAM,CAACb,GAAG,CAAC;IACjB,IAAI,CAACE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACO,UAAU,EAAEZ,GAAG,CAAC,EAAE;MAC1D,IAAIc,GAAG,GAAG,IAAI,CAACrB,IAAI;MACnB,IAAIc,KAAK,GAAGO,GAAG,CAAClB,MAAM;MACtBgB,UAAU,CAACZ,GAAG,CAAC,GAAGO,KAAK;MACvBO,GAAG,CAACC,IAAI,CAAC;QAAEf,GAAG,EAAEA,GAAG;QAAEM,QAAQ,EAAEA;MAAS,CAAC,CAAC;MAC1C,IAAI,CAACU,SAAS,CAACT,KAAK,CAAC;MACrB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA;AACF;AACA;EACEU,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzB,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;IACnC,IAAIa,GAAG,GAAG,IAAI,CAAChB,IAAI,CAAC0B,GAAG,CAAC,CAAC;IACzB,OAAO,IAAI,CAACzB,WAAW,CAACe,GAAG,CAACT,GAAG,CAAC;IAChC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAAC;IAChB,OAAOX,GAAG,CAACT,GAAG;EAChB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEqB,QAAQA,CAACrB,GAAG,EAAEM,QAAQ,EAAE;IACtB,IAAIC,KAAK,GAAG,IAAI,CAACb,WAAW,CAACM,GAAG,CAAC;IACjC,IAAIM,QAAQ,GAAG,IAAI,CAACb,IAAI,CAACc,KAAK,CAAC,CAACD,QAAQ,EAAE;MACxC,MAAM,IAAII,KAAK,CACb,iDAAiD,GAC/C,OAAO,GACPV,GAAG,GACH,QAAQ,GACR,IAAI,CAACP,IAAI,CAACc,KAAK,CAAC,CAACD,QAAQ,GACzB,QAAQ,GACRA,QACJ,CAAC;IACH;IACA,IAAI,CAACb,IAAI,CAACc,KAAK,CAAC,CAACD,QAAQ,GAAGA,QAAQ;IACpC,IAAI,CAACU,SAAS,CAACT,KAAK,CAAC;EACvB;EACAa,QAAQA,CAACE,CAAC,EAAE;IACV,IAAIR,GAAG,GAAG,IAAI,CAACrB,IAAI;IACnB,IAAI8B,CAAC,GAAG,CAAC,GAAGD,CAAC;IACb,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC;IACb,IAAIE,OAAO,GAAGH,CAAC;IACf,IAAIC,CAAC,GAAGT,GAAG,CAAClB,MAAM,EAAE;MAClB6B,OAAO,GAAGX,GAAG,CAACS,CAAC,CAAC,CAACjB,QAAQ,GAAGQ,GAAG,CAACW,OAAO,CAAC,CAACnB,QAAQ,GAAGiB,CAAC,GAAGE,OAAO;MAC/D,IAAID,CAAC,GAAGV,GAAG,CAAClB,MAAM,EAAE;QAClB6B,OAAO,GAAGX,GAAG,CAACU,CAAC,CAAC,CAAClB,QAAQ,GAAGQ,GAAG,CAACW,OAAO,CAAC,CAACnB,QAAQ,GAAGkB,CAAC,GAAGC,OAAO;MACjE;MACA,IAAIA,OAAO,KAAKH,CAAC,EAAE;QACjB,IAAI,CAACJ,KAAK,CAACI,CAAC,EAAEG,OAAO,CAAC;QACtB,IAAI,CAACL,QAAQ,CAACK,OAAO,CAAC;MACxB;IACF;EACF;EACAT,SAASA,CAACT,KAAK,EAAE;IACf,IAAIO,GAAG,GAAG,IAAI,CAACrB,IAAI;IACnB,IAAIa,QAAQ,GAAGQ,GAAG,CAACP,KAAK,CAAC,CAACD,QAAQ;IAClC,IAAIoB,MAAM;IACV,OAAOnB,KAAK,KAAK,CAAC,EAAE;MAClBmB,MAAM,GAAGnB,KAAK,IAAI,CAAC;MACnB,IAAIO,GAAG,CAACY,MAAM,CAAC,CAACpB,QAAQ,GAAGA,QAAQ,EAAE;QACnC;MACF;MACA,IAAI,CAACY,KAAK,CAACX,KAAK,EAAEmB,MAAM,CAAC;MACzBnB,KAAK,GAAGmB,MAAM;IAChB;EACF;EACAR,KAAKA,CAACI,CAAC,EAAEK,CAAC,EAAE;IACV,IAAIb,GAAG,GAAG,IAAI,CAACrB,IAAI;IACnB,IAAImB,UAAU,GAAG,IAAI,CAAClB,WAAW;IACjC,IAAIkC,QAAQ,GAAGd,GAAG,CAACQ,CAAC,CAAC;IACrB,IAAIO,QAAQ,GAAGf,GAAG,CAACa,CAAC,CAAC;IACrBb,GAAG,CAACQ,CAAC,CAAC,GAAGO,QAAQ;IACjBf,GAAG,CAACa,CAAC,CAAC,GAAGC,QAAQ;IACjBhB,UAAU,CAACiB,QAAQ,CAAC7B,GAAG,CAAC,GAAGsB,CAAC;IAC5BV,UAAU,CAACgB,QAAQ,CAAC5B,GAAG,CAAC,GAAG2B,CAAC;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}