{"ast": null, "code": "export default {\n  draw: function (context, size) {\n    var w = Math.sqrt(size),\n      x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};", "map": {"version": 3, "names": ["draw", "context", "size", "w", "Math", "sqrt", "x", "rect"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/symbol/square.js"], "sourcesContent": ["export default {\n  draw: function(context, size) {\n    var w = Math.sqrt(size),\n        x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n"], "mappings": "AAAA,eAAe;EACbA,IAAI,EAAE,SAAAA,CAASC,OAAO,EAAEC,IAAI,EAAE;IAC5B,IAAIC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACH,IAAI,CAAC;MACnBI,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC;IACdF,OAAO,CAACM,IAAI,CAACD,CAAC,EAAEA,CAAC,EAAEH,CAAC,EAAEA,CAAC,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}