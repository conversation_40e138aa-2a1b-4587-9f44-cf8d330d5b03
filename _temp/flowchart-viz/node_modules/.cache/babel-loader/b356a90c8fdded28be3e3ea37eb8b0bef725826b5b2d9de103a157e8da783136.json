{"ast": null, "code": "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst mul = 0x19660D;\nconst inc = 0x3C6EF35F;\nconst eps = 1 / 0x100000000;\nexport default function lcg(seed = Math.random()) {\n  let state = (0 <= seed && seed < 1 ? seed / eps : Math.abs(seed)) | 0;\n  return () => (state = mul * state + inc | 0, eps * (state >>> 0));\n}", "map": {"version": 3, "names": ["mul", "inc", "eps", "lcg", "seed", "Math", "random", "state", "abs"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/lcg.js"], "sourcesContent": ["// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst mul = 0x19660D;\nconst inc = 0x3C6EF35F;\nconst eps = 1 / 0x100000000;\n\nexport default function lcg(seed = Math.random()) {\n  let state = (0 <= seed && seed < 1 ? seed / eps : Math.abs(seed)) | 0;\n  return () => (state = mul * state + inc | 0, eps * (state >>> 0));\n}\n"], "mappings": "AAAA;AACA,MAAMA,GAAG,GAAG,QAAQ;AACpB,MAAMC,GAAG,GAAG,UAAU;AACtB,MAAMC,GAAG,GAAG,CAAC,GAAG,WAAW;AAE3B,eAAe,SAASC,GAAGA,CAACC,IAAI,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;EAChD,IAAIC,KAAK,GAAG,CAAC,CAAC,IAAIH,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGF,GAAG,GAAGG,IAAI,CAACG,GAAG,CAACJ,IAAI,CAAC,IAAI,CAAC;EACrE,OAAO,OAAOG,KAAK,GAAGP,GAAG,GAAGO,KAAK,GAAGN,GAAG,GAAG,CAAC,EAAEC,GAAG,IAAIK,KAAK,KAAK,CAAC,CAAC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}