{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"edf8b17fcdbb2c7fb8\", \"ffffcca1dab441b6c4225ea8\", \"ffffcca1dab441b6c42c7fb8253494\", \"ffffccc7e9b47fcdbb41b6c42c7fb8253494\", \"ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84\", \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84\", \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-scale-chromatic/src/sequential-multi/YlGnBu.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"edf8b17fcdbb2c7fb8\",\n  \"ffffcca1dab441b6c4225ea8\",\n  \"ffffcca1dab441b6c42c7fb8253494\",\n  \"ffffccc7e9b47fcdbb41b6c42c7fb8253494\",\n  \"ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84\",\n  \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84\",\n  \"ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}