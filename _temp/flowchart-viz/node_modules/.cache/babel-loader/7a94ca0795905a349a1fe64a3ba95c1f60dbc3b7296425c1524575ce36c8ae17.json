{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { Emitter } from '../utils/event.js';\nimport { Deferred } from '../utils/promise-utils.js';\n/**\n * Base configuration provider for building up other configuration providers\n */\nexport class DefaultConfigurationProvider {\n  constructor(services) {\n    this._ready = new Deferred();\n    this.settings = {};\n    this.workspaceConfig = false;\n    this.onConfigurationSectionUpdateEmitter = new Emitter();\n    this.serviceRegistry = services.ServiceRegistry;\n  }\n  get ready() {\n    return this._ready.promise;\n  }\n  initialize(params) {\n    var _a, _b;\n    this.workspaceConfig = (_b = (_a = params.capabilities.workspace) === null || _a === void 0 ? void 0 : _a.configuration) !== null && _b !== void 0 ? _b : false;\n  }\n  async initialized(params) {\n    if (this.workspaceConfig) {\n      if (params.register) {\n        // params.register(...) is a function to be provided by the calling language server for the sake of\n        //  decoupling this implementation from the concrete LSP implementations, specifically the LSP Connection\n        const languages = this.serviceRegistry.all;\n        params.register({\n          // Listen to configuration changes for all languages\n          section: languages.map(lang => this.toSectionName(lang.LanguageMetaData.languageId))\n        });\n      }\n      if (params.fetchConfiguration) {\n        // params.fetchConfiguration(...) is a function to be provided by the calling language server for the sake of\n        //  decoupling this implementation from the concrete LSP implementations, specifically the LSP Connection\n        const configToUpdate = this.serviceRegistry.all.map(lang => ({\n          // Fetch the configuration changes for all languages\n          section: this.toSectionName(lang.LanguageMetaData.languageId)\n        }));\n        // get workspace configurations (default scope URI)\n        const configs = await params.fetchConfiguration(configToUpdate);\n        configToUpdate.forEach((conf, idx) => {\n          this.updateSectionConfiguration(conf.section, configs[idx]);\n        });\n      }\n    }\n    this._ready.resolve();\n  }\n  /**\n   *  Updates the cached configurations using the `change` notification parameters.\n   *\n   * @param change The parameters of a change configuration notification.\n   * `settings` property of the change object could be expressed as `Record<string, Record<string, any>>`\n   */\n  updateConfiguration(change) {\n    if (!change.settings) {\n      return;\n    }\n    Object.keys(change.settings).forEach(section => {\n      const configuration = change.settings[section];\n      this.updateSectionConfiguration(section, configuration);\n      this.onConfigurationSectionUpdateEmitter.fire({\n        section,\n        configuration\n      });\n    });\n  }\n  updateSectionConfiguration(section, configuration) {\n    this.settings[section] = configuration;\n  }\n  /**\n  * Returns a configuration value stored for the given language.\n  *\n  * @param language The language id\n  * @param configuration Configuration name\n  */\n  async getConfiguration(language, configuration) {\n    await this.ready;\n    const sectionName = this.toSectionName(language);\n    if (this.settings[sectionName]) {\n      return this.settings[sectionName][configuration];\n    }\n  }\n  toSectionName(languageId) {\n    return `${languageId}`;\n  }\n  get onConfigurationSectionUpdate() {\n    return this.onConfigurationSectionUpdateEmitter.event;\n  }\n}", "map": {"version": 3, "names": ["Emitter", "Deferred", "DefaultConfigurationProvider", "constructor", "services", "_ready", "settings", "workspaceConfig", "onConfigurationSectionUpdateEmitter", "serviceRegistry", "ServiceRegistry", "ready", "promise", "initialize", "params", "_b", "_a", "capabilities", "workspace", "configuration", "initialized", "register", "languages", "all", "section", "map", "lang", "toSectionName", "LanguageMetaData", "languageId", "fetchConfiguration", "configToUpdate", "configs", "for<PERSON>ach", "conf", "idx", "updateSectionConfiguration", "resolve", "updateConfiguration", "change", "Object", "keys", "fire", "getConfiguration", "language", "sectionName", "onConfigurationSectionUpdate", "event"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/configuration.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { Emitter } from '../utils/event.js';\r\nimport type {\r\n    ConfigurationItem,\r\n    DidChangeConfigurationParams,\r\n    DidChangeConfigurationRegistrationOptions,\r\n    Disposable,\r\n    Event,\r\n    InitializeParams,\r\n    InitializedParams\r\n} from 'vscode-languageserver-protocol';\r\nimport type { ServiceRegistry } from '../service-registry.js';\r\nimport type { LangiumSharedCoreServices } from '../services.js';\r\nimport { Deferred } from '../utils/promise-utils.js';\r\n\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n\r\nexport interface ConfigurationProvider {\r\n\r\n    /**\r\n     * A promise that resolves when the configuration provider is ready to be used.\r\n     */\r\n    readonly ready: Promise<void>;\r\n\r\n    /**\r\n     * When used in a language server context, this method is called when the server receives\r\n     * the `initialize` request.\r\n     */\r\n    initialize(params: InitializeParams): void;\r\n\r\n    /**\r\n     * When used in a language server context, this method is called when the server receives\r\n     * the `initialized` notification.\r\n     */\r\n    initialized(params: ConfigurationInitializedParams): Promise<void>;\r\n\r\n    /**\r\n     * Returns a configuration value stored for the given language.\r\n     *\r\n     * @param language The language id\r\n     * @param configuration Configuration name\r\n     */\r\n    getConfiguration(language: string, configuration: string): Promise<any>;\r\n\r\n    /**\r\n     *  Updates the cached configurations using the `change` notification parameters.\r\n     *\r\n     * @param change The parameters of a change configuration notification.\r\n     * `settings` property of the change object could be expressed as `Record<string, Record<string, any>>`\r\n     */\r\n    updateConfiguration(change: DidChangeConfigurationParams): void;\r\n\r\n    /**\r\n     * Get notified after a configuration section has been updated.\r\n     */\r\n    onConfigurationSectionUpdate(callback: ConfigurationSectionUpdateListener): Disposable\r\n}\r\n\r\nexport interface ConfigurationInitializedParams extends InitializedParams {\r\n    register?: (params: DidChangeConfigurationRegistrationOptions) => void,\r\n    fetchConfiguration?: (configuration: ConfigurationItem[]) => Promise<any>\r\n}\r\n\r\nexport interface ConfigurationSectionUpdate {\r\n    /**\r\n     * The name of the configuration section that has been updated.\r\n     */\r\n    section: string;\r\n\r\n    /**\r\n     * The updated configuration section.\r\n     */\r\n    configuration: any;\r\n}\r\n\r\nexport type ConfigurationSectionUpdateListener = (update: ConfigurationSectionUpdate) => void;\r\n\r\n/**\r\n * Base configuration provider for building up other configuration providers\r\n */\r\nexport class DefaultConfigurationProvider implements ConfigurationProvider {\r\n\r\n    protected readonly serviceRegistry: ServiceRegistry;\r\n    protected readonly _ready = new Deferred<void>();\r\n    protected settings: Record<string, Record<string, any>> = {};\r\n    protected workspaceConfig = false;\r\n    protected onConfigurationSectionUpdateEmitter = new Emitter<ConfigurationSectionUpdate>();\r\n\r\n    constructor(services: LangiumSharedCoreServices) {\r\n        this.serviceRegistry = services.ServiceRegistry;\r\n    }\r\n\r\n    get ready(): Promise<void> {\r\n        return this._ready.promise;\r\n    }\r\n\r\n    initialize(params: InitializeParams): void {\r\n        this.workspaceConfig = params.capabilities.workspace?.configuration ?? false;\r\n    }\r\n\r\n    async initialized(params: ConfigurationInitializedParams): Promise<void> {\r\n        if (this.workspaceConfig) {\r\n            if (params.register) {\r\n                // params.register(...) is a function to be provided by the calling language server for the sake of\r\n                //  decoupling this implementation from the concrete LSP implementations, specifically the LSP Connection\r\n\r\n                const languages = this.serviceRegistry.all;\r\n                params.register({\r\n                    // Listen to configuration changes for all languages\r\n                    section: languages.map(lang => this.toSectionName(lang.LanguageMetaData.languageId))\r\n                });\r\n            }\r\n\r\n            if (params.fetchConfiguration) {\r\n                // params.fetchConfiguration(...) is a function to be provided by the calling language server for the sake of\r\n                //  decoupling this implementation from the concrete LSP implementations, specifically the LSP Connection\r\n                const configToUpdate = this.serviceRegistry.all.map(lang => <ConfigurationItem>{\r\n                    // Fetch the configuration changes for all languages\r\n                    section: this.toSectionName(lang.LanguageMetaData.languageId)\r\n                });\r\n\r\n                // get workspace configurations (default scope URI)\r\n                const configs = await params.fetchConfiguration(configToUpdate);\r\n                configToUpdate.forEach((conf, idx) => {\r\n                    this.updateSectionConfiguration(conf.section!, configs[idx]);\r\n                });\r\n            }\r\n        }\r\n        this._ready.resolve();\r\n    }\r\n\r\n    /**\r\n     *  Updates the cached configurations using the `change` notification parameters.\r\n     *\r\n     * @param change The parameters of a change configuration notification.\r\n     * `settings` property of the change object could be expressed as `Record<string, Record<string, any>>`\r\n     */\r\n    updateConfiguration(change: DidChangeConfigurationParams): void {\r\n        if (!change.settings) {\r\n            return;\r\n        }\r\n        Object.keys(change.settings).forEach(section => {\r\n            const configuration = change.settings[section];\r\n            this.updateSectionConfiguration(section, configuration);\r\n            this.onConfigurationSectionUpdateEmitter.fire({ section, configuration });\r\n        });\r\n    }\r\n\r\n    protected updateSectionConfiguration(section: string, configuration: any): void {\r\n        this.settings[section] = configuration;\r\n    }\r\n\r\n    /**\r\n    * Returns a configuration value stored for the given language.\r\n    *\r\n    * @param language The language id\r\n    * @param configuration Configuration name\r\n    */\r\n    async getConfiguration(language: string, configuration: string): Promise<any> {\r\n        await this.ready;\r\n\r\n        const sectionName = this.toSectionName(language);\r\n        if (this.settings[sectionName]) {\r\n            return this.settings[sectionName][configuration];\r\n        }\r\n    }\r\n\r\n    protected toSectionName(languageId: string): string {\r\n        return `${languageId}`;\r\n    }\r\n\r\n    get onConfigurationSectionUpdate(): Event<ConfigurationSectionUpdate> {\r\n        return this.onConfigurationSectionUpdateEmitter.event;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,OAAO,QAAQ,mBAAmB;AAY3C,SAASC,QAAQ,QAAQ,2BAA2B;AAgEpD;;;AAGA,OAAM,MAAOC,4BAA4B;EAQrCC,YAAYC,QAAmC;IAL5B,KAAAC,MAAM,GAAG,IAAIJ,QAAQ,EAAQ;IACtC,KAAAK,QAAQ,GAAwC,EAAE;IAClD,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,mCAAmC,GAAG,IAAIR,OAAO,EAA8B;IAGrF,IAAI,CAACS,eAAe,GAAGL,QAAQ,CAACM,eAAe;EACnD;EAEA,IAAIC,KAAKA,CAAA;IACL,OAAO,IAAI,CAACN,MAAM,CAACO,OAAO;EAC9B;EAEAC,UAAUA,CAACC,MAAwB;;IAC/B,IAAI,CAACP,eAAe,GAAG,CAAAQ,EAAA,IAAAC,EAAA,GAAAF,MAAM,CAACG,YAAY,CAACC,SAAS,cAAAF,EAAA,uBAAAA,EAAA,CAAEG,aAAa,cAAAJ,EAAA,cAAAA,EAAA,GAAI,KAAK;EAChF;EAEA,MAAMK,WAAWA,CAACN,MAAsC;IACpD,IAAI,IAAI,CAACP,eAAe,EAAE;MACtB,IAAIO,MAAM,CAACO,QAAQ,EAAE;QACjB;QACA;QAEA,MAAMC,SAAS,GAAG,IAAI,CAACb,eAAe,CAACc,GAAG;QAC1CT,MAAM,CAACO,QAAQ,CAAC;UACZ;UACAG,OAAO,EAAEF,SAAS,CAACG,GAAG,CAACC,IAAI,IAAI,IAAI,CAACC,aAAa,CAACD,IAAI,CAACE,gBAAgB,CAACC,UAAU,CAAC;SACtF,CAAC;MACN;MAEA,IAAIf,MAAM,CAACgB,kBAAkB,EAAE;QAC3B;QACA;QACA,MAAMC,cAAc,GAAG,IAAI,CAACtB,eAAe,CAACc,GAAG,CAACE,GAAG,CAACC,IAAI,KAAuB;UAC3E;UACAF,OAAO,EAAE,IAAI,CAACG,aAAa,CAACD,IAAI,CAACE,gBAAgB,CAACC,UAAU;SAC/D,EAAC;QAEF;QACA,MAAMG,OAAO,GAAG,MAAMlB,MAAM,CAACgB,kBAAkB,CAACC,cAAc,CAAC;QAC/DA,cAAc,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAI;UACjC,IAAI,CAACC,0BAA0B,CAACF,IAAI,CAACV,OAAQ,EAAEQ,OAAO,CAACG,GAAG,CAAC,CAAC;QAChE,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAAC9B,MAAM,CAACgC,OAAO,EAAE;EACzB;EAEA;;;;;;EAMAC,mBAAmBA,CAACC,MAAoC;IACpD,IAAI,CAACA,MAAM,CAACjC,QAAQ,EAAE;MAClB;IACJ;IACAkC,MAAM,CAACC,IAAI,CAACF,MAAM,CAACjC,QAAQ,CAAC,CAAC2B,OAAO,CAACT,OAAO,IAAG;MAC3C,MAAML,aAAa,GAAGoB,MAAM,CAACjC,QAAQ,CAACkB,OAAO,CAAC;MAC9C,IAAI,CAACY,0BAA0B,CAACZ,OAAO,EAAEL,aAAa,CAAC;MACvD,IAAI,CAACX,mCAAmC,CAACkC,IAAI,CAAC;QAAElB,OAAO;QAAEL;MAAa,CAAE,CAAC;IAC7E,CAAC,CAAC;EACN;EAEUiB,0BAA0BA,CAACZ,OAAe,EAAEL,aAAkB;IACpE,IAAI,CAACb,QAAQ,CAACkB,OAAO,CAAC,GAAGL,aAAa;EAC1C;EAEA;;;;;;EAMA,MAAMwB,gBAAgBA,CAACC,QAAgB,EAAEzB,aAAqB;IAC1D,MAAM,IAAI,CAACR,KAAK;IAEhB,MAAMkC,WAAW,GAAG,IAAI,CAAClB,aAAa,CAACiB,QAAQ,CAAC;IAChD,IAAI,IAAI,CAACtC,QAAQ,CAACuC,WAAW,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACvC,QAAQ,CAACuC,WAAW,CAAC,CAAC1B,aAAa,CAAC;IACpD;EACJ;EAEUQ,aAAaA,CAACE,UAAkB;IACtC,OAAO,GAAGA,UAAU,EAAE;EAC1B;EAEA,IAAIiB,4BAA4BA,CAAA;IAC5B,OAAO,IAAI,CAACtC,mCAAmC,CAACuC,KAAK;EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}