{"ast": null, "code": "/* IMPORT */\nimport channel from './channel.js';\n/* MAIN */\nconst alpha = color => {\n  return channel(color, 'a');\n};\n/* EXPORT */\nexport default alpha;", "map": {"version": 3, "names": ["channel", "alpha", "color"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/alpha.js"], "sourcesContent": ["/* IMPORT */\nimport channel from './channel.js';\n/* MAIN */\nconst alpha = (color) => {\n    return channel(color, 'a');\n};\n/* EXPORT */\nexport default alpha;\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,cAAc;AAClC;AACA,MAAMC,KAAK,GAAIC,KAAK,IAAK;EACrB,OAAOF,OAAO,CAACE,KAAK,EAAE,GAAG,CAAC;AAC9B,CAAC;AACD;AACA,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}