{"ast": null, "code": "export default function (a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n    ay = a[1],\n    bx = b[0],\n    by = b[1],\n    t0 = 0,\n    t1 = 1,\n    dx = bx - ax,\n    dy = by - ay,\n    r;\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}", "map": {"version": 3, "names": ["a", "b", "x0", "y0", "x1", "y1", "ax", "ay", "bx", "by", "t0", "t1", "dx", "dy", "r"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/clip/line.js"], "sourcesContent": ["export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5C,IAAIC,EAAE,GAAGN,CAAC,CAAC,CAAC,CAAC;IACTO,EAAE,GAAGP,CAAC,CAAC,CAAC,CAAC;IACTQ,EAAE,GAAGP,CAAC,CAAC,CAAC,CAAC;IACTQ,EAAE,GAAGR,CAAC,CAAC,CAAC,CAAC;IACTS,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAGJ,EAAE,GAAGF,EAAE;IACZO,EAAE,GAAGJ,EAAE,GAAGF,EAAE;IACZO,CAAC;EAELA,CAAC,GAAGZ,EAAE,GAAGI,EAAE;EACX,IAAI,CAACM,EAAE,IAAIE,CAAC,GAAG,CAAC,EAAE;EAClBA,CAAC,IAAIF,EAAE;EACP,IAAIA,EAAE,GAAG,CAAC,EAAE;IACV,IAAIE,CAAC,GAAGJ,EAAE,EAAE;IACZ,IAAII,CAAC,GAAGH,EAAE,EAAEA,EAAE,GAAGG,CAAC;EACpB,CAAC,MAAM,IAAIF,EAAE,GAAG,CAAC,EAAE;IACjB,IAAIE,CAAC,GAAGH,EAAE,EAAE;IACZ,IAAIG,CAAC,GAAGJ,EAAE,EAAEA,EAAE,GAAGI,CAAC;EACpB;EAEAA,CAAC,GAAGV,EAAE,GAAGE,EAAE;EACX,IAAI,CAACM,EAAE,IAAIE,CAAC,GAAG,CAAC,EAAE;EAClBA,CAAC,IAAIF,EAAE;EACP,IAAIA,EAAE,GAAG,CAAC,EAAE;IACV,IAAIE,CAAC,GAAGH,EAAE,EAAE;IACZ,IAAIG,CAAC,GAAGJ,EAAE,EAAEA,EAAE,GAAGI,CAAC;EACpB,CAAC,MAAM,IAAIF,EAAE,GAAG,CAAC,EAAE;IACjB,IAAIE,CAAC,GAAGJ,EAAE,EAAE;IACZ,IAAII,CAAC,GAAGH,EAAE,EAAEA,EAAE,GAAGG,CAAC;EACpB;EAEAA,CAAC,GAAGX,EAAE,GAAGI,EAAE;EACX,IAAI,CAACM,EAAE,IAAIC,CAAC,GAAG,CAAC,EAAE;EAClBA,CAAC,IAAID,EAAE;EACP,IAAIA,EAAE,GAAG,CAAC,EAAE;IACV,IAAIC,CAAC,GAAGJ,EAAE,EAAE;IACZ,IAAII,CAAC,GAAGH,EAAE,EAAEA,EAAE,GAAGG,CAAC;EACpB,CAAC,MAAM,IAAID,EAAE,GAAG,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAGH,EAAE,EAAE;IACZ,IAAIG,CAAC,GAAGJ,EAAE,EAAEA,EAAE,GAAGI,CAAC;EACpB;EAEAA,CAAC,GAAGT,EAAE,GAAGE,EAAE;EACX,IAAI,CAACM,EAAE,IAAIC,CAAC,GAAG,CAAC,EAAE;EAClBA,CAAC,IAAID,EAAE;EACP,IAAIA,EAAE,GAAG,CAAC,EAAE;IACV,IAAIC,CAAC,GAAGH,EAAE,EAAE;IACZ,IAAIG,CAAC,GAAGJ,EAAE,EAAEA,EAAE,GAAGI,CAAC;EACpB,CAAC,MAAM,IAAID,EAAE,GAAG,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAGJ,EAAE,EAAE;IACZ,IAAII,CAAC,GAAGH,EAAE,EAAEA,EAAE,GAAGG,CAAC;EACpB;EAEA,IAAIJ,EAAE,GAAG,CAAC,EAAEV,CAAC,CAAC,CAAC,CAAC,GAAGM,EAAE,GAAGI,EAAE,GAAGE,EAAE,EAAEZ,CAAC,CAAC,CAAC,CAAC,GAAGO,EAAE,GAAGG,EAAE,GAAGG,EAAE;EACpD,IAAIF,EAAE,GAAG,CAAC,EAAEV,CAAC,CAAC,CAAC,CAAC,GAAGK,EAAE,GAAGK,EAAE,GAAGC,EAAE,EAAEX,CAAC,CAAC,CAAC,CAAC,GAAGM,EAAE,GAAGI,EAAE,GAAGE,EAAE;EACpD,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}