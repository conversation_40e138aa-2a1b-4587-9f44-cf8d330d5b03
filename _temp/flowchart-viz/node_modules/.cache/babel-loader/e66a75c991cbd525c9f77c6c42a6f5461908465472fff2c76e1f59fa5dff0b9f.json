{"ast": null, "code": "import { acos, sin } from \"../math.js\";\nimport { azimuthalRaw, azimuthalInvert } from \"./azimuthal.js\";\nimport projection from \"./index.js\";\nexport var azimuthalEquidistantRaw = azimuthalRaw(function (c) {\n  return (c = acos(c)) && c / sin(c);\n});\nazimuthalEquidistantRaw.invert = azimuthalInvert(function (z) {\n  return z;\n});\nexport default function () {\n  return projection(azimuthalEquidistantRaw).scale(79.4188).clipAngle(180 - 1e-3);\n}", "map": {"version": 3, "names": ["acos", "sin", "azimuthalRaw", "azimuthalInvert", "projection", "azimuthalEquidistantRaw", "c", "invert", "z", "scale", "clipAngle"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/azimuthalEquidistant.js"], "sourcesContent": ["import {acos, sin} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEquidistantRaw = azimuthalRaw(function(c) {\n  return (c = acos(c)) && c / sin(c);\n});\n\nazimuthalEquidistantRaw.invert = azimuthalInvert(function(z) {\n  return z;\n});\n\nexport default function() {\n  return projection(azimuthalEquidistantRaw)\n      .scale(79.4188)\n      .clipAngle(180 - 1e-3);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,QAAO,YAAY;AACpC,SAAQC,YAAY,EAAEC,eAAe,QAAO,gBAAgB;AAC5D,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,IAAIC,uBAAuB,GAAGH,YAAY,CAAC,UAASI,CAAC,EAAE;EAC5D,OAAO,CAACA,CAAC,GAAGN,IAAI,CAACM,CAAC,CAAC,KAAKA,CAAC,GAAGL,GAAG,CAACK,CAAC,CAAC;AACpC,CAAC,CAAC;AAEFD,uBAAuB,CAACE,MAAM,GAAGJ,eAAe,CAAC,UAASK,CAAC,EAAE;EAC3D,OAAOA,CAAC;AACV,CAAC,CAAC;AAEF,eAAe,YAAW;EACxB,OAAOJ,UAAU,CAACC,uBAAuB,CAAC,CACrCI,KAAK,CAAC,OAAO,CAAC,CACdC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}