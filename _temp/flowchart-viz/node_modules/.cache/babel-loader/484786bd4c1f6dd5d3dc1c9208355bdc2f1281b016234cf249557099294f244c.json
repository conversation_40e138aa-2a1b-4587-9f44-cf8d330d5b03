{"ast": null, "code": "export function timer(func) {\n  const start = new Date().getTime();\n  const val = func();\n  const end = new Date().getTime();\n  const total = end - start;\n  return {\n    time: total,\n    value: val\n  };\n}", "map": {"version": 3, "names": ["timer", "func", "start", "Date", "getTime", "val", "end", "total", "time", "value"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/utils/src/timer.ts"], "sourcesContent": ["export function timer<T>(func: () => T): { time: number; value: T } {\n  const start = new Date().getTime();\n  const val = func();\n  const end = new Date().getTime();\n  const total = end - start;\n  return { time: total, value: val };\n}\n"], "mappings": "AAAA,OAAM,SAAUA,KAAKA,CAAIC,IAAa;EACpC,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;EAClC,MAAMC,GAAG,GAAGJ,IAAI,EAAE;EAClB,MAAMK,GAAG,GAAG,IAAIH,IAAI,EAAE,CAACC,OAAO,EAAE;EAChC,MAAMG,KAAK,GAAGD,GAAG,GAAGJ,KAAK;EACzB,OAAO;IAAEM,IAAI,EAAED,KAAK;IAAEE,KAAK,EAAEJ;EAAG,CAAE;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}