{"ast": null, "code": "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport { epsilon, halfPi } from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport { merge } from \"d3-array\";\nexport default function (pointVisible, clipLine, interpolate, start) {\n  return function (sink) {\n    var line = clipLine(sink),\n      ringBuffer = clipBuffer(),\n      ringSink = clipLine(ringBuffer),\n      polygonStarted = false,\n      polygon,\n      segments,\n      ring;\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function () {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function () {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function () {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n      var clean = ringSink.clean(),\n        ringSegments = ringBuffer.result(),\n        i,\n        n = ringSegments.length,\n        m,\n        segment,\n        point;\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n      segments.push(ringSegments.filter(validSegment));\n    }\n    return clip;\n  };\n}\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1]) - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}", "map": {"version": 3, "names": ["clipBuffer", "clip<PERSON><PERSON><PERSON><PERSON>", "epsilon", "halfPi", "polygonContains", "merge", "pointVisible", "clipLine", "interpolate", "start", "sink", "line", "<PERSON><PERSON><PERSON><PERSON>", "ringSink", "polygonStarted", "polygon", "segments", "ring", "clip", "point", "lineStart", "lineEnd", "polygonStart", "pointRing", "ringStart", "ringEnd", "polygonEnd", "startInside", "length", "compareIntersection", "sphere", "lambda", "phi", "pointLine", "push", "clean", "ringSegments", "result", "i", "n", "m", "segment", "pop", "concat", "shift", "filter", "validSegment", "a", "b", "x"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/clip/index.js"], "sourcesContent": ["import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,aAAa;AACpC,OAAOC,UAAU,MAAM,aAAa;AACpC,SAAQC,OAAO,EAAEC,MAAM,QAAO,YAAY;AAC1C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,SAAQC,KAAK,QAAO,UAAU;AAE9B,eAAe,UAASC,YAAY,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAClE,OAAO,UAASC,IAAI,EAAE;IACpB,IAAIC,IAAI,GAAGJ,QAAQ,CAACG,IAAI,CAAC;MACrBE,UAAU,GAAGZ,UAAU,CAAC,CAAC;MACzBa,QAAQ,GAAGN,QAAQ,CAACK,UAAU,CAAC;MAC/BE,cAAc,GAAG,KAAK;MACtBC,OAAO;MACPC,QAAQ;MACRC,IAAI;IAER,IAAIC,IAAI,GAAG;MACTC,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA,SAAS;MACpBC,OAAO,EAAEA,OAAO;MAChBC,YAAY,EAAE,SAAAA,CAAA,EAAW;QACvBJ,IAAI,CAACC,KAAK,GAAGI,SAAS;QACtBL,IAAI,CAACE,SAAS,GAAGI,SAAS;QAC1BN,IAAI,CAACG,OAAO,GAAGI,OAAO;QACtBT,QAAQ,GAAG,EAAE;QACbD,OAAO,GAAG,EAAE;MACd,CAAC;MACDW,UAAU,EAAE,SAAAA,CAAA,EAAW;QACrBR,IAAI,CAACC,KAAK,GAAGA,KAAK;QAClBD,IAAI,CAACE,SAAS,GAAGA,SAAS;QAC1BF,IAAI,CAACG,OAAO,GAAGA,OAAO;QACtBL,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAAC;QAC1B,IAAIW,WAAW,GAAGvB,eAAe,CAACW,OAAO,EAAEN,KAAK,CAAC;QACjD,IAAIO,QAAQ,CAACY,MAAM,EAAE;UACnB,IAAI,CAACd,cAAc,EAAEJ,IAAI,CAACY,YAAY,CAAC,CAAC,EAAER,cAAc,GAAG,IAAI;UAC/Db,UAAU,CAACe,QAAQ,EAAEa,mBAAmB,EAAEF,WAAW,EAAEnB,WAAW,EAAEE,IAAI,CAAC;QAC3E,CAAC,MAAM,IAAIiB,WAAW,EAAE;UACtB,IAAI,CAACb,cAAc,EAAEJ,IAAI,CAACY,YAAY,CAAC,CAAC,EAAER,cAAc,GAAG,IAAI;UAC/DJ,IAAI,CAACU,SAAS,CAAC,CAAC;UAChBZ,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;UAChCA,IAAI,CAACW,OAAO,CAAC,CAAC;QAChB;QACA,IAAIP,cAAc,EAAEJ,IAAI,CAACgB,UAAU,CAAC,CAAC,EAAEZ,cAAc,GAAG,KAAK;QAC7DE,QAAQ,GAAGD,OAAO,GAAG,IAAI;MAC3B,CAAC;MACDe,MAAM,EAAE,SAAAA,CAAA,EAAW;QACjBpB,IAAI,CAACY,YAAY,CAAC,CAAC;QACnBZ,IAAI,CAACU,SAAS,CAAC,CAAC;QAChBZ,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;QAChCA,IAAI,CAACW,OAAO,CAAC,CAAC;QACdX,IAAI,CAACgB,UAAU,CAAC,CAAC;MACnB;IACF,CAAC;IAED,SAASP,KAAKA,CAACY,MAAM,EAAEC,GAAG,EAAE;MAC1B,IAAI1B,YAAY,CAACyB,MAAM,EAAEC,GAAG,CAAC,EAAEtB,IAAI,CAACS,KAAK,CAACY,MAAM,EAAEC,GAAG,CAAC;IACxD;IAEA,SAASC,SAASA,CAACF,MAAM,EAAEC,GAAG,EAAE;MAC9BrB,IAAI,CAACQ,KAAK,CAACY,MAAM,EAAEC,GAAG,CAAC;IACzB;IAEA,SAASZ,SAASA,CAAA,EAAG;MACnBF,IAAI,CAACC,KAAK,GAAGc,SAAS;MACtBtB,IAAI,CAACS,SAAS,CAAC,CAAC;IAClB;IAEA,SAASC,OAAOA,CAAA,EAAG;MACjBH,IAAI,CAACC,KAAK,GAAGA,KAAK;MAClBR,IAAI,CAACU,OAAO,CAAC,CAAC;IAChB;IAEA,SAASE,SAASA,CAACQ,MAAM,EAAEC,GAAG,EAAE;MAC9Bf,IAAI,CAACiB,IAAI,CAAC,CAACH,MAAM,EAAEC,GAAG,CAAC,CAAC;MACxBnB,QAAQ,CAACM,KAAK,CAACY,MAAM,EAAEC,GAAG,CAAC;IAC7B;IAEA,SAASR,SAASA,CAAA,EAAG;MACnBX,QAAQ,CAACO,SAAS,CAAC,CAAC;MACpBH,IAAI,GAAG,EAAE;IACX;IAEA,SAASQ,OAAOA,CAAA,EAAG;MACjBF,SAAS,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCJ,QAAQ,CAACQ,OAAO,CAAC,CAAC;MAElB,IAAIc,KAAK,GAAGtB,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACxBC,YAAY,GAAGxB,UAAU,CAACyB,MAAM,CAAC,CAAC;QAClCC,CAAC;QAAEC,CAAC,GAAGH,YAAY,CAACR,MAAM;QAAEY,CAAC;QAC7BC,OAAO;QACPtB,KAAK;MAETF,IAAI,CAACyB,GAAG,CAAC,CAAC;MACV3B,OAAO,CAACmB,IAAI,CAACjB,IAAI,CAAC;MAClBA,IAAI,GAAG,IAAI;MAEX,IAAI,CAACsB,CAAC,EAAE;;MAER;MACA,IAAIJ,KAAK,GAAG,CAAC,EAAE;QACbM,OAAO,GAAGL,YAAY,CAAC,CAAC,CAAC;QACzB,IAAI,CAACI,CAAC,GAAGC,OAAO,CAACb,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;UAChC,IAAI,CAACd,cAAc,EAAEJ,IAAI,CAACY,YAAY,CAAC,CAAC,EAAER,cAAc,GAAG,IAAI;UAC/DJ,IAAI,CAACU,SAAS,CAAC,CAAC;UAChB,KAAKkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,EAAE,EAAEF,CAAC,EAAE5B,IAAI,CAACS,KAAK,CAAC,CAACA,KAAK,GAAGsB,OAAO,CAACH,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEnB,KAAK,CAAC,CAAC,CAAC,CAAC;UACrET,IAAI,CAACW,OAAO,CAAC,CAAC;QAChB;QACA;MACF;;MAEA;MACA;MACA,IAAIkB,CAAC,GAAG,CAAC,IAAIJ,KAAK,GAAG,CAAC,EAAEC,YAAY,CAACF,IAAI,CAACE,YAAY,CAACM,GAAG,CAAC,CAAC,CAACC,MAAM,CAACP,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;MAE1F5B,QAAQ,CAACkB,IAAI,CAACE,YAAY,CAACS,MAAM,CAACC,YAAY,CAAC,CAAC;IAClD;IAEA,OAAO5B,IAAI;EACb,CAAC;AACH;AAEA,SAAS4B,YAAYA,CAACL,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACb,MAAM,GAAG,CAAC;AAC3B;;AAEA;AACA;AACA,SAASC,mBAAmBA,CAACkB,CAAC,EAAEC,CAAC,EAAE;EACjC,OAAO,CAAC,CAACD,CAAC,GAAGA,CAAC,CAACE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAG5C,MAAM,GAAGD,OAAO,GAAGC,MAAM,GAAG4C,CAAC,CAAC,CAAC,CAAC,KAC1D,CAACC,CAAC,GAAGA,CAAC,CAACC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAG7C,MAAM,GAAGD,OAAO,GAAGC,MAAM,GAAG6C,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}