{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { Reduction, stream } from './stream.js';\n/**\n * A multimap is a variation of a Map that has potentially multiple values for every key.\n */\nexport class MultiMap {\n  constructor(elements) {\n    this.map = new Map();\n    if (elements) {\n      for (const [key, value] of elements) {\n        this.add(key, value);\n      }\n    }\n  }\n  /**\n   * The total number of values in the multimap.\n   */\n  get size() {\n    return Reduction.sum(stream(this.map.values()).map(a => a.length));\n  }\n  /**\n   * Clear all entries in the multimap.\n   */\n  clear() {\n    this.map.clear();\n  }\n  /**\n   * Operates differently depending on whether a `value` is given:\n   *  * With a value, this method deletes the specific key / value pair from the multimap.\n   *  * Without a value, all values associated with the given key are deleted.\n   *\n   * @returns `true` if a value existed and has been removed, or `false` if the specified\n   *     key / value does not exist.\n   */\n  delete(key, value) {\n    if (value === undefined) {\n      return this.map.delete(key);\n    } else {\n      const values = this.map.get(key);\n      if (values) {\n        const index = values.indexOf(value);\n        if (index >= 0) {\n          if (values.length === 1) {\n            this.map.delete(key);\n          } else {\n            values.splice(index, 1);\n          }\n          return true;\n        }\n      }\n      return false;\n    }\n  }\n  /**\n   * Returns an array of all values associated with the given key. If no value exists,\n   * an empty array is returned.\n   *\n   * _Note:_ The returned array is assumed not to be modified. Use the `set` method to add a\n   * value and `delete` to remove a value from the multimap.\n   */\n  get(key) {\n    var _a;\n    return (_a = this.map.get(key)) !== null && _a !== void 0 ? _a : [];\n  }\n  /**\n   * Operates differently depending on whether a `value` is given:\n   *  * With a value, this method returns `true` if the specific key / value pair is present in the multimap.\n   *  * Without a value, this method returns `true` if the given key is present in the multimap.\n   */\n  has(key, value) {\n    if (value === undefined) {\n      return this.map.has(key);\n    } else {\n      const values = this.map.get(key);\n      if (values) {\n        return values.indexOf(value) >= 0;\n      }\n      return false;\n    }\n  }\n  /**\n   * Add the given key / value pair to the multimap.\n   */\n  add(key, value) {\n    if (this.map.has(key)) {\n      this.map.get(key).push(value);\n    } else {\n      this.map.set(key, [value]);\n    }\n    return this;\n  }\n  /**\n   * Add the given set of key / value pairs to the multimap.\n   */\n  addAll(key, values) {\n    if (this.map.has(key)) {\n      this.map.get(key).push(...values);\n    } else {\n      this.map.set(key, Array.from(values));\n    }\n    return this;\n  }\n  /**\n   * Invokes the given callback function for every key / value pair in the multimap.\n   */\n  forEach(callbackfn) {\n    this.map.forEach((array, key) => array.forEach(value => callbackfn(value, key, this)));\n  }\n  /**\n   * Returns an iterator of key, value pairs for every entry in the map.\n   */\n  [Symbol.iterator]() {\n    return this.entries().iterator();\n  }\n  /**\n   * Returns a stream of key, value pairs for every entry in the map.\n   */\n  entries() {\n    return stream(this.map.entries()).flatMap(([key, array]) => array.map(value => [key, value]));\n  }\n  /**\n   * Returns a stream of keys in the map.\n   */\n  keys() {\n    return stream(this.map.keys());\n  }\n  /**\n   * Returns a stream of values in the map.\n   */\n  values() {\n    return stream(this.map.values()).flat();\n  }\n  /**\n   * Returns a stream of key, value set pairs for every key in the map.\n   */\n  entriesGroupedByKey() {\n    return stream(this.map.entries());\n  }\n}\nexport class BiMap {\n  get size() {\n    return this.map.size;\n  }\n  constructor(elements) {\n    this.map = new Map();\n    this.inverse = new Map();\n    if (elements) {\n      for (const [key, value] of elements) {\n        this.set(key, value);\n      }\n    }\n  }\n  clear() {\n    this.map.clear();\n    this.inverse.clear();\n  }\n  set(key, value) {\n    this.map.set(key, value);\n    this.inverse.set(value, key);\n    return this;\n  }\n  get(key) {\n    return this.map.get(key);\n  }\n  getKey(value) {\n    return this.inverse.get(value);\n  }\n  delete(key) {\n    const value = this.map.get(key);\n    if (value !== undefined) {\n      this.map.delete(key);\n      this.inverse.delete(value);\n      return true;\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["Reduction", "stream", "MultiMap", "constructor", "elements", "map", "Map", "key", "value", "add", "size", "sum", "values", "a", "length", "clear", "delete", "undefined", "get", "index", "indexOf", "splice", "_a", "has", "push", "set", "addAll", "Array", "from", "for<PERSON>ach", "callbackfn", "array", "Symbol", "iterator", "entries", "flatMap", "keys", "flat", "entriesGroupedByKey", "BiMap", "inverse", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/collections.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { Stream } from './stream.js';\r\nimport { Reduction, stream } from './stream.js';\r\n\r\n/**\r\n * A multimap is a variation of a Map that has potentially multiple values for every key.\r\n */\r\nexport class MultiMap<K, V> {\r\n\r\n    private map = new Map<K, V[]>();\r\n\r\n    constructor()\r\n    constructor(elements: Array<[K, V]>)\r\n    constructor(elements?: Array<[K, V]>) {\r\n        if (elements) {\r\n            for (const [key, value] of elements) {\r\n                this.add(key, value);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The total number of values in the multimap.\r\n     */\r\n    get size(): number {\r\n        return Reduction.sum(stream(this.map.values()).map(a => a.length));\r\n    }\r\n\r\n    /**\r\n     * Clear all entries in the multimap.\r\n     */\r\n    clear(): void {\r\n        this.map.clear();\r\n    }\r\n\r\n    /**\r\n     * Operates differently depending on whether a `value` is given:\r\n     *  * With a value, this method deletes the specific key / value pair from the multimap.\r\n     *  * Without a value, all values associated with the given key are deleted.\r\n     *\r\n     * @returns `true` if a value existed and has been removed, or `false` if the specified\r\n     *     key / value does not exist.\r\n     */\r\n    delete(key: K, value?: V): boolean {\r\n        if (value === undefined) {\r\n            return this.map.delete(key);\r\n        } else {\r\n            const values = this.map.get(key);\r\n            if (values) {\r\n                const index = values.indexOf(value);\r\n                if (index >= 0) {\r\n                    if (values.length === 1) {\r\n                        this.map.delete(key);\r\n                    } else {\r\n                        values.splice(index, 1);\r\n                    }\r\n                    return true;\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns an array of all values associated with the given key. If no value exists,\r\n     * an empty array is returned.\r\n     *\r\n     * _Note:_ The returned array is assumed not to be modified. Use the `set` method to add a\r\n     * value and `delete` to remove a value from the multimap.\r\n     */\r\n    get(key: K): readonly V[] {\r\n        return this.map.get(key) ?? [];\r\n    }\r\n\r\n    /**\r\n     * Operates differently depending on whether a `value` is given:\r\n     *  * With a value, this method returns `true` if the specific key / value pair is present in the multimap.\r\n     *  * Without a value, this method returns `true` if the given key is present in the multimap.\r\n     */\r\n    has(key: K, value?: V): boolean {\r\n        if (value === undefined) {\r\n            return this.map.has(key);\r\n        } else {\r\n            const values = this.map.get(key);\r\n            if (values) {\r\n                return values.indexOf(value) >= 0;\r\n            }\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add the given key / value pair to the multimap.\r\n     */\r\n    add(key: K, value: V): this {\r\n        if (this.map.has(key)) {\r\n            this.map.get(key)!.push(value);\r\n        } else {\r\n            this.map.set(key, [value]);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add the given set of key / value pairs to the multimap.\r\n     */\r\n    addAll(key: K, values: Iterable<V>): this {\r\n        if (this.map.has(key)) {\r\n            this.map.get(key)!.push(...values);\r\n        } else {\r\n            this.map.set(key, Array.from(values));\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Invokes the given callback function for every key / value pair in the multimap.\r\n     */\r\n    forEach(callbackfn: (value: V, key: K, map: this) => void): void {\r\n        this.map.forEach((array, key) =>\r\n            array.forEach(value => callbackfn(value, key, this))\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Returns an iterator of key, value pairs for every entry in the map.\r\n     */\r\n    [Symbol.iterator](): Iterator<[K, V]> {\r\n        return this.entries().iterator();\r\n    }\r\n\r\n    /**\r\n     * Returns a stream of key, value pairs for every entry in the map.\r\n     */\r\n    entries(): Stream<[K, V]> {\r\n        return stream(this.map.entries())\r\n            .flatMap(([key, array]) => array.map(value => [key, value] as [K, V]));\r\n    }\r\n\r\n    /**\r\n     * Returns a stream of keys in the map.\r\n     */\r\n    keys(): Stream<K> {\r\n        return stream(this.map.keys());\r\n    }\r\n\r\n    /**\r\n     * Returns a stream of values in the map.\r\n     */\r\n    values(): Stream<V> {\r\n        return stream(this.map.values()).flat();\r\n    }\r\n\r\n    /**\r\n     * Returns a stream of key, value set pairs for every key in the map.\r\n     */\r\n    entriesGroupedByKey(): Stream<[K, V[]]> {\r\n        return stream(this.map.entries());\r\n    }\r\n\r\n}\r\n\r\nexport class BiMap<K, V> {\r\n\r\n    private map = new Map<K, V>();\r\n    private inverse = new Map<V, K>();\r\n\r\n    get size(): number {\r\n        return this.map.size;\r\n    }\r\n\r\n    constructor()\r\n    constructor(elements: Array<[K, V]>)\r\n    constructor(elements?: Array<[K, V]>) {\r\n        if (elements) {\r\n            for (const [key, value] of elements) {\r\n                this.set(key, value);\r\n            }\r\n        }\r\n    }\r\n\r\n    clear(): void {\r\n        this.map.clear();\r\n        this.inverse.clear();\r\n    }\r\n\r\n    set(key: K, value: V): this {\r\n        this.map.set(key, value);\r\n        this.inverse.set(value, key);\r\n        return this;\r\n    }\r\n\r\n    get(key: K): V | undefined {\r\n        return this.map.get(key);\r\n    }\r\n\r\n    getKey(value: V): K | undefined {\r\n        return this.inverse.get(value);\r\n    }\r\n\r\n    delete(key: K): boolean {\r\n        const value = this.map.get(key);\r\n        if (value !== undefined) {\r\n            this.map.delete(key);\r\n            this.inverse.delete(value);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAOA,SAASA,SAAS,EAAEC,MAAM,QAAQ,aAAa;AAE/C;;;AAGA,OAAM,MAAOC,QAAQ;EAMjBC,YAAYC,QAAwB;IAJ5B,KAAAC,GAAG,GAAG,IAAIC,GAAG,EAAU;IAK3B,IAAIF,QAAQ,EAAE;MACV,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIJ,QAAQ,EAAE;QACjC,IAAI,CAACK,GAAG,CAACF,GAAG,EAAEC,KAAK,CAAC;MACxB;IACJ;EACJ;EAEA;;;EAGA,IAAIE,IAAIA,CAAA;IACJ,OAAOV,SAAS,CAACW,GAAG,CAACV,MAAM,CAAC,IAAI,CAACI,GAAG,CAACO,MAAM,EAAE,CAAC,CAACP,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC;EACtE;EAEA;;;EAGAC,KAAKA,CAAA;IACD,IAAI,CAACV,GAAG,CAACU,KAAK,EAAE;EACpB;EAEA;;;;;;;;EAQAC,MAAMA,CAACT,GAAM,EAAEC,KAAS;IACpB,IAAIA,KAAK,KAAKS,SAAS,EAAE;MACrB,OAAO,IAAI,CAACZ,GAAG,CAACW,MAAM,CAACT,GAAG,CAAC;IAC/B,CAAC,MAAM;MACH,MAAMK,MAAM,GAAG,IAAI,CAACP,GAAG,CAACa,GAAG,CAACX,GAAG,CAAC;MAChC,IAAIK,MAAM,EAAE;QACR,MAAMO,KAAK,GAAGP,MAAM,CAACQ,OAAO,CAACZ,KAAK,CAAC;QACnC,IAAIW,KAAK,IAAI,CAAC,EAAE;UACZ,IAAIP,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;YACrB,IAAI,CAACT,GAAG,CAACW,MAAM,CAACT,GAAG,CAAC;UACxB,CAAC,MAAM;YACHK,MAAM,CAACS,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC3B;UACA,OAAO,IAAI;QACf;MACJ;MACA,OAAO,KAAK;IAChB;EACJ;EAEA;;;;;;;EAOAD,GAAGA,CAACX,GAAM;;IACN,OAAO,CAAAe,EAAA,OAAI,CAACjB,GAAG,CAACa,GAAG,CAACX,GAAG,CAAC,cAAAe,EAAA,cAAAA,EAAA,GAAI,EAAE;EAClC;EAEA;;;;;EAKAC,GAAGA,CAAChB,GAAM,EAAEC,KAAS;IACjB,IAAIA,KAAK,KAAKS,SAAS,EAAE;MACrB,OAAO,IAAI,CAACZ,GAAG,CAACkB,GAAG,CAAChB,GAAG,CAAC;IAC5B,CAAC,MAAM;MACH,MAAMK,MAAM,GAAG,IAAI,CAACP,GAAG,CAACa,GAAG,CAACX,GAAG,CAAC;MAChC,IAAIK,MAAM,EAAE;QACR,OAAOA,MAAM,CAACQ,OAAO,CAACZ,KAAK,CAAC,IAAI,CAAC;MACrC;MACA,OAAO,KAAK;IAChB;EACJ;EAEA;;;EAGAC,GAAGA,CAACF,GAAM,EAAEC,KAAQ;IAChB,IAAI,IAAI,CAACH,GAAG,CAACkB,GAAG,CAAChB,GAAG,CAAC,EAAE;MACnB,IAAI,CAACF,GAAG,CAACa,GAAG,CAACX,GAAG,CAAE,CAACiB,IAAI,CAAChB,KAAK,CAAC;IAClC,CAAC,MAAM;MACH,IAAI,CAACH,GAAG,CAACoB,GAAG,CAAClB,GAAG,EAAE,CAACC,KAAK,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI;EACf;EAEA;;;EAGAkB,MAAMA,CAACnB,GAAM,EAAEK,MAAmB;IAC9B,IAAI,IAAI,CAACP,GAAG,CAACkB,GAAG,CAAChB,GAAG,CAAC,EAAE;MACnB,IAAI,CAACF,GAAG,CAACa,GAAG,CAACX,GAAG,CAAE,CAACiB,IAAI,CAAC,GAAGZ,MAAM,CAAC;IACtC,CAAC,MAAM;MACH,IAAI,CAACP,GAAG,CAACoB,GAAG,CAAClB,GAAG,EAAEoB,KAAK,CAACC,IAAI,CAAChB,MAAM,CAAC,CAAC;IACzC;IACA,OAAO,IAAI;EACf;EAEA;;;EAGAiB,OAAOA,CAACC,UAAiD;IACrD,IAAI,CAACzB,GAAG,CAACwB,OAAO,CAAC,CAACE,KAAK,EAAExB,GAAG,KACxBwB,KAAK,CAACF,OAAO,CAACrB,KAAK,IAAIsB,UAAU,CAACtB,KAAK,EAAED,GAAG,EAAE,IAAI,CAAC,CAAC,CACvD;EACL;EAEA;;;EAGA,CAACyB,MAAM,CAACC,QAAQ,IAAC;IACb,OAAO,IAAI,CAACC,OAAO,EAAE,CAACD,QAAQ,EAAE;EACpC;EAEA;;;EAGAC,OAAOA,CAAA;IACH,OAAOjC,MAAM,CAAC,IAAI,CAACI,GAAG,CAAC6B,OAAO,EAAE,CAAC,CAC5BC,OAAO,CAAC,CAAC,CAAC5B,GAAG,EAAEwB,KAAK,CAAC,KAAKA,KAAK,CAAC1B,GAAG,CAACG,KAAK,IAAI,CAACD,GAAG,EAAEC,KAAK,CAAW,CAAC,CAAC;EAC9E;EAEA;;;EAGA4B,IAAIA,CAAA;IACA,OAAOnC,MAAM,CAAC,IAAI,CAACI,GAAG,CAAC+B,IAAI,EAAE,CAAC;EAClC;EAEA;;;EAGAxB,MAAMA,CAAA;IACF,OAAOX,MAAM,CAAC,IAAI,CAACI,GAAG,CAACO,MAAM,EAAE,CAAC,CAACyB,IAAI,EAAE;EAC3C;EAEA;;;EAGAC,mBAAmBA,CAAA;IACf,OAAOrC,MAAM,CAAC,IAAI,CAACI,GAAG,CAAC6B,OAAO,EAAE,CAAC;EACrC;;AAIJ,OAAM,MAAOK,KAAK;EAKd,IAAI7B,IAAIA,CAAA;IACJ,OAAO,IAAI,CAACL,GAAG,CAACK,IAAI;EACxB;EAIAP,YAAYC,QAAwB;IAT5B,KAAAC,GAAG,GAAG,IAAIC,GAAG,EAAQ;IACrB,KAAAkC,OAAO,GAAG,IAAIlC,GAAG,EAAQ;IAS7B,IAAIF,QAAQ,EAAE;MACV,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIJ,QAAQ,EAAE;QACjC,IAAI,CAACqB,GAAG,CAAClB,GAAG,EAAEC,KAAK,CAAC;MACxB;IACJ;EACJ;EAEAO,KAAKA,CAAA;IACD,IAAI,CAACV,GAAG,CAACU,KAAK,EAAE;IAChB,IAAI,CAACyB,OAAO,CAACzB,KAAK,EAAE;EACxB;EAEAU,GAAGA,CAAClB,GAAM,EAAEC,KAAQ;IAChB,IAAI,CAACH,GAAG,CAACoB,GAAG,CAAClB,GAAG,EAAEC,KAAK,CAAC;IACxB,IAAI,CAACgC,OAAO,CAACf,GAAG,CAACjB,KAAK,EAAED,GAAG,CAAC;IAC5B,OAAO,IAAI;EACf;EAEAW,GAAGA,CAACX,GAAM;IACN,OAAO,IAAI,CAACF,GAAG,CAACa,GAAG,CAACX,GAAG,CAAC;EAC5B;EAEAkC,MAAMA,CAACjC,KAAQ;IACX,OAAO,IAAI,CAACgC,OAAO,CAACtB,GAAG,CAACV,KAAK,CAAC;EAClC;EAEAQ,MAAMA,CAACT,GAAM;IACT,MAAMC,KAAK,GAAG,IAAI,CAACH,GAAG,CAACa,GAAG,CAACX,GAAG,CAAC;IAC/B,IAAIC,KAAK,KAAKS,SAAS,EAAE;MACrB,IAAI,CAACZ,GAAG,CAACW,MAAM,CAACT,GAAG,CAAC;MACpB,IAAI,CAACiC,OAAO,CAACxB,MAAM,CAACR,KAAK,CAAC;MAC1B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}