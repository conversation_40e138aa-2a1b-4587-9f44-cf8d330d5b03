{"ast": null, "code": "/******************************************************************************\n * Copyright 2021-2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { streamAllContents, streamContents } from '../utils/ast-utils.js';\nimport { MultiMap } from '../utils/collections.js';\nimport { interruptAndCheck } from '../utils/promise-utils.js';\n/**\n * The default scope computation creates and collectes descriptions of the AST nodes to be exported into the\n * _global_ scope from the given document. By default those are the document's root AST node and its directly\n * contained child nodes.\n *\n * Besides, it gathers all AST nodes that have a name (according to the `NameProvider` service) and includes them\n * in the local scope of their particular container nodes. As a result, for every cross-reference in the AST,\n * target elements from the same level (siblings) and further up towards the root (parents and siblings of parents)\n * are visible. Elements being nested inside lower levels (children, children of siblings and parents' siblings)\n * are _invisible_ by default, but that can be changed by customizing this service.\n */\nexport class DefaultScopeComputation {\n  constructor(services) {\n    this.nameProvider = services.references.NameProvider;\n    this.descriptions = services.workspace.AstNodeDescriptionProvider;\n  }\n  async computeExports(document, cancelToken = CancellationToken.None) {\n    return this.computeExportsForNode(document.parseResult.value, document, undefined, cancelToken);\n  }\n  /**\n   * Creates {@link AstNodeDescription AstNodeDescriptions} for the given {@link AstNode parentNode} and its children.\n   * The list of children to be considered is determined by the function parameter {@link children}.\n   * By default only the direct children of {@link parentNode} are visited, nested nodes are not exported.\n   *\n   * @param parentNode AST node to be exported, i.e., of which an {@link AstNodeDescription} shall be added to the returned list.\n   * @param document The document containing the AST node to be exported.\n   * @param children A function called with {@link parentNode} as single argument and returning an {@link Iterable} supplying the children to be visited, which must be directly or transitively contained in {@link parentNode}.\n   * @param cancelToken Indicates when to cancel the current operation.\n   * @throws `OperationCancelled` if a user action occurs during execution.\n   * @returns A list of {@link AstNodeDescription AstNodeDescriptions} to be published to index.\n   */\n  async computeExportsForNode(parentNode, document, children = streamContents, cancelToken = CancellationToken.None) {\n    const exports = [];\n    this.exportNode(parentNode, exports, document);\n    for (const node of children(parentNode)) {\n      await interruptAndCheck(cancelToken);\n      this.exportNode(node, exports, document);\n    }\n    return exports;\n  }\n  /**\n   * Add a single node to the list of exports if it has a name. Override this method to change how\n   * symbols are exported, e.g. by modifying their exported name.\n   */\n  exportNode(node, exports, document) {\n    const name = this.nameProvider.getName(node);\n    if (name) {\n      exports.push(this.descriptions.createDescription(node, name, document));\n    }\n  }\n  async computeLocalScopes(document, cancelToken = CancellationToken.None) {\n    const rootNode = document.parseResult.value;\n    const scopes = new MultiMap();\n    // Here we navigate the full AST - local scopes shall be available in the whole document\n    for (const node of streamAllContents(rootNode)) {\n      await interruptAndCheck(cancelToken);\n      this.processNode(node, document, scopes);\n    }\n    return scopes;\n  }\n  /**\n   * Process a single node during scopes computation. The default implementation makes the node visible\n   * in the subtree of its container (if the node has a name). Override this method to change this,\n   * e.g. by increasing the visibility to a higher level in the AST.\n   */\n  processNode(node, document, scopes) {\n    const container = node.$container;\n    if (container) {\n      const name = this.nameProvider.getName(node);\n      if (name) {\n        scopes.add(container, this.descriptions.createDescription(node, name, document));\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "streamAllContents", "streamContents", "MultiMap", "interruptAndCheck", "DefaultScopeComputation", "constructor", "services", "nameProvider", "references", "Name<PERSON>rovider", "descriptions", "workspace", "AstNodeDescriptionProvider", "computeExports", "document", "cancelToken", "None", "computeExportsForNode", "parseResult", "value", "undefined", "parentNode", "children", "exports", "exportNode", "node", "name", "getName", "push", "createDescription", "computeLocalScopes", "rootNode", "scopes", "processNode", "container", "$container", "add"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/references/scope-computation.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021-2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, AstNodeDescription } from '../syntax-tree.js';\r\nimport type { AstNodeDescriptionProvider } from '../workspace/ast-descriptions.js';\r\nimport type { LangiumDocument, PrecomputedScopes } from '../workspace/documents.js';\r\nimport type { NameProvider } from './name-provider.js';\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { streamAllContents, streamContents } from '../utils/ast-utils.js';\r\nimport { MultiMap } from '../utils/collections.js';\r\nimport { interruptAndCheck } from '../utils/promise-utils.js';\r\n\r\n/**\r\n * Language-specific service for precomputing global and local scopes. The service methods are executed\r\n * as the first and second phase in the `DocumentBuilder`.\r\n */\r\nexport interface ScopeComputation {\r\n\r\n    /**\r\n     * Creates descriptions of all AST nodes that shall be exported into the _global_ scope from the given\r\n     * document. These descriptions are gathered by the `IndexManager` and stored in the global index so\r\n     * they can be referenced from other documents.\r\n     *\r\n     * _Note:_ You should not resolve any cross-references in this service method. Cross-reference resolution\r\n     * depends on the scope computation phase to be completed (`computeScope` method), which runs after the\r\n     * initial indexing where this method is used.\r\n     *\r\n     * @param document The document from which to gather exported AST nodes.\r\n     * @param cancelToken Indicates when to cancel the current operation.\r\n     * @throws `OperationCanceled` if a user action occurs during execution\r\n     */\r\n    computeExports(document: LangiumDocument, cancelToken?: CancellationToken): Promise<AstNodeDescription[]>;\r\n\r\n    /**\r\n     * Precomputes the _local_ scopes for a document, which are necessary for the default way of\r\n     * resolving references to symbols in the same document. The result is a multimap assigning a\r\n     * set of AST node descriptions to every level of the AST. These data are used by the `ScopeProvider`\r\n     * service to determine which target nodes are visible in the context of a specific cross-reference.\r\n     *\r\n     * _Note:_ You should not resolve any cross-references in this service method. Cross-reference\r\n     * resolution depends on the scope computation phase to be completed.\r\n     *\r\n     * @param document The document in which to compute scopes.\r\n     * @param cancelToken Indicates when to cancel the current operation.\r\n     * @throws `OperationCanceled` if a user action occurs during execution\r\n     */\r\n    computeLocalScopes(document: LangiumDocument, cancelToken?: CancellationToken): Promise<PrecomputedScopes>;\r\n\r\n}\r\n\r\n/**\r\n * The default scope computation creates and collectes descriptions of the AST nodes to be exported into the\r\n * _global_ scope from the given document. By default those are the document's root AST node and its directly\r\n * contained child nodes.\r\n *\r\n * Besides, it gathers all AST nodes that have a name (according to the `NameProvider` service) and includes them\r\n * in the local scope of their particular container nodes. As a result, for every cross-reference in the AST,\r\n * target elements from the same level (siblings) and further up towards the root (parents and siblings of parents)\r\n * are visible. Elements being nested inside lower levels (children, children of siblings and parents' siblings)\r\n * are _invisible_ by default, but that can be changed by customizing this service.\r\n */\r\nexport class DefaultScopeComputation implements ScopeComputation {\r\n\r\n    protected readonly nameProvider: NameProvider;\r\n    protected readonly descriptions: AstNodeDescriptionProvider;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.nameProvider = services.references.NameProvider;\r\n        this.descriptions = services.workspace.AstNodeDescriptionProvider;\r\n    }\r\n\r\n    async computeExports(document: LangiumDocument, cancelToken = CancellationToken.None): Promise<AstNodeDescription[]> {\r\n        return this.computeExportsForNode(document.parseResult.value, document, undefined, cancelToken);\r\n    }\r\n\r\n    /**\r\n     * Creates {@link AstNodeDescription AstNodeDescriptions} for the given {@link AstNode parentNode} and its children.\r\n     * The list of children to be considered is determined by the function parameter {@link children}.\r\n     * By default only the direct children of {@link parentNode} are visited, nested nodes are not exported.\r\n     *\r\n     * @param parentNode AST node to be exported, i.e., of which an {@link AstNodeDescription} shall be added to the returned list.\r\n     * @param document The document containing the AST node to be exported.\r\n     * @param children A function called with {@link parentNode} as single argument and returning an {@link Iterable} supplying the children to be visited, which must be directly or transitively contained in {@link parentNode}.\r\n     * @param cancelToken Indicates when to cancel the current operation.\r\n     * @throws `OperationCancelled` if a user action occurs during execution.\r\n     * @returns A list of {@link AstNodeDescription AstNodeDescriptions} to be published to index.\r\n     */\r\n    async computeExportsForNode(parentNode: AstNode, document: LangiumDocument<AstNode>, children: (root: AstNode) => Iterable<AstNode> = streamContents, cancelToken: CancellationToken = CancellationToken.None): Promise<AstNodeDescription[]> {\r\n        const exports: AstNodeDescription[] = [];\r\n\r\n        this.exportNode(parentNode, exports, document);\r\n        for (const node of children(parentNode)) {\r\n            await interruptAndCheck(cancelToken);\r\n            this.exportNode(node, exports, document);\r\n        }\r\n        return exports;\r\n    }\r\n\r\n    /**\r\n     * Add a single node to the list of exports if it has a name. Override this method to change how\r\n     * symbols are exported, e.g. by modifying their exported name.\r\n     */\r\n    protected exportNode(node: AstNode, exports: AstNodeDescription[], document: LangiumDocument): void {\r\n        const name = this.nameProvider.getName(node);\r\n        if (name) {\r\n            exports.push(this.descriptions.createDescription(node, name, document));\r\n        }\r\n    }\r\n\r\n    async computeLocalScopes(document: LangiumDocument, cancelToken = CancellationToken.None): Promise<PrecomputedScopes> {\r\n        const rootNode = document.parseResult.value;\r\n        const scopes = new MultiMap<AstNode, AstNodeDescription>();\r\n        // Here we navigate the full AST - local scopes shall be available in the whole document\r\n        for (const node of streamAllContents(rootNode)) {\r\n            await interruptAndCheck(cancelToken);\r\n            this.processNode(node, document, scopes);\r\n        }\r\n        return scopes;\r\n    }\r\n\r\n    /**\r\n     * Process a single node during scopes computation. The default implementation makes the node visible\r\n     * in the subtree of its container (if the node has a name). Override this method to change this,\r\n     * e.g. by increasing the visibility to a higher level in the AST.\r\n     */\r\n    protected processNode(node: AstNode, document: LangiumDocument, scopes: PrecomputedScopes): void {\r\n        const container = node.$container;\r\n        if (container) {\r\n            const name = this.nameProvider.getName(node);\r\n            if (name) {\r\n                scopes.add(container, this.descriptions.createDescription(node, name, document));\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAWA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,uBAAuB;AACzE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,iBAAiB,QAAQ,2BAA2B;AAwC7D;;;;;;;;;;;AAWA,OAAM,MAAOC,uBAAuB;EAKhCC,YAAYC,QAA6B;IACrC,IAAI,CAACC,YAAY,GAAGD,QAAQ,CAACE,UAAU,CAACC,YAAY;IACpD,IAAI,CAACC,YAAY,GAAGJ,QAAQ,CAACK,SAAS,CAACC,0BAA0B;EACrE;EAEA,MAAMC,cAAcA,CAACC,QAAyB,EAAEC,WAAW,GAAGhB,iBAAiB,CAACiB,IAAI;IAChF,OAAO,IAAI,CAACC,qBAAqB,CAACH,QAAQ,CAACI,WAAW,CAACC,KAAK,EAAEL,QAAQ,EAAEM,SAAS,EAAEL,WAAW,CAAC;EACnG;EAEA;;;;;;;;;;;;EAYA,MAAME,qBAAqBA,CAACI,UAAmB,EAAEP,QAAkC,EAAEQ,QAAA,GAAiDrB,cAAc,EAAEc,WAAA,GAAiChB,iBAAiB,CAACiB,IAAI;IACzM,MAAMO,OAAO,GAAyB,EAAE;IAExC,IAAI,CAACC,UAAU,CAACH,UAAU,EAAEE,OAAO,EAAET,QAAQ,CAAC;IAC9C,KAAK,MAAMW,IAAI,IAAIH,QAAQ,CAACD,UAAU,CAAC,EAAE;MACrC,MAAMlB,iBAAiB,CAACY,WAAW,CAAC;MACpC,IAAI,CAACS,UAAU,CAACC,IAAI,EAAEF,OAAO,EAAET,QAAQ,CAAC;IAC5C;IACA,OAAOS,OAAO;EAClB;EAEA;;;;EAIUC,UAAUA,CAACC,IAAa,EAAEF,OAA6B,EAAET,QAAyB;IACxF,MAAMY,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACoB,OAAO,CAACF,IAAI,CAAC;IAC5C,IAAIC,IAAI,EAAE;MACNH,OAAO,CAACK,IAAI,CAAC,IAAI,CAAClB,YAAY,CAACmB,iBAAiB,CAACJ,IAAI,EAAEC,IAAI,EAAEZ,QAAQ,CAAC,CAAC;IAC3E;EACJ;EAEA,MAAMgB,kBAAkBA,CAAChB,QAAyB,EAAEC,WAAW,GAAGhB,iBAAiB,CAACiB,IAAI;IACpF,MAAMe,QAAQ,GAAGjB,QAAQ,CAACI,WAAW,CAACC,KAAK;IAC3C,MAAMa,MAAM,GAAG,IAAI9B,QAAQ,EAA+B;IAC1D;IACA,KAAK,MAAMuB,IAAI,IAAIzB,iBAAiB,CAAC+B,QAAQ,CAAC,EAAE;MAC5C,MAAM5B,iBAAiB,CAACY,WAAW,CAAC;MACpC,IAAI,CAACkB,WAAW,CAACR,IAAI,EAAEX,QAAQ,EAAEkB,MAAM,CAAC;IAC5C;IACA,OAAOA,MAAM;EACjB;EAEA;;;;;EAKUC,WAAWA,CAACR,IAAa,EAAEX,QAAyB,EAAEkB,MAAyB;IACrF,MAAME,SAAS,GAAGT,IAAI,CAACU,UAAU;IACjC,IAAID,SAAS,EAAE;MACX,MAAMR,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACoB,OAAO,CAACF,IAAI,CAAC;MAC5C,IAAIC,IAAI,EAAE;QACNM,MAAM,CAACI,GAAG,CAACF,SAAS,EAAE,IAAI,CAACxB,YAAY,CAACmB,iBAAiB,CAACJ,IAAI,EAAEC,IAAI,EAAEZ,QAAQ,CAAC,CAAC;MACpF;IACJ;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}