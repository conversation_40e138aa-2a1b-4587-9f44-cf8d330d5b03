{"ast": null, "code": "export default function (a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}", "map": {"version": 3, "names": ["a", "b", "NaN"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/ascending.js"], "sourcesContent": ["export default function(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAG,CAAC,GAAGC,GAAG;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}