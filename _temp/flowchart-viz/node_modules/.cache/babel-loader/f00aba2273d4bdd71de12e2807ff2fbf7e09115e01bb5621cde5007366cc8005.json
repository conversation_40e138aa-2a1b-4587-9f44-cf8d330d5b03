{"ast": null, "code": "import ascending from \"./ascending.js\";\nexport default function (f) {\n  let delta = f;\n  let compare = f;\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = lo + hi >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n    }\n    return lo;\n  }\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = lo + hi >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;else lo = mid + 1;\n    }\n    return lo;\n  }\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}", "map": {"version": 3, "names": ["ascending", "f", "delta", "compare", "length", "d", "x", "ascendingComparator", "left", "a", "lo", "hi", "mid", "right", "center", "i"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/bisector.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\nexport default function(f) {\n  let delta = f;\n  let compare = f;\n\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;\n      else hi = mid;\n    }\n    return lo;\n  }\n\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;\n      else lo = mid + 1;\n    }\n    return lo;\n  }\n\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,UAASC,CAAC,EAAE;EACzB,IAAIC,KAAK,GAAGD,CAAC;EACb,IAAIE,OAAO,GAAGF,CAAC;EAEf,IAAIA,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IAClBF,KAAK,GAAGA,CAACG,CAAC,EAAEC,CAAC,KAAKL,CAAC,CAACI,CAAC,CAAC,GAAGC,CAAC;IAC1BH,OAAO,GAAGI,mBAAmB,CAACN,CAAC,CAAC;EAClC;EAEA,SAASO,IAAIA,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAE;IAC1B,IAAID,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAG,CAAC;IACtB,IAAIC,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGF,CAAC,CAACL,MAAM;IAC7B,OAAOM,EAAE,GAAGC,EAAE,EAAE;MACd,MAAMC,GAAG,GAAIF,EAAE,GAAGC,EAAE,KAAM,CAAC;MAC3B,IAAIR,OAAO,CAACM,CAAC,CAACG,GAAG,CAAC,EAAEN,CAAC,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAGE,GAAG,GAAG,CAAC,CAAC,KACpCD,EAAE,GAAGC,GAAG;IACf;IACA,OAAOF,EAAE;EACX;EAEA,SAASG,KAAKA,CAACJ,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAE;IAC3B,IAAID,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAG,CAAC;IACtB,IAAIC,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGF,CAAC,CAACL,MAAM;IAC7B,OAAOM,EAAE,GAAGC,EAAE,EAAE;MACd,MAAMC,GAAG,GAAIF,EAAE,GAAGC,EAAE,KAAM,CAAC;MAC3B,IAAIR,OAAO,CAACM,CAAC,CAACG,GAAG,CAAC,EAAEN,CAAC,CAAC,GAAG,CAAC,EAAEK,EAAE,GAAGC,GAAG,CAAC,KAChCF,EAAE,GAAGE,GAAG,GAAG,CAAC;IACnB;IACA,OAAOF,EAAE;EACX;EAEA,SAASI,MAAMA,CAACL,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAE;IAC5B,IAAID,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAG,CAAC;IACtB,IAAIC,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGF,CAAC,CAACL,MAAM;IAC7B,MAAMW,CAAC,GAAGP,IAAI,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;IAChC,OAAOI,CAAC,GAAGL,EAAE,IAAIR,KAAK,CAACO,CAAC,CAACM,CAAC,GAAG,CAAC,CAAC,EAAET,CAAC,CAAC,GAAG,CAACJ,KAAK,CAACO,CAAC,CAACM,CAAC,CAAC,EAAET,CAAC,CAAC,GAAGS,CAAC,GAAG,CAAC,GAAGA,CAAC;EACnE;EAEA,OAAO;IAACP,IAAI;IAAEM,MAAM;IAAED;EAAK,CAAC;AAC9B;AAEA,SAASN,mBAAmBA,CAACN,CAAC,EAAE;EAC9B,OAAO,CAACI,CAAC,EAAEC,CAAC,KAAKN,SAAS,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEC,CAAC,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}