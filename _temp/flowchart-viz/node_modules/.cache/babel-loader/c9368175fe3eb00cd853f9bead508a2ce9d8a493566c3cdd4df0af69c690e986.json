{"ast": null, "code": "/******************************************************************************\n * Copyright 2021-2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { assertUnreachable } from '../utils/errors.js';\nimport * as ast from '../languages/generated/ast.js';\nimport { isCompositeCstNode } from '../syntax-tree.js';\nimport { getContainerOfType, streamAllContents } from './ast-utils.js';\nimport { streamCst } from './cst-utils.js';\nimport { escapeRegExp, isWhitespace } from './regexp-utils.js';\n/**\n * Returns the entry rule of the given grammar, if any. If the grammar file does not contain an entry rule,\n * the result is `undefined`.\n */\nexport function getEntryRule(grammar) {\n  return grammar.rules.find(e => ast.isParserRule(e) && e.entry);\n}\n/**\n * Returns all hidden terminal rules of the given grammar, if any.\n */\nexport function getHiddenRules(grammar) {\n  return grammar.rules.filter(e => ast.isTerminalRule(e) && e.hidden);\n}\n/**\n * Returns all rules that can be reached from the topmost rules of the specified grammar (entry and hidden terminal rules).\n *\n * @param grammar The grammar that contains all rules\n * @param allTerminals Whether or not to include terminals that are referenced only by other terminals\n * @returns A list of referenced parser and terminal rules. If the grammar contains no entry rule,\n *      this function returns all rules of the specified grammar.\n */\nexport function getAllReachableRules(grammar, allTerminals) {\n  const ruleNames = new Set();\n  const entryRule = getEntryRule(grammar);\n  if (!entryRule) {\n    return new Set(grammar.rules);\n  }\n  const topMostRules = [entryRule].concat(getHiddenRules(grammar));\n  for (const rule of topMostRules) {\n    ruleDfs(rule, ruleNames, allTerminals);\n  }\n  const rules = new Set();\n  for (const rule of grammar.rules) {\n    if (ruleNames.has(rule.name) || ast.isTerminalRule(rule) && rule.hidden) {\n      rules.add(rule);\n    }\n  }\n  return rules;\n}\nfunction ruleDfs(rule, visitedSet, allTerminals) {\n  visitedSet.add(rule.name);\n  streamAllContents(rule).forEach(node => {\n    if (ast.isRuleCall(node) || allTerminals && ast.isTerminalRuleCall(node)) {\n      const refRule = node.rule.ref;\n      if (refRule && !visitedSet.has(refRule.name)) {\n        ruleDfs(refRule, visitedSet, allTerminals);\n      }\n    }\n  });\n}\n/**\n * Determines the grammar expression used to parse a cross-reference (usually a reference to a terminal rule).\n * A cross-reference can declare this expression explicitly in the form `[Type : Terminal]`, but if `Terminal`\n * is omitted, this function attempts to infer it from the name of the referenced `Type` (using `findNameAssignment`).\n *\n * Returns the grammar expression used to parse the given cross-reference, or `undefined` if it is not declared\n * and cannot be inferred.\n */\nexport function getCrossReferenceTerminal(crossRef) {\n  if (crossRef.terminal) {\n    return crossRef.terminal;\n  } else if (crossRef.type.ref) {\n    const nameAssigment = findNameAssignment(crossRef.type.ref);\n    return nameAssigment === null || nameAssigment === void 0 ? void 0 : nameAssigment.terminal;\n  }\n  return undefined;\n}\n/**\n * Determines whether the given terminal rule represents a comment. This is true if the rule is marked\n * as `hidden` and it does not match white space. This means every hidden token (i.e. excluded from the AST)\n * that contains visible characters is considered a comment.\n */\nexport function isCommentTerminal(terminalRule) {\n  return terminalRule.hidden && !isWhitespace(terminalRegex(terminalRule));\n}\n/**\n * Find all CST nodes within the given node that contribute to the specified property.\n *\n * @param node A CST node in which to look for property assignments. If this is undefined, the result is an empty array.\n * @param property A property name of the constructed AST node. If this is undefined, the result is an empty array.\n */\nexport function findNodesForProperty(node, property) {\n  if (!node || !property) {\n    return [];\n  }\n  return findNodesForPropertyInternal(node, property, node.astNode, true);\n}\n/**\n * Find a single CST node within the given node that contributes to the specified property.\n *\n * @param node A CST node in which to look for property assignments. If this is undefined, the result is `undefined`.\n * @param property A property name of the constructed AST node. If this is undefined, the result is `undefined`.\n * @param index If no index is specified or the index is less than zero, the first found node is returned. If the\n *        specified index exceeds the number of assignments to the property, the last found node is returned. Otherwise,\n *        the node with the specified index is returned.\n */\nexport function findNodeForProperty(node, property, index) {\n  if (!node || !property) {\n    return undefined;\n  }\n  const nodes = findNodesForPropertyInternal(node, property, node.astNode, true);\n  if (nodes.length === 0) {\n    return undefined;\n  }\n  if (index !== undefined) {\n    index = Math.max(0, Math.min(index, nodes.length - 1));\n  } else {\n    index = 0;\n  }\n  return nodes[index];\n}\nfunction findNodesForPropertyInternal(node, property, element, first) {\n  if (!first) {\n    const nodeFeature = getContainerOfType(node.grammarSource, ast.isAssignment);\n    if (nodeFeature && nodeFeature.feature === property) {\n      return [node];\n    }\n  }\n  if (isCompositeCstNode(node) && node.astNode === element) {\n    return node.content.flatMap(e => findNodesForPropertyInternal(e, property, element, false));\n  }\n  return [];\n}\n/**\n * Find all CST nodes within the given node that correspond to the specified keyword.\n *\n * @param node A CST node in which to look for keywords. If this is undefined, the result is an empty array.\n * @param keyword A keyword as specified in the grammar.\n */\nexport function findNodesForKeyword(node, keyword) {\n  if (!node) {\n    return [];\n  }\n  return findNodesForKeywordInternal(node, keyword, node === null || node === void 0 ? void 0 : node.astNode);\n}\n/**\n * Find a single CST node within the given node that corresponds to the specified keyword.\n *\n * @param node A CST node in which to look for keywords. If this is undefined, the result is `undefined`.\n * @param keyword A keyword as specified in the grammar.\n * @param index If no index is specified or the index is less than zero, the first found node is returned. If the\n *        specified index exceeds the number of keyword occurrences, the last found node is returned. Otherwise,\n *        the node with the specified index is returned.\n */\nexport function findNodeForKeyword(node, keyword, index) {\n  if (!node) {\n    return undefined;\n  }\n  const nodes = findNodesForKeywordInternal(node, keyword, node === null || node === void 0 ? void 0 : node.astNode);\n  if (nodes.length === 0) {\n    return undefined;\n  }\n  if (index !== undefined) {\n    index = Math.max(0, Math.min(index, nodes.length - 1));\n  } else {\n    index = 0;\n  }\n  return nodes[index];\n}\nexport function findNodesForKeywordInternal(node, keyword, element) {\n  if (node.astNode !== element) {\n    return [];\n  }\n  if (ast.isKeyword(node.grammarSource) && node.grammarSource.value === keyword) {\n    return [node];\n  }\n  const treeIterator = streamCst(node).iterator();\n  let result;\n  const keywordNodes = [];\n  do {\n    result = treeIterator.next();\n    if (!result.done) {\n      const childNode = result.value;\n      if (childNode.astNode === element) {\n        if (ast.isKeyword(childNode.grammarSource) && childNode.grammarSource.value === keyword) {\n          keywordNodes.push(childNode);\n        }\n      } else {\n        treeIterator.prune();\n      }\n    }\n  } while (!result.done);\n  return keywordNodes;\n}\n/**\n * If the given CST node was parsed in the context of a property assignment, the respective `Assignment` grammar\n * node is returned. If no assignment is found, the result is `undefined`.\n *\n * @param cstNode A CST node for which to find a property assignment.\n */\nexport function findAssignment(cstNode) {\n  var _a;\n  const astNode = cstNode.astNode;\n  // Only search until the ast node of the parent cst node is no longer the original ast node\n  // This would make us jump to a preceding rule call, which contains only unrelated assignments\n  while (astNode === ((_a = cstNode.container) === null || _a === void 0 ? void 0 : _a.astNode)) {\n    const assignment = getContainerOfType(cstNode.grammarSource, ast.isAssignment);\n    if (assignment) {\n      return assignment;\n    }\n    cstNode = cstNode.container;\n  }\n  return undefined;\n}\n/**\n * Find an assignment to the `name` property for the given grammar type. This requires the `type` to be inferred\n * from a parser rule, and that rule must contain an assignment to the `name` property. In all other cases,\n * this function returns `undefined`.\n */\nexport function findNameAssignment(type) {\n  let startNode = type;\n  if (ast.isInferredType(startNode)) {\n    // for inferred types, the location to start searching for the name-assignment is different\n    if (ast.isAction(startNode.$container)) {\n      // a type which is explicitly inferred by an action: investigate the sibbling of the Action node, i.e. start searching at the Action's parent\n      startNode = startNode.$container.$container;\n    } else if (ast.isParserRule(startNode.$container)) {\n      // investigate the parser rule with the explicitly inferred type\n      startNode = startNode.$container;\n    } else {\n      assertUnreachable(startNode.$container);\n    }\n  }\n  return findNameAssignmentInternal(type, startNode, new Map());\n}\nfunction findNameAssignmentInternal(type, startNode, cache) {\n  var _a;\n  // the cache is only required to prevent infinite loops\n  function go(node, refType) {\n    let childAssignment = undefined;\n    const parentAssignment = getContainerOfType(node, ast.isAssignment);\n    // No parent assignment implies unassigned rule call\n    if (!parentAssignment) {\n      childAssignment = findNameAssignmentInternal(refType, refType, cache);\n    }\n    cache.set(type, childAssignment);\n    return childAssignment;\n  }\n  if (cache.has(type)) {\n    return cache.get(type);\n  }\n  cache.set(type, undefined);\n  for (const node of streamAllContents(startNode)) {\n    if (ast.isAssignment(node) && node.feature.toLowerCase() === 'name') {\n      cache.set(type, node);\n      return node;\n    } else if (ast.isRuleCall(node) && ast.isParserRule(node.rule.ref)) {\n      return go(node, node.rule.ref);\n    } else if (ast.isSimpleType(node) && ((_a = node.typeRef) === null || _a === void 0 ? void 0 : _a.ref)) {\n      return go(node, node.typeRef.ref);\n    }\n  }\n  return undefined;\n}\nexport function getActionAtElement(element) {\n  const parent = element.$container;\n  if (ast.isGroup(parent)) {\n    const elements = parent.elements;\n    const index = elements.indexOf(element);\n    for (let i = index - 1; i >= 0; i--) {\n      const item = elements[i];\n      if (ast.isAction(item)) {\n        return item;\n      } else {\n        const action = streamAllContents(elements[i]).find(ast.isAction);\n        if (action) {\n          return action;\n        }\n      }\n    }\n  }\n  if (ast.isAbstractElement(parent)) {\n    return getActionAtElement(parent);\n  } else {\n    return undefined;\n  }\n}\nexport function isOptionalCardinality(cardinality, element) {\n  return cardinality === '?' || cardinality === '*' || ast.isGroup(element) && Boolean(element.guardCondition);\n}\nexport function isArrayCardinality(cardinality) {\n  return cardinality === '*' || cardinality === '+';\n}\nexport function isArrayOperator(operator) {\n  return operator === '+=';\n}\n/**\n * Determines whether the given parser rule is a _data type rule_, meaning that it has a\n * primitive return type like `number`, `boolean`, etc.\n */\nexport function isDataTypeRule(rule) {\n  return isDataTypeRuleInternal(rule, new Set());\n}\nfunction isDataTypeRuleInternal(rule, visited) {\n  if (visited.has(rule)) {\n    return true;\n  } else {\n    visited.add(rule);\n  }\n  for (const node of streamAllContents(rule)) {\n    if (ast.isRuleCall(node)) {\n      if (!node.rule.ref) {\n        // RuleCall to unresolved rule. Don't assume `rule` is a DataType rule.\n        return false;\n      }\n      if (ast.isParserRule(node.rule.ref) && !isDataTypeRuleInternal(node.rule.ref, visited)) {\n        return false;\n      }\n    } else if (ast.isAssignment(node)) {\n      return false;\n    } else if (ast.isAction(node)) {\n      return false;\n    }\n  }\n  return Boolean(rule.definition);\n}\nexport function isDataType(type) {\n  return isDataTypeInternal(type.type, new Set());\n}\nfunction isDataTypeInternal(type, visited) {\n  if (visited.has(type)) {\n    return true;\n  } else {\n    visited.add(type);\n  }\n  if (ast.isArrayType(type)) {\n    return false;\n  } else if (ast.isReferenceType(type)) {\n    return false;\n  } else if (ast.isUnionType(type)) {\n    return type.types.every(e => isDataTypeInternal(e, visited));\n  } else if (ast.isSimpleType(type)) {\n    if (type.primitiveType !== undefined) {\n      return true;\n    } else if (type.stringType !== undefined) {\n      return true;\n    } else if (type.typeRef !== undefined) {\n      const ref = type.typeRef.ref;\n      if (ast.isType(ref)) {\n        return isDataTypeInternal(ref.type, visited);\n      } else {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  } else {\n    return false;\n  }\n}\nexport function getExplicitRuleType(rule) {\n  if (rule.inferredType) {\n    return rule.inferredType.name;\n  } else if (rule.dataType) {\n    return rule.dataType;\n  } else if (rule.returnType) {\n    const refType = rule.returnType.ref;\n    if (refType) {\n      // check if we need to check Action as return type\n      if (ast.isParserRule(refType)) {\n        return refType.name;\n      } else if (ast.isInterface(refType) || ast.isType(refType)) {\n        return refType.name;\n      }\n    }\n  }\n  return undefined;\n}\nexport function getTypeName(type) {\n  var _a;\n  if (ast.isParserRule(type)) {\n    return isDataTypeRule(type) ? type.name : (_a = getExplicitRuleType(type)) !== null && _a !== void 0 ? _a : type.name;\n  } else if (ast.isInterface(type) || ast.isType(type) || ast.isReturnType(type)) {\n    return type.name;\n  } else if (ast.isAction(type)) {\n    const actionType = getActionType(type);\n    if (actionType) {\n      return actionType;\n    }\n  } else if (ast.isInferredType(type)) {\n    return type.name;\n  }\n  throw new Error('Cannot get name of Unknown Type');\n}\nexport function getActionType(action) {\n  var _a;\n  if (action.inferredType) {\n    return action.inferredType.name;\n  } else if ((_a = action.type) === null || _a === void 0 ? void 0 : _a.ref) {\n    return getTypeName(action.type.ref);\n  }\n  return undefined; // not inferring and not referencing a valid type\n}\n/**\n * This function is used at development time (for code generation and the internal type system) to get the type of the AST node produced by the given rule.\n * For data type rules, the name of the rule is returned,\n * e.g. \"INT_value returns number: MY_INT;\" returns \"INT_value\".\n * @param rule the given rule\n * @returns the name of the AST node type of the rule\n */\nexport function getRuleTypeName(rule) {\n  var _a, _b, _c;\n  if (ast.isTerminalRule(rule)) {\n    return (_b = (_a = rule.type) === null || _a === void 0 ? void 0 : _a.name) !== null && _b !== void 0 ? _b : 'string';\n  } else {\n    return isDataTypeRule(rule) ? rule.name : (_c = getExplicitRuleType(rule)) !== null && _c !== void 0 ? _c : rule.name;\n  }\n}\n/**\n * This function is used at runtime to get the actual type of the values produced by the given rule at runtime.\n * For data type rules, the name of the declared return type of the rule is returned (if any),\n * e.g. \"INT_value returns number: MY_INT;\" returns \"number\".\n * @param rule the given rule\n * @returns the name of the type of the produced values of the rule at runtime\n */\nexport function getRuleType(rule) {\n  var _a, _b, _c;\n  if (ast.isTerminalRule(rule)) {\n    return (_b = (_a = rule.type) === null || _a === void 0 ? void 0 : _a.name) !== null && _b !== void 0 ? _b : 'string';\n  } else {\n    return (_c = getExplicitRuleType(rule)) !== null && _c !== void 0 ? _c : rule.name;\n  }\n}\nexport function terminalRegex(terminalRule) {\n  const flags = {\n    s: false,\n    i: false,\n    u: false\n  };\n  const source = abstractElementToRegex(terminalRule.definition, flags);\n  const flagText = Object.entries(flags).filter(([, value]) => value).map(([name]) => name).join('');\n  return new RegExp(source, flagText);\n}\n// Using [\\s\\S]* allows to match everything, compared to . which doesn't match line terminators\nconst WILDCARD = /[\\s\\S]/.source;\nfunction abstractElementToRegex(element, flags) {\n  if (ast.isTerminalAlternatives(element)) {\n    return terminalAlternativesToRegex(element);\n  } else if (ast.isTerminalGroup(element)) {\n    return terminalGroupToRegex(element);\n  } else if (ast.isCharacterRange(element)) {\n    return characterRangeToRegex(element);\n  } else if (ast.isTerminalRuleCall(element)) {\n    const rule = element.rule.ref;\n    if (!rule) {\n      throw new Error('Missing rule reference.');\n    }\n    return withCardinality(abstractElementToRegex(rule.definition), {\n      cardinality: element.cardinality,\n      lookahead: element.lookahead\n    });\n  } else if (ast.isNegatedToken(element)) {\n    return negateTokenToRegex(element);\n  } else if (ast.isUntilToken(element)) {\n    return untilTokenToRegex(element);\n  } else if (ast.isRegexToken(element)) {\n    const lastSlash = element.regex.lastIndexOf('/');\n    const source = element.regex.substring(1, lastSlash);\n    const regexFlags = element.regex.substring(lastSlash + 1);\n    if (flags) {\n      flags.i = regexFlags.includes('i');\n      flags.s = regexFlags.includes('s');\n      flags.u = regexFlags.includes('u');\n    }\n    return withCardinality(source, {\n      cardinality: element.cardinality,\n      lookahead: element.lookahead,\n      wrap: false\n    });\n  } else if (ast.isWildcard(element)) {\n    return withCardinality(WILDCARD, {\n      cardinality: element.cardinality,\n      lookahead: element.lookahead\n    });\n  } else {\n    throw new Error(`Invalid terminal element: ${element === null || element === void 0 ? void 0 : element.$type}`);\n  }\n}\nfunction terminalAlternativesToRegex(alternatives) {\n  return withCardinality(alternatives.elements.map(e => abstractElementToRegex(e)).join('|'), {\n    cardinality: alternatives.cardinality,\n    lookahead: alternatives.lookahead\n  });\n}\nfunction terminalGroupToRegex(group) {\n  return withCardinality(group.elements.map(e => abstractElementToRegex(e)).join(''), {\n    cardinality: group.cardinality,\n    lookahead: group.lookahead\n  });\n}\nfunction untilTokenToRegex(until) {\n  return withCardinality(`${WILDCARD}*?${abstractElementToRegex(until.terminal)}`, {\n    cardinality: until.cardinality,\n    lookahead: until.lookahead\n  });\n}\nfunction negateTokenToRegex(negate) {\n  return withCardinality(`(?!${abstractElementToRegex(negate.terminal)})${WILDCARD}*?`, {\n    cardinality: negate.cardinality,\n    lookahead: negate.lookahead\n  });\n}\nfunction characterRangeToRegex(range) {\n  if (range.right) {\n    return withCardinality(`[${keywordToRegex(range.left)}-${keywordToRegex(range.right)}]`, {\n      cardinality: range.cardinality,\n      lookahead: range.lookahead,\n      wrap: false\n    });\n  }\n  return withCardinality(keywordToRegex(range.left), {\n    cardinality: range.cardinality,\n    lookahead: range.lookahead,\n    wrap: false\n  });\n}\nfunction keywordToRegex(keyword) {\n  return escapeRegExp(keyword.value);\n}\nfunction withCardinality(regex, options) {\n  var _a;\n  if (options.wrap !== false || options.lookahead) {\n    regex = `(${(_a = options.lookahead) !== null && _a !== void 0 ? _a : ''}${regex})`;\n  }\n  if (options.cardinality) {\n    return `${regex}${options.cardinality}`;\n  }\n  return regex;\n}", "map": {"version": 3, "names": ["assertUnreachable", "ast", "isCompositeCstNode", "getContainerOfType", "streamAllContents", "streamCst", "escapeRegExp", "isWhitespace", "getEntryRule", "grammar", "rules", "find", "e", "isParserRule", "entry", "getHiddenRules", "filter", "isTerminalRule", "hidden", "getAllReachableRules", "allTerminals", "ruleNames", "Set", "entryRule", "topMostRules", "concat", "rule", "ruleDfs", "has", "name", "add", "visitedSet", "for<PERSON>ach", "node", "isRuleCall", "isTerminalRuleCall", "refRule", "ref", "getCrossReferenceTerminal", "crossRef", "terminal", "type", "nameAssigment", "findNameAssignment", "undefined", "isCommentTerminal", "terminalRule", "terminalRegex", "findNodesForProperty", "property", "findNodesForPropertyInternal", "astNode", "findNodeForProperty", "index", "nodes", "length", "Math", "max", "min", "element", "first", "nodeFeature", "grammarSource", "isAssignment", "feature", "content", "flatMap", "findNodesForKeyword", "keyword", "findNodesForKeywordInternal", "findNodeForKeyword", "isKeyword", "value", "treeIterator", "iterator", "result", "keywordNodes", "next", "done", "childNode", "push", "prune", "findAssignment", "cstNode", "_a", "container", "assignment", "startNode", "isInferredType", "isAction", "$container", "findNameAssignmentInternal", "Map", "cache", "go", "refType", "childAssignment", "parentAssignment", "set", "get", "toLowerCase", "isSimpleType", "typeRef", "getActionAtElement", "parent", "isGroup", "elements", "indexOf", "i", "item", "action", "isAbstractElement", "isOptionalCardinality", "cardinality", "Boolean", "guardCondition", "isArrayCardinality", "isArrayOperator", "operator", "isDataTypeRule", "isDataTypeRuleInternal", "visited", "definition", "isDataType", "isDataTypeInternal", "isArrayType", "isReferenceType", "isUnionType", "types", "every", "primitiveType", "stringType", "isType", "getExplicitRuleType", "inferredType", "dataType", "returnType", "isInterface", "getTypeName", "isReturnType", "actionType", "getActionType", "Error", "getRuleTypeName", "_b", "_c", "getRuleType", "flags", "s", "u", "source", "abstractElementToRegex", "flagText", "Object", "entries", "map", "join", "RegExp", "WILDCARD", "isTerminalAlternatives", "terminalAlternativesToRegex", "isTerminalGroup", "terminalGroupToRegex", "isCharacterRange", "characterRangeToRegex", "withCardinality", "<PERSON><PERSON><PERSON>", "isNegatedToken", "negateTokenToRegex", "isUntilToken", "untilTokenToRegex", "isRegexToken", "lastSlash", "regex", "lastIndexOf", "substring", "regexFlags", "includes", "wrap", "isWildcard", "$type", "alternatives", "group", "until", "negate", "range", "right", "keywordToRegex", "left", "options"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/grammar-utils.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021-2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { assertUnreachable } from '../utils/errors.js';\r\nimport * as ast from '../languages/generated/ast.js';\r\nimport type { AstNode, CstNode } from '../syntax-tree.js';\r\nimport { isCompositeCstNode } from '../syntax-tree.js';\r\nimport { getContainerOfType, streamAllContents } from './ast-utils.js';\r\nimport { streamCst } from './cst-utils.js';\r\nimport { escapeRegExp, isWhitespace } from './regexp-utils.js';\r\n\r\n/**\r\n * Returns the entry rule of the given grammar, if any. If the grammar file does not contain an entry rule,\r\n * the result is `undefined`.\r\n */\r\nexport function getEntryRule(grammar: ast.Grammar): ast.ParserRule | undefined {\r\n    return grammar.rules.find(e => ast.isParserRule(e) && e.entry) as ast.ParserRule;\r\n}\r\n\r\n/**\r\n * Returns all hidden terminal rules of the given grammar, if any.\r\n */\r\nexport function getHiddenRules(grammar: ast.Grammar) {\r\n    return grammar.rules.filter((e): e is ast.TerminalRule => ast.isTerminalRule(e) && e.hidden);\r\n}\r\n\r\n/**\r\n * Returns all rules that can be reached from the topmost rules of the specified grammar (entry and hidden terminal rules).\r\n *\r\n * @param grammar The grammar that contains all rules\r\n * @param allTerminals Whether or not to include terminals that are referenced only by other terminals\r\n * @returns A list of referenced parser and terminal rules. If the grammar contains no entry rule,\r\n *      this function returns all rules of the specified grammar.\r\n */\r\nexport function getAllReachableRules(grammar: ast.Grammar, allTerminals: boolean): Set<ast.AbstractRule> {\r\n    const ruleNames = new Set<string>();\r\n    const entryRule = getEntryRule(grammar);\r\n    if (!entryRule) {\r\n        return new Set(grammar.rules);\r\n    }\r\n\r\n    const topMostRules = [entryRule as ast.AbstractRule].concat(getHiddenRules(grammar));\r\n    for (const rule of topMostRules) {\r\n        ruleDfs(rule, ruleNames, allTerminals);\r\n    }\r\n\r\n    const rules = new Set<ast.AbstractRule>();\r\n    for (const rule of grammar.rules) {\r\n        if (ruleNames.has(rule.name) || (ast.isTerminalRule(rule) && rule.hidden)) {\r\n            rules.add(rule);\r\n        }\r\n    }\r\n    return rules;\r\n}\r\n\r\nfunction ruleDfs(rule: ast.AbstractRule, visitedSet: Set<string>, allTerminals: boolean): void {\r\n    visitedSet.add(rule.name);\r\n    streamAllContents(rule).forEach(node => {\r\n        if (ast.isRuleCall(node) || (allTerminals && ast.isTerminalRuleCall(node))) {\r\n            const refRule = node.rule.ref;\r\n            if (refRule && !visitedSet.has(refRule.name)) {\r\n                ruleDfs(refRule, visitedSet, allTerminals);\r\n            }\r\n        }\r\n    });\r\n}\r\n\r\n/**\r\n * Determines the grammar expression used to parse a cross-reference (usually a reference to a terminal rule).\r\n * A cross-reference can declare this expression explicitly in the form `[Type : Terminal]`, but if `Terminal`\r\n * is omitted, this function attempts to infer it from the name of the referenced `Type` (using `findNameAssignment`).\r\n *\r\n * Returns the grammar expression used to parse the given cross-reference, or `undefined` if it is not declared\r\n * and cannot be inferred.\r\n */\r\nexport function getCrossReferenceTerminal(crossRef: ast.CrossReference): ast.AbstractElement | undefined {\r\n    if (crossRef.terminal) {\r\n        return crossRef.terminal;\r\n    } else if (crossRef.type.ref) {\r\n        const nameAssigment = findNameAssignment(crossRef.type.ref);\r\n        return nameAssigment?.terminal;\r\n    }\r\n    return undefined;\r\n}\r\n\r\n/**\r\n * Determines whether the given terminal rule represents a comment. This is true if the rule is marked\r\n * as `hidden` and it does not match white space. This means every hidden token (i.e. excluded from the AST)\r\n * that contains visible characters is considered a comment.\r\n */\r\nexport function isCommentTerminal(terminalRule: ast.TerminalRule): boolean {\r\n    return terminalRule.hidden && !isWhitespace(terminalRegex(terminalRule));\r\n}\r\n\r\n/**\r\n * Find all CST nodes within the given node that contribute to the specified property.\r\n *\r\n * @param node A CST node in which to look for property assignments. If this is undefined, the result is an empty array.\r\n * @param property A property name of the constructed AST node. If this is undefined, the result is an empty array.\r\n */\r\nexport function findNodesForProperty(node: CstNode | undefined, property: string | undefined): CstNode[] {\r\n    if (!node || !property) {\r\n        return [];\r\n    }\r\n    return findNodesForPropertyInternal(node, property, node.astNode, true);\r\n}\r\n\r\n/**\r\n * Find a single CST node within the given node that contributes to the specified property.\r\n *\r\n * @param node A CST node in which to look for property assignments. If this is undefined, the result is `undefined`.\r\n * @param property A property name of the constructed AST node. If this is undefined, the result is `undefined`.\r\n * @param index If no index is specified or the index is less than zero, the first found node is returned. If the\r\n *        specified index exceeds the number of assignments to the property, the last found node is returned. Otherwise,\r\n *        the node with the specified index is returned.\r\n */\r\nexport function findNodeForProperty(node: CstNode | undefined, property: string | undefined, index?: number): CstNode | undefined {\r\n    if (!node || !property) {\r\n        return undefined;\r\n    }\r\n    const nodes = findNodesForPropertyInternal(node, property, node.astNode, true);\r\n    if (nodes.length === 0) {\r\n        return undefined;\r\n    }\r\n    if (index !== undefined) {\r\n        index = Math.max(0, Math.min(index, nodes.length - 1));\r\n    } else {\r\n        index = 0;\r\n    }\r\n    return nodes[index];\r\n}\r\n\r\nfunction findNodesForPropertyInternal(node: CstNode, property: string, element: AstNode | undefined, first: boolean): CstNode[] {\r\n    if (!first) {\r\n        const nodeFeature = getContainerOfType(node.grammarSource, ast.isAssignment);\r\n        if (nodeFeature && nodeFeature.feature === property) {\r\n            return [node];\r\n        }\r\n    }\r\n    if (isCompositeCstNode(node) && node.astNode === element) {\r\n        return node.content.flatMap(e => findNodesForPropertyInternal(e, property, element, false));\r\n    }\r\n    return [];\r\n}\r\n\r\n/**\r\n * Find all CST nodes within the given node that correspond to the specified keyword.\r\n *\r\n * @param node A CST node in which to look for keywords. If this is undefined, the result is an empty array.\r\n * @param keyword A keyword as specified in the grammar.\r\n */\r\nexport function findNodesForKeyword(node: CstNode | undefined, keyword: string): CstNode[] {\r\n    if (!node) {\r\n        return [];\r\n    }\r\n    return findNodesForKeywordInternal(node, keyword, node?.astNode);\r\n}\r\n\r\n/**\r\n * Find a single CST node within the given node that corresponds to the specified keyword.\r\n *\r\n * @param node A CST node in which to look for keywords. If this is undefined, the result is `undefined`.\r\n * @param keyword A keyword as specified in the grammar.\r\n * @param index If no index is specified or the index is less than zero, the first found node is returned. If the\r\n *        specified index exceeds the number of keyword occurrences, the last found node is returned. Otherwise,\r\n *        the node with the specified index is returned.\r\n */\r\nexport function findNodeForKeyword(node: CstNode | undefined, keyword: string, index?: number): CstNode | undefined {\r\n    if (!node) {\r\n        return undefined;\r\n    }\r\n    const nodes = findNodesForKeywordInternal(node, keyword, node?.astNode);\r\n    if (nodes.length === 0) {\r\n        return undefined;\r\n    }\r\n    if (index !== undefined) {\r\n        index = Math.max(0, Math.min(index, nodes.length - 1));\r\n    } else {\r\n        index = 0;\r\n    }\r\n    return nodes[index];\r\n}\r\n\r\nexport function findNodesForKeywordInternal(node: CstNode, keyword: string, element: AstNode | undefined): CstNode[] {\r\n    if (node.astNode !== element) {\r\n        return [];\r\n    }\r\n    if (ast.isKeyword(node.grammarSource) && node.grammarSource.value === keyword) {\r\n        return [node];\r\n    }\r\n    const treeIterator = streamCst(node).iterator();\r\n    let result: IteratorResult<CstNode>;\r\n    const keywordNodes: CstNode[] = [];\r\n    do {\r\n        result = treeIterator.next();\r\n        if (!result.done) {\r\n            const childNode = result.value;\r\n            if (childNode.astNode === element) {\r\n                if (ast.isKeyword(childNode.grammarSource) && childNode.grammarSource.value === keyword) {\r\n                    keywordNodes.push(childNode);\r\n                }\r\n            } else {\r\n                treeIterator.prune();\r\n            }\r\n        }\r\n    } while (!result.done);\r\n    return keywordNodes;\r\n}\r\n\r\n/**\r\n * If the given CST node was parsed in the context of a property assignment, the respective `Assignment` grammar\r\n * node is returned. If no assignment is found, the result is `undefined`.\r\n *\r\n * @param cstNode A CST node for which to find a property assignment.\r\n */\r\nexport function findAssignment(cstNode: CstNode): ast.Assignment | undefined {\r\n    const astNode = cstNode.astNode;\r\n    // Only search until the ast node of the parent cst node is no longer the original ast node\r\n    // This would make us jump to a preceding rule call, which contains only unrelated assignments\r\n    while (astNode === cstNode.container?.astNode) {\r\n        const assignment = getContainerOfType(cstNode.grammarSource, ast.isAssignment);\r\n        if (assignment) {\r\n            return assignment;\r\n        }\r\n        cstNode = cstNode.container;\r\n    }\r\n    return undefined;\r\n}\r\n\r\n/**\r\n * Find an assignment to the `name` property for the given grammar type. This requires the `type` to be inferred\r\n * from a parser rule, and that rule must contain an assignment to the `name` property. In all other cases,\r\n * this function returns `undefined`.\r\n */\r\nexport function findNameAssignment(type: ast.AbstractType): ast.Assignment | undefined {\r\n    let startNode: AstNode = type;\r\n    if (ast.isInferredType(startNode)) {\r\n        // for inferred types, the location to start searching for the name-assignment is different\r\n        if (ast.isAction(startNode.$container)) {\r\n            // a type which is explicitly inferred by an action: investigate the sibbling of the Action node, i.e. start searching at the Action's parent\r\n            startNode = startNode.$container.$container!;\r\n        } else if (ast.isParserRule(startNode.$container)) {\r\n            // investigate the parser rule with the explicitly inferred type\r\n            startNode = startNode.$container;\r\n        } else {\r\n            assertUnreachable(startNode.$container);\r\n        }\r\n    }\r\n    return findNameAssignmentInternal(type, startNode, new Map());\r\n}\r\n\r\nfunction findNameAssignmentInternal(type: ast.AbstractType, startNode: AstNode, cache: Map<ast.AbstractType, ast.Assignment | undefined>): ast.Assignment | undefined {\r\n    // the cache is only required to prevent infinite loops\r\n    function go(node: AstNode, refType: ast.AbstractType): ast.Assignment | undefined {\r\n        let childAssignment: ast.Assignment | undefined = undefined;\r\n        const parentAssignment = getContainerOfType(node, ast.isAssignment);\r\n        // No parent assignment implies unassigned rule call\r\n        if (!parentAssignment) {\r\n            childAssignment = findNameAssignmentInternal(refType, refType, cache);\r\n        }\r\n        cache.set(type, childAssignment);\r\n        return childAssignment;\r\n    }\r\n\r\n    if (cache.has(type)) {\r\n        return cache.get(type);\r\n    }\r\n    cache.set(type, undefined);\r\n    for (const node of streamAllContents(startNode)) {\r\n        if (ast.isAssignment(node) && node.feature.toLowerCase() === 'name') {\r\n            cache.set(type, node);\r\n            return node;\r\n        } else if (ast.isRuleCall(node) && ast.isParserRule(node.rule.ref)) {\r\n            return go(node, node.rule.ref);\r\n        } else if (ast.isSimpleType(node) && node.typeRef?.ref) {\r\n            return go(node, node.typeRef.ref);\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function getActionAtElement(element: ast.AbstractElement): ast.Action | undefined {\r\n    const parent = element.$container;\r\n    if (ast.isGroup(parent)) {\r\n        const elements = parent.elements;\r\n        const index = elements.indexOf(element);\r\n        for (let i = index - 1; i >= 0; i--) {\r\n            const item = elements[i];\r\n            if (ast.isAction(item)) {\r\n                return item;\r\n            } else {\r\n                const action = streamAllContents(elements[i]).find(ast.isAction);\r\n                if (action) {\r\n                    return action;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (ast.isAbstractElement(parent)) {\r\n        return getActionAtElement(parent);\r\n    } else {\r\n        return undefined;\r\n    }\r\n}\r\n\r\nexport type Cardinality = '?' | '*' | '+' | undefined;\r\nexport type Operator = '=' | '+=' | '?=' | undefined;\r\n\r\nexport function isOptionalCardinality(cardinality?: Cardinality, element?: ast.AbstractElement): boolean {\r\n    return cardinality === '?' || cardinality === '*' || (ast.isGroup(element) && Boolean(element.guardCondition));\r\n}\r\n\r\nexport function isArrayCardinality(cardinality?: Cardinality): boolean {\r\n    return cardinality === '*' || cardinality === '+';\r\n}\r\n\r\nexport function isArrayOperator(operator?: Operator): boolean {\r\n    return operator === '+=';\r\n}\r\n\r\n/**\r\n * Determines whether the given parser rule is a _data type rule_, meaning that it has a\r\n * primitive return type like `number`, `boolean`, etc.\r\n */\r\nexport function isDataTypeRule(rule: ast.ParserRule): boolean {\r\n    return isDataTypeRuleInternal(rule, new Set());\r\n}\r\n\r\nfunction isDataTypeRuleInternal(rule: ast.ParserRule, visited: Set<ast.ParserRule>): boolean {\r\n    if (visited.has(rule)) {\r\n        return true;\r\n    } else {\r\n        visited.add(rule);\r\n    }\r\n    for (const node of streamAllContents(rule)) {\r\n        if (ast.isRuleCall(node)) {\r\n            if (!node.rule.ref) {\r\n                // RuleCall to unresolved rule. Don't assume `rule` is a DataType rule.\r\n                return false;\r\n            }\r\n            if (ast.isParserRule(node.rule.ref) && !isDataTypeRuleInternal(node.rule.ref, visited)) {\r\n                return false;\r\n            }\r\n        } else if (ast.isAssignment(node)) {\r\n            return false;\r\n        } else if (ast.isAction(node)) {\r\n            return false;\r\n        }\r\n    }\r\n    return Boolean(rule.definition);\r\n}\r\n\r\nexport function isDataType(type: ast.Type): boolean {\r\n    return isDataTypeInternal(type.type, new Set());\r\n}\r\n\r\nfunction isDataTypeInternal(type: ast.TypeDefinition, visited: Set<ast.TypeDefinition>): boolean {\r\n    if (visited.has(type)) {\r\n        return true;\r\n    } else {\r\n        visited.add(type);\r\n    }\r\n    if (ast.isArrayType(type)) {\r\n        return false;\r\n    } else if (ast.isReferenceType(type)) {\r\n        return false;\r\n    } else if (ast.isUnionType(type)) {\r\n        return type.types.every(e => isDataTypeInternal(e, visited));\r\n    } else if (ast.isSimpleType(type)) {\r\n        if (type.primitiveType !== undefined) {\r\n            return true;\r\n        } else if (type.stringType !== undefined) {\r\n            return true;\r\n        } else if (type.typeRef !== undefined) {\r\n            const ref = type.typeRef.ref;\r\n            if (ast.isType(ref)) {\r\n                return isDataTypeInternal(ref.type, visited);\r\n            } else {\r\n                return false;\r\n            }\r\n        } else {\r\n            return false;\r\n        }\r\n    } else {\r\n        return false;\r\n    }\r\n}\r\n\r\nexport function getExplicitRuleType(rule: ast.ParserRule): string | undefined {\r\n    if (rule.inferredType) {\r\n        return rule.inferredType.name;\r\n    } else if (rule.dataType) {\r\n        return rule.dataType;\r\n    } else if (rule.returnType) {\r\n        const refType = rule.returnType.ref;\r\n        if (refType) {\r\n            // check if we need to check Action as return type\r\n            if (ast.isParserRule(refType)) {\r\n                return refType.name;\r\n            } else if (ast.isInterface(refType) || ast.isType(refType)) {\r\n                return refType.name;\r\n            }\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function getTypeName(type: ast.AbstractType | ast.Action): string {\r\n    if (ast.isParserRule(type)) {\r\n        return isDataTypeRule(type) ? type.name : getExplicitRuleType(type) ?? type.name;\r\n    } else if (ast.isInterface(type) || ast.isType(type) || ast.isReturnType(type)) {\r\n        return type.name;\r\n    } else if (ast.isAction(type)) {\r\n        const actionType = getActionType(type);\r\n        if (actionType) {\r\n            return actionType;\r\n        }\r\n    } else if (ast.isInferredType(type)) {\r\n        return type.name;\r\n    }\r\n    throw new Error('Cannot get name of Unknown Type');\r\n}\r\n\r\nexport function getActionType(action: ast.Action): string | undefined {\r\n    if (action.inferredType) {\r\n        return action.inferredType.name;\r\n    } else if (action.type?.ref) {\r\n        return getTypeName(action.type.ref);\r\n    }\r\n    return undefined; // not inferring and not referencing a valid type\r\n}\r\n\r\n/**\r\n * This function is used at development time (for code generation and the internal type system) to get the type of the AST node produced by the given rule.\r\n * For data type rules, the name of the rule is returned,\r\n * e.g. \"INT_value returns number: MY_INT;\" returns \"INT_value\".\r\n * @param rule the given rule\r\n * @returns the name of the AST node type of the rule\r\n */\r\nexport function getRuleTypeName(rule: ast.AbstractRule): string {\r\n    if (ast.isTerminalRule(rule)) {\r\n        return rule.type?.name ?? 'string';\r\n    } else {\r\n        return isDataTypeRule(rule) ? rule.name : getExplicitRuleType(rule) ?? rule.name;\r\n    }\r\n}\r\n\r\n/**\r\n * This function is used at runtime to get the actual type of the values produced by the given rule at runtime.\r\n * For data type rules, the name of the declared return type of the rule is returned (if any),\r\n * e.g. \"INT_value returns number: MY_INT;\" returns \"number\".\r\n * @param rule the given rule\r\n * @returns the name of the type of the produced values of the rule at runtime\r\n */\r\nexport function getRuleType(rule: ast.AbstractRule): string {\r\n    if (ast.isTerminalRule(rule)) {\r\n        return rule.type?.name ?? 'string';\r\n    } else {\r\n        return getExplicitRuleType(rule) ?? rule.name;\r\n    }\r\n}\r\n\r\nexport function terminalRegex(terminalRule: ast.TerminalRule): RegExp {\r\n    const flags: Flags = {\r\n        s: false,\r\n        i: false,\r\n        u: false\r\n    };\r\n    const source = abstractElementToRegex(terminalRule.definition, flags);\r\n    const flagText = Object.entries(flags).filter(([, value]) => value).map(([name]) => name).join('');\r\n    return new RegExp(source, flagText);\r\n}\r\n\r\n// Using [\\s\\S]* allows to match everything, compared to . which doesn't match line terminators\r\nconst WILDCARD = /[\\s\\S]/.source;\r\n\r\ntype Flags = {\r\n    s: boolean;\r\n    i: boolean;\r\n    u: boolean;\r\n}\r\n\r\nfunction abstractElementToRegex(element: ast.AbstractElement, flags?: Flags): string {\r\n    if (ast.isTerminalAlternatives(element)) {\r\n        return terminalAlternativesToRegex(element);\r\n    } else if (ast.isTerminalGroup(element)) {\r\n        return terminalGroupToRegex(element);\r\n    } else if (ast.isCharacterRange(element)) {\r\n        return characterRangeToRegex(element);\r\n    } else if (ast.isTerminalRuleCall(element)) {\r\n        const rule = element.rule.ref;\r\n        if (!rule) {\r\n            throw new Error('Missing rule reference.');\r\n        }\r\n        return withCardinality(abstractElementToRegex(rule.definition), {\r\n            cardinality: element.cardinality,\r\n            lookahead: element.lookahead\r\n        });\r\n    } else if (ast.isNegatedToken(element)) {\r\n        return negateTokenToRegex(element);\r\n    } else if (ast.isUntilToken(element)) {\r\n        return untilTokenToRegex(element);\r\n    } else if (ast.isRegexToken(element)) {\r\n        const lastSlash = element.regex.lastIndexOf('/');\r\n        const source = element.regex.substring(1, lastSlash);\r\n        const regexFlags = element.regex.substring(lastSlash + 1);\r\n        if (flags) {\r\n            flags.i = regexFlags.includes('i');\r\n            flags.s = regexFlags.includes('s');\r\n            flags.u = regexFlags.includes('u');\r\n        }\r\n        return withCardinality(source, {\r\n            cardinality: element.cardinality,\r\n            lookahead: element.lookahead,\r\n            wrap: false\r\n        });\r\n    } else if (ast.isWildcard(element)) {\r\n        return withCardinality(WILDCARD, {\r\n            cardinality: element.cardinality,\r\n            lookahead: element.lookahead\r\n        });\r\n    } else {\r\n        throw new Error(`Invalid terminal element: ${element?.$type}`);\r\n    }\r\n}\r\n\r\nfunction terminalAlternativesToRegex(alternatives: ast.TerminalAlternatives): string {\r\n    return withCardinality(alternatives.elements.map(e => abstractElementToRegex(e)).join('|'), {\r\n        cardinality: alternatives.cardinality,\r\n        lookahead: alternatives.lookahead\r\n    });\r\n}\r\n\r\nfunction terminalGroupToRegex(group: ast.TerminalGroup): string {\r\n    return withCardinality(group.elements.map(e => abstractElementToRegex(e)).join(''), {\r\n        cardinality: group.cardinality,\r\n        lookahead: group.lookahead\r\n    });\r\n}\r\n\r\nfunction untilTokenToRegex(until: ast.UntilToken): string {\r\n    return withCardinality(`${WILDCARD}*?${abstractElementToRegex(until.terminal)}`, {\r\n        cardinality: until.cardinality,\r\n        lookahead: until.lookahead\r\n    });\r\n}\r\n\r\nfunction negateTokenToRegex(negate: ast.NegatedToken): string {\r\n    return withCardinality(`(?!${abstractElementToRegex(negate.terminal)})${WILDCARD}*?`, {\r\n        cardinality: negate.cardinality,\r\n        lookahead: negate.lookahead\r\n    });\r\n}\r\n\r\nfunction characterRangeToRegex(range: ast.CharacterRange): string {\r\n    if (range.right) {\r\n        return withCardinality(`[${keywordToRegex(range.left)}-${keywordToRegex(range.right)}]`, {\r\n            cardinality: range.cardinality,\r\n            lookahead: range.lookahead,\r\n            wrap: false\r\n        });\r\n    }\r\n    return withCardinality(keywordToRegex(range.left), {\r\n        cardinality: range.cardinality,\r\n        lookahead: range.lookahead,\r\n        wrap: false\r\n    });\r\n}\r\n\r\nfunction keywordToRegex(keyword: ast.Keyword): string {\r\n    return escapeRegExp(keyword.value);\r\n}\r\n\r\nfunction withCardinality(regex: string, options: {\r\n    cardinality?: string\r\n    wrap?: boolean\r\n    lookahead?: string\r\n}): string {\r\n    if (options.wrap !== false || options.lookahead) {\r\n        regex = `(${options.lookahead ?? ''}${regex})`;\r\n    }\r\n    if (options.cardinality) {\r\n        return `${regex}${options.cardinality}`;\r\n    }\r\n    return regex;\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,iBAAiB,QAAQ,oBAAoB;AACtD,OAAO,KAAKC,GAAG,MAAM,+BAA+B;AAEpD,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACtE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAE9D;;;;AAIA,OAAM,SAAUC,YAAYA,CAACC,OAAoB;EAC7C,OAAOA,OAAO,CAACC,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIX,GAAG,CAACY,YAAY,CAACD,CAAC,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAmB;AACpF;AAEA;;;AAGA,OAAM,SAAUC,cAAcA,CAACN,OAAoB;EAC/C,OAAOA,OAAO,CAACC,KAAK,CAACM,MAAM,CAAEJ,CAAC,IAA4BX,GAAG,CAACgB,cAAc,CAACL,CAAC,CAAC,IAAIA,CAAC,CAACM,MAAM,CAAC;AAChG;AAEA;;;;;;;;AAQA,OAAM,SAAUC,oBAAoBA,CAACV,OAAoB,EAAEW,YAAqB;EAC5E,MAAMC,SAAS,GAAG,IAAIC,GAAG,EAAU;EACnC,MAAMC,SAAS,GAAGf,YAAY,CAACC,OAAO,CAAC;EACvC,IAAI,CAACc,SAAS,EAAE;IACZ,OAAO,IAAID,GAAG,CAACb,OAAO,CAACC,KAAK,CAAC;EACjC;EAEA,MAAMc,YAAY,GAAG,CAACD,SAA6B,CAAC,CAACE,MAAM,CAACV,cAAc,CAACN,OAAO,CAAC,CAAC;EACpF,KAAK,MAAMiB,IAAI,IAAIF,YAAY,EAAE;IAC7BG,OAAO,CAACD,IAAI,EAAEL,SAAS,EAAED,YAAY,CAAC;EAC1C;EAEA,MAAMV,KAAK,GAAG,IAAIY,GAAG,EAAoB;EACzC,KAAK,MAAMI,IAAI,IAAIjB,OAAO,CAACC,KAAK,EAAE;IAC9B,IAAIW,SAAS,CAACO,GAAG,CAACF,IAAI,CAACG,IAAI,CAAC,IAAK5B,GAAG,CAACgB,cAAc,CAACS,IAAI,CAAC,IAAIA,IAAI,CAACR,MAAO,EAAE;MACvER,KAAK,CAACoB,GAAG,CAACJ,IAAI,CAAC;IACnB;EACJ;EACA,OAAOhB,KAAK;AAChB;AAEA,SAASiB,OAAOA,CAACD,IAAsB,EAAEK,UAAuB,EAAEX,YAAqB;EACnFW,UAAU,CAACD,GAAG,CAACJ,IAAI,CAACG,IAAI,CAAC;EACzBzB,iBAAiB,CAACsB,IAAI,CAAC,CAACM,OAAO,CAACC,IAAI,IAAG;IACnC,IAAIhC,GAAG,CAACiC,UAAU,CAACD,IAAI,CAAC,IAAKb,YAAY,IAAInB,GAAG,CAACkC,kBAAkB,CAACF,IAAI,CAAE,EAAE;MACxE,MAAMG,OAAO,GAAGH,IAAI,CAACP,IAAI,CAACW,GAAG;MAC7B,IAAID,OAAO,IAAI,CAACL,UAAU,CAACH,GAAG,CAACQ,OAAO,CAACP,IAAI,CAAC,EAAE;QAC1CF,OAAO,CAACS,OAAO,EAAEL,UAAU,EAAEX,YAAY,CAAC;MAC9C;IACJ;EACJ,CAAC,CAAC;AACN;AAEA;;;;;;;;AAQA,OAAM,SAAUkB,yBAAyBA,CAACC,QAA4B;EAClE,IAAIA,QAAQ,CAACC,QAAQ,EAAE;IACnB,OAAOD,QAAQ,CAACC,QAAQ;EAC5B,CAAC,MAAM,IAAID,QAAQ,CAACE,IAAI,CAACJ,GAAG,EAAE;IAC1B,MAAMK,aAAa,GAAGC,kBAAkB,CAACJ,QAAQ,CAACE,IAAI,CAACJ,GAAG,CAAC;IAC3D,OAAOK,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEF,QAAQ;EAClC;EACA,OAAOI,SAAS;AACpB;AAEA;;;;;AAKA,OAAM,SAAUC,iBAAiBA,CAACC,YAA8B;EAC5D,OAAOA,YAAY,CAAC5B,MAAM,IAAI,CAACX,YAAY,CAACwC,aAAa,CAACD,YAAY,CAAC,CAAC;AAC5E;AAEA;;;;;;AAMA,OAAM,SAAUE,oBAAoBA,CAACf,IAAyB,EAAEgB,QAA4B;EACxF,IAAI,CAAChB,IAAI,IAAI,CAACgB,QAAQ,EAAE;IACpB,OAAO,EAAE;EACb;EACA,OAAOC,4BAA4B,CAACjB,IAAI,EAAEgB,QAAQ,EAAEhB,IAAI,CAACkB,OAAO,EAAE,IAAI,CAAC;AAC3E;AAEA;;;;;;;;;AASA,OAAM,SAAUC,mBAAmBA,CAACnB,IAAyB,EAAEgB,QAA4B,EAAEI,KAAc;EACvG,IAAI,CAACpB,IAAI,IAAI,CAACgB,QAAQ,EAAE;IACpB,OAAOL,SAAS;EACpB;EACA,MAAMU,KAAK,GAAGJ,4BAA4B,CAACjB,IAAI,EAAEgB,QAAQ,EAAEhB,IAAI,CAACkB,OAAO,EAAE,IAAI,CAAC;EAC9E,IAAIG,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOX,SAAS;EACpB;EACA,IAAIS,KAAK,KAAKT,SAAS,EAAE;IACrBS,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACL,KAAK,EAAEC,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1D,CAAC,MAAM;IACHF,KAAK,GAAG,CAAC;EACb;EACA,OAAOC,KAAK,CAACD,KAAK,CAAC;AACvB;AAEA,SAASH,4BAA4BA,CAACjB,IAAa,EAAEgB,QAAgB,EAAEU,OAA4B,EAAEC,KAAc;EAC/G,IAAI,CAACA,KAAK,EAAE;IACR,MAAMC,WAAW,GAAG1D,kBAAkB,CAAC8B,IAAI,CAAC6B,aAAa,EAAE7D,GAAG,CAAC8D,YAAY,CAAC;IAC5E,IAAIF,WAAW,IAAIA,WAAW,CAACG,OAAO,KAAKf,QAAQ,EAAE;MACjD,OAAO,CAAChB,IAAI,CAAC;IACjB;EACJ;EACA,IAAI/B,kBAAkB,CAAC+B,IAAI,CAAC,IAAIA,IAAI,CAACkB,OAAO,KAAKQ,OAAO,EAAE;IACtD,OAAO1B,IAAI,CAACgC,OAAO,CAACC,OAAO,CAACtD,CAAC,IAAIsC,4BAA4B,CAACtC,CAAC,EAAEqC,QAAQ,EAAEU,OAAO,EAAE,KAAK,CAAC,CAAC;EAC/F;EACA,OAAO,EAAE;AACb;AAEA;;;;;;AAMA,OAAM,SAAUQ,mBAAmBA,CAAClC,IAAyB,EAAEmC,OAAe;EAC1E,IAAI,CAACnC,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,OAAOoC,2BAA2B,CAACpC,IAAI,EAAEmC,OAAO,EAAEnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,OAAO,CAAC;AACpE;AAEA;;;;;;;;;AASA,OAAM,SAAUmB,kBAAkBA,CAACrC,IAAyB,EAAEmC,OAAe,EAAEf,KAAc;EACzF,IAAI,CAACpB,IAAI,EAAE;IACP,OAAOW,SAAS;EACpB;EACA,MAAMU,KAAK,GAAGe,2BAA2B,CAACpC,IAAI,EAAEmC,OAAO,EAAEnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,OAAO,CAAC;EACvE,IAAIG,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOX,SAAS;EACpB;EACA,IAAIS,KAAK,KAAKT,SAAS,EAAE;IACrBS,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACL,KAAK,EAAEC,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1D,CAAC,MAAM;IACHF,KAAK,GAAG,CAAC;EACb;EACA,OAAOC,KAAK,CAACD,KAAK,CAAC;AACvB;AAEA,OAAM,SAAUgB,2BAA2BA,CAACpC,IAAa,EAAEmC,OAAe,EAAET,OAA4B;EACpG,IAAI1B,IAAI,CAACkB,OAAO,KAAKQ,OAAO,EAAE;IAC1B,OAAO,EAAE;EACb;EACA,IAAI1D,GAAG,CAACsE,SAAS,CAACtC,IAAI,CAAC6B,aAAa,CAAC,IAAI7B,IAAI,CAAC6B,aAAa,CAACU,KAAK,KAAKJ,OAAO,EAAE;IAC3E,OAAO,CAACnC,IAAI,CAAC;EACjB;EACA,MAAMwC,YAAY,GAAGpE,SAAS,CAAC4B,IAAI,CAAC,CAACyC,QAAQ,EAAE;EAC/C,IAAIC,MAA+B;EACnC,MAAMC,YAAY,GAAc,EAAE;EAClC,GAAG;IACCD,MAAM,GAAGF,YAAY,CAACI,IAAI,EAAE;IAC5B,IAAI,CAACF,MAAM,CAACG,IAAI,EAAE;MACd,MAAMC,SAAS,GAAGJ,MAAM,CAACH,KAAK;MAC9B,IAAIO,SAAS,CAAC5B,OAAO,KAAKQ,OAAO,EAAE;QAC/B,IAAI1D,GAAG,CAACsE,SAAS,CAACQ,SAAS,CAACjB,aAAa,CAAC,IAAIiB,SAAS,CAACjB,aAAa,CAACU,KAAK,KAAKJ,OAAO,EAAE;UACrFQ,YAAY,CAACI,IAAI,CAACD,SAAS,CAAC;QAChC;MACJ,CAAC,MAAM;QACHN,YAAY,CAACQ,KAAK,EAAE;MACxB;IACJ;EACJ,CAAC,QAAQ,CAACN,MAAM,CAACG,IAAI;EACrB,OAAOF,YAAY;AACvB;AAEA;;;;;;AAMA,OAAM,SAAUM,cAAcA,CAACC,OAAgB;;EAC3C,MAAMhC,OAAO,GAAGgC,OAAO,CAAChC,OAAO;EAC/B;EACA;EACA,OAAOA,OAAO,MAAK,CAAAiC,EAAA,GAAAD,OAAO,CAACE,SAAS,cAAAD,EAAA,uBAAAA,EAAA,CAAEjC,OAAO,GAAE;IAC3C,MAAMmC,UAAU,GAAGnF,kBAAkB,CAACgF,OAAO,CAACrB,aAAa,EAAE7D,GAAG,CAAC8D,YAAY,CAAC;IAC9E,IAAIuB,UAAU,EAAE;MACZ,OAAOA,UAAU;IACrB;IACAH,OAAO,GAAGA,OAAO,CAACE,SAAS;EAC/B;EACA,OAAOzC,SAAS;AACpB;AAEA;;;;;AAKA,OAAM,SAAUD,kBAAkBA,CAACF,IAAsB;EACrD,IAAI8C,SAAS,GAAY9C,IAAI;EAC7B,IAAIxC,GAAG,CAACuF,cAAc,CAACD,SAAS,CAAC,EAAE;IAC/B;IACA,IAAItF,GAAG,CAACwF,QAAQ,CAACF,SAAS,CAACG,UAAU,CAAC,EAAE;MACpC;MACAH,SAAS,GAAGA,SAAS,CAACG,UAAU,CAACA,UAAW;IAChD,CAAC,MAAM,IAAIzF,GAAG,CAACY,YAAY,CAAC0E,SAAS,CAACG,UAAU,CAAC,EAAE;MAC/C;MACAH,SAAS,GAAGA,SAAS,CAACG,UAAU;IACpC,CAAC,MAAM;MACH1F,iBAAiB,CAACuF,SAAS,CAACG,UAAU,CAAC;IAC3C;EACJ;EACA,OAAOC,0BAA0B,CAAClD,IAAI,EAAE8C,SAAS,EAAE,IAAIK,GAAG,EAAE,CAAC;AACjE;AAEA,SAASD,0BAA0BA,CAAClD,IAAsB,EAAE8C,SAAkB,EAAEM,KAAwD;;EACpI;EACA,SAASC,EAAEA,CAAC7D,IAAa,EAAE8D,OAAyB;IAChD,IAAIC,eAAe,GAA+BpD,SAAS;IAC3D,MAAMqD,gBAAgB,GAAG9F,kBAAkB,CAAC8B,IAAI,EAAEhC,GAAG,CAAC8D,YAAY,CAAC;IACnE;IACA,IAAI,CAACkC,gBAAgB,EAAE;MACnBD,eAAe,GAAGL,0BAA0B,CAACI,OAAO,EAAEA,OAAO,EAAEF,KAAK,CAAC;IACzE;IACAA,KAAK,CAACK,GAAG,CAACzD,IAAI,EAAEuD,eAAe,CAAC;IAChC,OAAOA,eAAe;EAC1B;EAEA,IAAIH,KAAK,CAACjE,GAAG,CAACa,IAAI,CAAC,EAAE;IACjB,OAAOoD,KAAK,CAACM,GAAG,CAAC1D,IAAI,CAAC;EAC1B;EACAoD,KAAK,CAACK,GAAG,CAACzD,IAAI,EAAEG,SAAS,CAAC;EAC1B,KAAK,MAAMX,IAAI,IAAI7B,iBAAiB,CAACmF,SAAS,CAAC,EAAE;IAC7C,IAAItF,GAAG,CAAC8D,YAAY,CAAC9B,IAAI,CAAC,IAAIA,IAAI,CAAC+B,OAAO,CAACoC,WAAW,EAAE,KAAK,MAAM,EAAE;MACjEP,KAAK,CAACK,GAAG,CAACzD,IAAI,EAAER,IAAI,CAAC;MACrB,OAAOA,IAAI;IACf,CAAC,MAAM,IAAIhC,GAAG,CAACiC,UAAU,CAACD,IAAI,CAAC,IAAIhC,GAAG,CAACY,YAAY,CAACoB,IAAI,CAACP,IAAI,CAACW,GAAG,CAAC,EAAE;MAChE,OAAOyD,EAAE,CAAC7D,IAAI,EAAEA,IAAI,CAACP,IAAI,CAACW,GAAG,CAAC;IAClC,CAAC,MAAM,IAAIpC,GAAG,CAACoG,YAAY,CAACpE,IAAI,CAAC,KAAI,CAAAmD,EAAA,GAAAnD,IAAI,CAACqE,OAAO,cAAAlB,EAAA,uBAAAA,EAAA,CAAE/C,GAAG,GAAE;MACpD,OAAOyD,EAAE,CAAC7D,IAAI,EAAEA,IAAI,CAACqE,OAAO,CAACjE,GAAG,CAAC;IACrC;EACJ;EACA,OAAOO,SAAS;AACpB;AAEA,OAAM,SAAU2D,kBAAkBA,CAAC5C,OAA4B;EAC3D,MAAM6C,MAAM,GAAG7C,OAAO,CAAC+B,UAAU;EACjC,IAAIzF,GAAG,CAACwG,OAAO,CAACD,MAAM,CAAC,EAAE;IACrB,MAAME,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAChC,MAAMrD,KAAK,GAAGqD,QAAQ,CAACC,OAAO,CAAChD,OAAO,CAAC;IACvC,KAAK,IAAIiD,CAAC,GAAGvD,KAAK,GAAG,CAAC,EAAEuD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjC,MAAMC,IAAI,GAAGH,QAAQ,CAACE,CAAC,CAAC;MACxB,IAAI3G,GAAG,CAACwF,QAAQ,CAACoB,IAAI,CAAC,EAAE;QACpB,OAAOA,IAAI;MACf,CAAC,MAAM;QACH,MAAMC,MAAM,GAAG1G,iBAAiB,CAACsG,QAAQ,CAACE,CAAC,CAAC,CAAC,CAACjG,IAAI,CAACV,GAAG,CAACwF,QAAQ,CAAC;QAChE,IAAIqB,MAAM,EAAE;UACR,OAAOA,MAAM;QACjB;MACJ;IACJ;EACJ;EACA,IAAI7G,GAAG,CAAC8G,iBAAiB,CAACP,MAAM,CAAC,EAAE;IAC/B,OAAOD,kBAAkB,CAACC,MAAM,CAAC;EACrC,CAAC,MAAM;IACH,OAAO5D,SAAS;EACpB;AACJ;AAKA,OAAM,SAAUoE,qBAAqBA,CAACC,WAAyB,EAAEtD,OAA6B;EAC1F,OAAOsD,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,IAAKhH,GAAG,CAACwG,OAAO,CAAC9C,OAAO,CAAC,IAAIuD,OAAO,CAACvD,OAAO,CAACwD,cAAc,CAAE;AAClH;AAEA,OAAM,SAAUC,kBAAkBA,CAACH,WAAyB;EACxD,OAAOA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG;AACrD;AAEA,OAAM,SAAUI,eAAeA,CAACC,QAAmB;EAC/C,OAAOA,QAAQ,KAAK,IAAI;AAC5B;AAEA;;;;AAIA,OAAM,SAAUC,cAAcA,CAAC7F,IAAoB;EAC/C,OAAO8F,sBAAsB,CAAC9F,IAAI,EAAE,IAAIJ,GAAG,EAAE,CAAC;AAClD;AAEA,SAASkG,sBAAsBA,CAAC9F,IAAoB,EAAE+F,OAA4B;EAC9E,IAAIA,OAAO,CAAC7F,GAAG,CAACF,IAAI,CAAC,EAAE;IACnB,OAAO,IAAI;EACf,CAAC,MAAM;IACH+F,OAAO,CAAC3F,GAAG,CAACJ,IAAI,CAAC;EACrB;EACA,KAAK,MAAMO,IAAI,IAAI7B,iBAAiB,CAACsB,IAAI,CAAC,EAAE;IACxC,IAAIzB,GAAG,CAACiC,UAAU,CAACD,IAAI,CAAC,EAAE;MACtB,IAAI,CAACA,IAAI,CAACP,IAAI,CAACW,GAAG,EAAE;QAChB;QACA,OAAO,KAAK;MAChB;MACA,IAAIpC,GAAG,CAACY,YAAY,CAACoB,IAAI,CAACP,IAAI,CAACW,GAAG,CAAC,IAAI,CAACmF,sBAAsB,CAACvF,IAAI,CAACP,IAAI,CAACW,GAAG,EAAEoF,OAAO,CAAC,EAAE;QACpF,OAAO,KAAK;MAChB;IACJ,CAAC,MAAM,IAAIxH,GAAG,CAAC8D,YAAY,CAAC9B,IAAI,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB,CAAC,MAAM,IAAIhC,GAAG,CAACwF,QAAQ,CAACxD,IAAI,CAAC,EAAE;MAC3B,OAAO,KAAK;IAChB;EACJ;EACA,OAAOiF,OAAO,CAACxF,IAAI,CAACgG,UAAU,CAAC;AACnC;AAEA,OAAM,SAAUC,UAAUA,CAAClF,IAAc;EACrC,OAAOmF,kBAAkB,CAACnF,IAAI,CAACA,IAAI,EAAE,IAAInB,GAAG,EAAE,CAAC;AACnD;AAEA,SAASsG,kBAAkBA,CAACnF,IAAwB,EAAEgF,OAAgC;EAClF,IAAIA,OAAO,CAAC7F,GAAG,CAACa,IAAI,CAAC,EAAE;IACnB,OAAO,IAAI;EACf,CAAC,MAAM;IACHgF,OAAO,CAAC3F,GAAG,CAACW,IAAI,CAAC;EACrB;EACA,IAAIxC,GAAG,CAAC4H,WAAW,CAACpF,IAAI,CAAC,EAAE;IACvB,OAAO,KAAK;EAChB,CAAC,MAAM,IAAIxC,GAAG,CAAC6H,eAAe,CAACrF,IAAI,CAAC,EAAE;IAClC,OAAO,KAAK;EAChB,CAAC,MAAM,IAAIxC,GAAG,CAAC8H,WAAW,CAACtF,IAAI,CAAC,EAAE;IAC9B,OAAOA,IAAI,CAACuF,KAAK,CAACC,KAAK,CAACrH,CAAC,IAAIgH,kBAAkB,CAAChH,CAAC,EAAE6G,OAAO,CAAC,CAAC;EAChE,CAAC,MAAM,IAAIxH,GAAG,CAACoG,YAAY,CAAC5D,IAAI,CAAC,EAAE;IAC/B,IAAIA,IAAI,CAACyF,aAAa,KAAKtF,SAAS,EAAE;MAClC,OAAO,IAAI;IACf,CAAC,MAAM,IAAIH,IAAI,CAAC0F,UAAU,KAAKvF,SAAS,EAAE;MACtC,OAAO,IAAI;IACf,CAAC,MAAM,IAAIH,IAAI,CAAC6D,OAAO,KAAK1D,SAAS,EAAE;MACnC,MAAMP,GAAG,GAAGI,IAAI,CAAC6D,OAAO,CAACjE,GAAG;MAC5B,IAAIpC,GAAG,CAACmI,MAAM,CAAC/F,GAAG,CAAC,EAAE;QACjB,OAAOuF,kBAAkB,CAACvF,GAAG,CAACI,IAAI,EAAEgF,OAAO,CAAC;MAChD,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC,MAAM;MACH,OAAO,KAAK;IAChB;EACJ,CAAC,MAAM;IACH,OAAO,KAAK;EAChB;AACJ;AAEA,OAAM,SAAUY,mBAAmBA,CAAC3G,IAAoB;EACpD,IAAIA,IAAI,CAAC4G,YAAY,EAAE;IACnB,OAAO5G,IAAI,CAAC4G,YAAY,CAACzG,IAAI;EACjC,CAAC,MAAM,IAAIH,IAAI,CAAC6G,QAAQ,EAAE;IACtB,OAAO7G,IAAI,CAAC6G,QAAQ;EACxB,CAAC,MAAM,IAAI7G,IAAI,CAAC8G,UAAU,EAAE;IACxB,MAAMzC,OAAO,GAAGrE,IAAI,CAAC8G,UAAU,CAACnG,GAAG;IACnC,IAAI0D,OAAO,EAAE;MACT;MACA,IAAI9F,GAAG,CAACY,YAAY,CAACkF,OAAO,CAAC,EAAE;QAC3B,OAAOA,OAAO,CAAClE,IAAI;MACvB,CAAC,MAAM,IAAI5B,GAAG,CAACwI,WAAW,CAAC1C,OAAO,CAAC,IAAI9F,GAAG,CAACmI,MAAM,CAACrC,OAAO,CAAC,EAAE;QACxD,OAAOA,OAAO,CAAClE,IAAI;MACvB;IACJ;EACJ;EACA,OAAOe,SAAS;AACpB;AAEA,OAAM,SAAU8F,WAAWA,CAACjG,IAAmC;;EAC3D,IAAIxC,GAAG,CAACY,YAAY,CAAC4B,IAAI,CAAC,EAAE;IACxB,OAAO8E,cAAc,CAAC9E,IAAI,CAAC,GAAGA,IAAI,CAACZ,IAAI,GAAG,CAAAuD,EAAA,GAAAiD,mBAAmB,CAAC5F,IAAI,CAAC,cAAA2C,EAAA,cAAAA,EAAA,GAAI3C,IAAI,CAACZ,IAAI;EACpF,CAAC,MAAM,IAAI5B,GAAG,CAACwI,WAAW,CAAChG,IAAI,CAAC,IAAIxC,GAAG,CAACmI,MAAM,CAAC3F,IAAI,CAAC,IAAIxC,GAAG,CAAC0I,YAAY,CAAClG,IAAI,CAAC,EAAE;IAC5E,OAAOA,IAAI,CAACZ,IAAI;EACpB,CAAC,MAAM,IAAI5B,GAAG,CAACwF,QAAQ,CAAChD,IAAI,CAAC,EAAE;IAC3B,MAAMmG,UAAU,GAAGC,aAAa,CAACpG,IAAI,CAAC;IACtC,IAAImG,UAAU,EAAE;MACZ,OAAOA,UAAU;IACrB;EACJ,CAAC,MAAM,IAAI3I,GAAG,CAACuF,cAAc,CAAC/C,IAAI,CAAC,EAAE;IACjC,OAAOA,IAAI,CAACZ,IAAI;EACpB;EACA,MAAM,IAAIiH,KAAK,CAAC,iCAAiC,CAAC;AACtD;AAEA,OAAM,SAAUD,aAAaA,CAAC/B,MAAkB;;EAC5C,IAAIA,MAAM,CAACwB,YAAY,EAAE;IACrB,OAAOxB,MAAM,CAACwB,YAAY,CAACzG,IAAI;EACnC,CAAC,MAAM,IAAI,CAAAuD,EAAA,GAAA0B,MAAM,CAACrE,IAAI,cAAA2C,EAAA,uBAAAA,EAAA,CAAE/C,GAAG,EAAE;IACzB,OAAOqG,WAAW,CAAC5B,MAAM,CAACrE,IAAI,CAACJ,GAAG,CAAC;EACvC;EACA,OAAOO,SAAS,CAAC,CAAC;AACtB;AAEA;;;;;;;AAOA,OAAM,SAAUmG,eAAeA,CAACrH,IAAsB;;EAClD,IAAIzB,GAAG,CAACgB,cAAc,CAACS,IAAI,CAAC,EAAE;IAC1B,OAAO,CAAAsH,EAAA,IAAA5D,EAAA,GAAA1D,IAAI,CAACe,IAAI,cAAA2C,EAAA,uBAAAA,EAAA,CAAEvD,IAAI,cAAAmH,EAAA,cAAAA,EAAA,GAAI,QAAQ;EACtC,CAAC,MAAM;IACH,OAAOzB,cAAc,CAAC7F,IAAI,CAAC,GAAGA,IAAI,CAACG,IAAI,GAAG,CAAAoH,EAAA,GAAAZ,mBAAmB,CAAC3G,IAAI,CAAC,cAAAuH,EAAA,cAAAA,EAAA,GAAIvH,IAAI,CAACG,IAAI;EACpF;AACJ;AAEA;;;;;;;AAOA,OAAM,SAAUqH,WAAWA,CAACxH,IAAsB;;EAC9C,IAAIzB,GAAG,CAACgB,cAAc,CAACS,IAAI,CAAC,EAAE;IAC1B,OAAO,CAAAsH,EAAA,IAAA5D,EAAA,GAAA1D,IAAI,CAACe,IAAI,cAAA2C,EAAA,uBAAAA,EAAA,CAAEvD,IAAI,cAAAmH,EAAA,cAAAA,EAAA,GAAI,QAAQ;EACtC,CAAC,MAAM;IACH,OAAO,CAAAC,EAAA,GAAAZ,mBAAmB,CAAC3G,IAAI,CAAC,cAAAuH,EAAA,cAAAA,EAAA,GAAIvH,IAAI,CAACG,IAAI;EACjD;AACJ;AAEA,OAAM,SAAUkB,aAAaA,CAACD,YAA8B;EACxD,MAAMqG,KAAK,GAAU;IACjBC,CAAC,EAAE,KAAK;IACRxC,CAAC,EAAE,KAAK;IACRyC,CAAC,EAAE;GACN;EACD,MAAMC,MAAM,GAAGC,sBAAsB,CAACzG,YAAY,CAAC4E,UAAU,EAAEyB,KAAK,CAAC;EACrE,MAAMK,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAACP,KAAK,CAAC,CAACnI,MAAM,CAAC,CAAC,GAAGwD,KAAK,CAAC,KAAKA,KAAK,CAAC,CAACmF,GAAG,CAAC,CAAC,CAAC9H,IAAI,CAAC,KAAKA,IAAI,CAAC,CAAC+H,IAAI,CAAC,EAAE,CAAC;EAClG,OAAO,IAAIC,MAAM,CAACP,MAAM,EAAEE,QAAQ,CAAC;AACvC;AAEA;AACA,MAAMM,QAAQ,GAAG,QAAQ,CAACR,MAAM;AAQhC,SAASC,sBAAsBA,CAAC5F,OAA4B,EAAEwF,KAAa;EACvE,IAAIlJ,GAAG,CAAC8J,sBAAsB,CAACpG,OAAO,CAAC,EAAE;IACrC,OAAOqG,2BAA2B,CAACrG,OAAO,CAAC;EAC/C,CAAC,MAAM,IAAI1D,GAAG,CAACgK,eAAe,CAACtG,OAAO,CAAC,EAAE;IACrC,OAAOuG,oBAAoB,CAACvG,OAAO,CAAC;EACxC,CAAC,MAAM,IAAI1D,GAAG,CAACkK,gBAAgB,CAACxG,OAAO,CAAC,EAAE;IACtC,OAAOyG,qBAAqB,CAACzG,OAAO,CAAC;EACzC,CAAC,MAAM,IAAI1D,GAAG,CAACkC,kBAAkB,CAACwB,OAAO,CAAC,EAAE;IACxC,MAAMjC,IAAI,GAAGiC,OAAO,CAACjC,IAAI,CAACW,GAAG;IAC7B,IAAI,CAACX,IAAI,EAAE;MACP,MAAM,IAAIoH,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAOuB,eAAe,CAACd,sBAAsB,CAAC7H,IAAI,CAACgG,UAAU,CAAC,EAAE;MAC5DT,WAAW,EAAEtD,OAAO,CAACsD,WAAW;MAChCqD,SAAS,EAAE3G,OAAO,CAAC2G;KACtB,CAAC;EACN,CAAC,MAAM,IAAIrK,GAAG,CAACsK,cAAc,CAAC5G,OAAO,CAAC,EAAE;IACpC,OAAO6G,kBAAkB,CAAC7G,OAAO,CAAC;EACtC,CAAC,MAAM,IAAI1D,GAAG,CAACwK,YAAY,CAAC9G,OAAO,CAAC,EAAE;IAClC,OAAO+G,iBAAiB,CAAC/G,OAAO,CAAC;EACrC,CAAC,MAAM,IAAI1D,GAAG,CAAC0K,YAAY,CAAChH,OAAO,CAAC,EAAE;IAClC,MAAMiH,SAAS,GAAGjH,OAAO,CAACkH,KAAK,CAACC,WAAW,CAAC,GAAG,CAAC;IAChD,MAAMxB,MAAM,GAAG3F,OAAO,CAACkH,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEH,SAAS,CAAC;IACpD,MAAMI,UAAU,GAAGrH,OAAO,CAACkH,KAAK,CAACE,SAAS,CAACH,SAAS,GAAG,CAAC,CAAC;IACzD,IAAIzB,KAAK,EAAE;MACPA,KAAK,CAACvC,CAAC,GAAGoE,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC;MAClC9B,KAAK,CAACC,CAAC,GAAG4B,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC;MAClC9B,KAAK,CAACE,CAAC,GAAG2B,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC;IACtC;IACA,OAAOZ,eAAe,CAACf,MAAM,EAAE;MAC3BrC,WAAW,EAAEtD,OAAO,CAACsD,WAAW;MAChCqD,SAAS,EAAE3G,OAAO,CAAC2G,SAAS;MAC5BY,IAAI,EAAE;KACT,CAAC;EACN,CAAC,MAAM,IAAIjL,GAAG,CAACkL,UAAU,CAACxH,OAAO,CAAC,EAAE;IAChC,OAAO0G,eAAe,CAACP,QAAQ,EAAE;MAC7B7C,WAAW,EAAEtD,OAAO,CAACsD,WAAW;MAChCqD,SAAS,EAAE3G,OAAO,CAAC2G;KACtB,CAAC;EACN,CAAC,MAAM;IACH,MAAM,IAAIxB,KAAK,CAAC,6BAA6BnF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyH,KAAK,EAAE,CAAC;EAClE;AACJ;AAEA,SAASpB,2BAA2BA,CAACqB,YAAsC;EACvE,OAAOhB,eAAe,CAACgB,YAAY,CAAC3E,QAAQ,CAACiD,GAAG,CAAC/I,CAAC,IAAI2I,sBAAsB,CAAC3I,CAAC,CAAC,CAAC,CAACgJ,IAAI,CAAC,GAAG,CAAC,EAAE;IACxF3C,WAAW,EAAEoE,YAAY,CAACpE,WAAW;IACrCqD,SAAS,EAAEe,YAAY,CAACf;GAC3B,CAAC;AACN;AAEA,SAASJ,oBAAoBA,CAACoB,KAAwB;EAClD,OAAOjB,eAAe,CAACiB,KAAK,CAAC5E,QAAQ,CAACiD,GAAG,CAAC/I,CAAC,IAAI2I,sBAAsB,CAAC3I,CAAC,CAAC,CAAC,CAACgJ,IAAI,CAAC,EAAE,CAAC,EAAE;IAChF3C,WAAW,EAAEqE,KAAK,CAACrE,WAAW;IAC9BqD,SAAS,EAAEgB,KAAK,CAAChB;GACpB,CAAC;AACN;AAEA,SAASI,iBAAiBA,CAACa,KAAqB;EAC5C,OAAOlB,eAAe,CAAC,GAAGP,QAAQ,KAAKP,sBAAsB,CAACgC,KAAK,CAAC/I,QAAQ,CAAC,EAAE,EAAE;IAC7EyE,WAAW,EAAEsE,KAAK,CAACtE,WAAW;IAC9BqD,SAAS,EAAEiB,KAAK,CAACjB;GACpB,CAAC;AACN;AAEA,SAASE,kBAAkBA,CAACgB,MAAwB;EAChD,OAAOnB,eAAe,CAAC,MAAMd,sBAAsB,CAACiC,MAAM,CAAChJ,QAAQ,CAAC,IAAIsH,QAAQ,IAAI,EAAE;IAClF7C,WAAW,EAAEuE,MAAM,CAACvE,WAAW;IAC/BqD,SAAS,EAAEkB,MAAM,CAAClB;GACrB,CAAC;AACN;AAEA,SAASF,qBAAqBA,CAACqB,KAAyB;EACpD,IAAIA,KAAK,CAACC,KAAK,EAAE;IACb,OAAOrB,eAAe,CAAC,IAAIsB,cAAc,CAACF,KAAK,CAACG,IAAI,CAAC,IAAID,cAAc,CAACF,KAAK,CAACC,KAAK,CAAC,GAAG,EAAE;MACrFzE,WAAW,EAAEwE,KAAK,CAACxE,WAAW;MAC9BqD,SAAS,EAAEmB,KAAK,CAACnB,SAAS;MAC1BY,IAAI,EAAE;KACT,CAAC;EACN;EACA,OAAOb,eAAe,CAACsB,cAAc,CAACF,KAAK,CAACG,IAAI,CAAC,EAAE;IAC/C3E,WAAW,EAAEwE,KAAK,CAACxE,WAAW;IAC9BqD,SAAS,EAAEmB,KAAK,CAACnB,SAAS;IAC1BY,IAAI,EAAE;GACT,CAAC;AACN;AAEA,SAASS,cAAcA,CAACvH,OAAoB;EACxC,OAAO9D,YAAY,CAAC8D,OAAO,CAACI,KAAK,CAAC;AACtC;AAEA,SAAS6F,eAAeA,CAACQ,KAAa,EAAEgB,OAIvC;;EACG,IAAIA,OAAO,CAACX,IAAI,KAAK,KAAK,IAAIW,OAAO,CAACvB,SAAS,EAAE;IAC7CO,KAAK,GAAG,IAAI,CAAAzF,EAAA,GAAAyG,OAAO,CAACvB,SAAS,cAAAlF,EAAA,cAAAA,EAAA,GAAI,EAAE,GAAGyF,KAAK,GAAG;EAClD;EACA,IAAIgB,OAAO,CAAC5E,WAAW,EAAE;IACrB,OAAO,GAAG4D,KAAK,GAAGgB,OAAO,CAAC5E,WAAW,EAAE;EAC3C;EACA,OAAO4D,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}