{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { Deferred, interruptAndCheck } from '../utils/promise-utils.js';\nimport { URI, UriUtils } from '../utils/uri-utils.js';\nexport class DefaultWorkspaceManager {\n  constructor(services) {\n    this.initialBuildOptions = {};\n    this._ready = new Deferred();\n    this.serviceRegistry = services.ServiceRegistry;\n    this.langiumDocuments = services.workspace.LangiumDocuments;\n    this.documentBuilder = services.workspace.DocumentBuilder;\n    this.fileSystemProvider = services.workspace.FileSystemProvider;\n    this.mutex = services.workspace.WorkspaceLock;\n  }\n  get ready() {\n    return this._ready.promise;\n  }\n  get workspaceFolders() {\n    return this.folders;\n  }\n  initialize(params) {\n    var _a;\n    this.folders = (_a = params.workspaceFolders) !== null && _a !== void 0 ? _a : undefined;\n  }\n  initialized(_params) {\n    // Initialize the workspace even if there are no workspace folders\n    // We still want to load additional documents (language library or similar) during initialization\n    return this.mutex.write(token => {\n      var _a;\n      return this.initializeWorkspace((_a = this.folders) !== null && _a !== void 0 ? _a : [], token);\n    });\n  }\n  async initializeWorkspace(folders, cancelToken = CancellationToken.None) {\n    const documents = await this.performStartup(folders);\n    // Only after creating all documents do we check whether we need to cancel the initialization\n    // The document builder will later pick up on all unprocessed documents\n    await interruptAndCheck(cancelToken);\n    await this.documentBuilder.build(documents, this.initialBuildOptions, cancelToken);\n  }\n  /**\n   * Performs the uninterruptable startup sequence of the workspace manager.\n   * This methods loads all documents in the workspace and other documents and returns them.\n   */\n  async performStartup(folders) {\n    const fileExtensions = this.serviceRegistry.all.flatMap(e => e.LanguageMetaData.fileExtensions);\n    const documents = [];\n    const collector = document => {\n      documents.push(document);\n      if (!this.langiumDocuments.hasDocument(document.uri)) {\n        this.langiumDocuments.addDocument(document);\n      }\n    };\n    // Even though we don't await the initialization of the workspace manager,\n    // we can still assume that all library documents and file documents are loaded by the time we start building documents.\n    // The mutex prevents anything from performing a workspace build until we check the cancellation token\n    await this.loadAdditionalDocuments(folders, collector);\n    await Promise.all(folders.map(wf => [wf, this.getRootFolder(wf)]).map(async entry => this.traverseFolder(...entry, fileExtensions, collector)));\n    this._ready.resolve();\n    return documents;\n  }\n  /**\n   * Load all additional documents that shall be visible in the context of the given workspace\n   * folders and add them to the collector. This can be used to include built-in libraries of\n   * your language, which can be either loaded from provided files or constructed in memory.\n   */\n  loadAdditionalDocuments(_folders, _collector) {\n    return Promise.resolve();\n  }\n  /**\n   * Determine the root folder of the source documents in the given workspace folder.\n   * The default implementation returns the URI of the workspace folder, but you can override\n   * this to return a subfolder like `src` instead.\n   */\n  getRootFolder(workspaceFolder) {\n    return URI.parse(workspaceFolder.uri);\n  }\n  /**\n   * Traverse the file system folder identified by the given URI and its subfolders. All\n   * contained files that match the file extensions are added to the collector.\n   */\n  async traverseFolder(workspaceFolder, folderPath, fileExtensions, collector) {\n    const content = await this.fileSystemProvider.readDirectory(folderPath);\n    await Promise.all(content.map(async entry => {\n      if (this.includeEntry(workspaceFolder, entry, fileExtensions)) {\n        if (entry.isDirectory) {\n          await this.traverseFolder(workspaceFolder, entry.uri, fileExtensions, collector);\n        } else if (entry.isFile) {\n          const document = await this.langiumDocuments.getOrCreateDocument(entry.uri);\n          collector(document);\n        }\n      }\n    }));\n  }\n  /**\n   * Determine whether the given folder entry shall be included while indexing the workspace.\n   */\n  includeEntry(_workspaceFolder, entry, fileExtensions) {\n    const name = UriUtils.basename(entry.uri);\n    if (name.startsWith('.')) {\n      return false;\n    }\n    if (entry.isDirectory) {\n      return name !== 'node_modules' && name !== 'out';\n    } else if (entry.isFile) {\n      const extname = UriUtils.extname(entry.uri);\n      return fileExtensions.includes(extname);\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "Deferred", "interruptAndCheck", "URI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultWorkspaceManager", "constructor", "services", "initialBuildOptions", "_ready", "serviceRegistry", "ServiceRegistry", "langiumDocuments", "workspace", "LangiumDocuments", "documentBuilder", "DocumentBuilder", "fileSystemProvider", "FileSystemProvider", "mutex", "WorkspaceLock", "ready", "promise", "workspaceFolders", "folders", "initialize", "params", "_a", "undefined", "initialized", "_params", "write", "token", "initializeWorkspace", "cancelToken", "None", "documents", "performStartup", "build", "fileExtensions", "all", "flatMap", "e", "LanguageMetaData", "collector", "document", "push", "hasDocument", "uri", "addDocument", "loadAdditionalDocuments", "Promise", "map", "wf", "getRootFolder", "entry", "traverseFolder", "resolve", "_folders", "_collector", "workspaceFolder", "parse", "folderPath", "content", "readDirectory", "includeEntry", "isDirectory", "isFile", "getOrCreateDocument", "_workspaceFolder", "name", "basename", "startsWith", "extname", "includes"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/workspace-manager.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { InitializeParams, InitializedParams } from 'vscode-languageserver-protocol';\r\nimport type { WorkspaceFolder } from 'vscode-languageserver-types';\r\nimport type { ServiceRegistry } from '../service-registry.js';\r\nimport type { LangiumSharedCoreServices } from '../services.js';\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { Deferred, interruptAndCheck } from '../utils/promise-utils.js';\r\nimport { URI, UriUtils } from '../utils/uri-utils.js';\r\nimport type { BuildOptions, DocumentBuilder } from './document-builder.js';\r\nimport type { LangiumDocument, LangiumDocuments } from './documents.js';\r\nimport type { FileSystemNode, FileSystemProvider } from './file-system-provider.js';\r\nimport type { WorkspaceLock } from './workspace-lock.js';\r\n\r\n// export type WorkspaceFolder from 'vscode-languageserver-types' for convenience,\r\n//  is supposed to avoid confusion as 'WorkspaceFolder' might accidentally be imported via 'vscode-languageclient'\r\nexport type { WorkspaceFolder };\r\n\r\n/**\r\n * The workspace manager is responsible for finding source files in the workspace.\r\n * This service is shared between all languages of a language server.\r\n */\r\nexport interface WorkspaceManager {\r\n\r\n    /** The options used for the initial workspace build. */\r\n    initialBuildOptions: BuildOptions | undefined;\r\n\r\n    /**\r\n     * A promise that resolves when the workspace manager is ready to be used.\r\n     * Use this to ensure that the workspace manager has finished its initialization.\r\n     */\r\n    readonly ready: Promise<void>;\r\n\r\n    /**\r\n     * The workspace folders of the current workspace.\r\n     * Available only after the `ready` promise resolves.\r\n     */\r\n    get workspaceFolders(): readonly WorkspaceFolder[] | undefined;\r\n\r\n    /**\r\n     * When used in a language server context, this method is called when the server receives\r\n     * the `initialize` request.\r\n     */\r\n    initialize(params: InitializeParams): void;\r\n\r\n    /**\r\n     * When used in a language server context, this method is called when the server receives\r\n     * the `initialized` notification.\r\n     */\r\n    initialized(params: InitializedParams): Promise<void>;\r\n\r\n    /**\r\n     * Does the initial indexing of workspace folders.\r\n     * Collects information about exported and referenced AstNodes in\r\n     * each language file and stores it locally.\r\n     *\r\n     * @param folders The set of workspace folders to be indexed.\r\n     * @param cancelToken A cancellation token that can be used to cancel the operation.\r\n     *\r\n     * @throws OperationCancelled if a cancellation event has been detected\r\n     */\r\n    initializeWorkspace(folders: WorkspaceFolder[], cancelToken?: CancellationToken): Promise<void>;\r\n\r\n}\r\n\r\nexport class DefaultWorkspaceManager implements WorkspaceManager {\r\n\r\n    initialBuildOptions: BuildOptions = {};\r\n\r\n    protected readonly serviceRegistry: ServiceRegistry;\r\n    protected readonly langiumDocuments: LangiumDocuments;\r\n    protected readonly documentBuilder: DocumentBuilder;\r\n    protected readonly fileSystemProvider: FileSystemProvider;\r\n    protected readonly mutex: WorkspaceLock;\r\n    protected readonly _ready = new Deferred<void>();\r\n    protected folders?: WorkspaceFolder[];\r\n\r\n    constructor(services: LangiumSharedCoreServices) {\r\n        this.serviceRegistry = services.ServiceRegistry;\r\n        this.langiumDocuments = services.workspace.LangiumDocuments;\r\n        this.documentBuilder = services.workspace.DocumentBuilder;\r\n        this.fileSystemProvider = services.workspace.FileSystemProvider;\r\n        this.mutex = services.workspace.WorkspaceLock;\r\n    }\r\n\r\n    get ready(): Promise<void> {\r\n        return this._ready.promise;\r\n    }\r\n\r\n    get workspaceFolders(): readonly WorkspaceFolder[] | undefined {\r\n        return this.folders;\r\n    }\r\n\r\n    initialize(params: InitializeParams): void {\r\n        this.folders = params.workspaceFolders ?? undefined;\r\n    }\r\n\r\n    initialized(_params: InitializedParams): Promise<void> {\r\n        // Initialize the workspace even if there are no workspace folders\r\n        // We still want to load additional documents (language library or similar) during initialization\r\n        return this.mutex.write(token => this.initializeWorkspace(this.folders ?? [], token));\r\n    }\r\n\r\n    async initializeWorkspace(folders: WorkspaceFolder[], cancelToken = CancellationToken.None): Promise<void> {\r\n        const documents = await this.performStartup(folders);\r\n        // Only after creating all documents do we check whether we need to cancel the initialization\r\n        // The document builder will later pick up on all unprocessed documents\r\n        await interruptAndCheck(cancelToken);\r\n        await this.documentBuilder.build(documents, this.initialBuildOptions, cancelToken);\r\n    }\r\n\r\n    /**\r\n     * Performs the uninterruptable startup sequence of the workspace manager.\r\n     * This methods loads all documents in the workspace and other documents and returns them.\r\n     */\r\n    protected async performStartup(folders: WorkspaceFolder[]): Promise<LangiumDocument[]> {\r\n        const fileExtensions = this.serviceRegistry.all.flatMap(e => e.LanguageMetaData.fileExtensions);\r\n        const documents: LangiumDocument[] = [];\r\n        const collector = (document: LangiumDocument) => {\r\n            documents.push(document);\r\n            if (!this.langiumDocuments.hasDocument(document.uri)) {\r\n                this.langiumDocuments.addDocument(document);\r\n            }\r\n        };\r\n        // Even though we don't await the initialization of the workspace manager,\r\n        // we can still assume that all library documents and file documents are loaded by the time we start building documents.\r\n        // The mutex prevents anything from performing a workspace build until we check the cancellation token\r\n        await this.loadAdditionalDocuments(folders, collector);\r\n        await Promise.all(\r\n            folders.map(wf => [wf, this.getRootFolder(wf)] as [WorkspaceFolder, URI])\r\n                .map(async entry => this.traverseFolder(...entry, fileExtensions, collector))\r\n        );\r\n        this._ready.resolve();\r\n        return documents;\r\n    }\r\n\r\n    /**\r\n     * Load all additional documents that shall be visible in the context of the given workspace\r\n     * folders and add them to the collector. This can be used to include built-in libraries of\r\n     * your language, which can be either loaded from provided files or constructed in memory.\r\n     */\r\n    protected loadAdditionalDocuments(_folders: WorkspaceFolder[], _collector: (document: LangiumDocument) => void): Promise<void> {\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /**\r\n     * Determine the root folder of the source documents in the given workspace folder.\r\n     * The default implementation returns the URI of the workspace folder, but you can override\r\n     * this to return a subfolder like `src` instead.\r\n     */\r\n    protected getRootFolder(workspaceFolder: WorkspaceFolder): URI {\r\n        return URI.parse(workspaceFolder.uri);\r\n    }\r\n\r\n    /**\r\n     * Traverse the file system folder identified by the given URI and its subfolders. All\r\n     * contained files that match the file extensions are added to the collector.\r\n     */\r\n    protected async traverseFolder(workspaceFolder: WorkspaceFolder, folderPath: URI, fileExtensions: string[], collector: (document: LangiumDocument) => void): Promise<void> {\r\n        const content = await this.fileSystemProvider.readDirectory(folderPath);\r\n        await Promise.all(content.map(async entry => {\r\n            if (this.includeEntry(workspaceFolder, entry, fileExtensions)) {\r\n                if (entry.isDirectory) {\r\n                    await this.traverseFolder(workspaceFolder, entry.uri, fileExtensions, collector);\r\n                } else if (entry.isFile) {\r\n                    const document = await this.langiumDocuments.getOrCreateDocument(entry.uri);\r\n                    collector(document);\r\n                }\r\n            }\r\n        }));\r\n    }\r\n\r\n    /**\r\n     * Determine whether the given folder entry shall be included while indexing the workspace.\r\n     */\r\n    protected includeEntry(_workspaceFolder: WorkspaceFolder, entry: FileSystemNode, fileExtensions: string[]): boolean {\r\n        const name = UriUtils.basename(entry.uri);\r\n        if (name.startsWith('.')) {\r\n            return false;\r\n        }\r\n        if (entry.isDirectory) {\r\n            return name !== 'node_modules' && name !== 'out';\r\n        } else if (entry.isFile) {\r\n            const extname = UriUtils.extname(entry.uri);\r\n            return fileExtensions.includes(extname);\r\n        }\r\n        return false;\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAUA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,2BAA2B;AACvE,SAASC,GAAG,EAAEC,QAAQ,QAAQ,uBAAuB;AAyDrD,OAAM,MAAOC,uBAAuB;EAYhCC,YAAYC,QAAmC;IAV/C,KAAAC,mBAAmB,GAAiB,EAAE;IAOnB,KAAAC,MAAM,GAAG,IAAIR,QAAQ,EAAQ;IAI5C,IAAI,CAACS,eAAe,GAAGH,QAAQ,CAACI,eAAe;IAC/C,IAAI,CAACC,gBAAgB,GAAGL,QAAQ,CAACM,SAAS,CAACC,gBAAgB;IAC3D,IAAI,CAACC,eAAe,GAAGR,QAAQ,CAACM,SAAS,CAACG,eAAe;IACzD,IAAI,CAACC,kBAAkB,GAAGV,QAAQ,CAACM,SAAS,CAACK,kBAAkB;IAC/D,IAAI,CAACC,KAAK,GAAGZ,QAAQ,CAACM,SAAS,CAACO,aAAa;EACjD;EAEA,IAAIC,KAAKA,CAAA;IACL,OAAO,IAAI,CAACZ,MAAM,CAACa,OAAO;EAC9B;EAEA,IAAIC,gBAAgBA,CAAA;IAChB,OAAO,IAAI,CAACC,OAAO;EACvB;EAEAC,UAAUA,CAACC,MAAwB;;IAC/B,IAAI,CAACF,OAAO,GAAG,CAAAG,EAAA,GAAAD,MAAM,CAACH,gBAAgB,cAAAI,EAAA,cAAAA,EAAA,GAAIC,SAAS;EACvD;EAEAC,WAAWA,CAACC,OAA0B;IAClC;IACA;IACA,OAAO,IAAI,CAACX,KAAK,CAACY,KAAK,CAACC,KAAK,IAAG;MAAA,IAAAL,EAAA;MAAC,WAAI,CAACM,mBAAmB,CAAC,CAAAN,EAAA,OAAI,CAACH,OAAO,cAAAG,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEK,KAAK,CAAC;IAAA,EAAC;EACzF;EAEA,MAAMC,mBAAmBA,CAACT,OAA0B,EAAEU,WAAW,GAAGlC,iBAAiB,CAACmC,IAAI;IACtF,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACC,cAAc,CAACb,OAAO,CAAC;IACpD;IACA;IACA,MAAMtB,iBAAiB,CAACgC,WAAW,CAAC;IACpC,MAAM,IAAI,CAACnB,eAAe,CAACuB,KAAK,CAACF,SAAS,EAAE,IAAI,CAAC5B,mBAAmB,EAAE0B,WAAW,CAAC;EACtF;EAEA;;;;EAIU,MAAMG,cAAcA,CAACb,OAA0B;IACrD,MAAMe,cAAc,GAAG,IAAI,CAAC7B,eAAe,CAAC8B,GAAG,CAACC,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAACJ,cAAc,CAAC;IAC/F,MAAMH,SAAS,GAAsB,EAAE;IACvC,MAAMQ,SAAS,GAAIC,QAAyB,IAAI;MAC5CT,SAAS,CAACU,IAAI,CAACD,QAAQ,CAAC;MACxB,IAAI,CAAC,IAAI,CAACjC,gBAAgB,CAACmC,WAAW,CAACF,QAAQ,CAACG,GAAG,CAAC,EAAE;QAClD,IAAI,CAACpC,gBAAgB,CAACqC,WAAW,CAACJ,QAAQ,CAAC;MAC/C;IACJ,CAAC;IACD;IACA;IACA;IACA,MAAM,IAAI,CAACK,uBAAuB,CAAC1B,OAAO,EAAEoB,SAAS,CAAC;IACtD,MAAMO,OAAO,CAACX,GAAG,CACbhB,OAAO,CAAC4B,GAAG,CAACC,EAAE,IAAI,CAACA,EAAE,EAAE,IAAI,CAACC,aAAa,CAACD,EAAE,CAAC,CAA2B,CAAC,CACpED,GAAG,CAAC,MAAMG,KAAK,IAAI,IAAI,CAACC,cAAc,CAAC,GAAGD,KAAK,EAAEhB,cAAc,EAAEK,SAAS,CAAC,CAAC,CACpF;IACD,IAAI,CAACnC,MAAM,CAACgD,OAAO,EAAE;IACrB,OAAOrB,SAAS;EACpB;EAEA;;;;;EAKUc,uBAAuBA,CAACQ,QAA2B,EAAEC,UAA+C;IAC1G,OAAOR,OAAO,CAACM,OAAO,EAAE;EAC5B;EAEA;;;;;EAKUH,aAAaA,CAACM,eAAgC;IACpD,OAAOzD,GAAG,CAAC0D,KAAK,CAACD,eAAe,CAACZ,GAAG,CAAC;EACzC;EAEA;;;;EAIU,MAAMQ,cAAcA,CAACI,eAAgC,EAAEE,UAAe,EAAEvB,cAAwB,EAAEK,SAA8C;IACtJ,MAAMmB,OAAO,GAAG,MAAM,IAAI,CAAC9C,kBAAkB,CAAC+C,aAAa,CAACF,UAAU,CAAC;IACvE,MAAMX,OAAO,CAACX,GAAG,CAACuB,OAAO,CAACX,GAAG,CAAC,MAAMG,KAAK,IAAG;MACxC,IAAI,IAAI,CAACU,YAAY,CAACL,eAAe,EAAEL,KAAK,EAAEhB,cAAc,CAAC,EAAE;QAC3D,IAAIgB,KAAK,CAACW,WAAW,EAAE;UACnB,MAAM,IAAI,CAACV,cAAc,CAACI,eAAe,EAAEL,KAAK,CAACP,GAAG,EAAET,cAAc,EAAEK,SAAS,CAAC;QACpF,CAAC,MAAM,IAAIW,KAAK,CAACY,MAAM,EAAE;UACrB,MAAMtB,QAAQ,GAAG,MAAM,IAAI,CAACjC,gBAAgB,CAACwD,mBAAmB,CAACb,KAAK,CAACP,GAAG,CAAC;UAC3EJ,SAAS,CAACC,QAAQ,CAAC;QACvB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EAEA;;;EAGUoB,YAAYA,CAACI,gBAAiC,EAAEd,KAAqB,EAAEhB,cAAwB;IACrG,MAAM+B,IAAI,GAAGlE,QAAQ,CAACmE,QAAQ,CAAChB,KAAK,CAACP,GAAG,CAAC;IACzC,IAAIsB,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAIjB,KAAK,CAACW,WAAW,EAAE;MACnB,OAAOI,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,KAAK;IACpD,CAAC,MAAM,IAAIf,KAAK,CAACY,MAAM,EAAE;MACrB,MAAMM,OAAO,GAAGrE,QAAQ,CAACqE,OAAO,CAAClB,KAAK,CAACP,GAAG,CAAC;MAC3C,OAAOT,cAAc,CAACmC,QAAQ,CAACD,OAAO,CAAC;IAC3C;IACA,OAAO,KAAK;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}