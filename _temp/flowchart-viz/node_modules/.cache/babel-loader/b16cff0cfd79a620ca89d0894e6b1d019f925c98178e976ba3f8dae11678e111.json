{"ast": null, "code": "import { tweenValue } from \"./tween.js\";\nfunction textConstant(value) {\n  return function () {\n    this.textContent = value;\n  };\n}\nfunction textFunction(value) {\n  return function () {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\nexport default function (value) {\n  return this.tween(\"text\", typeof value === \"function\" ? textFunction(tweenValue(this, \"text\", value)) : textConstant(value == null ? \"\" : value + \"\"));\n}", "map": {"version": 3, "names": ["tweenValue", "textConstant", "value", "textContent", "textFunction", "value1", "tween"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-transition/src/transition/text.js"], "sourcesContent": ["import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,YAAY;AAErC,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAI,CAACC,WAAW,GAAGD,KAAK;EAC1B,CAAC;AACH;AAEA,SAASE,YAAYA,CAACF,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAIG,MAAM,GAAGH,KAAK,CAAC,IAAI,CAAC;IACxB,IAAI,CAACC,WAAW,GAAGE,MAAM,IAAI,IAAI,GAAG,EAAE,GAAGA,MAAM;EACjD,CAAC;AACH;AAEA,eAAe,UAASH,KAAK,EAAE;EAC7B,OAAO,IAAI,CAACI,KAAK,CAAC,MAAM,EAAE,OAAOJ,KAAK,KAAK,UAAU,GAC/CE,YAAY,CAACJ,UAAU,CAAC,IAAI,EAAE,MAAM,EAAEE,KAAK,CAAC,CAAC,GAC7CD,YAAY,CAACC,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}