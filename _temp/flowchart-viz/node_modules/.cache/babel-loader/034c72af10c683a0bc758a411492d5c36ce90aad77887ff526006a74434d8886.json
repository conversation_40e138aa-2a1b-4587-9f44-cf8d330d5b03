{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport map from \"lodash-es/map.js\";\nimport filter from \"lodash-es/filter.js\";\nimport { Alternation, NonTerminal, Option, RepetitionMandatory, Repetition, Terminal, RepetitionWithSeparator, RepetitionMandatoryWithSeparator } from \"chevrotain\";\nexport function buildATNKey(rule, type, occurrence) {\n  return `${rule.name}_${type}_${occurrence}`;\n}\nexport const ATN_INVALID_TYPE = 0;\nexport const ATN_BASIC = 1;\nexport const ATN_RULE_START = 2;\nexport const ATN_PLUS_BLOCK_START = 4;\nexport const ATN_STAR_BLOCK_START = 5;\n// Currently unused as the ATN is not used for lexing\nexport const ATN_TOKEN_START = 6;\nexport const ATN_RULE_STOP = 7;\nexport const ATN_BLOCK_END = 8;\nexport const ATN_STAR_LOOP_BACK = 9;\nexport const ATN_STAR_LOOP_ENTRY = 10;\nexport const ATN_PLUS_LOOP_BACK = 11;\nexport const ATN_LOOP_END = 12;\nexport class AbstractTransition {\n  constructor(target) {\n    this.target = target;\n  }\n  isEpsilon() {\n    return false;\n  }\n}\nexport class AtomTransition extends AbstractTransition {\n  constructor(target, tokenType) {\n    super(target);\n    this.tokenType = tokenType;\n  }\n}\nexport class EpsilonTransition extends AbstractTransition {\n  constructor(target) {\n    super(target);\n  }\n  isEpsilon() {\n    return true;\n  }\n}\nexport class RuleTransition extends AbstractTransition {\n  constructor(ruleStart, rule, followState) {\n    super(ruleStart);\n    this.rule = rule;\n    this.followState = followState;\n  }\n  isEpsilon() {\n    return true;\n  }\n}\nexport function createATN(rules) {\n  const atn = {\n    decisionMap: {},\n    decisionStates: [],\n    ruleToStartState: new Map(),\n    ruleToStopState: new Map(),\n    states: []\n  };\n  createRuleStartAndStopATNStates(atn, rules);\n  const ruleLength = rules.length;\n  for (let i = 0; i < ruleLength; i++) {\n    const rule = rules[i];\n    const ruleBlock = block(atn, rule, rule);\n    if (ruleBlock === undefined) {\n      continue;\n    }\n    buildRuleHandle(atn, rule, ruleBlock);\n  }\n  return atn;\n}\nfunction createRuleStartAndStopATNStates(atn, rules) {\n  const ruleLength = rules.length;\n  for (let i = 0; i < ruleLength; i++) {\n    const rule = rules[i];\n    const start = newState(atn, rule, undefined, {\n      type: ATN_RULE_START\n    });\n    const stop = newState(atn, rule, undefined, {\n      type: ATN_RULE_STOP\n    });\n    start.stop = stop;\n    atn.ruleToStartState.set(rule, start);\n    atn.ruleToStopState.set(rule, stop);\n  }\n}\nfunction atom(atn, rule, production) {\n  if (production instanceof Terminal) {\n    return tokenRef(atn, rule, production.terminalType, production);\n  } else if (production instanceof NonTerminal) {\n    return ruleRef(atn, rule, production);\n  } else if (production instanceof Alternation) {\n    return alternation(atn, rule, production);\n  } else if (production instanceof Option) {\n    return option(atn, rule, production);\n  } else if (production instanceof Repetition) {\n    return repetition(atn, rule, production);\n  } else if (production instanceof RepetitionWithSeparator) {\n    return repetitionSep(atn, rule, production);\n  } else if (production instanceof RepetitionMandatory) {\n    return repetitionMandatory(atn, rule, production);\n  } else if (production instanceof RepetitionMandatoryWithSeparator) {\n    return repetitionMandatorySep(atn, rule, production);\n  } else {\n    return block(atn, rule, production);\n  }\n}\nfunction repetition(atn, rule, repetition) {\n  const starState = newState(atn, rule, repetition, {\n    type: ATN_STAR_BLOCK_START\n  });\n  defineDecisionState(atn, starState);\n  const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n  return star(atn, rule, repetition, handle);\n}\nfunction repetitionSep(atn, rule, repetition) {\n  const starState = newState(atn, rule, repetition, {\n    type: ATN_STAR_BLOCK_START\n  });\n  defineDecisionState(atn, starState);\n  const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n  const sep = tokenRef(atn, rule, repetition.separator, repetition);\n  return star(atn, rule, repetition, handle, sep);\n}\nfunction repetitionMandatory(atn, rule, repetition) {\n  const plusState = newState(atn, rule, repetition, {\n    type: ATN_PLUS_BLOCK_START\n  });\n  defineDecisionState(atn, plusState);\n  const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n  return plus(atn, rule, repetition, handle);\n}\nfunction repetitionMandatorySep(atn, rule, repetition) {\n  const plusState = newState(atn, rule, repetition, {\n    type: ATN_PLUS_BLOCK_START\n  });\n  defineDecisionState(atn, plusState);\n  const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n  const sep = tokenRef(atn, rule, repetition.separator, repetition);\n  return plus(atn, rule, repetition, handle, sep);\n}\nfunction alternation(atn, rule, alternation) {\n  const start = newState(atn, rule, alternation, {\n    type: ATN_BASIC\n  });\n  defineDecisionState(atn, start);\n  const alts = map(alternation.definition, e => atom(atn, rule, e));\n  const handle = makeAlts(atn, rule, start, alternation, ...alts);\n  return handle;\n}\nfunction option(atn, rule, option) {\n  const start = newState(atn, rule, option, {\n    type: ATN_BASIC\n  });\n  defineDecisionState(atn, start);\n  const handle = makeAlts(atn, rule, start, option, block(atn, rule, option));\n  return optional(atn, rule, option, handle);\n}\nfunction block(atn, rule, block) {\n  const handles = filter(map(block.definition, e => atom(atn, rule, e)), e => e !== undefined);\n  if (handles.length === 1) {\n    return handles[0];\n  } else if (handles.length === 0) {\n    return undefined;\n  } else {\n    return makeBlock(atn, handles);\n  }\n}\nfunction plus(atn, rule, plus, handle, sep) {\n  const blkStart = handle.left;\n  const blkEnd = handle.right;\n  const loop = newState(atn, rule, plus, {\n    type: ATN_PLUS_LOOP_BACK\n  });\n  defineDecisionState(atn, loop);\n  const end = newState(atn, rule, plus, {\n    type: ATN_LOOP_END\n  });\n  blkStart.loopback = loop;\n  end.loopback = loop;\n  atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionMandatoryWithSeparator' : 'RepetitionMandatory', plus.idx)] = loop;\n  epsilon(blkEnd, loop); // block can see loop back\n  // Depending on whether we have a separator we put the exit transition at index 1 or 0\n  // This influences the chosen option in the lookahead DFA\n  if (sep === undefined) {\n    epsilon(loop, blkStart); // loop back to start\n    epsilon(loop, end); // exit\n  } else {\n    epsilon(loop, end); // exit\n    // loop back to start with separator\n    epsilon(loop, sep.left);\n    epsilon(sep.right, blkStart);\n  }\n  return {\n    left: blkStart,\n    right: end\n  };\n}\nfunction star(atn, rule, star, handle, sep) {\n  const start = handle.left;\n  const end = handle.right;\n  const entry = newState(atn, rule, star, {\n    type: ATN_STAR_LOOP_ENTRY\n  });\n  defineDecisionState(atn, entry);\n  const loopEnd = newState(atn, rule, star, {\n    type: ATN_LOOP_END\n  });\n  const loop = newState(atn, rule, star, {\n    type: ATN_STAR_LOOP_BACK\n  });\n  entry.loopback = loop;\n  loopEnd.loopback = loop;\n  epsilon(entry, start); // loop enter edge (alt 2)\n  epsilon(entry, loopEnd); // bypass loop edge (alt 1)\n  epsilon(end, loop); // block end hits loop back\n  if (sep !== undefined) {\n    epsilon(loop, loopEnd); // end loop\n    // loop back to start of handle using separator\n    epsilon(loop, sep.left);\n    epsilon(sep.right, start);\n  } else {\n    epsilon(loop, entry); // loop back to entry/exit decision\n  }\n  atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionWithSeparator' : 'Repetition', star.idx)] = entry;\n  return {\n    left: entry,\n    right: loopEnd\n  };\n}\nfunction optional(atn, rule, optional, handle) {\n  const start = handle.left;\n  const end = handle.right;\n  epsilon(start, end);\n  atn.decisionMap[buildATNKey(rule, 'Option', optional.idx)] = start;\n  return handle;\n}\nfunction defineDecisionState(atn, state) {\n  atn.decisionStates.push(state);\n  state.decision = atn.decisionStates.length - 1;\n  return state.decision;\n}\nfunction makeAlts(atn, rule, start, production, ...alts) {\n  const end = newState(atn, rule, production, {\n    type: ATN_BLOCK_END,\n    start\n  });\n  start.end = end;\n  for (const alt of alts) {\n    if (alt !== undefined) {\n      // hook alts up to decision block\n      epsilon(start, alt.left);\n      epsilon(alt.right, end);\n    } else {\n      epsilon(start, end);\n    }\n  }\n  const handle = {\n    left: start,\n    right: end\n  };\n  atn.decisionMap[buildATNKey(rule, getProdType(production), production.idx)] = start;\n  return handle;\n}\nfunction getProdType(production) {\n  if (production instanceof Alternation) {\n    return 'Alternation';\n  } else if (production instanceof Option) {\n    return 'Option';\n  } else if (production instanceof Repetition) {\n    return 'Repetition';\n  } else if (production instanceof RepetitionWithSeparator) {\n    return 'RepetitionWithSeparator';\n  } else if (production instanceof RepetitionMandatory) {\n    return 'RepetitionMandatory';\n  } else if (production instanceof RepetitionMandatoryWithSeparator) {\n    return 'RepetitionMandatoryWithSeparator';\n  } else {\n    throw new Error('Invalid production type encountered');\n  }\n}\nfunction makeBlock(atn, alts) {\n  const altsLength = alts.length;\n  for (let i = 0; i < altsLength - 1; i++) {\n    const handle = alts[i];\n    let transition;\n    if (handle.left.transitions.length === 1) {\n      transition = handle.left.transitions[0];\n    }\n    const isRuleTransition = transition instanceof RuleTransition;\n    const ruleTransition = transition;\n    const next = alts[i + 1].left;\n    if (handle.left.type === ATN_BASIC && handle.right.type === ATN_BASIC && transition !== undefined && (isRuleTransition && ruleTransition.followState === handle.right || transition.target === handle.right)) {\n      // we can avoid epsilon edge to next element\n      if (isRuleTransition) {\n        ruleTransition.followState = next;\n      } else {\n        transition.target = next;\n      }\n      removeState(atn, handle.right); // we skipped over this state\n    } else {\n      // need epsilon if previous block's right end node is complex\n      epsilon(handle.right, next);\n    }\n  }\n  const first = alts[0];\n  const last = alts[altsLength - 1];\n  return {\n    left: first.left,\n    right: last.right\n  };\n}\nfunction tokenRef(atn, rule, tokenType, production) {\n  const left = newState(atn, rule, production, {\n    type: ATN_BASIC\n  });\n  const right = newState(atn, rule, production, {\n    type: ATN_BASIC\n  });\n  addTransition(left, new AtomTransition(right, tokenType));\n  return {\n    left,\n    right\n  };\n}\nfunction ruleRef(atn, currentRule, nonTerminal) {\n  const rule = nonTerminal.referencedRule;\n  const start = atn.ruleToStartState.get(rule);\n  const left = newState(atn, currentRule, nonTerminal, {\n    type: ATN_BASIC\n  });\n  const right = newState(atn, currentRule, nonTerminal, {\n    type: ATN_BASIC\n  });\n  const call = new RuleTransition(start, rule, right);\n  addTransition(left, call);\n  return {\n    left,\n    right\n  };\n}\nfunction buildRuleHandle(atn, rule, block) {\n  const start = atn.ruleToStartState.get(rule);\n  epsilon(start, block.left);\n  const stop = atn.ruleToStopState.get(rule);\n  epsilon(block.right, stop);\n  const handle = {\n    left: start,\n    right: stop\n  };\n  return handle;\n}\nfunction epsilon(a, b) {\n  const transition = new EpsilonTransition(b);\n  addTransition(a, transition);\n}\nfunction newState(atn, rule, production, partial) {\n  const t = Object.assign({\n    atn,\n    production,\n    epsilonOnlyTransitions: false,\n    rule,\n    transitions: [],\n    nextTokenWithinRule: [],\n    stateNumber: atn.states.length\n  }, partial);\n  atn.states.push(t);\n  return t;\n}\nfunction addTransition(state, transition) {\n  // A single ATN state can only contain epsilon transitions or non-epsilon transitions\n  // Because they are never mixed, only setting the property for the first transition is fine\n  if (state.transitions.length === 0) {\n    state.epsilonOnlyTransitions = transition.isEpsilon();\n  }\n  state.transitions.push(transition);\n}\nfunction removeState(atn, state) {\n  atn.states.splice(atn.states.indexOf(state), 1);\n}", "map": {"version": 3, "names": ["map", "filter", "Alternation", "NonTerminal", "Option", "RepetitionMandatory", "Repetition", "Terminal", "RepetitionWithSeparator", "RepetitionMandatoryWithSeparator", "buildATNKey", "rule", "type", "occurrence", "name", "ATN_INVALID_TYPE", "ATN_BASIC", "ATN_RULE_START", "ATN_PLUS_BLOCK_START", "ATN_STAR_BLOCK_START", "ATN_TOKEN_START", "ATN_RULE_STOP", "ATN_BLOCK_END", "ATN_STAR_LOOP_BACK", "ATN_STAR_LOOP_ENTRY", "ATN_PLUS_LOOP_BACK", "ATN_LOOP_END", "AbstractTransition", "constructor", "target", "isEpsilon", "AtomTransition", "tokenType", "EpsilonTransition", "RuleTransition", "ruleStart", "followState", "createATN", "rules", "atn", "decisionMap", "decisionStates", "ruleToStartState", "Map", "ruleToStopState", "states", "createRuleStartAndStopATNStates", "rule<PERSON><PERSON><PERSON>", "length", "i", "ruleBlock", "block", "undefined", "buildRuleHandle", "start", "newState", "stop", "set", "atom", "production", "tokenRef", "terminalType", "ruleRef", "alternation", "option", "repetition", "repetitionSep", "repetitionMandatory", "repetitionMandatorySep", "starState", "defineDecisionState", "handle", "makeAlts", "star", "sep", "separator", "plusState", "plus", "alts", "definition", "e", "optional", "handles", "makeBlock", "blkStart", "left", "blkEnd", "right", "loop", "end", "loopback", "idx", "epsilon", "entry", "loopEnd", "state", "push", "decision", "alt", "getProdType", "Error", "altsLength", "transition", "transitions", "isRuleTransition", "ruleTransition", "next", "removeState", "first", "last", "addTransition", "currentRule", "nonTerminal", "referencedRule", "get", "call", "a", "b", "partial", "t", "Object", "assign", "epsilonOnlyTransitions", "nextTokenWithinRule", "stateNumber", "splice", "indexOf"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain-allstar/src/atn.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport map from \"lodash-es/map.js\"\r\nimport filter from \"lodash-es/filter.js\"\r\nimport {\r\n    IProduction,\r\n    IProductionWithOccurrence,\r\n    TokenType,\r\n    Alternation,\r\n    NonTerminal,\r\n    Rule,\r\n    Option,\r\n    RepetitionMandatory,\r\n    Repetition,\r\n    Terminal,\r\n    Alternative,\r\n    RepetitionWithSeparator,\r\n    RepetitionMandatoryWithSeparator,\r\n    LookaheadProductionType\r\n} from \"chevrotain\"\r\n\r\nexport function buildATNKey(rule: Rule, type: LookaheadProductionType, occurrence: number): string {\r\n    return `${rule.name}_${type}_${occurrence}`;\r\n}\r\n\r\nexport interface ATN {\r\n    decisionMap: Record<string, DecisionState>\r\n    states: ATNState[]\r\n    decisionStates: DecisionState[]\r\n    ruleToStartState: Map<Rule, RuleStartState>\r\n    ruleToStopState: Map<Rule, RuleStopState>\r\n}\r\n\r\nexport const ATN_INVALID_TYPE = 0\r\nexport const ATN_BASIC = 1\r\nexport const ATN_RULE_START = 2\r\nexport const ATN_PLUS_BLOCK_START = 4\r\nexport const ATN_STAR_BLOCK_START = 5\r\n// Currently unused as the ATN is not used for lexing\r\nexport const ATN_TOKEN_START = 6\r\nexport const ATN_RULE_STOP = 7\r\nexport const ATN_BLOCK_END = 8\r\nexport const ATN_STAR_LOOP_BACK = 9\r\nexport const ATN_STAR_LOOP_ENTRY = 10\r\nexport const ATN_PLUS_LOOP_BACK = 11\r\nexport const ATN_LOOP_END = 12\r\n\r\nexport type ATNState =\r\n    | BasicState\r\n    | BasicBlockStartState\r\n    | PlusBlockStartState\r\n    | PlusLoopbackState\r\n    | StarBlockStartState\r\n    | StarLoopbackState\r\n    | StarLoopEntryState\r\n    | BlockEndState\r\n    | RuleStartState\r\n    | RuleStopState\r\n    | LoopEndState\r\n\r\nexport interface ATNBaseState {\r\n    atn: ATN\r\n    production: IProductionWithOccurrence\r\n    stateNumber: number\r\n    rule: Rule\r\n    epsilonOnlyTransitions: boolean\r\n    transitions: Transition[]\r\n    nextTokenWithinRule: number[]\r\n}\r\n\r\nexport interface BasicState extends ATNBaseState {\r\n    type: typeof ATN_BASIC\r\n}\r\n\r\nexport interface BlockStartState extends DecisionState {\r\n    end: BlockEndState\r\n}\r\n\r\nexport interface BasicBlockStartState extends BlockStartState {\r\n    type: typeof ATN_BASIC\r\n}\r\n\r\nexport interface PlusBlockStartState extends BlockStartState {\r\n    loopback: PlusLoopbackState\r\n    type: typeof ATN_PLUS_BLOCK_START\r\n}\r\n\r\nexport interface PlusLoopbackState extends DecisionState {\r\n    type: typeof ATN_PLUS_LOOP_BACK\r\n}\r\n\r\nexport interface StarBlockStartState extends BlockStartState {\r\n    type: typeof ATN_STAR_BLOCK_START\r\n}\r\n\r\nexport interface StarLoopbackState extends ATNBaseState {\r\n    type: typeof ATN_STAR_LOOP_BACK\r\n}\r\n\r\nexport interface StarLoopEntryState extends DecisionState {\r\n    loopback: StarLoopbackState\r\n    type: typeof ATN_STAR_LOOP_ENTRY\r\n}\r\n\r\nexport interface BlockEndState extends ATNBaseState {\r\n    start: BlockStartState\r\n    type: typeof ATN_BLOCK_END\r\n}\r\n\r\nexport interface DecisionState extends ATNBaseState {\r\n    decision: number\r\n}\r\n\r\nexport interface LoopEndState extends ATNBaseState {\r\n    loopback: ATNState\r\n    type: typeof ATN_LOOP_END\r\n}\r\n\r\nexport interface RuleStartState extends ATNBaseState {\r\n    stop: RuleStopState\r\n    type: typeof ATN_RULE_START\r\n}\r\n\r\nexport interface RuleStopState extends ATNBaseState {\r\n    type: typeof ATN_RULE_STOP\r\n}\r\n\r\nexport interface Transition {\r\n    target: ATNState\r\n    isEpsilon(): boolean\r\n}\r\n\r\nexport abstract class AbstractTransition implements Transition {\r\n    target: ATNState\r\n\r\n    constructor(target: ATNState) {\r\n        this.target = target\r\n    }\r\n\r\n    isEpsilon() {\r\n        return false\r\n    }\r\n}\r\n\r\nexport class AtomTransition extends AbstractTransition {\r\n    tokenType: TokenType\r\n\r\n    constructor(target: ATNState, tokenType: TokenType) {\r\n        super(target)\r\n        this.tokenType = tokenType\r\n    }\r\n}\r\n\r\nexport class EpsilonTransition extends AbstractTransition {\r\n    constructor(target: ATNState) {\r\n        super(target)\r\n    }\r\n\r\n    isEpsilon() {\r\n        return true\r\n    }\r\n}\r\n\r\nexport class RuleTransition extends AbstractTransition {\r\n    rule: Rule\r\n    followState: ATNState\r\n\r\n    constructor(ruleStart: RuleStartState, rule: Rule, followState: ATNState) {\r\n        super(ruleStart)\r\n        this.rule = rule\r\n        this.followState = followState\r\n    }\r\n\r\n    isEpsilon() {\r\n        return true\r\n    }\r\n}\r\n\r\ninterface ATNHandle {\r\n    left: ATNState\r\n    right: ATNState\r\n}\r\n\r\nexport function createATN(rules: Rule[]): ATN {\r\n    const atn: ATN = {\r\n        decisionMap: {},\r\n        decisionStates: [],\r\n        ruleToStartState: new Map(),\r\n        ruleToStopState: new Map(),\r\n        states: []\r\n    }\r\n    createRuleStartAndStopATNStates(atn, rules)\r\n    const ruleLength = rules.length\r\n    for (let i = 0; i < ruleLength; i++) {\r\n        const rule = rules[i]\r\n        const ruleBlock = block(atn, rule, rule)\r\n        if (ruleBlock === undefined) {\r\n            continue\r\n        }\r\n        buildRuleHandle(atn, rule, ruleBlock)\r\n    }\r\n    return atn\r\n}\r\n\r\nfunction createRuleStartAndStopATNStates(atn: ATN, rules: Rule[]): void {\r\n    const ruleLength = rules.length\r\n    for (let i = 0; i < ruleLength; i++) {\r\n        const rule = rules[i]\r\n        const start = newState<RuleStartState>(atn, rule, undefined, {\r\n            type: ATN_RULE_START\r\n        })\r\n        const stop = newState<RuleStopState>(atn, rule, undefined, {\r\n            type: ATN_RULE_STOP\r\n        })\r\n        start.stop = stop\r\n        atn.ruleToStartState.set(rule, start)\r\n        atn.ruleToStopState.set(rule, stop)\r\n    }\r\n}\r\n\r\nfunction atom(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    production: IProduction\r\n): ATNHandle | undefined {\r\n    if (production instanceof Terminal) {\r\n        return tokenRef(atn, rule, production.terminalType, production)\r\n    } else if (production instanceof NonTerminal) {\r\n        return ruleRef(atn, rule, production)\r\n    } else if (production instanceof Alternation) {\r\n        return alternation(atn, rule, production)\r\n    } else if (production instanceof Option) {\r\n        return option(atn, rule, production)\r\n    } else if (production instanceof Repetition) {\r\n        return repetition(atn, rule, production)\r\n    } else if (production instanceof RepetitionWithSeparator) {\r\n        return repetitionSep(atn, rule, production)\r\n    } else if (production instanceof RepetitionMandatory) {\r\n        return repetitionMandatory(atn, rule, production)\r\n    } else if (production instanceof RepetitionMandatoryWithSeparator) {\r\n        return repetitionMandatorySep(atn, rule, production)\r\n    } else {\r\n        return block(atn, rule, production as Alternative)\r\n    }\r\n}\r\n\r\nfunction repetition(atn: ATN, rule: Rule, repetition: Repetition): ATNHandle {\r\n    const starState = newState<StarBlockStartState>(atn, rule, repetition, {\r\n        type: ATN_STAR_BLOCK_START\r\n    })\r\n    defineDecisionState(atn, starState)\r\n    const handle = makeAlts(\r\n        atn,\r\n        rule,\r\n        starState,\r\n        repetition,\r\n        block(atn, rule, repetition)\r\n    )\r\n    return star(atn, rule, repetition, handle)\r\n}\r\n\r\nfunction repetitionSep(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    repetition: RepetitionWithSeparator\r\n): ATNHandle {\r\n    const starState = newState<StarBlockStartState>(atn, rule, repetition, {\r\n        type: ATN_STAR_BLOCK_START\r\n    })\r\n    defineDecisionState(atn, starState)\r\n    const handle = makeAlts(\r\n        atn,\r\n        rule,\r\n        starState,\r\n        repetition,\r\n        block(atn, rule, repetition)\r\n    )\r\n    const sep = tokenRef(atn, rule, repetition.separator, repetition)\r\n    return star(atn, rule, repetition, handle, sep)\r\n}\r\n\r\nfunction repetitionMandatory(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    repetition: RepetitionMandatory\r\n): ATNHandle {\r\n    const plusState = newState<PlusBlockStartState>(atn, rule, repetition, {\r\n        type: ATN_PLUS_BLOCK_START\r\n    })\r\n    defineDecisionState(atn, plusState)\r\n    const handle = makeAlts(\r\n        atn,\r\n        rule,\r\n        plusState,\r\n        repetition,\r\n        block(atn, rule, repetition)\r\n    )\r\n    return plus(atn, rule, repetition, handle)\r\n}\r\n\r\nfunction repetitionMandatorySep(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    repetition: RepetitionMandatoryWithSeparator\r\n): ATNHandle {\r\n    const plusState = newState<PlusBlockStartState>(atn, rule, repetition, {\r\n        type: ATN_PLUS_BLOCK_START\r\n    })\r\n    defineDecisionState(atn, plusState)\r\n    const handle = makeAlts(\r\n        atn,\r\n        rule,\r\n        plusState,\r\n        repetition,\r\n        block(atn, rule, repetition)\r\n    )\r\n    const sep = tokenRef(atn, rule, repetition.separator, repetition)\r\n    return plus(atn, rule, repetition, handle, sep)\r\n}\r\n\r\nfunction alternation(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    alternation: Alternation\r\n): ATNHandle {\r\n    const start = newState<BasicBlockStartState>(atn, rule, alternation, {\r\n        type: ATN_BASIC\r\n    })\r\n    defineDecisionState(atn, start)\r\n    const alts = map(alternation.definition, (e) => atom(atn, rule, e))\r\n    const handle = makeAlts(atn, rule, start, alternation, ...alts)\r\n    return handle\r\n}\r\n\r\nfunction option(atn: ATN, rule: Rule, option: Option): ATNHandle {\r\n    const start = newState<BasicBlockStartState>(atn, rule, option, {\r\n        type: ATN_BASIC\r\n    })\r\n    defineDecisionState(atn, start)\r\n    const handle = makeAlts(atn, rule, start, option, block(atn, rule, option))\r\n    return optional(atn, rule, option, handle)\r\n}\r\n\r\nfunction block(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    block: { definition: IProduction[] }\r\n): ATNHandle | undefined {\r\n    const handles = filter(\r\n        map(block.definition, (e) => atom(atn, rule, e)),\r\n        (e) => e !== undefined\r\n    ) as ATNHandle[]\r\n    if (handles.length === 1) {\r\n        return handles[0]\r\n    } else if (handles.length === 0) {\r\n        return undefined\r\n    } else {\r\n        return makeBlock(atn, handles)\r\n    }\r\n}\r\n\r\nfunction plus(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    plus: IProductionWithOccurrence,\r\n    handle: ATNHandle,\r\n    sep?: ATNHandle\r\n): ATNHandle {\r\n    const blkStart = handle.left as PlusBlockStartState\r\n    const blkEnd = handle.right\r\n\r\n    const loop = newState<PlusLoopbackState>(atn, rule, plus, {\r\n        type: ATN_PLUS_LOOP_BACK\r\n    })\r\n    defineDecisionState(atn, loop)\r\n    const end = newState<LoopEndState>(atn, rule, plus, {\r\n        type: ATN_LOOP_END\r\n    })\r\n    blkStart.loopback = loop\r\n    end.loopback = loop\r\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionMandatoryWithSeparator' : 'RepetitionMandatory', plus.idx)] = loop;\r\n    epsilon(blkEnd, loop) // block can see loop back\r\n\r\n    // Depending on whether we have a separator we put the exit transition at index 1 or 0\r\n    // This influences the chosen option in the lookahead DFA\r\n    if (sep === undefined) {\r\n        epsilon(loop, blkStart) // loop back to start\r\n        epsilon(loop, end) // exit\r\n    } else {\r\n        epsilon(loop, end) // exit\r\n        // loop back to start with separator\r\n        epsilon(loop, sep.left)\r\n        epsilon(sep.right, blkStart)\r\n    }\r\n\r\n    return {\r\n        left: blkStart,\r\n        right: end\r\n    }\r\n}\r\n\r\nfunction star(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    star: IProductionWithOccurrence,\r\n    handle: ATNHandle,\r\n    sep?: ATNHandle\r\n): ATNHandle {\r\n    const start = handle.left\r\n    const end = handle.right\r\n\r\n    const entry = newState<StarLoopEntryState>(atn, rule, star, {\r\n        type: ATN_STAR_LOOP_ENTRY\r\n    })\r\n    defineDecisionState(atn, entry)\r\n    const loopEnd = newState<LoopEndState>(atn, rule, star, {\r\n        type: ATN_LOOP_END\r\n    })\r\n    const loop = newState<StarLoopbackState>(atn, rule, star, {\r\n        type: ATN_STAR_LOOP_BACK\r\n    })\r\n    entry.loopback = loop\r\n    loopEnd.loopback = loop\r\n\r\n    epsilon(entry, start) // loop enter edge (alt 2)\r\n    epsilon(entry, loopEnd) // bypass loop edge (alt 1)\r\n    epsilon(end, loop) // block end hits loop back\r\n\r\n    if (sep !== undefined) {\r\n        epsilon(loop, loopEnd) // end loop\r\n        // loop back to start of handle using separator\r\n        epsilon(loop, sep.left)\r\n        epsilon(sep.right, start)\r\n    } else {\r\n        epsilon(loop, entry) // loop back to entry/exit decision\r\n    }\r\n\r\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionWithSeparator' : 'Repetition', star.idx)] = entry;\r\n    return {\r\n        left: entry,\r\n        right: loopEnd\r\n    }\r\n}\r\n\r\nfunction optional(atn: ATN, rule: Rule, optional: Option, handle: ATNHandle): ATNHandle {\r\n    const start = handle.left as DecisionState\r\n    const end = handle.right\r\n\r\n    epsilon(start, end)\r\n\r\n    atn.decisionMap[buildATNKey(rule, 'Option', optional.idx)] = start;\r\n    return handle\r\n}\r\n\r\nfunction defineDecisionState(atn: ATN, state: DecisionState): number {\r\n    atn.decisionStates.push(state)\r\n    state.decision = atn.decisionStates.length - 1\r\n    return state.decision\r\n}\r\n\r\nfunction makeAlts(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    start: BlockStartState,\r\n    production: IProductionWithOccurrence,\r\n    ...alts: (ATNHandle | undefined)[]\r\n): ATNHandle {\r\n    const end = newState<BlockEndState>(atn, rule, production, {\r\n        type: ATN_BLOCK_END,\r\n        start\r\n    })\r\n    start.end = end\r\n    for (const alt of alts) {\r\n        if (alt !== undefined) {\r\n            // hook alts up to decision block\r\n            epsilon(start, alt.left)\r\n            epsilon(alt.right, end)\r\n        } else {\r\n            epsilon(start, end)\r\n        }\r\n    }\r\n\r\n    const handle: ATNHandle = {\r\n        left: start as ATNState,\r\n        right: end\r\n    }\r\n    atn.decisionMap[buildATNKey(rule, getProdType(production), production.idx)] = start\r\n    return handle\r\n}\r\n\r\nfunction getProdType(production: IProduction): LookaheadProductionType {\r\n    if (production instanceof Alternation) {\r\n        return 'Alternation';\r\n    } else if (production instanceof Option) {\r\n        return 'Option';\r\n    } else if (production instanceof Repetition) {\r\n        return 'Repetition';\r\n    } else if (production instanceof RepetitionWithSeparator) {\r\n        return 'RepetitionWithSeparator';\r\n    } else if (production instanceof RepetitionMandatory) {\r\n        return 'RepetitionMandatory';\r\n    } else if (production instanceof RepetitionMandatoryWithSeparator) {\r\n        return 'RepetitionMandatoryWithSeparator';\r\n    } else {\r\n        throw new Error('Invalid production type encountered');\r\n    }\r\n}\r\n\r\nfunction makeBlock(atn: ATN, alts: ATNHandle[]): ATNHandle {\r\n    const altsLength = alts.length\r\n    for (let i = 0; i < altsLength - 1; i++) {\r\n        const handle = alts[i]\r\n        let transition: Transition | undefined\r\n        if (handle.left.transitions.length === 1) {\r\n            transition = handle.left.transitions[0]\r\n        }\r\n        const isRuleTransition = transition instanceof RuleTransition\r\n        const ruleTransition = transition as RuleTransition\r\n        const next = alts[i + 1].left\r\n        if (\r\n            handle.left.type === ATN_BASIC &&\r\n            handle.right.type === ATN_BASIC &&\r\n            transition !== undefined &&\r\n            ((isRuleTransition && ruleTransition.followState === handle.right) ||\r\n                transition.target === handle.right)\r\n        ) {\r\n            // we can avoid epsilon edge to next element\r\n            if (isRuleTransition) {\r\n                ruleTransition.followState = next\r\n            } else {\r\n                transition.target = next\r\n            }\r\n            removeState(atn, handle.right) // we skipped over this state\r\n        } else {\r\n            // need epsilon if previous block's right end node is complex\r\n            epsilon(handle.right, next)\r\n        }\r\n    }\r\n\r\n    const first = alts[0]\r\n    const last = alts[altsLength - 1]\r\n    return {\r\n        left: first.left,\r\n        right: last.right\r\n    }\r\n}\r\n\r\nfunction tokenRef(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    tokenType: TokenType,\r\n    production: IProductionWithOccurrence\r\n): ATNHandle {\r\n    const left = newState<BasicState>(atn, rule, production, {\r\n        type: ATN_BASIC\r\n    })\r\n    const right = newState<BasicState>(atn, rule, production, {\r\n        type: ATN_BASIC\r\n    })\r\n    addTransition(left, new AtomTransition(right, tokenType))\r\n    return {\r\n        left,\r\n        right\r\n    }\r\n}\r\n\r\nfunction ruleRef(\r\n    atn: ATN,\r\n    currentRule: Rule,\r\n    nonTerminal: NonTerminal\r\n): ATNHandle {\r\n    const rule = nonTerminal.referencedRule\r\n    const start = atn.ruleToStartState.get(rule)!\r\n    const left = newState<BasicBlockStartState>(atn, currentRule, nonTerminal, {\r\n        type: ATN_BASIC\r\n    })\r\n    const right = newState<BasicBlockStartState>(atn, currentRule, nonTerminal, {\r\n        type: ATN_BASIC\r\n    })\r\n\r\n    const call = new RuleTransition(start, rule, right)\r\n    addTransition(left, call)\r\n\r\n    return {\r\n        left,\r\n        right\r\n    }\r\n}\r\n\r\nfunction buildRuleHandle(atn: ATN, rule: Rule, block: ATNHandle): ATNHandle {\r\n    const start = atn.ruleToStartState.get(rule)!\r\n    epsilon(start, block.left)\r\n    const stop = atn.ruleToStopState.get(rule)!\r\n    epsilon(block.right, stop)\r\n    const handle: ATNHandle = {\r\n        left: start,\r\n        right: stop\r\n    }\r\n    return handle\r\n}\r\n\r\nfunction epsilon(a: ATNBaseState, b: ATNBaseState): void {\r\n    const transition = new EpsilonTransition(b as ATNState)\r\n    addTransition(a, transition)\r\n}\r\n\r\nfunction newState<T extends ATNState>(\r\n    atn: ATN,\r\n    rule: Rule,\r\n    production: IProductionWithOccurrence | undefined,\r\n    partial: Partial<T>\r\n): T {\r\n    const t: T = {\r\n        atn,\r\n        production,\r\n        epsilonOnlyTransitions: false,\r\n        rule,\r\n        transitions: [],\r\n        nextTokenWithinRule: [],\r\n        stateNumber: atn.states.length,\r\n        ...partial\r\n    } as unknown as T\r\n    atn.states.push(t)\r\n    return t\r\n}\r\n\r\nfunction addTransition(state: ATNBaseState, transition: Transition) {\r\n    // A single ATN state can only contain epsilon transitions or non-epsilon transitions\r\n    // Because they are never mixed, only setting the property for the first transition is fine\r\n    if (state.transitions.length === 0) {\r\n        state.epsilonOnlyTransitions = transition.isEpsilon()\r\n    }\r\n    state.transitions.push(transition)\r\n}\r\n\r\nfunction removeState(atn: ATN, state: ATNState): void {\r\n    atn.states.splice(atn.states.indexOf(state), 1)\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,OAAOA,GAAG,MAAM,kBAAkB;AAClC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAIIC,WAAW,EACXC,WAAW,EAEXC,MAAM,EACNC,mBAAmB,EACnBC,UAAU,EACVC,QAAQ,EAERC,uBAAuB,EACvBC,gCAAgC,QAE7B,YAAY;AAEnB,OAAM,SAAUC,WAAWA,CAACC,IAAU,EAAEC,IAA6B,EAAEC,UAAkB;EACrF,OAAO,GAAGF,IAAI,CAACG,IAAI,IAAIF,IAAI,IAAIC,UAAU,EAAE;AAC/C;AAUA,OAAO,MAAME,gBAAgB,GAAG,CAAC;AACjC,OAAO,MAAMC,SAAS,GAAG,CAAC;AAC1B,OAAO,MAAMC,cAAc,GAAG,CAAC;AAC/B,OAAO,MAAMC,oBAAoB,GAAG,CAAC;AACrC,OAAO,MAAMC,oBAAoB,GAAG,CAAC;AACrC;AACA,OAAO,MAAMC,eAAe,GAAG,CAAC;AAChC,OAAO,MAAMC,aAAa,GAAG,CAAC;AAC9B,OAAO,MAAMC,aAAa,GAAG,CAAC;AAC9B,OAAO,MAAMC,kBAAkB,GAAG,CAAC;AACnC,OAAO,MAAMC,mBAAmB,GAAG,EAAE;AACrC,OAAO,MAAMC,kBAAkB,GAAG,EAAE;AACpC,OAAO,MAAMC,YAAY,GAAG,EAAE;AAuF9B,OAAM,MAAgBC,kBAAkB;EAGpCC,YAAYC,MAAgB;IACxB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EAEAC,SAASA,CAAA;IACL,OAAO,KAAK;EAChB;;AAGJ,OAAM,MAAOC,cAAe,SAAQJ,kBAAkB;EAGlDC,YAAYC,MAAgB,EAAEG,SAAoB;IAC9C,KAAK,CAACH,MAAM,CAAC;IACb,IAAI,CAACG,SAAS,GAAGA,SAAS;EAC9B;;AAGJ,OAAM,MAAOC,iBAAkB,SAAQN,kBAAkB;EACrDC,YAAYC,MAAgB;IACxB,KAAK,CAACA,MAAM,CAAC;EACjB;EAEAC,SAASA,CAAA;IACL,OAAO,IAAI;EACf;;AAGJ,OAAM,MAAOI,cAAe,SAAQP,kBAAkB;EAIlDC,YAAYO,SAAyB,EAAExB,IAAU,EAAEyB,WAAqB;IACpE,KAAK,CAACD,SAAS,CAAC;IAChB,IAAI,CAACxB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACyB,WAAW,GAAGA,WAAW;EAClC;EAEAN,SAASA,CAAA;IACL,OAAO,IAAI;EACf;;AAQJ,OAAM,SAAUO,SAASA,CAACC,KAAa;EACnC,MAAMC,GAAG,GAAQ;IACbC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,IAAIC,GAAG,EAAE;IAC3BC,eAAe,EAAE,IAAID,GAAG,EAAE;IAC1BE,MAAM,EAAE;GACX;EACDC,+BAA+B,CAACP,GAAG,EAAED,KAAK,CAAC;EAC3C,MAAMS,UAAU,GAAGT,KAAK,CAACU,MAAM;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACjC,MAAMtC,IAAI,GAAG2B,KAAK,CAACW,CAAC,CAAC;IACrB,MAAMC,SAAS,GAAGC,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEA,IAAI,CAAC;IACxC,IAAIuC,SAAS,KAAKE,SAAS,EAAE;MACzB;;IAEJC,eAAe,CAACd,GAAG,EAAE5B,IAAI,EAAEuC,SAAS,CAAC;;EAEzC,OAAOX,GAAG;AACd;AAEA,SAASO,+BAA+BA,CAACP,GAAQ,EAAED,KAAa;EAC5D,MAAMS,UAAU,GAAGT,KAAK,CAACU,MAAM;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACjC,MAAMtC,IAAI,GAAG2B,KAAK,CAACW,CAAC,CAAC;IACrB,MAAMK,KAAK,GAAGC,QAAQ,CAAiBhB,GAAG,EAAE5B,IAAI,EAAEyC,SAAS,EAAE;MACzDxC,IAAI,EAAEK;KACT,CAAC;IACF,MAAMuC,IAAI,GAAGD,QAAQ,CAAgBhB,GAAG,EAAE5B,IAAI,EAAEyC,SAAS,EAAE;MACvDxC,IAAI,EAAES;KACT,CAAC;IACFiC,KAAK,CAACE,IAAI,GAAGA,IAAI;IACjBjB,GAAG,CAACG,gBAAgB,CAACe,GAAG,CAAC9C,IAAI,EAAE2C,KAAK,CAAC;IACrCf,GAAG,CAACK,eAAe,CAACa,GAAG,CAAC9C,IAAI,EAAE6C,IAAI,CAAC;;AAE3C;AAEA,SAASE,IAAIA,CACTnB,GAAQ,EACR5B,IAAU,EACVgD,UAAuB;EAEvB,IAAIA,UAAU,YAAYpD,QAAQ,EAAE;IAChC,OAAOqD,QAAQ,CAACrB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAACE,YAAY,EAAEF,UAAU,CAAC;GAClE,MAAM,IAAIA,UAAU,YAAYxD,WAAW,EAAE;IAC1C,OAAO2D,OAAO,CAACvB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GACxC,MAAM,IAAIA,UAAU,YAAYzD,WAAW,EAAE;IAC1C,OAAO6D,WAAW,CAACxB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GAC5C,MAAM,IAAIA,UAAU,YAAYvD,MAAM,EAAE;IACrC,OAAO4D,MAAM,CAACzB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GACvC,MAAM,IAAIA,UAAU,YAAYrD,UAAU,EAAE;IACzC,OAAO2D,UAAU,CAAC1B,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GAC3C,MAAM,IAAIA,UAAU,YAAYnD,uBAAuB,EAAE;IACtD,OAAO0D,aAAa,CAAC3B,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GAC9C,MAAM,IAAIA,UAAU,YAAYtD,mBAAmB,EAAE;IAClD,OAAO8D,mBAAmB,CAAC5B,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GACpD,MAAM,IAAIA,UAAU,YAAYlD,gCAAgC,EAAE;IAC/D,OAAO2D,sBAAsB,CAAC7B,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,CAAC;GACvD,MAAM;IACH,OAAOR,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEgD,UAAyB,CAAC;;AAE1D;AAEA,SAASM,UAAUA,CAAC1B,GAAQ,EAAE5B,IAAU,EAAEsD,UAAsB;EAC5D,MAAMI,SAAS,GAAGd,QAAQ,CAAsBhB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAE;IACnErD,IAAI,EAAEO;GACT,CAAC;EACFmD,mBAAmB,CAAC/B,GAAG,EAAE8B,SAAS,CAAC;EACnC,MAAME,MAAM,GAAGC,QAAQ,CACnBjC,GAAG,EACH5B,IAAI,EACJ0D,SAAS,EACTJ,UAAU,EACVd,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAAC,CAC/B;EACD,OAAOQ,IAAI,CAAClC,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAEM,MAAM,CAAC;AAC9C;AAEA,SAASL,aAAaA,CAClB3B,GAAQ,EACR5B,IAAU,EACVsD,UAAmC;EAEnC,MAAMI,SAAS,GAAGd,QAAQ,CAAsBhB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAE;IACnErD,IAAI,EAAEO;GACT,CAAC;EACFmD,mBAAmB,CAAC/B,GAAG,EAAE8B,SAAS,CAAC;EACnC,MAAME,MAAM,GAAGC,QAAQ,CACnBjC,GAAG,EACH5B,IAAI,EACJ0D,SAAS,EACTJ,UAAU,EACVd,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAAC,CAC/B;EACD,MAAMS,GAAG,GAAGd,QAAQ,CAACrB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAACU,SAAS,EAAEV,UAAU,CAAC;EACjE,OAAOQ,IAAI,CAAClC,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAEM,MAAM,EAAEG,GAAG,CAAC;AACnD;AAEA,SAASP,mBAAmBA,CACxB5B,GAAQ,EACR5B,IAAU,EACVsD,UAA+B;EAE/B,MAAMW,SAAS,GAAGrB,QAAQ,CAAsBhB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAE;IACnErD,IAAI,EAAEM;GACT,CAAC;EACFoD,mBAAmB,CAAC/B,GAAG,EAAEqC,SAAS,CAAC;EACnC,MAAML,MAAM,GAAGC,QAAQ,CACnBjC,GAAG,EACH5B,IAAI,EACJiE,SAAS,EACTX,UAAU,EACVd,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAAC,CAC/B;EACD,OAAOY,IAAI,CAACtC,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAEM,MAAM,CAAC;AAC9C;AAEA,SAASH,sBAAsBA,CAC3B7B,GAAQ,EACR5B,IAAU,EACVsD,UAA4C;EAE5C,MAAMW,SAAS,GAAGrB,QAAQ,CAAsBhB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAE;IACnErD,IAAI,EAAEM;GACT,CAAC;EACFoD,mBAAmB,CAAC/B,GAAG,EAAEqC,SAAS,CAAC;EACnC,MAAML,MAAM,GAAGC,QAAQ,CACnBjC,GAAG,EACH5B,IAAI,EACJiE,SAAS,EACTX,UAAU,EACVd,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAAC,CAC/B;EACD,MAAMS,GAAG,GAAGd,QAAQ,CAACrB,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,CAACU,SAAS,EAAEV,UAAU,CAAC;EACjE,OAAOY,IAAI,CAACtC,GAAG,EAAE5B,IAAI,EAAEsD,UAAU,EAAEM,MAAM,EAAEG,GAAG,CAAC;AACnD;AAEA,SAASX,WAAWA,CAChBxB,GAAQ,EACR5B,IAAU,EACVoD,WAAwB;EAExB,MAAMT,KAAK,GAAGC,QAAQ,CAAuBhB,GAAG,EAAE5B,IAAI,EAAEoD,WAAW,EAAE;IACjEnD,IAAI,EAAEI;GACT,CAAC;EACFsD,mBAAmB,CAAC/B,GAAG,EAAEe,KAAK,CAAC;EAC/B,MAAMwB,IAAI,GAAG9E,GAAG,CAAC+D,WAAW,CAACgB,UAAU,EAAGC,CAAC,IAAKtB,IAAI,CAACnB,GAAG,EAAE5B,IAAI,EAAEqE,CAAC,CAAC,CAAC;EACnE,MAAMT,MAAM,GAAGC,QAAQ,CAACjC,GAAG,EAAE5B,IAAI,EAAE2C,KAAK,EAAES,WAAW,EAAE,GAAGe,IAAI,CAAC;EAC/D,OAAOP,MAAM;AACjB;AAEA,SAASP,MAAMA,CAACzB,GAAQ,EAAE5B,IAAU,EAAEqD,MAAc;EAChD,MAAMV,KAAK,GAAGC,QAAQ,CAAuBhB,GAAG,EAAE5B,IAAI,EAAEqD,MAAM,EAAE;IAC5DpD,IAAI,EAAEI;GACT,CAAC;EACFsD,mBAAmB,CAAC/B,GAAG,EAAEe,KAAK,CAAC;EAC/B,MAAMiB,MAAM,GAAGC,QAAQ,CAACjC,GAAG,EAAE5B,IAAI,EAAE2C,KAAK,EAAEU,MAAM,EAAEb,KAAK,CAACZ,GAAG,EAAE5B,IAAI,EAAEqD,MAAM,CAAC,CAAC;EAC3E,OAAOiB,QAAQ,CAAC1C,GAAG,EAAE5B,IAAI,EAAEqD,MAAM,EAAEO,MAAM,CAAC;AAC9C;AAEA,SAASpB,KAAKA,CACVZ,GAAQ,EACR5B,IAAU,EACVwC,KAAoC;EAEpC,MAAM+B,OAAO,GAAGjF,MAAM,CAClBD,GAAG,CAACmD,KAAK,CAAC4B,UAAU,EAAGC,CAAC,IAAKtB,IAAI,CAACnB,GAAG,EAAE5B,IAAI,EAAEqE,CAAC,CAAC,CAAC,EAC/CA,CAAC,IAAKA,CAAC,KAAK5B,SAAS,CACV;EAChB,IAAI8B,OAAO,CAAClC,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOkC,OAAO,CAAC,CAAC,CAAC;GACpB,MAAM,IAAIA,OAAO,CAAClC,MAAM,KAAK,CAAC,EAAE;IAC7B,OAAOI,SAAS;GACnB,MAAM;IACH,OAAO+B,SAAS,CAAC5C,GAAG,EAAE2C,OAAO,CAAC;;AAEtC;AAEA,SAASL,IAAIA,CACTtC,GAAQ,EACR5B,IAAU,EACVkE,IAA+B,EAC/BN,MAAiB,EACjBG,GAAe;EAEf,MAAMU,QAAQ,GAAGb,MAAM,CAACc,IAA2B;EACnD,MAAMC,MAAM,GAAGf,MAAM,CAACgB,KAAK;EAE3B,MAAMC,IAAI,GAAGjC,QAAQ,CAAoBhB,GAAG,EAAE5B,IAAI,EAAEkE,IAAI,EAAE;IACtDjE,IAAI,EAAEa;GACT,CAAC;EACF6C,mBAAmB,CAAC/B,GAAG,EAAEiD,IAAI,CAAC;EAC9B,MAAMC,GAAG,GAAGlC,QAAQ,CAAehB,GAAG,EAAE5B,IAAI,EAAEkE,IAAI,EAAE;IAChDjE,IAAI,EAAEc;GACT,CAAC;EACF0D,QAAQ,CAACM,QAAQ,GAAGF,IAAI;EACxBC,GAAG,CAACC,QAAQ,GAAGF,IAAI;EACnBjD,GAAG,CAACC,WAAW,CAAC9B,WAAW,CAACC,IAAI,EAAE+D,GAAG,GAAG,kCAAkC,GAAG,qBAAqB,EAAEG,IAAI,CAACc,GAAG,CAAC,CAAC,GAAGH,IAAI;EACrHI,OAAO,CAACN,MAAM,EAAEE,IAAI,CAAC,EAAC;EAEtB;EACA;EACA,IAAId,GAAG,KAAKtB,SAAS,EAAE;IACnBwC,OAAO,CAACJ,IAAI,EAAEJ,QAAQ,CAAC,EAAC;IACxBQ,OAAO,CAACJ,IAAI,EAAEC,GAAG,CAAC,EAAC;GACtB,MAAM;IACHG,OAAO,CAACJ,IAAI,EAAEC,GAAG,CAAC,EAAC;IACnB;IACAG,OAAO,CAACJ,IAAI,EAAEd,GAAG,CAACW,IAAI,CAAC;IACvBO,OAAO,CAAClB,GAAG,CAACa,KAAK,EAAEH,QAAQ,CAAC;;EAGhC,OAAO;IACHC,IAAI,EAAED,QAAQ;IACdG,KAAK,EAAEE;GACV;AACL;AAEA,SAAShB,IAAIA,CACTlC,GAAQ,EACR5B,IAAU,EACV8D,IAA+B,EAC/BF,MAAiB,EACjBG,GAAe;EAEf,MAAMpB,KAAK,GAAGiB,MAAM,CAACc,IAAI;EACzB,MAAMI,GAAG,GAAGlB,MAAM,CAACgB,KAAK;EAExB,MAAMM,KAAK,GAAGtC,QAAQ,CAAqBhB,GAAG,EAAE5B,IAAI,EAAE8D,IAAI,EAAE;IACxD7D,IAAI,EAAEY;GACT,CAAC;EACF8C,mBAAmB,CAAC/B,GAAG,EAAEsD,KAAK,CAAC;EAC/B,MAAMC,OAAO,GAAGvC,QAAQ,CAAehB,GAAG,EAAE5B,IAAI,EAAE8D,IAAI,EAAE;IACpD7D,IAAI,EAAEc;GACT,CAAC;EACF,MAAM8D,IAAI,GAAGjC,QAAQ,CAAoBhB,GAAG,EAAE5B,IAAI,EAAE8D,IAAI,EAAE;IACtD7D,IAAI,EAAEW;GACT,CAAC;EACFsE,KAAK,CAACH,QAAQ,GAAGF,IAAI;EACrBM,OAAO,CAACJ,QAAQ,GAAGF,IAAI;EAEvBI,OAAO,CAACC,KAAK,EAAEvC,KAAK,CAAC,EAAC;EACtBsC,OAAO,CAACC,KAAK,EAAEC,OAAO,CAAC,EAAC;EACxBF,OAAO,CAACH,GAAG,EAAED,IAAI,CAAC,EAAC;EAEnB,IAAId,GAAG,KAAKtB,SAAS,EAAE;IACnBwC,OAAO,CAACJ,IAAI,EAAEM,OAAO,CAAC,EAAC;IACvB;IACAF,OAAO,CAACJ,IAAI,EAAEd,GAAG,CAACW,IAAI,CAAC;IACvBO,OAAO,CAAClB,GAAG,CAACa,KAAK,EAAEjC,KAAK,CAAC;GAC5B,MAAM;IACHsC,OAAO,CAACJ,IAAI,EAAEK,KAAK,CAAC,EAAC;;EAGzBtD,GAAG,CAACC,WAAW,CAAC9B,WAAW,CAACC,IAAI,EAAE+D,GAAG,GAAG,yBAAyB,GAAG,YAAY,EAAED,IAAI,CAACkB,GAAG,CAAC,CAAC,GAAGE,KAAK;EACpG,OAAO;IACHR,IAAI,EAAEQ,KAAK;IACXN,KAAK,EAAEO;GACV;AACL;AAEA,SAASb,QAAQA,CAAC1C,GAAQ,EAAE5B,IAAU,EAAEsE,QAAgB,EAAEV,MAAiB;EACvE,MAAMjB,KAAK,GAAGiB,MAAM,CAACc,IAAqB;EAC1C,MAAMI,GAAG,GAAGlB,MAAM,CAACgB,KAAK;EAExBK,OAAO,CAACtC,KAAK,EAAEmC,GAAG,CAAC;EAEnBlD,GAAG,CAACC,WAAW,CAAC9B,WAAW,CAACC,IAAI,EAAE,QAAQ,EAAEsE,QAAQ,CAACU,GAAG,CAAC,CAAC,GAAGrC,KAAK;EAClE,OAAOiB,MAAM;AACjB;AAEA,SAASD,mBAAmBA,CAAC/B,GAAQ,EAAEwD,KAAoB;EACvDxD,GAAG,CAACE,cAAc,CAACuD,IAAI,CAACD,KAAK,CAAC;EAC9BA,KAAK,CAACE,QAAQ,GAAG1D,GAAG,CAACE,cAAc,CAACO,MAAM,GAAG,CAAC;EAC9C,OAAO+C,KAAK,CAACE,QAAQ;AACzB;AAEA,SAASzB,QAAQA,CACbjC,GAAQ,EACR5B,IAAU,EACV2C,KAAsB,EACtBK,UAAqC,EACrC,GAAGmB,IAA+B;EAElC,MAAMW,GAAG,GAAGlC,QAAQ,CAAgBhB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,EAAE;IACvD/C,IAAI,EAAEU,aAAa;IACnBgC;GACH,CAAC;EACFA,KAAK,CAACmC,GAAG,GAAGA,GAAG;EACf,KAAK,MAAMS,GAAG,IAAIpB,IAAI,EAAE;IACpB,IAAIoB,GAAG,KAAK9C,SAAS,EAAE;MACnB;MACAwC,OAAO,CAACtC,KAAK,EAAE4C,GAAG,CAACb,IAAI,CAAC;MACxBO,OAAO,CAACM,GAAG,CAACX,KAAK,EAAEE,GAAG,CAAC;KAC1B,MAAM;MACHG,OAAO,CAACtC,KAAK,EAAEmC,GAAG,CAAC;;;EAI3B,MAAMlB,MAAM,GAAc;IACtBc,IAAI,EAAE/B,KAAiB;IACvBiC,KAAK,EAAEE;GACV;EACDlD,GAAG,CAACC,WAAW,CAAC9B,WAAW,CAACC,IAAI,EAAEwF,WAAW,CAACxC,UAAU,CAAC,EAAEA,UAAU,CAACgC,GAAG,CAAC,CAAC,GAAGrC,KAAK;EACnF,OAAOiB,MAAM;AACjB;AAEA,SAAS4B,WAAWA,CAACxC,UAAuB;EACxC,IAAIA,UAAU,YAAYzD,WAAW,EAAE;IACnC,OAAO,aAAa;GACvB,MAAM,IAAIyD,UAAU,YAAYvD,MAAM,EAAE;IACrC,OAAO,QAAQ;GAClB,MAAM,IAAIuD,UAAU,YAAYrD,UAAU,EAAE;IACzC,OAAO,YAAY;GACtB,MAAM,IAAIqD,UAAU,YAAYnD,uBAAuB,EAAE;IACtD,OAAO,yBAAyB;GACnC,MAAM,IAAImD,UAAU,YAAYtD,mBAAmB,EAAE;IAClD,OAAO,qBAAqB;GAC/B,MAAM,IAAIsD,UAAU,YAAYlD,gCAAgC,EAAE;IAC/D,OAAO,kCAAkC;GAC5C,MAAM;IACH,MAAM,IAAI2F,KAAK,CAAC,qCAAqC,CAAC;;AAE9D;AAEA,SAASjB,SAASA,CAAC5C,GAAQ,EAAEuC,IAAiB;EAC1C,MAAMuB,UAAU,GAAGvB,IAAI,CAAC9B,MAAM;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,UAAU,GAAG,CAAC,EAAEpD,CAAC,EAAE,EAAE;IACrC,MAAMsB,MAAM,GAAGO,IAAI,CAAC7B,CAAC,CAAC;IACtB,IAAIqD,UAAkC;IACtC,IAAI/B,MAAM,CAACc,IAAI,CAACkB,WAAW,CAACvD,MAAM,KAAK,CAAC,EAAE;MACtCsD,UAAU,GAAG/B,MAAM,CAACc,IAAI,CAACkB,WAAW,CAAC,CAAC,CAAC;;IAE3C,MAAMC,gBAAgB,GAAGF,UAAU,YAAYpE,cAAc;IAC7D,MAAMuE,cAAc,GAAGH,UAA4B;IACnD,MAAMI,IAAI,GAAG5B,IAAI,CAAC7B,CAAC,GAAG,CAAC,CAAC,CAACoC,IAAI;IAC7B,IACId,MAAM,CAACc,IAAI,CAACzE,IAAI,KAAKI,SAAS,IAC9BuD,MAAM,CAACgB,KAAK,CAAC3E,IAAI,KAAKI,SAAS,IAC/BsF,UAAU,KAAKlD,SAAS,KACtBoD,gBAAgB,IAAIC,cAAc,CAACrE,WAAW,KAAKmC,MAAM,CAACgB,KAAK,IAC7De,UAAU,CAACzE,MAAM,KAAK0C,MAAM,CAACgB,KAAK,CAAC,EACzC;MACE;MACA,IAAIiB,gBAAgB,EAAE;QAClBC,cAAc,CAACrE,WAAW,GAAGsE,IAAI;OACpC,MAAM;QACHJ,UAAU,CAACzE,MAAM,GAAG6E,IAAI;;MAE5BC,WAAW,CAACpE,GAAG,EAAEgC,MAAM,CAACgB,KAAK,CAAC,EAAC;KAClC,MAAM;MACH;MACAK,OAAO,CAACrB,MAAM,CAACgB,KAAK,EAAEmB,IAAI,CAAC;;;EAInC,MAAME,KAAK,GAAG9B,IAAI,CAAC,CAAC,CAAC;EACrB,MAAM+B,IAAI,GAAG/B,IAAI,CAACuB,UAAU,GAAG,CAAC,CAAC;EACjC,OAAO;IACHhB,IAAI,EAAEuB,KAAK,CAACvB,IAAI;IAChBE,KAAK,EAAEsB,IAAI,CAACtB;GACf;AACL;AAEA,SAAS3B,QAAQA,CACbrB,GAAQ,EACR5B,IAAU,EACVqB,SAAoB,EACpB2B,UAAqC;EAErC,MAAM0B,IAAI,GAAG9B,QAAQ,CAAahB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,EAAE;IACrD/C,IAAI,EAAEI;GACT,CAAC;EACF,MAAMuE,KAAK,GAAGhC,QAAQ,CAAahB,GAAG,EAAE5B,IAAI,EAAEgD,UAAU,EAAE;IACtD/C,IAAI,EAAEI;GACT,CAAC;EACF8F,aAAa,CAACzB,IAAI,EAAE,IAAItD,cAAc,CAACwD,KAAK,EAAEvD,SAAS,CAAC,CAAC;EACzD,OAAO;IACHqD,IAAI;IACJE;GACH;AACL;AAEA,SAASzB,OAAOA,CACZvB,GAAQ,EACRwE,WAAiB,EACjBC,WAAwB;EAExB,MAAMrG,IAAI,GAAGqG,WAAW,CAACC,cAAc;EACvC,MAAM3D,KAAK,GAAGf,GAAG,CAACG,gBAAgB,CAACwE,GAAG,CAACvG,IAAI,CAAE;EAC7C,MAAM0E,IAAI,GAAG9B,QAAQ,CAAuBhB,GAAG,EAAEwE,WAAW,EAAEC,WAAW,EAAE;IACvEpG,IAAI,EAAEI;GACT,CAAC;EACF,MAAMuE,KAAK,GAAGhC,QAAQ,CAAuBhB,GAAG,EAAEwE,WAAW,EAAEC,WAAW,EAAE;IACxEpG,IAAI,EAAEI;GACT,CAAC;EAEF,MAAMmG,IAAI,GAAG,IAAIjF,cAAc,CAACoB,KAAK,EAAE3C,IAAI,EAAE4E,KAAK,CAAC;EACnDuB,aAAa,CAACzB,IAAI,EAAE8B,IAAI,CAAC;EAEzB,OAAO;IACH9B,IAAI;IACJE;GACH;AACL;AAEA,SAASlC,eAAeA,CAACd,GAAQ,EAAE5B,IAAU,EAAEwC,KAAgB;EAC3D,MAAMG,KAAK,GAAGf,GAAG,CAACG,gBAAgB,CAACwE,GAAG,CAACvG,IAAI,CAAE;EAC7CiF,OAAO,CAACtC,KAAK,EAAEH,KAAK,CAACkC,IAAI,CAAC;EAC1B,MAAM7B,IAAI,GAAGjB,GAAG,CAACK,eAAe,CAACsE,GAAG,CAACvG,IAAI,CAAE;EAC3CiF,OAAO,CAACzC,KAAK,CAACoC,KAAK,EAAE/B,IAAI,CAAC;EAC1B,MAAMe,MAAM,GAAc;IACtBc,IAAI,EAAE/B,KAAK;IACXiC,KAAK,EAAE/B;GACV;EACD,OAAOe,MAAM;AACjB;AAEA,SAASqB,OAAOA,CAACwB,CAAe,EAAEC,CAAe;EAC7C,MAAMf,UAAU,GAAG,IAAIrE,iBAAiB,CAACoF,CAAa,CAAC;EACvDP,aAAa,CAACM,CAAC,EAAEd,UAAU,CAAC;AAChC;AAEA,SAAS/C,QAAQA,CACbhB,GAAQ,EACR5B,IAAU,EACVgD,UAAiD,EACjD2D,OAAmB;EAEnB,MAAMC,CAAC,GAAMC,MAAA,CAAAC,MAAA;IACTlF,GAAG;IACHoB,UAAU;IACV+D,sBAAsB,EAAE,KAAK;IAC7B/G,IAAI;IACJ4F,WAAW,EAAE,EAAE;IACfoB,mBAAmB,EAAE,EAAE;IACvBC,WAAW,EAAErF,GAAG,CAACM,MAAM,CAACG;EAAM,GAC3BsE,OAAO,CACG;EACjB/E,GAAG,CAACM,MAAM,CAACmD,IAAI,CAACuB,CAAC,CAAC;EAClB,OAAOA,CAAC;AACZ;AAEA,SAAST,aAAaA,CAACf,KAAmB,EAAEO,UAAsB;EAC9D;EACA;EACA,IAAIP,KAAK,CAACQ,WAAW,CAACvD,MAAM,KAAK,CAAC,EAAE;IAChC+C,KAAK,CAAC2B,sBAAsB,GAAGpB,UAAU,CAACxE,SAAS,EAAE;;EAEzDiE,KAAK,CAACQ,WAAW,CAACP,IAAI,CAACM,UAAU,CAAC;AACtC;AAEA,SAASK,WAAWA,CAACpE,GAAQ,EAAEwD,KAAe;EAC1CxD,GAAG,CAACM,MAAM,CAACgF,MAAM,CAACtF,GAAG,CAACM,MAAM,CAACiF,OAAO,CAAC/B,KAAK,CAAC,EAAE,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}