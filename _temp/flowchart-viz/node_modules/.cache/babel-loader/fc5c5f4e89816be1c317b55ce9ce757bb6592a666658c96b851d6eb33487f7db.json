{"ast": null, "code": "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](),\n    set = new Set();\n  for (const o of other) {\n    if (set.has(o)) continue;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) return false;\n      set.add(value);\n      if (Object.is(o, value)) break;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["superset", "values", "other", "iterator", "Symbol", "set", "Set", "o", "has", "value", "done", "next", "add", "Object", "is"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/superset.js"], "sourcesContent": ["export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    if (set.has(o)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      set.add(value);\n      if (Object.is(o, value)) break;\n    }\n  }\n  return true;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC9C,MAAMC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3D,KAAK,MAAMC,CAAC,IAAIL,KAAK,EAAE;IACrB,IAAIG,GAAG,CAACG,GAAG,CAACD,CAAC,CAAC,EAAE;IAChB,IAAIE,KAAK,EAAEC,IAAI;IACf,OAAQ;MAACD,KAAK;MAAEC;IAAI,CAAC,GAAGP,QAAQ,CAACQ,IAAI,CAAC,CAAC,EAAG;MACxC,IAAID,IAAI,EAAE,OAAO,KAAK;MACtBL,GAAG,CAACO,GAAG,CAACH,KAAK,CAAC;MACd,IAAII,MAAM,CAACC,EAAE,CAACP,CAAC,EAAEE,KAAK,CAAC,EAAE;IAC3B;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}