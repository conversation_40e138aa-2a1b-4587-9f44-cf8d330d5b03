{"ast": null, "code": "const NAME = \"name\";\nexport function defineNameProp(obj, nameValue) {\n  Object.defineProperty(obj, NAME, {\n    enumerable: false,\n    configurable: true,\n    writable: false,\n    value: nameValue\n  });\n}", "map": {"version": 3, "names": ["NAME", "defineNameProp", "obj", "nameValue", "Object", "defineProperty", "enumerable", "configurable", "writable", "value"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/lang/lang_extensions.ts"], "sourcesContent": ["const NAME = \"name\";\n\nexport function defineNameProp(obj: {}, nameValue: string): void {\n  Object.defineProperty(obj, NAME, {\n    enumerable: false,\n    configurable: true,\n    writable: false,\n    value: nameValue,\n  });\n}\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG,MAAM;AAEnB,OAAM,SAAUC,cAAcA,CAACC,GAAO,EAAEC,SAAiB;EACvDC,MAAM,CAACC,cAAc,CAACH,GAAG,EAAEF,IAAI,EAAE;IAC/BM,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAEN;GACR,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}