{"ast": null, "code": "import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last string symbol\n * that is not found in the character symbols.\n *\n * @private\n * @param {Array} strSymbols The string symbols to inspect.\n * @param {Array} chrSymbols The character symbols to find.\n * @returns {number} Returns the index of the last unmatched string symbol.\n */\nfunction charsEndIndex(strSymbols, chrSymbols) {\n  var index = strSymbols.length;\n  while (index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}\n  return index;\n}\nexport default charsEndIndex;", "map": {"version": 3, "names": ["baseIndexOf", "charsEndIndex", "strSymbols", "chrSymbols", "index", "length"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_charsEndIndex.js"], "sourcesContent": ["import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last string symbol\n * that is not found in the character symbols.\n *\n * @private\n * @param {Array} strSymbols The string symbols to inspect.\n * @param {Array} chrSymbols The character symbols to find.\n * @returns {number} Returns the index of the last unmatched string symbol.\n */\nfunction charsEndIndex(strSymbols, chrSymbols) {\n  var index = strSymbols.length;\n\n  while (index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}\n  return index;\n}\n\nexport default charsEndIndex;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAC7C,IAAIC,KAAK,GAAGF,UAAU,CAACG,MAAM;EAE7B,OAAOD,KAAK,EAAE,IAAIJ,WAAW,CAACG,UAAU,EAAED,UAAU,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EACvE,OAAOA,KAAK;AACd;AAEA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}