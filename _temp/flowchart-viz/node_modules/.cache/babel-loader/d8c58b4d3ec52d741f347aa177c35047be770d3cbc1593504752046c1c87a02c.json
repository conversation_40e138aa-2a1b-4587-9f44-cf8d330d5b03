{"ast": null, "code": "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}", "map": {"version": 3, "names": ["Polygon", "constructor", "_", "moveTo", "x", "y", "push", "closePath", "slice", "lineTo", "value", "length"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-delaunay/src/polygon.js"], "sourcesContent": ["export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n"], "mappings": "AAAA,eAAe,MAAMA,OAAO,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,CAAC,GAAG,EAAE;EACb;EACAC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACX,IAAI,CAACH,CAAC,CAACI,IAAI,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;EACrB;EACAE,SAASA,CAAA,EAAG;IACV,IAAI,CAACL,CAAC,CAACI,IAAI,CAAC,IAAI,CAACJ,CAAC,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC;EAChC;EACAC,MAAMA,CAACL,CAAC,EAAEC,CAAC,EAAE;IACX,IAAI,CAACH,CAAC,CAACI,IAAI,CAAC,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC;EACrB;EACAK,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACR,CAAC,CAACS,MAAM,GAAG,IAAI,CAACT,CAAC,GAAG,IAAI;EACtC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}