{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport var Module;\n(function (Module) {\n  Module.merge = (m1, m2) => _merge(_merge({}, m1), m2);\n})(Module || (Module = {}));\n/**\n * Given a set of modules, the inject function returns a lazily evaluated injector\n * that injects dependencies into the requested service when it is requested the\n * first time. Subsequent requests will return the same service.\n *\n * In the case of cyclic dependencies, an Error will be thrown. This can be fixed\n * by injecting a provider `() => T` instead of a `T`.\n *\n * Please note that the arguments may be objects or arrays. However, the result will\n * be an object. Using it with for..of will have no effect.\n *\n * @param module1 first Module\n * @param module2 (optional) second Module\n * @param module3 (optional) third Module\n * @param module4 (optional) fourth Module\n * @param module5 (optional) fifth Module\n * @param module6 (optional) sixth Module\n * @param module7 (optional) seventh Module\n * @param module8 (optional) eighth Module\n * @param module9 (optional) ninth Module\n * @returns a new object of type I\n */\nexport function inject(module1, module2, module3, module4, module5, module6, module7, module8, module9) {\n  const module = [module1, module2, module3, module4, module5, module6, module7, module8, module9].reduce(_merge, {});\n  return _inject(module);\n}\nconst isProxy = Symbol('isProxy');\n/**\n * Eagerly load all services in the given dependency injection container. This is sometimes\n * necessary because services can register event listeners in their constructors.\n */\nexport function eagerLoad(item) {\n  if (item && item[isProxy]) {\n    for (const value of Object.values(item)) {\n      eagerLoad(value);\n    }\n  }\n  return item;\n}\n/**\n * Helper function that returns an injector by creating a proxy.\n * Invariant: injector is of type I. If injector is undefined, then T = I.\n */\nfunction _inject(module, injector) {\n  const proxy = new Proxy({}, {\n    deleteProperty: () => false,\n    set: () => {\n      throw new Error('Cannot set property on injected service container');\n    },\n    get: (obj, prop) => {\n      if (prop === isProxy) {\n        return true;\n      } else {\n        return _resolve(obj, prop, module, injector || proxy);\n      }\n    },\n    getOwnPropertyDescriptor: (obj, prop) => (_resolve(obj, prop, module, injector || proxy), Object.getOwnPropertyDescriptor(obj, prop)),\n    // used by for..in\n    has: (_, prop) => prop in module,\n    // used by ..in..\n    ownKeys: () => [...Object.getOwnPropertyNames(module)] // used by for..in\n  });\n  return proxy;\n}\n/**\n * Internally used to tag a requested dependency, directly before calling the factory.\n * This allows us to find cycles during instance creation.\n */\nconst __requested__ = Symbol();\n/**\n * Returns the value `obj[prop]`. If the value does not exist, yet, it is resolved from\n * the module description. The result of service factories is cached. Groups are\n * recursively proxied.\n *\n * @param obj an object holding all group proxies and services\n * @param prop the key of a value within obj\n * @param module an object containing groups and service factories\n * @param injector the first level proxy that provides access to all values\n * @returns the requested value `obj[prop]`\n * @throws Error if a dependency cycle is detected\n */\nfunction _resolve(obj, prop, module, injector) {\n  if (prop in obj) {\n    if (obj[prop] instanceof Error) {\n      throw new Error('Construction failure. Please make sure that your dependencies are constructable.', {\n        cause: obj[prop]\n      });\n    }\n    if (obj[prop] === __requested__) {\n      throw new Error('Cycle detected. Please make \"' + String(prop) + '\" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');\n    }\n    return obj[prop];\n  } else if (prop in module) {\n    const value = module[prop];\n    obj[prop] = __requested__;\n    try {\n      obj[prop] = typeof value === 'function' ? value(injector) : _inject(value, injector);\n    } catch (error) {\n      obj[prop] = error instanceof Error ? error : undefined;\n      throw error;\n    }\n    return obj[prop];\n  } else {\n    return undefined;\n  }\n}\n/**\n * Performs a deep-merge of two modules by writing source entries into the target module.\n *\n * @param target the module which is written\n * @param source the module which is read\n * @returns the target module\n */\nfunction _merge(target, source) {\n  if (source) {\n    for (const [key, value2] of Object.entries(source)) {\n      if (value2 !== undefined) {\n        const value1 = target[key];\n        if (value1 !== null && value2 !== null && typeof value1 === 'object' && typeof value2 === 'object') {\n          target[key] = _merge(value1, value2);\n        } else {\n          target[key] = value2;\n        }\n      }\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "merge", "m1", "m2", "_merge", "inject", "module1", "module2", "module3", "module4", "module5", "module6", "module7", "module8", "module9", "module", "reduce", "_inject", "isProxy", "Symbol", "eagerLoad", "item", "value", "Object", "values", "injector", "proxy", "Proxy", "deleteProperty", "set", "Error", "get", "obj", "prop", "_resolve", "getOwnPropertyDescriptor", "has", "_", "ownKeys", "getOwnPropertyNames", "__requested__", "cause", "String", "error", "undefined", "target", "source", "key", "value2", "entries", "value1"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/dependency-injection.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n\r\n/**\r\n * A `Module<I>` is a description of possibly grouped service factories.\r\n *\r\n * Given a type I = { group: { service: A } },\r\n * Module<I> := { group: { service: (injector: I) => A } }\r\n *\r\n * Making `I` available during the creation of `I` allows us to create cyclic\r\n * dependencies.\r\n */\r\nexport type Module<I, T = I> = {\r\n    [K in keyof T]: Module<I, T[K]> | ((injector: I) => T[K])\r\n}\r\n\r\nexport namespace Module {\r\n    export const merge = <M1, M2, R extends M1 & M2>(m1: Module<R, M1>, m2: Module<R, M2>) => (_merge(_merge({}, m1), m2) as Module<R, M1 & M2>);\r\n}\r\n\r\n/**\r\n * Given a set of modules, the inject function returns a lazily evaluated injector\r\n * that injects dependencies into the requested service when it is requested the\r\n * first time. Subsequent requests will return the same service.\r\n *\r\n * In the case of cyclic dependencies, an Error will be thrown. This can be fixed\r\n * by injecting a provider `() => T` instead of a `T`.\r\n *\r\n * Please note that the arguments may be objects or arrays. However, the result will\r\n * be an object. Using it with for..of will have no effect.\r\n *\r\n * @param module1 first Module\r\n * @param module2 (optional) second Module\r\n * @param module3 (optional) third Module\r\n * @param module4 (optional) fourth Module\r\n * @param module5 (optional) fifth Module\r\n * @param module6 (optional) sixth Module\r\n * @param module7 (optional) seventh Module\r\n * @param module8 (optional) eighth Module\r\n * @param module9 (optional) ninth Module\r\n * @returns a new object of type I\r\n */\r\nexport function inject<I1, I2, I3, I4, I5, I6, I7, I8, I9, I extends I1 & I2 & I3 & I4 & I5 & I6 & I7 & I8 & I9>(\r\n    module1: Module<I, I1>, module2?: Module<I, I2>, module3?: Module<I, I3>, module4?: Module<I, I4>, module5?: Module<I, I5>, module6?: Module<I, I6>, module7?: Module<I, I7>, module8?: Module<I, I8>, module9?: Module<I, I9>\r\n): I {\r\n    const module = [module1, module2, module3, module4, module5, module6, module7, module8, module9].reduce(_merge, {}) as Module<I>;\r\n    return _inject(module);\r\n}\r\n\r\nconst isProxy = Symbol('isProxy');\r\n\r\n/**\r\n * Eagerly load all services in the given dependency injection container. This is sometimes\r\n * necessary because services can register event listeners in their constructors.\r\n */\r\nexport function eagerLoad<T>(item: T): T {\r\n    if (item && (item as any)[isProxy]) {\r\n        for (const value of Object.values(item)) {\r\n            eagerLoad(value);\r\n        }\r\n    }\r\n    return item;\r\n}\r\n\r\n/**\r\n * Helper function that returns an injector by creating a proxy.\r\n * Invariant: injector is of type I. If injector is undefined, then T = I.\r\n */\r\nfunction _inject<I, T>(module: Module<I, T>, injector?: any): T {\r\n    const proxy: any = new Proxy({} as any, {\r\n        deleteProperty: () => false,\r\n        set: () => {\r\n            throw new Error('Cannot set property on injected service container');\r\n        },\r\n        get: (obj, prop) => {\r\n            if (prop === isProxy) {\r\n                return true;\r\n            } else {\r\n                return _resolve(obj, prop, module, injector || proxy);\r\n            }\r\n        },\r\n        getOwnPropertyDescriptor: (obj, prop) => (_resolve(obj, prop, module, injector || proxy), Object.getOwnPropertyDescriptor(obj, prop)), // used by for..in\r\n        has: (_, prop) => prop in module, // used by ..in..\r\n        ownKeys: () => [...Object.getOwnPropertyNames(module)] // used by for..in\r\n    });\r\n    return proxy;\r\n}\r\n\r\n/**\r\n * Internally used to tag a requested dependency, directly before calling the factory.\r\n * This allows us to find cycles during instance creation.\r\n */\r\nconst __requested__ = Symbol();\r\n\r\n/**\r\n * Returns the value `obj[prop]`. If the value does not exist, yet, it is resolved from\r\n * the module description. The result of service factories is cached. Groups are\r\n * recursively proxied.\r\n *\r\n * @param obj an object holding all group proxies and services\r\n * @param prop the key of a value within obj\r\n * @param module an object containing groups and service factories\r\n * @param injector the first level proxy that provides access to all values\r\n * @returns the requested value `obj[prop]`\r\n * @throws Error if a dependency cycle is detected\r\n */\r\nfunction _resolve<I, T>(obj: any, prop: string | symbol | number, module: Module<I, T>, injector: I): T[keyof T] | undefined {\r\n    if (prop in obj) {\r\n        if (obj[prop] instanceof Error) {\r\n            throw new Error('Construction failure. Please make sure that your dependencies are constructable.', {cause: obj[prop]});\r\n        }\r\n        if (obj[prop] === __requested__) {\r\n            throw new Error('Cycle detected. Please make \"' + String(prop) + '\" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');\r\n        }\r\n        return obj[prop];\r\n    } else if (prop in module) {\r\n        const value: Module<I, T[keyof T]> | ((injector: I) => T[keyof T]) = module[prop as keyof T];\r\n        obj[prop] = __requested__;\r\n        try {\r\n            obj[prop] = (typeof value === 'function') ? value(injector) : _inject(value, injector);\r\n        } catch (error) {\r\n            obj[prop] = error instanceof Error ? error : undefined;\r\n            throw error;\r\n        }\r\n        return obj[prop];\r\n    } else {\r\n        return undefined;\r\n    }\r\n}\r\n\r\n/**\r\n * Performs a deep-merge of two modules by writing source entries into the target module.\r\n *\r\n * @param target the module which is written\r\n * @param source the module which is read\r\n * @returns the target module\r\n */\r\nfunction _merge(target: Module<any>, source?: Module<any>): Module<unknown> {\r\n    if (source) {\r\n        for (const [key, value2] of Object.entries(source)) {\r\n            if (value2 !== undefined) {\r\n                const value1 = target[key];\r\n                if (value1 !== null && value2 !== null && typeof value1 === 'object' && typeof value2 === 'object') {\r\n                    target[key] = _merge(value1, value2);\r\n                } else {\r\n                    target[key] = value2;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return target;\r\n}\r\n"], "mappings": "AAAA;;;;;AAqBA,OAAM,IAAWA,MAAM;AAAvB,WAAiBA,MAAM;EACNA,MAAA,CAAAC,KAAK,GAAG,CAA4BC,EAAiB,EAAEC,EAAiB,KAAMC,MAAM,CAACA,MAAM,CAAC,EAAE,EAAEF,EAAE,CAAC,EAAEC,EAAE,CAAwB;AAChJ,CAAC,EAFgBH,MAAM,KAANA,MAAM;AAIvB;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAM,SAAUK,MAAMA,CAClBC,OAAsB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB,EAAEC,OAAuB;EAE9N,MAAMC,MAAM,GAAG,CAACT,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAACE,MAAM,CAACZ,MAAM,EAAE,EAAE,CAAc;EAChI,OAAOa,OAAO,CAACF,MAAM,CAAC;AAC1B;AAEA,MAAMG,OAAO,GAAGC,MAAM,CAAC,SAAS,CAAC;AAEjC;;;;AAIA,OAAM,SAAUC,SAASA,CAAIC,IAAO;EAChC,IAAIA,IAAI,IAAKA,IAAY,CAACH,OAAO,CAAC,EAAE;IAChC,KAAK,MAAMI,KAAK,IAAIC,MAAM,CAACC,MAAM,CAACH,IAAI,CAAC,EAAE;MACrCD,SAAS,CAACE,KAAK,CAAC;IACpB;EACJ;EACA,OAAOD,IAAI;AACf;AAEA;;;;AAIA,SAASJ,OAAOA,CAAOF,MAAoB,EAAEU,QAAc;EACvD,MAAMC,KAAK,GAAQ,IAAIC,KAAK,CAAC,EAAS,EAAE;IACpCC,cAAc,EAAEA,CAAA,KAAM,KAAK;IAC3BC,GAAG,EAAEA,CAAA,KAAK;MACN,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;IACxE,CAAC;IACDC,GAAG,EAAEA,CAACC,GAAG,EAAEC,IAAI,KAAI;MACf,IAAIA,IAAI,KAAKf,OAAO,EAAE;QAClB,OAAO,IAAI;MACf,CAAC,MAAM;QACH,OAAOgB,QAAQ,CAACF,GAAG,EAAEC,IAAI,EAAElB,MAAM,EAAEU,QAAQ,IAAIC,KAAK,CAAC;MACzD;IACJ,CAAC;IACDS,wBAAwB,EAAEA,CAACH,GAAG,EAAEC,IAAI,MAAMC,QAAQ,CAACF,GAAG,EAAEC,IAAI,EAAElB,MAAM,EAAEU,QAAQ,IAAIC,KAAK,CAAC,EAAEH,MAAM,CAACY,wBAAwB,CAACH,GAAG,EAAEC,IAAI,CAAC,CAAC;IAAE;IACvIG,GAAG,EAAEA,CAACC,CAAC,EAAEJ,IAAI,KAAKA,IAAI,IAAIlB,MAAM;IAAE;IAClCuB,OAAO,EAAEA,CAAA,KAAM,CAAC,GAAGf,MAAM,CAACgB,mBAAmB,CAACxB,MAAM,CAAC,CAAC,CAAC;GAC1D,CAAC;EACF,OAAOW,KAAK;AAChB;AAEA;;;;AAIA,MAAMc,aAAa,GAAGrB,MAAM,EAAE;AAE9B;;;;;;;;;;;;AAYA,SAASe,QAAQA,CAAOF,GAAQ,EAAEC,IAA8B,EAAElB,MAAoB,EAAEU,QAAW;EAC/F,IAAIQ,IAAI,IAAID,GAAG,EAAE;IACb,IAAIA,GAAG,CAACC,IAAI,CAAC,YAAYH,KAAK,EAAE;MAC5B,MAAM,IAAIA,KAAK,CAAC,kFAAkF,EAAE;QAACW,KAAK,EAAET,GAAG,CAACC,IAAI;MAAC,CAAC,CAAC;IAC3H;IACA,IAAID,GAAG,CAACC,IAAI,CAAC,KAAKO,aAAa,EAAE;MAC7B,MAAM,IAAIV,KAAK,CAAC,+BAA+B,GAAGY,MAAM,CAACT,IAAI,CAAC,GAAG,wGAAwG,CAAC;IAC9K;IACA,OAAOD,GAAG,CAACC,IAAI,CAAC;EACpB,CAAC,MAAM,IAAIA,IAAI,IAAIlB,MAAM,EAAE;IACvB,MAAMO,KAAK,GAA0DP,MAAM,CAACkB,IAAe,CAAC;IAC5FD,GAAG,CAACC,IAAI,CAAC,GAAGO,aAAa;IACzB,IAAI;MACAR,GAAG,CAACC,IAAI,CAAC,GAAI,OAAOX,KAAK,KAAK,UAAU,GAAIA,KAAK,CAACG,QAAQ,CAAC,GAAGR,OAAO,CAACK,KAAK,EAAEG,QAAQ,CAAC;IAC1F,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZX,GAAG,CAACC,IAAI,CAAC,GAAGU,KAAK,YAAYb,KAAK,GAAGa,KAAK,GAAGC,SAAS;MACtD,MAAMD,KAAK;IACf;IACA,OAAOX,GAAG,CAACC,IAAI,CAAC;EACpB,CAAC,MAAM;IACH,OAAOW,SAAS;EACpB;AACJ;AAEA;;;;;;;AAOA,SAASxC,MAAMA,CAACyC,MAAmB,EAAEC,MAAoB;EACrD,IAAIA,MAAM,EAAE;IACR,KAAK,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,IAAIzB,MAAM,CAAC0B,OAAO,CAACH,MAAM,CAAC,EAAE;MAChD,IAAIE,MAAM,KAAKJ,SAAS,EAAE;QACtB,MAAMM,MAAM,GAAGL,MAAM,CAACE,GAAG,CAAC;QAC1B,IAAIG,MAAM,KAAK,IAAI,IAAIF,MAAM,KAAK,IAAI,IAAI,OAAOE,MAAM,KAAK,QAAQ,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;UAChGH,MAAM,CAACE,GAAG,CAAC,GAAG3C,MAAM,CAAC8C,MAAM,EAAEF,MAAM,CAAC;QACxC,CAAC,MAAM;UACHH,MAAM,CAACE,GAAG,CAAC,GAAGC,MAAM;QACxB;MACJ;IACJ;EACJ;EACA,OAAOH,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}