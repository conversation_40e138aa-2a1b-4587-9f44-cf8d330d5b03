{"ast": null, "code": "import { clone, compact, difference, flatten, forEach, has, includes, isArray, isEmpty, map } from \"lodash-es\";\nexport function tokenStructuredMatcher(tokInstance, tokConstructor) {\n  const instanceType = tokInstance.tokenTypeIdx;\n  if (instanceType === tokConstructor.tokenTypeIdx) {\n    return true;\n  } else {\n    return tokConstructor.isParent === true && tokConstructor.categoryMatchesMap[instanceType] === true;\n  }\n}\n// Optimized tokenMatcher in case our grammar does not use token categories\n// Being so tiny it is much more likely to be in-lined and this avoid the function call overhead\nexport function tokenStructuredMatcherNoCategories(token, tokType) {\n  return token.tokenTypeIdx === tokType.tokenTypeIdx;\n}\nexport let tokenShortNameIdx = 1;\nexport const tokenIdxToClass = {};\nexport function augmentTokenTypes(tokenTypes) {\n  // collect the parent Token Types as well.\n  const tokenTypesAndParents = expandCategories(tokenTypes);\n  // add required tokenType and categoryMatches properties\n  assignTokenDefaultProps(tokenTypesAndParents);\n  // fill up the categoryMatches\n  assignCategoriesMapProp(tokenTypesAndParents);\n  assignCategoriesTokensProp(tokenTypesAndParents);\n  forEach(tokenTypesAndParents, tokType => {\n    tokType.isParent = tokType.categoryMatches.length > 0;\n  });\n}\nexport function expandCategories(tokenTypes) {\n  let result = clone(tokenTypes);\n  let categories = tokenTypes;\n  let searching = true;\n  while (searching) {\n    categories = compact(flatten(map(categories, currTokType => currTokType.CATEGORIES)));\n    const newCategories = difference(categories, result);\n    result = result.concat(newCategories);\n    if (isEmpty(newCategories)) {\n      searching = false;\n    } else {\n      categories = newCategories;\n    }\n  }\n  return result;\n}\nexport function assignTokenDefaultProps(tokenTypes) {\n  forEach(tokenTypes, currTokType => {\n    if (!hasShortKeyProperty(currTokType)) {\n      tokenIdxToClass[tokenShortNameIdx] = currTokType;\n      currTokType.tokenTypeIdx = tokenShortNameIdx++;\n    }\n    // CATEGORIES? : TokenType | TokenType[]\n    if (hasCategoriesProperty(currTokType) && !isArray(currTokType.CATEGORIES)\n    // &&\n    // !isUndefined(currTokType.CATEGORIES.PATTERN)\n    ) {\n      currTokType.CATEGORIES = [currTokType.CATEGORIES];\n    }\n    if (!hasCategoriesProperty(currTokType)) {\n      currTokType.CATEGORIES = [];\n    }\n    if (!hasExtendingTokensTypesProperty(currTokType)) {\n      currTokType.categoryMatches = [];\n    }\n    if (!hasExtendingTokensTypesMapProperty(currTokType)) {\n      currTokType.categoryMatchesMap = {};\n    }\n  });\n}\nexport function assignCategoriesTokensProp(tokenTypes) {\n  forEach(tokenTypes, currTokType => {\n    // avoid duplications\n    currTokType.categoryMatches = [];\n    forEach(currTokType.categoryMatchesMap, (val, key) => {\n      currTokType.categoryMatches.push(tokenIdxToClass[key].tokenTypeIdx);\n    });\n  });\n}\nexport function assignCategoriesMapProp(tokenTypes) {\n  forEach(tokenTypes, currTokType => {\n    singleAssignCategoriesToksMap([], currTokType);\n  });\n}\nexport function singleAssignCategoriesToksMap(path, nextNode) {\n  forEach(path, pathNode => {\n    nextNode.categoryMatchesMap[pathNode.tokenTypeIdx] = true;\n  });\n  forEach(nextNode.CATEGORIES, nextCategory => {\n    const newPath = path.concat(nextNode);\n    // avoids infinite loops due to cyclic categories.\n    if (!includes(newPath, nextCategory)) {\n      singleAssignCategoriesToksMap(newPath, nextCategory);\n    }\n  });\n}\nexport function hasShortKeyProperty(tokType) {\n  return has(tokType, \"tokenTypeIdx\");\n}\nexport function hasCategoriesProperty(tokType) {\n  return has(tokType, \"CATEGORIES\");\n}\nexport function hasExtendingTokensTypesProperty(tokType) {\n  return has(tokType, \"categoryMatches\");\n}\nexport function hasExtendingTokensTypesMapProperty(tokType) {\n  return has(tokType, \"categoryMatchesMap\");\n}\nexport function isTokenType(tokType) {\n  return has(tokType, \"tokenTypeIdx\");\n}", "map": {"version": 3, "names": ["clone", "compact", "difference", "flatten", "for<PERSON>ach", "has", "includes", "isArray", "isEmpty", "map", "tokenStructuredMatcher", "tokInstance", "tokConstructor", "instanceType", "tokenTypeIdx", "isParent", "categoryMatchesMap", "tokenStructuredMatcherNoCategories", "token", "tokType", "tokenShortNameIdx", "tokenIdxToClass", "augmentTokenTypes", "tokenTypes", "tokenTypesAndParents", "expandCategories", "assignTokenDefaultProps", "assignCategoriesMapProp", "assignCategoriesTokensProp", "categoryMatches", "length", "result", "categories", "searching", "currTokType", "CATEGORIES", "newCategories", "concat", "hasShortKeyProperty", "hasCategoriesProperty", "hasExtendingTokensTypesProperty", "hasExtendingTokensTypesMapProperty", "val", "key", "push", "singleAssignCategoriesToksMap", "path", "nextNode", "pathNode", "nextCategory", "newPath", "isTokenType"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/tokens.ts"], "sourcesContent": ["import {\n  clone,\n  compact,\n  difference,\n  flatten,\n  forEach,\n  has,\n  includes,\n  isArray,\n  isEmpty,\n  map,\n} from \"lodash-es\";\nimport { IToken, TokenType } from \"@chevrotain/types\";\n\nexport function tokenStructuredMatcher(\n  tokInstance: IToken,\n  tokConstructor: TokenType,\n) {\n  const instanceType = tokInstance.tokenTypeIdx;\n  if (instanceType === tokConstructor.tokenTypeIdx) {\n    return true;\n  } else {\n    return (\n      tokConstructor.isParent === true &&\n      tokConstructor.categoryMatchesMap![instanceType] === true\n    );\n  }\n}\n\n// Optimized tokenMatcher in case our grammar does not use token categories\n// Being so tiny it is much more likely to be in-lined and this avoid the function call overhead\nexport function tokenStructuredMatcherNoCategories(\n  token: IToken,\n  tokType: TokenType,\n) {\n  return token.tokenTypeIdx === tokType.tokenTypeIdx;\n}\n\nexport let tokenShortNameIdx = 1;\nexport const tokenIdxToClass: { [tokenIdx: number]: TokenType } = {};\n\nexport function augmentTokenTypes(tokenTypes: TokenType[]): void {\n  // collect the parent Token Types as well.\n  const tokenTypesAndParents = expandCategories(tokenTypes);\n\n  // add required tokenType and categoryMatches properties\n  assignTokenDefaultProps(tokenTypesAndParents);\n\n  // fill up the categoryMatches\n  assignCategoriesMapProp(tokenTypesAndParents);\n  assignCategoriesTokensProp(tokenTypesAndParents);\n\n  forEach(tokenTypesAndParents, (tokType) => {\n    tokType.isParent = tokType.categoryMatches!.length > 0;\n  });\n}\n\nexport function expandCategories(tokenTypes: TokenType[]): TokenType[] {\n  let result = clone(tokenTypes);\n\n  let categories = tokenTypes;\n  let searching = true;\n  while (searching) {\n    categories = compact(\n      flatten(map(categories, (currTokType) => currTokType.CATEGORIES)),\n    );\n\n    const newCategories = difference(categories, result);\n\n    result = result.concat(newCategories);\n\n    if (isEmpty(newCategories)) {\n      searching = false;\n    } else {\n      categories = newCategories;\n    }\n  }\n  return result;\n}\n\nexport function assignTokenDefaultProps(tokenTypes: TokenType[]): void {\n  forEach(tokenTypes, (currTokType) => {\n    if (!hasShortKeyProperty(currTokType)) {\n      tokenIdxToClass[tokenShortNameIdx] = currTokType;\n      (<any>currTokType).tokenTypeIdx = tokenShortNameIdx++;\n    }\n\n    // CATEGORIES? : TokenType | TokenType[]\n    if (\n      hasCategoriesProperty(currTokType) &&\n      !isArray(currTokType.CATEGORIES)\n      // &&\n      // !isUndefined(currTokType.CATEGORIES.PATTERN)\n    ) {\n      currTokType.CATEGORIES = [currTokType.CATEGORIES as unknown as TokenType];\n    }\n\n    if (!hasCategoriesProperty(currTokType)) {\n      currTokType.CATEGORIES = [];\n    }\n\n    if (!hasExtendingTokensTypesProperty(currTokType)) {\n      currTokType.categoryMatches = [];\n    }\n\n    if (!hasExtendingTokensTypesMapProperty(currTokType)) {\n      currTokType.categoryMatchesMap = {};\n    }\n  });\n}\n\nexport function assignCategoriesTokensProp(tokenTypes: TokenType[]): void {\n  forEach(tokenTypes, (currTokType) => {\n    // avoid duplications\n    currTokType.categoryMatches = [];\n    forEach(currTokType.categoryMatchesMap!, (val, key) => {\n      currTokType.categoryMatches!.push(\n        tokenIdxToClass[key as unknown as number].tokenTypeIdx!,\n      );\n    });\n  });\n}\n\nexport function assignCategoriesMapProp(tokenTypes: TokenType[]): void {\n  forEach(tokenTypes, (currTokType) => {\n    singleAssignCategoriesToksMap([], currTokType);\n  });\n}\n\nexport function singleAssignCategoriesToksMap(\n  path: TokenType[],\n  nextNode: TokenType,\n): void {\n  forEach(path, (pathNode) => {\n    nextNode.categoryMatchesMap![pathNode.tokenTypeIdx!] = true;\n  });\n\n  forEach(nextNode.CATEGORIES, (nextCategory) => {\n    const newPath = path.concat(nextNode);\n    // avoids infinite loops due to cyclic categories.\n    if (!includes(newPath, nextCategory)) {\n      singleAssignCategoriesToksMap(newPath, nextCategory);\n    }\n  });\n}\n\nexport function hasShortKeyProperty(tokType: TokenType): boolean {\n  return has(tokType, \"tokenTypeIdx\");\n}\n\nexport function hasCategoriesProperty(tokType: TokenType): boolean {\n  return has(tokType, \"CATEGORIES\");\n}\n\nexport function hasExtendingTokensTypesProperty(tokType: TokenType): boolean {\n  return has(tokType, \"categoryMatches\");\n}\n\nexport function hasExtendingTokensTypesMapProperty(\n  tokType: TokenType,\n): boolean {\n  return has(tokType, \"categoryMatchesMap\");\n}\n\nexport function isTokenType(tokType: TokenType): boolean {\n  return has(tokType, \"tokenTypeIdx\");\n}\n"], "mappings": "AAAA,SACEA,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,GAAG,QACE,WAAW;AAGlB,OAAM,SAAUC,sBAAsBA,CACpCC,WAAmB,EACnBC,cAAyB;EAEzB,MAAMC,YAAY,GAAGF,WAAW,CAACG,YAAY;EAC7C,IAAID,YAAY,KAAKD,cAAc,CAACE,YAAY,EAAE;IAChD,OAAO,IAAI;GACZ,MAAM;IACL,OACEF,cAAc,CAACG,QAAQ,KAAK,IAAI,IAChCH,cAAc,CAACI,kBAAmB,CAACH,YAAY,CAAC,KAAK,IAAI;;AAG/D;AAEA;AACA;AACA,OAAM,SAAUI,kCAAkCA,CAChDC,KAAa,EACbC,OAAkB;EAElB,OAAOD,KAAK,CAACJ,YAAY,KAAKK,OAAO,CAACL,YAAY;AACpD;AAEA,OAAO,IAAIM,iBAAiB,GAAG,CAAC;AAChC,OAAO,MAAMC,eAAe,GAAsC,EAAE;AAEpE,OAAM,SAAUC,iBAAiBA,CAACC,UAAuB;EACvD;EACA,MAAMC,oBAAoB,GAAGC,gBAAgB,CAACF,UAAU,CAAC;EAEzD;EACAG,uBAAuB,CAACF,oBAAoB,CAAC;EAE7C;EACAG,uBAAuB,CAACH,oBAAoB,CAAC;EAC7CI,0BAA0B,CAACJ,oBAAoB,CAAC;EAEhDpB,OAAO,CAACoB,oBAAoB,EAAGL,OAAO,IAAI;IACxCA,OAAO,CAACJ,QAAQ,GAAGI,OAAO,CAACU,eAAgB,CAACC,MAAM,GAAG,CAAC;EACxD,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUL,gBAAgBA,CAACF,UAAuB;EACtD,IAAIQ,MAAM,GAAG/B,KAAK,CAACuB,UAAU,CAAC;EAE9B,IAAIS,UAAU,GAAGT,UAAU;EAC3B,IAAIU,SAAS,GAAG,IAAI;EACpB,OAAOA,SAAS,EAAE;IAChBD,UAAU,GAAG/B,OAAO,CAClBE,OAAO,CAACM,GAAG,CAACuB,UAAU,EAAGE,WAAW,IAAKA,WAAW,CAACC,UAAU,CAAC,CAAC,CAClE;IAED,MAAMC,aAAa,GAAGlC,UAAU,CAAC8B,UAAU,EAAED,MAAM,CAAC;IAEpDA,MAAM,GAAGA,MAAM,CAACM,MAAM,CAACD,aAAa,CAAC;IAErC,IAAI5B,OAAO,CAAC4B,aAAa,CAAC,EAAE;MAC1BH,SAAS,GAAG,KAAK;KAClB,MAAM;MACLD,UAAU,GAAGI,aAAa;;;EAG9B,OAAOL,MAAM;AACf;AAEA,OAAM,SAAUL,uBAAuBA,CAACH,UAAuB;EAC7DnB,OAAO,CAACmB,UAAU,EAAGW,WAAW,IAAI;IAClC,IAAI,CAACI,mBAAmB,CAACJ,WAAW,CAAC,EAAE;MACrCb,eAAe,CAACD,iBAAiB,CAAC,GAAGc,WAAW;MAC1CA,WAAY,CAACpB,YAAY,GAAGM,iBAAiB,EAAE;;IAGvD;IACA,IACEmB,qBAAqB,CAACL,WAAW,CAAC,IAClC,CAAC3B,OAAO,CAAC2B,WAAW,CAACC,UAAU;IAC/B;IACA;IAAA,EACA;MACAD,WAAW,CAACC,UAAU,GAAG,CAACD,WAAW,CAACC,UAAkC,CAAC;;IAG3E,IAAI,CAACI,qBAAqB,CAACL,WAAW,CAAC,EAAE;MACvCA,WAAW,CAACC,UAAU,GAAG,EAAE;;IAG7B,IAAI,CAACK,+BAA+B,CAACN,WAAW,CAAC,EAAE;MACjDA,WAAW,CAACL,eAAe,GAAG,EAAE;;IAGlC,IAAI,CAACY,kCAAkC,CAACP,WAAW,CAAC,EAAE;MACpDA,WAAW,CAAClB,kBAAkB,GAAG,EAAE;;EAEvC,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUY,0BAA0BA,CAACL,UAAuB;EAChEnB,OAAO,CAACmB,UAAU,EAAGW,WAAW,IAAI;IAClC;IACAA,WAAW,CAACL,eAAe,GAAG,EAAE;IAChCzB,OAAO,CAAC8B,WAAW,CAAClB,kBAAmB,EAAE,CAAC0B,GAAG,EAAEC,GAAG,KAAI;MACpDT,WAAW,CAACL,eAAgB,CAACe,IAAI,CAC/BvB,eAAe,CAACsB,GAAwB,CAAC,CAAC7B,YAAa,CACxD;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUa,uBAAuBA,CAACJ,UAAuB;EAC7DnB,OAAO,CAACmB,UAAU,EAAGW,WAAW,IAAI;IAClCW,6BAA6B,CAAC,EAAE,EAAEX,WAAW,CAAC;EAChD,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUW,6BAA6BA,CAC3CC,IAAiB,EACjBC,QAAmB;EAEnB3C,OAAO,CAAC0C,IAAI,EAAGE,QAAQ,IAAI;IACzBD,QAAQ,CAAC/B,kBAAmB,CAACgC,QAAQ,CAAClC,YAAa,CAAC,GAAG,IAAI;EAC7D,CAAC,CAAC;EAEFV,OAAO,CAAC2C,QAAQ,CAACZ,UAAU,EAAGc,YAAY,IAAI;IAC5C,MAAMC,OAAO,GAAGJ,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC;IACrC;IACA,IAAI,CAACzC,QAAQ,CAAC4C,OAAO,EAAED,YAAY,CAAC,EAAE;MACpCJ,6BAA6B,CAACK,OAAO,EAAED,YAAY,CAAC;;EAExD,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUX,mBAAmBA,CAACnB,OAAkB;EACpD,OAAOd,GAAG,CAACc,OAAO,EAAE,cAAc,CAAC;AACrC;AAEA,OAAM,SAAUoB,qBAAqBA,CAACpB,OAAkB;EACtD,OAAOd,GAAG,CAACc,OAAO,EAAE,YAAY,CAAC;AACnC;AAEA,OAAM,SAAUqB,+BAA+BA,CAACrB,OAAkB;EAChE,OAAOd,GAAG,CAACc,OAAO,EAAE,iBAAiB,CAAC;AACxC;AAEA,OAAM,SAAUsB,kCAAkCA,CAChDtB,OAAkB;EAElB,OAAOd,GAAG,CAACc,OAAO,EAAE,oBAAoB,CAAC;AAC3C;AAEA,OAAM,SAAUgC,WAAWA,CAAChC,OAAkB;EAC5C,OAAOd,GAAG,CAACc,OAAO,EAAE,cAAc,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}