{"ast": null, "code": "import { path } from \"d3-path\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport { x as pointX, y as pointY } from \"./point.js\";\nexport default function () {\n  var x0 = pointX,\n    x1 = null,\n    y0 = constant(0),\n    y1 = pointY,\n    defined = constant(true),\n    context = null,\n    curve = curveLinear,\n    output = null;\n  function area(data) {\n    var i,\n      j,\n      k,\n      n = data.length,\n      d,\n      defined0 = false,\n      buffer,\n      x0z = new Array(n),\n      y0z = new Array(n);\n    if (context == null) output = curve(buffer = path());\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n  area.x = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n  area.x0 = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n  area.x1 = function (_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n  area.y = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n  area.y0 = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n  area.y1 = function (_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n  area.lineX0 = area.lineY0 = function () {\n    return arealine().x(x0).y(y0);\n  };\n  area.lineY1 = function () {\n    return arealine().x(x0).y(y1);\n  };\n  area.lineX1 = function () {\n    return arealine().x(x1).y(y0);\n  };\n  area.defined = function (_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n  area.curve = function (_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n  area.context = function (_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n  return area;\n}", "map": {"version": 3, "names": ["path", "constant", "curveLinear", "line", "x", "pointX", "y", "pointY", "x0", "x1", "y0", "y1", "defined", "context", "curve", "output", "area", "data", "i", "j", "k", "n", "length", "d", "defined0", "buffer", "x0z", "Array", "y0z", "areaStart", "lineStart", "lineEnd", "point", "areaEnd", "arealine", "_", "arguments", "lineX0", "lineY0", "lineY1", "lineX1"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/area.js"], "sourcesContent": ["import {path} from \"d3-path\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function() {\n  var x0 = pointX,\n      x1 = null,\n      y0 = constant(0),\n      y1 = pointY,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null;\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = data.length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,SAAS;AAC5B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAAQC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,MAAM,QAAO,YAAY;AAEnD,eAAe,YAAW;EACxB,IAAIC,EAAE,GAAGH,MAAM;IACXI,EAAE,GAAG,IAAI;IACTC,EAAE,GAAGT,QAAQ,CAAC,CAAC,CAAC;IAChBU,EAAE,GAAGJ,MAAM;IACXK,OAAO,GAAGX,QAAQ,CAAC,IAAI,CAAC;IACxBY,OAAO,GAAG,IAAI;IACdC,KAAK,GAAGZ,WAAW;IACnBa,MAAM,GAAG,IAAI;EAEjB,SAASC,IAAIA,CAACC,IAAI,EAAE;IAClB,IAAIC,CAAC;MACDC,CAAC;MACDC,CAAC;MACDC,CAAC,GAAGJ,IAAI,CAACK,MAAM;MACfC,CAAC;MACDC,QAAQ,GAAG,KAAK;MAChBC,MAAM;MACNC,GAAG,GAAG,IAAIC,KAAK,CAACN,CAAC,CAAC;MAClBO,GAAG,GAAG,IAAID,KAAK,CAACN,CAAC,CAAC;IAEtB,IAAIR,OAAO,IAAI,IAAI,EAAEE,MAAM,GAAGD,KAAK,CAACW,MAAM,GAAGzB,IAAI,CAAC,CAAC,CAAC;IAEpD,KAAKkB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACvB,IAAI,EAAEA,CAAC,GAAGG,CAAC,IAAIT,OAAO,CAACW,CAAC,GAAGN,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,CAAC,KAAKO,QAAQ,EAAE;QAC1D,IAAIA,QAAQ,GAAG,CAACA,QAAQ,EAAE;UACxBL,CAAC,GAAGD,CAAC;UACLH,MAAM,CAACc,SAAS,CAAC,CAAC;UAClBd,MAAM,CAACe,SAAS,CAAC,CAAC;QACpB,CAAC,MAAM;UACLf,MAAM,CAACgB,OAAO,CAAC,CAAC;UAChBhB,MAAM,CAACe,SAAS,CAAC,CAAC;UAClB,KAAKV,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAID,CAAC,EAAE,EAAEC,CAAC,EAAE;YAC3BL,MAAM,CAACiB,KAAK,CAACN,GAAG,CAACN,CAAC,CAAC,EAAEQ,GAAG,CAACR,CAAC,CAAC,CAAC;UAC9B;UACAL,MAAM,CAACgB,OAAO,CAAC,CAAC;UAChBhB,MAAM,CAACkB,OAAO,CAAC,CAAC;QAClB;MACF;MACA,IAAIT,QAAQ,EAAE;QACZE,GAAG,CAACR,CAAC,CAAC,GAAG,CAACV,EAAE,CAACe,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,EAAEW,GAAG,CAACV,CAAC,CAAC,GAAG,CAACR,EAAE,CAACa,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC;QAClDF,MAAM,CAACiB,KAAK,CAACvB,EAAE,GAAG,CAACA,EAAE,CAACc,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGS,GAAG,CAACR,CAAC,CAAC,EAAEP,EAAE,GAAG,CAACA,EAAE,CAACY,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGW,GAAG,CAACV,CAAC,CAAC,CAAC;MAC5E;IACF;IAEA,IAAIO,MAAM,EAAE,OAAOV,MAAM,GAAG,IAAI,EAAEU,MAAM,GAAG,EAAE,IAAI,IAAI;EACvD;EAEA,SAASS,QAAQA,CAAA,EAAG;IAClB,OAAO/B,IAAI,CAAC,CAAC,CAACS,OAAO,CAACA,OAAO,CAAC,CAACE,KAAK,CAACA,KAAK,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;EAC9D;EAEAG,IAAI,CAACZ,CAAC,GAAG,UAAS+B,CAAC,EAAE;IACnB,OAAOC,SAAS,CAACd,MAAM,IAAId,EAAE,GAAG,OAAO2B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAE1B,EAAE,GAAG,IAAI,EAAEO,IAAI,IAAIR,EAAE;EACnG,CAAC;EAEDQ,IAAI,CAACR,EAAE,GAAG,UAAS2B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAId,EAAE,GAAG,OAAO2B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAEnB,IAAI,IAAIR,EAAE;EACxF,CAAC;EAEDQ,IAAI,CAACP,EAAE,GAAG,UAAS0B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIb,EAAE,GAAG0B,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAEnB,IAAI,IAAIP,EAAE;EAC3G,CAAC;EAEDO,IAAI,CAACV,CAAC,GAAG,UAAS6B,CAAC,EAAE;IACnB,OAAOC,SAAS,CAACd,MAAM,IAAIZ,EAAE,GAAG,OAAOyB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAExB,EAAE,GAAG,IAAI,EAAEK,IAAI,IAAIN,EAAE;EACnG,CAAC;EAEDM,IAAI,CAACN,EAAE,GAAG,UAASyB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIZ,EAAE,GAAG,OAAOyB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAEnB,IAAI,IAAIN,EAAE;EACxF,CAAC;EAEDM,IAAI,CAACL,EAAE,GAAG,UAASwB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIX,EAAE,GAAGwB,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAEnB,IAAI,IAAIL,EAAE;EAC3G,CAAC;EAEDK,IAAI,CAACqB,MAAM,GACXrB,IAAI,CAACsB,MAAM,GAAG,YAAW;IACvB,OAAOJ,QAAQ,CAAC,CAAC,CAAC9B,CAAC,CAACI,EAAE,CAAC,CAACF,CAAC,CAACI,EAAE,CAAC;EAC/B,CAAC;EAEDM,IAAI,CAACuB,MAAM,GAAG,YAAW;IACvB,OAAOL,QAAQ,CAAC,CAAC,CAAC9B,CAAC,CAACI,EAAE,CAAC,CAACF,CAAC,CAACK,EAAE,CAAC;EAC/B,CAAC;EAEDK,IAAI,CAACwB,MAAM,GAAG,YAAW;IACvB,OAAON,QAAQ,CAAC,CAAC,CAAC9B,CAAC,CAACK,EAAE,CAAC,CAACH,CAAC,CAACI,EAAE,CAAC;EAC/B,CAAC;EAEDM,IAAI,CAACJ,OAAO,GAAG,UAASuB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACd,MAAM,IAAIV,OAAO,GAAG,OAAOuB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAACkC,CAAC,CAAC,EAAEnB,IAAI,IAAIJ,OAAO;EACnG,CAAC;EAEDI,IAAI,CAACF,KAAK,GAAG,UAASqB,CAAC,EAAE;IACvB,OAAOC,SAAS,CAACd,MAAM,IAAIR,KAAK,GAAGqB,CAAC,EAAEtB,OAAO,IAAI,IAAI,KAAKE,MAAM,GAAGD,KAAK,CAACD,OAAO,CAAC,CAAC,EAAEG,IAAI,IAAIF,KAAK;EACnG,CAAC;EAEDE,IAAI,CAACH,OAAO,GAAG,UAASsB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACd,MAAM,IAAIa,CAAC,IAAI,IAAI,GAAGtB,OAAO,GAAGE,MAAM,GAAG,IAAI,GAAGA,MAAM,GAAGD,KAAK,CAACD,OAAO,GAAGsB,CAAC,CAAC,EAAEnB,IAAI,IAAIH,OAAO;EAC/G,CAAC;EAED,OAAOG,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}