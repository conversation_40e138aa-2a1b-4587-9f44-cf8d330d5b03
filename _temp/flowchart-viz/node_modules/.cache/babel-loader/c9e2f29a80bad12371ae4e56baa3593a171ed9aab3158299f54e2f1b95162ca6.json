{"ast": null, "code": "function removeFunction(id) {\n  return function () {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\nexport default function () {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}", "map": {"version": 3, "names": ["removeFunction", "id", "parent", "parentNode", "i", "__transition", "<PERSON><PERSON><PERSON><PERSON>", "on", "_id"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-transition/src/transition/remove.js"], "sourcesContent": ["function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,EAAE,EAAE;EAC1B,OAAO,YAAW;IAChB,IAAIC,MAAM,GAAG,IAAI,CAACC,UAAU;IAC5B,KAAK,IAAIC,CAAC,IAAI,IAAI,CAACC,YAAY,EAAE,IAAI,CAACD,CAAC,KAAKH,EAAE,EAAE;IAChD,IAAIC,MAAM,EAAEA,MAAM,CAACI,WAAW,CAAC,IAAI,CAAC;EACtC,CAAC;AACH;AAEA,eAAe,YAAW;EACxB,OAAO,IAAI,CAACC,EAAE,CAAC,YAAY,EAAEP,cAAc,CAAC,IAAI,CAACQ,GAAG,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}