{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { isLinkingError } from '../syntax-tree.js';\nimport { getDocument, streamAst, streamReferences } from '../utils/ast-utils.js';\nimport { toDocumentSegment } from '../utils/cst-utils.js';\nimport { interruptAndCheck } from '../utils/promise-utils.js';\nimport { UriUtils } from '../utils/uri-utils.js';\nexport class DefaultAstNodeDescriptionProvider {\n  constructor(services) {\n    this.astNodeLocator = services.workspace.AstNodeLocator;\n    this.nameProvider = services.references.NameProvider;\n  }\n  createDescription(node, name, document) {\n    const doc = document !== null && document !== void 0 ? document : getDocument(node);\n    name !== null && name !== void 0 ? name : name = this.nameProvider.getName(node);\n    const path = this.astNodeLocator.getAstNodePath(node);\n    if (!name) {\n      throw new Error(`Node at path ${path} has no name.`);\n    }\n    let nameNodeSegment;\n    const nameSegmentGetter = () => {\n      var _a;\n      return nameNodeSegment !== null && nameNodeSegment !== void 0 ? nameNodeSegment : nameNodeSegment = toDocumentSegment((_a = this.nameProvider.getNameNode(node)) !== null && _a !== void 0 ? _a : node.$cstNode);\n    };\n    return {\n      node,\n      name,\n      get nameSegment() {\n        return nameSegmentGetter();\n      },\n      selectionSegment: toDocumentSegment(node.$cstNode),\n      type: node.$type,\n      documentUri: doc.uri,\n      path\n    };\n  }\n}\nexport class DefaultReferenceDescriptionProvider {\n  constructor(services) {\n    this.nodeLocator = services.workspace.AstNodeLocator;\n  }\n  async createDescriptions(document, cancelToken = CancellationToken.None) {\n    const descr = [];\n    const rootNode = document.parseResult.value;\n    for (const astNode of streamAst(rootNode)) {\n      await interruptAndCheck(cancelToken);\n      streamReferences(astNode).filter(refInfo => !isLinkingError(refInfo)).forEach(refInfo => {\n        // TODO: Consider logging a warning or throw an exception when DocumentState is < than Linked\n        const description = this.createDescription(refInfo);\n        if (description) {\n          descr.push(description);\n        }\n      });\n    }\n    return descr;\n  }\n  createDescription(refInfo) {\n    const targetNodeDescr = refInfo.reference.$nodeDescription;\n    const refCstNode = refInfo.reference.$refNode;\n    if (!targetNodeDescr || !refCstNode) {\n      return undefined;\n    }\n    const docUri = getDocument(refInfo.container).uri;\n    return {\n      sourceUri: docUri,\n      sourcePath: this.nodeLocator.getAstNodePath(refInfo.container),\n      targetUri: targetNodeDescr.documentUri,\n      targetPath: targetNodeDescr.path,\n      segment: toDocumentSegment(refCstNode),\n      local: UriUtils.equals(targetNodeDescr.documentUri, docUri)\n    };\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "isLinkingError", "getDocument", "streamAst", "streamReferences", "toDocumentSegment", "interruptAndCheck", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultAstNodeDescriptionProvider", "constructor", "services", "astNodeLocator", "workspace", "AstNodeLocator", "nameProvider", "references", "Name<PERSON>rovider", "createDescription", "node", "name", "document", "doc", "getName", "path", "getAstNodePath", "Error", "nameNodeSegment", "nameSegmentGetter", "_a", "getNameNode", "$cstNode", "nameSegment", "selectionSegment", "type", "$type", "documentUri", "uri", "DefaultReferenceDescriptionProvider", "nodeLocator", "createDescriptions", "cancelToken", "None", "descr", "rootNode", "parseResult", "value", "astNode", "filter", "refInfo", "for<PERSON>ach", "description", "push", "targetNodeDescr", "reference", "$nodeDescription", "refCstNode", "$refNode", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "container", "sourceUri", "sourcePath", "targetUri", "targetPath", "segment", "local", "equals"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/ast-descriptions.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { URI } from '../utils/uri-utils.js';\r\nimport type { NameProvider } from '../references/name-provider.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, AstNodeDescription, ReferenceInfo } from '../syntax-tree.js';\r\nimport type { AstNodeLocator } from './ast-node-locator.js';\r\nimport type { DocumentSegment, LangiumDocument } from './documents.js';\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { isLinkingError } from '../syntax-tree.js';\r\nimport { getDocument, streamAst, streamReferences } from '../utils/ast-utils.js';\r\nimport { toDocumentSegment } from '../utils/cst-utils.js';\r\nimport { interruptAndCheck } from '../utils/promise-utils.js';\r\nimport { UriUtils } from '../utils/uri-utils.js';\r\n\r\n/**\r\n * Language-specific service for creating descriptions of AST nodes to be used for cross-reference resolutions.\r\n */\r\nexport interface AstNodeDescriptionProvider {\r\n\r\n    /**\r\n     * Create a description for the given AST node. This service method is typically used while indexing\r\n     * the contents of a document and during scope computation.\r\n     *\r\n     * @param node An AST node.\r\n     * @param name The name to be used to refer to the AST node. By default, this is determined by the\r\n     *     `NameProvider` service, but alternative names may be provided according to the semantics\r\n     *     of your language.\r\n     * @param document The document containing the AST node. If omitted, it is taken from the root AST node.\r\n     */\r\n    createDescription(node: AstNode, name: string | undefined, document?: LangiumDocument): AstNodeDescription;\r\n\r\n}\r\n\r\nexport class DefaultAstNodeDescriptionProvider implements AstNodeDescriptionProvider {\r\n\r\n    protected readonly astNodeLocator: AstNodeLocator;\r\n    protected readonly nameProvider: NameProvider;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.astNodeLocator = services.workspace.AstNodeLocator;\r\n        this.nameProvider = services.references.NameProvider;\r\n    }\r\n\r\n    createDescription(node: AstNode, name: string | undefined, document?: LangiumDocument): AstNodeDescription {\r\n        const doc = document ?? getDocument(node);\r\n        name ??= this.nameProvider.getName(node);\r\n        const path = this.astNodeLocator.getAstNodePath(node);\r\n        if (!name) {\r\n            throw new Error(`Node at path ${path} has no name.`);\r\n        }\r\n        let nameNodeSegment: DocumentSegment | undefined;\r\n        const nameSegmentGetter = () => nameNodeSegment ??= toDocumentSegment(this.nameProvider.getNameNode(node) ?? node.$cstNode);\r\n        return {\r\n            node,\r\n            name,\r\n            get nameSegment() {\r\n                return nameSegmentGetter();\r\n            },\r\n            selectionSegment: toDocumentSegment(node.$cstNode),\r\n            type: node.$type,\r\n            documentUri: doc.uri,\r\n            path\r\n        };\r\n    }\r\n\r\n}\r\n\r\n/**\r\n * Describes a cross-reference within a document or between two documents.\r\n */\r\nexport interface ReferenceDescription {\r\n    /** URI of the document that holds a reference */\r\n    sourceUri: URI\r\n    /** Path to AstNode that holds a reference */\r\n    sourcePath: string\r\n    /** Target document uri */\r\n    targetUri: URI\r\n    /** Path to the target AstNode inside the document */\r\n    targetPath: string\r\n    /** Segment of the reference text. */\r\n    segment: DocumentSegment\r\n    /** Marks a local reference i.e. a cross reference inside a document.   */\r\n    local?: boolean\r\n}\r\n\r\n/**\r\n * Language-specific service to create descriptions of all cross-references in a document. These are used by the `IndexManager`\r\n * to determine which documents are affected and should be rebuilt when a document is changed.\r\n */\r\nexport interface ReferenceDescriptionProvider {\r\n    /**\r\n     * Create descriptions of all cross-references found in the given document. These descriptions are\r\n     * gathered by the `IndexManager` and stored in the global index so they can be considered when\r\n     * a document change is reported by the client.\r\n     *\r\n     * @param document The document in which to gather cross-references.\r\n     * @param cancelToken Indicates when to cancel the current operation.\r\n     * @throws `OperationCanceled` if a user action occurs during execution\r\n     */\r\n    createDescriptions(document: LangiumDocument, cancelToken?: CancellationToken): Promise<ReferenceDescription[]>;\r\n}\r\n\r\nexport class DefaultReferenceDescriptionProvider implements ReferenceDescriptionProvider {\r\n\r\n    protected readonly nodeLocator: AstNodeLocator;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.nodeLocator = services.workspace.AstNodeLocator;\r\n    }\r\n\r\n    async createDescriptions(document: LangiumDocument, cancelToken = CancellationToken.None): Promise<ReferenceDescription[]> {\r\n        const descr: ReferenceDescription[] = [];\r\n        const rootNode = document.parseResult.value;\r\n        for (const astNode of streamAst(rootNode)) {\r\n            await interruptAndCheck(cancelToken);\r\n            streamReferences(astNode).filter(refInfo => !isLinkingError(refInfo)).forEach(refInfo => {\r\n                // TODO: Consider logging a warning or throw an exception when DocumentState is < than Linked\r\n                const description = this.createDescription(refInfo);\r\n                if (description) {\r\n                    descr.push(description);\r\n                }\r\n            });\r\n        }\r\n        return descr;\r\n    }\r\n\r\n    protected createDescription(refInfo: ReferenceInfo): ReferenceDescription | undefined {\r\n        const targetNodeDescr = refInfo.reference.$nodeDescription;\r\n        const refCstNode = refInfo.reference.$refNode;\r\n        if (!targetNodeDescr || !refCstNode) {\r\n            return undefined;\r\n        }\r\n        const docUri = getDocument(refInfo.container).uri;\r\n        return {\r\n            sourceUri: docUri,\r\n            sourcePath: this.nodeLocator.getAstNodePath(refInfo.container),\r\n            targetUri: targetNodeDescr.documentUri,\r\n            targetPath: targetNodeDescr.path,\r\n            segment: toDocumentSegment(refCstNode),\r\n            local: UriUtils.equals(targetNodeDescr.documentUri, docUri)\r\n        };\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAYA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,uBAAuB;AAChF,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAqBhD,OAAM,MAAOC,iCAAiC;EAK1CC,YAAYC,QAA6B;IACrC,IAAI,CAACC,cAAc,GAAGD,QAAQ,CAACE,SAAS,CAACC,cAAc;IACvD,IAAI,CAACC,YAAY,GAAGJ,QAAQ,CAACK,UAAU,CAACC,YAAY;EACxD;EAEAC,iBAAiBA,CAACC,IAAa,EAAEC,IAAwB,EAAEC,QAA0B;IACjF,MAAMC,GAAG,GAAGD,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAIlB,WAAW,CAACgB,IAAI,CAAC;IACzCC,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAJA,IAAI,GAAK,IAAI,CAACL,YAAY,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACxC,MAAMK,IAAI,GAAG,IAAI,CAACZ,cAAc,CAACa,cAAc,CAACN,IAAI,CAAC;IACrD,IAAI,CAACC,IAAI,EAAE;MACP,MAAM,IAAIM,KAAK,CAAC,gBAAgBF,IAAI,eAAe,CAAC;IACxD;IACA,IAAIG,eAA4C;IAChD,MAAMC,iBAAiB,GAAGA,CAAA,KAAK;MAAA,IAAAC,EAAA;MAAA,OAACF,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAfA,eAAe,GAAKrB,iBAAiB,CAAC,CAAAuB,EAAA,OAAI,CAACd,YAAY,CAACe,WAAW,CAACX,IAAI,CAAC,cAAAU,EAAA,cAAAA,EAAA,GAAIV,IAAI,CAACY,QAAQ,CAAC;IAAA;IAC3H,OAAO;MACHZ,IAAI;MACJC,IAAI;MACJ,IAAIY,WAAWA,CAAA;QACX,OAAOJ,iBAAiB,EAAE;MAC9B,CAAC;MACDK,gBAAgB,EAAE3B,iBAAiB,CAACa,IAAI,CAACY,QAAQ,CAAC;MAClDG,IAAI,EAAEf,IAAI,CAACgB,KAAK;MAChBC,WAAW,EAAEd,GAAG,CAACe,GAAG;MACpBb;KACH;EACL;;AAuCJ,OAAM,MAAOc,mCAAmC;EAI5C5B,YAAYC,QAA6B;IACrC,IAAI,CAAC4B,WAAW,GAAG5B,QAAQ,CAACE,SAAS,CAACC,cAAc;EACxD;EAEA,MAAM0B,kBAAkBA,CAACnB,QAAyB,EAAEoB,WAAW,GAAGxC,iBAAiB,CAACyC,IAAI;IACpF,MAAMC,KAAK,GAA2B,EAAE;IACxC,MAAMC,QAAQ,GAAGvB,QAAQ,CAACwB,WAAW,CAACC,KAAK;IAC3C,KAAK,MAAMC,OAAO,IAAI3C,SAAS,CAACwC,QAAQ,CAAC,EAAE;MACvC,MAAMrC,iBAAiB,CAACkC,WAAW,CAAC;MACpCpC,gBAAgB,CAAC0C,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,IAAI,CAAC/C,cAAc,CAAC+C,OAAO,CAAC,CAAC,CAACC,OAAO,CAACD,OAAO,IAAG;QACpF;QACA,MAAME,WAAW,GAAG,IAAI,CAACjC,iBAAiB,CAAC+B,OAAO,CAAC;QACnD,IAAIE,WAAW,EAAE;UACbR,KAAK,CAACS,IAAI,CAACD,WAAW,CAAC;QAC3B;MACJ,CAAC,CAAC;IACN;IACA,OAAOR,KAAK;EAChB;EAEUzB,iBAAiBA,CAAC+B,OAAsB;IAC9C,MAAMI,eAAe,GAAGJ,OAAO,CAACK,SAAS,CAACC,gBAAgB;IAC1D,MAAMC,UAAU,GAAGP,OAAO,CAACK,SAAS,CAACG,QAAQ;IAC7C,IAAI,CAACJ,eAAe,IAAI,CAACG,UAAU,EAAE;MACjC,OAAOE,SAAS;IACpB;IACA,MAAMC,MAAM,GAAGxD,WAAW,CAAC8C,OAAO,CAACW,SAAS,CAAC,CAACvB,GAAG;IACjD,OAAO;MACHwB,SAAS,EAAEF,MAAM;MACjBG,UAAU,EAAE,IAAI,CAACvB,WAAW,CAACd,cAAc,CAACwB,OAAO,CAACW,SAAS,CAAC;MAC9DG,SAAS,EAAEV,eAAe,CAACjB,WAAW;MACtC4B,UAAU,EAAEX,eAAe,CAAC7B,IAAI;MAChCyC,OAAO,EAAE3D,iBAAiB,CAACkD,UAAU,CAAC;MACtCU,KAAK,EAAE1D,QAAQ,CAAC2D,MAAM,CAACd,eAAe,CAACjB,WAAW,EAAEuB,MAAM;KAC7D;EACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}