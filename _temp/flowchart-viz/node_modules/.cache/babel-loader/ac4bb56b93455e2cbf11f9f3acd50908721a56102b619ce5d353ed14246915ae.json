{"ast": null, "code": "/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst adjustChannel = (color, channel, amount) => {\n  const channels = Color.parse(color);\n  const amountCurrent = channels[channel];\n  const amountNext = _.channel.clamp[channel](amountCurrent + amount);\n  if (amountCurrent !== amountNext) channels[channel] = amountNext;\n  return Color.stringify(channels);\n};\n/* EXPORT */\nexport default adjustChannel;", "map": {"version": 3, "names": ["_", "Color", "adjustChannel", "color", "channel", "amount", "channels", "parse", "amountCurrent", "amountNext", "clamp", "stringify"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/adjust_channel.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst adjustChannel = (color, channel, amount) => {\n    const channels = Color.parse(color);\n    const amountCurrent = channels[channel];\n    const amountNext = _.channel.clamp[channel](amountCurrent + amount);\n    if (amountCurrent !== amountNext)\n        channels[channel] = amountNext;\n    return Color.stringify(channels);\n};\n/* EXPORT */\nexport default adjustChannel;\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,mBAAmB;AACjC,OAAOC,KAAK,MAAM,mBAAmB;AACrC;AACA,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,KAAK;EAC9C,MAAMC,QAAQ,GAAGL,KAAK,CAACM,KAAK,CAACJ,KAAK,CAAC;EACnC,MAAMK,aAAa,GAAGF,QAAQ,CAACF,OAAO,CAAC;EACvC,MAAMK,UAAU,GAAGT,CAAC,CAACI,OAAO,CAACM,KAAK,CAACN,OAAO,CAAC,CAACI,aAAa,GAAGH,MAAM,CAAC;EACnE,IAAIG,aAAa,KAAKC,UAAU,EAC5BH,QAAQ,CAACF,OAAO,CAAC,GAAGK,UAAU;EAClC,OAAOR,KAAK,CAACU,SAAS,CAACL,QAAQ,CAAC;AACpC,CAAC;AACD;AACA,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}