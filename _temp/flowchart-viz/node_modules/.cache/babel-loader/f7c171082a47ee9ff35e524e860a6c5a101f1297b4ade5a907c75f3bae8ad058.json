{"ast": null, "code": "/* IMPORT */\nimport change from './change.js';\n/* MAIN */\nconst grayscale = color => {\n  return change(color, {\n    s: 0\n  });\n};\n/* EXPORT */\nexport default grayscale;", "map": {"version": 3, "names": ["change", "grayscale", "color", "s"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/grayscale.js"], "sourcesContent": ["/* IMPORT */\nimport change from './change.js';\n/* MAIN */\nconst grayscale = (color) => {\n    return change(color, { s: 0 });\n};\n/* EXPORT */\nexport default grayscale;\n"], "mappings": "AAAA;AACA,OAAOA,MAAM,MAAM,aAAa;AAChC;AACA,MAAMC,SAAS,GAAIC,KAAK,IAAK;EACzB,OAAOF,MAAM,CAACE,KAAK,EAAE;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;AAClC,CAAC;AACD;AACA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}