{"ast": null, "code": "export function applyMixins(derivedCtor, baseCtors) {\n  baseCtors.forEach(baseCtor => {\n    const baseProto = baseCtor.prototype;\n    Object.getOwnPropertyNames(baseProto).forEach(propName => {\n      if (propName === \"constructor\") {\n        return;\n      }\n      const basePropDescriptor = Object.getOwnPropertyDescriptor(baseProto, propName);\n      // Handle Accessors\n      if (basePropDescriptor && (basePropDescriptor.get || basePropDescriptor.set)) {\n        Object.defineProperty(derivedCtor.prototype, propName, basePropDescriptor);\n      } else {\n        derivedCtor.prototype[propName] = baseCtor.prototype[propName];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["applyMixins", "derivedCtor", "baseCtors", "for<PERSON>ach", "baseCtor", "baseProto", "prototype", "Object", "getOwnPropertyNames", "propName", "basePropDescriptor", "getOwnPropertyDescriptor", "get", "set", "defineProperty"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/utils/apply_mixins.ts"], "sourcesContent": ["export function applyMixins(derivedCtor: any, baseCtors: any[]) {\n  baseCtors.forEach((baseCtor) => {\n    const baseProto = baseCtor.prototype;\n    Object.getOwnPropertyNames(baseProto).forEach((propName) => {\n      if (propName === \"constructor\") {\n        return;\n      }\n\n      const basePropDescriptor = Object.getOwnPropertyDescriptor(\n        baseProto,\n        propName,\n      );\n      // Handle Accessors\n      if (\n        basePropDescriptor &&\n        (basePropDescriptor.get || basePropDescriptor.set)\n      ) {\n        Object.defineProperty(\n          derivedCtor.prototype,\n          propName,\n          basePropDescriptor,\n        );\n      } else {\n        derivedCtor.prototype[propName] = baseCtor.prototype[propName];\n      }\n    });\n  });\n}\n"], "mappings": "AAAA,OAAM,SAAUA,WAAWA,CAACC,WAAgB,EAAEC,SAAgB;EAC5DA,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAI;IAC7B,MAAMC,SAAS,GAAGD,QAAQ,CAACE,SAAS;IACpCC,MAAM,CAACC,mBAAmB,CAACH,SAAS,CAAC,CAACF,OAAO,CAAEM,QAAQ,IAAI;MACzD,IAAIA,QAAQ,KAAK,aAAa,EAAE;QAC9B;;MAGF,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,wBAAwB,CACxDN,SAAS,EACTI,QAAQ,CACT;MACD;MACA,IACEC,kBAAkB,KACjBA,kBAAkB,CAACE,GAAG,IAAIF,kBAAkB,CAACG,GAAG,CAAC,EAClD;QACAN,MAAM,CAACO,cAAc,CACnBb,WAAW,CAACK,SAAS,EACrBG,QAAQ,EACRC,kBAAkB,CACnB;OACF,MAAM;QACLT,WAAW,CAACK,SAAS,CAACG,QAAQ,CAAC,GAAGL,QAAQ,CAACE,SAAS,CAACG,QAAQ,CAAC;;IAElE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}