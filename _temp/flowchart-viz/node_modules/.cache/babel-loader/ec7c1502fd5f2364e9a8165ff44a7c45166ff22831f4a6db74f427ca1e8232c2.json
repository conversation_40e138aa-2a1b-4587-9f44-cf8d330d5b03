{"ast": null, "code": "import { Adder } from \"d3-array\";\nimport { sqrt } from \"../math.js\";\nimport noop from \"../noop.js\";\nvar lengthSum = new Adder(),\n  lengthRing,\n  x00,\n  y00,\n  x0,\n  y0;\nvar lengthStream = {\n  point: noop,\n  lineStart: function () {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function () {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function () {\n    lengthRing = true;\n  },\n  polygonEnd: function () {\n    lengthRing = null;\n  },\n  result: function () {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\nexport default lengthStream;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "sqrt", "noop", "lengthSum", "lengthRing", "x00", "y00", "x0", "y0", "lengthStream", "point", "lineStart", "lengthPointFirst", "lineEnd", "lengthPoint", "polygonStart", "polygonEnd", "result", "length", "x", "y", "add"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/path/measure.js"], "sourcesContent": ["import {Adder} from \"d3-array\";\nimport {sqrt} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar lengthSum = new Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: noop,\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\nexport default lengthStream;\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,SAAQC,IAAI,QAAO,YAAY;AAC/B,OAAOC,IAAI,MAAM,YAAY;AAE7B,IAAIC,SAAS,GAAG,IAAIH,KAAK,CAAC,CAAC;EACvBI,UAAU;EACVC,GAAG;EACHC,GAAG;EACHC,EAAE;EACFC,EAAE;AAEN,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAER,IAAI;EACXS,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpBF,YAAY,CAACC,KAAK,GAAGE,gBAAgB;EACvC,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAIT,UAAU,EAAEU,WAAW,CAACT,GAAG,EAAEC,GAAG,CAAC;IACrCG,YAAY,CAACC,KAAK,GAAGR,IAAI;EAC3B,CAAC;EACDa,YAAY,EAAE,SAAAA,CAAA,EAAW;IACvBX,UAAU,GAAG,IAAI;EACnB,CAAC;EACDY,UAAU,EAAE,SAAAA,CAAA,EAAW;IACrBZ,UAAU,GAAG,IAAI;EACnB,CAAC;EACDa,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,IAAIC,MAAM,GAAG,CAACf,SAAS;IACvBA,SAAS,GAAG,IAAIH,KAAK,CAAC,CAAC;IACvB,OAAOkB,MAAM;EACf;AACF,CAAC;AAED,SAASN,gBAAgBA,CAACO,CAAC,EAAEC,CAAC,EAAE;EAC9BX,YAAY,CAACC,KAAK,GAAGI,WAAW;EAChCT,GAAG,GAAGE,EAAE,GAAGY,CAAC,EAAEb,GAAG,GAAGE,EAAE,GAAGY,CAAC;AAC5B;AAEA,SAASN,WAAWA,CAACK,CAAC,EAAEC,CAAC,EAAE;EACzBb,EAAE,IAAIY,CAAC,EAAEX,EAAE,IAAIY,CAAC;EAChBjB,SAAS,CAACkB,GAAG,CAACpB,IAAI,CAACM,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,CAAC;EACtCD,EAAE,GAAGY,CAAC,EAAEX,EAAE,GAAGY,CAAC;AAChB;AAEA,eAAeX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}