{"ast": null, "code": "export default function (ring, hole) {\n  var i = -1,\n    n = hole.length,\n    c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\nfunction ringContains(ring, point) {\n  var x = point[0],\n    y = point[1],\n    contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i],\n      xi = pi[0],\n      yi = pi[1],\n      pj = ring[j],\n      xj = pj[0],\n      yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) contains = -contains;\n  }\n  return contains;\n}\nfunction segmentContains(a, b, c) {\n  var i;\n  return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}", "map": {"version": 3, "names": ["ring", "hole", "i", "n", "length", "c", "ringContains", "point", "x", "y", "contains", "j", "pi", "xi", "yi", "pj", "xj", "yj", "segmentContains", "a", "b", "collinear", "within", "p", "q", "r"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-contour/src/contains.js"], "sourcesContent": ["export default function(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  var i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n"], "mappings": "AAAA,eAAe,UAASA,IAAI,EAAEC,IAAI,EAAE;EAClC,IAAIC,CAAC,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAGF,IAAI,CAACG,MAAM;IAAEC,CAAC;EAC9B,OAAO,EAAEH,CAAC,GAAGC,CAAC,EAAE,IAAIE,CAAC,GAAGC,YAAY,CAACN,IAAI,EAAEC,IAAI,CAACC,CAAC,CAAC,CAAC,EAAE,OAAOG,CAAC;EAC7D,OAAO,CAAC;AACV;AAEA,SAASC,YAAYA,CAACN,IAAI,EAAEO,KAAK,EAAE;EACjC,IAAIC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IAAEE,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAAEG,QAAQ,GAAG,CAAC,CAAC;EAC7C,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAEO,CAAC,GAAGR,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGC,CAAC,EAAEQ,CAAC,GAAGT,CAAC,EAAE,EAAE;IAC1D,IAAIU,EAAE,GAAGZ,IAAI,CAACE,CAAC,CAAC;MAAEW,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;MAAEE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;MAAEG,EAAE,GAAGf,IAAI,CAACW,CAAC,CAAC;MAAEK,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;MAAEE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;IAC9E,IAAIG,eAAe,CAACN,EAAE,EAAEG,EAAE,EAAER,KAAK,CAAC,EAAE,OAAO,CAAC;IAC5C,IAAMO,EAAE,GAAGL,CAAC,KAAOQ,EAAE,GAAGR,CAAE,IAAOD,CAAC,GAAG,CAACQ,EAAE,GAAGH,EAAE,KAAKJ,CAAC,GAAGK,EAAE,CAAC,IAAIG,EAAE,GAAGH,EAAE,CAAC,GAAGD,EAAI,EAAEH,QAAQ,GAAG,CAACA,QAAQ;EACpG;EACA,OAAOA,QAAQ;AACjB;AAEA,SAASQ,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEf,CAAC,EAAE;EAChC,IAAIH,CAAC;EAAE,OAAOmB,SAAS,CAACF,CAAC,EAAEC,CAAC,EAAEf,CAAC,CAAC,IAAIiB,MAAM,CAACH,CAAC,CAACjB,CAAC,GAAG,EAAEiB,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEf,CAAC,CAACH,CAAC,CAAC,EAAEkB,CAAC,CAAClB,CAAC,CAAC,CAAC;AACjF;AAEA,SAASmB,SAASA,CAACF,CAAC,EAAEC,CAAC,EAAEf,CAAC,EAAE;EAC1B,OAAO,CAACe,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,KAAKd,CAAC,CAAC,CAAC,CAAC,GAAGc,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAACd,CAAC,CAAC,CAAC,CAAC,GAAGc,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE;AAEA,SAASG,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAOF,CAAC,IAAIC,CAAC,IAAIA,CAAC,IAAIC,CAAC,IAAIA,CAAC,IAAID,CAAC,IAAIA,CAAC,IAAID,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}