{"ast": null, "code": "import { ParserDefinitionErrorType } from \"../parser/parser.js\";\nimport { forEach, values } from \"lodash-es\";\nimport { GAstVisitor } from \"@chevrotain/gast\";\nexport function resolveGrammar(topLevels, errMsgProvider) {\n  const refResolver = new GastRefResolverVisitor(topLevels, errMsgProvider);\n  refResolver.resolveRefs();\n  return refResolver.errors;\n}\nexport class GastRefResolverVisitor extends GAstVisitor {\n  constructor(nameToTopRule, errMsgProvider) {\n    super();\n    this.nameToTopRule = nameToTopRule;\n    this.errMsgProvider = errMsgProvider;\n    this.errors = [];\n  }\n  resolveRefs() {\n    forEach(values(this.nameToTopRule), prod => {\n      this.currTopLevel = prod;\n      prod.accept(this);\n    });\n  }\n  visitNonTerminal(node) {\n    const ref = this.nameToTopRule[node.nonTerminalName];\n    if (!ref) {\n      const msg = this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel, node);\n      this.errors.push({\n        message: msg,\n        type: ParserDefinitionErrorType.UNRESOLVED_SUBRULE_REF,\n        ruleName: this.currTopLevel.name,\n        unresolvedRefName: node.nonTerminalName\n      });\n    } else {\n      node.referencedRule = ref;\n    }\n  }\n}", "map": {"version": 3, "names": ["ParserDefinitionErrorType", "for<PERSON>ach", "values", "GAstVisitor", "resolveGrammar", "topLevels", "err<PERSON><PERSON><PERSON><PERSON><PERSON>", "refResolver", "GastRefResolverVisitor", "resolveRefs", "errors", "constructor", "nameToTopRule", "prod", "currTopLevel", "accept", "visitNonTerminal", "node", "ref", "nonTerminalName", "msg", "buildRuleNotFoundError", "push", "message", "type", "UNRESOLVED_SUBRULE_REF", "ruleName", "name", "unresolvedRefName", "referencedRule"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/resolver.ts"], "sourcesContent": ["import {\n  IParserUnresolvedRefDefinitionError,\n  ParserDefinitionErrorType,\n} from \"../parser/parser.js\";\nimport { forEach, values } from \"lodash-es\";\nimport { GAstVisitor, NonTerminal, Rule } from \"@chevrotain/gast\";\nimport {\n  IGrammarResolverErrorMessageProvider,\n  IParserDefinitionError,\n} from \"./types.js\";\n\nexport function resolveGrammar(\n  topLevels: Record<string, Rule>,\n  errMsgProvider: IGrammarResolverErrorMessageProvider,\n): IParserDefinitionError[] {\n  const refResolver = new GastRefResolverVisitor(topLevels, errMsgProvider);\n  refResolver.resolveRefs();\n  return refResolver.errors;\n}\n\nexport class GastRefResolverVisitor extends GAstVisitor {\n  public errors: IParserUnresolvedRefDefinitionError[] = [];\n  private currTopLevel: Rule;\n\n  constructor(\n    private nameToTopRule: Record<string, Rule>,\n    private errMsgProvider: IGrammarResolverErrorMessageProvider,\n  ) {\n    super();\n  }\n\n  public resolveRefs(): void {\n    forEach(values(this.nameToTopRule), (prod) => {\n      this.currTopLevel = prod;\n      prod.accept(this);\n    });\n  }\n\n  public visitNonTerminal(node: NonTerminal): void {\n    const ref = this.nameToTopRule[node.nonTerminalName];\n\n    if (!ref) {\n      const msg = this.errMsgProvider.buildRuleNotFoundError(\n        this.currTopLevel,\n        node,\n      );\n      this.errors.push({\n        message: msg,\n        type: ParserDefinitionErrorType.UNRESOLVED_SUBRULE_REF,\n        ruleName: this.currTopLevel.name,\n        unresolvedRefName: node.nonTerminalName,\n      });\n    } else {\n      node.referencedRule = ref;\n    }\n  }\n}\n"], "mappings": "AAAA,SAEEA,yBAAyB,QACpB,qBAAqB;AAC5B,SAASC,OAAO,EAAEC,MAAM,QAAQ,WAAW;AAC3C,SAASC,WAAW,QAA2B,kBAAkB;AAMjE,OAAM,SAAUC,cAAcA,CAC5BC,SAA+B,EAC/BC,cAAoD;EAEpD,MAAMC,WAAW,GAAG,IAAIC,sBAAsB,CAACH,SAAS,EAAEC,cAAc,CAAC;EACzEC,WAAW,CAACE,WAAW,EAAE;EACzB,OAAOF,WAAW,CAACG,MAAM;AAC3B;AAEA,OAAM,MAAOF,sBAAuB,SAAQL,WAAW;EAIrDQ,YACUC,aAAmC,EACnCN,cAAoD;IAE5D,KAAK,EAAE;IAHC,KAAAM,aAAa,GAAbA,aAAa;IACb,KAAAN,cAAc,GAAdA,cAAc;IALjB,KAAAI,MAAM,GAA0C,EAAE;EAQzD;EAEOD,WAAWA,CAAA;IAChBR,OAAO,CAACC,MAAM,CAAC,IAAI,CAACU,aAAa,CAAC,EAAGC,IAAI,IAAI;MAC3C,IAAI,CAACC,YAAY,GAAGD,IAAI;MACxBA,IAAI,CAACE,MAAM,CAAC,IAAI,CAAC;IACnB,CAAC,CAAC;EACJ;EAEOC,gBAAgBA,CAACC,IAAiB;IACvC,MAAMC,GAAG,GAAG,IAAI,CAACN,aAAa,CAACK,IAAI,CAACE,eAAe,CAAC;IAEpD,IAAI,CAACD,GAAG,EAAE;MACR,MAAME,GAAG,GAAG,IAAI,CAACd,cAAc,CAACe,sBAAsB,CACpD,IAAI,CAACP,YAAY,EACjBG,IAAI,CACL;MACD,IAAI,CAACP,MAAM,CAACY,IAAI,CAAC;QACfC,OAAO,EAAEH,GAAG;QACZI,IAAI,EAAExB,yBAAyB,CAACyB,sBAAsB;QACtDC,QAAQ,EAAE,IAAI,CAACZ,YAAY,CAACa,IAAI;QAChCC,iBAAiB,EAAEX,IAAI,CAACE;OACzB,CAAC;KACH,MAAM;MACLF,IAAI,CAACY,cAAc,GAAGX,GAAG;;EAE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}