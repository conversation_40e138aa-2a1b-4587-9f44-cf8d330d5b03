{"ast": null, "code": "import { asin, atan2, cos, sin, sqrt } from \"../math.js\";\nexport function azimuthalRaw(scale) {\n  return function (x, y) {\n    var cx = cos(x),\n      cy = cos(y),\n      k = scale(cx * cy);\n    if (k === Infinity) return [2, 0];\n    return [k * cy * sin(x), k * sin(y)];\n  };\n}\nexport function azimuthalInvert(angle) {\n  return function (x, y) {\n    var z = sqrt(x * x + y * y),\n      c = angle(z),\n      sc = sin(c),\n      cc = cos(c);\n    return [atan2(x * sc, z * cc), asin(z && y * sc / z)];\n  };\n}", "map": {"version": 3, "names": ["asin", "atan2", "cos", "sin", "sqrt", "azimuthalRaw", "scale", "x", "y", "cx", "cy", "k", "Infinity", "azimuthalInvert", "angle", "z", "c", "sc", "cc"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/azimuthal.js"], "sourcesContent": ["import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAO,YAAY;AAEtD,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,UAASC,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIC,EAAE,GAAGP,GAAG,CAACK,CAAC,CAAC;MACXG,EAAE,GAAGR,GAAG,CAACM,CAAC,CAAC;MACXG,CAAC,GAAGL,KAAK,CAACG,EAAE,GAAGC,EAAE,CAAC;IAClB,IAAIC,CAAC,KAAKC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,OAAO,CACLD,CAAC,GAAGD,EAAE,GAAGP,GAAG,CAACI,CAAC,CAAC,EACfI,CAAC,GAAGR,GAAG,CAACK,CAAC,CAAC,CACX;EACH,CAAC;AACH;AAEA,OAAO,SAASK,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,UAASP,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIO,CAAC,GAAGX,IAAI,CAACG,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;MACvBQ,CAAC,GAAGF,KAAK,CAACC,CAAC,CAAC;MACZE,EAAE,GAAGd,GAAG,CAACa,CAAC,CAAC;MACXE,EAAE,GAAGhB,GAAG,CAACc,CAAC,CAAC;IACf,OAAO,CACLf,KAAK,CAACM,CAAC,GAAGU,EAAE,EAAEF,CAAC,GAAGG,EAAE,CAAC,EACrBlB,IAAI,CAACe,CAAC,IAAIP,CAAC,GAAGS,EAAE,GAAGF,CAAC,CAAC,CACtB;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}