{"ast": null, "code": "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\nexport default function (values, min, max) {\n  return Math.ceil((max - min) / (2 * (quantile(values, 0.75) - quantile(values, 0.25)) * Math.pow(count(values), -1 / 3)));\n}", "map": {"version": 3, "names": ["count", "quantile", "values", "min", "max", "Math", "ceil", "pow"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/threshold/freedmanDiaconis.js"], "sourcesContent": ["import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (2 * (quantile(values, 0.75) - quantile(values, 0.25)) * Math.pow(count(values), -1 / 3)));\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,eAAe,UAASC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACxC,OAAOC,IAAI,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGD,GAAG,KAAK,CAAC,IAAIF,QAAQ,CAACC,MAAM,EAAE,IAAI,CAAC,GAAGD,QAAQ,CAACC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACP,KAAK,CAACE,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}