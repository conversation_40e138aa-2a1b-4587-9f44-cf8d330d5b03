{"ast": null, "code": "\"use strict\";\n\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.stringArray = exports.array = exports.func = exports.error = exports.number = exports.string = exports.boolean = void 0;\nfunction boolean(value) {\n  return value === true || value === false;\n}\nexports.boolean = boolean;\nfunction string(value) {\n  return typeof value === 'string' || value instanceof String;\n}\nexports.string = string;\nfunction number(value) {\n  return typeof value === 'number' || value instanceof Number;\n}\nexports.number = number;\nfunction error(value) {\n  return value instanceof Error;\n}\nexports.error = error;\nfunction func(value) {\n  return typeof value === 'function';\n}\nexports.func = func;\nfunction array(value) {\n  return Array.isArray(value);\n}\nexports.array = array;\nfunction stringArray(value) {\n  return array(value) && value.every(elem => string(elem));\n}\nexports.stringArray = stringArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "stringArray", "array", "func", "error", "number", "string", "boolean", "String", "Number", "Error", "Array", "isArray", "every", "elem"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-jsonrpc/lib/common/is.js"], "sourcesContent": ["\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.stringArray = exports.array = exports.func = exports.error = exports.number = exports.string = exports.boolean = void 0;\nfunction boolean(value) {\n    return value === true || value === false;\n}\nexports.boolean = boolean;\nfunction string(value) {\n    return typeof value === 'string' || value instanceof String;\n}\nexports.string = string;\nfunction number(value) {\n    return typeof value === 'number' || value instanceof Number;\n}\nexports.number = number;\nfunction error(value) {\n    return value instanceof Error;\n}\nexports.error = error;\nfunction func(value) {\n    return typeof value === 'function';\n}\nexports.func = func;\nfunction array(value) {\n    return Array.isArray(value);\n}\nexports.array = array;\nfunction stringArray(value) {\n    return array(value) && value.every(elem => string(elem));\n}\nexports.stringArray = stringArray;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACAA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,KAAK,GAAGH,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACK,KAAK,GAAGL,OAAO,CAACM,MAAM,GAAGN,OAAO,CAACO,MAAM,GAAGP,OAAO,CAACQ,OAAO,GAAG,KAAK,CAAC;AAC/H,SAASA,OAAOA,CAACP,KAAK,EAAE;EACpB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK;AAC5C;AACAD,OAAO,CAACQ,OAAO,GAAGA,OAAO;AACzB,SAASD,MAAMA,CAACN,KAAK,EAAE;EACnB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYQ,MAAM;AAC/D;AACAT,OAAO,CAACO,MAAM,GAAGA,MAAM;AACvB,SAASD,MAAMA,CAACL,KAAK,EAAE;EACnB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYS,MAAM;AAC/D;AACAV,OAAO,CAACM,MAAM,GAAGA,MAAM;AACvB,SAASD,KAAKA,CAACJ,KAAK,EAAE;EAClB,OAAOA,KAAK,YAAYU,KAAK;AACjC;AACAX,OAAO,CAACK,KAAK,GAAGA,KAAK;AACrB,SAASD,IAAIA,CAACH,KAAK,EAAE;EACjB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACAD,OAAO,CAACI,IAAI,GAAGA,IAAI;AACnB,SAASD,KAAKA,CAACF,KAAK,EAAE;EAClB,OAAOW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC;AAC/B;AACAD,OAAO,CAACG,KAAK,GAAGA,KAAK;AACrB,SAASD,WAAWA,CAACD,KAAK,EAAE;EACxB,OAAOE,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACa,KAAK,CAACC,IAAI,IAAIR,MAAM,CAACQ,IAAI,CAAC,CAAC;AAC5D;AACAf,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}