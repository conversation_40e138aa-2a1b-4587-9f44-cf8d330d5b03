{"ast": null, "code": "import { addFlag, ASSERT_EXISTS, ASSERT_NEVER_REACH_HERE, cc, insertToSet, is<PERSON><PERSON>cter } from \"./utils.js\";\nimport { digitsCharCodes, whitespaceCodes, wordCharCodes } from \"./character-classes.js\";\n// consts and utilities\nconst hexDigitPattern = /[0-9a-fA-F]/;\nconst decimalPattern = /[0-9]/;\nconst decimalPatternNoZero = /[1-9]/;\n// https://hackernoon.com/the-madness-of-parsing-real-world-javascript-regexps-d9ee336df983\n// https://www.ecma-international.org/ecma-262/8.0/index.html#prod-Pattern\nexport class RegExpParser {\n  constructor() {\n    this.idx = 0;\n    this.input = \"\";\n    this.groupIdx = 0;\n  }\n  saveState() {\n    return {\n      idx: this.idx,\n      input: this.input,\n      groupIdx: this.groupIdx\n    };\n  }\n  restoreState(newState) {\n    this.idx = newState.idx;\n    this.input = newState.input;\n    this.groupIdx = newState.groupIdx;\n  }\n  pattern(input) {\n    // parser state\n    this.idx = 0;\n    this.input = input;\n    this.groupIdx = 0;\n    this.consumeChar(\"/\");\n    const value = this.disjunction();\n    this.consumeChar(\"/\");\n    const flags = {\n      type: \"Flags\",\n      loc: {\n        begin: this.idx,\n        end: input.length\n      },\n      global: false,\n      ignoreCase: false,\n      multiLine: false,\n      unicode: false,\n      sticky: false\n    };\n    while (this.isRegExpFlag()) {\n      switch (this.popChar()) {\n        case \"g\":\n          addFlag(flags, \"global\");\n          break;\n        case \"i\":\n          addFlag(flags, \"ignoreCase\");\n          break;\n        case \"m\":\n          addFlag(flags, \"multiLine\");\n          break;\n        case \"u\":\n          addFlag(flags, \"unicode\");\n          break;\n        case \"y\":\n          addFlag(flags, \"sticky\");\n          break;\n      }\n    }\n    if (this.idx !== this.input.length) {\n      throw Error(\"Redundant input: \" + this.input.substring(this.idx));\n    }\n    return {\n      type: \"Pattern\",\n      flags: flags,\n      value: value,\n      loc: this.loc(0)\n    };\n  }\n  disjunction() {\n    const alts = [];\n    const begin = this.idx;\n    alts.push(this.alternative());\n    while (this.peekChar() === \"|\") {\n      this.consumeChar(\"|\");\n      alts.push(this.alternative());\n    }\n    return {\n      type: \"Disjunction\",\n      value: alts,\n      loc: this.loc(begin)\n    };\n  }\n  alternative() {\n    const terms = [];\n    const begin = this.idx;\n    while (this.isTerm()) {\n      terms.push(this.term());\n    }\n    return {\n      type: \"Alternative\",\n      value: terms,\n      loc: this.loc(begin)\n    };\n  }\n  term() {\n    if (this.isAssertion()) {\n      return this.assertion();\n    } else {\n      return this.atom();\n    }\n  }\n  assertion() {\n    const begin = this.idx;\n    switch (this.popChar()) {\n      case \"^\":\n        return {\n          type: \"StartAnchor\",\n          loc: this.loc(begin)\n        };\n      case \"$\":\n        return {\n          type: \"EndAnchor\",\n          loc: this.loc(begin)\n        };\n      // '\\b' or '\\B'\n      case \"\\\\\":\n        switch (this.popChar()) {\n          case \"b\":\n            return {\n              type: \"WordBoundary\",\n              loc: this.loc(begin)\n            };\n          case \"B\":\n            return {\n              type: \"NonWordBoundary\",\n              loc: this.loc(begin)\n            };\n        }\n        // istanbul ignore next\n        throw Error(\"Invalid Assertion Escape\");\n      // '(?=' or '(?!'\n      case \"(\":\n        this.consumeChar(\"?\");\n        let type;\n        switch (this.popChar()) {\n          case \"=\":\n            type = \"Lookahead\";\n            break;\n          case \"!\":\n            type = \"NegativeLookahead\";\n            break;\n        }\n        ASSERT_EXISTS(type);\n        const disjunction = this.disjunction();\n        this.consumeChar(\")\");\n        return {\n          type: type,\n          value: disjunction,\n          loc: this.loc(begin)\n        };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n  quantifier(isBacktracking = false) {\n    let range = undefined;\n    const begin = this.idx;\n    switch (this.popChar()) {\n      case \"*\":\n        range = {\n          atLeast: 0,\n          atMost: Infinity\n        };\n        break;\n      case \"+\":\n        range = {\n          atLeast: 1,\n          atMost: Infinity\n        };\n        break;\n      case \"?\":\n        range = {\n          atLeast: 0,\n          atMost: 1\n        };\n        break;\n      case \"{\":\n        const atLeast = this.integerIncludingZero();\n        switch (this.popChar()) {\n          case \"}\":\n            range = {\n              atLeast: atLeast,\n              atMost: atLeast\n            };\n            break;\n          case \",\":\n            let atMost;\n            if (this.isDigit()) {\n              atMost = this.integerIncludingZero();\n              range = {\n                atLeast: atLeast,\n                atMost: atMost\n              };\n            } else {\n              range = {\n                atLeast: atLeast,\n                atMost: Infinity\n              };\n            }\n            this.consumeChar(\"}\");\n            break;\n        }\n        // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n        // causes severe performance degradations\n        if (isBacktracking === true && range === undefined) {\n          return undefined;\n        }\n        ASSERT_EXISTS(range);\n        break;\n    }\n    // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n    // causes severe performance degradations\n    if (isBacktracking === true && range === undefined) {\n      return undefined;\n    }\n    // istanbul ignore else\n    if (ASSERT_EXISTS(range)) {\n      if (this.peekChar(0) === \"?\") {\n        this.consumeChar(\"?\");\n        range.greedy = false;\n      } else {\n        range.greedy = true;\n      }\n      range.type = \"Quantifier\";\n      range.loc = this.loc(begin);\n      return range;\n    }\n  }\n  atom() {\n    let atom;\n    const begin = this.idx;\n    switch (this.peekChar()) {\n      case \".\":\n        atom = this.dotAll();\n        break;\n      case \"\\\\\":\n        atom = this.atomEscape();\n        break;\n      case \"[\":\n        atom = this.characterClass();\n        break;\n      case \"(\":\n        atom = this.group();\n        break;\n    }\n    if (atom === undefined && this.isPatternCharacter()) {\n      atom = this.patternCharacter();\n    }\n    // istanbul ignore else\n    if (ASSERT_EXISTS(atom)) {\n      atom.loc = this.loc(begin);\n      if (this.isQuantifier()) {\n        atom.quantifier = this.quantifier();\n      }\n      return atom;\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n  dotAll() {\n    this.consumeChar(\".\");\n    return {\n      type: \"Set\",\n      complement: true,\n      value: [cc(\"\\n\"), cc(\"\\r\"), cc(\"\\u2028\"), cc(\"\\u2029\")]\n    };\n  }\n  atomEscape() {\n    this.consumeChar(\"\\\\\");\n    switch (this.peekChar()) {\n      case \"1\":\n      case \"2\":\n      case \"3\":\n      case \"4\":\n      case \"5\":\n      case \"6\":\n      case \"7\":\n      case \"8\":\n      case \"9\":\n        return this.decimalEscapeAtom();\n      case \"d\":\n      case \"D\":\n      case \"s\":\n      case \"S\":\n      case \"w\":\n      case \"W\":\n        return this.characterClassEscape();\n      case \"f\":\n      case \"n\":\n      case \"r\":\n      case \"t\":\n      case \"v\":\n        return this.controlEscapeAtom();\n      case \"c\":\n        return this.controlLetterEscapeAtom();\n      case \"0\":\n        return this.nulCharacterAtom();\n      case \"x\":\n        return this.hexEscapeSequenceAtom();\n      case \"u\":\n        return this.regExpUnicodeEscapeSequenceAtom();\n      default:\n        return this.identityEscapeAtom();\n    }\n  }\n  decimalEscapeAtom() {\n    const value = this.positiveInteger();\n    return {\n      type: \"GroupBackReference\",\n      value: value\n    };\n  }\n  characterClassEscape() {\n    let set;\n    let complement = false;\n    switch (this.popChar()) {\n      case \"d\":\n        set = digitsCharCodes;\n        break;\n      case \"D\":\n        set = digitsCharCodes;\n        complement = true;\n        break;\n      case \"s\":\n        set = whitespaceCodes;\n        break;\n      case \"S\":\n        set = whitespaceCodes;\n        complement = true;\n        break;\n      case \"w\":\n        set = wordCharCodes;\n        break;\n      case \"W\":\n        set = wordCharCodes;\n        complement = true;\n        break;\n    }\n    // istanbul ignore else\n    if (ASSERT_EXISTS(set)) {\n      return {\n        type: \"Set\",\n        value: set,\n        complement: complement\n      };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n  controlEscapeAtom() {\n    let escapeCode;\n    switch (this.popChar()) {\n      case \"f\":\n        escapeCode = cc(\"\\f\");\n        break;\n      case \"n\":\n        escapeCode = cc(\"\\n\");\n        break;\n      case \"r\":\n        escapeCode = cc(\"\\r\");\n        break;\n      case \"t\":\n        escapeCode = cc(\"\\t\");\n        break;\n      case \"v\":\n        escapeCode = cc(\"\\v\");\n        break;\n    }\n    // istanbul ignore else\n    if (ASSERT_EXISTS(escapeCode)) {\n      return {\n        type: \"Character\",\n        value: escapeCode\n      };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n  controlLetterEscapeAtom() {\n    this.consumeChar(\"c\");\n    const letter = this.popChar();\n    if (/[a-zA-Z]/.test(letter) === false) {\n      throw Error(\"Invalid \");\n    }\n    const letterCode = letter.toUpperCase().charCodeAt(0) - 64;\n    return {\n      type: \"Character\",\n      value: letterCode\n    };\n  }\n  nulCharacterAtom() {\n    // TODO implement '[lookahead ∉ DecimalDigit]'\n    // TODO: for the deprecated octal escape sequence\n    this.consumeChar(\"0\");\n    return {\n      type: \"Character\",\n      value: cc(\"\\0\")\n    };\n  }\n  hexEscapeSequenceAtom() {\n    this.consumeChar(\"x\");\n    return this.parseHexDigits(2);\n  }\n  regExpUnicodeEscapeSequenceAtom() {\n    this.consumeChar(\"u\");\n    return this.parseHexDigits(4);\n  }\n  identityEscapeAtom() {\n    // TODO: implement \"SourceCharacter but not UnicodeIDContinue\"\n    // // http://unicode.org/reports/tr31/#Specific_Character_Adjustments\n    const escapedChar = this.popChar();\n    return {\n      type: \"Character\",\n      value: cc(escapedChar)\n    };\n  }\n  classPatternCharacterAtom() {\n    switch (this.peekChar()) {\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n      // istanbul ignore next\n      case \"\\\\\":\n      // istanbul ignore next\n      case \"]\":\n        throw Error(\"TBD\");\n      default:\n        const nextChar = this.popChar();\n        return {\n          type: \"Character\",\n          value: cc(nextChar)\n        };\n    }\n  }\n  characterClass() {\n    const set = [];\n    let complement = false;\n    this.consumeChar(\"[\");\n    if (this.peekChar(0) === \"^\") {\n      this.consumeChar(\"^\");\n      complement = true;\n    }\n    while (this.isClassAtom()) {\n      const from = this.classAtom();\n      const isFromSingleChar = from.type === \"Character\";\n      if (isCharacter(from) && this.isRangeDash()) {\n        this.consumeChar(\"-\");\n        const to = this.classAtom();\n        const isToSingleChar = to.type === \"Character\";\n        // a range can only be used when both sides are single characters\n        if (isCharacter(to)) {\n          if (to.value < from.value) {\n            throw Error(\"Range out of order in character class\");\n          }\n          set.push({\n            from: from.value,\n            to: to.value\n          });\n        } else {\n          // literal dash\n          insertToSet(from.value, set);\n          set.push(cc(\"-\"));\n          insertToSet(to.value, set);\n        }\n      } else {\n        insertToSet(from.value, set);\n      }\n    }\n    this.consumeChar(\"]\");\n    return {\n      type: \"Set\",\n      complement: complement,\n      value: set\n    };\n  }\n  classAtom() {\n    switch (this.peekChar()) {\n      // istanbul ignore next\n      case \"]\":\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n        throw Error(\"TBD\");\n      case \"\\\\\":\n        return this.classEscape();\n      default:\n        return this.classPatternCharacterAtom();\n    }\n  }\n  classEscape() {\n    this.consumeChar(\"\\\\\");\n    switch (this.peekChar()) {\n      // Matches a backspace.\n      // (Not to be confused with \\b word boundary outside characterClass)\n      case \"b\":\n        this.consumeChar(\"b\");\n        return {\n          type: \"Character\",\n          value: cc(\"\\u0008\")\n        };\n      case \"d\":\n      case \"D\":\n      case \"s\":\n      case \"S\":\n      case \"w\":\n      case \"W\":\n        return this.characterClassEscape();\n      case \"f\":\n      case \"n\":\n      case \"r\":\n      case \"t\":\n      case \"v\":\n        return this.controlEscapeAtom();\n      case \"c\":\n        return this.controlLetterEscapeAtom();\n      case \"0\":\n        return this.nulCharacterAtom();\n      case \"x\":\n        return this.hexEscapeSequenceAtom();\n      case \"u\":\n        return this.regExpUnicodeEscapeSequenceAtom();\n      default:\n        return this.identityEscapeAtom();\n    }\n  }\n  group() {\n    let capturing = true;\n    this.consumeChar(\"(\");\n    switch (this.peekChar(0)) {\n      case \"?\":\n        this.consumeChar(\"?\");\n        this.consumeChar(\":\");\n        capturing = false;\n        break;\n      default:\n        this.groupIdx++;\n        break;\n    }\n    const value = this.disjunction();\n    this.consumeChar(\")\");\n    const groupAst = {\n      type: \"Group\",\n      capturing: capturing,\n      value: value\n    };\n    if (capturing) {\n      groupAst[\"idx\"] = this.groupIdx;\n    }\n    return groupAst;\n  }\n  positiveInteger() {\n    let number = this.popChar();\n    // istanbul ignore next - can't ever get here due to previous lookahead checks\n    // still implementing this error checking in case this ever changes.\n    if (decimalPatternNoZero.test(number) === false) {\n      throw Error(\"Expecting a positive integer\");\n    }\n    while (decimalPattern.test(this.peekChar(0))) {\n      number += this.popChar();\n    }\n    return parseInt(number, 10);\n  }\n  integerIncludingZero() {\n    let number = this.popChar();\n    if (decimalPattern.test(number) === false) {\n      throw Error(\"Expecting an integer\");\n    }\n    while (decimalPattern.test(this.peekChar(0))) {\n      number += this.popChar();\n    }\n    return parseInt(number, 10);\n  }\n  patternCharacter() {\n    const nextChar = this.popChar();\n    switch (nextChar) {\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n      // istanbul ignore next\n      case \"^\":\n      // istanbul ignore next\n      case \"$\":\n      // istanbul ignore next\n      case \"\\\\\":\n      // istanbul ignore next\n      case \".\":\n      // istanbul ignore next\n      case \"*\":\n      // istanbul ignore next\n      case \"+\":\n      // istanbul ignore next\n      case \"?\":\n      // istanbul ignore next\n      case \"(\":\n      // istanbul ignore next\n      case \")\":\n      // istanbul ignore next\n      case \"[\":\n      // istanbul ignore next\n      case \"|\":\n        // istanbul ignore next\n        throw Error(\"TBD\");\n      default:\n        return {\n          type: \"Character\",\n          value: cc(nextChar)\n        };\n    }\n  }\n  isRegExpFlag() {\n    switch (this.peekChar(0)) {\n      case \"g\":\n      case \"i\":\n      case \"m\":\n      case \"u\":\n      case \"y\":\n        return true;\n      default:\n        return false;\n    }\n  }\n  isRangeDash() {\n    return this.peekChar() === \"-\" && this.isClassAtom(1);\n  }\n  isDigit() {\n    return decimalPattern.test(this.peekChar(0));\n  }\n  isClassAtom(howMuch = 0) {\n    switch (this.peekChar(howMuch)) {\n      case \"]\":\n      case \"\\n\":\n      case \"\\r\":\n      case \"\\u2028\":\n      case \"\\u2029\":\n        return false;\n      default:\n        return true;\n    }\n  }\n  isTerm() {\n    return this.isAtom() || this.isAssertion();\n  }\n  isAtom() {\n    if (this.isPatternCharacter()) {\n      return true;\n    }\n    switch (this.peekChar(0)) {\n      case \".\":\n      case \"\\\\\": // atomEscape\n      case \"[\": // characterClass\n      // TODO: isAtom must be called before isAssertion - disambiguate\n      case \"(\":\n        // group\n        return true;\n      default:\n        return false;\n    }\n  }\n  isAssertion() {\n    switch (this.peekChar(0)) {\n      case \"^\":\n      case \"$\":\n        return true;\n      // '\\b' or '\\B'\n      case \"\\\\\":\n        switch (this.peekChar(1)) {\n          case \"b\":\n          case \"B\":\n            return true;\n          default:\n            return false;\n        }\n      // '(?=' or '(?!'\n      case \"(\":\n        return this.peekChar(1) === \"?\" && (this.peekChar(2) === \"=\" || this.peekChar(2) === \"!\");\n      default:\n        return false;\n    }\n  }\n  isQuantifier() {\n    const prevState = this.saveState();\n    try {\n      return this.quantifier(true) !== undefined;\n    } catch (e) {\n      return false;\n    } finally {\n      this.restoreState(prevState);\n    }\n  }\n  isPatternCharacter() {\n    switch (this.peekChar()) {\n      case \"^\":\n      case \"$\":\n      case \"\\\\\":\n      case \".\":\n      case \"*\":\n      case \"+\":\n      case \"?\":\n      case \"(\":\n      case \")\":\n      case \"[\":\n      case \"|\":\n      case \"/\":\n      case \"\\n\":\n      case \"\\r\":\n      case \"\\u2028\":\n      case \"\\u2029\":\n        return false;\n      default:\n        return true;\n    }\n  }\n  parseHexDigits(howMany) {\n    let hexString = \"\";\n    for (let i = 0; i < howMany; i++) {\n      const hexChar = this.popChar();\n      if (hexDigitPattern.test(hexChar) === false) {\n        throw Error(\"Expecting a HexDecimal digits\");\n      }\n      hexString += hexChar;\n    }\n    const charCode = parseInt(hexString, 16);\n    return {\n      type: \"Character\",\n      value: charCode\n    };\n  }\n  peekChar(howMuch = 0) {\n    return this.input[this.idx + howMuch];\n  }\n  popChar() {\n    const nextChar = this.peekChar(0);\n    this.consumeChar(undefined);\n    return nextChar;\n  }\n  consumeChar(char) {\n    if (char !== undefined && this.input[this.idx] !== char) {\n      throw Error(\"Expected: '\" + char + \"' but found: '\" + this.input[this.idx] + \"' at offset: \" + this.idx);\n    }\n    if (this.idx >= this.input.length) {\n      throw Error(\"Unexpected end of input\");\n    }\n    this.idx++;\n  }\n  loc(begin) {\n    return {\n      begin: begin,\n      end: this.idx\n    };\n  }\n}", "map": {"version": 3, "names": ["addFlag", "ASSERT_EXISTS", "ASSERT_NEVER_REACH_HERE", "cc", "insertToSet", "isCharacter", "digitsCharCodes", "whitespaceCodes", "wordCharCodes", "hexDigitPattern", "decimalPattern", "decimalPatternNoZero", "Reg<PERSON>xp<PERSON><PERSON><PERSON>", "constructor", "idx", "input", "groupIdx", "saveState", "restoreState", "newState", "pattern", "consumeChar", "value", "disjunction", "flags", "type", "loc", "begin", "end", "length", "global", "ignoreCase", "multiLine", "unicode", "sticky", "isRegExpFlag", "popChar", "Error", "substring", "alts", "push", "alternative", "peekChar", "terms", "isTerm", "term", "isAssertion", "assertion", "atom", "quantifier", "isBacktracking", "range", "undefined", "atLeast", "atMost", "Infinity", "integerIncludingZero", "isDigit", "greedy", "dotAll", "atomEscape", "characterClass", "group", "isPatternCharacter", "patternCharacter", "isQuantifier", "complement", "decimalEscapeAtom", "characterClassEscape", "controlEscapeAtom", "controlLetterEscapeAtom", "nulCharacterAtom", "hexEscapeSequenceAtom", "regExpUnicodeEscapeSequenceAtom", "identityEscapeAtom", "positiveInteger", "set", "escapeCode", "letter", "test", "letterCode", "toUpperCase", "charCodeAt", "parseHexDigits", "escapedChar", "classPatternCharacterAtom", "nextChar", "isClassAtom", "from", "classAtom", "isFromSingleChar", "isRangeDash", "to", "isToSingleChar", "classEscape", "capturing", "groupAst", "number", "parseInt", "<PERSON><PERSON><PERSON>", "isAtom", "prevState", "e", "how<PERSON><PERSON>", "hexString", "i", "hexChar", "charCode", "char"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/regexp-to-ast/src/regexp-parser.ts"], "sourcesContent": ["import type {\n  Alternative,\n  Assertion,\n  Atom,\n  Character,\n  Disjunction,\n  Group,\n  GroupBackReference,\n  Location,\n  Quantifier,\n  Range,\n  RegExpFlags,\n  RegExpPattern,\n  Set,\n  Term,\n} from \"../types\";\nimport {\n  addFlag,\n  ASSERT_EXISTS,\n  ASSERT_NEVER_REACH_HERE,\n  cc,\n  insertToSet,\n  is<PERSON><PERSON><PERSON>,\n} from \"./utils.js\";\nimport {\n  digitsCharCodes,\n  whitespaceCodes,\n  wordCharCodes,\n} from \"./character-classes.js\";\n\n// consts and utilities\nconst hexDigitPattern = /[0-9a-fA-F]/;\nconst decimalPattern = /[0-9]/;\nconst decimalPatternNoZero = /[1-9]/;\n\n// https://hackernoon.com/the-madness-of-parsing-real-world-javascript-regexps-d9ee336df983\n// https://www.ecma-international.org/ecma-262/8.0/index.html#prod-Pattern\nexport class RegExpParser {\n  protected idx: number = 0;\n  protected input: string = \"\";\n  protected groupIdx: number = 0;\n\n  protected saveState() {\n    return {\n      idx: this.idx,\n      input: this.input,\n      groupIdx: this.groupIdx,\n    };\n  }\n\n  protected restoreState(newState: {\n    idx: number;\n    input: string;\n    groupIdx: number;\n  }) {\n    this.idx = newState.idx;\n    this.input = newState.input;\n    this.groupIdx = newState.groupIdx;\n  }\n\n  public pattern(input: string): RegExpPattern {\n    // parser state\n    this.idx = 0;\n    this.input = input;\n    this.groupIdx = 0;\n\n    this.consumeChar(\"/\");\n    const value = this.disjunction();\n    this.consumeChar(\"/\");\n\n    const flags: RegExpFlags = {\n      type: \"Flags\",\n      loc: { begin: this.idx, end: input.length },\n      global: false,\n      ignoreCase: false,\n      multiLine: false,\n      unicode: false,\n      sticky: false,\n    };\n\n    while (this.isRegExpFlag()) {\n      switch (this.popChar()) {\n        case \"g\":\n          addFlag(flags, \"global\");\n          break;\n        case \"i\":\n          addFlag(flags, \"ignoreCase\");\n          break;\n        case \"m\":\n          addFlag(flags, \"multiLine\");\n          break;\n        case \"u\":\n          addFlag(flags, \"unicode\");\n          break;\n        case \"y\":\n          addFlag(flags, \"sticky\");\n          break;\n      }\n    }\n\n    if (this.idx !== this.input.length) {\n      throw Error(\"Redundant input: \" + this.input.substring(this.idx));\n    }\n    return {\n      type: \"Pattern\",\n      flags: flags,\n      value: value,\n      loc: this.loc(0),\n    };\n  }\n\n  protected disjunction(): Disjunction {\n    const alts = [];\n    const begin = this.idx;\n\n    alts.push(this.alternative());\n\n    while (this.peekChar() === \"|\") {\n      this.consumeChar(\"|\");\n      alts.push(this.alternative());\n    }\n\n    return { type: \"Disjunction\", value: alts, loc: this.loc(begin) };\n  }\n\n  protected alternative(): Alternative {\n    const terms = [];\n    const begin = this.idx;\n\n    while (this.isTerm()) {\n      terms.push(this.term());\n    }\n\n    return { type: \"Alternative\", value: terms, loc: this.loc(begin) };\n  }\n\n  protected term(): Term {\n    if (this.isAssertion()) {\n      return this.assertion();\n    } else {\n      return this.atom();\n    }\n  }\n\n  protected assertion(): Assertion {\n    const begin = this.idx;\n    switch (this.popChar()) {\n      case \"^\":\n        return {\n          type: \"StartAnchor\",\n          loc: this.loc(begin),\n        };\n      case \"$\":\n        return { type: \"EndAnchor\", loc: this.loc(begin) };\n      // '\\b' or '\\B'\n      case \"\\\\\":\n        switch (this.popChar()) {\n          case \"b\":\n            return {\n              type: \"WordBoundary\",\n              loc: this.loc(begin),\n            };\n          case \"B\":\n            return {\n              type: \"NonWordBoundary\",\n              loc: this.loc(begin),\n            };\n        }\n        // istanbul ignore next\n        throw Error(\"Invalid Assertion Escape\");\n      // '(?=' or '(?!'\n      case \"(\":\n        this.consumeChar(\"?\");\n\n        let type: \"Lookahead\" | \"NegativeLookahead\" | undefined;\n        switch (this.popChar()) {\n          case \"=\":\n            type = \"Lookahead\";\n            break;\n          case \"!\":\n            type = \"NegativeLookahead\";\n            break;\n        }\n        ASSERT_EXISTS(type);\n\n        const disjunction = this.disjunction();\n\n        this.consumeChar(\")\");\n\n        return {\n          type: type!,\n          value: disjunction,\n          loc: this.loc(begin),\n        };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n\n  protected quantifier(\n    isBacktracking: boolean = false,\n  ): Quantifier | undefined {\n    let range: Partial<Quantifier> | undefined = undefined;\n    const begin = this.idx;\n    switch (this.popChar()) {\n      case \"*\":\n        range = {\n          atLeast: 0,\n          atMost: Infinity,\n        };\n        break;\n      case \"+\":\n        range = {\n          atLeast: 1,\n          atMost: Infinity,\n        };\n        break;\n      case \"?\":\n        range = {\n          atLeast: 0,\n          atMost: 1,\n        };\n        break;\n      case \"{\":\n        const atLeast = this.integerIncludingZero();\n        switch (this.popChar()) {\n          case \"}\":\n            range = {\n              atLeast: atLeast,\n              atMost: atLeast,\n            };\n            break;\n          case \",\":\n            let atMost;\n            if (this.isDigit()) {\n              atMost = this.integerIncludingZero();\n              range = {\n                atLeast: atLeast,\n                atMost: atMost,\n              };\n            } else {\n              range = {\n                atLeast: atLeast,\n                atMost: Infinity,\n              };\n            }\n            this.consumeChar(\"}\");\n            break;\n        }\n        // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n        // causes severe performance degradations\n        if (isBacktracking === true && range === undefined) {\n          return undefined;\n        }\n        ASSERT_EXISTS(range);\n        break;\n    }\n\n    // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n    // causes severe performance degradations\n    if (isBacktracking === true && range === undefined) {\n      return undefined;\n    }\n\n    // istanbul ignore else\n    if (ASSERT_EXISTS(range)) {\n      if (this.peekChar(0) === \"?\") {\n        this.consumeChar(\"?\");\n        range.greedy = false;\n      } else {\n        range.greedy = true;\n      }\n\n      range.type = \"Quantifier\";\n      range.loc = this.loc(begin);\n      return range as Quantifier;\n    }\n  }\n\n  protected atom(): Atom {\n    let atom: Omit<Atom, \"loc\" | \"type\"> | undefined;\n    const begin = this.idx;\n    switch (this.peekChar()) {\n      case \".\":\n        atom = this.dotAll();\n        break;\n      case \"\\\\\":\n        atom = this.atomEscape();\n        break;\n      case \"[\":\n        atom = this.characterClass();\n        break;\n      case \"(\":\n        atom = this.group();\n        break;\n    }\n\n    if (atom === undefined && this.isPatternCharacter()) {\n      atom = this.patternCharacter();\n    }\n\n    // istanbul ignore else\n    if (ASSERT_EXISTS<Atom>(atom)) {\n      atom.loc = this.loc(begin);\n\n      if (this.isQuantifier()) {\n        atom.quantifier = this.quantifier();\n      }\n\n      return atom;\n    }\n\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n\n  protected dotAll(): Omit<Set, \"loc\"> {\n    this.consumeChar(\".\");\n    return {\n      type: \"Set\",\n      complement: true,\n      value: [cc(\"\\n\"), cc(\"\\r\"), cc(\"\\u2028\"), cc(\"\\u2029\")],\n    };\n  }\n\n  protected atomEscape(): Omit<GroupBackReference | Set | Character, \"loc\"> {\n    this.consumeChar(\"\\\\\");\n\n    switch (this.peekChar()) {\n      case \"1\":\n      case \"2\":\n      case \"3\":\n      case \"4\":\n      case \"5\":\n      case \"6\":\n      case \"7\":\n      case \"8\":\n      case \"9\":\n        return this.decimalEscapeAtom();\n      case \"d\":\n      case \"D\":\n      case \"s\":\n      case \"S\":\n      case \"w\":\n      case \"W\":\n        return this.characterClassEscape();\n      case \"f\":\n      case \"n\":\n      case \"r\":\n      case \"t\":\n      case \"v\":\n        return this.controlEscapeAtom();\n      case \"c\":\n        return this.controlLetterEscapeAtom();\n      case \"0\":\n        return this.nulCharacterAtom();\n      case \"x\":\n        return this.hexEscapeSequenceAtom();\n      case \"u\":\n        return this.regExpUnicodeEscapeSequenceAtom();\n      default:\n        return this.identityEscapeAtom();\n    }\n  }\n\n  protected decimalEscapeAtom(): Omit<GroupBackReference, \"loc\"> {\n    const value = this.positiveInteger();\n\n    return { type: \"GroupBackReference\", value: value };\n  }\n\n  protected characterClassEscape(): Omit<Set, \"loc\"> {\n    let set: (number | Range)[] | undefined;\n    let complement = false;\n    switch (this.popChar()) {\n      case \"d\":\n        set = digitsCharCodes;\n        break;\n      case \"D\":\n        set = digitsCharCodes;\n        complement = true;\n        break;\n      case \"s\":\n        set = whitespaceCodes;\n        break;\n      case \"S\":\n        set = whitespaceCodes;\n        complement = true;\n        break;\n      case \"w\":\n        set = wordCharCodes;\n        break;\n      case \"W\":\n        set = wordCharCodes;\n        complement = true;\n        break;\n    }\n\n    // istanbul ignore else\n    if (ASSERT_EXISTS(set)) {\n      return { type: \"Set\", value: set, complement: complement };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n\n  protected controlEscapeAtom(): Omit<Character, \"loc\"> {\n    let escapeCode;\n    switch (this.popChar()) {\n      case \"f\":\n        escapeCode = cc(\"\\f\");\n        break;\n      case \"n\":\n        escapeCode = cc(\"\\n\");\n        break;\n      case \"r\":\n        escapeCode = cc(\"\\r\");\n        break;\n      case \"t\":\n        escapeCode = cc(\"\\t\");\n        break;\n      case \"v\":\n        escapeCode = cc(\"\\v\");\n        break;\n    }\n\n    // istanbul ignore else\n    if (ASSERT_EXISTS(escapeCode)) {\n      return { type: \"Character\", value: escapeCode };\n    }\n    // istanbul ignore next\n    return ASSERT_NEVER_REACH_HERE();\n  }\n\n  protected controlLetterEscapeAtom(): Omit<Character, \"loc\"> {\n    this.consumeChar(\"c\");\n    const letter = this.popChar();\n    if (/[a-zA-Z]/.test(letter) === false) {\n      throw Error(\"Invalid \");\n    }\n\n    const letterCode = letter.toUpperCase().charCodeAt(0) - 64;\n    return { type: \"Character\", value: letterCode };\n  }\n\n  protected nulCharacterAtom(): Omit<Character, \"loc\"> {\n    // TODO implement '[lookahead ∉ DecimalDigit]'\n    // TODO: for the deprecated octal escape sequence\n    this.consumeChar(\"0\");\n    return { type: \"Character\", value: cc(\"\\0\") };\n  }\n\n  protected hexEscapeSequenceAtom(): Omit<Character, \"loc\"> {\n    this.consumeChar(\"x\");\n    return this.parseHexDigits(2);\n  }\n\n  protected regExpUnicodeEscapeSequenceAtom(): Omit<Character, \"loc\"> {\n    this.consumeChar(\"u\");\n    return this.parseHexDigits(4);\n  }\n\n  protected identityEscapeAtom(): Omit<Character, \"loc\"> {\n    // TODO: implement \"SourceCharacter but not UnicodeIDContinue\"\n    // // http://unicode.org/reports/tr31/#Specific_Character_Adjustments\n    const escapedChar = this.popChar();\n    return { type: \"Character\", value: cc(escapedChar) };\n  }\n\n  protected classPatternCharacterAtom(): Omit<Character, \"loc\"> {\n    switch (this.peekChar()) {\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n      // istanbul ignore next\n      case \"\\\\\":\n      // istanbul ignore next\n      case \"]\":\n        throw Error(\"TBD\");\n      default:\n        const nextChar = this.popChar();\n        return { type: \"Character\", value: cc(nextChar) };\n    }\n  }\n\n  protected characterClass(): Omit<Set, \"loc\"> {\n    const set: (number | Range)[] = [];\n    let complement = false;\n    this.consumeChar(\"[\");\n    if (this.peekChar(0) === \"^\") {\n      this.consumeChar(\"^\");\n      complement = true;\n    }\n\n    while (this.isClassAtom()) {\n      const from = this.classAtom();\n      const isFromSingleChar = from.type === \"Character\";\n      if (isCharacter(from) && this.isRangeDash()) {\n        this.consumeChar(\"-\");\n        const to = this.classAtom();\n        const isToSingleChar = to.type === \"Character\";\n\n        // a range can only be used when both sides are single characters\n        if (isCharacter(to)) {\n          if (to.value < from.value) {\n            throw Error(\"Range out of order in character class\");\n          }\n          set.push({ from: from.value, to: to.value });\n        } else {\n          // literal dash\n          insertToSet(from.value, set);\n          set.push(cc(\"-\"));\n          insertToSet(to.value, set);\n        }\n      } else {\n        insertToSet(from.value, set);\n      }\n    }\n\n    this.consumeChar(\"]\");\n\n    return { type: \"Set\", complement: complement, value: set };\n  }\n\n  protected classAtom(): Omit<Character | Set, \"loc\"> {\n    switch (this.peekChar()) {\n      // istanbul ignore next\n      case \"]\":\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n        throw Error(\"TBD\");\n      case \"\\\\\":\n        return this.classEscape();\n      default:\n        return this.classPatternCharacterAtom();\n    }\n  }\n\n  protected classEscape(): Omit<Character | Set, \"loc\"> {\n    this.consumeChar(\"\\\\\");\n    switch (this.peekChar()) {\n      // Matches a backspace.\n      // (Not to be confused with \\b word boundary outside characterClass)\n      case \"b\":\n        this.consumeChar(\"b\");\n        return { type: \"Character\", value: cc(\"\\u0008\") };\n      case \"d\":\n      case \"D\":\n      case \"s\":\n      case \"S\":\n      case \"w\":\n      case \"W\":\n        return this.characterClassEscape();\n      case \"f\":\n      case \"n\":\n      case \"r\":\n      case \"t\":\n      case \"v\":\n        return this.controlEscapeAtom();\n      case \"c\":\n        return this.controlLetterEscapeAtom();\n      case \"0\":\n        return this.nulCharacterAtom();\n      case \"x\":\n        return this.hexEscapeSequenceAtom();\n      case \"u\":\n        return this.regExpUnicodeEscapeSequenceAtom();\n      default:\n        return this.identityEscapeAtom();\n    }\n  }\n\n  protected group(): Omit<Group, \"loc\"> {\n    let capturing = true;\n    this.consumeChar(\"(\");\n    switch (this.peekChar(0)) {\n      case \"?\":\n        this.consumeChar(\"?\");\n        this.consumeChar(\":\");\n        capturing = false;\n        break;\n      default:\n        this.groupIdx++;\n        break;\n    }\n    const value = this.disjunction();\n    this.consumeChar(\")\");\n\n    const groupAst: Omit<Group, \"loc\"> = {\n      type: \"Group\",\n      capturing: capturing,\n      value: value,\n    };\n\n    if (capturing) {\n      groupAst[\"idx\"] = this.groupIdx;\n    }\n\n    return groupAst;\n  }\n\n  protected positiveInteger(): number {\n    let number = this.popChar();\n\n    // istanbul ignore next - can't ever get here due to previous lookahead checks\n    // still implementing this error checking in case this ever changes.\n    if (decimalPatternNoZero.test(number) === false) {\n      throw Error(\"Expecting a positive integer\");\n    }\n\n    while (decimalPattern.test(this.peekChar(0))) {\n      number += this.popChar();\n    }\n\n    return parseInt(number, 10);\n  }\n\n  protected integerIncludingZero(): number {\n    let number = this.popChar();\n    if (decimalPattern.test(number) === false) {\n      throw Error(\"Expecting an integer\");\n    }\n\n    while (decimalPattern.test(this.peekChar(0))) {\n      number += this.popChar();\n    }\n\n    return parseInt(number, 10);\n  }\n\n  protected patternCharacter(): Omit<Character, \"loc\"> {\n    const nextChar = this.popChar();\n    switch (nextChar) {\n      // istanbul ignore next\n      case \"\\n\":\n      // istanbul ignore next\n      case \"\\r\":\n      // istanbul ignore next\n      case \"\\u2028\":\n      // istanbul ignore next\n      case \"\\u2029\":\n      // istanbul ignore next\n      case \"^\":\n      // istanbul ignore next\n      case \"$\":\n      // istanbul ignore next\n      case \"\\\\\":\n      // istanbul ignore next\n      case \".\":\n      // istanbul ignore next\n      case \"*\":\n      // istanbul ignore next\n      case \"+\":\n      // istanbul ignore next\n      case \"?\":\n      // istanbul ignore next\n      case \"(\":\n      // istanbul ignore next\n      case \")\":\n      // istanbul ignore next\n      case \"[\":\n      // istanbul ignore next\n      case \"|\":\n        // istanbul ignore next\n        throw Error(\"TBD\");\n      default:\n        return { type: \"Character\", value: cc(nextChar) };\n    }\n  }\n  protected isRegExpFlag(): boolean {\n    switch (this.peekChar(0)) {\n      case \"g\":\n      case \"i\":\n      case \"m\":\n      case \"u\":\n      case \"y\":\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  protected isRangeDash(): boolean {\n    return this.peekChar() === \"-\" && this.isClassAtom(1);\n  }\n\n  protected isDigit(): boolean {\n    return decimalPattern.test(this.peekChar(0));\n  }\n\n  protected isClassAtom(howMuch = 0): boolean {\n    switch (this.peekChar(howMuch)) {\n      case \"]\":\n      case \"\\n\":\n      case \"\\r\":\n      case \"\\u2028\":\n      case \"\\u2029\":\n        return false;\n      default:\n        return true;\n    }\n  }\n\n  protected isTerm() {\n    return this.isAtom() || this.isAssertion();\n  }\n\n  protected isAtom(): boolean {\n    if (this.isPatternCharacter()) {\n      return true;\n    }\n\n    switch (this.peekChar(0)) {\n      case \".\":\n      case \"\\\\\": // atomEscape\n      case \"[\": // characterClass\n      // TODO: isAtom must be called before isAssertion - disambiguate\n      case \"(\": // group\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  protected isAssertion(): boolean {\n    switch (this.peekChar(0)) {\n      case \"^\":\n      case \"$\":\n        return true;\n      // '\\b' or '\\B'\n      case \"\\\\\":\n        switch (this.peekChar(1)) {\n          case \"b\":\n          case \"B\":\n            return true;\n          default:\n            return false;\n        }\n      // '(?=' or '(?!'\n      case \"(\":\n        return (\n          this.peekChar(1) === \"?\" &&\n          (this.peekChar(2) === \"=\" || this.peekChar(2) === \"!\")\n        );\n      default:\n        return false;\n    }\n  }\n\n  protected isQuantifier(): boolean {\n    const prevState = this.saveState();\n    try {\n      return this.quantifier(true) !== undefined;\n    } catch (e) {\n      return false;\n    } finally {\n      this.restoreState(prevState);\n    }\n  }\n\n  protected isPatternCharacter(): boolean {\n    switch (this.peekChar()) {\n      case \"^\":\n      case \"$\":\n      case \"\\\\\":\n      case \".\":\n      case \"*\":\n      case \"+\":\n      case \"?\":\n      case \"(\":\n      case \")\":\n      case \"[\":\n      case \"|\":\n      case \"/\":\n      case \"\\n\":\n      case \"\\r\":\n      case \"\\u2028\":\n      case \"\\u2029\":\n        return false;\n      default:\n        return true;\n    }\n  }\n\n  protected parseHexDigits(howMany: number): Omit<Character, \"loc\"> {\n    let hexString = \"\";\n    for (let i = 0; i < howMany; i++) {\n      const hexChar = this.popChar();\n      if (hexDigitPattern.test(hexChar) === false) {\n        throw Error(\"Expecting a HexDecimal digits\");\n      }\n      hexString += hexChar;\n    }\n    const charCode = parseInt(hexString, 16);\n    return { type: \"Character\", value: charCode };\n  }\n\n  protected peekChar(howMuch = 0): string {\n    return this.input[this.idx + howMuch];\n  }\n\n  protected popChar(): string {\n    const nextChar = this.peekChar(0);\n    this.consumeChar(undefined);\n    return nextChar;\n  }\n\n  protected consumeChar(char: string | undefined): void {\n    if (char !== undefined && this.input[this.idx] !== char) {\n      throw Error(\n        \"Expected: '\" +\n          char +\n          \"' but found: '\" +\n          this.input[this.idx] +\n          \"' at offset: \" +\n          this.idx,\n      );\n    }\n\n    if (this.idx >= this.input.length) {\n      throw Error(\"Unexpected end of input\");\n    }\n    this.idx++;\n  }\n\n  protected loc(begin: number): Location {\n    return { begin: begin, end: this.idx };\n  }\n}\n"], "mappings": "AAgBA,SACEA,OAAO,EACPC,aAAa,EACbC,uBAAuB,EACvBC,EAAE,EACFC,WAAW,EACXC,WAAW,QACN,YAAY;AACnB,SACEC,eAAe,EACfC,eAAe,EACfC,aAAa,QACR,wBAAwB;AAE/B;AACA,MAAMC,eAAe,GAAG,aAAa;AACrC,MAAMC,cAAc,GAAG,OAAO;AAC9B,MAAMC,oBAAoB,GAAG,OAAO;AAEpC;AACA;AACA,OAAM,MAAOC,YAAY;EAAzBC,YAAA;IACY,KAAAC,GAAG,GAAW,CAAC;IACf,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,CAAC;EA+xBhC;EA7xBYC,SAASA,CAAA;IACjB,OAAO;MACLH,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,QAAQ,EAAE,IAAI,CAACA;KAChB;EACH;EAEUE,YAAYA,CAACC,QAItB;IACC,IAAI,CAACL,GAAG,GAAGK,QAAQ,CAACL,GAAG;IACvB,IAAI,CAACC,KAAK,GAAGI,QAAQ,CAACJ,KAAK;IAC3B,IAAI,CAACC,QAAQ,GAAGG,QAAQ,CAACH,QAAQ;EACnC;EAEOI,OAAOA,CAACL,KAAa;IAC1B;IACA,IAAI,CAACD,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC;IAEjB,IAAI,CAACK,WAAW,CAAC,GAAG,CAAC;IACrB,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,EAAE;IAChC,IAAI,CAACF,WAAW,CAAC,GAAG,CAAC;IAErB,MAAMG,KAAK,GAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE;QAAEC,KAAK,EAAE,IAAI,CAACb,GAAG;QAAEc,GAAG,EAAEb,KAAK,CAACc;MAAM,CAAE;MAC3CC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;KACT;IAED,OAAO,IAAI,CAACC,YAAY,EAAE,EAAE;MAC1B,QAAQ,IAAI,CAACC,OAAO,EAAE;QACpB,KAAK,GAAG;UACNpC,OAAO,CAACwB,KAAK,EAAE,QAAQ,CAAC;UACxB;QACF,KAAK,GAAG;UACNxB,OAAO,CAACwB,KAAK,EAAE,YAAY,CAAC;UAC5B;QACF,KAAK,GAAG;UACNxB,OAAO,CAACwB,KAAK,EAAE,WAAW,CAAC;UAC3B;QACF,KAAK,GAAG;UACNxB,OAAO,CAACwB,KAAK,EAAE,SAAS,CAAC;UACzB;QACF,KAAK,GAAG;UACNxB,OAAO,CAACwB,KAAK,EAAE,QAAQ,CAAC;UACxB;;;IAIN,IAAI,IAAI,CAACV,GAAG,KAAK,IAAI,CAACC,KAAK,CAACc,MAAM,EAAE;MAClC,MAAMQ,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAACtB,KAAK,CAACuB,SAAS,CAAC,IAAI,CAACxB,GAAG,CAAC,CAAC;;IAEnE,OAAO;MACLW,IAAI,EAAE,SAAS;MACfD,KAAK,EAAEA,KAAK;MACZF,KAAK,EAAEA,KAAK;MACZI,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC;KAChB;EACH;EAEUH,WAAWA,CAAA;IACnB,MAAMgB,IAAI,GAAG,EAAE;IACf,MAAMZ,KAAK,GAAG,IAAI,CAACb,GAAG;IAEtByB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,CAAC;IAE7B,OAAO,IAAI,CAACC,QAAQ,EAAE,KAAK,GAAG,EAAE;MAC9B,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC;MACrBkB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,CAAC;;IAG/B,OAAO;MAAEhB,IAAI,EAAE,aAAa;MAAEH,KAAK,EAAEiB,IAAI;MAAEb,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;IAAC,CAAE;EACnE;EAEUc,WAAWA,CAAA;IACnB,MAAME,KAAK,GAAG,EAAE;IAChB,MAAMhB,KAAK,GAAG,IAAI,CAACb,GAAG;IAEtB,OAAO,IAAI,CAAC8B,MAAM,EAAE,EAAE;MACpBD,KAAK,CAACH,IAAI,CAAC,IAAI,CAACK,IAAI,EAAE,CAAC;;IAGzB,OAAO;MAAEpB,IAAI,EAAE,aAAa;MAAEH,KAAK,EAAEqB,KAAK;MAAEjB,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;IAAC,CAAE;EACpE;EAEUkB,IAAIA,CAAA;IACZ,IAAI,IAAI,CAACC,WAAW,EAAE,EAAE;MACtB,OAAO,IAAI,CAACC,SAAS,EAAE;KACxB,MAAM;MACL,OAAO,IAAI,CAACC,IAAI,EAAE;;EAEtB;EAEUD,SAASA,CAAA;IACjB,MAAMpB,KAAK,GAAG,IAAI,CAACb,GAAG;IACtB,QAAQ,IAAI,CAACsB,OAAO,EAAE;MACpB,KAAK,GAAG;QACN,OAAO;UACLX,IAAI,EAAE,aAAa;UACnBC,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;SACpB;MACH,KAAK,GAAG;QACN,OAAO;UAAEF,IAAI,EAAE,WAAW;UAAEC,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;QAAC,CAAE;MACpD;MACA,KAAK,IAAI;QACP,QAAQ,IAAI,CAACS,OAAO,EAAE;UACpB,KAAK,GAAG;YACN,OAAO;cACLX,IAAI,EAAE,cAAc;cACpBC,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;aACpB;UACH,KAAK,GAAG;YACN,OAAO;cACLF,IAAI,EAAE,iBAAiB;cACvBC,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;aACpB;;QAEL;QACA,MAAMU,KAAK,CAAC,0BAA0B,CAAC;MACzC;MACA,KAAK,GAAG;QACN,IAAI,CAAChB,WAAW,CAAC,GAAG,CAAC;QAErB,IAAII,IAAmD;QACvD,QAAQ,IAAI,CAACW,OAAO,EAAE;UACpB,KAAK,GAAG;YACNX,IAAI,GAAG,WAAW;YAClB;UACF,KAAK,GAAG;YACNA,IAAI,GAAG,mBAAmB;YAC1B;;QAEJxB,aAAa,CAACwB,IAAI,CAAC;QAEnB,MAAMF,WAAW,GAAG,IAAI,CAACA,WAAW,EAAE;QAEtC,IAAI,CAACF,WAAW,CAAC,GAAG,CAAC;QAErB,OAAO;UACLI,IAAI,EAAEA,IAAK;UACXH,KAAK,EAAEC,WAAW;UAClBG,GAAG,EAAE,IAAI,CAACA,GAAG,CAACC,KAAK;SACpB;;IAEL;IACA,OAAOzB,uBAAuB,EAAE;EAClC;EAEU+C,UAAUA,CAClBC,cAAA,GAA0B,KAAK;IAE/B,IAAIC,KAAK,GAAoCC,SAAS;IACtD,MAAMzB,KAAK,GAAG,IAAI,CAACb,GAAG;IACtB,QAAQ,IAAI,CAACsB,OAAO,EAAE;MACpB,KAAK,GAAG;QACNe,KAAK,GAAG;UACNE,OAAO,EAAE,CAAC;UACVC,MAAM,EAAEC;SACT;QACD;MACF,KAAK,GAAG;QACNJ,KAAK,GAAG;UACNE,OAAO,EAAE,CAAC;UACVC,MAAM,EAAEC;SACT;QACD;MACF,KAAK,GAAG;QACNJ,KAAK,GAAG;UACNE,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE;SACT;QACD;MACF,KAAK,GAAG;QACN,MAAMD,OAAO,GAAG,IAAI,CAACG,oBAAoB,EAAE;QAC3C,QAAQ,IAAI,CAACpB,OAAO,EAAE;UACpB,KAAK,GAAG;YACNe,KAAK,GAAG;cACNE,OAAO,EAAEA,OAAO;cAChBC,MAAM,EAAED;aACT;YACD;UACF,KAAK,GAAG;YACN,IAAIC,MAAM;YACV,IAAI,IAAI,CAACG,OAAO,EAAE,EAAE;cAClBH,MAAM,GAAG,IAAI,CAACE,oBAAoB,EAAE;cACpCL,KAAK,GAAG;gBACNE,OAAO,EAAEA,OAAO;gBAChBC,MAAM,EAAEA;eACT;aACF,MAAM;cACLH,KAAK,GAAG;gBACNE,OAAO,EAAEA,OAAO;gBAChBC,MAAM,EAAEC;eACT;;YAEH,IAAI,CAAClC,WAAW,CAAC,GAAG,CAAC;YACrB;;QAEJ;QACA;QACA,IAAI6B,cAAc,KAAK,IAAI,IAAIC,KAAK,KAAKC,SAAS,EAAE;UAClD,OAAOA,SAAS;;QAElBnD,aAAa,CAACkD,KAAK,CAAC;QACpB;;IAGJ;IACA;IACA,IAAID,cAAc,KAAK,IAAI,IAAIC,KAAK,KAAKC,SAAS,EAAE;MAClD,OAAOA,SAAS;;IAGlB;IACA,IAAInD,aAAa,CAACkD,KAAK,CAAC,EAAE;MACxB,IAAI,IAAI,CAACT,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5B,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC;QACrB8B,KAAK,CAACO,MAAM,GAAG,KAAK;OACrB,MAAM;QACLP,KAAK,CAACO,MAAM,GAAG,IAAI;;MAGrBP,KAAK,CAAC1B,IAAI,GAAG,YAAY;MACzB0B,KAAK,CAACzB,GAAG,GAAG,IAAI,CAACA,GAAG,CAACC,KAAK,CAAC;MAC3B,OAAOwB,KAAmB;;EAE9B;EAEUH,IAAIA,CAAA;IACZ,IAAIA,IAA4C;IAChD,MAAMrB,KAAK,GAAG,IAAI,CAACb,GAAG;IACtB,QAAQ,IAAI,CAAC4B,QAAQ,EAAE;MACrB,KAAK,GAAG;QACNM,IAAI,GAAG,IAAI,CAACW,MAAM,EAAE;QACpB;MACF,KAAK,IAAI;QACPX,IAAI,GAAG,IAAI,CAACY,UAAU,EAAE;QACxB;MACF,KAAK,GAAG;QACNZ,IAAI,GAAG,IAAI,CAACa,cAAc,EAAE;QAC5B;MACF,KAAK,GAAG;QACNb,IAAI,GAAG,IAAI,CAACc,KAAK,EAAE;QACnB;;IAGJ,IAAId,IAAI,KAAKI,SAAS,IAAI,IAAI,CAACW,kBAAkB,EAAE,EAAE;MACnDf,IAAI,GAAG,IAAI,CAACgB,gBAAgB,EAAE;;IAGhC;IACA,IAAI/D,aAAa,CAAO+C,IAAI,CAAC,EAAE;MAC7BA,IAAI,CAACtB,GAAG,GAAG,IAAI,CAACA,GAAG,CAACC,KAAK,CAAC;MAE1B,IAAI,IAAI,CAACsC,YAAY,EAAE,EAAE;QACvBjB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,EAAE;;MAGrC,OAAOD,IAAI;;IAGb;IACA,OAAO9C,uBAAuB,EAAE;EAClC;EAEUyD,MAAMA,CAAA;IACd,IAAI,CAACtC,WAAW,CAAC,GAAG,CAAC;IACrB,OAAO;MACLI,IAAI,EAAE,KAAK;MACXyC,UAAU,EAAE,IAAI;MAChB5C,KAAK,EAAE,CAACnB,EAAE,CAAC,IAAI,CAAC,EAAEA,EAAE,CAAC,IAAI,CAAC,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAEA,EAAE,CAAC,QAAQ,CAAC;KACvD;EACH;EAEUyD,UAAUA,CAAA;IAClB,IAAI,CAACvC,WAAW,CAAC,IAAI,CAAC;IAEtB,QAAQ,IAAI,CAACqB,QAAQ,EAAE;MACrB,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,CAACyB,iBAAiB,EAAE;MACjC,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,oBAAoB,EAAE;MACpC,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,iBAAiB,EAAE;MACjC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,uBAAuB,EAAE;MACvC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,gBAAgB,EAAE;MAChC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,qBAAqB,EAAE;MACrC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,+BAA+B,EAAE;MAC/C;QACE,OAAO,IAAI,CAACC,kBAAkB,EAAE;;EAEtC;EAEUP,iBAAiBA,CAAA;IACzB,MAAM7C,KAAK,GAAG,IAAI,CAACqD,eAAe,EAAE;IAEpC,OAAO;MAAElD,IAAI,EAAE,oBAAoB;MAAEH,KAAK,EAAEA;IAAK,CAAE;EACrD;EAEU8C,oBAAoBA,CAAA;IAC5B,IAAIQ,GAAmC;IACvC,IAAIV,UAAU,GAAG,KAAK;IACtB,QAAQ,IAAI,CAAC9B,OAAO,EAAE;MACpB,KAAK,GAAG;QACNwC,GAAG,GAAGtE,eAAe;QACrB;MACF,KAAK,GAAG;QACNsE,GAAG,GAAGtE,eAAe;QACrB4D,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,GAAG;QACNU,GAAG,GAAGrE,eAAe;QACrB;MACF,KAAK,GAAG;QACNqE,GAAG,GAAGrE,eAAe;QACrB2D,UAAU,GAAG,IAAI;QACjB;MACF,KAAK,GAAG;QACNU,GAAG,GAAGpE,aAAa;QACnB;MACF,KAAK,GAAG;QACNoE,GAAG,GAAGpE,aAAa;QACnB0D,UAAU,GAAG,IAAI;QACjB;;IAGJ;IACA,IAAIjE,aAAa,CAAC2E,GAAG,CAAC,EAAE;MACtB,OAAO;QAAEnD,IAAI,EAAE,KAAK;QAAEH,KAAK,EAAEsD,GAAG;QAAEV,UAAU,EAAEA;MAAU,CAAE;;IAE5D;IACA,OAAOhE,uBAAuB,EAAE;EAClC;EAEUmE,iBAAiBA,CAAA;IACzB,IAAIQ,UAAU;IACd,QAAQ,IAAI,CAACzC,OAAO,EAAE;MACpB,KAAK,GAAG;QACNyC,UAAU,GAAG1E,EAAE,CAAC,IAAI,CAAC;QACrB;MACF,KAAK,GAAG;QACN0E,UAAU,GAAG1E,EAAE,CAAC,IAAI,CAAC;QACrB;MACF,KAAK,GAAG;QACN0E,UAAU,GAAG1E,EAAE,CAAC,IAAI,CAAC;QACrB;MACF,KAAK,GAAG;QACN0E,UAAU,GAAG1E,EAAE,CAAC,IAAI,CAAC;QACrB;MACF,KAAK,GAAG;QACN0E,UAAU,GAAG1E,EAAE,CAAC,IAAI,CAAC;QACrB;;IAGJ;IACA,IAAIF,aAAa,CAAC4E,UAAU,CAAC,EAAE;MAC7B,OAAO;QAAEpD,IAAI,EAAE,WAAW;QAAEH,KAAK,EAAEuD;MAAU,CAAE;;IAEjD;IACA,OAAO3E,uBAAuB,EAAE;EAClC;EAEUoE,uBAAuBA,CAAA;IAC/B,IAAI,CAACjD,WAAW,CAAC,GAAG,CAAC;IACrB,MAAMyD,MAAM,GAAG,IAAI,CAAC1C,OAAO,EAAE;IAC7B,IAAI,UAAU,CAAC2C,IAAI,CAACD,MAAM,CAAC,KAAK,KAAK,EAAE;MACrC,MAAMzC,KAAK,CAAC,UAAU,CAAC;;IAGzB,MAAM2C,UAAU,GAAGF,MAAM,CAACG,WAAW,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;IAC1D,OAAO;MAAEzD,IAAI,EAAE,WAAW;MAAEH,KAAK,EAAE0D;IAAU,CAAE;EACjD;EAEUT,gBAAgBA,CAAA;IACxB;IACA;IACA,IAAI,CAAClD,WAAW,CAAC,GAAG,CAAC;IACrB,OAAO;MAAEI,IAAI,EAAE,WAAW;MAAEH,KAAK,EAAEnB,EAAE,CAAC,IAAI;IAAC,CAAE;EAC/C;EAEUqE,qBAAqBA,CAAA;IAC7B,IAAI,CAACnD,WAAW,CAAC,GAAG,CAAC;IACrB,OAAO,IAAI,CAAC8D,cAAc,CAAC,CAAC,CAAC;EAC/B;EAEUV,+BAA+BA,CAAA;IACvC,IAAI,CAACpD,WAAW,CAAC,GAAG,CAAC;IACrB,OAAO,IAAI,CAAC8D,cAAc,CAAC,CAAC,CAAC;EAC/B;EAEUT,kBAAkBA,CAAA;IAC1B;IACA;IACA,MAAMU,WAAW,GAAG,IAAI,CAAChD,OAAO,EAAE;IAClC,OAAO;MAAEX,IAAI,EAAE,WAAW;MAAEH,KAAK,EAAEnB,EAAE,CAACiF,WAAW;IAAC,CAAE;EACtD;EAEUC,yBAAyBA,CAAA;IACjC,QAAQ,IAAI,CAAC3C,QAAQ,EAAE;MACrB;MACA,KAAK,IAAI;MACT;MACA,KAAK,IAAI;MACT;MACA,KAAK,QAAQ;MACb;MACA,KAAK,QAAQ;MACb;MACA,KAAK,IAAI;MACT;MACA,KAAK,GAAG;QACN,MAAML,KAAK,CAAC,KAAK,CAAC;MACpB;QACE,MAAMiD,QAAQ,GAAG,IAAI,CAAClD,OAAO,EAAE;QAC/B,OAAO;UAAEX,IAAI,EAAE,WAAW;UAAEH,KAAK,EAAEnB,EAAE,CAACmF,QAAQ;QAAC,CAAE;;EAEvD;EAEUzB,cAAcA,CAAA;IACtB,MAAMe,GAAG,GAAuB,EAAE;IAClC,IAAIV,UAAU,GAAG,KAAK;IACtB,IAAI,CAAC7C,WAAW,CAAC,GAAG,CAAC;IACrB,IAAI,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC;MACrB6C,UAAU,GAAG,IAAI;;IAGnB,OAAO,IAAI,CAACqB,WAAW,EAAE,EAAE;MACzB,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,EAAE;MAC7B,MAAMC,gBAAgB,GAAGF,IAAI,CAAC/D,IAAI,KAAK,WAAW;MAClD,IAAIpB,WAAW,CAACmF,IAAI,CAAC,IAAI,IAAI,CAACG,WAAW,EAAE,EAAE;QAC3C,IAAI,CAACtE,WAAW,CAAC,GAAG,CAAC;QACrB,MAAMuE,EAAE,GAAG,IAAI,CAACH,SAAS,EAAE;QAC3B,MAAMI,cAAc,GAAGD,EAAE,CAACnE,IAAI,KAAK,WAAW;QAE9C;QACA,IAAIpB,WAAW,CAACuF,EAAE,CAAC,EAAE;UACnB,IAAIA,EAAE,CAACtE,KAAK,GAAGkE,IAAI,CAAClE,KAAK,EAAE;YACzB,MAAMe,KAAK,CAAC,uCAAuC,CAAC;;UAEtDuC,GAAG,CAACpC,IAAI,CAAC;YAAEgD,IAAI,EAAEA,IAAI,CAAClE,KAAK;YAAEsE,EAAE,EAAEA,EAAE,CAACtE;UAAK,CAAE,CAAC;SAC7C,MAAM;UACL;UACAlB,WAAW,CAACoF,IAAI,CAAClE,KAAK,EAAEsD,GAAG,CAAC;UAC5BA,GAAG,CAACpC,IAAI,CAACrC,EAAE,CAAC,GAAG,CAAC,CAAC;UACjBC,WAAW,CAACwF,EAAE,CAACtE,KAAK,EAAEsD,GAAG,CAAC;;OAE7B,MAAM;QACLxE,WAAW,CAACoF,IAAI,CAAClE,KAAK,EAAEsD,GAAG,CAAC;;;IAIhC,IAAI,CAACvD,WAAW,CAAC,GAAG,CAAC;IAErB,OAAO;MAAEI,IAAI,EAAE,KAAK;MAAEyC,UAAU,EAAEA,UAAU;MAAE5C,KAAK,EAAEsD;IAAG,CAAE;EAC5D;EAEUa,SAASA,CAAA;IACjB,QAAQ,IAAI,CAAC/C,QAAQ,EAAE;MACrB;MACA,KAAK,GAAG;MACR;MACA,KAAK,IAAI;MACT;MACA,KAAK,IAAI;MACT;MACA,KAAK,QAAQ;MACb;MACA,KAAK,QAAQ;QACX,MAAML,KAAK,CAAC,KAAK,CAAC;MACpB,KAAK,IAAI;QACP,OAAO,IAAI,CAACyD,WAAW,EAAE;MAC3B;QACE,OAAO,IAAI,CAACT,yBAAyB,EAAE;;EAE7C;EAEUS,WAAWA,CAAA;IACnB,IAAI,CAACzE,WAAW,CAAC,IAAI,CAAC;IACtB,QAAQ,IAAI,CAACqB,QAAQ,EAAE;MACrB;MACA;MACA,KAAK,GAAG;QACN,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC;QACrB,OAAO;UAAEI,IAAI,EAAE,WAAW;UAAEH,KAAK,EAAEnB,EAAE,CAAC,QAAQ;QAAC,CAAE;MACnD,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,CAACiE,oBAAoB,EAAE;MACpC,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,iBAAiB,EAAE;MACjC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,uBAAuB,EAAE;MACvC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,gBAAgB,EAAE;MAChC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,qBAAqB,EAAE;MACrC,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,+BAA+B,EAAE;MAC/C;QACE,OAAO,IAAI,CAACC,kBAAkB,EAAE;;EAEtC;EAEUZ,KAAKA,CAAA;IACb,IAAIiC,SAAS,GAAG,IAAI;IACpB,IAAI,CAAC1E,WAAW,CAAC,GAAG,CAAC;IACrB,QAAQ,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;MACtB,KAAK,GAAG;QACN,IAAI,CAACrB,WAAW,CAAC,GAAG,CAAC;QACrB,IAAI,CAACA,WAAW,CAAC,GAAG,CAAC;QACrB0E,SAAS,GAAG,KAAK;QACjB;MACF;QACE,IAAI,CAAC/E,QAAQ,EAAE;QACf;;IAEJ,MAAMM,KAAK,GAAG,IAAI,CAACC,WAAW,EAAE;IAChC,IAAI,CAACF,WAAW,CAAC,GAAG,CAAC;IAErB,MAAM2E,QAAQ,GAAuB;MACnCvE,IAAI,EAAE,OAAO;MACbsE,SAAS,EAAEA,SAAS;MACpBzE,KAAK,EAAEA;KACR;IAED,IAAIyE,SAAS,EAAE;MACbC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAChF,QAAQ;;IAGjC,OAAOgF,QAAQ;EACjB;EAEUrB,eAAeA,CAAA;IACvB,IAAIsB,MAAM,GAAG,IAAI,CAAC7D,OAAO,EAAE;IAE3B;IACA;IACA,IAAIzB,oBAAoB,CAACoE,IAAI,CAACkB,MAAM,CAAC,KAAK,KAAK,EAAE;MAC/C,MAAM5D,KAAK,CAAC,8BAA8B,CAAC;;IAG7C,OAAO3B,cAAc,CAACqE,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5CuD,MAAM,IAAI,IAAI,CAAC7D,OAAO,EAAE;;IAG1B,OAAO8D,QAAQ,CAACD,MAAM,EAAE,EAAE,CAAC;EAC7B;EAEUzC,oBAAoBA,CAAA;IAC5B,IAAIyC,MAAM,GAAG,IAAI,CAAC7D,OAAO,EAAE;IAC3B,IAAI1B,cAAc,CAACqE,IAAI,CAACkB,MAAM,CAAC,KAAK,KAAK,EAAE;MACzC,MAAM5D,KAAK,CAAC,sBAAsB,CAAC;;IAGrC,OAAO3B,cAAc,CAACqE,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5CuD,MAAM,IAAI,IAAI,CAAC7D,OAAO,EAAE;;IAG1B,OAAO8D,QAAQ,CAACD,MAAM,EAAE,EAAE,CAAC;EAC7B;EAEUjC,gBAAgBA,CAAA;IACxB,MAAMsB,QAAQ,GAAG,IAAI,CAAClD,OAAO,EAAE;IAC/B,QAAQkD,QAAQ;MACd;MACA,KAAK,IAAI;MACT;MACA,KAAK,IAAI;MACT;MACA,KAAK,QAAQ;MACb;MACA,KAAK,QAAQ;MACb;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,IAAI;MACT;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;MACR;MACA,KAAK,GAAG;QACN;QACA,MAAMjD,KAAK,CAAC,KAAK,CAAC;MACpB;QACE,OAAO;UAAEZ,IAAI,EAAE,WAAW;UAAEH,KAAK,EAAEnB,EAAE,CAACmF,QAAQ;QAAC,CAAE;;EAEvD;EACUnD,YAAYA,CAAA;IACpB,QAAQ,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;MACtB,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI;MACb;QACE,OAAO,KAAK;;EAElB;EAEUiD,WAAWA,CAAA;IACnB,OAAO,IAAI,CAACjD,QAAQ,EAAE,KAAK,GAAG,IAAI,IAAI,CAAC6C,WAAW,CAAC,CAAC,CAAC;EACvD;EAEU9B,OAAOA,CAAA;IACf,OAAO/C,cAAc,CAACqE,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEU6C,WAAWA,CAACY,OAAO,GAAG,CAAC;IAC/B,QAAQ,IAAI,CAACzD,QAAQ,CAACyD,OAAO,CAAC;MAC5B,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,IAAI;MACT,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAO,KAAK;MACd;QACE,OAAO,IAAI;;EAEjB;EAEUvD,MAAMA,CAAA;IACd,OAAO,IAAI,CAACwD,MAAM,EAAE,IAAI,IAAI,CAACtD,WAAW,EAAE;EAC5C;EAEUsD,MAAMA,CAAA;IACd,IAAI,IAAI,CAACrC,kBAAkB,EAAE,EAAE;MAC7B,OAAO,IAAI;;IAGb,QAAQ,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC;MACtB,KAAK,GAAG;MACR,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,GAAG,CAAC,CAAC;MACV;MACA,KAAK,GAAG;QAAE;QACR,OAAO,IAAI;MACb;QACE,OAAO,KAAK;;EAElB;EAEUI,WAAWA,CAAA;IACnB,QAAQ,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC;MACtB,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI;MACb;MACA,KAAK,IAAI;QACP,QAAQ,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;UACtB,KAAK,GAAG;UACR,KAAK,GAAG;YACN,OAAO,IAAI;UACb;YACE,OAAO,KAAK;;MAElB;MACA,KAAK,GAAG;QACN,OACE,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,KACvB,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;MAE1D;QACE,OAAO,KAAK;;EAElB;EAEUuB,YAAYA,CAAA;IACpB,MAAMoC,SAAS,GAAG,IAAI,CAACpF,SAAS,EAAE;IAClC,IAAI;MACF,OAAO,IAAI,CAACgC,UAAU,CAAC,IAAI,CAAC,KAAKG,SAAS;KAC3C,CAAC,OAAOkD,CAAC,EAAE;MACV,OAAO,KAAK;KACb,SAAS;MACR,IAAI,CAACpF,YAAY,CAACmF,SAAS,CAAC;;EAEhC;EAEUtC,kBAAkBA,CAAA;IAC1B,QAAQ,IAAI,CAACrB,QAAQ,EAAE;MACrB,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,IAAI;MACT,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAO,KAAK;MACd;QACE,OAAO,IAAI;;EAEjB;EAEUyC,cAAcA,CAACoB,OAAe;IACtC,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;MAChC,MAAMC,OAAO,GAAG,IAAI,CAACtE,OAAO,EAAE;MAC9B,IAAI3B,eAAe,CAACsE,IAAI,CAAC2B,OAAO,CAAC,KAAK,KAAK,EAAE;QAC3C,MAAMrE,KAAK,CAAC,+BAA+B,CAAC;;MAE9CmE,SAAS,IAAIE,OAAO;;IAEtB,MAAMC,QAAQ,GAAGT,QAAQ,CAACM,SAAS,EAAE,EAAE,CAAC;IACxC,OAAO;MAAE/E,IAAI,EAAE,WAAW;MAAEH,KAAK,EAAEqF;IAAQ,CAAE;EAC/C;EAEUjE,QAAQA,CAACyD,OAAO,GAAG,CAAC;IAC5B,OAAO,IAAI,CAACpF,KAAK,CAAC,IAAI,CAACD,GAAG,GAAGqF,OAAO,CAAC;EACvC;EAEU/D,OAAOA,CAAA;IACf,MAAMkD,QAAQ,GAAG,IAAI,CAAC5C,QAAQ,CAAC,CAAC,CAAC;IACjC,IAAI,CAACrB,WAAW,CAAC+B,SAAS,CAAC;IAC3B,OAAOkC,QAAQ;EACjB;EAEUjE,WAAWA,CAACuF,IAAwB;IAC5C,IAAIA,IAAI,KAAKxD,SAAS,IAAI,IAAI,CAACrC,KAAK,CAAC,IAAI,CAACD,GAAG,CAAC,KAAK8F,IAAI,EAAE;MACvD,MAAMvE,KAAK,CACT,aAAa,GACXuE,IAAI,GACJ,gBAAgB,GAChB,IAAI,CAAC7F,KAAK,CAAC,IAAI,CAACD,GAAG,CAAC,GACpB,eAAe,GACf,IAAI,CAACA,GAAG,CACX;;IAGH,IAAI,IAAI,CAACA,GAAG,IAAI,IAAI,CAACC,KAAK,CAACc,MAAM,EAAE;MACjC,MAAMQ,KAAK,CAAC,yBAAyB,CAAC;;IAExC,IAAI,CAACvB,GAAG,EAAE;EACZ;EAEUY,GAAGA,CAACC,KAAa;IACzB,OAAO;MAAEA,KAAK,EAAEA,KAAK;MAAEC,GAAG,EAAE,IAAI,CAACd;IAAG,CAAE;EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}