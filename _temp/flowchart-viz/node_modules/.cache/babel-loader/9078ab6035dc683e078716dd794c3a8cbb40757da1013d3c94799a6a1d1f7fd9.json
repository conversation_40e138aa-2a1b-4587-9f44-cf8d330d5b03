{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { isCompositeCstNode, isLeafCstNode, isRootCstNode } from '../syntax-tree.js';\nimport { TreeStreamImpl } from './stream.js';\n/**\n * Create a stream of all CST nodes that are directly and indirectly contained in the given root node,\n * including the root node itself.\n */\nexport function streamCst(node) {\n  return new TreeStreamImpl(node, element => {\n    if (isCompositeCstNode(element)) {\n      return element.content;\n    } else {\n      return [];\n    }\n  }, {\n    includeRoot: true\n  });\n}\n/**\n * Create a stream of all leaf nodes that are directly and indirectly contained in the given root node.\n */\nexport function flattenCst(node) {\n  return streamCst(node).filter(isLeafCstNode);\n}\n/**\n * Determines whether the specified cst node is a child of the specified parent node.\n */\nexport function isChildNode(child, parent) {\n  while (child.container) {\n    child = child.container;\n    if (child === parent) {\n      return true;\n    }\n  }\n  return false;\n}\nexport function tokenToRange(token) {\n  // Chevrotain uses 1-based indices everywhere\n  // So we subtract 1 from every value to align with the LSP\n  return {\n    start: {\n      character: token.startColumn - 1,\n      line: token.startLine - 1\n    },\n    end: {\n      character: token.endColumn,\n      // endColumn uses the correct index\n      line: token.endLine - 1\n    }\n  };\n}\nexport function toDocumentSegment(node) {\n  if (!node) {\n    return undefined;\n  }\n  const {\n    offset,\n    end,\n    range\n  } = node;\n  return {\n    range,\n    offset,\n    end,\n    length: end - offset\n  };\n}\nexport var RangeComparison;\n(function (RangeComparison) {\n  RangeComparison[RangeComparison[\"Before\"] = 0] = \"Before\";\n  RangeComparison[RangeComparison[\"After\"] = 1] = \"After\";\n  RangeComparison[RangeComparison[\"OverlapFront\"] = 2] = \"OverlapFront\";\n  RangeComparison[RangeComparison[\"OverlapBack\"] = 3] = \"OverlapBack\";\n  RangeComparison[RangeComparison[\"Inside\"] = 4] = \"Inside\";\n  RangeComparison[RangeComparison[\"Outside\"] = 5] = \"Outside\";\n})(RangeComparison || (RangeComparison = {}));\nexport function compareRange(range, to) {\n  if (range.end.line < to.start.line || range.end.line === to.start.line && range.end.character <= to.start.character) {\n    return RangeComparison.Before;\n  } else if (range.start.line > to.end.line || range.start.line === to.end.line && range.start.character >= to.end.character) {\n    return RangeComparison.After;\n  }\n  const startInside = range.start.line > to.start.line || range.start.line === to.start.line && range.start.character >= to.start.character;\n  const endInside = range.end.line < to.end.line || range.end.line === to.end.line && range.end.character <= to.end.character;\n  if (startInside && endInside) {\n    return RangeComparison.Inside;\n  } else if (startInside) {\n    return RangeComparison.OverlapBack;\n  } else if (endInside) {\n    return RangeComparison.OverlapFront;\n  } else {\n    return RangeComparison.Outside;\n  }\n}\nexport function inRange(range, to) {\n  const comparison = compareRange(range, to);\n  return comparison > RangeComparison.After;\n}\n// The \\p{L} regex matches any unicode letter character, i.e. characters from non-english alphabets\n// Together with \\w it matches any kind of character which can commonly appear in IDs\nexport const DefaultNameRegexp = /^[\\w\\p{L}]$/u;\n/**\n * Performs `findLeafNodeAtOffset` with a minor difference: When encountering a character that matches the `nameRegexp` argument,\n * it will instead return the leaf node at the `offset - 1` position.\n *\n * For LSP services, users expect that the declaration of an element is available if the cursor is directly after the element.\n */\nexport function findDeclarationNodeAtOffset(cstNode, offset, nameRegexp = DefaultNameRegexp) {\n  if (cstNode) {\n    if (offset > 0) {\n      const localOffset = offset - cstNode.offset;\n      const textAtOffset = cstNode.text.charAt(localOffset);\n      if (!nameRegexp.test(textAtOffset)) {\n        offset--;\n      }\n    }\n    return findLeafNodeAtOffset(cstNode, offset);\n  }\n  return undefined;\n}\nexport function findCommentNode(cstNode, commentNames) {\n  if (cstNode) {\n    const previous = getPreviousNode(cstNode, true);\n    if (previous && isCommentNode(previous, commentNames)) {\n      return previous;\n    }\n    if (isRootCstNode(cstNode)) {\n      // Go from the first non-hidden node through all nodes in reverse order\n      // We do this to find the comment node which directly precedes the root node\n      const endIndex = cstNode.content.findIndex(e => !e.hidden);\n      for (let i = endIndex - 1; i >= 0; i--) {\n        const child = cstNode.content[i];\n        if (isCommentNode(child, commentNames)) {\n          return child;\n        }\n      }\n    }\n  }\n  return undefined;\n}\nexport function isCommentNode(cstNode, commentNames) {\n  return isLeafCstNode(cstNode) && commentNames.includes(cstNode.tokenType.name);\n}\n/**\n * Finds the leaf CST node at the specified 0-based string offset.\n * Note that the given offset will be within the range of the returned leaf node.\n *\n * If the offset does not point to a CST node (but just white space), this method will return `undefined`.\n *\n * @param node The CST node to search through.\n * @param offset The specified offset.\n * @returns The CST node at the specified offset.\n */\nexport function findLeafNodeAtOffset(node, offset) {\n  if (isLeafCstNode(node)) {\n    return node;\n  } else if (isCompositeCstNode(node)) {\n    const searchResult = binarySearch(node, offset, false);\n    if (searchResult) {\n      return findLeafNodeAtOffset(searchResult, offset);\n    }\n  }\n  return undefined;\n}\n/**\n * Finds the leaf CST node at the specified 0-based string offset.\n * If no CST node exists at the specified position, it will return the leaf node before it.\n *\n * If there is no leaf node before the specified offset, this method will return `undefined`.\n *\n * @param node The CST node to search through.\n * @param offset The specified offset.\n * @returns The CST node closest to the specified offset.\n */\nexport function findLeafNodeBeforeOffset(node, offset) {\n  if (isLeafCstNode(node)) {\n    return node;\n  } else if (isCompositeCstNode(node)) {\n    const searchResult = binarySearch(node, offset, true);\n    if (searchResult) {\n      return findLeafNodeBeforeOffset(searchResult, offset);\n    }\n  }\n  return undefined;\n}\nfunction binarySearch(node, offset, closest) {\n  let left = 0;\n  let right = node.content.length - 1;\n  let closestNode = undefined;\n  while (left <= right) {\n    const middle = Math.floor((left + right) / 2);\n    const middleNode = node.content[middle];\n    if (middleNode.offset <= offset && middleNode.end > offset) {\n      // Found an exact match\n      return middleNode;\n    }\n    if (middleNode.end <= offset) {\n      // Update the closest node (less than offset) and move to the right half\n      closestNode = closest ? middleNode : undefined;\n      left = middle + 1;\n    } else {\n      // Move to the left half\n      right = middle - 1;\n    }\n  }\n  return closestNode;\n}\nexport function getPreviousNode(node, hidden = true) {\n  while (node.container) {\n    const parent = node.container;\n    let index = parent.content.indexOf(node);\n    while (index > 0) {\n      index--;\n      const previous = parent.content[index];\n      if (hidden || !previous.hidden) {\n        return previous;\n      }\n    }\n    node = parent;\n  }\n  return undefined;\n}\nexport function getNextNode(node, hidden = true) {\n  while (node.container) {\n    const parent = node.container;\n    let index = parent.content.indexOf(node);\n    const last = parent.content.length - 1;\n    while (index < last) {\n      index++;\n      const next = parent.content[index];\n      if (hidden || !next.hidden) {\n        return next;\n      }\n    }\n    node = parent;\n  }\n  return undefined;\n}\nexport function getStartlineNode(node) {\n  if (node.range.start.character === 0) {\n    return node;\n  }\n  const line = node.range.start.line;\n  let last = node;\n  let index;\n  while (node.container) {\n    const parent = node.container;\n    const selfIndex = index !== null && index !== void 0 ? index : parent.content.indexOf(node);\n    if (selfIndex === 0) {\n      node = parent;\n      index = undefined;\n    } else {\n      index = selfIndex - 1;\n      node = parent.content[index];\n    }\n    if (node.range.start.line !== line) {\n      break;\n    }\n    last = node;\n  }\n  return last;\n}\nexport function getInteriorNodes(start, end) {\n  const commonParent = getCommonParent(start, end);\n  if (!commonParent) {\n    return [];\n  }\n  return commonParent.parent.content.slice(commonParent.a + 1, commonParent.b);\n}\nfunction getCommonParent(a, b) {\n  const aParents = getParentChain(a);\n  const bParents = getParentChain(b);\n  let current;\n  for (let i = 0; i < aParents.length && i < bParents.length; i++) {\n    const aParent = aParents[i];\n    const bParent = bParents[i];\n    if (aParent.parent === bParent.parent) {\n      current = {\n        parent: aParent.parent,\n        a: aParent.index,\n        b: bParent.index\n      };\n    } else {\n      break;\n    }\n  }\n  return current;\n}\nfunction getParentChain(node) {\n  const chain = [];\n  while (node.container) {\n    const parent = node.container;\n    const index = parent.content.indexOf(node);\n    chain.push({\n      parent,\n      index\n    });\n    node = parent;\n  }\n  return chain.reverse();\n}", "map": {"version": 3, "names": ["isCompositeCstNode", "isLeafCstNode", "isRootCstNode", "TreeStreamImpl", "streamCst", "node", "element", "content", "includeRoot", "flattenCst", "filter", "isChildNode", "child", "parent", "container", "tokenToRange", "token", "start", "character", "startColumn", "line", "startLine", "end", "endColumn", "endLine", "toDocumentSegment", "undefined", "offset", "range", "length", "RangeComparison", "compareRange", "to", "Before", "After", "startInside", "endInside", "Inside", "OverlapBack", "OverlapFront", "Outside", "inRange", "comparison", "DefaultNameRegexp", "findDeclarationNodeAtOffset", "cstNode", "nameRegexp", "localOffset", "textAtOffset", "text", "char<PERSON>t", "test", "findLeafNodeAtOffset", "findCommentNode", "commentNames", "previous", "getPreviousNode", "isCommentNode", "endIndex", "findIndex", "e", "hidden", "i", "includes", "tokenType", "name", "searchResult", "binarySearch", "findLeafNodeBeforeOffset", "closest", "left", "right", "closestNode", "middle", "Math", "floor", "middleNode", "index", "indexOf", "getNextNode", "last", "next", "getStartlineNode", "selfIndex", "getInteriorNodes", "commonParent", "getCommonParent", "slice", "a", "b", "aParents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bPare<PERSON>", "current", "a<PERSON>arent", "b<PERSON><PERSON><PERSON>", "chain", "push", "reverse"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/cst-utils.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { IToken } from '@chevrotain/types';\r\nimport type { Range } from 'vscode-languageserver-types';\r\nimport type { CstNode, CompositeCstNode, LeafCstNode } from '../syntax-tree.js';\r\nimport type { DocumentSegment } from '../workspace/documents.js';\r\nimport type { Stream, TreeStream } from './stream.js';\r\nimport { isCompositeCstNode, isLeafCstNode, isRootCstNode } from '../syntax-tree.js';\r\nimport { TreeStreamImpl } from './stream.js';\r\n\r\n/**\r\n * Create a stream of all CST nodes that are directly and indirectly contained in the given root node,\r\n * including the root node itself.\r\n */\r\nexport function streamCst(node: CstNode): TreeStream<CstNode> {\r\n    return new TreeStreamImpl(node, element => {\r\n        if (isCompositeCstNode(element)) {\r\n            return element.content;\r\n        } else {\r\n            return [];\r\n        }\r\n    }, { includeRoot: true });\r\n}\r\n\r\n/**\r\n * Create a stream of all leaf nodes that are directly and indirectly contained in the given root node.\r\n */\r\nexport function flattenCst(node: CstNode): Stream<LeafCstNode> {\r\n    return streamCst(node).filter(isLeafCstNode);\r\n}\r\n\r\n/**\r\n * Determines whether the specified cst node is a child of the specified parent node.\r\n */\r\nexport function isChildNode(child: CstNode, parent: CstNode): boolean {\r\n    while (child.container) {\r\n        child = child.container;\r\n        if (child === parent) {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\nexport function tokenToRange(token: IToken): Range {\r\n    // Chevrotain uses 1-based indices everywhere\r\n    // So we subtract 1 from every value to align with the LSP\r\n    return {\r\n        start: {\r\n            character: token.startColumn! - 1,\r\n            line: token.startLine! - 1\r\n        },\r\n        end: {\r\n            character: token.endColumn!, // endColumn uses the correct index\r\n            line: token.endLine! - 1\r\n        }\r\n    };\r\n}\r\n\r\nexport function toDocumentSegment(node: CstNode): DocumentSegment;\r\nexport function toDocumentSegment(node?: CstNode): DocumentSegment | undefined;\r\nexport function toDocumentSegment(node?: CstNode): DocumentSegment | undefined {\r\n    if (!node) {\r\n        return undefined;\r\n    }\r\n    const { offset, end, range } = node;\r\n    return {\r\n        range,\r\n        offset,\r\n        end,\r\n        length: end - offset\r\n    };\r\n}\r\n\r\nexport enum RangeComparison {\r\n    Before = 0,\r\n    After = 1,\r\n    OverlapFront = 2,\r\n    OverlapBack = 3,\r\n    Inside = 4,\r\n    Outside = 5,\r\n}\r\n\r\nexport function compareRange(range: Range, to: Range): RangeComparison {\r\n    if (range.end.line < to.start.line || (range.end.line === to.start.line && range.end.character <= to.start.character)) {\r\n        return RangeComparison.Before;\r\n    } else if (range.start.line > to.end.line || (range.start.line === to.end.line && range.start.character >= to.end.character)) {\r\n        return RangeComparison.After;\r\n    }\r\n    const startInside = range.start.line > to.start.line || (range.start.line === to.start.line && range.start.character >= to.start.character);\r\n    const endInside = range.end.line < to.end.line || (range.end.line === to.end.line && range.end.character <= to.end.character);\r\n    if (startInside && endInside) {\r\n        return RangeComparison.Inside;\r\n    } else if (startInside) {\r\n        return RangeComparison.OverlapBack;\r\n    } else if (endInside) {\r\n        return RangeComparison.OverlapFront;\r\n    } else {\r\n        return RangeComparison.Outside;\r\n    }\r\n}\r\n\r\nexport function inRange(range: Range, to: Range): boolean {\r\n    const comparison = compareRange(range, to);\r\n    return comparison > RangeComparison.After;\r\n}\r\n\r\n// The \\p{L} regex matches any unicode letter character, i.e. characters from non-english alphabets\r\n// Together with \\w it matches any kind of character which can commonly appear in IDs\r\nexport const DefaultNameRegexp = /^[\\w\\p{L}]$/u;\r\n\r\n/**\r\n * Performs `findLeafNodeAtOffset` with a minor difference: When encountering a character that matches the `nameRegexp` argument,\r\n * it will instead return the leaf node at the `offset - 1` position.\r\n *\r\n * For LSP services, users expect that the declaration of an element is available if the cursor is directly after the element.\r\n */\r\nexport function findDeclarationNodeAtOffset(cstNode: CstNode | undefined, offset: number, nameRegexp = DefaultNameRegexp): LeafCstNode | undefined {\r\n    if (cstNode) {\r\n        if (offset > 0) {\r\n            const localOffset = offset - cstNode.offset;\r\n            const textAtOffset = cstNode.text.charAt(localOffset);\r\n            if (!nameRegexp.test(textAtOffset)) {\r\n                offset--;\r\n            }\r\n        }\r\n        return findLeafNodeAtOffset(cstNode, offset);\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function findCommentNode(cstNode: CstNode | undefined, commentNames: string[]): CstNode | undefined {\r\n    if (cstNode) {\r\n        const previous = getPreviousNode(cstNode, true);\r\n        if (previous && isCommentNode(previous, commentNames)) {\r\n            return previous;\r\n        }\r\n        if (isRootCstNode(cstNode)) {\r\n            // Go from the first non-hidden node through all nodes in reverse order\r\n            // We do this to find the comment node which directly precedes the root node\r\n            const endIndex = cstNode.content.findIndex(e => !e.hidden);\r\n            for (let i = endIndex - 1; i >= 0; i--) {\r\n                const child = cstNode.content[i];\r\n                if (isCommentNode(child, commentNames)) {\r\n                    return child;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function isCommentNode(cstNode: CstNode, commentNames: string[]): boolean {\r\n    return isLeafCstNode(cstNode) && commentNames.includes(cstNode.tokenType.name);\r\n}\r\n\r\n/**\r\n * Finds the leaf CST node at the specified 0-based string offset.\r\n * Note that the given offset will be within the range of the returned leaf node.\r\n *\r\n * If the offset does not point to a CST node (but just white space), this method will return `undefined`.\r\n *\r\n * @param node The CST node to search through.\r\n * @param offset The specified offset.\r\n * @returns The CST node at the specified offset.\r\n */\r\nexport function findLeafNodeAtOffset(node: CstNode, offset: number): LeafCstNode | undefined {\r\n    if (isLeafCstNode(node)) {\r\n        return node;\r\n    } else if (isCompositeCstNode(node)) {\r\n        const searchResult = binarySearch(node, offset, false);\r\n        if (searchResult) {\r\n            return findLeafNodeAtOffset(searchResult, offset);\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\n/**\r\n * Finds the leaf CST node at the specified 0-based string offset.\r\n * If no CST node exists at the specified position, it will return the leaf node before it.\r\n *\r\n * If there is no leaf node before the specified offset, this method will return `undefined`.\r\n *\r\n * @param node The CST node to search through.\r\n * @param offset The specified offset.\r\n * @returns The CST node closest to the specified offset.\r\n */\r\nexport function findLeafNodeBeforeOffset(node: CstNode, offset: number): LeafCstNode | undefined {\r\n    if (isLeafCstNode(node)) {\r\n        return node;\r\n    } else if (isCompositeCstNode(node)) {\r\n        const searchResult = binarySearch(node, offset, true);\r\n        if (searchResult) {\r\n            return findLeafNodeBeforeOffset(searchResult, offset);\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction binarySearch(node: CompositeCstNode, offset: number, closest: boolean): CstNode | undefined {\r\n    let left = 0;\r\n    let right = node.content.length - 1;\r\n    let closestNode: CstNode | undefined = undefined;\r\n\r\n    while (left <= right) {\r\n        const middle = Math.floor((left + right) / 2);\r\n        const middleNode = node.content[middle];\r\n\r\n        if (middleNode.offset <= offset && middleNode.end > offset) {\r\n            // Found an exact match\r\n            return middleNode;\r\n        }\r\n\r\n        if (middleNode.end <= offset) {\r\n            // Update the closest node (less than offset) and move to the right half\r\n            closestNode = closest ? middleNode : undefined;\r\n            left = middle + 1;\r\n        } else {\r\n            // Move to the left half\r\n            right = middle - 1;\r\n        }\r\n    }\r\n\r\n    return closestNode;\r\n}\r\n\r\nexport function getPreviousNode(node: CstNode, hidden = true): CstNode | undefined {\r\n    while (node.container) {\r\n        const parent = node.container;\r\n        let index = parent.content.indexOf(node);\r\n        while (index > 0) {\r\n            index--;\r\n            const previous = parent.content[index];\r\n            if (hidden || !previous.hidden) {\r\n                return previous;\r\n            }\r\n        }\r\n        node = parent;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function getNextNode(node: CstNode, hidden = true): CstNode | undefined {\r\n    while (node.container) {\r\n        const parent = node.container;\r\n        let index = parent.content.indexOf(node);\r\n        const last = parent.content.length - 1;\r\n        while (index < last) {\r\n            index++;\r\n            const next = parent.content[index];\r\n            if (hidden || !next.hidden) {\r\n                return next;\r\n            }\r\n        }\r\n        node = parent;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport function getStartlineNode(node: CstNode): CstNode {\r\n    if (node.range.start.character === 0) {\r\n        return node;\r\n    }\r\n    const line = node.range.start.line;\r\n    let last = node;\r\n    let index: number | undefined;\r\n    while (node.container) {\r\n        const parent = node.container;\r\n        const selfIndex = index ?? parent.content.indexOf(node);\r\n        if (selfIndex === 0) {\r\n            node = parent;\r\n            index = undefined;\r\n        } else {\r\n            index = selfIndex - 1;\r\n            node = parent.content[index];\r\n        }\r\n        if (node.range.start.line !== line) {\r\n            break;\r\n        }\r\n        last = node;\r\n    }\r\n    return last;\r\n}\r\n\r\nexport function getInteriorNodes(start: CstNode, end: CstNode): CstNode[] {\r\n    const commonParent = getCommonParent(start, end);\r\n    if (!commonParent) {\r\n        return [];\r\n    }\r\n    return commonParent.parent.content.slice(commonParent.a + 1, commonParent.b);\r\n}\r\n\r\nfunction getCommonParent(a: CstNode, b: CstNode): CommonParent | undefined {\r\n    const aParents = getParentChain(a);\r\n    const bParents = getParentChain(b);\r\n    let current: CommonParent | undefined;\r\n    for (let i = 0; i < aParents.length && i < bParents.length; i++) {\r\n        const aParent = aParents[i];\r\n        const bParent = bParents[i];\r\n        if (aParent.parent === bParent.parent) {\r\n            current = {\r\n                parent: aParent.parent,\r\n                a: aParent.index,\r\n                b: bParent.index\r\n            };\r\n        } else {\r\n            break;\r\n        }\r\n    }\r\n    return current;\r\n}\r\n\r\ninterface CommonParent {\r\n    parent: CompositeCstNode\r\n    a: number\r\n    b: number\r\n}\r\n\r\nfunction getParentChain(node: CstNode): ParentLink[] {\r\n    const chain: ParentLink[] = [];\r\n    while (node.container) {\r\n        const parent = node.container;\r\n        const index = parent.content.indexOf(node);\r\n        chain.push({\r\n            parent,\r\n            index\r\n        });\r\n        node = parent;\r\n    }\r\n    return chain.reverse();\r\n}\r\n\r\ninterface ParentLink {\r\n    parent: CompositeCstNode\r\n    index: number\r\n}\r\n"], "mappings": "AAAA;;;;;AAWA,SAASA,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AACpF,SAASC,cAAc,QAAQ,aAAa;AAE5C;;;;AAIA,OAAM,SAAUC,SAASA,CAACC,IAAa;EACnC,OAAO,IAAIF,cAAc,CAACE,IAAI,EAAEC,OAAO,IAAG;IACtC,IAAIN,kBAAkB,CAACM,OAAO,CAAC,EAAE;MAC7B,OAAOA,OAAO,CAACC,OAAO;IAC1B,CAAC,MAAM;MACH,OAAO,EAAE;IACb;EACJ,CAAC,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAE,CAAC;AAC7B;AAEA;;;AAGA,OAAM,SAAUC,UAAUA,CAACJ,IAAa;EACpC,OAAOD,SAAS,CAACC,IAAI,CAAC,CAACK,MAAM,CAACT,aAAa,CAAC;AAChD;AAEA;;;AAGA,OAAM,SAAUU,WAAWA,CAACC,KAAc,EAAEC,MAAe;EACvD,OAAOD,KAAK,CAACE,SAAS,EAAE;IACpBF,KAAK,GAAGA,KAAK,CAACE,SAAS;IACvB,IAAIF,KAAK,KAAKC,MAAM,EAAE;MAClB,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AAEA,OAAM,SAAUE,YAAYA,CAACC,KAAa;EACtC;EACA;EACA,OAAO;IACHC,KAAK,EAAE;MACHC,SAAS,EAAEF,KAAK,CAACG,WAAY,GAAG,CAAC;MACjCC,IAAI,EAAEJ,KAAK,CAACK,SAAU,GAAG;KAC5B;IACDC,GAAG,EAAE;MACDJ,SAAS,EAAEF,KAAK,CAACO,SAAU;MAAE;MAC7BH,IAAI,EAAEJ,KAAK,CAACQ,OAAQ,GAAG;;GAE9B;AACL;AAIA,OAAM,SAAUC,iBAAiBA,CAACpB,IAAc;EAC5C,IAAI,CAACA,IAAI,EAAE;IACP,OAAOqB,SAAS;EACpB;EACA,MAAM;IAAEC,MAAM;IAAEL,GAAG;IAAEM;EAAK,CAAE,GAAGvB,IAAI;EACnC,OAAO;IACHuB,KAAK;IACLD,MAAM;IACNL,GAAG;IACHO,MAAM,EAAEP,GAAG,GAAGK;GACjB;AACL;AAEA,WAAYG,eAOX;AAPD,WAAYA,eAAe;EACvBA,eAAA,CAAAA,eAAA,0BAAU;EACVA,eAAA,CAAAA,eAAA,wBAAS;EACTA,eAAA,CAAAA,eAAA,sCAAgB;EAChBA,eAAA,CAAAA,eAAA,oCAAe;EACfA,eAAA,CAAAA,eAAA,0BAAU;EACVA,eAAA,CAAAA,eAAA,4BAAW;AACf,CAAC,EAPWA,eAAe,KAAfA,eAAe;AAS3B,OAAM,SAAUC,YAAYA,CAACH,KAAY,EAAEI,EAAS;EAChD,IAAIJ,KAAK,CAACN,GAAG,CAACF,IAAI,GAAGY,EAAE,CAACf,KAAK,CAACG,IAAI,IAAKQ,KAAK,CAACN,GAAG,CAACF,IAAI,KAAKY,EAAE,CAACf,KAAK,CAACG,IAAI,IAAIQ,KAAK,CAACN,GAAG,CAACJ,SAAS,IAAIc,EAAE,CAACf,KAAK,CAACC,SAAU,EAAE;IACnH,OAAOY,eAAe,CAACG,MAAM;EACjC,CAAC,MAAM,IAAIL,KAAK,CAACX,KAAK,CAACG,IAAI,GAAGY,EAAE,CAACV,GAAG,CAACF,IAAI,IAAKQ,KAAK,CAACX,KAAK,CAACG,IAAI,KAAKY,EAAE,CAACV,GAAG,CAACF,IAAI,IAAIQ,KAAK,CAACX,KAAK,CAACC,SAAS,IAAIc,EAAE,CAACV,GAAG,CAACJ,SAAU,EAAE;IAC1H,OAAOY,eAAe,CAACI,KAAK;EAChC;EACA,MAAMC,WAAW,GAAGP,KAAK,CAACX,KAAK,CAACG,IAAI,GAAGY,EAAE,CAACf,KAAK,CAACG,IAAI,IAAKQ,KAAK,CAACX,KAAK,CAACG,IAAI,KAAKY,EAAE,CAACf,KAAK,CAACG,IAAI,IAAIQ,KAAK,CAACX,KAAK,CAACC,SAAS,IAAIc,EAAE,CAACf,KAAK,CAACC,SAAU;EAC3I,MAAMkB,SAAS,GAAGR,KAAK,CAACN,GAAG,CAACF,IAAI,GAAGY,EAAE,CAACV,GAAG,CAACF,IAAI,IAAKQ,KAAK,CAACN,GAAG,CAACF,IAAI,KAAKY,EAAE,CAACV,GAAG,CAACF,IAAI,IAAIQ,KAAK,CAACN,GAAG,CAACJ,SAAS,IAAIc,EAAE,CAACV,GAAG,CAACJ,SAAU;EAC7H,IAAIiB,WAAW,IAAIC,SAAS,EAAE;IAC1B,OAAON,eAAe,CAACO,MAAM;EACjC,CAAC,MAAM,IAAIF,WAAW,EAAE;IACpB,OAAOL,eAAe,CAACQ,WAAW;EACtC,CAAC,MAAM,IAAIF,SAAS,EAAE;IAClB,OAAON,eAAe,CAACS,YAAY;EACvC,CAAC,MAAM;IACH,OAAOT,eAAe,CAACU,OAAO;EAClC;AACJ;AAEA,OAAM,SAAUC,OAAOA,CAACb,KAAY,EAAEI,EAAS;EAC3C,MAAMU,UAAU,GAAGX,YAAY,CAACH,KAAK,EAAEI,EAAE,CAAC;EAC1C,OAAOU,UAAU,GAAGZ,eAAe,CAACI,KAAK;AAC7C;AAEA;AACA;AACA,OAAO,MAAMS,iBAAiB,GAAG,cAAc;AAE/C;;;;;;AAMA,OAAM,SAAUC,2BAA2BA,CAACC,OAA4B,EAAElB,MAAc,EAAEmB,UAAU,GAAGH,iBAAiB;EACpH,IAAIE,OAAO,EAAE;IACT,IAAIlB,MAAM,GAAG,CAAC,EAAE;MACZ,MAAMoB,WAAW,GAAGpB,MAAM,GAAGkB,OAAO,CAAClB,MAAM;MAC3C,MAAMqB,YAAY,GAAGH,OAAO,CAACI,IAAI,CAACC,MAAM,CAACH,WAAW,CAAC;MACrD,IAAI,CAACD,UAAU,CAACK,IAAI,CAACH,YAAY,CAAC,EAAE;QAChCrB,MAAM,EAAE;MACZ;IACJ;IACA,OAAOyB,oBAAoB,CAACP,OAAO,EAAElB,MAAM,CAAC;EAChD;EACA,OAAOD,SAAS;AACpB;AAEA,OAAM,SAAU2B,eAAeA,CAACR,OAA4B,EAAES,YAAsB;EAChF,IAAIT,OAAO,EAAE;IACT,MAAMU,QAAQ,GAAGC,eAAe,CAACX,OAAO,EAAE,IAAI,CAAC;IAC/C,IAAIU,QAAQ,IAAIE,aAAa,CAACF,QAAQ,EAAED,YAAY,CAAC,EAAE;MACnD,OAAOC,QAAQ;IACnB;IACA,IAAIrD,aAAa,CAAC2C,OAAO,CAAC,EAAE;MACxB;MACA;MACA,MAAMa,QAAQ,GAAGb,OAAO,CAACtC,OAAO,CAACoD,SAAS,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,MAAM,CAAC;MAC1D,KAAK,IAAIC,CAAC,GAAGJ,QAAQ,GAAG,CAAC,EAAEI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpC,MAAMlD,KAAK,GAAGiC,OAAO,CAACtC,OAAO,CAACuD,CAAC,CAAC;QAChC,IAAIL,aAAa,CAAC7C,KAAK,EAAE0C,YAAY,CAAC,EAAE;UACpC,OAAO1C,KAAK;QAChB;MACJ;IACJ;EACJ;EACA,OAAOc,SAAS;AACpB;AAEA,OAAM,SAAU+B,aAAaA,CAACZ,OAAgB,EAAES,YAAsB;EAClE,OAAOrD,aAAa,CAAC4C,OAAO,CAAC,IAAIS,YAAY,CAACS,QAAQ,CAAClB,OAAO,CAACmB,SAAS,CAACC,IAAI,CAAC;AAClF;AAEA;;;;;;;;;;AAUA,OAAM,SAAUb,oBAAoBA,CAAC/C,IAAa,EAAEsB,MAAc;EAC9D,IAAI1B,aAAa,CAACI,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI;EACf,CAAC,MAAM,IAAIL,kBAAkB,CAACK,IAAI,CAAC,EAAE;IACjC,MAAM6D,YAAY,GAAGC,YAAY,CAAC9D,IAAI,EAAEsB,MAAM,EAAE,KAAK,CAAC;IACtD,IAAIuC,YAAY,EAAE;MACd,OAAOd,oBAAoB,CAACc,YAAY,EAAEvC,MAAM,CAAC;IACrD;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA;;;;;;;;;;AAUA,OAAM,SAAU0C,wBAAwBA,CAAC/D,IAAa,EAAEsB,MAAc;EAClE,IAAI1B,aAAa,CAACI,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI;EACf,CAAC,MAAM,IAAIL,kBAAkB,CAACK,IAAI,CAAC,EAAE;IACjC,MAAM6D,YAAY,GAAGC,YAAY,CAAC9D,IAAI,EAAEsB,MAAM,EAAE,IAAI,CAAC;IACrD,IAAIuC,YAAY,EAAE;MACd,OAAOE,wBAAwB,CAACF,YAAY,EAAEvC,MAAM,CAAC;IACzD;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASyC,YAAYA,CAAC9D,IAAsB,EAAEsB,MAAc,EAAE0C,OAAgB;EAC1E,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAGlE,IAAI,CAACE,OAAO,CAACsB,MAAM,GAAG,CAAC;EACnC,IAAI2C,WAAW,GAAwB9C,SAAS;EAEhD,OAAO4C,IAAI,IAAIC,KAAK,EAAE;IAClB,MAAME,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;IAC7C,MAAMK,UAAU,GAAGvE,IAAI,CAACE,OAAO,CAACkE,MAAM,CAAC;IAEvC,IAAIG,UAAU,CAACjD,MAAM,IAAIA,MAAM,IAAIiD,UAAU,CAACtD,GAAG,GAAGK,MAAM,EAAE;MACxD;MACA,OAAOiD,UAAU;IACrB;IAEA,IAAIA,UAAU,CAACtD,GAAG,IAAIK,MAAM,EAAE;MAC1B;MACA6C,WAAW,GAAGH,OAAO,GAAGO,UAAU,GAAGlD,SAAS;MAC9C4C,IAAI,GAAGG,MAAM,GAAG,CAAC;IACrB,CAAC,MAAM;MACH;MACAF,KAAK,GAAGE,MAAM,GAAG,CAAC;IACtB;EACJ;EAEA,OAAOD,WAAW;AACtB;AAEA,OAAM,SAAUhB,eAAeA,CAACnD,IAAa,EAAEwD,MAAM,GAAG,IAAI;EACxD,OAAOxD,IAAI,CAACS,SAAS,EAAE;IACnB,MAAMD,MAAM,GAAGR,IAAI,CAACS,SAAS;IAC7B,IAAI+D,KAAK,GAAGhE,MAAM,CAACN,OAAO,CAACuE,OAAO,CAACzE,IAAI,CAAC;IACxC,OAAOwE,KAAK,GAAG,CAAC,EAAE;MACdA,KAAK,EAAE;MACP,MAAMtB,QAAQ,GAAG1C,MAAM,CAACN,OAAO,CAACsE,KAAK,CAAC;MACtC,IAAIhB,MAAM,IAAI,CAACN,QAAQ,CAACM,MAAM,EAAE;QAC5B,OAAON,QAAQ;MACnB;IACJ;IACAlD,IAAI,GAAGQ,MAAM;EACjB;EACA,OAAOa,SAAS;AACpB;AAEA,OAAM,SAAUqD,WAAWA,CAAC1E,IAAa,EAAEwD,MAAM,GAAG,IAAI;EACpD,OAAOxD,IAAI,CAACS,SAAS,EAAE;IACnB,MAAMD,MAAM,GAAGR,IAAI,CAACS,SAAS;IAC7B,IAAI+D,KAAK,GAAGhE,MAAM,CAACN,OAAO,CAACuE,OAAO,CAACzE,IAAI,CAAC;IACxC,MAAM2E,IAAI,GAAGnE,MAAM,CAACN,OAAO,CAACsB,MAAM,GAAG,CAAC;IACtC,OAAOgD,KAAK,GAAGG,IAAI,EAAE;MACjBH,KAAK,EAAE;MACP,MAAMI,IAAI,GAAGpE,MAAM,CAACN,OAAO,CAACsE,KAAK,CAAC;MAClC,IAAIhB,MAAM,IAAI,CAACoB,IAAI,CAACpB,MAAM,EAAE;QACxB,OAAOoB,IAAI;MACf;IACJ;IACA5E,IAAI,GAAGQ,MAAM;EACjB;EACA,OAAOa,SAAS;AACpB;AAEA,OAAM,SAAUwD,gBAAgBA,CAAC7E,IAAa;EAC1C,IAAIA,IAAI,CAACuB,KAAK,CAACX,KAAK,CAACC,SAAS,KAAK,CAAC,EAAE;IAClC,OAAOb,IAAI;EACf;EACA,MAAMe,IAAI,GAAGf,IAAI,CAACuB,KAAK,CAACX,KAAK,CAACG,IAAI;EAClC,IAAI4D,IAAI,GAAG3E,IAAI;EACf,IAAIwE,KAAyB;EAC7B,OAAOxE,IAAI,CAACS,SAAS,EAAE;IACnB,MAAMD,MAAM,GAAGR,IAAI,CAACS,SAAS;IAC7B,MAAMqE,SAAS,GAAGN,KAAK,aAALA,KAAK,cAALA,KAAK,GAAIhE,MAAM,CAACN,OAAO,CAACuE,OAAO,CAACzE,IAAI,CAAC;IACvD,IAAI8E,SAAS,KAAK,CAAC,EAAE;MACjB9E,IAAI,GAAGQ,MAAM;MACbgE,KAAK,GAAGnD,SAAS;IACrB,CAAC,MAAM;MACHmD,KAAK,GAAGM,SAAS,GAAG,CAAC;MACrB9E,IAAI,GAAGQ,MAAM,CAACN,OAAO,CAACsE,KAAK,CAAC;IAChC;IACA,IAAIxE,IAAI,CAACuB,KAAK,CAACX,KAAK,CAACG,IAAI,KAAKA,IAAI,EAAE;MAChC;IACJ;IACA4D,IAAI,GAAG3E,IAAI;EACf;EACA,OAAO2E,IAAI;AACf;AAEA,OAAM,SAAUI,gBAAgBA,CAACnE,KAAc,EAAEK,GAAY;EACzD,MAAM+D,YAAY,GAAGC,eAAe,CAACrE,KAAK,EAAEK,GAAG,CAAC;EAChD,IAAI,CAAC+D,YAAY,EAAE;IACf,OAAO,EAAE;EACb;EACA,OAAOA,YAAY,CAACxE,MAAM,CAACN,OAAO,CAACgF,KAAK,CAACF,YAAY,CAACG,CAAC,GAAG,CAAC,EAAEH,YAAY,CAACI,CAAC,CAAC;AAChF;AAEA,SAASH,eAAeA,CAACE,CAAU,EAAEC,CAAU;EAC3C,MAAMC,QAAQ,GAAGC,cAAc,CAACH,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAGD,cAAc,CAACF,CAAC,CAAC;EAClC,IAAII,OAAiC;EACrC,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,QAAQ,CAAC7D,MAAM,IAAIiC,CAAC,GAAG8B,QAAQ,CAAC/D,MAAM,EAAEiC,CAAC,EAAE,EAAE;IAC7D,MAAMgC,OAAO,GAAGJ,QAAQ,CAAC5B,CAAC,CAAC;IAC3B,MAAMiC,OAAO,GAAGH,QAAQ,CAAC9B,CAAC,CAAC;IAC3B,IAAIgC,OAAO,CAACjF,MAAM,KAAKkF,OAAO,CAAClF,MAAM,EAAE;MACnCgF,OAAO,GAAG;QACNhF,MAAM,EAAEiF,OAAO,CAACjF,MAAM;QACtB2E,CAAC,EAAEM,OAAO,CAACjB,KAAK;QAChBY,CAAC,EAAEM,OAAO,CAAClB;OACd;IACL,CAAC,MAAM;MACH;IACJ;EACJ;EACA,OAAOgB,OAAO;AAClB;AAQA,SAASF,cAAcA,CAACtF,IAAa;EACjC,MAAM2F,KAAK,GAAiB,EAAE;EAC9B,OAAO3F,IAAI,CAACS,SAAS,EAAE;IACnB,MAAMD,MAAM,GAAGR,IAAI,CAACS,SAAS;IAC7B,MAAM+D,KAAK,GAAGhE,MAAM,CAACN,OAAO,CAACuE,OAAO,CAACzE,IAAI,CAAC;IAC1C2F,KAAK,CAACC,IAAI,CAAC;MACPpF,MAAM;MACNgE;KACH,CAAC;IACFxE,IAAI,GAAGQ,MAAM;EACjB;EACA,OAAOmF,KAAK,CAACE,OAAO,EAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}