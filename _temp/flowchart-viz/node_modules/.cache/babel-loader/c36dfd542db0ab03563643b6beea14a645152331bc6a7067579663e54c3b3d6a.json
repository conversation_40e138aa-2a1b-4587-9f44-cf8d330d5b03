{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nimport binomial from \"./binomial.js\";\nimport gamma from \"./gamma.js\";\nexport default (function sourceRandomPoisson(source) {\n  var G = gamma.source(source),\n    B = binomial.source(source);\n  function randomPoisson(lambda) {\n    return function () {\n      var acc = 0,\n        l = lambda;\n      while (l > 16) {\n        var n = Math.floor(0.875 * l),\n          t = G(n)();\n        if (t > l) return acc + B(n - 1, l / t)();\n        acc += n;\n        l -= t;\n      }\n      for (var s = -Math.log1p(-source()), k = 0; s <= l; ++k) s -= Math.log1p(-source());\n      return acc + k;\n    };\n  }\n  randomPoisson.source = sourceRandomPoisson;\n  return randomPoisson;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "binomial", "gamma", "sourceRandom<PERSON><PERSON>son", "source", "G", "B", "<PERSON><PERSON><PERSON><PERSON>", "lambda", "acc", "l", "n", "Math", "floor", "t", "s", "log1p", "k"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/poisson.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\nimport binomial from \"./binomial.js\";\nimport gamma from \"./gamma.js\";\n\nexport default (function sourceRandomPoisson(source) {\n  var G = gamma.source(source),\n      B = binomial.source(source);\n\n  function randomPoisson(lambda) {\n    return function() {\n      var acc = 0, l = lambda;\n      while (l > 16) {\n        var n = Math.floor(0.875 * l),\n            t = G(n)();\n        if (t > l) return acc + B(n - 1, l / t)();\n        acc += n;\n        l -= t;\n      }\n      for (var s = -Math.log1p(-source()), k = 0; s <= l; ++k) s -= Math.log1p(-source());\n      return acc + k;\n    };\n  }\n\n  randomPoisson.source = sourceRandomPoisson;\n\n  return randomPoisson;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,YAAY;AAE9B,eAAe,CAAC,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnD,IAAIC,CAAC,GAAGH,KAAK,CAACE,MAAM,CAACA,MAAM,CAAC;IACxBE,CAAC,GAAGL,QAAQ,CAACG,MAAM,CAACA,MAAM,CAAC;EAE/B,SAASG,aAAaA,CAACC,MAAM,EAAE;IAC7B,OAAO,YAAW;MAChB,IAAIC,GAAG,GAAG,CAAC;QAAEC,CAAC,GAAGF,MAAM;MACvB,OAAOE,CAAC,GAAG,EAAE,EAAE;QACb,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,KAAK,GAAGH,CAAC,CAAC;UACzBI,CAAC,GAAGT,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC;QACd,IAAIG,CAAC,GAAGJ,CAAC,EAAE,OAAOD,GAAG,GAAGH,CAAC,CAACK,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;QACzCL,GAAG,IAAIE,CAAC;QACRD,CAAC,IAAII,CAAC;MACR;MACA,KAAK,IAAIC,CAAC,GAAG,CAACH,IAAI,CAACI,KAAK,CAAC,CAACZ,MAAM,CAAC,CAAC,CAAC,EAAEa,CAAC,GAAG,CAAC,EAAEF,CAAC,IAAIL,CAAC,EAAE,EAAEO,CAAC,EAAEF,CAAC,IAAIH,IAAI,CAACI,KAAK,CAAC,CAACZ,MAAM,CAAC,CAAC,CAAC;MACnF,OAAOK,GAAG,GAAGQ,CAAC;IAChB,CAAC;EACH;EAEAV,aAAa,CAACH,MAAM,GAAGD,mBAAmB;EAE1C,OAAOI,aAAa;AACtB,CAAC,EAAEP,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}