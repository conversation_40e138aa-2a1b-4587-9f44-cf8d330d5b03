{"ast": null, "code": "import { includes } from \"lodash-es\";\nconst MISMATCHED_TOKEN_EXCEPTION = \"MismatchedTokenException\";\nconst NO_VIABLE_ALT_EXCEPTION = \"NoViableAltException\";\nconst EARLY_EXIT_EXCEPTION = \"EarlyExitException\";\nconst NOT_ALL_INPUT_PARSED_EXCEPTION = \"NotAllInputParsedException\";\nconst RECOGNITION_EXCEPTION_NAMES = [MISMATCHED_TOKEN_EXCEPTION, NO_VIABLE_ALT_EXCEPTION, EARLY_EXIT_EXCEPTION, NOT_ALL_INPUT_PARSED_EXCEPTION];\nObject.freeze(RECOGNITION_EXCEPTION_NAMES);\n// hacks to bypass no support for custom Errors in javascript/typescript\nexport function isRecognitionException(error) {\n  // can't do instanceof on hacked custom js exceptions\n  return includes(RECOGNITION_EXCEPTION_NAMES, error.name);\n}\nclass RecognitionException extends Error {\n  constructor(message, token) {\n    super(message);\n    this.token = token;\n    this.resyncedTokens = [];\n    // fix prototype chain when typescript target is ES5\n    Object.setPrototypeOf(this, new.target.prototype);\n    /* istanbul ignore next - V8 workaround to remove constructor from stacktrace when typescript target is ES5 */\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n}\nexport class MismatchedTokenException extends RecognitionException {\n  constructor(message, token, previousToken) {\n    super(message, token);\n    this.previousToken = previousToken;\n    this.name = MISMATCHED_TOKEN_EXCEPTION;\n  }\n}\nexport class NoViableAltException extends RecognitionException {\n  constructor(message, token, previousToken) {\n    super(message, token);\n    this.previousToken = previousToken;\n    this.name = NO_VIABLE_ALT_EXCEPTION;\n  }\n}\nexport class NotAllInputParsedException extends RecognitionException {\n  constructor(message, token) {\n    super(message, token);\n    this.name = NOT_ALL_INPUT_PARSED_EXCEPTION;\n  }\n}\nexport class EarlyExitException extends RecognitionException {\n  constructor(message, token, previousToken) {\n    super(message, token);\n    this.previousToken = previousToken;\n    this.name = EARLY_EXIT_EXCEPTION;\n  }\n}", "map": {"version": 3, "names": ["includes", "MISMATCHED_TOKEN_EXCEPTION", "NO_VIABLE_ALT_EXCEPTION", "EARLY_EXIT_EXCEPTION", "NOT_ALL_INPUT_PARSED_EXCEPTION", "RECOGNITION_EXCEPTION_NAMES", "Object", "freeze", "isRecognitionException", "error", "name", "RecognitionException", "Error", "constructor", "message", "token", "resyncedTokens", "setPrototypeOf", "new", "target", "prototype", "captureStackTrace", "MismatchedTokenException", "previousToken", "NoViableAltException", "NotAllInputParsedException", "EarlyExitException"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/exceptions_public.ts"], "sourcesContent": ["import { includes } from \"lodash-es\";\nimport {\n  IR<PERSON>ognitionException,\n  IRecognizerContext,\n  IToken,\n} from \"@chevrotain/types\";\n\nconst MISMATCHED_TOKEN_EXCEPTION = \"MismatchedTokenException\";\nconst NO_VIABLE_ALT_EXCEPTION = \"NoViableAltException\";\nconst EARLY_EXIT_EXCEPTION = \"EarlyExitException\";\nconst NOT_ALL_INPUT_PARSED_EXCEPTION = \"NotAllInputParsedException\";\n\nconst RECOGNITION_EXCEPTION_NAMES = [\n  MISMATCHED_TOKEN_EXCEPTION,\n  NO_VIABLE_ALT_EXCEPTION,\n  EARLY_EXIT_EXCEPTION,\n  NOT_ALL_INPUT_PARSED_EXCEPTION,\n];\n\nObject.freeze(RECOGNITION_EXCEPTION_NAMES);\n\n// hacks to bypass no support for custom Errors in javascript/typescript\nexport function isRecognitionException(error: Error) {\n  // can't do instanceof on hacked custom js exceptions\n  return includes(RECOGNITION_EXCEPTION_NAMES, error.name);\n}\n\nabstract class RecognitionException\n  extends Error\n  implements IRecognitionException\n{\n  context: IRecognizerContext;\n  resyncedTokens: IToken[] = [];\n\n  protected constructor(\n    message: string,\n    public token: IToken,\n  ) {\n    super(message);\n\n    // fix prototype chain when typescript target is ES5\n    Object.setPrototypeOf(this, new.target.prototype);\n\n    /* istanbul ignore next - V8 workaround to remove constructor from stacktrace when typescript target is ES5 */\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n}\n\nexport class MismatchedTokenException extends RecognitionException {\n  constructor(\n    message: string,\n    token: IToken,\n    public previousToken: IToken,\n  ) {\n    super(message, token);\n    this.name = MISMATCHED_TOKEN_EXCEPTION;\n  }\n}\n\nexport class NoViableAltException extends RecognitionException {\n  constructor(\n    message: string,\n    token: IToken,\n    public previousToken: IToken,\n  ) {\n    super(message, token);\n    this.name = NO_VIABLE_ALT_EXCEPTION;\n  }\n}\n\nexport class NotAllInputParsedException extends RecognitionException {\n  constructor(message: string, token: IToken) {\n    super(message, token);\n    this.name = NOT_ALL_INPUT_PARSED_EXCEPTION;\n  }\n}\n\nexport class EarlyExitException extends RecognitionException {\n  constructor(\n    message: string,\n    token: IToken,\n    public previousToken: IToken,\n  ) {\n    super(message, token);\n    this.name = EARLY_EXIT_EXCEPTION;\n  }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AAOpC,MAAMC,0BAA0B,GAAG,0BAA0B;AAC7D,MAAMC,uBAAuB,GAAG,sBAAsB;AACtD,MAAMC,oBAAoB,GAAG,oBAAoB;AACjD,MAAMC,8BAA8B,GAAG,4BAA4B;AAEnE,MAAMC,2BAA2B,GAAG,CAClCJ,0BAA0B,EAC1BC,uBAAuB,EACvBC,oBAAoB,EACpBC,8BAA8B,CAC/B;AAEDE,MAAM,CAACC,MAAM,CAACF,2BAA2B,CAAC;AAE1C;AACA,OAAM,SAAUG,sBAAsBA,CAACC,KAAY;EACjD;EACA,OAAOT,QAAQ,CAACK,2BAA2B,EAAEI,KAAK,CAACC,IAAI,CAAC;AAC1D;AAEA,MAAeC,oBACb,SAAQC,KAAK;EAMbC,YACEC,OAAe,EACRC,KAAa;IAEpB,KAAK,CAACD,OAAO,CAAC;IAFP,KAAAC,KAAK,GAALA,KAAK;IAJd,KAAAC,cAAc,GAAa,EAAE;IAQ3B;IACAV,MAAM,CAACW,cAAc,CAAC,IAAI,EAAEC,GAAG,CAACC,MAAM,CAACC,SAAS,CAAC;IAEjD;IACA,IAAIR,KAAK,CAACS,iBAAiB,EAAE;MAC3BT,KAAK,CAACS,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACR,WAAW,CAAC;;EAEnD;;AAGF,OAAM,MAAOS,wBAAyB,SAAQX,oBAAoB;EAChEE,YACEC,OAAe,EACfC,KAAa,EACNQ,aAAqB;IAE5B,KAAK,CAACT,OAAO,EAAEC,KAAK,CAAC;IAFd,KAAAQ,aAAa,GAAbA,aAAa;IAGpB,IAAI,CAACb,IAAI,GAAGT,0BAA0B;EACxC;;AAGF,OAAM,MAAOuB,oBAAqB,SAAQb,oBAAoB;EAC5DE,YACEC,OAAe,EACfC,KAAa,EACNQ,aAAqB;IAE5B,KAAK,CAACT,OAAO,EAAEC,KAAK,CAAC;IAFd,KAAAQ,aAAa,GAAbA,aAAa;IAGpB,IAAI,CAACb,IAAI,GAAGR,uBAAuB;EACrC;;AAGF,OAAM,MAAOuB,0BAA2B,SAAQd,oBAAoB;EAClEE,YAAYC,OAAe,EAAEC,KAAa;IACxC,KAAK,CAACD,OAAO,EAAEC,KAAK,CAAC;IACrB,IAAI,CAACL,IAAI,GAAGN,8BAA8B;EAC5C;;AAGF,OAAM,MAAOsB,kBAAmB,SAAQf,oBAAoB;EAC1DE,YACEC,OAAe,EACfC,KAAa,EACNQ,aAAqB;IAE5B,KAAK,CAACT,OAAO,EAAEC,KAAK,CAAC;IAFd,KAAAQ,aAAa,GAAbA,aAAa;IAGpB,IAAI,CAACb,IAAI,GAAGP,oBAAoB;EAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}