{"ast": null, "code": "/** Used as the internal argument placeholder. */\nvar PLACEHOLDER = '__lodash_placeholder__';\n\n/**\n * Replaces all `placeholder` elements in `array` with an internal placeholder\n * and returns an array of their indexes.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {*} placeholder The placeholder to replace.\n * @returns {Array} Returns the new array of placeholder indexes.\n */\nfunction replaceHolders(array, placeholder) {\n  var index = -1,\n    length = array.length,\n    resIndex = 0,\n    result = [];\n  while (++index < length) {\n    var value = array[index];\n    if (value === placeholder || value === PLACEHOLDER) {\n      array[index] = PLACEHOLDER;\n      result[resIndex++] = index;\n    }\n  }\n  return result;\n}\nexport default replaceHolders;", "map": {"version": 3, "names": ["PLACEHOLDER", "replaceHolders", "array", "placeholder", "index", "length", "resIndex", "result", "value"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_replaceHolders.js"], "sourcesContent": ["/** Used as the internal argument placeholder. */\nvar PLACEHOLDER = '__lodash_placeholder__';\n\n/**\n * Replaces all `placeholder` elements in `array` with an internal placeholder\n * and returns an array of their indexes.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {*} placeholder The placeholder to replace.\n * @returns {Array} Returns the new array of placeholder indexes.\n */\nfunction replaceHolders(array, placeholder) {\n  var index = -1,\n      length = array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (value === placeholder || value === PLACEHOLDER) {\n      array[index] = PLACEHOLDER;\n      result[resIndex++] = index;\n    }\n  }\n  return result;\n}\n\nexport default replaceHolders;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAG,wBAAwB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAC1C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,QAAQ,GAAG,CAAC;IACZC,MAAM,GAAG,EAAE;EAEf,OAAO,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGN,KAAK,CAACE,KAAK,CAAC;IACxB,IAAII,KAAK,KAAKL,WAAW,IAAIK,KAAK,KAAKR,WAAW,EAAE;MAClDE,KAAK,CAACE,KAAK,CAAC,GAAGJ,WAAW;MAC1BO,MAAM,CAACD,QAAQ,EAAE,CAAC,GAAGF,KAAK;IAC5B;EACF;EACA,OAAOG,MAAM;AACf;AAEA,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}