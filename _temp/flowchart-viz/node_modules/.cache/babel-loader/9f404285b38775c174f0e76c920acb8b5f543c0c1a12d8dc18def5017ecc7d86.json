{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { Position, Range } from 'vscode-languageserver-types';\nimport { NEWLINE_REGEXP, escapeRegExp } from '../utils/regexp-utils.js';\nimport { URI } from '../utils/uri-utils.js';\nexport function parseJSDoc(node, start, options) {\n  let opts;\n  let position;\n  if (typeof node === 'string') {\n    position = start;\n    opts = options;\n  } else {\n    position = node.range.start;\n    opts = start;\n  }\n  if (!position) {\n    position = Position.create(0, 0);\n  }\n  const lines = getLines(node);\n  const normalizedOptions = normalizeOptions(opts);\n  const tokens = tokenize({\n    lines,\n    position,\n    options: normalizedOptions\n  });\n  return parseJSDocComment({\n    index: 0,\n    tokens,\n    position\n  });\n}\nexport function isJSDoc(node, options) {\n  const normalizedOptions = normalizeOptions(options);\n  const lines = getLines(node);\n  if (lines.length === 0) {\n    return false;\n  }\n  const first = lines[0];\n  const last = lines[lines.length - 1];\n  const firstRegex = normalizedOptions.start;\n  const lastRegex = normalizedOptions.end;\n  return Boolean(firstRegex === null || firstRegex === void 0 ? void 0 : firstRegex.exec(first)) && Boolean(lastRegex === null || lastRegex === void 0 ? void 0 : lastRegex.exec(last));\n}\nfunction getLines(node) {\n  let content = '';\n  if (typeof node === 'string') {\n    content = node;\n  } else {\n    content = node.text;\n  }\n  const lines = content.split(NEWLINE_REGEXP);\n  return lines;\n}\nconst tagRegex = /\\s*(@([\\p{L}][\\p{L}\\p{N}]*)?)/uy;\nconst inlineTagRegex = /\\{(@[\\p{L}][\\p{L}\\p{N}]*)(\\s*)([^\\r\\n}]+)?\\}/gu;\nfunction tokenize(context) {\n  var _a, _b, _c;\n  const tokens = [];\n  let currentLine = context.position.line;\n  let currentCharacter = context.position.character;\n  for (let i = 0; i < context.lines.length; i++) {\n    const first = i === 0;\n    const last = i === context.lines.length - 1;\n    let line = context.lines[i];\n    let index = 0;\n    if (first && context.options.start) {\n      const match = (_a = context.options.start) === null || _a === void 0 ? void 0 : _a.exec(line);\n      if (match) {\n        index = match.index + match[0].length;\n      }\n    } else {\n      const match = (_b = context.options.line) === null || _b === void 0 ? void 0 : _b.exec(line);\n      if (match) {\n        index = match.index + match[0].length;\n      }\n    }\n    if (last) {\n      const match = (_c = context.options.end) === null || _c === void 0 ? void 0 : _c.exec(line);\n      if (match) {\n        line = line.substring(0, match.index);\n      }\n    }\n    line = line.substring(0, lastCharacter(line));\n    const whitespaceEnd = skipWhitespace(line, index);\n    if (whitespaceEnd >= line.length) {\n      // Only create a break token when we already have previous tokens\n      if (tokens.length > 0) {\n        const position = Position.create(currentLine, currentCharacter);\n        tokens.push({\n          type: 'break',\n          content: '',\n          range: Range.create(position, position)\n        });\n      }\n    } else {\n      tagRegex.lastIndex = index;\n      const tagMatch = tagRegex.exec(line);\n      if (tagMatch) {\n        const fullMatch = tagMatch[0];\n        const value = tagMatch[1];\n        const start = Position.create(currentLine, currentCharacter + index);\n        const end = Position.create(currentLine, currentCharacter + index + fullMatch.length);\n        tokens.push({\n          type: 'tag',\n          content: value,\n          range: Range.create(start, end)\n        });\n        index += fullMatch.length;\n        index = skipWhitespace(line, index);\n      }\n      if (index < line.length) {\n        const rest = line.substring(index);\n        const inlineTagMatches = Array.from(rest.matchAll(inlineTagRegex));\n        tokens.push(...buildInlineTokens(inlineTagMatches, rest, currentLine, currentCharacter + index));\n      }\n    }\n    currentLine++;\n    currentCharacter = 0;\n  }\n  // Remove last break token if there is one\n  if (tokens.length > 0 && tokens[tokens.length - 1].type === 'break') {\n    return tokens.slice(0, -1);\n  }\n  return tokens;\n}\nfunction buildInlineTokens(tags, line, lineIndex, characterIndex) {\n  const tokens = [];\n  if (tags.length === 0) {\n    const start = Position.create(lineIndex, characterIndex);\n    const end = Position.create(lineIndex, characterIndex + line.length);\n    tokens.push({\n      type: 'text',\n      content: line,\n      range: Range.create(start, end)\n    });\n  } else {\n    let lastIndex = 0;\n    for (const match of tags) {\n      const matchIndex = match.index;\n      const startContent = line.substring(lastIndex, matchIndex);\n      if (startContent.length > 0) {\n        tokens.push({\n          type: 'text',\n          content: line.substring(lastIndex, matchIndex),\n          range: Range.create(Position.create(lineIndex, lastIndex + characterIndex), Position.create(lineIndex, matchIndex + characterIndex))\n        });\n      }\n      let offset = startContent.length + 1;\n      const tagName = match[1];\n      tokens.push({\n        type: 'inline-tag',\n        content: tagName,\n        range: Range.create(Position.create(lineIndex, lastIndex + offset + characterIndex), Position.create(lineIndex, lastIndex + offset + tagName.length + characterIndex))\n      });\n      offset += tagName.length;\n      if (match.length === 4) {\n        offset += match[2].length;\n        const value = match[3];\n        tokens.push({\n          type: 'text',\n          content: value,\n          range: Range.create(Position.create(lineIndex, lastIndex + offset + characterIndex), Position.create(lineIndex, lastIndex + offset + value.length + characterIndex))\n        });\n      } else {\n        tokens.push({\n          type: 'text',\n          content: '',\n          range: Range.create(Position.create(lineIndex, lastIndex + offset + characterIndex), Position.create(lineIndex, lastIndex + offset + characterIndex))\n        });\n      }\n      lastIndex = matchIndex + match[0].length;\n    }\n    const endContent = line.substring(lastIndex);\n    if (endContent.length > 0) {\n      tokens.push({\n        type: 'text',\n        content: endContent,\n        range: Range.create(Position.create(lineIndex, lastIndex + characterIndex), Position.create(lineIndex, lastIndex + characterIndex + endContent.length))\n      });\n    }\n  }\n  return tokens;\n}\nconst nonWhitespaceRegex = /\\S/;\nconst whitespaceEndRegex = /\\s*$/;\nfunction skipWhitespace(line, index) {\n  const match = line.substring(index).match(nonWhitespaceRegex);\n  if (match) {\n    return index + match.index;\n  } else {\n    return line.length;\n  }\n}\nfunction lastCharacter(line) {\n  const match = line.match(whitespaceEndRegex);\n  if (match && typeof match.index === 'number') {\n    return match.index;\n  }\n  return undefined;\n}\n// Parsing\nfunction parseJSDocComment(context) {\n  var _a, _b, _c, _d;\n  const startPosition = Position.create(context.position.line, context.position.character);\n  if (context.tokens.length === 0) {\n    return new JSDocCommentImpl([], Range.create(startPosition, startPosition));\n  }\n  const elements = [];\n  while (context.index < context.tokens.length) {\n    const element = parseJSDocElement(context, elements[elements.length - 1]);\n    if (element) {\n      elements.push(element);\n    }\n  }\n  const start = (_b = (_a = elements[0]) === null || _a === void 0 ? void 0 : _a.range.start) !== null && _b !== void 0 ? _b : startPosition;\n  const end = (_d = (_c = elements[elements.length - 1]) === null || _c === void 0 ? void 0 : _c.range.end) !== null && _d !== void 0 ? _d : startPosition;\n  return new JSDocCommentImpl(elements, Range.create(start, end));\n}\nfunction parseJSDocElement(context, last) {\n  const next = context.tokens[context.index];\n  if (next.type === 'tag') {\n    return parseJSDocTag(context, false);\n  } else if (next.type === 'text' || next.type === 'inline-tag') {\n    return parseJSDocText(context);\n  } else {\n    appendEmptyLine(next, last);\n    context.index++;\n    return undefined;\n  }\n}\nfunction appendEmptyLine(token, element) {\n  if (element) {\n    const line = new JSDocLineImpl('', token.range);\n    if ('inlines' in element) {\n      element.inlines.push(line);\n    } else {\n      element.content.inlines.push(line);\n    }\n  }\n}\nfunction parseJSDocText(context) {\n  let token = context.tokens[context.index];\n  const firstToken = token;\n  let lastToken = token;\n  const lines = [];\n  while (token && token.type !== 'break' && token.type !== 'tag') {\n    lines.push(parseJSDocInline(context));\n    lastToken = token;\n    token = context.tokens[context.index];\n  }\n  return new JSDocTextImpl(lines, Range.create(firstToken.range.start, lastToken.range.end));\n}\nfunction parseJSDocInline(context) {\n  const token = context.tokens[context.index];\n  if (token.type === 'inline-tag') {\n    return parseJSDocTag(context, true);\n  } else {\n    return parseJSDocLine(context);\n  }\n}\nfunction parseJSDocTag(context, inline) {\n  const tagToken = context.tokens[context.index++];\n  const name = tagToken.content.substring(1);\n  const nextToken = context.tokens[context.index];\n  if ((nextToken === null || nextToken === void 0 ? void 0 : nextToken.type) === 'text') {\n    if (inline) {\n      const docLine = parseJSDocLine(context);\n      return new JSDocTagImpl(name, new JSDocTextImpl([docLine], docLine.range), inline, Range.create(tagToken.range.start, docLine.range.end));\n    } else {\n      const textDoc = parseJSDocText(context);\n      return new JSDocTagImpl(name, textDoc, inline, Range.create(tagToken.range.start, textDoc.range.end));\n    }\n  } else {\n    const range = tagToken.range;\n    return new JSDocTagImpl(name, new JSDocTextImpl([], range), inline, range);\n  }\n}\nfunction parseJSDocLine(context) {\n  const token = context.tokens[context.index++];\n  return new JSDocLineImpl(token.content, token.range);\n}\nfunction normalizeOptions(options) {\n  if (!options) {\n    return normalizeOptions({\n      start: '/**',\n      end: '*/',\n      line: '*'\n    });\n  }\n  const {\n    start,\n    end,\n    line\n  } = options;\n  return {\n    start: normalizeOption(start, true),\n    end: normalizeOption(end, false),\n    line: normalizeOption(line, true)\n  };\n}\nfunction normalizeOption(option, start) {\n  if (typeof option === 'string' || typeof option === 'object') {\n    const escaped = typeof option === 'string' ? escapeRegExp(option) : option.source;\n    if (start) {\n      return new RegExp(`^\\\\s*${escaped}`);\n    } else {\n      return new RegExp(`\\\\s*${escaped}\\\\s*$`);\n    }\n  } else {\n    return option;\n  }\n}\nclass JSDocCommentImpl {\n  constructor(elements, range) {\n    this.elements = elements;\n    this.range = range;\n  }\n  getTag(name) {\n    return this.getAllTags().find(e => e.name === name);\n  }\n  getTags(name) {\n    return this.getAllTags().filter(e => e.name === name);\n  }\n  getAllTags() {\n    return this.elements.filter(e => 'name' in e);\n  }\n  toString() {\n    let value = '';\n    for (const element of this.elements) {\n      if (value.length === 0) {\n        value = element.toString();\n      } else {\n        const text = element.toString();\n        value += fillNewlines(value) + text;\n      }\n    }\n    return value.trim();\n  }\n  toMarkdown(options) {\n    let value = '';\n    for (const element of this.elements) {\n      if (value.length === 0) {\n        value = element.toMarkdown(options);\n      } else {\n        const text = element.toMarkdown(options);\n        value += fillNewlines(value) + text;\n      }\n    }\n    return value.trim();\n  }\n}\nclass JSDocTagImpl {\n  constructor(name, content, inline, range) {\n    this.name = name;\n    this.content = content;\n    this.inline = inline;\n    this.range = range;\n  }\n  toString() {\n    let text = `@${this.name}`;\n    const content = this.content.toString();\n    if (this.content.inlines.length === 1) {\n      text = `${text} ${content}`;\n    } else if (this.content.inlines.length > 1) {\n      text = `${text}\\n${content}`;\n    }\n    if (this.inline) {\n      // Inline tags are surrounded by curly braces\n      return `{${text}}`;\n    } else {\n      return text;\n    }\n  }\n  toMarkdown(options) {\n    var _a, _b;\n    return (_b = (_a = options === null || options === void 0 ? void 0 : options.renderTag) === null || _a === void 0 ? void 0 : _a.call(options, this)) !== null && _b !== void 0 ? _b : this.toMarkdownDefault(options);\n  }\n  toMarkdownDefault(options) {\n    const content = this.content.toMarkdown(options);\n    if (this.inline) {\n      const rendered = renderInlineTag(this.name, content, options !== null && options !== void 0 ? options : {});\n      if (typeof rendered === 'string') {\n        return rendered;\n      }\n    }\n    let marker = '';\n    if ((options === null || options === void 0 ? void 0 : options.tag) === 'italic' || (options === null || options === void 0 ? void 0 : options.tag) === undefined) {\n      marker = '*';\n    } else if ((options === null || options === void 0 ? void 0 : options.tag) === 'bold') {\n      marker = '**';\n    } else if ((options === null || options === void 0 ? void 0 : options.tag) === 'bold-italic') {\n      marker = '***';\n    }\n    let text = `${marker}@${this.name}${marker}`;\n    if (this.content.inlines.length === 1) {\n      text = `${text} — ${content}`;\n    } else if (this.content.inlines.length > 1) {\n      text = `${text}\\n${content}`;\n    }\n    if (this.inline) {\n      // Inline tags are surrounded by curly braces\n      return `{${text}}`;\n    } else {\n      return text;\n    }\n  }\n}\nfunction renderInlineTag(tag, content, options) {\n  var _a, _b;\n  if (tag === 'linkplain' || tag === 'linkcode' || tag === 'link') {\n    const index = content.indexOf(' ');\n    let display = content;\n    if (index > 0) {\n      const displayStart = skipWhitespace(content, index);\n      display = content.substring(displayStart);\n      content = content.substring(0, index);\n    }\n    if (tag === 'linkcode' || tag === 'link' && options.link === 'code') {\n      // Surround the display value in a markdown inline code block\n      display = `\\`${display}\\``;\n    }\n    const renderedLink = (_b = (_a = options.renderLink) === null || _a === void 0 ? void 0 : _a.call(options, content, display)) !== null && _b !== void 0 ? _b : renderLinkDefault(content, display);\n    return renderedLink;\n  }\n  return undefined;\n}\nfunction renderLinkDefault(content, display) {\n  try {\n    URI.parse(content, true);\n    return `[${display}](${content})`;\n  } catch (_a) {\n    return content;\n  }\n}\nclass JSDocTextImpl {\n  constructor(lines, range) {\n    this.inlines = lines;\n    this.range = range;\n  }\n  toString() {\n    let text = '';\n    for (let i = 0; i < this.inlines.length; i++) {\n      const inline = this.inlines[i];\n      const next = this.inlines[i + 1];\n      text += inline.toString();\n      if (next && next.range.start.line > inline.range.start.line) {\n        text += '\\n';\n      }\n    }\n    return text;\n  }\n  toMarkdown(options) {\n    let text = '';\n    for (let i = 0; i < this.inlines.length; i++) {\n      const inline = this.inlines[i];\n      const next = this.inlines[i + 1];\n      text += inline.toMarkdown(options);\n      if (next && next.range.start.line > inline.range.start.line) {\n        text += '\\n';\n      }\n    }\n    return text;\n  }\n}\nclass JSDocLineImpl {\n  constructor(text, range) {\n    this.text = text;\n    this.range = range;\n  }\n  toString() {\n    return this.text;\n  }\n  toMarkdown() {\n    return this.text;\n  }\n}\nfunction fillNewlines(text) {\n  if (text.endsWith('\\n')) {\n    return '\\n';\n  } else {\n    return '\\n\\n';\n  }\n}", "map": {"version": 3, "names": ["Position", "Range", "NEWLINE_REGEXP", "escapeRegExp", "URI", "parseJSDoc", "node", "start", "options", "opts", "position", "range", "create", "lines", "getLines", "normalizedOptions", "normalizeOptions", "tokens", "tokenize", "parseJSDocComment", "index", "isJSDoc", "length", "first", "last", "firstRegex", "lastRegex", "end", "Boolean", "exec", "content", "text", "split", "tagRegex", "inlineTagRegex", "context", "currentLine", "line", "currentCharacter", "character", "i", "match", "_a", "_b", "_c", "substring", "lastCharacter", "whitespaceEnd", "skipWhitespace", "push", "type", "lastIndex", "tagMatch", "fullMatch", "value", "rest", "inlineTagMatches", "Array", "from", "matchAll", "buildInlineTokens", "slice", "tags", "lineIndex", "characterIndex", "matchIndex", "startContent", "offset", "tagName", "endContent", "nonWhitespaceRegex", "whitespaceEndRegex", "undefined", "startPosition", "JSDocCommentImpl", "elements", "element", "parseJSDocElement", "_d", "next", "parseJSDocTag", "parseJSDocText", "appendEmptyLine", "token", "JSDocLineImpl", "inlines", "firstToken", "lastToken", "parseJSDocInline", "JSDocTextImpl", "parseJSDocLine", "inline", "tagToken", "name", "nextToken", "docLine", "JSDocTagImpl", "textDoc", "normalizeOption", "option", "escaped", "source", "RegExp", "constructor", "getTag", "getAllTags", "find", "e", "getTags", "filter", "toString", "fillNewlines", "trim", "toMarkdown", "renderTag", "call", "toMarkdownDefault", "rendered", "renderInlineTag", "marker", "tag", "indexOf", "display", "displayStart", "link", "renderedLink", "renderLink", "renderLinkDefault", "parse", "endsWith"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/documentation/jsdoc.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { Position, Range } from 'vscode-languageserver-types';\r\nimport type { CstNode } from '../syntax-tree.js';\r\nimport { NEWLINE_REGEXP, escapeRegExp } from '../utils/regexp-utils.js';\r\nimport { URI } from '../utils/uri-utils.js';\r\n\r\nexport interface JSDocComment extends JSDocValue {\r\n    readonly elements: JSDocElement[]\r\n    getTag(name: string): JSDocTag | undefined\r\n    getTags(name: string): JSDocTag[]\r\n}\r\n\r\nexport type JSDocElement = JSDocParagraph | JSDocTag;\r\n\r\nexport type JSDocInline = JSDocTag | JSDocLine;\r\n\r\nexport interface JSDocValue {\r\n    /**\r\n     * Represents the range that this JSDoc element occupies.\r\n     * If the JSDoc was parsed from a `CstNode`, the range will represent the location in the source document.\r\n     */\r\n    readonly range: Range\r\n    /**\r\n     * Renders this JSDoc element to a plain text representation.\r\n     */\r\n    toString(): string\r\n    /**\r\n     * Renders this JSDoc element to a markdown representation.\r\n     *\r\n     * @param options Rendering options to customize the markdown result.\r\n     */\r\n    toMarkdown(options?: JSDocRenderOptions): string\r\n}\r\n\r\nexport interface JSDocParagraph extends JSDocValue {\r\n    readonly inlines: JSDocInline[]\r\n}\r\n\r\nexport interface JSDocLine extends JSDocValue {\r\n    readonly text: string\r\n}\r\n\r\nexport interface JSDocTag extends JSDocValue {\r\n    readonly name: string\r\n    readonly content: JSDocParagraph\r\n    readonly inline: boolean\r\n}\r\n\r\nexport interface JSDocParseOptions {\r\n    /**\r\n     * The start symbol of your comment format. Defaults to `/**`.\r\n     */\r\n    readonly start?: RegExp | string\r\n    /**\r\n     * The symbol that start a line of your comment format. Defaults to `*`.\r\n     */\r\n    readonly line?: RegExp | string\r\n    /**\r\n     * The end symbol of your comment format. Defaults to `*\\/`.\r\n     */\r\n    readonly end?: RegExp | string\r\n}\r\n\r\nexport interface JSDocRenderOptions {\r\n    /**\r\n     * Determines the style for rendering tags. Defaults to `italic`.\r\n     */\r\n    tag?: 'plain' | 'italic' | 'bold' | 'bold-italic'\r\n    /**\r\n     * Determines the default for rendering `@link` tags. Defaults to `plain`.\r\n     */\r\n    link?: 'code' | 'plain'\r\n    /**\r\n     * Custom tag rendering function.\r\n     * Return a markdown formatted tag or `undefined` to fall back to the default rendering.\r\n     */\r\n    renderTag?(tag: JSDocTag): string | undefined\r\n    /**\r\n     * Custom link rendering function. Accepts a link target and a display value for the link.\r\n     * Return a markdown formatted link with the format `[$display]($link)` or `undefined` if the link is not a valid target.\r\n     */\r\n    renderLink?(link: string, display: string): string | undefined\r\n}\r\n\r\n/**\r\n * Parses a JSDoc from a `CstNode` containing a comment.\r\n *\r\n * @param node A `CstNode` from a parsed Langium document.\r\n * @param options Parsing options specialized to your language. See {@link JSDocParseOptions}.\r\n */\r\nexport function parseJSDoc(node: CstNode, options?: JSDocParseOptions): JSDocComment;\r\n/**\r\n * Parses a JSDoc from a string comment.\r\n *\r\n * @param content A string containing the source of the JSDoc comment.\r\n * @param start The start position the comment occupies in the source document.\r\n * @param options Parsing options specialized to your language. See {@link JSDocParseOptions}.\r\n */\r\nexport function parseJSDoc(content: string, start?: Position, options?: JSDocParseOptions): JSDocComment;\r\nexport function parseJSDoc(node: CstNode | string, start?: Position | JSDocParseOptions, options?: JSDocParseOptions): JSDocComment {\r\n    let opts: JSDocParseOptions | undefined;\r\n    let position: Position | undefined;\r\n    if (typeof node === 'string') {\r\n        position = start as Position | undefined;\r\n        opts = options as JSDocParseOptions | undefined;\r\n    } else {\r\n        position = node.range.start;\r\n        opts = start as JSDocParseOptions | undefined;\r\n    }\r\n    if (!position) {\r\n        position = Position.create(0, 0);\r\n    }\r\n\r\n    const lines = getLines(node);\r\n    const normalizedOptions = normalizeOptions(opts);\r\n\r\n    const tokens = tokenize({\r\n        lines,\r\n        position,\r\n        options: normalizedOptions\r\n    });\r\n\r\n    return parseJSDocComment({\r\n        index: 0,\r\n        tokens,\r\n        position\r\n    });\r\n}\r\n\r\nexport function isJSDoc(node: CstNode | string, options?: JSDocParseOptions): boolean {\r\n    const normalizedOptions = normalizeOptions(options);\r\n    const lines = getLines(node);\r\n    if (lines.length === 0) {\r\n        return false;\r\n    }\r\n\r\n    const first = lines[0];\r\n    const last = lines[lines.length - 1];\r\n    const firstRegex = normalizedOptions.start;\r\n    const lastRegex = normalizedOptions.end;\r\n\r\n    return Boolean(firstRegex?.exec(first)) && Boolean(lastRegex?.exec(last));\r\n}\r\n\r\nfunction getLines(node: CstNode | string): string[] {\r\n    let content = '';\r\n    if (typeof node === 'string') {\r\n        content = node;\r\n    } else {\r\n        content = node.text;\r\n    }\r\n    const lines = content.split(NEWLINE_REGEXP);\r\n    return lines;\r\n}\r\n\r\n// Tokenization\r\n\r\ninterface JSDocToken {\r\n    type: 'text' | 'tag' | 'inline-tag' | 'break'\r\n    content: string\r\n    range: Range\r\n}\r\n\r\nconst tagRegex = /\\s*(@([\\p{L}][\\p{L}\\p{N}]*)?)/uy;\r\nconst inlineTagRegex = /\\{(@[\\p{L}][\\p{L}\\p{N}]*)(\\s*)([^\\r\\n}]+)?\\}/gu;\r\n\r\nfunction tokenize(context: TokenizationContext): JSDocToken[] {\r\n    const tokens: JSDocToken[] = [];\r\n    let currentLine = context.position.line;\r\n    let currentCharacter = context.position.character;\r\n    for (let i = 0; i < context.lines.length; i++) {\r\n        const first = i === 0;\r\n        const last = i === context.lines.length - 1;\r\n        let line = context.lines[i];\r\n        let index = 0;\r\n\r\n        if (first && context.options.start) {\r\n            const match = context.options.start?.exec(line);\r\n            if (match) {\r\n                index = match.index + match[0].length;\r\n            }\r\n        } else {\r\n            const match = context.options.line?.exec(line);\r\n            if (match) {\r\n                index = match.index + match[0].length;\r\n            }\r\n        }\r\n        if (last) {\r\n            const match = context.options.end?.exec(line);\r\n            if (match) {\r\n                line = line.substring(0, match.index);\r\n            }\r\n        }\r\n\r\n        line = line.substring(0, lastCharacter(line));\r\n        const whitespaceEnd = skipWhitespace(line, index);\r\n\r\n        if (whitespaceEnd >= line.length) {\r\n            // Only create a break token when we already have previous tokens\r\n            if (tokens.length > 0) {\r\n                const position = Position.create(currentLine, currentCharacter);\r\n                tokens.push({\r\n                    type: 'break',\r\n                    content: '',\r\n                    range: Range.create(position, position)\r\n                });\r\n            }\r\n        } else {\r\n            tagRegex.lastIndex = index;\r\n            const tagMatch = tagRegex.exec(line);\r\n            if (tagMatch) {\r\n                const fullMatch = tagMatch[0];\r\n                const value = tagMatch[1];\r\n                const start = Position.create(currentLine, currentCharacter + index);\r\n                const end = Position.create(currentLine, currentCharacter + index + fullMatch.length);\r\n                tokens.push({\r\n                    type: 'tag',\r\n                    content: value,\r\n                    range: Range.create(start, end)\r\n                });\r\n                index += fullMatch.length;\r\n                index = skipWhitespace(line, index);\r\n            }\r\n\r\n            if (index < line.length) {\r\n                const rest = line.substring(index);\r\n                const inlineTagMatches = Array.from(rest.matchAll(inlineTagRegex));\r\n                tokens.push(...buildInlineTokens(inlineTagMatches, rest, currentLine, currentCharacter + index));\r\n            }\r\n        }\r\n\r\n        currentLine++;\r\n        currentCharacter = 0;\r\n    }\r\n\r\n    // Remove last break token if there is one\r\n    if (tokens.length > 0 && tokens[tokens.length - 1].type === 'break') {\r\n        return tokens.slice(0, -1);\r\n    }\r\n\r\n    return tokens;\r\n}\r\n\r\nfunction buildInlineTokens(tags: RegExpMatchArray[], line: string, lineIndex: number, characterIndex: number): JSDocToken[] {\r\n    const tokens: JSDocToken[] = [];\r\n\r\n    if (tags.length === 0) {\r\n        const start = Position.create(lineIndex, characterIndex);\r\n        const end = Position.create(lineIndex, characterIndex + line.length);\r\n        tokens.push({\r\n            type: 'text',\r\n            content: line,\r\n            range: Range.create(start, end)\r\n        });\r\n    } else {\r\n        let lastIndex = 0;\r\n        for (const match of tags) {\r\n            const matchIndex = match.index!;\r\n            const startContent = line.substring(lastIndex, matchIndex);\r\n            if (startContent.length > 0) {\r\n                tokens.push({\r\n                    type: 'text',\r\n                    content: line.substring(lastIndex, matchIndex),\r\n                    range: Range.create(\r\n                        Position.create(lineIndex, lastIndex + characterIndex),\r\n                        Position.create(lineIndex, matchIndex + characterIndex)\r\n                    )\r\n                });\r\n            }\r\n            let offset = startContent.length + 1;\r\n            const tagName = match[1];\r\n            tokens.push({\r\n                type: 'inline-tag',\r\n                content: tagName,\r\n                range: Range.create(\r\n                    Position.create(lineIndex, lastIndex + offset + characterIndex),\r\n                    Position.create(lineIndex, lastIndex + offset + tagName.length + characterIndex)\r\n                )\r\n            });\r\n            offset += tagName.length;\r\n            if (match.length === 4) {\r\n                offset += match[2].length;\r\n                const value = match[3];\r\n                tokens.push({\r\n                    type: 'text',\r\n                    content: value,\r\n                    range: Range.create(\r\n                        Position.create(lineIndex, lastIndex + offset + characterIndex),\r\n                        Position.create(lineIndex, lastIndex + offset + value.length + characterIndex)\r\n                    )\r\n                });\r\n            } else {\r\n                tokens.push({\r\n                    type: 'text',\r\n                    content: '',\r\n                    range: Range.create(\r\n                        Position.create(lineIndex, lastIndex + offset + characterIndex),\r\n                        Position.create(lineIndex, lastIndex + offset + characterIndex)\r\n                    )\r\n                });\r\n            }\r\n            lastIndex = matchIndex + match[0].length;\r\n        }\r\n        const endContent = line.substring(lastIndex);\r\n        if (endContent.length > 0) {\r\n            tokens.push({\r\n                type: 'text',\r\n                content: endContent,\r\n                range: Range.create(\r\n                    Position.create(lineIndex, lastIndex + characterIndex),\r\n                    Position.create(lineIndex, lastIndex + characterIndex + endContent.length)\r\n                )\r\n            });\r\n        }\r\n    }\r\n\r\n    return tokens;\r\n}\r\n\r\nconst nonWhitespaceRegex = /\\S/;\r\nconst whitespaceEndRegex = /\\s*$/;\r\n\r\nfunction skipWhitespace(line: string, index: number): number {\r\n    const match = line.substring(index).match(nonWhitespaceRegex);\r\n    if (match) {\r\n        return index + match.index!;\r\n    } else {\r\n        return line.length;\r\n    }\r\n}\r\n\r\nfunction lastCharacter(line: string): number | undefined {\r\n    const match = line.match(whitespaceEndRegex);\r\n    if (match && typeof match.index === 'number') {\r\n        return match.index;\r\n    }\r\n    return undefined;\r\n}\r\n\r\n// Parsing\r\n\r\nfunction parseJSDocComment(context: ParseContext): JSDocComment {\r\n    const startPosition: Position = Position.create(context.position.line, context.position.character);\r\n    if (context.tokens.length === 0) {\r\n        return new JSDocCommentImpl([], Range.create(startPosition, startPosition));\r\n    }\r\n    const elements: JSDocElement[] = [];\r\n    while (context.index < context.tokens.length) {\r\n        const element = parseJSDocElement(context, elements[elements.length - 1]);\r\n        if (element) {\r\n            elements.push(element);\r\n        }\r\n    }\r\n    const start = elements[0]?.range.start ?? startPosition;\r\n    const end = elements[elements.length - 1]?.range.end ?? startPosition;\r\n    return new JSDocCommentImpl(elements, Range.create(start, end));\r\n}\r\n\r\nfunction parseJSDocElement(context: ParseContext, last?: JSDocElement): JSDocElement | undefined {\r\n    const next = context.tokens[context.index];\r\n    if (next.type === 'tag') {\r\n        return parseJSDocTag(context, false);\r\n    } else if (next.type === 'text' || next.type === 'inline-tag') {\r\n        return parseJSDocText(context);\r\n    } else {\r\n        appendEmptyLine(next, last);\r\n        context.index++;\r\n        return undefined;\r\n    }\r\n}\r\n\r\nfunction appendEmptyLine(token: JSDocToken, element?: JSDocElement): void {\r\n    if (element) {\r\n        const line = new JSDocLineImpl('', token.range);\r\n        if ('inlines' in element) {\r\n            element.inlines.push(line);\r\n        } else {\r\n            element.content.inlines.push(line);\r\n        }\r\n    }\r\n}\r\n\r\nfunction parseJSDocText(context: ParseContext): JSDocParagraph {\r\n    let token = context.tokens[context.index];\r\n    const firstToken = token;\r\n    let lastToken = token;\r\n    const lines: JSDocInline[] = [];\r\n    while (token && token.type !== 'break' && token.type !== 'tag') {\r\n        lines.push(parseJSDocInline(context));\r\n        lastToken = token;\r\n        token = context.tokens[context.index];\r\n    }\r\n    return new JSDocTextImpl(lines, Range.create(firstToken.range.start, lastToken.range.end));\r\n}\r\n\r\nfunction parseJSDocInline(context: ParseContext): JSDocInline {\r\n    const token = context.tokens[context.index];\r\n    if (token.type === 'inline-tag') {\r\n        return parseJSDocTag(context, true);\r\n    } else {\r\n        return parseJSDocLine(context);\r\n    }\r\n}\r\n\r\nfunction parseJSDocTag(context: ParseContext, inline: boolean): JSDocTag {\r\n    const tagToken = context.tokens[context.index++];\r\n    const name = tagToken.content.substring(1);\r\n    const nextToken = context.tokens[context.index];\r\n    if (nextToken?.type === 'text') {\r\n        if (inline) {\r\n            const docLine = parseJSDocLine(context);\r\n            return new JSDocTagImpl(\r\n                name,\r\n                new JSDocTextImpl([docLine], docLine.range),\r\n                inline,\r\n                Range.create(tagToken.range.start, docLine.range.end)\r\n            );\r\n        } else {\r\n            const textDoc = parseJSDocText(context);\r\n            return new JSDocTagImpl(\r\n                name,\r\n                textDoc,\r\n                inline,\r\n                Range.create(tagToken.range.start, textDoc.range.end)\r\n            );\r\n        }\r\n    } else {\r\n        const range = tagToken.range;\r\n        return new JSDocTagImpl(name, new JSDocTextImpl([], range), inline, range);\r\n    }\r\n}\r\n\r\nfunction parseJSDocLine(context: ParseContext): JSDocLine {\r\n    const token = context.tokens[context.index++];\r\n    return new JSDocLineImpl(token.content, token.range);\r\n}\r\n\r\ninterface NormalizedOptions {\r\n    start?: RegExp\r\n    end?: RegExp\r\n    line?: RegExp\r\n}\r\n\r\ninterface TokenizationContext {\r\n    position: Position\r\n    lines: string[]\r\n    options: NormalizedOptions\r\n}\r\n\r\ninterface ParseContext {\r\n    position: Position\r\n    tokens: JSDocToken[]\r\n    index: number\r\n}\r\n\r\nfunction normalizeOptions(options?: JSDocParseOptions): NormalizedOptions {\r\n    if (!options) {\r\n        return normalizeOptions({\r\n            start: '/**',\r\n            end: '*/',\r\n            line: '*'\r\n        });\r\n    }\r\n    const { start, end, line } = options;\r\n    return {\r\n        start: normalizeOption(start, true),\r\n        end: normalizeOption(end, false),\r\n        line: normalizeOption(line, true)\r\n    };\r\n}\r\n\r\nfunction normalizeOption(option: RegExp | string | undefined, start: boolean): RegExp | undefined {\r\n    if (typeof option === 'string' || typeof option === 'object') {\r\n        const escaped = typeof option === 'string' ? escapeRegExp(option) : option.source;\r\n        if (start) {\r\n            return new RegExp(`^\\\\s*${escaped}`);\r\n        } else {\r\n            return new RegExp(`\\\\s*${escaped}\\\\s*$`);\r\n        }\r\n    } else {\r\n        return option;\r\n    }\r\n}\r\n\r\nclass JSDocCommentImpl implements JSDocComment {\r\n\r\n    readonly elements: JSDocElement[];\r\n    readonly range: Range;\r\n\r\n    constructor(elements: JSDocElement[], range: Range) {\r\n        this.elements = elements;\r\n        this.range = range;\r\n    }\r\n\r\n    getTag(name: string): JSDocTag | undefined {\r\n        return this.getAllTags().find(e => e.name === name);\r\n    }\r\n\r\n    getTags(name: string): JSDocTag[] {\r\n        return this.getAllTags().filter(e => e.name === name);\r\n    }\r\n\r\n    private getAllTags(): JSDocTag[] {\r\n        return this.elements.filter((e): e is JSDocTag => 'name' in e);\r\n    }\r\n\r\n    toString(): string {\r\n        let value = '';\r\n        for (const element of this.elements) {\r\n            if (value.length === 0) {\r\n                value = element.toString();\r\n            } else {\r\n                const text = element.toString();\r\n                value += fillNewlines(value) + text;\r\n            }\r\n        }\r\n        return value.trim();\r\n    }\r\n\r\n    toMarkdown(options?: JSDocRenderOptions): string {\r\n        let value = '';\r\n        for (const element of this.elements) {\r\n            if (value.length === 0) {\r\n                value = element.toMarkdown(options);\r\n            } else {\r\n                const text = element.toMarkdown(options);\r\n                value += fillNewlines(value) + text;\r\n            }\r\n        }\r\n        return value.trim();\r\n    }\r\n}\r\n\r\nclass JSDocTagImpl implements JSDocTag {\r\n    name: string;\r\n    content: JSDocParagraph;\r\n    range: Range;\r\n    inline: boolean;\r\n\r\n    constructor(name: string, content: JSDocParagraph, inline: boolean, range: Range) {\r\n        this.name = name;\r\n        this.content = content;\r\n        this.inline = inline;\r\n        this.range = range;\r\n    }\r\n\r\n    toString(): string {\r\n        let text = `@${this.name}`;\r\n        const content = this.content.toString();\r\n        if (this.content.inlines.length === 1) {\r\n            text = `${text} ${content}`;\r\n        } else if (this.content.inlines.length > 1) {\r\n            text = `${text}\\n${content}`;\r\n        }\r\n        if (this.inline) {\r\n            // Inline tags are surrounded by curly braces\r\n            return `{${text}}`;\r\n        } else {\r\n            return text;\r\n        }\r\n    }\r\n\r\n    toMarkdown(options?: JSDocRenderOptions): string {\r\n        return options?.renderTag?.(this) ?? this.toMarkdownDefault(options);\r\n    }\r\n\r\n    private toMarkdownDefault(options?: JSDocRenderOptions): string {\r\n        const content = this.content.toMarkdown(options);\r\n        if (this.inline) {\r\n            const rendered = renderInlineTag(this.name, content, options ?? {});\r\n            if (typeof rendered === 'string') {\r\n                return rendered;\r\n            }\r\n        }\r\n        let marker = '';\r\n        if (options?.tag === 'italic' || options?.tag === undefined) {\r\n            marker = '*';\r\n        } else if (options?.tag === 'bold') {\r\n            marker = '**';\r\n        } else if (options?.tag === 'bold-italic') {\r\n            marker = '***';\r\n        }\r\n        let text = `${marker}@${this.name}${marker}`;\r\n        if (this.content.inlines.length === 1) {\r\n            text = `${text} — ${content}`;\r\n        } else if (this.content.inlines.length > 1) {\r\n            text = `${text}\\n${content}`;\r\n        }\r\n        if (this.inline) {\r\n            // Inline tags are surrounded by curly braces\r\n            return `{${text}}`;\r\n        } else {\r\n            return text;\r\n        }\r\n    }\r\n}\r\n\r\nfunction renderInlineTag(tag: string, content: string, options: JSDocRenderOptions): string | undefined {\r\n    if (tag === 'linkplain' || tag === 'linkcode' || tag === 'link') {\r\n        const index = content.indexOf(' ');\r\n        let display = content;\r\n        if (index > 0) {\r\n            const displayStart = skipWhitespace(content, index);\r\n            display = content.substring(displayStart);\r\n            content = content.substring(0, index);\r\n        }\r\n        if (tag === 'linkcode' || (tag === 'link' && options.link === 'code')) {\r\n            // Surround the display value in a markdown inline code block\r\n            display = `\\`${display}\\``;\r\n        }\r\n        const renderedLink = options.renderLink?.(content, display) ?? renderLinkDefault(content, display);\r\n        return renderedLink;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction renderLinkDefault(content: string, display: string): string {\r\n    try {\r\n        URI.parse(content, true);\r\n        return `[${display}](${content})`;\r\n    } catch {\r\n        return content;\r\n    }\r\n}\r\n\r\nclass JSDocTextImpl implements JSDocParagraph {\r\n    inlines: JSDocInline[];\r\n    range: Range;\r\n\r\n    constructor(lines: JSDocInline[], range: Range) {\r\n        this.inlines = lines;\r\n        this.range = range;\r\n    }\r\n\r\n    toString(): string {\r\n        let text = '';\r\n        for (let i = 0; i < this.inlines.length; i++) {\r\n            const inline = this.inlines[i];\r\n            const next = this.inlines[i + 1];\r\n            text += inline.toString();\r\n            if (next && next.range.start.line > inline.range.start.line) {\r\n                text += '\\n';\r\n            }\r\n        }\r\n        return text;\r\n    }\r\n\r\n    toMarkdown(options?: JSDocRenderOptions): string {\r\n        let text = '';\r\n        for (let i = 0; i < this.inlines.length; i++) {\r\n            const inline = this.inlines[i];\r\n            const next = this.inlines[i + 1];\r\n            text += inline.toMarkdown(options);\r\n            if (next && next.range.start.line > inline.range.start.line) {\r\n                text += '\\n';\r\n            }\r\n        }\r\n        return text;\r\n    }\r\n}\r\n\r\nclass JSDocLineImpl implements JSDocLine {\r\n    text: string;\r\n    range: Range;\r\n\r\n    constructor(text: string, range: Range) {\r\n        this.text = text;\r\n        this.range = range;\r\n    }\r\n\r\n    toString(): string {\r\n        return this.text;\r\n    }\r\n    toMarkdown(): string {\r\n        return this.text;\r\n    }\r\n\r\n}\r\n\r\nfunction fillNewlines(text: string): string {\r\n    if (text.endsWith('\\n')) {\r\n        return '\\n';\r\n    } else {\r\n        return '\\n\\n';\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,QAAQ,EAAEC,KAAK,QAAQ,6BAA6B;AAE7D,SAASC,cAAc,EAAEC,YAAY,QAAQ,0BAA0B;AACvE,SAASC,GAAG,QAAQ,uBAAuB;AA+F3C,OAAM,SAAUC,UAAUA,CAACC,IAAsB,EAAEC,KAAoC,EAAEC,OAA2B;EAChH,IAAIC,IAAmC;EACvC,IAAIC,QAA8B;EAClC,IAAI,OAAOJ,IAAI,KAAK,QAAQ,EAAE;IAC1BI,QAAQ,GAAGH,KAA6B;IACxCE,IAAI,GAAGD,OAAwC;EACnD,CAAC,MAAM;IACHE,QAAQ,GAAGJ,IAAI,CAACK,KAAK,CAACJ,KAAK;IAC3BE,IAAI,GAAGF,KAAsC;EACjD;EACA,IAAI,CAACG,QAAQ,EAAE;IACXA,QAAQ,GAAGV,QAAQ,CAACY,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EAEA,MAAMC,KAAK,GAAGC,QAAQ,CAACR,IAAI,CAAC;EAC5B,MAAMS,iBAAiB,GAAGC,gBAAgB,CAACP,IAAI,CAAC;EAEhD,MAAMQ,MAAM,GAAGC,QAAQ,CAAC;IACpBL,KAAK;IACLH,QAAQ;IACRF,OAAO,EAAEO;GACZ,CAAC;EAEF,OAAOI,iBAAiB,CAAC;IACrBC,KAAK,EAAE,CAAC;IACRH,MAAM;IACNP;GACH,CAAC;AACN;AAEA,OAAM,SAAUW,OAAOA,CAACf,IAAsB,EAAEE,OAA2B;EACvE,MAAMO,iBAAiB,GAAGC,gBAAgB,CAACR,OAAO,CAAC;EACnD,MAAMK,KAAK,GAAGC,QAAQ,CAACR,IAAI,CAAC;EAC5B,IAAIO,KAAK,CAACS,MAAM,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EAChB;EAEA,MAAMC,KAAK,GAAGV,KAAK,CAAC,CAAC,CAAC;EACtB,MAAMW,IAAI,GAAGX,KAAK,CAACA,KAAK,CAACS,MAAM,GAAG,CAAC,CAAC;EACpC,MAAMG,UAAU,GAAGV,iBAAiB,CAACR,KAAK;EAC1C,MAAMmB,SAAS,GAAGX,iBAAiB,CAACY,GAAG;EAEvC,OAAOC,OAAO,CAACH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,IAAI,CAACN,KAAK,CAAC,CAAC,IAAIK,OAAO,CAACF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,IAAI,CAACL,IAAI,CAAC,CAAC;AAC7E;AAEA,SAASV,QAAQA,CAACR,IAAsB;EACpC,IAAIwB,OAAO,GAAG,EAAE;EAChB,IAAI,OAAOxB,IAAI,KAAK,QAAQ,EAAE;IAC1BwB,OAAO,GAAGxB,IAAI;EAClB,CAAC,MAAM;IACHwB,OAAO,GAAGxB,IAAI,CAACyB,IAAI;EACvB;EACA,MAAMlB,KAAK,GAAGiB,OAAO,CAACE,KAAK,CAAC9B,cAAc,CAAC;EAC3C,OAAOW,KAAK;AAChB;AAUA,MAAMoB,QAAQ,GAAG,iCAAiC;AAClD,MAAMC,cAAc,GAAG,gDAAgD;AAEvE,SAAShB,QAAQA,CAACiB,OAA4B;;EAC1C,MAAMlB,MAAM,GAAiB,EAAE;EAC/B,IAAImB,WAAW,GAAGD,OAAO,CAACzB,QAAQ,CAAC2B,IAAI;EACvC,IAAIC,gBAAgB,GAAGH,OAAO,CAACzB,QAAQ,CAAC6B,SAAS;EACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,CAACtB,KAAK,CAACS,MAAM,EAAEkB,CAAC,EAAE,EAAE;IAC3C,MAAMjB,KAAK,GAAGiB,CAAC,KAAK,CAAC;IACrB,MAAMhB,IAAI,GAAGgB,CAAC,KAAKL,OAAO,CAACtB,KAAK,CAACS,MAAM,GAAG,CAAC;IAC3C,IAAIe,IAAI,GAAGF,OAAO,CAACtB,KAAK,CAAC2B,CAAC,CAAC;IAC3B,IAAIpB,KAAK,GAAG,CAAC;IAEb,IAAIG,KAAK,IAAIY,OAAO,CAAC3B,OAAO,CAACD,KAAK,EAAE;MAChC,MAAMkC,KAAK,GAAG,CAAAC,EAAA,GAAAP,OAAO,CAAC3B,OAAO,CAACD,KAAK,cAAAmC,EAAA,uBAAAA,EAAA,CAAEb,IAAI,CAACQ,IAAI,CAAC;MAC/C,IAAII,KAAK,EAAE;QACPrB,KAAK,GAAGqB,KAAK,CAACrB,KAAK,GAAGqB,KAAK,CAAC,CAAC,CAAC,CAACnB,MAAM;MACzC;IACJ,CAAC,MAAM;MACH,MAAMmB,KAAK,GAAG,CAAAE,EAAA,GAAAR,OAAO,CAAC3B,OAAO,CAAC6B,IAAI,cAAAM,EAAA,uBAAAA,EAAA,CAAEd,IAAI,CAACQ,IAAI,CAAC;MAC9C,IAAII,KAAK,EAAE;QACPrB,KAAK,GAAGqB,KAAK,CAACrB,KAAK,GAAGqB,KAAK,CAAC,CAAC,CAAC,CAACnB,MAAM;MACzC;IACJ;IACA,IAAIE,IAAI,EAAE;MACN,MAAMiB,KAAK,GAAG,CAAAG,EAAA,GAAAT,OAAO,CAAC3B,OAAO,CAACmB,GAAG,cAAAiB,EAAA,uBAAAA,EAAA,CAAEf,IAAI,CAACQ,IAAI,CAAC;MAC7C,IAAII,KAAK,EAAE;QACPJ,IAAI,GAAGA,IAAI,CAACQ,SAAS,CAAC,CAAC,EAAEJ,KAAK,CAACrB,KAAK,CAAC;MACzC;IACJ;IAEAiB,IAAI,GAAGA,IAAI,CAACQ,SAAS,CAAC,CAAC,EAAEC,aAAa,CAACT,IAAI,CAAC,CAAC;IAC7C,MAAMU,aAAa,GAAGC,cAAc,CAACX,IAAI,EAAEjB,KAAK,CAAC;IAEjD,IAAI2B,aAAa,IAAIV,IAAI,CAACf,MAAM,EAAE;MAC9B;MACA,IAAIL,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;QACnB,MAAMZ,QAAQ,GAAGV,QAAQ,CAACY,MAAM,CAACwB,WAAW,EAAEE,gBAAgB,CAAC;QAC/DrB,MAAM,CAACgC,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbpB,OAAO,EAAE,EAAE;UACXnB,KAAK,EAAEV,KAAK,CAACW,MAAM,CAACF,QAAQ,EAAEA,QAAQ;SACzC,CAAC;MACN;IACJ,CAAC,MAAM;MACHuB,QAAQ,CAACkB,SAAS,GAAG/B,KAAK;MAC1B,MAAMgC,QAAQ,GAAGnB,QAAQ,CAACJ,IAAI,CAACQ,IAAI,CAAC;MACpC,IAAIe,QAAQ,EAAE;QACV,MAAMC,SAAS,GAAGD,QAAQ,CAAC,CAAC,CAAC;QAC7B,MAAME,KAAK,GAAGF,QAAQ,CAAC,CAAC,CAAC;QACzB,MAAM7C,KAAK,GAAGP,QAAQ,CAACY,MAAM,CAACwB,WAAW,EAAEE,gBAAgB,GAAGlB,KAAK,CAAC;QACpE,MAAMO,GAAG,GAAG3B,QAAQ,CAACY,MAAM,CAACwB,WAAW,EAAEE,gBAAgB,GAAGlB,KAAK,GAAGiC,SAAS,CAAC/B,MAAM,CAAC;QACrFL,MAAM,CAACgC,IAAI,CAAC;UACRC,IAAI,EAAE,KAAK;UACXpB,OAAO,EAAEwB,KAAK;UACd3C,KAAK,EAAEV,KAAK,CAACW,MAAM,CAACL,KAAK,EAAEoB,GAAG;SACjC,CAAC;QACFP,KAAK,IAAIiC,SAAS,CAAC/B,MAAM;QACzBF,KAAK,GAAG4B,cAAc,CAACX,IAAI,EAAEjB,KAAK,CAAC;MACvC;MAEA,IAAIA,KAAK,GAAGiB,IAAI,CAACf,MAAM,EAAE;QACrB,MAAMiC,IAAI,GAAGlB,IAAI,CAACQ,SAAS,CAACzB,KAAK,CAAC;QAClC,MAAMoC,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACH,IAAI,CAACI,QAAQ,CAACzB,cAAc,CAAC,CAAC;QAClEjB,MAAM,CAACgC,IAAI,CAAC,GAAGW,iBAAiB,CAACJ,gBAAgB,EAAED,IAAI,EAAEnB,WAAW,EAAEE,gBAAgB,GAAGlB,KAAK,CAAC,CAAC;MACpG;IACJ;IAEAgB,WAAW,EAAE;IACbE,gBAAgB,GAAG,CAAC;EACxB;EAEA;EACA,IAAIrB,MAAM,CAACK,MAAM,GAAG,CAAC,IAAIL,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4B,IAAI,KAAK,OAAO,EAAE;IACjE,OAAOjC,MAAM,CAAC4C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B;EAEA,OAAO5C,MAAM;AACjB;AAEA,SAAS2C,iBAAiBA,CAACE,IAAwB,EAAEzB,IAAY,EAAE0B,SAAiB,EAAEC,cAAsB;EACxG,MAAM/C,MAAM,GAAiB,EAAE;EAE/B,IAAI6C,IAAI,CAACxC,MAAM,KAAK,CAAC,EAAE;IACnB,MAAMf,KAAK,GAAGP,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEC,cAAc,CAAC;IACxD,MAAMrC,GAAG,GAAG3B,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEC,cAAc,GAAG3B,IAAI,CAACf,MAAM,CAAC;IACpEL,MAAM,CAACgC,IAAI,CAAC;MACRC,IAAI,EAAE,MAAM;MACZpB,OAAO,EAAEO,IAAI;MACb1B,KAAK,EAAEV,KAAK,CAACW,MAAM,CAACL,KAAK,EAAEoB,GAAG;KACjC,CAAC;EACN,CAAC,MAAM;IACH,IAAIwB,SAAS,GAAG,CAAC;IACjB,KAAK,MAAMV,KAAK,IAAIqB,IAAI,EAAE;MACtB,MAAMG,UAAU,GAAGxB,KAAK,CAACrB,KAAM;MAC/B,MAAM8C,YAAY,GAAG7B,IAAI,CAACQ,SAAS,CAACM,SAAS,EAAEc,UAAU,CAAC;MAC1D,IAAIC,YAAY,CAAC5C,MAAM,GAAG,CAAC,EAAE;QACzBL,MAAM,CAACgC,IAAI,CAAC;UACRC,IAAI,EAAE,MAAM;UACZpB,OAAO,EAAEO,IAAI,CAACQ,SAAS,CAACM,SAAS,EAAEc,UAAU,CAAC;UAC9CtD,KAAK,EAAEV,KAAK,CAACW,MAAM,CACfZ,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGa,cAAc,CAAC,EACtDhE,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEE,UAAU,GAAGD,cAAc,CAAC;SAE9D,CAAC;MACN;MACA,IAAIG,MAAM,GAAGD,YAAY,CAAC5C,MAAM,GAAG,CAAC;MACpC,MAAM8C,OAAO,GAAG3B,KAAK,CAAC,CAAC,CAAC;MACxBxB,MAAM,CAACgC,IAAI,CAAC;QACRC,IAAI,EAAE,YAAY;QAClBpB,OAAO,EAAEsC,OAAO;QAChBzD,KAAK,EAAEV,KAAK,CAACW,MAAM,CACfZ,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGH,cAAc,CAAC,EAC/DhE,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGC,OAAO,CAAC9C,MAAM,GAAG0C,cAAc,CAAC;OAEvF,CAAC;MACFG,MAAM,IAAIC,OAAO,CAAC9C,MAAM;MACxB,IAAImB,KAAK,CAACnB,MAAM,KAAK,CAAC,EAAE;QACpB6C,MAAM,IAAI1B,KAAK,CAAC,CAAC,CAAC,CAACnB,MAAM;QACzB,MAAMgC,KAAK,GAAGb,KAAK,CAAC,CAAC,CAAC;QACtBxB,MAAM,CAACgC,IAAI,CAAC;UACRC,IAAI,EAAE,MAAM;UACZpB,OAAO,EAAEwB,KAAK;UACd3C,KAAK,EAAEV,KAAK,CAACW,MAAM,CACfZ,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGH,cAAc,CAAC,EAC/DhE,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGb,KAAK,CAAChC,MAAM,GAAG0C,cAAc,CAAC;SAErF,CAAC;MACN,CAAC,MAAM;QACH/C,MAAM,CAACgC,IAAI,CAAC;UACRC,IAAI,EAAE,MAAM;UACZpB,OAAO,EAAE,EAAE;UACXnB,KAAK,EAAEV,KAAK,CAACW,MAAM,CACfZ,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGH,cAAc,CAAC,EAC/DhE,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGgB,MAAM,GAAGH,cAAc,CAAC;SAEtE,CAAC;MACN;MACAb,SAAS,GAAGc,UAAU,GAAGxB,KAAK,CAAC,CAAC,CAAC,CAACnB,MAAM;IAC5C;IACA,MAAM+C,UAAU,GAAGhC,IAAI,CAACQ,SAAS,CAACM,SAAS,CAAC;IAC5C,IAAIkB,UAAU,CAAC/C,MAAM,GAAG,CAAC,EAAE;MACvBL,MAAM,CAACgC,IAAI,CAAC;QACRC,IAAI,EAAE,MAAM;QACZpB,OAAO,EAAEuC,UAAU;QACnB1D,KAAK,EAAEV,KAAK,CAACW,MAAM,CACfZ,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGa,cAAc,CAAC,EACtDhE,QAAQ,CAACY,MAAM,CAACmD,SAAS,EAAEZ,SAAS,GAAGa,cAAc,GAAGK,UAAU,CAAC/C,MAAM,CAAC;OAEjF,CAAC;IACN;EACJ;EAEA,OAAOL,MAAM;AACjB;AAEA,MAAMqD,kBAAkB,GAAG,IAAI;AAC/B,MAAMC,kBAAkB,GAAG,MAAM;AAEjC,SAASvB,cAAcA,CAACX,IAAY,EAAEjB,KAAa;EAC/C,MAAMqB,KAAK,GAAGJ,IAAI,CAACQ,SAAS,CAACzB,KAAK,CAAC,CAACqB,KAAK,CAAC6B,kBAAkB,CAAC;EAC7D,IAAI7B,KAAK,EAAE;IACP,OAAOrB,KAAK,GAAGqB,KAAK,CAACrB,KAAM;EAC/B,CAAC,MAAM;IACH,OAAOiB,IAAI,CAACf,MAAM;EACtB;AACJ;AAEA,SAASwB,aAAaA,CAACT,IAAY;EAC/B,MAAMI,KAAK,GAAGJ,IAAI,CAACI,KAAK,CAAC8B,kBAAkB,CAAC;EAC5C,IAAI9B,KAAK,IAAI,OAAOA,KAAK,CAACrB,KAAK,KAAK,QAAQ,EAAE;IAC1C,OAAOqB,KAAK,CAACrB,KAAK;EACtB;EACA,OAAOoD,SAAS;AACpB;AAEA;AAEA,SAASrD,iBAAiBA,CAACgB,OAAqB;;EAC5C,MAAMsC,aAAa,GAAazE,QAAQ,CAACY,MAAM,CAACuB,OAAO,CAACzB,QAAQ,CAAC2B,IAAI,EAAEF,OAAO,CAACzB,QAAQ,CAAC6B,SAAS,CAAC;EAClG,IAAIJ,OAAO,CAAClB,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;IAC7B,OAAO,IAAIoD,gBAAgB,CAAC,EAAE,EAAEzE,KAAK,CAACW,MAAM,CAAC6D,aAAa,EAAEA,aAAa,CAAC,CAAC;EAC/E;EACA,MAAME,QAAQ,GAAmB,EAAE;EACnC,OAAOxC,OAAO,CAACf,KAAK,GAAGe,OAAO,CAAClB,MAAM,CAACK,MAAM,EAAE;IAC1C,MAAMsD,OAAO,GAAGC,iBAAiB,CAAC1C,OAAO,EAAEwC,QAAQ,CAACA,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC,CAAC;IACzE,IAAIsD,OAAO,EAAE;MACTD,QAAQ,CAAC1B,IAAI,CAAC2B,OAAO,CAAC;IAC1B;EACJ;EACA,MAAMrE,KAAK,GAAG,CAAAoC,EAAA,IAAAD,EAAA,GAAAiC,QAAQ,CAAC,CAAC,CAAC,cAAAjC,EAAA,uBAAAA,EAAA,CAAE/B,KAAK,CAACJ,KAAK,cAAAoC,EAAA,cAAAA,EAAA,GAAI8B,aAAa;EACvD,MAAM9C,GAAG,GAAG,CAAAmD,EAAA,IAAAlC,EAAA,GAAA+B,QAAQ,CAACA,QAAQ,CAACrD,MAAM,GAAG,CAAC,CAAC,cAAAsB,EAAA,uBAAAA,EAAA,CAAEjC,KAAK,CAACgB,GAAG,cAAAmD,EAAA,cAAAA,EAAA,GAAIL,aAAa;EACrE,OAAO,IAAIC,gBAAgB,CAACC,QAAQ,EAAE1E,KAAK,CAACW,MAAM,CAACL,KAAK,EAAEoB,GAAG,CAAC,CAAC;AACnE;AAEA,SAASkD,iBAAiBA,CAAC1C,OAAqB,EAAEX,IAAmB;EACjE,MAAMuD,IAAI,GAAG5C,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,CAAC;EAC1C,IAAI2D,IAAI,CAAC7B,IAAI,KAAK,KAAK,EAAE;IACrB,OAAO8B,aAAa,CAAC7C,OAAO,EAAE,KAAK,CAAC;EACxC,CAAC,MAAM,IAAI4C,IAAI,CAAC7B,IAAI,KAAK,MAAM,IAAI6B,IAAI,CAAC7B,IAAI,KAAK,YAAY,EAAE;IAC3D,OAAO+B,cAAc,CAAC9C,OAAO,CAAC;EAClC,CAAC,MAAM;IACH+C,eAAe,CAACH,IAAI,EAAEvD,IAAI,CAAC;IAC3BW,OAAO,CAACf,KAAK,EAAE;IACf,OAAOoD,SAAS;EACpB;AACJ;AAEA,SAASU,eAAeA,CAACC,KAAiB,EAAEP,OAAsB;EAC9D,IAAIA,OAAO,EAAE;IACT,MAAMvC,IAAI,GAAG,IAAI+C,aAAa,CAAC,EAAE,EAAED,KAAK,CAACxE,KAAK,CAAC;IAC/C,IAAI,SAAS,IAAIiE,OAAO,EAAE;MACtBA,OAAO,CAACS,OAAO,CAACpC,IAAI,CAACZ,IAAI,CAAC;IAC9B,CAAC,MAAM;MACHuC,OAAO,CAAC9C,OAAO,CAACuD,OAAO,CAACpC,IAAI,CAACZ,IAAI,CAAC;IACtC;EACJ;AACJ;AAEA,SAAS4C,cAAcA,CAAC9C,OAAqB;EACzC,IAAIgD,KAAK,GAAGhD,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,CAAC;EACzC,MAAMkE,UAAU,GAAGH,KAAK;EACxB,IAAII,SAAS,GAAGJ,KAAK;EACrB,MAAMtE,KAAK,GAAkB,EAAE;EAC/B,OAAOsE,KAAK,IAAIA,KAAK,CAACjC,IAAI,KAAK,OAAO,IAAIiC,KAAK,CAACjC,IAAI,KAAK,KAAK,EAAE;IAC5DrC,KAAK,CAACoC,IAAI,CAACuC,gBAAgB,CAACrD,OAAO,CAAC,CAAC;IACrCoD,SAAS,GAAGJ,KAAK;IACjBA,KAAK,GAAGhD,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,CAAC;EACzC;EACA,OAAO,IAAIqE,aAAa,CAAC5E,KAAK,EAAEZ,KAAK,CAACW,MAAM,CAAC0E,UAAU,CAAC3E,KAAK,CAACJ,KAAK,EAAEgF,SAAS,CAAC5E,KAAK,CAACgB,GAAG,CAAC,CAAC;AAC9F;AAEA,SAAS6D,gBAAgBA,CAACrD,OAAqB;EAC3C,MAAMgD,KAAK,GAAGhD,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,CAAC;EAC3C,IAAI+D,KAAK,CAACjC,IAAI,KAAK,YAAY,EAAE;IAC7B,OAAO8B,aAAa,CAAC7C,OAAO,EAAE,IAAI,CAAC;EACvC,CAAC,MAAM;IACH,OAAOuD,cAAc,CAACvD,OAAO,CAAC;EAClC;AACJ;AAEA,SAAS6C,aAAaA,CAAC7C,OAAqB,EAAEwD,MAAe;EACzD,MAAMC,QAAQ,GAAGzD,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,EAAE,CAAC;EAChD,MAAMyE,IAAI,GAAGD,QAAQ,CAAC9D,OAAO,CAACe,SAAS,CAAC,CAAC,CAAC;EAC1C,MAAMiD,SAAS,GAAG3D,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,CAAC;EAC/C,IAAI,CAAA0E,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5C,IAAI,MAAK,MAAM,EAAE;IAC5B,IAAIyC,MAAM,EAAE;MACR,MAAMI,OAAO,GAAGL,cAAc,CAACvD,OAAO,CAAC;MACvC,OAAO,IAAI6D,YAAY,CACnBH,IAAI,EACJ,IAAIJ,aAAa,CAAC,CAACM,OAAO,CAAC,EAAEA,OAAO,CAACpF,KAAK,CAAC,EAC3CgF,MAAM,EACN1F,KAAK,CAACW,MAAM,CAACgF,QAAQ,CAACjF,KAAK,CAACJ,KAAK,EAAEwF,OAAO,CAACpF,KAAK,CAACgB,GAAG,CAAC,CACxD;IACL,CAAC,MAAM;MACH,MAAMsE,OAAO,GAAGhB,cAAc,CAAC9C,OAAO,CAAC;MACvC,OAAO,IAAI6D,YAAY,CACnBH,IAAI,EACJI,OAAO,EACPN,MAAM,EACN1F,KAAK,CAACW,MAAM,CAACgF,QAAQ,CAACjF,KAAK,CAACJ,KAAK,EAAE0F,OAAO,CAACtF,KAAK,CAACgB,GAAG,CAAC,CACxD;IACL;EACJ,CAAC,MAAM;IACH,MAAMhB,KAAK,GAAGiF,QAAQ,CAACjF,KAAK;IAC5B,OAAO,IAAIqF,YAAY,CAACH,IAAI,EAAE,IAAIJ,aAAa,CAAC,EAAE,EAAE9E,KAAK,CAAC,EAAEgF,MAAM,EAAEhF,KAAK,CAAC;EAC9E;AACJ;AAEA,SAAS+E,cAAcA,CAACvD,OAAqB;EACzC,MAAMgD,KAAK,GAAGhD,OAAO,CAAClB,MAAM,CAACkB,OAAO,CAACf,KAAK,EAAE,CAAC;EAC7C,OAAO,IAAIgE,aAAa,CAACD,KAAK,CAACrD,OAAO,EAAEqD,KAAK,CAACxE,KAAK,CAAC;AACxD;AAoBA,SAASK,gBAAgBA,CAACR,OAA2B;EACjD,IAAI,CAACA,OAAO,EAAE;IACV,OAAOQ,gBAAgB,CAAC;MACpBT,KAAK,EAAE,KAAK;MACZoB,GAAG,EAAE,IAAI;MACTU,IAAI,EAAE;KACT,CAAC;EACN;EACA,MAAM;IAAE9B,KAAK;IAAEoB,GAAG;IAAEU;EAAI,CAAE,GAAG7B,OAAO;EACpC,OAAO;IACHD,KAAK,EAAE2F,eAAe,CAAC3F,KAAK,EAAE,IAAI,CAAC;IACnCoB,GAAG,EAAEuE,eAAe,CAACvE,GAAG,EAAE,KAAK,CAAC;IAChCU,IAAI,EAAE6D,eAAe,CAAC7D,IAAI,EAAE,IAAI;GACnC;AACL;AAEA,SAAS6D,eAAeA,CAACC,MAAmC,EAAE5F,KAAc;EACxE,IAAI,OAAO4F,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC1D,MAAMC,OAAO,GAAG,OAAOD,MAAM,KAAK,QAAQ,GAAGhG,YAAY,CAACgG,MAAM,CAAC,GAAGA,MAAM,CAACE,MAAM;IACjF,IAAI9F,KAAK,EAAE;MACP,OAAO,IAAI+F,MAAM,CAAC,QAAQF,OAAO,EAAE,CAAC;IACxC,CAAC,MAAM;MACH,OAAO,IAAIE,MAAM,CAAC,OAAOF,OAAO,OAAO,CAAC;IAC5C;EACJ,CAAC,MAAM;IACH,OAAOD,MAAM;EACjB;AACJ;AAEA,MAAMzB,gBAAgB;EAKlB6B,YAAY5B,QAAwB,EAAEhE,KAAY;IAC9C,IAAI,CAACgE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChE,KAAK,GAAGA,KAAK;EACtB;EAEA6F,MAAMA,CAACX,IAAY;IACf,OAAO,IAAI,CAACY,UAAU,EAAE,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,IAAI,KAAKA,IAAI,CAAC;EACvD;EAEAe,OAAOA,CAACf,IAAY;IAChB,OAAO,IAAI,CAACY,UAAU,EAAE,CAACI,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACd,IAAI,KAAKA,IAAI,CAAC;EACzD;EAEQY,UAAUA,CAAA;IACd,OAAO,IAAI,CAAC9B,QAAQ,CAACkC,MAAM,CAAEF,CAAC,IAAoB,MAAM,IAAIA,CAAC,CAAC;EAClE;EAEAG,QAAQA,CAAA;IACJ,IAAIxD,KAAK,GAAG,EAAE;IACd,KAAK,MAAMsB,OAAO,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjC,IAAIrB,KAAK,CAAChC,MAAM,KAAK,CAAC,EAAE;QACpBgC,KAAK,GAAGsB,OAAO,CAACkC,QAAQ,EAAE;MAC9B,CAAC,MAAM;QACH,MAAM/E,IAAI,GAAG6C,OAAO,CAACkC,QAAQ,EAAE;QAC/BxD,KAAK,IAAIyD,YAAY,CAACzD,KAAK,CAAC,GAAGvB,IAAI;MACvC;IACJ;IACA,OAAOuB,KAAK,CAAC0D,IAAI,EAAE;EACvB;EAEAC,UAAUA,CAACzG,OAA4B;IACnC,IAAI8C,KAAK,GAAG,EAAE;IACd,KAAK,MAAMsB,OAAO,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjC,IAAIrB,KAAK,CAAChC,MAAM,KAAK,CAAC,EAAE;QACpBgC,KAAK,GAAGsB,OAAO,CAACqC,UAAU,CAACzG,OAAO,CAAC;MACvC,CAAC,MAAM;QACH,MAAMuB,IAAI,GAAG6C,OAAO,CAACqC,UAAU,CAACzG,OAAO,CAAC;QACxC8C,KAAK,IAAIyD,YAAY,CAACzD,KAAK,CAAC,GAAGvB,IAAI;MACvC;IACJ;IACA,OAAOuB,KAAK,CAAC0D,IAAI,EAAE;EACvB;;AAGJ,MAAMhB,YAAY;EAMdO,YAAYV,IAAY,EAAE/D,OAAuB,EAAE6D,MAAe,EAAEhF,KAAY;IAC5E,IAAI,CAACkF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC/D,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAChF,KAAK,GAAGA,KAAK;EACtB;EAEAmG,QAAQA,CAAA;IACJ,IAAI/E,IAAI,GAAG,IAAI,IAAI,CAAC8D,IAAI,EAAE;IAC1B,MAAM/D,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgF,QAAQ,EAAE;IACvC,IAAI,IAAI,CAAChF,OAAO,CAACuD,OAAO,CAAC/D,MAAM,KAAK,CAAC,EAAE;MACnCS,IAAI,GAAG,GAAGA,IAAI,IAAID,OAAO,EAAE;IAC/B,CAAC,MAAM,IAAI,IAAI,CAACA,OAAO,CAACuD,OAAO,CAAC/D,MAAM,GAAG,CAAC,EAAE;MACxCS,IAAI,GAAG,GAAGA,IAAI,KAAKD,OAAO,EAAE;IAChC;IACA,IAAI,IAAI,CAAC6D,MAAM,EAAE;MACb;MACA,OAAO,IAAI5D,IAAI,GAAG;IACtB,CAAC,MAAM;MACH,OAAOA,IAAI;IACf;EACJ;EAEAkF,UAAUA,CAACzG,OAA4B;;IACnC,OAAO,CAAAmC,EAAA,IAAAD,EAAA,GAAAlC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0G,SAAS,cAAAxE,EAAA,uBAAAA,EAAA,CAAAyE,IAAA,CAAA3G,OAAA,EAAG,IAAI,CAAC,cAAAmC,EAAA,cAAAA,EAAA,GAAI,IAAI,CAACyE,iBAAiB,CAAC5G,OAAO,CAAC;EACxE;EAEQ4G,iBAAiBA,CAAC5G,OAA4B;IAClD,MAAMsB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACmF,UAAU,CAACzG,OAAO,CAAC;IAChD,IAAI,IAAI,CAACmF,MAAM,EAAE;MACb,MAAM0B,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAACzB,IAAI,EAAE/D,OAAO,EAAEtB,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,EAAE,CAAC;MACnE,IAAI,OAAO6G,QAAQ,KAAK,QAAQ,EAAE;QAC9B,OAAOA,QAAQ;MACnB;IACJ;IACA,IAAIE,MAAM,GAAG,EAAE;IACf,IAAI,CAAA/G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgH,GAAG,MAAK,QAAQ,IAAI,CAAAhH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgH,GAAG,MAAKhD,SAAS,EAAE;MACzD+C,MAAM,GAAG,GAAG;IAChB,CAAC,MAAM,IAAI,CAAA/G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgH,GAAG,MAAK,MAAM,EAAE;MAChCD,MAAM,GAAG,IAAI;IACjB,CAAC,MAAM,IAAI,CAAA/G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgH,GAAG,MAAK,aAAa,EAAE;MACvCD,MAAM,GAAG,KAAK;IAClB;IACA,IAAIxF,IAAI,GAAG,GAAGwF,MAAM,IAAI,IAAI,CAAC1B,IAAI,GAAG0B,MAAM,EAAE;IAC5C,IAAI,IAAI,CAACzF,OAAO,CAACuD,OAAO,CAAC/D,MAAM,KAAK,CAAC,EAAE;MACnCS,IAAI,GAAG,GAAGA,IAAI,MAAMD,OAAO,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAACA,OAAO,CAACuD,OAAO,CAAC/D,MAAM,GAAG,CAAC,EAAE;MACxCS,IAAI,GAAG,GAAGA,IAAI,KAAKD,OAAO,EAAE;IAChC;IACA,IAAI,IAAI,CAAC6D,MAAM,EAAE;MACb;MACA,OAAO,IAAI5D,IAAI,GAAG;IACtB,CAAC,MAAM;MACH,OAAOA,IAAI;IACf;EACJ;;AAGJ,SAASuF,eAAeA,CAACE,GAAW,EAAE1F,OAAe,EAAEtB,OAA2B;;EAC9E,IAAIgH,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,MAAM,EAAE;IAC7D,MAAMpG,KAAK,GAAGU,OAAO,CAAC2F,OAAO,CAAC,GAAG,CAAC;IAClC,IAAIC,OAAO,GAAG5F,OAAO;IACrB,IAAIV,KAAK,GAAG,CAAC,EAAE;MACX,MAAMuG,YAAY,GAAG3E,cAAc,CAAClB,OAAO,EAAEV,KAAK,CAAC;MACnDsG,OAAO,GAAG5F,OAAO,CAACe,SAAS,CAAC8E,YAAY,CAAC;MACzC7F,OAAO,GAAGA,OAAO,CAACe,SAAS,CAAC,CAAC,EAAEzB,KAAK,CAAC;IACzC;IACA,IAAIoG,GAAG,KAAK,UAAU,IAAKA,GAAG,KAAK,MAAM,IAAIhH,OAAO,CAACoH,IAAI,KAAK,MAAO,EAAE;MACnE;MACAF,OAAO,GAAG,KAAKA,OAAO,IAAI;IAC9B;IACA,MAAMG,YAAY,GAAG,CAAAlF,EAAA,IAAAD,EAAA,GAAAlC,OAAO,CAACsH,UAAU,cAAApF,EAAA,uBAAAA,EAAA,CAAAyE,IAAA,CAAA3G,OAAA,EAAGsB,OAAO,EAAE4F,OAAO,CAAC,cAAA/E,EAAA,cAAAA,EAAA,GAAIoF,iBAAiB,CAACjG,OAAO,EAAE4F,OAAO,CAAC;IAClG,OAAOG,YAAY;EACvB;EACA,OAAOrD,SAAS;AACpB;AAEA,SAASuD,iBAAiBA,CAACjG,OAAe,EAAE4F,OAAe;EACvD,IAAI;IACAtH,GAAG,CAAC4H,KAAK,CAAClG,OAAO,EAAE,IAAI,CAAC;IACxB,OAAO,IAAI4F,OAAO,KAAK5F,OAAO,GAAG;EACrC,CAAC,CAAC,OAAAY,EAAA,EAAM;IACJ,OAAOZ,OAAO;EAClB;AACJ;AAEA,MAAM2D,aAAa;EAIfc,YAAY1F,KAAoB,EAAEF,KAAY;IAC1C,IAAI,CAAC0E,OAAO,GAAGxE,KAAK;IACpB,IAAI,CAACF,KAAK,GAAGA,KAAK;EACtB;EAEAmG,QAAQA,CAAA;IACJ,IAAI/E,IAAI,GAAG,EAAE;IACb,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6C,OAAO,CAAC/D,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC1C,MAAMmD,MAAM,GAAG,IAAI,CAACN,OAAO,CAAC7C,CAAC,CAAC;MAC9B,MAAMuC,IAAI,GAAG,IAAI,CAACM,OAAO,CAAC7C,CAAC,GAAG,CAAC,CAAC;MAChCT,IAAI,IAAI4D,MAAM,CAACmB,QAAQ,EAAE;MACzB,IAAI/B,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAACJ,KAAK,CAAC8B,IAAI,GAAGsD,MAAM,CAAChF,KAAK,CAACJ,KAAK,CAAC8B,IAAI,EAAE;QACzDN,IAAI,IAAI,IAAI;MAChB;IACJ;IACA,OAAOA,IAAI;EACf;EAEAkF,UAAUA,CAACzG,OAA4B;IACnC,IAAIuB,IAAI,GAAG,EAAE;IACb,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6C,OAAO,CAAC/D,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC1C,MAAMmD,MAAM,GAAG,IAAI,CAACN,OAAO,CAAC7C,CAAC,CAAC;MAC9B,MAAMuC,IAAI,GAAG,IAAI,CAACM,OAAO,CAAC7C,CAAC,GAAG,CAAC,CAAC;MAChCT,IAAI,IAAI4D,MAAM,CAACsB,UAAU,CAACzG,OAAO,CAAC;MAClC,IAAIuE,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAACJ,KAAK,CAAC8B,IAAI,GAAGsD,MAAM,CAAChF,KAAK,CAACJ,KAAK,CAAC8B,IAAI,EAAE;QACzDN,IAAI,IAAI,IAAI;MAChB;IACJ;IACA,OAAOA,IAAI;EACf;;AAGJ,MAAMqD,aAAa;EAIfmB,YAAYxE,IAAY,EAAEpB,KAAY;IAClC,IAAI,CAACoB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACpB,KAAK,GAAGA,KAAK;EACtB;EAEAmG,QAAQA,CAAA;IACJ,OAAO,IAAI,CAAC/E,IAAI;EACpB;EACAkF,UAAUA,CAAA;IACN,OAAO,IAAI,CAAClF,IAAI;EACpB;;AAIJ,SAASgF,YAAYA,CAAChF,IAAY;EAC9B,IAAIA,IAAI,CAACkG,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrB,OAAO,IAAI;EACf,CAAC,MAAM;IACH,OAAO,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}