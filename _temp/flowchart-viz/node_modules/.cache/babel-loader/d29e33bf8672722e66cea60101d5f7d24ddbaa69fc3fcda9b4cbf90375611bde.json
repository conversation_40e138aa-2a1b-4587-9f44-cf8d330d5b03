{"ast": null, "code": "import { atan, exp, halfPi, log, pi, tan, tau } from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\nmercatorRaw.invert = function (x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\nexport default function () {\n  return mercatorProjection(mercatorRaw).scale(961 / tau);\n}\nexport function mercatorProjection(project) {\n  var m = projection(project),\n    center = m.center,\n    scale = m.scale,\n    translate = m.translate,\n    clipExtent = m.clipExtent,\n    x0 = null,\n    y0,\n    x1,\n    y1; // clip extent\n\n  m.scale = function (_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n  m.translate = function (_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n  m.center = function (_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n  m.clipExtent = function (_) {\n    return arguments.length ? (_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  function reclip() {\n    var k = pi * scale(),\n      t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]] : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n  return reclip();\n}", "map": {"version": 3, "names": ["atan", "exp", "halfPi", "log", "pi", "tan", "tau", "rotation", "projection", "mercatorRaw", "lambda", "phi", "invert", "x", "y", "mercatorProjection", "scale", "project", "m", "center", "translate", "clipExtent", "x0", "y0", "x1", "y1", "_", "arguments", "length", "reclip", "k", "t", "rotate", "Math", "max", "min"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/mercator.js"], "sourcesContent": ["import {atan, exp, halfPi, log, pi, tan, tau} from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\n\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\n\nexport default function() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / tau);\n}\n\nexport function mercatorProjection(project) {\n  var m = projection(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = pi * scale(),\n        t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,QAAO,YAAY;AAC/D,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACvC,OAAO,CAACD,MAAM,EAAEP,GAAG,CAACE,GAAG,CAAC,CAACH,MAAM,GAAGS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/C;AAEAF,WAAW,CAACG,MAAM,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAE;EAClC,OAAO,CAACD,CAAC,EAAE,CAAC,GAAGb,IAAI,CAACC,GAAG,CAACa,CAAC,CAAC,CAAC,GAAGZ,MAAM,CAAC;AACvC,CAAC;AAED,eAAe,YAAW;EACxB,OAAOa,kBAAkB,CAACN,WAAW,CAAC,CACjCO,KAAK,CAAC,GAAG,GAAGV,GAAG,CAAC;AACvB;AAEA,OAAO,SAASS,kBAAkBA,CAACE,OAAO,EAAE;EAC1C,IAAIC,CAAC,GAAGV,UAAU,CAACS,OAAO,CAAC;IACvBE,MAAM,GAAGD,CAAC,CAACC,MAAM;IACjBH,KAAK,GAAGE,CAAC,CAACF,KAAK;IACfI,SAAS,GAAGF,CAAC,CAACE,SAAS;IACvBC,UAAU,GAAGH,CAAC,CAACG,UAAU;IACzBC,EAAE,GAAG,IAAI;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE,CAAC,CAAC;;EAE3BP,CAAC,CAACF,KAAK,GAAG,UAASU,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACC,MAAM,IAAIZ,KAAK,CAACU,CAAC,CAAC,EAAEG,MAAM,CAAC,CAAC,IAAIb,KAAK,CAAC,CAAC;EAC1D,CAAC;EAEDE,CAAC,CAACE,SAAS,GAAG,UAASM,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIR,SAAS,CAACM,CAAC,CAAC,EAAEG,MAAM,CAAC,CAAC,IAAIT,SAAS,CAAC,CAAC;EAClE,CAAC;EAEDF,CAAC,CAACC,MAAM,GAAG,UAASO,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACC,MAAM,IAAIT,MAAM,CAACO,CAAC,CAAC,EAAEG,MAAM,CAAC,CAAC,IAAIV,MAAM,CAAC,CAAC;EAC5D,CAAC;EAEDD,CAAC,CAACG,UAAU,GAAG,UAASK,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAKF,CAAC,IAAI,IAAI,GAAGJ,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,IAAI,IAAIH,EAAE,GAAG,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEH,EAAE,GAAG,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,EAAE,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,EAAE,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAGG,MAAM,CAAC,CAAC,IAAIP,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAACA,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;EACxL,CAAC;EAED,SAASI,MAAMA,CAAA,EAAG;IAChB,IAAIC,CAAC,GAAG1B,EAAE,GAAGY,KAAK,CAAC,CAAC;MAChBe,CAAC,GAAGb,CAAC,CAACX,QAAQ,CAACW,CAAC,CAACc,MAAM,CAAC,CAAC,CAAC,CAACpB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAOS,UAAU,CAACC,EAAE,IAAI,IAAI,GACtB,CAAC,CAACS,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,GAAGb,OAAO,KAAKR,WAAW,GACtE,CAAC,CAACwB,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAER,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAACU,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAEN,EAAE,CAAC,EAAEC,EAAE,CAAC,CAAC,GAC5D,CAAC,CAACH,EAAE,EAAEW,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAEP,EAAE,CAAC,CAAC,EAAE,CAACC,EAAE,EAAES,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAEL,EAAE,CAAC,CAAC,CAAC,CAAC;EACrE;EAEA,OAAOI,MAAM,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}