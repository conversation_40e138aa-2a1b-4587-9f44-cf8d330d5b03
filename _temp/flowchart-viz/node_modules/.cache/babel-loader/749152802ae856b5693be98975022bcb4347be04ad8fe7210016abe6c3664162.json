{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { Disposable } from '../utils/disposable.js';\nimport { MultiMap } from '../utils/collections.js';\nimport { OperationCancelled, interruptAndCheck, isOperationCancelled } from '../utils/promise-utils.js';\nimport { stream } from '../utils/stream.js';\nimport { ValidationCategory } from '../validation/validation-registry.js';\nimport { DocumentState } from './documents.js';\nexport class DefaultDocumentBuilder {\n  constructor(services) {\n    this.updateBuildOptions = {\n      // Default: run only the built-in validation checks and those in the _fast_ category (includes those without category)\n      validation: {\n        categories: ['built-in', 'fast']\n      }\n    };\n    this.updateListeners = [];\n    this.buildPhaseListeners = new MultiMap();\n    this.documentPhaseListeners = new MultiMap();\n    this.buildState = new Map();\n    this.documentBuildWaiters = new Map();\n    this.currentState = DocumentState.Changed;\n    this.langiumDocuments = services.workspace.LangiumDocuments;\n    this.langiumDocumentFactory = services.workspace.LangiumDocumentFactory;\n    this.textDocuments = services.workspace.TextDocuments;\n    this.indexManager = services.workspace.IndexManager;\n    this.serviceRegistry = services.ServiceRegistry;\n  }\n  async build(documents, options = {}, cancelToken = CancellationToken.None) {\n    var _a, _b;\n    for (const document of documents) {\n      const key = document.uri.toString();\n      if (document.state === DocumentState.Validated) {\n        if (typeof options.validation === 'boolean' && options.validation) {\n          // Force re-running all validation checks\n          document.state = DocumentState.IndexedReferences;\n          document.diagnostics = undefined;\n          this.buildState.delete(key);\n        } else if (typeof options.validation === 'object') {\n          const buildState = this.buildState.get(key);\n          const previousCategories = (_a = buildState === null || buildState === void 0 ? void 0 : buildState.result) === null || _a === void 0 ? void 0 : _a.validationChecks;\n          if (previousCategories) {\n            // Validation with explicit options was requested for a document that has already been partly validated.\n            // In this case, we need to merge the previous validation categories with the new ones.\n            const newCategories = (_b = options.validation.categories) !== null && _b !== void 0 ? _b : ValidationCategory.all;\n            const categories = newCategories.filter(c => !previousCategories.includes(c));\n            if (categories.length > 0) {\n              this.buildState.set(key, {\n                completed: false,\n                options: {\n                  validation: Object.assign(Object.assign({}, options.validation), {\n                    categories\n                  })\n                },\n                result: buildState.result\n              });\n              document.state = DocumentState.IndexedReferences;\n            }\n          }\n        }\n      } else {\n        // Default: forget any previous build options\n        this.buildState.delete(key);\n      }\n    }\n    this.currentState = DocumentState.Changed;\n    await this.emitUpdate(documents.map(e => e.uri), []);\n    await this.buildDocuments(documents, options, cancelToken);\n  }\n  async update(changed, deleted, cancelToken = CancellationToken.None) {\n    this.currentState = DocumentState.Changed;\n    // Remove all metadata of documents that are reported as deleted\n    for (const deletedUri of deleted) {\n      this.langiumDocuments.deleteDocument(deletedUri);\n      this.buildState.delete(deletedUri.toString());\n      this.indexManager.remove(deletedUri);\n    }\n    // Set the state of all changed documents to `Changed` so they are completely rebuilt\n    for (const changedUri of changed) {\n      const invalidated = this.langiumDocuments.invalidateDocument(changedUri);\n      if (!invalidated) {\n        // We create an unparsed, invalid document.\n        // This will be parsed as soon as we reach the first document builder phase.\n        // This allows to cancel the parsing process later in case we need it.\n        const newDocument = this.langiumDocumentFactory.fromModel({\n          $type: 'INVALID'\n        }, changedUri);\n        newDocument.state = DocumentState.Changed;\n        this.langiumDocuments.addDocument(newDocument);\n      }\n      this.buildState.delete(changedUri.toString());\n    }\n    // Set the state of all documents that should be relinked to `ComputedScopes` (if not already lower)\n    const allChangedUris = stream(changed).concat(deleted).map(uri => uri.toString()).toSet();\n    this.langiumDocuments.all.filter(doc => !allChangedUris.has(doc.uri.toString()) && this.shouldRelink(doc, allChangedUris)).forEach(doc => {\n      const linker = this.serviceRegistry.getServices(doc.uri).references.Linker;\n      linker.unlink(doc);\n      doc.state = Math.min(doc.state, DocumentState.ComputedScopes);\n      doc.diagnostics = undefined;\n    });\n    // Notify listeners of the update\n    await this.emitUpdate(changed, deleted);\n    // Only allow interrupting the execution after all state changes are done\n    await interruptAndCheck(cancelToken);\n    // Collect and sort all documents that we should rebuild\n    const rebuildDocuments = this.sortDocuments(this.langiumDocuments.all.filter(doc => {\n      var _a;\n      // This includes those that were reported as changed and those that we selected for relinking\n      return doc.state < DocumentState.Linked\n      // This includes those for which a previous build has been cancelled\n      || !((_a = this.buildState.get(doc.uri.toString())) === null || _a === void 0 ? void 0 : _a.completed);\n    }).toArray());\n    await this.buildDocuments(rebuildDocuments, this.updateBuildOptions, cancelToken);\n  }\n  async emitUpdate(changed, deleted) {\n    await Promise.all(this.updateListeners.map(listener => listener(changed, deleted)));\n  }\n  /**\n   * Sort the given documents by priority. By default, documents with an open text document are prioritized.\n   * This is useful to ensure that visible documents show their diagnostics before all other documents.\n   *\n   * This improves the responsiveness in large workspaces as users usually don't care about diagnostics\n   * in files that are currently not opened in the editor.\n   */\n  sortDocuments(documents) {\n    let left = 0;\n    let right = documents.length - 1;\n    while (left < right) {\n      while (left < documents.length && this.hasTextDocument(documents[left])) {\n        left++;\n      }\n      while (right >= 0 && !this.hasTextDocument(documents[right])) {\n        right--;\n      }\n      if (left < right) {\n        [documents[left], documents[right]] = [documents[right], documents[left]];\n      }\n    }\n    return documents;\n  }\n  hasTextDocument(doc) {\n    var _a;\n    return Boolean((_a = this.textDocuments) === null || _a === void 0 ? void 0 : _a.get(doc.uri));\n  }\n  /**\n   * Check whether the given document should be relinked after changes were found in the given URIs.\n   */\n  shouldRelink(document, changedUris) {\n    // Relink documents with linking errors -- maybe those references can be resolved now\n    if (document.references.some(ref => ref.error !== undefined)) {\n      return true;\n    }\n    // Check whether the document is affected by any of the changed URIs\n    return this.indexManager.isAffected(document, changedUris);\n  }\n  onUpdate(callback) {\n    this.updateListeners.push(callback);\n    return Disposable.create(() => {\n      const index = this.updateListeners.indexOf(callback);\n      if (index >= 0) {\n        this.updateListeners.splice(index, 1);\n      }\n    });\n  }\n  /**\n   * Build the given documents by stepping through all build phases. If a document's state indicates\n   * that a certain build phase is already done, the phase is skipped for that document.\n   *\n   * @param documents The documents to build.\n   * @param options the {@link BuildOptions} to use.\n   * @param cancelToken A cancellation token that can be used to cancel the build.\n   * @returns A promise that resolves when the build is done.\n   */\n  async buildDocuments(documents, options, cancelToken) {\n    this.prepareBuild(documents, options);\n    // 0. Parse content\n    await this.runCancelable(documents, DocumentState.Parsed, cancelToken, doc => this.langiumDocumentFactory.update(doc, cancelToken));\n    // 1. Index content\n    await this.runCancelable(documents, DocumentState.IndexedContent, cancelToken, doc => this.indexManager.updateContent(doc, cancelToken));\n    // 2. Compute scopes\n    await this.runCancelable(documents, DocumentState.ComputedScopes, cancelToken, async doc => {\n      const scopeComputation = this.serviceRegistry.getServices(doc.uri).references.ScopeComputation;\n      doc.precomputedScopes = await scopeComputation.computeLocalScopes(doc, cancelToken);\n    });\n    // 3. Linking\n    await this.runCancelable(documents, DocumentState.Linked, cancelToken, doc => {\n      const linker = this.serviceRegistry.getServices(doc.uri).references.Linker;\n      return linker.link(doc, cancelToken);\n    });\n    // 4. Index references\n    await this.runCancelable(documents, DocumentState.IndexedReferences, cancelToken, doc => this.indexManager.updateReferences(doc, cancelToken));\n    // 5. Validation\n    const toBeValidated = documents.filter(doc => this.shouldValidate(doc));\n    await this.runCancelable(toBeValidated, DocumentState.Validated, cancelToken, doc => this.validate(doc, cancelToken));\n    // If we've made it to this point without being cancelled, we can mark the build state as completed.\n    for (const doc of documents) {\n      const state = this.buildState.get(doc.uri.toString());\n      if (state) {\n        state.completed = true;\n      }\n    }\n  }\n  /**\n   * Runs prior to beginning the build process to update the {@link DocumentBuildState} for each document\n   *\n   * @param documents collection of documents to be built\n   * @param options the {@link BuildOptions} to use\n   */\n  prepareBuild(documents, options) {\n    for (const doc of documents) {\n      const key = doc.uri.toString();\n      const state = this.buildState.get(key);\n      // If the document has no previous build state, we set it. If it has one, but it's already marked\n      // as completed, we overwrite it. If the previous build was not completed, we keep its state\n      // and continue where it was cancelled.\n      if (!state || state.completed) {\n        this.buildState.set(key, {\n          completed: false,\n          options,\n          result: state === null || state === void 0 ? void 0 : state.result\n        });\n      }\n    }\n  }\n  /**\n   * Runs a cancelable operation on a set of documents to bring them to a specified {@link DocumentState}.\n   *\n   * @param documents The array of documents to process.\n   * @param targetState The target {@link DocumentState} to bring the documents to.\n   * @param cancelToken A token that can be used to cancel the operation.\n   * @param callback A function to be called for each document.\n   * @returns A promise that resolves when all documents have been processed or the operation is canceled.\n   * @throws Will throw `OperationCancelled` if the operation is canceled via a `CancellationToken`.\n   */\n  async runCancelable(documents, targetState, cancelToken, callback) {\n    const filtered = documents.filter(doc => doc.state < targetState);\n    for (const document of filtered) {\n      await interruptAndCheck(cancelToken);\n      await callback(document);\n      document.state = targetState;\n      await this.notifyDocumentPhase(document, targetState, cancelToken);\n    }\n    // Do not use `filtered` here, as that will miss documents that have previously reached the current target state\n    // For example, this happens in case the cancellation triggers between the processing of two documents\n    // Or files that were picked up during the workspace initialization\n    const targetStateDocs = documents.filter(doc => doc.state === targetState);\n    await this.notifyBuildPhase(targetStateDocs, targetState, cancelToken);\n    this.currentState = targetState;\n  }\n  onBuildPhase(targetState, callback) {\n    this.buildPhaseListeners.add(targetState, callback);\n    return Disposable.create(() => {\n      this.buildPhaseListeners.delete(targetState, callback);\n    });\n  }\n  onDocumentPhase(targetState, callback) {\n    this.documentPhaseListeners.add(targetState, callback);\n    return Disposable.create(() => {\n      this.documentPhaseListeners.delete(targetState, callback);\n    });\n  }\n  waitUntil(state, uriOrToken, cancelToken) {\n    let uri = undefined;\n    if (uriOrToken && 'path' in uriOrToken) {\n      uri = uriOrToken;\n    } else {\n      cancelToken = uriOrToken;\n    }\n    cancelToken !== null && cancelToken !== void 0 ? cancelToken : cancelToken = CancellationToken.None;\n    if (uri) {\n      const document = this.langiumDocuments.getDocument(uri);\n      if (document && document.state > state) {\n        return Promise.resolve(uri);\n      }\n    }\n    if (this.currentState >= state) {\n      return Promise.resolve(undefined);\n    } else if (cancelToken.isCancellationRequested) {\n      return Promise.reject(OperationCancelled);\n    }\n    return new Promise((resolve, reject) => {\n      const buildDisposable = this.onBuildPhase(state, () => {\n        buildDisposable.dispose();\n        cancelDisposable.dispose();\n        if (uri) {\n          const document = this.langiumDocuments.getDocument(uri);\n          resolve(document === null || document === void 0 ? void 0 : document.uri);\n        } else {\n          resolve(undefined);\n        }\n      });\n      const cancelDisposable = cancelToken.onCancellationRequested(() => {\n        buildDisposable.dispose();\n        cancelDisposable.dispose();\n        reject(OperationCancelled);\n      });\n    });\n  }\n  async notifyDocumentPhase(document, state, cancelToken) {\n    const listeners = this.documentPhaseListeners.get(state);\n    const listenersCopy = listeners.slice();\n    for (const listener of listenersCopy) {\n      try {\n        await listener(document, cancelToken);\n      } catch (err) {\n        // Ignore cancellation errors\n        // We want to finish the listeners before throwing\n        if (!isOperationCancelled(err)) {\n          throw err;\n        }\n      }\n    }\n  }\n  async notifyBuildPhase(documents, state, cancelToken) {\n    if (documents.length === 0) {\n      // Don't notify when no document has been processed\n      return;\n    }\n    const listeners = this.buildPhaseListeners.get(state);\n    const listenersCopy = listeners.slice();\n    for (const listener of listenersCopy) {\n      await interruptAndCheck(cancelToken);\n      await listener(documents, cancelToken);\n    }\n  }\n  /**\n   * Determine whether the given document should be validated during a build. The default\n   * implementation checks the `validation` property of the build options. If it's set to `true`\n   * or a `ValidationOptions` object, the document is included in the validation phase.\n   */\n  shouldValidate(document) {\n    return Boolean(this.getBuildOptions(document).validation);\n  }\n  /**\n   * Run validation checks on the given document and store the resulting diagnostics in the document.\n   * If the document already contains diagnostics, the new ones are added to the list.\n   */\n  async validate(document, cancelToken) {\n    var _a, _b;\n    const validator = this.serviceRegistry.getServices(document.uri).validation.DocumentValidator;\n    const validationSetting = this.getBuildOptions(document).validation;\n    const options = typeof validationSetting === 'object' ? validationSetting : undefined;\n    const diagnostics = await validator.validateDocument(document, options, cancelToken);\n    if (document.diagnostics) {\n      document.diagnostics.push(...diagnostics);\n    } else {\n      document.diagnostics = diagnostics;\n    }\n    // Store information about the executed validation in the build state\n    const state = this.buildState.get(document.uri.toString());\n    if (state) {\n      (_a = state.result) !== null && _a !== void 0 ? _a : state.result = {};\n      const newCategories = (_b = options === null || options === void 0 ? void 0 : options.categories) !== null && _b !== void 0 ? _b : ValidationCategory.all;\n      if (state.result.validationChecks) {\n        state.result.validationChecks.push(...newCategories);\n      } else {\n        state.result.validationChecks = [...newCategories];\n      }\n    }\n  }\n  getBuildOptions(document) {\n    var _a, _b;\n    return (_b = (_a = this.buildState.get(document.uri.toString())) === null || _a === void 0 ? void 0 : _a.options) !== null && _b !== void 0 ? _b : {};\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "Disposable", "MultiMap", "OperationCancelled", "interruptAndCheck", "isOperationCancelled", "stream", "ValidationCategory", "DocumentState", "DefaultDocumentBuilder", "constructor", "services", "updateBuildOptions", "validation", "categories", "updateListeners", "buildPhaseListeners", "documentPhaseListeners", "buildState", "Map", "documentBuildWaiters", "currentState", "Changed", "langiumDocuments", "workspace", "LangiumDocuments", "langiumDocumentFactory", "LangiumDocumentFactory", "textDocuments", "TextDocuments", "indexManager", "IndexManager", "serviceRegistry", "ServiceRegistry", "build", "documents", "options", "cancelToken", "None", "document", "key", "uri", "toString", "state", "Validated", "IndexedReferences", "diagnostics", "undefined", "delete", "get", "previousCategories", "_a", "result", "validationChecks", "newCategories", "_b", "all", "filter", "c", "includes", "length", "set", "completed", "Object", "assign", "emitUpdate", "map", "e", "buildDocuments", "update", "changed", "deleted", "<PERSON><PERSON><PERSON>", "deleteDocument", "remove", "<PERSON><PERSON><PERSON>", "invalidated", "invalidateDocument", "newDocument", "fromModel", "$type", "addDocument", "allChangedUris", "concat", "toSet", "doc", "has", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "linker", "getServices", "references", "<PERSON><PERSON>", "unlink", "Math", "min", "ComputedScopes", "rebuildDocuments", "sortDocuments", "Linked", "toArray", "Promise", "listener", "left", "right", "hasTextDocument", "Boolean", "<PERSON><PERSON><PERSON>", "some", "ref", "error", "isAffected", "onUpdate", "callback", "push", "create", "index", "indexOf", "splice", "prepareBuild", "runCancelable", "Parsed", "IndexedContent", "updateContent", "scopeComputation", "ScopeComputation", "precomputedScopes", "computeLocalScopes", "link", "updateReferences", "toBeValidated", "shouldValidate", "validate", "targetState", "filtered", "notifyDocumentPhase", "targetStateDocs", "notifyBuildPhase", "onBuildPhase", "add", "onDocumentPhase", "waitUntil", "uriOrToken", "getDocument", "resolve", "isCancellationRequested", "reject", "buildDisposable", "dispose", "cancelDisposable", "onCancellationRequested", "listeners", "listenersCopy", "slice", "err", "getBuildOptions", "validator", "DocumentValidator", "validationSetting", "validateDocument"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/document-builder.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { Disposable } from '../utils/disposable.js';\r\nimport type { ServiceRegistry } from '../service-registry.js';\r\nimport type { LangiumSharedCoreServices } from '../services.js';\r\nimport type { AstNode } from '../syntax-tree.js';\r\nimport type { MaybePromise } from '../utils/promise-utils.js';\r\nimport type { Deferred } from '../utils/promise-utils.js';\r\nimport type { ValidationOptions } from '../validation/document-validator.js';\r\nimport type { IndexManager } from '../workspace/index-manager.js';\r\nimport type { LangiumDocument, LangiumDocuments, LangiumDocumentFactory, TextDocumentProvider } from './documents.js';\r\nimport { MultiMap } from '../utils/collections.js';\r\nimport { OperationCancelled, interruptAndCheck, isOperationCancelled } from '../utils/promise-utils.js';\r\nimport { stream } from '../utils/stream.js';\r\nimport type { URI } from '../utils/uri-utils.js';\r\nimport { ValidationCategory } from '../validation/validation-registry.js';\r\nimport { DocumentState } from './documents.js';\r\n\r\nexport interface BuildOptions {\r\n    /**\r\n     * Control the validation phase with this option:\r\n     *  - `true` enables all validation checks and forces revalidating the documents\r\n     *  - `false` or `undefined` disables all validation checks\r\n     *  - An object runs only the necessary validation checks; the `categories` property restricts this to a specific subset\r\n     */\r\n    validation?: boolean | ValidationOptions\r\n}\r\n\r\nexport interface DocumentBuildState {\r\n    /** Whether a document has completed its last build process. */\r\n    completed: boolean\r\n    /** The options used for the last build process. */\r\n    options: BuildOptions\r\n    /** Additional information about the last build result. */\r\n    result?: {\r\n        validationChecks?: ValidationCategory[]\r\n    }\r\n}\r\n\r\n/**\r\n * Shared-service for building and updating `LangiumDocument`s.\r\n */\r\nexport interface DocumentBuilder {\r\n\r\n    /** The options used for rebuilding documents after an update. */\r\n    updateBuildOptions: BuildOptions;\r\n\r\n    /**\r\n     * Execute all necessary build steps for the given documents.\r\n     *\r\n     * @param documents Set of documents to be built.\r\n     * @param options Options for the document builder.\r\n     * @param cancelToken Indicates when to cancel the current operation.\r\n     * @throws `OperationCanceled` if a user action occurs during execution\r\n     */\r\n    build<T extends AstNode>(documents: Array<LangiumDocument<T>>, options?: BuildOptions, cancelToken?: CancellationToken): Promise<void>;\r\n\r\n    /**\r\n     * This method is called when a document change is detected. It updates the state of all\r\n     * affected documents, including those with references to the changed ones, so they are rebuilt.\r\n     *\r\n     * @param changed URIs of changed or created documents\r\n     * @param deleted URIs of deleted documents\r\n     * @param cancelToken allows to cancel the current operation\r\n     * @throws `OperationCancelled` if cancellation is detected during execution\r\n     */\r\n    update(changed: URI[], deleted: URI[], cancelToken?: CancellationToken): Promise<void>;\r\n\r\n    /**\r\n     * Notify the given callback when a document update was triggered, but before any document\r\n     * is rebuilt. Listeners to this event should not perform any long-running task.\r\n     */\r\n    onUpdate(callback: DocumentUpdateListener): Disposable;\r\n\r\n    /**\r\n     * Notify the given callback when a set of documents has been built reaching the specified target state.\r\n     */\r\n    onBuildPhase(targetState: DocumentState, callback: DocumentBuildListener): Disposable;\r\n\r\n    /**\r\n     * Notify the specified callback when a document has been built reaching the specified target state.\r\n     * Unlike {@link onBuildPhase} the listener is called for every single document.\r\n     *\r\n     * There are two main advantages compared to {@link onBuildPhase}:\r\n     * 1. If the build is cancelled, {@link onDocumentPhase} will still fire for documents that have reached a specific state.\r\n     *    Meanwhile, {@link onBuildPhase} won't fire for that state.\r\n     * 2. The {@link DocumentBuilder} ensures that all {@link DocumentPhaseListener} instances are called for a built document.\r\n     *    Even if the build is cancelled before those listeners were called.\r\n     */\r\n    onDocumentPhase(targetState: DocumentState, callback: DocumentPhaseListener): Disposable;\r\n\r\n    /**\r\n     * Wait until the workspace has reached the specified state for all documents.\r\n     *\r\n     * @param state The desired state. The promise won't resolve until all documents have reached this state\r\n     * @param cancelToken Optionally allows to cancel the wait operation, disposing any listeners in the process\r\n     * @throws `OperationCancelled` if cancellation has been requested before the state has been reached\r\n     */\r\n    waitUntil(state: DocumentState, cancelToken?: CancellationToken): Promise<void>;\r\n\r\n    /**\r\n     * Wait until the document specified by the {@link uri} has reached the specified state.\r\n     *\r\n     * @param state The desired state. The promise won't resolve until the document has reached this state.\r\n     * @param uri The specified URI that points to the document. If the URI does not exist, the promise will resolve once the workspace has reached the specified state.\r\n     * @param cancelToken Optionally allows to cancel the wait operation, disposing any listeners in the process.\r\n     * @return The URI of the document that has reached the desired state, or `undefined` if the document does not exist.\r\n     * @throws `OperationCancelled` if cancellation has been requested before the state has been reached\r\n     */\r\n    waitUntil(state: DocumentState, uri?: URI, cancelToken?: CancellationToken): Promise<URI | undefined>;\r\n}\r\n\r\nexport type DocumentUpdateListener = (changed: URI[], deleted: URI[]) => void | Promise<void>\r\nexport type DocumentBuildListener = (built: LangiumDocument[], cancelToken: CancellationToken) => void | Promise<void>\r\nexport type DocumentPhaseListener = (built: LangiumDocument, cancelToken: CancellationToken) => void | Promise<void>\r\nexport class DefaultDocumentBuilder implements DocumentBuilder {\r\n\r\n    updateBuildOptions: BuildOptions = {\r\n        // Default: run only the built-in validation checks and those in the _fast_ category (includes those without category)\r\n        validation: {\r\n            categories: ['built-in', 'fast']\r\n        }\r\n    };\r\n\r\n    protected readonly langiumDocuments: LangiumDocuments;\r\n    protected readonly langiumDocumentFactory: LangiumDocumentFactory;\r\n    protected readonly textDocuments: TextDocumentProvider | undefined;\r\n    protected readonly indexManager: IndexManager;\r\n    protected readonly serviceRegistry: ServiceRegistry;\r\n    protected readonly updateListeners: DocumentUpdateListener[] = [];\r\n    protected readonly buildPhaseListeners = new MultiMap<DocumentState, DocumentBuildListener>();\r\n    protected readonly documentPhaseListeners = new MultiMap<DocumentState, DocumentPhaseListener>();\r\n    protected readonly buildState = new Map<string, DocumentBuildState>();\r\n    protected readonly documentBuildWaiters = new Map<string, Deferred<void>>();\r\n    protected currentState = DocumentState.Changed;\r\n\r\n    constructor(services: LangiumSharedCoreServices) {\r\n        this.langiumDocuments = services.workspace.LangiumDocuments;\r\n        this.langiumDocumentFactory = services.workspace.LangiumDocumentFactory;\r\n        this.textDocuments = services.workspace.TextDocuments;\r\n        this.indexManager = services.workspace.IndexManager;\r\n        this.serviceRegistry = services.ServiceRegistry;\r\n    }\r\n\r\n    async build<T extends AstNode>(documents: Array<LangiumDocument<T>>, options: BuildOptions = {}, cancelToken = CancellationToken.None): Promise<void> {\r\n        for (const document of documents) {\r\n            const key = document.uri.toString();\r\n            if (document.state === DocumentState.Validated) {\r\n                if (typeof options.validation === 'boolean' && options.validation) {\r\n                    // Force re-running all validation checks\r\n                    document.state = DocumentState.IndexedReferences;\r\n                    document.diagnostics = undefined;\r\n                    this.buildState.delete(key);\r\n                } else if (typeof options.validation === 'object') {\r\n                    const buildState = this.buildState.get(key);\r\n                    const previousCategories = buildState?.result?.validationChecks;\r\n                    if (previousCategories) {\r\n                        // Validation with explicit options was requested for a document that has already been partly validated.\r\n                        // In this case, we need to merge the previous validation categories with the new ones.\r\n                        const newCategories = options.validation.categories ?? ValidationCategory.all as ValidationCategory[];\r\n                        const categories = newCategories.filter(c => !previousCategories.includes(c));\r\n                        if (categories.length > 0) {\r\n                            this.buildState.set(key, {\r\n                                completed: false,\r\n                                options: {\r\n                                    validation: {\r\n                                        ...options.validation,\r\n                                        categories\r\n                                    }\r\n                                },\r\n                                result: buildState.result\r\n                            });\r\n                            document.state = DocumentState.IndexedReferences;\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                // Default: forget any previous build options\r\n                this.buildState.delete(key);\r\n            }\r\n        }\r\n        this.currentState = DocumentState.Changed;\r\n        await this.emitUpdate(documents.map(e => e.uri), []);\r\n        await this.buildDocuments(documents, options, cancelToken);\r\n    }\r\n\r\n    async update(changed: URI[], deleted: URI[], cancelToken = CancellationToken.None): Promise<void> {\r\n        this.currentState = DocumentState.Changed;\r\n        // Remove all metadata of documents that are reported as deleted\r\n        for (const deletedUri of deleted) {\r\n            this.langiumDocuments.deleteDocument(deletedUri);\r\n            this.buildState.delete(deletedUri.toString());\r\n            this.indexManager.remove(deletedUri);\r\n        }\r\n        // Set the state of all changed documents to `Changed` so they are completely rebuilt\r\n        for (const changedUri of changed) {\r\n            const invalidated = this.langiumDocuments.invalidateDocument(changedUri);\r\n            if (!invalidated) {\r\n                // We create an unparsed, invalid document.\r\n                // This will be parsed as soon as we reach the first document builder phase.\r\n                // This allows to cancel the parsing process later in case we need it.\r\n                const newDocument = this.langiumDocumentFactory.fromModel({ $type: 'INVALID' }, changedUri);\r\n                newDocument.state = DocumentState.Changed;\r\n                this.langiumDocuments.addDocument(newDocument);\r\n            }\r\n            this.buildState.delete(changedUri.toString());\r\n        }\r\n        // Set the state of all documents that should be relinked to `ComputedScopes` (if not already lower)\r\n        const allChangedUris = stream(changed).concat(deleted).map(uri => uri.toString()).toSet();\r\n        this.langiumDocuments.all\r\n            .filter(doc => !allChangedUris.has(doc.uri.toString()) && this.shouldRelink(doc, allChangedUris))\r\n            .forEach(doc => {\r\n                const linker = this.serviceRegistry.getServices(doc.uri).references.Linker;\r\n                linker.unlink(doc);\r\n                doc.state = Math.min(doc.state, DocumentState.ComputedScopes);\r\n                doc.diagnostics = undefined;\r\n            });\r\n        // Notify listeners of the update\r\n        await this.emitUpdate(changed, deleted);\r\n        // Only allow interrupting the execution after all state changes are done\r\n        await interruptAndCheck(cancelToken);\r\n\r\n        // Collect and sort all documents that we should rebuild\r\n        const rebuildDocuments = this.sortDocuments(\r\n            this.langiumDocuments.all\r\n                .filter(doc =>\r\n                    // This includes those that were reported as changed and those that we selected for relinking\r\n                    doc.state < DocumentState.Linked\r\n                    // This includes those for which a previous build has been cancelled\r\n                    || !this.buildState.get(doc.uri.toString())?.completed\r\n                )\r\n                .toArray()\r\n        );\r\n        await this.buildDocuments(rebuildDocuments, this.updateBuildOptions, cancelToken);\r\n    }\r\n\r\n    protected async emitUpdate(changed: URI[], deleted: URI[]): Promise<void> {\r\n        await Promise.all(this.updateListeners.map(listener => listener(changed, deleted)));\r\n    }\r\n\r\n    /**\r\n     * Sort the given documents by priority. By default, documents with an open text document are prioritized.\r\n     * This is useful to ensure that visible documents show their diagnostics before all other documents.\r\n     *\r\n     * This improves the responsiveness in large workspaces as users usually don't care about diagnostics\r\n     * in files that are currently not opened in the editor.\r\n     */\r\n    protected sortDocuments(documents: LangiumDocument[]): LangiumDocument[] {\r\n        let left = 0;\r\n        let right = documents.length - 1;\r\n\r\n        while (left < right) {\r\n            while (left < documents.length && this.hasTextDocument(documents[left])) {\r\n                left++;\r\n            }\r\n\r\n            while (right >= 0 && !this.hasTextDocument(documents[right])) {\r\n                right--;\r\n            }\r\n\r\n            if (left < right) {\r\n                [documents[left], documents[right]] = [documents[right], documents[left]];\r\n            }\r\n        }\r\n\r\n        return documents;\r\n    }\r\n\r\n    private hasTextDocument(doc: LangiumDocument): boolean {\r\n        return Boolean(this.textDocuments?.get(doc.uri));\r\n    }\r\n\r\n    /**\r\n     * Check whether the given document should be relinked after changes were found in the given URIs.\r\n     */\r\n    protected shouldRelink(document: LangiumDocument, changedUris: Set<string>): boolean {\r\n        // Relink documents with linking errors -- maybe those references can be resolved now\r\n        if (document.references.some(ref => ref.error !== undefined)) {\r\n            return true;\r\n        }\r\n        // Check whether the document is affected by any of the changed URIs\r\n        return this.indexManager.isAffected(document, changedUris);\r\n    }\r\n\r\n    onUpdate(callback: DocumentUpdateListener): Disposable {\r\n        this.updateListeners.push(callback);\r\n        return Disposable.create(() => {\r\n            const index = this.updateListeners.indexOf(callback);\r\n            if (index >= 0) {\r\n                this.updateListeners.splice(index, 1);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Build the given documents by stepping through all build phases. If a document's state indicates\r\n     * that a certain build phase is already done, the phase is skipped for that document.\r\n     *\r\n     * @param documents The documents to build.\r\n     * @param options the {@link BuildOptions} to use.\r\n     * @param cancelToken A cancellation token that can be used to cancel the build.\r\n     * @returns A promise that resolves when the build is done.\r\n     */\r\n    protected async buildDocuments(documents: LangiumDocument[], options: BuildOptions, cancelToken: CancellationToken): Promise<void> {\r\n        this.prepareBuild(documents, options);\r\n        // 0. Parse content\r\n        await this.runCancelable(documents, DocumentState.Parsed, cancelToken, doc =>\r\n            this.langiumDocumentFactory.update(doc, cancelToken)\r\n        );\r\n        // 1. Index content\r\n        await this.runCancelable(documents, DocumentState.IndexedContent, cancelToken, doc =>\r\n            this.indexManager.updateContent(doc, cancelToken)\r\n        );\r\n        // 2. Compute scopes\r\n        await this.runCancelable(documents, DocumentState.ComputedScopes, cancelToken, async doc => {\r\n            const scopeComputation = this.serviceRegistry.getServices(doc.uri).references.ScopeComputation;\r\n            doc.precomputedScopes = await scopeComputation.computeLocalScopes(doc, cancelToken);\r\n        });\r\n        // 3. Linking\r\n        await this.runCancelable(documents, DocumentState.Linked, cancelToken, doc => {\r\n            const linker = this.serviceRegistry.getServices(doc.uri).references.Linker;\r\n            return linker.link(doc, cancelToken);\r\n        });\r\n        // 4. Index references\r\n        await this.runCancelable(documents, DocumentState.IndexedReferences, cancelToken, doc =>\r\n            this.indexManager.updateReferences(doc, cancelToken)\r\n        );\r\n        // 5. Validation\r\n        const toBeValidated = documents.filter(doc => this.shouldValidate(doc));\r\n        await this.runCancelable(toBeValidated, DocumentState.Validated, cancelToken, doc =>\r\n            this.validate(doc, cancelToken)\r\n        );\r\n\r\n        // If we've made it to this point without being cancelled, we can mark the build state as completed.\r\n        for (const doc of documents) {\r\n            const state = this.buildState.get(doc.uri.toString());\r\n            if (state) {\r\n                state.completed = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Runs prior to beginning the build process to update the {@link DocumentBuildState} for each document\r\n     *\r\n     * @param documents collection of documents to be built\r\n     * @param options the {@link BuildOptions} to use\r\n     */\r\n    protected prepareBuild(documents: LangiumDocument[], options: BuildOptions): void {\r\n        for (const doc of documents) {\r\n            const key = doc.uri.toString();\r\n            const state = this.buildState.get(key);\r\n            // If the document has no previous build state, we set it. If it has one, but it's already marked\r\n            // as completed, we overwrite it. If the previous build was not completed, we keep its state\r\n            // and continue where it was cancelled.\r\n            if (!state || state.completed) {\r\n                this.buildState.set(key, {\r\n                    completed: false,\r\n                    options,\r\n                    result: state?.result\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Runs a cancelable operation on a set of documents to bring them to a specified {@link DocumentState}.\r\n     *\r\n     * @param documents The array of documents to process.\r\n     * @param targetState The target {@link DocumentState} to bring the documents to.\r\n     * @param cancelToken A token that can be used to cancel the operation.\r\n     * @param callback A function to be called for each document.\r\n     * @returns A promise that resolves when all documents have been processed or the operation is canceled.\r\n     * @throws Will throw `OperationCancelled` if the operation is canceled via a `CancellationToken`.\r\n     */\r\n    protected async runCancelable(documents: LangiumDocument[], targetState: DocumentState, cancelToken: CancellationToken,\r\n        callback: (document: LangiumDocument) => MaybePromise<unknown>): Promise<void> {\r\n        const filtered = documents.filter(doc => doc.state < targetState);\r\n        for (const document of filtered) {\r\n            await interruptAndCheck(cancelToken);\r\n            await callback(document);\r\n            document.state = targetState;\r\n            await this.notifyDocumentPhase(document, targetState, cancelToken);\r\n        }\r\n\r\n        // Do not use `filtered` here, as that will miss documents that have previously reached the current target state\r\n        // For example, this happens in case the cancellation triggers between the processing of two documents\r\n        // Or files that were picked up during the workspace initialization\r\n        const targetStateDocs = documents.filter(doc => doc.state === targetState);\r\n        await this.notifyBuildPhase(targetStateDocs, targetState, cancelToken);\r\n        this.currentState = targetState;\r\n    }\r\n\r\n    onBuildPhase(targetState: DocumentState, callback: DocumentBuildListener): Disposable {\r\n        this.buildPhaseListeners.add(targetState, callback);\r\n        return Disposable.create(() => {\r\n            this.buildPhaseListeners.delete(targetState, callback);\r\n        });\r\n    }\r\n\r\n    onDocumentPhase(targetState: DocumentState, callback: DocumentPhaseListener): Disposable {\r\n        this.documentPhaseListeners.add(targetState, callback);\r\n        return Disposable.create(() => {\r\n            this.documentPhaseListeners.delete(targetState, callback);\r\n        });\r\n    }\r\n\r\n    waitUntil(state: DocumentState, cancelToken?: CancellationToken): Promise<void>;\r\n    waitUntil(state: DocumentState, uri?: URI, cancelToken?: CancellationToken): Promise<URI | undefined>;\r\n    waitUntil(state: DocumentState, uriOrToken?: URI | CancellationToken, cancelToken?: CancellationToken): Promise<URI | undefined | void> {\r\n        let uri: URI | undefined = undefined;\r\n        if (uriOrToken && 'path' in uriOrToken) {\r\n            uri = uriOrToken;\r\n        } else {\r\n            cancelToken = uriOrToken;\r\n        }\r\n        cancelToken ??= CancellationToken.None;\r\n        if (uri) {\r\n            const document = this.langiumDocuments.getDocument(uri);\r\n            if (document && document.state > state) {\r\n                return Promise.resolve(uri);\r\n            }\r\n        }\r\n        if (this.currentState >= state) {\r\n            return Promise.resolve(undefined);\r\n        } else if (cancelToken.isCancellationRequested) {\r\n            return Promise.reject(OperationCancelled);\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            const buildDisposable = this.onBuildPhase(state, () => {\r\n                buildDisposable.dispose();\r\n                cancelDisposable.dispose();\r\n                if (uri) {\r\n                    const document = this.langiumDocuments.getDocument(uri);\r\n                    resolve(document?.uri);\r\n                } else {\r\n                    resolve(undefined);\r\n                }\r\n            });\r\n            const cancelDisposable = cancelToken!.onCancellationRequested(() => {\r\n                buildDisposable.dispose();\r\n                cancelDisposable.dispose();\r\n                reject(OperationCancelled);\r\n            });\r\n        });\r\n    }\r\n\r\n    protected async notifyDocumentPhase(document: LangiumDocument, state: DocumentState, cancelToken: CancellationToken): Promise<void> {\r\n        const listeners = this.documentPhaseListeners.get(state);\r\n        const listenersCopy = listeners.slice();\r\n        for (const listener of listenersCopy) {\r\n            try {\r\n                await listener(document, cancelToken);\r\n            } catch (err) {\r\n                // Ignore cancellation errors\r\n                // We want to finish the listeners before throwing\r\n                if (!isOperationCancelled(err)) {\r\n                    throw err;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected async notifyBuildPhase(documents: LangiumDocument[], state: DocumentState, cancelToken: CancellationToken): Promise<void> {\r\n        if (documents.length === 0) {\r\n            // Don't notify when no document has been processed\r\n            return;\r\n        }\r\n        const listeners = this.buildPhaseListeners.get(state);\r\n        const listenersCopy = listeners.slice();\r\n        for (const listener of listenersCopy) {\r\n            await interruptAndCheck(cancelToken);\r\n            await listener(documents, cancelToken);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Determine whether the given document should be validated during a build. The default\r\n     * implementation checks the `validation` property of the build options. If it's set to `true`\r\n     * or a `ValidationOptions` object, the document is included in the validation phase.\r\n     */\r\n    protected shouldValidate(document: LangiumDocument): boolean {\r\n        return Boolean(this.getBuildOptions(document).validation);\r\n    }\r\n\r\n    /**\r\n     * Run validation checks on the given document and store the resulting diagnostics in the document.\r\n     * If the document already contains diagnostics, the new ones are added to the list.\r\n     */\r\n    protected async validate(document: LangiumDocument, cancelToken: CancellationToken): Promise<void> {\r\n        const validator = this.serviceRegistry.getServices(document.uri).validation.DocumentValidator;\r\n        const validationSetting = this.getBuildOptions(document).validation;\r\n        const options = typeof validationSetting === 'object' ? validationSetting : undefined;\r\n        const diagnostics = await validator.validateDocument(document, options, cancelToken);\r\n        if (document.diagnostics) {\r\n            document.diagnostics.push(...diagnostics);\r\n        } else {\r\n            document.diagnostics = diagnostics;\r\n        }\r\n\r\n        // Store information about the executed validation in the build state\r\n        const state = this.buildState.get(document.uri.toString());\r\n        if (state) {\r\n            state.result ??= {};\r\n            const newCategories = options?.categories ?? ValidationCategory.all;\r\n            if (state.result.validationChecks) {\r\n                state.result.validationChecks.push(...newCategories);\r\n            } else {\r\n                state.result.validationChecks = [...newCategories];\r\n            }\r\n        }\r\n    }\r\n\r\n    protected getBuildOptions(document: LangiumDocument): BuildOptions {\r\n        return this.buildState.get(document.uri.toString())?.options ?? {};\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,UAAU,QAAQ,wBAAwB;AASnD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,2BAA2B;AACvG,SAASC,MAAM,QAAQ,oBAAoB;AAE3C,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,aAAa,QAAQ,gBAAgB;AAmG9C,OAAM,MAAOC,sBAAsB;EAqB/BC,YAAYC,QAAmC;IAnB/C,KAAAC,kBAAkB,GAAiB;MAC/B;MACAC,UAAU,EAAE;QACRC,UAAU,EAAE,CAAC,UAAU,EAAE,MAAM;;KAEtC;IAOkB,KAAAC,eAAe,GAA6B,EAAE;IAC9C,KAAAC,mBAAmB,GAAG,IAAId,QAAQ,EAAwC;IAC1E,KAAAe,sBAAsB,GAAG,IAAIf,QAAQ,EAAwC;IAC7E,KAAAgB,UAAU,GAAG,IAAIC,GAAG,EAA8B;IAClD,KAAAC,oBAAoB,GAAG,IAAID,GAAG,EAA0B;IACjE,KAAAE,YAAY,GAAGb,aAAa,CAACc,OAAO;IAG1C,IAAI,CAACC,gBAAgB,GAAGZ,QAAQ,CAACa,SAAS,CAACC,gBAAgB;IAC3D,IAAI,CAACC,sBAAsB,GAAGf,QAAQ,CAACa,SAAS,CAACG,sBAAsB;IACvE,IAAI,CAACC,aAAa,GAAGjB,QAAQ,CAACa,SAAS,CAACK,aAAa;IACrD,IAAI,CAACC,YAAY,GAAGnB,QAAQ,CAACa,SAAS,CAACO,YAAY;IACnD,IAAI,CAACC,eAAe,GAAGrB,QAAQ,CAACsB,eAAe;EACnD;EAEA,MAAMC,KAAKA,CAAoBC,SAAoC,EAAEC,OAAA,GAAwB,EAAE,EAAEC,WAAW,GAAGrC,iBAAiB,CAACsC,IAAI;;IACjI,KAAK,MAAMC,QAAQ,IAAIJ,SAAS,EAAE;MAC9B,MAAMK,GAAG,GAAGD,QAAQ,CAACE,GAAG,CAACC,QAAQ,EAAE;MACnC,IAAIH,QAAQ,CAACI,KAAK,KAAKnC,aAAa,CAACoC,SAAS,EAAE;QAC5C,IAAI,OAAOR,OAAO,CAACvB,UAAU,KAAK,SAAS,IAAIuB,OAAO,CAACvB,UAAU,EAAE;UAC/D;UACA0B,QAAQ,CAACI,KAAK,GAAGnC,aAAa,CAACqC,iBAAiB;UAChDN,QAAQ,CAACO,WAAW,GAAGC,SAAS;UAChC,IAAI,CAAC7B,UAAU,CAAC8B,MAAM,CAACR,GAAG,CAAC;QAC/B,CAAC,MAAM,IAAI,OAAOJ,OAAO,CAACvB,UAAU,KAAK,QAAQ,EAAE;UAC/C,MAAMK,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC+B,GAAG,CAACT,GAAG,CAAC;UAC3C,MAAMU,kBAAkB,GAAG,CAAAC,EAAA,GAAAjC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkC,MAAM,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,gBAAgB;UAC/D,IAAIH,kBAAkB,EAAE;YACpB;YACA;YACA,MAAMI,aAAa,GAAG,CAAAC,EAAA,GAAAnB,OAAO,CAACvB,UAAU,CAACC,UAAU,cAAAyC,EAAA,cAAAA,EAAA,GAAIhD,kBAAkB,CAACiD,GAA2B;YACrG,MAAM1C,UAAU,GAAGwC,aAAa,CAACG,MAAM,CAACC,CAAC,IAAI,CAACR,kBAAkB,CAACS,QAAQ,CAACD,CAAC,CAAC,CAAC;YAC7E,IAAI5C,UAAU,CAAC8C,MAAM,GAAG,CAAC,EAAE;cACvB,IAAI,CAAC1C,UAAU,CAAC2C,GAAG,CAACrB,GAAG,EAAE;gBACrBsB,SAAS,EAAE,KAAK;gBAChB1B,OAAO,EAAE;kBACLvB,UAAU,EAAAkD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACH5B,OAAO,CAACvB,UAAU;oBACrBC;kBAAU;iBAEjB;gBACDsC,MAAM,EAAElC,UAAU,CAACkC;eACtB,CAAC;cACFb,QAAQ,CAACI,KAAK,GAAGnC,aAAa,CAACqC,iBAAiB;YACpD;UACJ;QACJ;MACJ,CAAC,MAAM;QACH;QACA,IAAI,CAAC3B,UAAU,CAAC8B,MAAM,CAACR,GAAG,CAAC;MAC/B;IACJ;IACA,IAAI,CAACnB,YAAY,GAAGb,aAAa,CAACc,OAAO;IACzC,MAAM,IAAI,CAAC2C,UAAU,CAAC9B,SAAS,CAAC+B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1B,GAAG,CAAC,EAAE,EAAE,CAAC;IACpD,MAAM,IAAI,CAAC2B,cAAc,CAACjC,SAAS,EAAEC,OAAO,EAAEC,WAAW,CAAC;EAC9D;EAEA,MAAMgC,MAAMA,CAACC,OAAc,EAAEC,OAAc,EAAElC,WAAW,GAAGrC,iBAAiB,CAACsC,IAAI;IAC7E,IAAI,CAACjB,YAAY,GAAGb,aAAa,CAACc,OAAO;IACzC;IACA,KAAK,MAAMkD,UAAU,IAAID,OAAO,EAAE;MAC9B,IAAI,CAAChD,gBAAgB,CAACkD,cAAc,CAACD,UAAU,CAAC;MAChD,IAAI,CAACtD,UAAU,CAAC8B,MAAM,CAACwB,UAAU,CAAC9B,QAAQ,EAAE,CAAC;MAC7C,IAAI,CAACZ,YAAY,CAAC4C,MAAM,CAACF,UAAU,CAAC;IACxC;IACA;IACA,KAAK,MAAMG,UAAU,IAAIL,OAAO,EAAE;MAC9B,MAAMM,WAAW,GAAG,IAAI,CAACrD,gBAAgB,CAACsD,kBAAkB,CAACF,UAAU,CAAC;MACxE,IAAI,CAACC,WAAW,EAAE;QACd;QACA;QACA;QACA,MAAME,WAAW,GAAG,IAAI,CAACpD,sBAAsB,CAACqD,SAAS,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAE,EAAEL,UAAU,CAAC;QAC3FG,WAAW,CAACnC,KAAK,GAAGnC,aAAa,CAACc,OAAO;QACzC,IAAI,CAACC,gBAAgB,CAAC0D,WAAW,CAACH,WAAW,CAAC;MAClD;MACA,IAAI,CAAC5D,UAAU,CAAC8B,MAAM,CAAC2B,UAAU,CAACjC,QAAQ,EAAE,CAAC;IACjD;IACA;IACA,MAAMwC,cAAc,GAAG5E,MAAM,CAACgE,OAAO,CAAC,CAACa,MAAM,CAACZ,OAAO,CAAC,CAACL,GAAG,CAACzB,GAAG,IAAIA,GAAG,CAACC,QAAQ,EAAE,CAAC,CAAC0C,KAAK,EAAE;IACzF,IAAI,CAAC7D,gBAAgB,CAACiC,GAAG,CACpBC,MAAM,CAAC4B,GAAG,IAAI,CAACH,cAAc,CAACI,GAAG,CAACD,GAAG,CAAC5C,GAAG,CAACC,QAAQ,EAAE,CAAC,IAAI,IAAI,CAAC6C,YAAY,CAACF,GAAG,EAAEH,cAAc,CAAC,CAAC,CAChGM,OAAO,CAACH,GAAG,IAAG;MACX,MAAMI,MAAM,GAAG,IAAI,CAACzD,eAAe,CAAC0D,WAAW,CAACL,GAAG,CAAC5C,GAAG,CAAC,CAACkD,UAAU,CAACC,MAAM;MAC1EH,MAAM,CAACI,MAAM,CAACR,GAAG,CAAC;MAClBA,GAAG,CAAC1C,KAAK,GAAGmD,IAAI,CAACC,GAAG,CAACV,GAAG,CAAC1C,KAAK,EAAEnC,aAAa,CAACwF,cAAc,CAAC;MAC7DX,GAAG,CAACvC,WAAW,GAAGC,SAAS;IAC/B,CAAC,CAAC;IACN;IACA,MAAM,IAAI,CAACkB,UAAU,CAACK,OAAO,EAAEC,OAAO,CAAC;IACvC;IACA,MAAMnE,iBAAiB,CAACiC,WAAW,CAAC;IAEpC;IACA,MAAM4D,gBAAgB,GAAG,IAAI,CAACC,aAAa,CACvC,IAAI,CAAC3E,gBAAgB,CAACiC,GAAG,CACpBC,MAAM,CAAC4B,GAAG,IAAG;;MACV;MACA,OAAAA,GAAG,CAAC1C,KAAK,GAAGnC,aAAa,CAAC2F;MAC1B;MAAA,GACG,EAAC,CAAAhD,EAAA,OAAI,CAACjC,UAAU,CAAC+B,GAAG,CAACoC,GAAG,CAAC5C,GAAG,CAACC,QAAQ,EAAE,CAAC,cAAAS,EAAA,uBAAAA,EAAA,CAAEW,SAAS;KAAA,CACzD,CACAsC,OAAO,EAAE,CACjB;IACD,MAAM,IAAI,CAAChC,cAAc,CAAC6B,gBAAgB,EAAE,IAAI,CAACrF,kBAAkB,EAAEyB,WAAW,CAAC;EACrF;EAEU,MAAM4B,UAAUA,CAACK,OAAc,EAAEC,OAAc;IACrD,MAAM8B,OAAO,CAAC7C,GAAG,CAAC,IAAI,CAACzC,eAAe,CAACmD,GAAG,CAACoC,QAAQ,IAAIA,QAAQ,CAAChC,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC;EACvF;EAEA;;;;;;;EAOU2B,aAAaA,CAAC/D,SAA4B;IAChD,IAAIoE,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAGrE,SAAS,CAACyB,MAAM,GAAG,CAAC;IAEhC,OAAO2C,IAAI,GAAGC,KAAK,EAAE;MACjB,OAAOD,IAAI,GAAGpE,SAAS,CAACyB,MAAM,IAAI,IAAI,CAAC6C,eAAe,CAACtE,SAAS,CAACoE,IAAI,CAAC,CAAC,EAAE;QACrEA,IAAI,EAAE;MACV;MAEA,OAAOC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACtE,SAAS,CAACqE,KAAK,CAAC,CAAC,EAAE;QAC1DA,KAAK,EAAE;MACX;MAEA,IAAID,IAAI,GAAGC,KAAK,EAAE;QACd,CAACrE,SAAS,CAACoE,IAAI,CAAC,EAAEpE,SAAS,CAACqE,KAAK,CAAC,CAAC,GAAG,CAACrE,SAAS,CAACqE,KAAK,CAAC,EAAErE,SAAS,CAACoE,IAAI,CAAC,CAAC;MAC7E;IACJ;IAEA,OAAOpE,SAAS;EACpB;EAEQsE,eAAeA,CAACpB,GAAoB;;IACxC,OAAOqB,OAAO,CAAC,CAAAvD,EAAA,OAAI,CAACvB,aAAa,cAAAuB,EAAA,uBAAAA,EAAA,CAAEF,GAAG,CAACoC,GAAG,CAAC5C,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGU8C,YAAYA,CAAChD,QAAyB,EAAEoE,WAAwB;IACtE;IACA,IAAIpE,QAAQ,CAACoD,UAAU,CAACiB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAK/D,SAAS,CAAC,EAAE;MAC1D,OAAO,IAAI;IACf;IACA;IACA,OAAO,IAAI,CAACjB,YAAY,CAACiF,UAAU,CAACxE,QAAQ,EAAEoE,WAAW,CAAC;EAC9D;EAEAK,QAAQA,CAACC,QAAgC;IACrC,IAAI,CAAClG,eAAe,CAACmG,IAAI,CAACD,QAAQ,CAAC;IACnC,OAAOhH,UAAU,CAACkH,MAAM,CAAC,MAAK;MAC1B,MAAMC,KAAK,GAAG,IAAI,CAACrG,eAAe,CAACsG,OAAO,CAACJ,QAAQ,CAAC;MACpD,IAAIG,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACrG,eAAe,CAACuG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACzC;IACJ,CAAC,CAAC;EACN;EAEA;;;;;;;;;EASU,MAAMhD,cAAcA,CAACjC,SAA4B,EAAEC,OAAqB,EAAEC,WAA8B;IAC9G,IAAI,CAACkF,YAAY,CAACpF,SAAS,EAAEC,OAAO,CAAC;IACrC;IACA,MAAM,IAAI,CAACoF,aAAa,CAACrF,SAAS,EAAE3B,aAAa,CAACiH,MAAM,EAAEpF,WAAW,EAAEgD,GAAG,IACtE,IAAI,CAAC3D,sBAAsB,CAAC2C,MAAM,CAACgB,GAAG,EAAEhD,WAAW,CAAC,CACvD;IACD;IACA,MAAM,IAAI,CAACmF,aAAa,CAACrF,SAAS,EAAE3B,aAAa,CAACkH,cAAc,EAAErF,WAAW,EAAEgD,GAAG,IAC9E,IAAI,CAACvD,YAAY,CAAC6F,aAAa,CAACtC,GAAG,EAAEhD,WAAW,CAAC,CACpD;IACD;IACA,MAAM,IAAI,CAACmF,aAAa,CAACrF,SAAS,EAAE3B,aAAa,CAACwF,cAAc,EAAE3D,WAAW,EAAE,MAAMgD,GAAG,IAAG;MACvF,MAAMuC,gBAAgB,GAAG,IAAI,CAAC5F,eAAe,CAAC0D,WAAW,CAACL,GAAG,CAAC5C,GAAG,CAAC,CAACkD,UAAU,CAACkC,gBAAgB;MAC9FxC,GAAG,CAACyC,iBAAiB,GAAG,MAAMF,gBAAgB,CAACG,kBAAkB,CAAC1C,GAAG,EAAEhD,WAAW,CAAC;IACvF,CAAC,CAAC;IACF;IACA,MAAM,IAAI,CAACmF,aAAa,CAACrF,SAAS,EAAE3B,aAAa,CAAC2F,MAAM,EAAE9D,WAAW,EAAEgD,GAAG,IAAG;MACzE,MAAMI,MAAM,GAAG,IAAI,CAACzD,eAAe,CAAC0D,WAAW,CAACL,GAAG,CAAC5C,GAAG,CAAC,CAACkD,UAAU,CAACC,MAAM;MAC1E,OAAOH,MAAM,CAACuC,IAAI,CAAC3C,GAAG,EAAEhD,WAAW,CAAC;IACxC,CAAC,CAAC;IACF;IACA,MAAM,IAAI,CAACmF,aAAa,CAACrF,SAAS,EAAE3B,aAAa,CAACqC,iBAAiB,EAAER,WAAW,EAAEgD,GAAG,IACjF,IAAI,CAACvD,YAAY,CAACmG,gBAAgB,CAAC5C,GAAG,EAAEhD,WAAW,CAAC,CACvD;IACD;IACA,MAAM6F,aAAa,GAAG/F,SAAS,CAACsB,MAAM,CAAC4B,GAAG,IAAI,IAAI,CAAC8C,cAAc,CAAC9C,GAAG,CAAC,CAAC;IACvE,MAAM,IAAI,CAACmC,aAAa,CAACU,aAAa,EAAE1H,aAAa,CAACoC,SAAS,EAAEP,WAAW,EAAEgD,GAAG,IAC7E,IAAI,CAAC+C,QAAQ,CAAC/C,GAAG,EAAEhD,WAAW,CAAC,CAClC;IAED;IACA,KAAK,MAAMgD,GAAG,IAAIlD,SAAS,EAAE;MACzB,MAAMQ,KAAK,GAAG,IAAI,CAACzB,UAAU,CAAC+B,GAAG,CAACoC,GAAG,CAAC5C,GAAG,CAACC,QAAQ,EAAE,CAAC;MACrD,IAAIC,KAAK,EAAE;QACPA,KAAK,CAACmB,SAAS,GAAG,IAAI;MAC1B;IACJ;EACJ;EAEA;;;;;;EAMUyD,YAAYA,CAACpF,SAA4B,EAAEC,OAAqB;IACtE,KAAK,MAAMiD,GAAG,IAAIlD,SAAS,EAAE;MACzB,MAAMK,GAAG,GAAG6C,GAAG,CAAC5C,GAAG,CAACC,QAAQ,EAAE;MAC9B,MAAMC,KAAK,GAAG,IAAI,CAACzB,UAAU,CAAC+B,GAAG,CAACT,GAAG,CAAC;MACtC;MACA;MACA;MACA,IAAI,CAACG,KAAK,IAAIA,KAAK,CAACmB,SAAS,EAAE;QAC3B,IAAI,CAAC5C,UAAU,CAAC2C,GAAG,CAACrB,GAAG,EAAE;UACrBsB,SAAS,EAAE,KAAK;UAChB1B,OAAO;UACPgB,MAAM,EAAET,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES;SAClB,CAAC;MACN;IACJ;EACJ;EAEA;;;;;;;;;;EAUU,MAAMoE,aAAaA,CAACrF,SAA4B,EAAEkG,WAA0B,EAAEhG,WAA8B,EAClH4E,QAA8D;IAC9D,MAAMqB,QAAQ,GAAGnG,SAAS,CAACsB,MAAM,CAAC4B,GAAG,IAAIA,GAAG,CAAC1C,KAAK,GAAG0F,WAAW,CAAC;IACjE,KAAK,MAAM9F,QAAQ,IAAI+F,QAAQ,EAAE;MAC7B,MAAMlI,iBAAiB,CAACiC,WAAW,CAAC;MACpC,MAAM4E,QAAQ,CAAC1E,QAAQ,CAAC;MACxBA,QAAQ,CAACI,KAAK,GAAG0F,WAAW;MAC5B,MAAM,IAAI,CAACE,mBAAmB,CAAChG,QAAQ,EAAE8F,WAAW,EAAEhG,WAAW,CAAC;IACtE;IAEA;IACA;IACA;IACA,MAAMmG,eAAe,GAAGrG,SAAS,CAACsB,MAAM,CAAC4B,GAAG,IAAIA,GAAG,CAAC1C,KAAK,KAAK0F,WAAW,CAAC;IAC1E,MAAM,IAAI,CAACI,gBAAgB,CAACD,eAAe,EAAEH,WAAW,EAAEhG,WAAW,CAAC;IACtE,IAAI,CAAChB,YAAY,GAAGgH,WAAW;EACnC;EAEAK,YAAYA,CAACL,WAA0B,EAAEpB,QAA+B;IACpE,IAAI,CAACjG,mBAAmB,CAAC2H,GAAG,CAACN,WAAW,EAAEpB,QAAQ,CAAC;IACnD,OAAOhH,UAAU,CAACkH,MAAM,CAAC,MAAK;MAC1B,IAAI,CAACnG,mBAAmB,CAACgC,MAAM,CAACqF,WAAW,EAAEpB,QAAQ,CAAC;IAC1D,CAAC,CAAC;EACN;EAEA2B,eAAeA,CAACP,WAA0B,EAAEpB,QAA+B;IACvE,IAAI,CAAChG,sBAAsB,CAAC0H,GAAG,CAACN,WAAW,EAAEpB,QAAQ,CAAC;IACtD,OAAOhH,UAAU,CAACkH,MAAM,CAAC,MAAK;MAC1B,IAAI,CAAClG,sBAAsB,CAAC+B,MAAM,CAACqF,WAAW,EAAEpB,QAAQ,CAAC;IAC7D,CAAC,CAAC;EACN;EAIA4B,SAASA,CAAClG,KAAoB,EAAEmG,UAAoC,EAAEzG,WAA+B;IACjG,IAAII,GAAG,GAAoBM,SAAS;IACpC,IAAI+F,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;MACpCrG,GAAG,GAAGqG,UAAU;IACpB,CAAC,MAAM;MACHzG,WAAW,GAAGyG,UAAU;IAC5B;IACAzG,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAXA,WAAW,GAAKrC,iBAAiB,CAACsC,IAAI;IACtC,IAAIG,GAAG,EAAE;MACL,MAAMF,QAAQ,GAAG,IAAI,CAAChB,gBAAgB,CAACwH,WAAW,CAACtG,GAAG,CAAC;MACvD,IAAIF,QAAQ,IAAIA,QAAQ,CAACI,KAAK,GAAGA,KAAK,EAAE;QACpC,OAAO0D,OAAO,CAAC2C,OAAO,CAACvG,GAAG,CAAC;MAC/B;IACJ;IACA,IAAI,IAAI,CAACpB,YAAY,IAAIsB,KAAK,EAAE;MAC5B,OAAO0D,OAAO,CAAC2C,OAAO,CAACjG,SAAS,CAAC;IACrC,CAAC,MAAM,IAAIV,WAAW,CAAC4G,uBAAuB,EAAE;MAC5C,OAAO5C,OAAO,CAAC6C,MAAM,CAAC/I,kBAAkB,CAAC;IAC7C;IACA,OAAO,IAAIkG,OAAO,CAAC,CAAC2C,OAAO,EAAEE,MAAM,KAAI;MACnC,MAAMC,eAAe,GAAG,IAAI,CAACT,YAAY,CAAC/F,KAAK,EAAE,MAAK;QAClDwG,eAAe,CAACC,OAAO,EAAE;QACzBC,gBAAgB,CAACD,OAAO,EAAE;QAC1B,IAAI3G,GAAG,EAAE;UACL,MAAMF,QAAQ,GAAG,IAAI,CAAChB,gBAAgB,CAACwH,WAAW,CAACtG,GAAG,CAAC;UACvDuG,OAAO,CAACzG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,GAAG,CAAC;QAC1B,CAAC,MAAM;UACHuG,OAAO,CAACjG,SAAS,CAAC;QACtB;MACJ,CAAC,CAAC;MACF,MAAMsG,gBAAgB,GAAGhH,WAAY,CAACiH,uBAAuB,CAAC,MAAK;QAC/DH,eAAe,CAACC,OAAO,EAAE;QACzBC,gBAAgB,CAACD,OAAO,EAAE;QAC1BF,MAAM,CAAC/I,kBAAkB,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEU,MAAMoI,mBAAmBA,CAAChG,QAAyB,EAAEI,KAAoB,EAAEN,WAA8B;IAC/G,MAAMkH,SAAS,GAAG,IAAI,CAACtI,sBAAsB,CAACgC,GAAG,CAACN,KAAK,CAAC;IACxD,MAAM6G,aAAa,GAAGD,SAAS,CAACE,KAAK,EAAE;IACvC,KAAK,MAAMnD,QAAQ,IAAIkD,aAAa,EAAE;MAClC,IAAI;QACA,MAAMlD,QAAQ,CAAC/D,QAAQ,EAAEF,WAAW,CAAC;MACzC,CAAC,CAAC,OAAOqH,GAAG,EAAE;QACV;QACA;QACA,IAAI,CAACrJ,oBAAoB,CAACqJ,GAAG,CAAC,EAAE;UAC5B,MAAMA,GAAG;QACb;MACJ;IACJ;EACJ;EAEU,MAAMjB,gBAAgBA,CAACtG,SAA4B,EAAEQ,KAAoB,EAAEN,WAA8B;IAC/G,IAAIF,SAAS,CAACyB,MAAM,KAAK,CAAC,EAAE;MACxB;MACA;IACJ;IACA,MAAM2F,SAAS,GAAG,IAAI,CAACvI,mBAAmB,CAACiC,GAAG,CAACN,KAAK,CAAC;IACrD,MAAM6G,aAAa,GAAGD,SAAS,CAACE,KAAK,EAAE;IACvC,KAAK,MAAMnD,QAAQ,IAAIkD,aAAa,EAAE;MAClC,MAAMpJ,iBAAiB,CAACiC,WAAW,CAAC;MACpC,MAAMiE,QAAQ,CAACnE,SAAS,EAAEE,WAAW,CAAC;IAC1C;EACJ;EAEA;;;;;EAKU8F,cAAcA,CAAC5F,QAAyB;IAC9C,OAAOmE,OAAO,CAAC,IAAI,CAACiD,eAAe,CAACpH,QAAQ,CAAC,CAAC1B,UAAU,CAAC;EAC7D;EAEA;;;;EAIU,MAAMuH,QAAQA,CAAC7F,QAAyB,EAAEF,WAA8B;;IAC9E,MAAMuH,SAAS,GAAG,IAAI,CAAC5H,eAAe,CAAC0D,WAAW,CAACnD,QAAQ,CAACE,GAAG,CAAC,CAAC5B,UAAU,CAACgJ,iBAAiB;IAC7F,MAAMC,iBAAiB,GAAG,IAAI,CAACH,eAAe,CAACpH,QAAQ,CAAC,CAAC1B,UAAU;IACnE,MAAMuB,OAAO,GAAG,OAAO0H,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAG/G,SAAS;IACrF,MAAMD,WAAW,GAAG,MAAM8G,SAAS,CAACG,gBAAgB,CAACxH,QAAQ,EAAEH,OAAO,EAAEC,WAAW,CAAC;IACpF,IAAIE,QAAQ,CAACO,WAAW,EAAE;MACtBP,QAAQ,CAACO,WAAW,CAACoE,IAAI,CAAC,GAAGpE,WAAW,CAAC;IAC7C,CAAC,MAAM;MACHP,QAAQ,CAACO,WAAW,GAAGA,WAAW;IACtC;IAEA;IACA,MAAMH,KAAK,GAAG,IAAI,CAACzB,UAAU,CAAC+B,GAAG,CAACV,QAAQ,CAACE,GAAG,CAACC,QAAQ,EAAE,CAAC;IAC1D,IAAIC,KAAK,EAAE;MACP,CAAAQ,EAAA,GAAAR,KAAK,CAACS,MAAM,cAAAD,EAAA,cAAAA,EAAA,GAAZR,KAAK,CAACS,MAAM,GAAK,EAAE;MACnB,MAAME,aAAa,GAAG,CAAAC,EAAA,GAAAnB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEtB,UAAU,cAAAyC,EAAA,cAAAA,EAAA,GAAIhD,kBAAkB,CAACiD,GAAG;MACnE,IAAIb,KAAK,CAACS,MAAM,CAACC,gBAAgB,EAAE;QAC/BV,KAAK,CAACS,MAAM,CAACC,gBAAgB,CAAC6D,IAAI,CAAC,GAAG5D,aAAa,CAAC;MACxD,CAAC,MAAM;QACHX,KAAK,CAACS,MAAM,CAACC,gBAAgB,GAAG,CAAC,GAAGC,aAAa,CAAC;MACtD;IACJ;EACJ;EAEUqG,eAAeA,CAACpH,QAAyB;;IAC/C,OAAO,CAAAgB,EAAA,IAAAJ,EAAA,OAAI,CAACjC,UAAU,CAAC+B,GAAG,CAACV,QAAQ,CAACE,GAAG,CAACC,QAAQ,EAAE,CAAC,cAAAS,EAAA,uBAAAA,EAAA,CAAEf,OAAO,cAAAmB,EAAA,cAAAA,EAAA,GAAI,EAAE;EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}