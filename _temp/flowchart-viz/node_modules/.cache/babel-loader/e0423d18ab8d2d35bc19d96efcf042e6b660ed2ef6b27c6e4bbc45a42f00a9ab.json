{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nexport default (function sourceRandomIrwinHall(source) {\n  function randomIrwinHall(n) {\n    if ((n = +n) <= 0) return () => 0;\n    return function () {\n      for (var sum = 0, i = n; i > 1; --i) sum += source();\n      return sum + i * source();\n    };\n  }\n  randomIrwinHall.source = sourceRandomIrwinHall;\n  return randomIrwinHall;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "sourceRandomIrwinHall", "source", "randomIrwinHall", "n", "sum", "i"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/irwinHall.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomIrwinHall(source) {\n  function randomIrwinHall(n) {\n    if ((n = +n) <= 0) return () => 0;\n    return function() {\n      for (var sum = 0, i = n; i > 1; --i) sum += source();\n      return sum + i * source();\n    };\n  }\n\n  randomIrwinHall.source = sourceRandomIrwinHall;\n\n  return randomIrwinHall;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAE9C,eAAe,CAAC,SAASC,qBAAqBA,CAACC,MAAM,EAAE;EACrD,SAASC,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAI,CAACA,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;IACjC,OAAO,YAAW;MAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEC,CAAC,GAAGF,CAAC,EAAEE,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAED,GAAG,IAAIH,MAAM,CAAC,CAAC;MACpD,OAAOG,GAAG,GAAGC,CAAC,GAAGJ,MAAM,CAAC,CAAC;IAC3B,CAAC;EACH;EAEAC,eAAe,CAACD,MAAM,GAAGD,qBAAqB;EAE9C,OAAOE,eAAe;AACxB,CAAC,EAAEH,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}