{"ast": null, "code": "export { RegExpParser } from \"./regexp-parser.js\";\nexport { BaseRegExpVisitor } from \"./base-regexp-visitor.js\";", "map": {"version": 3, "names": ["Reg<PERSON>xp<PERSON><PERSON><PERSON>", "BaseRegExpVisitor"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/regexp-to-ast/src/api.ts"], "sourcesContent": ["export { RegExpParser } from \"./regexp-parser.js\";\nexport { BaseRegExpVisitor } from \"./base-regexp-visitor.js\";\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,iBAAiB,QAAQ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}