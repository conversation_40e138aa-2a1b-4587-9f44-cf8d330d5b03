{"ast": null, "code": "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\n\nclass FullTextDocument {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = undefined;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      const start = this.offsetAt(range.start);\n      const end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(changes, version) {\n    for (const change of changes) {\n      if (FullTextDocument.isIncremental(change)) {\n        // makes sure start is before end\n        const range = getWellformedRange(change.range);\n        // update content\n        const startOffset = this.offsetAt(range.start);\n        const endOffset = this.offsetAt(range.end);\n        this._content = this._content.substring(0, startOffset) + change.text + this._content.substring(endOffset, this._content.length);\n        // update the offsets\n        const startLine = Math.max(range.start.line, 0);\n        const endLine = Math.max(range.end.line, 0);\n        let lineOffsets = this._lineOffsets;\n        const addedLineOffsets = computeLineOffsets(change.text, false, startOffset);\n        if (endLine - startLine === addedLineOffsets.length) {\n          for (let i = 0, len = addedLineOffsets.length; i < len; i++) {\n            lineOffsets[i + startLine + 1] = addedLineOffsets[i];\n          }\n        } else {\n          if (addedLineOffsets.length < 10000) {\n            lineOffsets.splice(startLine + 1, endLine - startLine, ...addedLineOffsets);\n          } else {\n            // avoid too many arguments for splice\n            this._lineOffsets = lineOffsets = lineOffsets.slice(0, startLine + 1).concat(addedLineOffsets, lineOffsets.slice(endLine + 1));\n          }\n        }\n        const diff = change.text.length - (endOffset - startOffset);\n        if (diff !== 0) {\n          for (let i = startLine + 1 + addedLineOffsets.length, len = lineOffsets.length; i < len; i++) {\n            lineOffsets[i] = lineOffsets[i] + diff;\n          }\n        }\n      } else if (FullTextDocument.isFull(change)) {\n        this._content = change.text;\n        this._lineOffsets = undefined;\n      } else {\n        throw new Error('Unknown change event received');\n      }\n    }\n    this._version = version;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === undefined) {\n      this._lineOffsets = computeLineOffsets(this._content, true);\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    const lineOffsets = this.getLineOffsets();\n    let low = 0,\n      high = lineOffsets.length;\n    if (high === 0) {\n      return {\n        line: 0,\n        character: offset\n      };\n    }\n    while (low < high) {\n      const mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    // low is the least x for which the line offset is larger than the current offset\n    // or array.length if no line offset is larger than the current offset\n    const line = low - 1;\n    offset = this.ensureBeforeEOL(offset, lineOffsets[line]);\n    return {\n      line,\n      character: offset - lineOffsets[line]\n    };\n  }\n  offsetAt(position) {\n    const lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    const lineOffset = lineOffsets[position.line];\n    if (position.character <= 0) {\n      return lineOffset;\n    }\n    const nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    const offset = Math.min(lineOffset + position.character, nextLineOffset);\n    return this.ensureBeforeEOL(offset, lineOffset);\n  }\n  ensureBeforeEOL(offset, lineOffset) {\n    while (offset > lineOffset && isEOL(this._content.charCodeAt(offset - 1))) {\n      offset--;\n    }\n    return offset;\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n  static isIncremental(event) {\n    const candidate = event;\n    return candidate !== undefined && candidate !== null && typeof candidate.text === 'string' && candidate.range !== undefined && (candidate.rangeLength === undefined || typeof candidate.rangeLength === 'number');\n  }\n  static isFull(event) {\n    const candidate = event;\n    return candidate !== undefined && candidate !== null && typeof candidate.text === 'string' && candidate.range === undefined && candidate.rangeLength === undefined;\n  }\n}\nexport var TextDocument;\n(function (TextDocument) {\n  /**\n   * Creates a new text document.\n   *\n   * @param uri The document's uri.\n   * @param languageId  The document's language Id.\n   * @param version The document's initial version number.\n   * @param content The document's content.\n   */\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument.create = create;\n  /**\n   * Updates a TextDocument by modifying its content.\n   *\n   * @param document the document to update. Only documents created by TextDocument.create are valid inputs.\n   * @param changes the changes to apply to the document.\n   * @param version the changes version for the document.\n   * @returns The updated TextDocument. Note: That's the same document instance passed in as first parameter.\n   *\n   */\n  function update(document, changes, version) {\n    if (document instanceof FullTextDocument) {\n      document.update(changes, version);\n      return document;\n    } else {\n      throw new Error('TextDocument.update: document must be created by TextDocument.create');\n    }\n  }\n  TextDocument.update = update;\n  function applyEdits(document, edits) {\n    const text = document.getText();\n    const sortedEdits = mergeSort(edits.map(getWellformedEdit), (a, b) => {\n      const diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = 0;\n    const spans = [];\n    for (const e of sortedEdits) {\n      const startOffset = document.offsetAt(e.range.start);\n      if (startOffset < lastModifiedOffset) {\n        throw new Error('Overlapping edit');\n      } else if (startOffset > lastModifiedOffset) {\n        spans.push(text.substring(lastModifiedOffset, startOffset));\n      }\n      if (e.newText.length) {\n        spans.push(e.newText);\n      }\n      lastModifiedOffset = document.offsetAt(e.range.end);\n    }\n    spans.push(text.substr(lastModifiedOffset));\n    return spans.join('');\n  }\n  TextDocument.applyEdits = applyEdits;\n})(TextDocument || (TextDocument = {}));\nfunction mergeSort(data, compare) {\n  if (data.length <= 1) {\n    // sorted\n    return data;\n  }\n  const p = data.length / 2 | 0;\n  const left = data.slice(0, p);\n  const right = data.slice(p);\n  mergeSort(left, compare);\n  mergeSort(right, compare);\n  let leftIdx = 0;\n  let rightIdx = 0;\n  let i = 0;\n  while (leftIdx < left.length && rightIdx < right.length) {\n    const ret = compare(left[leftIdx], right[rightIdx]);\n    if (ret <= 0) {\n      // smaller_equal -> take left to preserve order\n      data[i++] = left[leftIdx++];\n    } else {\n      // greater -> take right\n      data[i++] = right[rightIdx++];\n    }\n  }\n  while (leftIdx < left.length) {\n    data[i++] = left[leftIdx++];\n  }\n  while (rightIdx < right.length) {\n    data[i++] = right[rightIdx++];\n  }\n  return data;\n}\nfunction computeLineOffsets(text, isAtLineStart, textOffset = 0) {\n  const result = isAtLineStart ? [textOffset] : [];\n  for (let i = 0; i < text.length; i++) {\n    const ch = text.charCodeAt(i);\n    if (isEOL(ch)) {\n      if (ch === 13 /* CharCode.CarriageReturn */ && i + 1 < text.length && text.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n        i++;\n      }\n      result.push(textOffset + i + 1);\n    }\n  }\n  return result;\n}\nfunction isEOL(char) {\n  return char === 13 /* CharCode.CarriageReturn */ || char === 10 /* CharCode.LineFeed */;\n}\nfunction getWellformedRange(range) {\n  const start = range.start;\n  const end = range.end;\n  if (start.line > end.line || start.line === end.line && start.character > end.character) {\n    return {\n      start: end,\n      end: start\n    };\n  }\n  return range;\n}\nfunction getWellformedEdit(textEdit) {\n  const range = getWellformedRange(textEdit.range);\n  if (range !== textEdit.range) {\n    return {\n      newText: textEdit.newText,\n      range\n    };\n  }\n  return textEdit;\n}", "map": {"version": 3, "names": ["FullTextDocument", "constructor", "uri", "languageId", "version", "content", "_uri", "_languageId", "_version", "_content", "_lineOffsets", "undefined", "getText", "range", "start", "offsetAt", "end", "substring", "update", "changes", "change", "isIncremental", "getWellformedRange", "startOffset", "endOffset", "text", "length", "startLine", "Math", "max", "line", "endLine", "lineOffsets", "addedLineOffsets", "computeLineOffsets", "i", "len", "splice", "slice", "concat", "diff", "isFull", "Error", "getLineOffsets", "positionAt", "offset", "min", "low", "high", "character", "mid", "floor", "ensureBeforeEOL", "position", "lineOffset", "nextLineOffset", "isEOL", "charCodeAt", "lineCount", "event", "candidate", "rangeLength", "TextDocument", "create", "document", "applyEdits", "edits", "sortedEdits", "mergeSort", "map", "getWellformedEdit", "a", "b", "lastModifiedOffset", "spans", "e", "push", "newText", "substr", "join", "data", "compare", "p", "left", "right", "leftIdx", "rightIdx", "ret", "isAtLineStart", "textOffset", "result", "ch", "char", "textEdit"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-languageserver-textdocument/lib/esm/main.js"], "sourcesContent": ["/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nclass FullTextDocument {\n    constructor(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    get uri() {\n        return this._uri;\n    }\n    get languageId() {\n        return this._languageId;\n    }\n    get version() {\n        return this._version;\n    }\n    getText(range) {\n        if (range) {\n            const start = this.offsetAt(range.start);\n            const end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    }\n    update(changes, version) {\n        for (const change of changes) {\n            if (FullTextDocument.isIncremental(change)) {\n                // makes sure start is before end\n                const range = getWellformedRange(change.range);\n                // update content\n                const startOffset = this.offsetAt(range.start);\n                const endOffset = this.offsetAt(range.end);\n                this._content = this._content.substring(0, startOffset) + change.text + this._content.substring(endOffset, this._content.length);\n                // update the offsets\n                const startLine = Math.max(range.start.line, 0);\n                const endLine = Math.max(range.end.line, 0);\n                let lineOffsets = this._lineOffsets;\n                const addedLineOffsets = computeLineOffsets(change.text, false, startOffset);\n                if (endLine - startLine === addedLineOffsets.length) {\n                    for (let i = 0, len = addedLineOffsets.length; i < len; i++) {\n                        lineOffsets[i + startLine + 1] = addedLineOffsets[i];\n                    }\n                }\n                else {\n                    if (addedLineOffsets.length < 10000) {\n                        lineOffsets.splice(startLine + 1, endLine - startLine, ...addedLineOffsets);\n                    }\n                    else { // avoid too many arguments for splice\n                        this._lineOffsets = lineOffsets = lineOffsets.slice(0, startLine + 1).concat(addedLineOffsets, lineOffsets.slice(endLine + 1));\n                    }\n                }\n                const diff = change.text.length - (endOffset - startOffset);\n                if (diff !== 0) {\n                    for (let i = startLine + 1 + addedLineOffsets.length, len = lineOffsets.length; i < len; i++) {\n                        lineOffsets[i] = lineOffsets[i] + diff;\n                    }\n                }\n            }\n            else if (FullTextDocument.isFull(change)) {\n                this._content = change.text;\n                this._lineOffsets = undefined;\n            }\n            else {\n                throw new Error('Unknown change event received');\n            }\n        }\n        this._version = version;\n    }\n    getLineOffsets() {\n        if (this._lineOffsets === undefined) {\n            this._lineOffsets = computeLineOffsets(this._content, true);\n        }\n        return this._lineOffsets;\n    }\n    positionAt(offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        const lineOffsets = this.getLineOffsets();\n        let low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return { line: 0, character: offset };\n        }\n        while (low < high) {\n            const mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        const line = low - 1;\n        offset = this.ensureBeforeEOL(offset, lineOffsets[line]);\n        return { line, character: offset - lineOffsets[line] };\n    }\n    offsetAt(position) {\n        const lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        const lineOffset = lineOffsets[position.line];\n        if (position.character <= 0) {\n            return lineOffset;\n        }\n        const nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        const offset = Math.min(lineOffset + position.character, nextLineOffset);\n        return this.ensureBeforeEOL(offset, lineOffset);\n    }\n    ensureBeforeEOL(offset, lineOffset) {\n        while (offset > lineOffset && isEOL(this._content.charCodeAt(offset - 1))) {\n            offset--;\n        }\n        return offset;\n    }\n    get lineCount() {\n        return this.getLineOffsets().length;\n    }\n    static isIncremental(event) {\n        const candidate = event;\n        return candidate !== undefined && candidate !== null &&\n            typeof candidate.text === 'string' && candidate.range !== undefined &&\n            (candidate.rangeLength === undefined || typeof candidate.rangeLength === 'number');\n    }\n    static isFull(event) {\n        const candidate = event;\n        return candidate !== undefined && candidate !== null &&\n            typeof candidate.text === 'string' && candidate.range === undefined && candidate.rangeLength === undefined;\n    }\n}\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new text document.\n     *\n     * @param uri The document's uri.\n     * @param languageId  The document's language Id.\n     * @param version The document's initial version number.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Updates a TextDocument by modifying its content.\n     *\n     * @param document the document to update. Only documents created by TextDocument.create are valid inputs.\n     * @param changes the changes to apply to the document.\n     * @param version the changes version for the document.\n     * @returns The updated TextDocument. Note: That's the same document instance passed in as first parameter.\n     *\n     */\n    function update(document, changes, version) {\n        if (document instanceof FullTextDocument) {\n            document.update(changes, version);\n            return document;\n        }\n        else {\n            throw new Error('TextDocument.update: document must be created by TextDocument.create');\n        }\n    }\n    TextDocument.update = update;\n    function applyEdits(document, edits) {\n        const text = document.getText();\n        const sortedEdits = mergeSort(edits.map(getWellformedEdit), (a, b) => {\n            const diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        let lastModifiedOffset = 0;\n        const spans = [];\n        for (const e of sortedEdits) {\n            const startOffset = document.offsetAt(e.range.start);\n            if (startOffset < lastModifiedOffset) {\n                throw new Error('Overlapping edit');\n            }\n            else if (startOffset > lastModifiedOffset) {\n                spans.push(text.substring(lastModifiedOffset, startOffset));\n            }\n            if (e.newText.length) {\n                spans.push(e.newText);\n            }\n            lastModifiedOffset = document.offsetAt(e.range.end);\n        }\n        spans.push(text.substr(lastModifiedOffset));\n        return spans.join('');\n    }\n    TextDocument.applyEdits = applyEdits;\n})(TextDocument || (TextDocument = {}));\nfunction mergeSort(data, compare) {\n    if (data.length <= 1) {\n        // sorted\n        return data;\n    }\n    const p = (data.length / 2) | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n        const ret = compare(left[leftIdx], right[rightIdx]);\n        if (ret <= 0) {\n            // smaller_equal -> take left to preserve order\n            data[i++] = left[leftIdx++];\n        }\n        else {\n            // greater -> take right\n            data[i++] = right[rightIdx++];\n        }\n    }\n    while (leftIdx < left.length) {\n        data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n        data[i++] = right[rightIdx++];\n    }\n    return data;\n}\nfunction computeLineOffsets(text, isAtLineStart, textOffset = 0) {\n    const result = isAtLineStart ? [textOffset] : [];\n    for (let i = 0; i < text.length; i++) {\n        const ch = text.charCodeAt(i);\n        if (isEOL(ch)) {\n            if (ch === 13 /* CharCode.CarriageReturn */ && i + 1 < text.length && text.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n                i++;\n            }\n            result.push(textOffset + i + 1);\n        }\n    }\n    return result;\n}\nfunction isEOL(char) {\n    return char === 13 /* CharCode.CarriageReturn */ || char === 10 /* CharCode.LineFeed */;\n}\nfunction getWellformedRange(range) {\n    const start = range.start;\n    const end = range.end;\n    if (start.line > end.line || (start.line === end.line && start.character > end.character)) {\n        return { start: end, end: start };\n    }\n    return range;\n}\nfunction getWellformedEdit(textEdit) {\n    const range = getWellformedRange(textEdit.range);\n    if (range !== textEdit.range) {\n        return { newText: textEdit.newText, range };\n    }\n    return textEdit;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,YAAY;;AACZ,MAAMA,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC3C,IAAI,CAACC,IAAI,GAAGJ,GAAG;IACf,IAAI,CAACK,WAAW,GAAGJ,UAAU;IAC7B,IAAI,CAACK,QAAQ,GAAGJ,OAAO;IACvB,IAAI,CAACK,QAAQ,GAAGJ,OAAO;IACvB,IAAI,CAACK,YAAY,GAAGC,SAAS;EACjC;EACA,IAAIT,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACI,IAAI;EACpB;EACA,IAAIH,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACI,WAAW;EAC3B;EACA,IAAIH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACI,QAAQ;EACxB;EACAI,OAAOA,CAACC,KAAK,EAAE;IACX,IAAIA,KAAK,EAAE;MACP,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACF,KAAK,CAACC,KAAK,CAAC;MACxC,MAAME,GAAG,GAAG,IAAI,CAACD,QAAQ,CAACF,KAAK,CAACG,GAAG,CAAC;MACpC,OAAO,IAAI,CAACP,QAAQ,CAACQ,SAAS,CAACH,KAAK,EAAEE,GAAG,CAAC;IAC9C;IACA,OAAO,IAAI,CAACP,QAAQ;EACxB;EACAS,MAAMA,CAACC,OAAO,EAAEf,OAAO,EAAE;IACrB,KAAK,MAAMgB,MAAM,IAAID,OAAO,EAAE;MAC1B,IAAInB,gBAAgB,CAACqB,aAAa,CAACD,MAAM,CAAC,EAAE;QACxC;QACA,MAAMP,KAAK,GAAGS,kBAAkB,CAACF,MAAM,CAACP,KAAK,CAAC;QAC9C;QACA,MAAMU,WAAW,GAAG,IAAI,CAACR,QAAQ,CAACF,KAAK,CAACC,KAAK,CAAC;QAC9C,MAAMU,SAAS,GAAG,IAAI,CAACT,QAAQ,CAACF,KAAK,CAACG,GAAG,CAAC;QAC1C,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACQ,SAAS,CAAC,CAAC,EAAEM,WAAW,CAAC,GAAGH,MAAM,CAACK,IAAI,GAAG,IAAI,CAAChB,QAAQ,CAACQ,SAAS,CAACO,SAAS,EAAE,IAAI,CAACf,QAAQ,CAACiB,MAAM,CAAC;QAChI;QACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAChB,KAAK,CAACC,KAAK,CAACgB,IAAI,EAAE,CAAC,CAAC;QAC/C,MAAMC,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAChB,KAAK,CAACG,GAAG,CAACc,IAAI,EAAE,CAAC,CAAC;QAC3C,IAAIE,WAAW,GAAG,IAAI,CAACtB,YAAY;QACnC,MAAMuB,gBAAgB,GAAGC,kBAAkB,CAACd,MAAM,CAACK,IAAI,EAAE,KAAK,EAAEF,WAAW,CAAC;QAC5E,IAAIQ,OAAO,GAAGJ,SAAS,KAAKM,gBAAgB,CAACP,MAAM,EAAE;UACjD,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,gBAAgB,CAACP,MAAM,EAAES,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;YACzDH,WAAW,CAACG,CAAC,GAAGR,SAAS,GAAG,CAAC,CAAC,GAAGM,gBAAgB,CAACE,CAAC,CAAC;UACxD;QACJ,CAAC,MACI;UACD,IAAIF,gBAAgB,CAACP,MAAM,GAAG,KAAK,EAAE;YACjCM,WAAW,CAACK,MAAM,CAACV,SAAS,GAAG,CAAC,EAAEI,OAAO,GAAGJ,SAAS,EAAE,GAAGM,gBAAgB,CAAC;UAC/E,CAAC,MACI;YAAE;YACH,IAAI,CAACvB,YAAY,GAAGsB,WAAW,GAAGA,WAAW,CAACM,KAAK,CAAC,CAAC,EAAEX,SAAS,GAAG,CAAC,CAAC,CAACY,MAAM,CAACN,gBAAgB,EAAED,WAAW,CAACM,KAAK,CAACP,OAAO,GAAG,CAAC,CAAC,CAAC;UAClI;QACJ;QACA,MAAMS,IAAI,GAAGpB,MAAM,CAACK,IAAI,CAACC,MAAM,IAAIF,SAAS,GAAGD,WAAW,CAAC;QAC3D,IAAIiB,IAAI,KAAK,CAAC,EAAE;UACZ,KAAK,IAAIL,CAAC,GAAGR,SAAS,GAAG,CAAC,GAAGM,gBAAgB,CAACP,MAAM,EAAEU,GAAG,GAAGJ,WAAW,CAACN,MAAM,EAAES,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;YAC1FH,WAAW,CAACG,CAAC,CAAC,GAAGH,WAAW,CAACG,CAAC,CAAC,GAAGK,IAAI;UAC1C;QACJ;MACJ,CAAC,MACI,IAAIxC,gBAAgB,CAACyC,MAAM,CAACrB,MAAM,CAAC,EAAE;QACtC,IAAI,CAACX,QAAQ,GAAGW,MAAM,CAACK,IAAI;QAC3B,IAAI,CAACf,YAAY,GAAGC,SAAS;MACjC,CAAC,MACI;QACD,MAAM,IAAI+B,KAAK,CAAC,+BAA+B,CAAC;MACpD;IACJ;IACA,IAAI,CAAClC,QAAQ,GAAGJ,OAAO;EAC3B;EACAuC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjC,YAAY,KAAKC,SAAS,EAAE;MACjC,IAAI,CAACD,YAAY,GAAGwB,kBAAkB,CAAC,IAAI,CAACzB,QAAQ,EAAE,IAAI,CAAC;IAC/D;IACA,OAAO,IAAI,CAACC,YAAY;EAC5B;EACAkC,UAAUA,CAACC,MAAM,EAAE;IACfA,MAAM,GAAGjB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACkB,GAAG,CAACD,MAAM,EAAE,IAAI,CAACpC,QAAQ,CAACiB,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5D,MAAMM,WAAW,GAAG,IAAI,CAACW,cAAc,CAAC,CAAC;IACzC,IAAII,GAAG,GAAG,CAAC;MAAEC,IAAI,GAAGhB,WAAW,CAACN,MAAM;IACtC,IAAIsB,IAAI,KAAK,CAAC,EAAE;MACZ,OAAO;QAAElB,IAAI,EAAE,CAAC;QAAEmB,SAAS,EAAEJ;MAAO,CAAC;IACzC;IACA,OAAOE,GAAG,GAAGC,IAAI,EAAE;MACf,MAAME,GAAG,GAAGtB,IAAI,CAACuB,KAAK,CAAC,CAACJ,GAAG,GAAGC,IAAI,IAAI,CAAC,CAAC;MACxC,IAAIhB,WAAW,CAACkB,GAAG,CAAC,GAAGL,MAAM,EAAE;QAC3BG,IAAI,GAAGE,GAAG;MACd,CAAC,MACI;QACDH,GAAG,GAAGG,GAAG,GAAG,CAAC;MACjB;IACJ;IACA;IACA;IACA,MAAMpB,IAAI,GAAGiB,GAAG,GAAG,CAAC;IACpBF,MAAM,GAAG,IAAI,CAACO,eAAe,CAACP,MAAM,EAAEb,WAAW,CAACF,IAAI,CAAC,CAAC;IACxD,OAAO;MAAEA,IAAI;MAAEmB,SAAS,EAAEJ,MAAM,GAAGb,WAAW,CAACF,IAAI;IAAE,CAAC;EAC1D;EACAf,QAAQA,CAACsC,QAAQ,EAAE;IACf,MAAMrB,WAAW,GAAG,IAAI,CAACW,cAAc,CAAC,CAAC;IACzC,IAAIU,QAAQ,CAACvB,IAAI,IAAIE,WAAW,CAACN,MAAM,EAAE;MACrC,OAAO,IAAI,CAACjB,QAAQ,CAACiB,MAAM;IAC/B,CAAC,MACI,IAAI2B,QAAQ,CAACvB,IAAI,GAAG,CAAC,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,MAAMwB,UAAU,GAAGtB,WAAW,CAACqB,QAAQ,CAACvB,IAAI,CAAC;IAC7C,IAAIuB,QAAQ,CAACJ,SAAS,IAAI,CAAC,EAAE;MACzB,OAAOK,UAAU;IACrB;IACA,MAAMC,cAAc,GAAIF,QAAQ,CAACvB,IAAI,GAAG,CAAC,GAAGE,WAAW,CAACN,MAAM,GAAIM,WAAW,CAACqB,QAAQ,CAACvB,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAACrB,QAAQ,CAACiB,MAAM;IACvH,MAAMmB,MAAM,GAAGjB,IAAI,CAACkB,GAAG,CAACQ,UAAU,GAAGD,QAAQ,CAACJ,SAAS,EAAEM,cAAc,CAAC;IACxE,OAAO,IAAI,CAACH,eAAe,CAACP,MAAM,EAAES,UAAU,CAAC;EACnD;EACAF,eAAeA,CAACP,MAAM,EAAES,UAAU,EAAE;IAChC,OAAOT,MAAM,GAAGS,UAAU,IAAIE,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,UAAU,CAACZ,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;MACvEA,MAAM,EAAE;IACZ;IACA,OAAOA,MAAM;EACjB;EACA,IAAIa,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACf,cAAc,CAAC,CAAC,CAACjB,MAAM;EACvC;EACA,OAAOL,aAAaA,CAACsC,KAAK,EAAE;IACxB,MAAMC,SAAS,GAAGD,KAAK;IACvB,OAAOC,SAAS,KAAKjD,SAAS,IAAIiD,SAAS,KAAK,IAAI,IAChD,OAAOA,SAAS,CAACnC,IAAI,KAAK,QAAQ,IAAImC,SAAS,CAAC/C,KAAK,KAAKF,SAAS,KAClEiD,SAAS,CAACC,WAAW,KAAKlD,SAAS,IAAI,OAAOiD,SAAS,CAACC,WAAW,KAAK,QAAQ,CAAC;EAC1F;EACA,OAAOpB,MAAMA,CAACkB,KAAK,EAAE;IACjB,MAAMC,SAAS,GAAGD,KAAK;IACvB,OAAOC,SAAS,KAAKjD,SAAS,IAAIiD,SAAS,KAAK,IAAI,IAChD,OAAOA,SAAS,CAACnC,IAAI,KAAK,QAAQ,IAAImC,SAAS,CAAC/C,KAAK,KAAKF,SAAS,IAAIiD,SAAS,CAACC,WAAW,KAAKlD,SAAS;EAClH;AACJ;AACA,OAAO,IAAImD,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,MAAMA,CAAC7D,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC/C,OAAO,IAAIL,gBAAgB,CAACE,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,CAAC;EAClE;EACAyD,YAAY,CAACC,MAAM,GAAGA,MAAM;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7C,MAAMA,CAAC8C,QAAQ,EAAE7C,OAAO,EAAEf,OAAO,EAAE;IACxC,IAAI4D,QAAQ,YAAYhE,gBAAgB,EAAE;MACtCgE,QAAQ,CAAC9C,MAAM,CAACC,OAAO,EAAEf,OAAO,CAAC;MACjC,OAAO4D,QAAQ;IACnB,CAAC,MACI;MACD,MAAM,IAAItB,KAAK,CAAC,sEAAsE,CAAC;IAC3F;EACJ;EACAoB,YAAY,CAAC5C,MAAM,GAAGA,MAAM;EAC5B,SAAS+C,UAAUA,CAACD,QAAQ,EAAEE,KAAK,EAAE;IACjC,MAAMzC,IAAI,GAAGuC,QAAQ,CAACpD,OAAO,CAAC,CAAC;IAC/B,MAAMuD,WAAW,GAAGC,SAAS,CAACF,KAAK,CAACG,GAAG,CAACC,iBAAiB,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK;MAClE,MAAMhC,IAAI,GAAG+B,CAAC,CAAC1D,KAAK,CAACC,KAAK,CAACgB,IAAI,GAAG0C,CAAC,CAAC3D,KAAK,CAACC,KAAK,CAACgB,IAAI;MACpD,IAAIU,IAAI,KAAK,CAAC,EAAE;QACZ,OAAO+B,CAAC,CAAC1D,KAAK,CAACC,KAAK,CAACmC,SAAS,GAAGuB,CAAC,CAAC3D,KAAK,CAACC,KAAK,CAACmC,SAAS;MAC5D;MACA,OAAOT,IAAI;IACf,CAAC,CAAC;IACF,IAAIiC,kBAAkB,GAAG,CAAC;IAC1B,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMC,CAAC,IAAIR,WAAW,EAAE;MACzB,MAAM5C,WAAW,GAAGyC,QAAQ,CAACjD,QAAQ,CAAC4D,CAAC,CAAC9D,KAAK,CAACC,KAAK,CAAC;MACpD,IAAIS,WAAW,GAAGkD,kBAAkB,EAAE;QAClC,MAAM,IAAI/B,KAAK,CAAC,kBAAkB,CAAC;MACvC,CAAC,MACI,IAAInB,WAAW,GAAGkD,kBAAkB,EAAE;QACvCC,KAAK,CAACE,IAAI,CAACnD,IAAI,CAACR,SAAS,CAACwD,kBAAkB,EAAElD,WAAW,CAAC,CAAC;MAC/D;MACA,IAAIoD,CAAC,CAACE,OAAO,CAACnD,MAAM,EAAE;QAClBgD,KAAK,CAACE,IAAI,CAACD,CAAC,CAACE,OAAO,CAAC;MACzB;MACAJ,kBAAkB,GAAGT,QAAQ,CAACjD,QAAQ,CAAC4D,CAAC,CAAC9D,KAAK,CAACG,GAAG,CAAC;IACvD;IACA0D,KAAK,CAACE,IAAI,CAACnD,IAAI,CAACqD,MAAM,CAACL,kBAAkB,CAAC,CAAC;IAC3C,OAAOC,KAAK,CAACK,IAAI,CAAC,EAAE,CAAC;EACzB;EACAjB,YAAY,CAACG,UAAU,GAAGA,UAAU;AACxC,CAAC,EAAEH,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,SAASM,SAASA,CAACY,IAAI,EAAEC,OAAO,EAAE;EAC9B,IAAID,IAAI,CAACtD,MAAM,IAAI,CAAC,EAAE;IAClB;IACA,OAAOsD,IAAI;EACf;EACA,MAAME,CAAC,GAAIF,IAAI,CAACtD,MAAM,GAAG,CAAC,GAAI,CAAC;EAC/B,MAAMyD,IAAI,GAAGH,IAAI,CAAC1C,KAAK,CAAC,CAAC,EAAE4C,CAAC,CAAC;EAC7B,MAAME,KAAK,GAAGJ,IAAI,CAAC1C,KAAK,CAAC4C,CAAC,CAAC;EAC3Bd,SAAS,CAACe,IAAI,EAAEF,OAAO,CAAC;EACxBb,SAAS,CAACgB,KAAK,EAAEH,OAAO,CAAC;EACzB,IAAII,OAAO,GAAG,CAAC;EACf,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAInD,CAAC,GAAG,CAAC;EACT,OAAOkD,OAAO,GAAGF,IAAI,CAACzD,MAAM,IAAI4D,QAAQ,GAAGF,KAAK,CAAC1D,MAAM,EAAE;IACrD,MAAM6D,GAAG,GAAGN,OAAO,CAACE,IAAI,CAACE,OAAO,CAAC,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC;IACnD,IAAIC,GAAG,IAAI,CAAC,EAAE;MACV;MACAP,IAAI,CAAC7C,CAAC,EAAE,CAAC,GAAGgD,IAAI,CAACE,OAAO,EAAE,CAAC;IAC/B,CAAC,MACI;MACD;MACAL,IAAI,CAAC7C,CAAC,EAAE,CAAC,GAAGiD,KAAK,CAACE,QAAQ,EAAE,CAAC;IACjC;EACJ;EACA,OAAOD,OAAO,GAAGF,IAAI,CAACzD,MAAM,EAAE;IAC1BsD,IAAI,CAAC7C,CAAC,EAAE,CAAC,GAAGgD,IAAI,CAACE,OAAO,EAAE,CAAC;EAC/B;EACA,OAAOC,QAAQ,GAAGF,KAAK,CAAC1D,MAAM,EAAE;IAC5BsD,IAAI,CAAC7C,CAAC,EAAE,CAAC,GAAGiD,KAAK,CAACE,QAAQ,EAAE,CAAC;EACjC;EACA,OAAON,IAAI;AACf;AACA,SAAS9C,kBAAkBA,CAACT,IAAI,EAAE+D,aAAa,EAAEC,UAAU,GAAG,CAAC,EAAE;EAC7D,MAAMC,MAAM,GAAGF,aAAa,GAAG,CAACC,UAAU,CAAC,GAAG,EAAE;EAChD,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACC,MAAM,EAAES,CAAC,EAAE,EAAE;IAClC,MAAMwD,EAAE,GAAGlE,IAAI,CAACgC,UAAU,CAACtB,CAAC,CAAC;IAC7B,IAAIqB,KAAK,CAACmC,EAAE,CAAC,EAAE;MACX,IAAIA,EAAE,KAAK,EAAE,CAAC,iCAAiCxD,CAAC,GAAG,CAAC,GAAGV,IAAI,CAACC,MAAM,IAAID,IAAI,CAACgC,UAAU,CAACtB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB;QACzHA,CAAC,EAAE;MACP;MACAuD,MAAM,CAACd,IAAI,CAACa,UAAU,GAAGtD,CAAC,GAAG,CAAC,CAAC;IACnC;EACJ;EACA,OAAOuD,MAAM;AACjB;AACA,SAASlC,KAAKA,CAACoC,IAAI,EAAE;EACjB,OAAOA,IAAI,KAAK,EAAE,CAAC,iCAAiCA,IAAI,KAAK,EAAE,CAAC;AACpE;AACA,SAAStE,kBAAkBA,CAACT,KAAK,EAAE;EAC/B,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;EACzB,MAAME,GAAG,GAAGH,KAAK,CAACG,GAAG;EACrB,IAAIF,KAAK,CAACgB,IAAI,GAAGd,GAAG,CAACc,IAAI,IAAKhB,KAAK,CAACgB,IAAI,KAAKd,GAAG,CAACc,IAAI,IAAIhB,KAAK,CAACmC,SAAS,GAAGjC,GAAG,CAACiC,SAAU,EAAE;IACvF,OAAO;MAAEnC,KAAK,EAAEE,GAAG;MAAEA,GAAG,EAAEF;IAAM,CAAC;EACrC;EACA,OAAOD,KAAK;AAChB;AACA,SAASyD,iBAAiBA,CAACuB,QAAQ,EAAE;EACjC,MAAMhF,KAAK,GAAGS,kBAAkB,CAACuE,QAAQ,CAAChF,KAAK,CAAC;EAChD,IAAIA,KAAK,KAAKgF,QAAQ,CAAChF,KAAK,EAAE;IAC1B,OAAO;MAAEgE,OAAO,EAAEgB,QAAQ,CAAChB,OAAO;MAAEhE;IAAM,CAAC;EAC/C;EACA,OAAOgF,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}