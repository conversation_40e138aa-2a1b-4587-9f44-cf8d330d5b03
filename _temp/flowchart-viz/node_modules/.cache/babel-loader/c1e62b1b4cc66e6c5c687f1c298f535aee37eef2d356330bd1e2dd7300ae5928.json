{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nimport normal from \"./normal.js\";\nexport default (function sourceRandomLogNormal(source) {\n  var N = normal.source(source);\n  function randomLogNormal() {\n    var randomNormal = N.apply(this, arguments);\n    return function () {\n      return Math.exp(randomNormal());\n    };\n  }\n  randomLogNormal.source = sourceRandomLogNormal;\n  return randomLogNormal;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "normal", "sourceRandomLogNormal", "source", "N", "randomLogNormal", "randomNormal", "apply", "arguments", "Math", "exp"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/logNormal.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\nimport normal from \"./normal.js\";\n\nexport default (function sourceRandomLogNormal(source) {\n  var N = normal.source(source);\n\n  function randomLogNormal() {\n    var randomNormal = N.apply(this, arguments);\n    return function() {\n      return Math.exp(randomNormal());\n    };\n  }\n\n  randomLogNormal.source = sourceRandomLogNormal;\n\n  return randomLogNormal;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,MAAM,MAAM,aAAa;AAEhC,eAAe,CAAC,SAASC,qBAAqBA,CAACC,MAAM,EAAE;EACrD,IAAIC,CAAC,GAAGH,MAAM,CAACE,MAAM,CAACA,MAAM,CAAC;EAE7B,SAASE,eAAeA,CAAA,EAAG;IACzB,IAAIC,YAAY,GAAGF,CAAC,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC3C,OAAO,YAAW;MAChB,OAAOC,IAAI,CAACC,GAAG,CAACJ,YAAY,CAAC,CAAC,CAAC;IACjC,CAAC;EACH;EAEAD,eAAe,CAACF,MAAM,GAAGD,qBAAqB;EAE9C,OAAOG,eAAe;AACxB,CAAC,EAAEL,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}