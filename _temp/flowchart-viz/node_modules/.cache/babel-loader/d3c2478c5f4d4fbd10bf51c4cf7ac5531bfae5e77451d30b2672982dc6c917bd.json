{"ast": null, "code": "import * as _ from 'lodash-es';\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(southLayer, _.map(southLayer, function (v, i) {\n    return i;\n  }));\n  var southEntries = _.flatten(_.map(northLayer, function (v) {\n    return _.sortBy(_.map(g.outEdges(v), function (e) {\n      return {\n        pos: southPos[e.w],\n        weight: g.edge(e).weight\n      };\n    }), 'pos');\n  }));\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n  // @ts-expect-error\n  southEntries.forEach(function (entry) {\n    var index = entry.pos + firstIndex;\n    tree[index] += entry.weight;\n    var weightSum = 0;\n    // @ts-expect-error\n    while (index > 0) {\n      // @ts-expect-error\n      if (index % 2) {\n        weightSum += tree[index + 1];\n      }\n      // @ts-expect-error\n      index = index - 1 >> 1;\n      tree[index] += entry.weight;\n    }\n    cc += entry.weight * weightSum;\n  }));\n  return cc;\n}", "map": {"version": 3, "names": ["_", "crossCount", "g", "layering", "cc", "i", "length", "twoLayerCrossCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "southPos", "zipObject", "map", "v", "southEntries", "flatten", "sortBy", "outEdges", "e", "pos", "w", "weight", "edge", "firstIndex", "treeSize", "tree", "Array", "for<PERSON>ach", "entry", "index", "weightSum"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/dagre/order/cross-count.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAE9B,SAASC,UAAU;;AAEnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,CAAC,EAAEC,QAAQ,EAAE;EAC/B,IAAIC,EAAE,GAAG,CAAC;EACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;IACxCD,EAAE,IAAIG,kBAAkB,CAACL,CAAC,EAAEC,QAAQ,CAACE,CAAC,GAAG,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC;EAC3D;EACA,OAAOD,EAAE;AACX;AAEA,SAASG,kBAAkBA,CAACL,CAAC,EAAEM,UAAU,EAAEC,UAAU,EAAE;EACrD;EACA;EACA;EACA,IAAIC,QAAQ,GAAGV,CAAC,CAACW,SAAS,CACxBF,UAAU,EACVT,CAAC,CAACY,GAAG,CAACH,UAAU,EAAE,UAAUI,CAAC,EAAER,CAAC,EAAE;IAChC,OAAOA,CAAC;EACV,CAAC,CACH,CAAC;EACD,IAAIS,YAAY,GAAGd,CAAC,CAACe,OAAO,CAC1Bf,CAAC,CAACY,GAAG,CAACJ,UAAU,EAAE,UAAUK,CAAC,EAAE;IAC7B,OAAOb,CAAC,CAACgB,MAAM,CACbhB,CAAC,CAACY,GAAG,CAACV,CAAC,CAACe,QAAQ,CAACJ,CAAC,CAAC,EAAE,UAAUK,CAAC,EAAE;MAChC,OAAO;QAAEC,GAAG,EAAET,QAAQ,CAACQ,CAAC,CAACE,CAAC,CAAC;QAAEC,MAAM,EAAEnB,CAAC,CAACoB,IAAI,CAACJ,CAAC,CAAC,CAACG;MAAO,CAAC;IACzD,CAAC,CAAC,EACF,KACF,CAAC;EACH,CAAC,CACH,CAAC;;EAED;EACA,IAAIE,UAAU,GAAG,CAAC;EAClB,OAAOA,UAAU,GAAGd,UAAU,CAACH,MAAM,EAAEiB,UAAU,KAAK,CAAC;EACvD,IAAIC,QAAQ,GAAG,CAAC,GAAGD,UAAU,GAAG,CAAC;EACjCA,UAAU,IAAI,CAAC;EACf,IAAIE,IAAI,GAAGzB,CAAC,CAACY,GAAG,CAAC,IAAIc,KAAK,CAACF,QAAQ,CAAC,EAAE,YAAY;IAChD,OAAO,CAAC;EACV,CAAC,CAAC;;EAEF;EACA,IAAIpB,EAAE,GAAG,CAAC;EACVJ,CAAC,CAAC2B,OAAO;EACP;EACAb,YAAY,CAACa,OAAO,CAAC,UAAUC,KAAK,EAAE;IACpC,IAAIC,KAAK,GAAGD,KAAK,CAACT,GAAG,GAAGI,UAAU;IAClCE,IAAI,CAACI,KAAK,CAAC,IAAID,KAAK,CAACP,MAAM;IAC3B,IAAIS,SAAS,GAAG,CAAC;IACjB;IACA,OAAOD,KAAK,GAAG,CAAC,EAAE;MAChB;MACA,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbC,SAAS,IAAIL,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAC9B;MACA;MACAA,KAAK,GAAIA,KAAK,GAAG,CAAC,IAAK,CAAC;MACxBJ,IAAI,CAACI,KAAK,CAAC,IAAID,KAAK,CAACP,MAAM;IAC7B;IACAjB,EAAE,IAAIwB,KAAK,CAACP,MAAM,GAAGS,SAAS;EAChC,CAAC,CACH,CAAC;EAED,OAAO1B,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}