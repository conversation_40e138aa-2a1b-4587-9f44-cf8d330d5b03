{"ast": null, "code": "import { EarlyExitException, isRecognitionException, NoViableAltException } from \"../../exceptions_public.js\";\nimport { clone, has } from \"lodash-es\";\nimport { getLookaheadPathsForOptionalProd, getLookaheadPathsForOr } from \"../../grammar/lookahead.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n/**\n * Trait responsible for runtime parsing errors.\n */\nexport class ErrorHandler {\n  initErrorHandler(config) {\n    this._errors = [];\n    this.errorMessageProvider = has(config, \"errorMessageProvider\") ? config.errorMessageProvider // assumes end user provides the correct config value/type\n    : DEFAULT_PARSER_CONFIG.errorMessageProvider;\n  }\n  SAVE_ERROR(error) {\n    if (isRecognitionException(error)) {\n      error.context = {\n        ruleStack: this.getHumanReadableRuleStack(),\n        ruleOccurrenceStack: clone(this.RULE_OCCURRENCE_STACK)\n      };\n      this._errors.push(error);\n      return error;\n    } else {\n      throw Error(\"Trying to save an Error which is not a RecognitionException\");\n    }\n  }\n  get errors() {\n    return clone(this._errors);\n  }\n  set errors(newErrors) {\n    this._errors = newErrors;\n  }\n  // TODO: consider caching the error message computed information\n  raiseEarlyExitException(occurrence, prodType, userDefinedErrMsg) {\n    const ruleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[ruleName];\n    const lookAheadPathsPerAlternative = getLookaheadPathsForOptionalProd(occurrence, ruleGrammar, prodType, this.maxLookahead);\n    const insideProdPaths = lookAheadPathsPerAlternative[0];\n    const actualTokens = [];\n    for (let i = 1; i <= this.maxLookahead; i++) {\n      actualTokens.push(this.LA(i));\n    }\n    const msg = this.errorMessageProvider.buildEarlyExitMessage({\n      expectedIterationPaths: insideProdPaths,\n      actual: actualTokens,\n      previous: this.LA(0),\n      customUserDescription: userDefinedErrMsg,\n      ruleName: ruleName\n    });\n    throw this.SAVE_ERROR(new EarlyExitException(msg, this.LA(1), this.LA(0)));\n  }\n  // TODO: consider caching the error message computed information\n  raiseNoAltException(occurrence, errMsgTypes) {\n    const ruleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[ruleName];\n    // TODO: getLookaheadPathsForOr can be slow for large enough maxLookahead and certain grammars, consider caching ?\n    const lookAheadPathsPerAlternative = getLookaheadPathsForOr(occurrence, ruleGrammar, this.maxLookahead);\n    const actualTokens = [];\n    for (let i = 1; i <= this.maxLookahead; i++) {\n      actualTokens.push(this.LA(i));\n    }\n    const previousToken = this.LA(0);\n    const errMsg = this.errorMessageProvider.buildNoViableAltMessage({\n      expectedPathsPerAlt: lookAheadPathsPerAlternative,\n      actual: actualTokens,\n      previous: previousToken,\n      customUserDescription: errMsgTypes,\n      ruleName: this.getCurrRuleFullName()\n    });\n    throw this.SAVE_ERROR(new NoViableAltException(errMsg, this.LA(1), previousToken));\n  }\n}", "map": {"version": 3, "names": ["EarlyExitException", "isRecognitionException", "NoViableAltException", "clone", "has", "getLookaheadPathsForOptionalProd", "getLookaheadPathsForOr", "DEFAULT_PARSER_CONFIG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initErrorHandler", "config", "_errors", "errorMessageProvider", "SAVE_ERROR", "error", "context", "ruleStack", "getHumanReadableRuleStack", "ruleOccurrenceStack", "RULE_OCCURRENCE_STACK", "push", "Error", "errors", "newErrors", "raiseEarlyExitException", "occurrence", "prodType", "userDefinedErrMsg", "ruleName", "getCurrRuleFullName", "ruleGrammar", "getGAstProductions", "lookAheadPathsPerAlternative", "max<PERSON><PERSON><PERSON><PERSON>", "insideProdPaths", "actualTokens", "i", "LA", "msg", "buildEarlyExitMessage", "expectedIterationPaths", "actual", "previous", "customUserDescription", "raiseNoAltException", "errMsgTypes", "previousToken", "errMsg", "buildNoViableAltMessage", "expectedPathsPerAlt"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/error_handler.ts"], "sourcesContent": ["import {\n  IParserConfig,\n  IParserErrorMessageProvider,\n  IRecognitionException,\n} from \"@chevrotain/types\";\nimport {\n  EarlyExitException,\n  isRecognitionException,\n  NoViableAltException,\n} from \"../../exceptions_public.js\";\nimport { clone, has } from \"lodash-es\";\nimport {\n  getLookaheadPathsForOptionalProd,\n  getLookaheadPathsForOr,\n  PROD_TYPE,\n} from \"../../grammar/lookahead.js\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n\n/**\n * Trait responsible for runtime parsing errors.\n */\nexport class ErrorHandler {\n  _errors: IRecognitionException[];\n  errorMessageProvider: IParserErrorMessageProvider;\n\n  initErrorHandler(config: IParserConfig) {\n    this._errors = [];\n    this.errorMessageProvider = has(config, \"errorMessageProvider\")\n      ? (config.errorMessageProvider as IParserErrorMessageProvider) // assumes end user provides the correct config value/type\n      : DEFAULT_PARSER_CONFIG.errorMessageProvider;\n  }\n\n  SAVE_ERROR(\n    this: MixedInParser,\n    error: IRecognitionException,\n  ): IRecognitionException {\n    if (isRecognitionException(error)) {\n      error.context = {\n        ruleStack: this.getHumanReadableRuleStack(),\n        ruleOccurrenceStack: clone(this.RULE_OCCURRENCE_STACK),\n      };\n      this._errors.push(error);\n      return error;\n    } else {\n      throw Error(\n        \"Trying to save an Error which is not a RecognitionException\",\n      );\n    }\n  }\n\n  get errors(): IRecognitionException[] {\n    return clone(this._errors);\n  }\n\n  set errors(newErrors: IRecognitionException[]) {\n    this._errors = newErrors;\n  }\n\n  // TODO: consider caching the error message computed information\n  raiseEarlyExitException(\n    this: MixedInParser,\n    occurrence: number,\n    prodType: PROD_TYPE,\n    userDefinedErrMsg: string | undefined,\n  ): never {\n    const ruleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[ruleName];\n    const lookAheadPathsPerAlternative = getLookaheadPathsForOptionalProd(\n      occurrence,\n      ruleGrammar,\n      prodType,\n      this.maxLookahead,\n    );\n    const insideProdPaths = lookAheadPathsPerAlternative[0];\n    const actualTokens = [];\n    for (let i = 1; i <= this.maxLookahead; i++) {\n      actualTokens.push(this.LA(i));\n    }\n    const msg = this.errorMessageProvider.buildEarlyExitMessage({\n      expectedIterationPaths: insideProdPaths,\n      actual: actualTokens,\n      previous: this.LA(0),\n      customUserDescription: userDefinedErrMsg,\n      ruleName: ruleName,\n    });\n\n    throw this.SAVE_ERROR(new EarlyExitException(msg, this.LA(1), this.LA(0)));\n  }\n\n  // TODO: consider caching the error message computed information\n  raiseNoAltException(\n    this: MixedInParser,\n    occurrence: number,\n    errMsgTypes: string | undefined,\n  ): never {\n    const ruleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[ruleName];\n    // TODO: getLookaheadPathsForOr can be slow for large enough maxLookahead and certain grammars, consider caching ?\n    const lookAheadPathsPerAlternative = getLookaheadPathsForOr(\n      occurrence,\n      ruleGrammar,\n      this.maxLookahead,\n    );\n\n    const actualTokens = [];\n    for (let i = 1; i <= this.maxLookahead; i++) {\n      actualTokens.push(this.LA(i));\n    }\n    const previousToken = this.LA(0);\n\n    const errMsg = this.errorMessageProvider.buildNoViableAltMessage({\n      expectedPathsPerAlt: lookAheadPathsPerAlternative,\n      actual: actualTokens,\n      previous: previousToken,\n      customUserDescription: errMsgTypes,\n      ruleName: this.getCurrRuleFullName(),\n    });\n\n    throw this.SAVE_ERROR(\n      new NoViableAltException(errMsg, this.LA(1), previousToken),\n    );\n  }\n}\n"], "mappings": "AAKA,SACEA,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAoB,QACf,4BAA4B;AACnC,SAASC,KAAK,EAAEC,GAAG,QAAQ,WAAW;AACtC,SACEC,gCAAgC,EAChCC,sBAAsB,QAEjB,4BAA4B;AAEnC,SAASC,qBAAqB,QAAQ,cAAc;AAEpD;;;AAGA,OAAM,MAAOC,YAAY;EAIvBC,gBAAgBA,CAACC,MAAqB;IACpC,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,oBAAoB,GAAGR,GAAG,CAACM,MAAM,EAAE,sBAAsB,CAAC,GAC1DA,MAAM,CAACE,oBAAoD,CAAC;IAAA,EAC7DL,qBAAqB,CAACK,oBAAoB;EAChD;EAEAC,UAAUA,CAERC,KAA4B;IAE5B,IAAIb,sBAAsB,CAACa,KAAK,CAAC,EAAE;MACjCA,KAAK,CAACC,OAAO,GAAG;QACdC,SAAS,EAAE,IAAI,CAACC,yBAAyB,EAAE;QAC3CC,mBAAmB,EAAEf,KAAK,CAAC,IAAI,CAACgB,qBAAqB;OACtD;MACD,IAAI,CAACR,OAAO,CAACS,IAAI,CAACN,KAAK,CAAC;MACxB,OAAOA,KAAK;KACb,MAAM;MACL,MAAMO,KAAK,CACT,6DAA6D,CAC9D;;EAEL;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAOnB,KAAK,CAAC,IAAI,CAACQ,OAAO,CAAC;EAC5B;EAEA,IAAIW,MAAMA,CAACC,SAAkC;IAC3C,IAAI,CAACZ,OAAO,GAAGY,SAAS;EAC1B;EAEA;EACAC,uBAAuBA,CAErBC,UAAkB,EAClBC,QAAmB,EACnBC,iBAAqC;IAErC,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC3C,MAAMC,WAAW,GAAG,IAAI,CAACC,kBAAkB,EAAE,CAACH,QAAQ,CAAC;IACvD,MAAMI,4BAA4B,GAAG3B,gCAAgC,CACnEoB,UAAU,EACVK,WAAW,EACXJ,QAAQ,EACR,IAAI,CAACO,YAAY,CAClB;IACD,MAAMC,eAAe,GAAGF,4BAA4B,CAAC,CAAC,CAAC;IACvD,MAAMG,YAAY,GAAG,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACH,YAAY,EAAEG,CAAC,EAAE,EAAE;MAC3CD,YAAY,CAACf,IAAI,CAAC,IAAI,CAACiB,EAAE,CAACD,CAAC,CAAC,CAAC;;IAE/B,MAAME,GAAG,GAAG,IAAI,CAAC1B,oBAAoB,CAAC2B,qBAAqB,CAAC;MAC1DC,sBAAsB,EAAEN,eAAe;MACvCO,MAAM,EAAEN,YAAY;MACpBO,QAAQ,EAAE,IAAI,CAACL,EAAE,CAAC,CAAC,CAAC;MACpBM,qBAAqB,EAAEhB,iBAAiB;MACxCC,QAAQ,EAAEA;KACX,CAAC;IAEF,MAAM,IAAI,CAACf,UAAU,CAAC,IAAIb,kBAAkB,CAACsC,GAAG,EAAE,IAAI,CAACD,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5E;EAEA;EACAO,mBAAmBA,CAEjBnB,UAAkB,EAClBoB,WAA+B;IAE/B,MAAMjB,QAAQ,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC3C,MAAMC,WAAW,GAAG,IAAI,CAACC,kBAAkB,EAAE,CAACH,QAAQ,CAAC;IACvD;IACA,MAAMI,4BAA4B,GAAG1B,sBAAsB,CACzDmB,UAAU,EACVK,WAAW,EACX,IAAI,CAACG,YAAY,CAClB;IAED,MAAME,YAAY,GAAG,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACH,YAAY,EAAEG,CAAC,EAAE,EAAE;MAC3CD,YAAY,CAACf,IAAI,CAAC,IAAI,CAACiB,EAAE,CAACD,CAAC,CAAC,CAAC;;IAE/B,MAAMU,aAAa,GAAG,IAAI,CAACT,EAAE,CAAC,CAAC,CAAC;IAEhC,MAAMU,MAAM,GAAG,IAAI,CAACnC,oBAAoB,CAACoC,uBAAuB,CAAC;MAC/DC,mBAAmB,EAAEjB,4BAA4B;MACjDS,MAAM,EAAEN,YAAY;MACpBO,QAAQ,EAAEI,aAAa;MACvBH,qBAAqB,EAAEE,WAAW;MAClCjB,QAAQ,EAAE,IAAI,CAACC,mBAAmB;KACnC,CAAC;IAEF,MAAM,IAAI,CAAChB,UAAU,CACnB,IAAIX,oBAAoB,CAAC6C,MAAM,EAAE,IAAI,CAACV,EAAE,CAAC,CAAC,CAAC,EAAES,aAAa,CAAC,CAC5D;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}