{"ast": null, "code": "export default function (source, keys) {\n  return Array.from(keys, key => source[key]);\n}", "map": {"version": 3, "names": ["source", "keys", "Array", "from", "key"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/permute.js"], "sourcesContent": ["export default function(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,IAAI,EAAE;EACpC,OAAOC,KAAK,CAACC,IAAI,CAACF,IAAI,EAAEG,GAAG,IAAIJ,MAAM,CAACI,GAAG,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}