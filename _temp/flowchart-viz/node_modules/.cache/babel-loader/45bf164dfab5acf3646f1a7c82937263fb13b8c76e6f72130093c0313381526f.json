{"ast": null, "code": "import { every, flatten, forEach, has, isEmpty, map, reduce } from \"lodash-es\";\nimport { possiblePathsFrom } from \"./interpreter.js\";\nimport { RestWalker } from \"./rest.js\";\nimport { tokenStructuredMatcher, tokenStructuredMatcherNoCategories } from \"../../scan/tokens.js\";\nimport { Alternation, Alternative as AlternativeGAST, GAstVisitor, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator } from \"@chevrotain/gast\";\nexport var PROD_TYPE;\n(function (PROD_TYPE) {\n  PROD_TYPE[PROD_TYPE[\"OPTION\"] = 0] = \"OPTION\";\n  PROD_TYPE[PROD_TYPE[\"REPETITION\"] = 1] = \"REPETITION\";\n  PROD_TYPE[PROD_TYPE[\"REPETITION_MANDATORY\"] = 2] = \"REPETITION_MANDATORY\";\n  PROD_TYPE[PROD_TYPE[\"REPETITION_MANDATORY_WITH_SEPARATOR\"] = 3] = \"REPETITION_MANDATORY_WITH_SEPARATOR\";\n  PROD_TYPE[PROD_TYPE[\"REPETITION_WITH_SEPARATOR\"] = 4] = \"REPETITION_WITH_SEPARATOR\";\n  PROD_TYPE[PROD_TYPE[\"ALTERNATION\"] = 5] = \"ALTERNATION\";\n})(PROD_TYPE || (PROD_TYPE = {}));\nexport function getProdType(prod) {\n  /* istanbul ignore else */\n  if (prod instanceof Option || prod === \"Option\") {\n    return PROD_TYPE.OPTION;\n  } else if (prod instanceof Repetition || prod === \"Repetition\") {\n    return PROD_TYPE.REPETITION;\n  } else if (prod instanceof RepetitionMandatory || prod === \"RepetitionMandatory\") {\n    return PROD_TYPE.REPETITION_MANDATORY;\n  } else if (prod instanceof RepetitionMandatoryWithSeparator || prod === \"RepetitionMandatoryWithSeparator\") {\n    return PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR;\n  } else if (prod instanceof RepetitionWithSeparator || prod === \"RepetitionWithSeparator\") {\n    return PROD_TYPE.REPETITION_WITH_SEPARATOR;\n  } else if (prod instanceof Alternation || prod === \"Alternation\") {\n    return PROD_TYPE.ALTERNATION;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nexport function getLookaheadPaths(options) {\n  const {\n    occurrence,\n    rule,\n    prodType,\n    maxLookahead\n  } = options;\n  const type = getProdType(prodType);\n  if (type === PROD_TYPE.ALTERNATION) {\n    return getLookaheadPathsForOr(occurrence, rule, maxLookahead);\n  } else {\n    return getLookaheadPathsForOptionalProd(occurrence, rule, type, maxLookahead);\n  }\n}\nexport function buildLookaheadFuncForOr(occurrence, ruleGrammar, maxLookahead, hasPredicates, dynamicTokensEnabled, laFuncBuilder) {\n  const lookAheadPaths = getLookaheadPathsForOr(occurrence, ruleGrammar, maxLookahead);\n  const tokenMatcher = areTokenCategoriesNotUsed(lookAheadPaths) ? tokenStructuredMatcherNoCategories : tokenStructuredMatcher;\n  return laFuncBuilder(lookAheadPaths, hasPredicates, tokenMatcher, dynamicTokensEnabled);\n}\n/**\n *  When dealing with an Optional production (OPTION/MANY/2nd iteration of AT_LEAST_ONE/...) we need to compare\n *  the lookahead \"inside\" the production and the lookahead immediately \"after\" it in the same top level rule (context free).\n *\n *  Example: given a production:\n *  ABC(DE)?DF\n *\n *  The optional '(DE)?' should only be entered if we see 'DE'. a single Token 'D' is not sufficient to distinguish between the two\n *  alternatives.\n *\n *  @returns A Lookahead function which will return true IFF the parser should parse the Optional production.\n */\nexport function buildLookaheadFuncForOptionalProd(occurrence, ruleGrammar, k, dynamicTokensEnabled, prodType, lookaheadBuilder) {\n  const lookAheadPaths = getLookaheadPathsForOptionalProd(occurrence, ruleGrammar, prodType, k);\n  const tokenMatcher = areTokenCategoriesNotUsed(lookAheadPaths) ? tokenStructuredMatcherNoCategories : tokenStructuredMatcher;\n  return lookaheadBuilder(lookAheadPaths[0], tokenMatcher, dynamicTokensEnabled);\n}\nexport function buildAlternativesLookAheadFunc(alts, hasPredicates, tokenMatcher, dynamicTokensEnabled) {\n  const numOfAlts = alts.length;\n  const areAllOneTokenLookahead = every(alts, currAlt => {\n    return every(currAlt, currPath => {\n      return currPath.length === 1;\n    });\n  });\n  // This version takes into account the predicates as well.\n  if (hasPredicates) {\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function (orAlts) {\n      // unfortunately the predicates must be extracted every single time\n      // as they cannot be cached due to references to parameters(vars) which are no longer valid.\n      // note that in the common case of no predicates, no cpu time will be wasted on this (see else block)\n      const predicates = map(orAlts, currAlt => currAlt.GATE);\n      for (let t = 0; t < numOfAlts; t++) {\n        const currAlt = alts[t];\n        const currNumOfPaths = currAlt.length;\n        const currPredicate = predicates[t];\n        if (currPredicate !== undefined && currPredicate.call(this) === false) {\n          // if the predicate does not match there is no point in checking the paths\n          continue;\n        }\n        nextPath: for (let j = 0; j < currNumOfPaths; j++) {\n          const currPath = currAlt[j];\n          const currPathLength = currPath.length;\n          for (let i = 0; i < currPathLength; i++) {\n            const nextToken = this.LA(i + 1);\n            if (tokenMatcher(nextToken, currPath[i]) === false) {\n              // mismatch in current path\n              // try the next pth\n              continue nextPath;\n            }\n          }\n          // found a full path that matches.\n          // this will also work for an empty ALT as the loop will be skipped\n          return t;\n        }\n        // none of the paths for the current alternative matched\n        // try the next alternative\n      }\n      // none of the alternatives could be matched\n      return undefined;\n    };\n  } else if (areAllOneTokenLookahead && !dynamicTokensEnabled) {\n    // optimized (common) case of all the lookaheads paths requiring only\n    // a single token lookahead. These Optimizations cannot work if dynamically defined Tokens are used.\n    const singleTokenAlts = map(alts, currAlt => {\n      return flatten(currAlt);\n    });\n    const choiceToAlt = reduce(singleTokenAlts, (result, currAlt, idx) => {\n      forEach(currAlt, currTokType => {\n        if (!has(result, currTokType.tokenTypeIdx)) {\n          result[currTokType.tokenTypeIdx] = idx;\n        }\n        forEach(currTokType.categoryMatches, currExtendingType => {\n          if (!has(result, currExtendingType)) {\n            result[currExtendingType] = idx;\n          }\n        });\n      });\n      return result;\n    }, {});\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function () {\n      const nextToken = this.LA(1);\n      return choiceToAlt[nextToken.tokenTypeIdx];\n    };\n  } else {\n    // optimized lookahead without needing to check the predicates at all.\n    // this causes code duplication which is intentional to improve performance.\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function () {\n      for (let t = 0; t < numOfAlts; t++) {\n        const currAlt = alts[t];\n        const currNumOfPaths = currAlt.length;\n        nextPath: for (let j = 0; j < currNumOfPaths; j++) {\n          const currPath = currAlt[j];\n          const currPathLength = currPath.length;\n          for (let i = 0; i < currPathLength; i++) {\n            const nextToken = this.LA(i + 1);\n            if (tokenMatcher(nextToken, currPath[i]) === false) {\n              // mismatch in current path\n              // try the next pth\n              continue nextPath;\n            }\n          }\n          // found a full path that matches.\n          // this will also work for an empty ALT as the loop will be skipped\n          return t;\n        }\n        // none of the paths for the current alternative matched\n        // try the next alternative\n      }\n      // none of the alternatives could be matched\n      return undefined;\n    };\n  }\n}\nexport function buildSingleAlternativeLookaheadFunction(alt, tokenMatcher, dynamicTokensEnabled) {\n  const areAllOneTokenLookahead = every(alt, currPath => {\n    return currPath.length === 1;\n  });\n  const numOfPaths = alt.length;\n  // optimized (common) case of all the lookaheads paths requiring only\n  // a single token lookahead.\n  if (areAllOneTokenLookahead && !dynamicTokensEnabled) {\n    const singleTokensTypes = flatten(alt);\n    if (singleTokensTypes.length === 1 && isEmpty(singleTokensTypes[0].categoryMatches)) {\n      const expectedTokenType = singleTokensTypes[0];\n      const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx;\n      return function () {\n        return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;\n      };\n    } else {\n      const choiceToAlt = reduce(singleTokensTypes, (result, currTokType, idx) => {\n        result[currTokType.tokenTypeIdx] = true;\n        forEach(currTokType.categoryMatches, currExtendingType => {\n          result[currExtendingType] = true;\n        });\n        return result;\n      }, []);\n      return function () {\n        const nextToken = this.LA(1);\n        return choiceToAlt[nextToken.tokenTypeIdx] === true;\n      };\n    }\n  } else {\n    return function () {\n      nextPath: for (let j = 0; j < numOfPaths; j++) {\n        const currPath = alt[j];\n        const currPathLength = currPath.length;\n        for (let i = 0; i < currPathLength; i++) {\n          const nextToken = this.LA(i + 1);\n          if (tokenMatcher(nextToken, currPath[i]) === false) {\n            // mismatch in current path\n            // try the next pth\n            continue nextPath;\n          }\n        }\n        // found a full path that matches.\n        return true;\n      }\n      // none of the paths matched\n      return false;\n    };\n  }\n}\nclass RestDefinitionFinderWalker extends RestWalker {\n  constructor(topProd, targetOccurrence, targetProdType) {\n    super();\n    this.topProd = topProd;\n    this.targetOccurrence = targetOccurrence;\n    this.targetProdType = targetProdType;\n  }\n  startWalking() {\n    this.walk(this.topProd);\n    return this.restDef;\n  }\n  checkIsTarget(node, expectedProdType, currRest, prevRest) {\n    if (node.idx === this.targetOccurrence && this.targetProdType === expectedProdType) {\n      this.restDef = currRest.concat(prevRest);\n      return true;\n    }\n    // performance optimization, do not iterate over the entire Grammar ast after we have found the target\n    return false;\n  }\n  walkOption(optionProd, currRest, prevRest) {\n    if (!this.checkIsTarget(optionProd, PROD_TYPE.OPTION, currRest, prevRest)) {\n      super.walkOption(optionProd, currRest, prevRest);\n    }\n  }\n  walkAtLeastOne(atLeastOneProd, currRest, prevRest) {\n    if (!this.checkIsTarget(atLeastOneProd, PROD_TYPE.REPETITION_MANDATORY, currRest, prevRest)) {\n      super.walkOption(atLeastOneProd, currRest, prevRest);\n    }\n  }\n  walkAtLeastOneSep(atLeastOneSepProd, currRest, prevRest) {\n    if (!this.checkIsTarget(atLeastOneSepProd, PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR, currRest, prevRest)) {\n      super.walkOption(atLeastOneSepProd, currRest, prevRest);\n    }\n  }\n  walkMany(manyProd, currRest, prevRest) {\n    if (!this.checkIsTarget(manyProd, PROD_TYPE.REPETITION, currRest, prevRest)) {\n      super.walkOption(manyProd, currRest, prevRest);\n    }\n  }\n  walkManySep(manySepProd, currRest, prevRest) {\n    if (!this.checkIsTarget(manySepProd, PROD_TYPE.REPETITION_WITH_SEPARATOR, currRest, prevRest)) {\n      super.walkOption(manySepProd, currRest, prevRest);\n    }\n  }\n}\n/**\n * Returns the definition of a target production in a top level level rule.\n */\nclass InsideDefinitionFinderVisitor extends GAstVisitor {\n  constructor(targetOccurrence, targetProdType, targetRef) {\n    super();\n    this.targetOccurrence = targetOccurrence;\n    this.targetProdType = targetProdType;\n    this.targetRef = targetRef;\n    this.result = [];\n  }\n  checkIsTarget(node, expectedProdName) {\n    if (node.idx === this.targetOccurrence && this.targetProdType === expectedProdName && (this.targetRef === undefined || node === this.targetRef)) {\n      this.result = node.definition;\n    }\n  }\n  visitOption(node) {\n    this.checkIsTarget(node, PROD_TYPE.OPTION);\n  }\n  visitRepetition(node) {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION);\n  }\n  visitRepetitionMandatory(node) {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_MANDATORY);\n  }\n  visitRepetitionMandatoryWithSeparator(node) {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR);\n  }\n  visitRepetitionWithSeparator(node) {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_WITH_SEPARATOR);\n  }\n  visitAlternation(node) {\n    this.checkIsTarget(node, PROD_TYPE.ALTERNATION);\n  }\n}\nfunction initializeArrayOfArrays(size) {\n  const result = new Array(size);\n  for (let i = 0; i < size; i++) {\n    result[i] = [];\n  }\n  return result;\n}\n/**\n * A sort of hash function between a Path in the grammar and a string.\n * Note that this returns multiple \"hashes\" to support the scenario of token categories.\n * -  A single path with categories may match multiple **actual** paths.\n */\nfunction pathToHashKeys(path) {\n  let keys = [\"\"];\n  for (let i = 0; i < path.length; i++) {\n    const tokType = path[i];\n    const longerKeys = [];\n    for (let j = 0; j < keys.length; j++) {\n      const currShorterKey = keys[j];\n      longerKeys.push(currShorterKey + \"_\" + tokType.tokenTypeIdx);\n      for (let t = 0; t < tokType.categoryMatches.length; t++) {\n        const categoriesKeySuffix = \"_\" + tokType.categoryMatches[t];\n        longerKeys.push(currShorterKey + categoriesKeySuffix);\n      }\n    }\n    keys = longerKeys;\n  }\n  return keys;\n}\n/**\n * Imperative style due to being called from a hot spot\n */\nfunction isUniquePrefixHash(altKnownPathsKeys, searchPathKeys, idx) {\n  for (let currAltIdx = 0; currAltIdx < altKnownPathsKeys.length; currAltIdx++) {\n    // We only want to test vs the other alternatives\n    if (currAltIdx === idx) {\n      continue;\n    }\n    const otherAltKnownPathsKeys = altKnownPathsKeys[currAltIdx];\n    for (let searchIdx = 0; searchIdx < searchPathKeys.length; searchIdx++) {\n      const searchKey = searchPathKeys[searchIdx];\n      if (otherAltKnownPathsKeys[searchKey] === true) {\n        return false;\n      }\n    }\n  }\n  // None of the SearchPathKeys were found in any of the other alternatives\n  return true;\n}\nexport function lookAheadSequenceFromAlternatives(altsDefs, k) {\n  const partialAlts = map(altsDefs, currAlt => possiblePathsFrom([currAlt], 1));\n  const finalResult = initializeArrayOfArrays(partialAlts.length);\n  const altsHashes = map(partialAlts, currAltPaths => {\n    const dict = {};\n    forEach(currAltPaths, item => {\n      const keys = pathToHashKeys(item.partialPath);\n      forEach(keys, currKey => {\n        dict[currKey] = true;\n      });\n    });\n    return dict;\n  });\n  let newData = partialAlts;\n  // maxLookahead loop\n  for (let pathLength = 1; pathLength <= k; pathLength++) {\n    const currDataset = newData;\n    newData = initializeArrayOfArrays(currDataset.length);\n    // alternatives loop\n    for (let altIdx = 0; altIdx < currDataset.length; altIdx++) {\n      const currAltPathsAndSuffixes = currDataset[altIdx];\n      // paths in current alternative loop\n      for (let currPathIdx = 0; currPathIdx < currAltPathsAndSuffixes.length; currPathIdx++) {\n        const currPathPrefix = currAltPathsAndSuffixes[currPathIdx].partialPath;\n        const suffixDef = currAltPathsAndSuffixes[currPathIdx].suffixDef;\n        const prefixKeys = pathToHashKeys(currPathPrefix);\n        const isUnique = isUniquePrefixHash(altsHashes, prefixKeys, altIdx);\n        // End of the line for this path.\n        if (isUnique || isEmpty(suffixDef) || currPathPrefix.length === k) {\n          const currAltResult = finalResult[altIdx];\n          // TODO: Can we implement a containsPath using Maps/Dictionaries?\n          if (containsPath(currAltResult, currPathPrefix) === false) {\n            currAltResult.push(currPathPrefix);\n            // Update all new  keys for the current path.\n            for (let j = 0; j < prefixKeys.length; j++) {\n              const currKey = prefixKeys[j];\n              altsHashes[altIdx][currKey] = true;\n            }\n          }\n        }\n        // Expand longer paths\n        else {\n          const newPartialPathsAndSuffixes = possiblePathsFrom(suffixDef, pathLength + 1, currPathPrefix);\n          newData[altIdx] = newData[altIdx].concat(newPartialPathsAndSuffixes);\n          // Update keys for new known paths\n          forEach(newPartialPathsAndSuffixes, item => {\n            const prefixKeys = pathToHashKeys(item.partialPath);\n            forEach(prefixKeys, key => {\n              altsHashes[altIdx][key] = true;\n            });\n          });\n        }\n      }\n    }\n  }\n  return finalResult;\n}\nexport function getLookaheadPathsForOr(occurrence, ruleGrammar, k, orProd) {\n  const visitor = new InsideDefinitionFinderVisitor(occurrence, PROD_TYPE.ALTERNATION, orProd);\n  ruleGrammar.accept(visitor);\n  return lookAheadSequenceFromAlternatives(visitor.result, k);\n}\nexport function getLookaheadPathsForOptionalProd(occurrence, ruleGrammar, prodType, k) {\n  const insideDefVisitor = new InsideDefinitionFinderVisitor(occurrence, prodType);\n  ruleGrammar.accept(insideDefVisitor);\n  const insideDef = insideDefVisitor.result;\n  const afterDefWalker = new RestDefinitionFinderWalker(ruleGrammar, occurrence, prodType);\n  const afterDef = afterDefWalker.startWalking();\n  const insideFlat = new AlternativeGAST({\n    definition: insideDef\n  });\n  const afterFlat = new AlternativeGAST({\n    definition: afterDef\n  });\n  return lookAheadSequenceFromAlternatives([insideFlat, afterFlat], k);\n}\nexport function containsPath(alternative, searchPath) {\n  compareOtherPath: for (let i = 0; i < alternative.length; i++) {\n    const otherPath = alternative[i];\n    if (otherPath.length !== searchPath.length) {\n      continue;\n    }\n    for (let j = 0; j < otherPath.length; j++) {\n      const searchTok = searchPath[j];\n      const otherTok = otherPath[j];\n      const matchingTokens = searchTok === otherTok || otherTok.categoryMatchesMap[searchTok.tokenTypeIdx] !== undefined;\n      if (matchingTokens === false) {\n        continue compareOtherPath;\n      }\n    }\n    return true;\n  }\n  return false;\n}\nexport function isStrictPrefixOfPath(prefix, other) {\n  return prefix.length < other.length && every(prefix, (tokType, idx) => {\n    const otherTokType = other[idx];\n    return tokType === otherTokType || otherTokType.categoryMatchesMap[tokType.tokenTypeIdx];\n  });\n}\nexport function areTokenCategoriesNotUsed(lookAheadPaths) {\n  return every(lookAheadPaths, singleAltPaths => every(singleAltPaths, singlePath => every(singlePath, token => isEmpty(token.categoryMatches))));\n}", "map": {"version": 3, "names": ["every", "flatten", "for<PERSON>ach", "has", "isEmpty", "map", "reduce", "possiblePathsFrom", "<PERSON><PERSON><PERSON><PERSON>", "tokenStructuredMatcher", "tokenStructuredMatcherNoCategories", "Alternation", "Alternative", "AlternativeGAST", "GAstVisitor", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "PROD_TYPE", "getProdType", "prod", "OPTION", "REPETITION", "REPETITION_MANDATORY", "REPETITION_MANDATORY_WITH_SEPARATOR", "REPETITION_WITH_SEPARATOR", "ALTERNATION", "Error", "get<PERSON><PERSON><PERSON>eadPath<PERSON>", "options", "occurrence", "rule", "prodType", "max<PERSON><PERSON><PERSON><PERSON>", "type", "getLookaheadPathsForOr", "getLookaheadPathsForOptionalProd", "buildLookaheadFuncForOr", "ruleGrammar", "hasPredicates", "dynamicTokensEnabled", "laFuncBuilder", "lookAheadPaths", "tokenMatcher", "areTokenCategoriesNotUsed", "buildLookaheadFuncForOptionalProd", "k", "lookaheadBuilder", "buildAlternativesLookAheadFunc", "alts", "numOfAlts", "length", "areAllOneTokenLookahead", "currAlt", "currPath", "orAlts", "predicates", "GATE", "t", "currNumOfPaths", "currPredicate", "undefined", "call", "nextPath", "j", "curr<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "nextToken", "LA", "singleTokenAlts", "choiceToAlt", "result", "idx", "currTokType", "tokenTypeIdx", "categoryMatches", "currExtendingType", "buildSingleAlternativeLookaheadFunction", "alt", "numOfPaths", "singleTokensTypes", "expectedTokenType", "expectedTokenUniqueKey", "RestDefinitionFinderWalker", "constructor", "topProd", "targetOccurrence", "targetProdType", "startWalking", "walk", "restDef", "checkIsTarget", "node", "expectedProdType", "currRest", "prevRest", "concat", "walkOption", "optionProd", "walkAtLeastOne", "atLeastOneProd", "walkAtLeastOneSep", "atLeastOneSepProd", "walkMany", "manyProd", "walkManySep", "manySepProd", "InsideDefinitionFinderVisitor", "targetRef", "expectedProdName", "definition", "visitOption", "visitRepetition", "visitRepetitionMandatory", "visitRepetitionMandatoryWithSeparator", "visitRepetitionWithSeparator", "visitAlternation", "initializeArrayOfArrays", "size", "Array", "pathToHashKeys", "path", "keys", "tokType", "longerKeys", "curr<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "categoriesKeySuffix", "isUniquePrefixHash", "altKnownPathsKeys", "searchPath<PERSON>eys", "currAltIdx", "otherAltKnownPathsKeys", "searchIdx", "search<PERSON>ey", "lookAheadSequenceFromAlternatives", "altsDefs", "partialAlts", "finalResult", "altsHashes", "currAltPaths", "dict", "item", "partialPath", "curr<PERSON><PERSON>", "newData", "<PERSON><PERSON><PERSON><PERSON>", "currData<PERSON>", "altIdx", "currAltPathsAndSuffixes", "currPathIdx", "currPathPrefix", "suffixDef", "prefixKeys", "isUnique", "currAltResult", "containsPath", "newPartialPathsAndSuffixes", "key", "<PERSON><PERSON><PERSON>", "visitor", "accept", "insideDefVisitor", "insideDef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afterDef", "insideFlat", "after<PERSON>lat", "alternative", "searchPath", "compareOtherPath", "otherPath", "searchTok", "otherTok", "matchingTokens", "categoryMatchesMap", "isStrictPrefixOfPath", "prefix", "other", "otherTokType", "singleAltPaths", "singlePath", "token"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/lookahead.ts"], "sourcesContent": ["import { every, flatten, forEach, has, isEmpty, map, reduce } from \"lodash-es\";\nimport { possiblePathsFrom } from \"./interpreter.js\";\nimport { RestWalker } from \"./rest.js\";\nimport { Predicate, TokenMatcher } from \"../parser/parser.js\";\nimport {\n  tokenStructuredMatcher,\n  tokenStructuredMatcherNoCategories,\n} from \"../../scan/tokens.js\";\nimport {\n  Alternation,\n  Alternative as AlternativeGAST,\n  GAstVisitor,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n} from \"@chevrotain/gast\";\nimport {\n  BaseParser,\n  IOrAlt,\n  IProduction,\n  IProductionWithOccurrence,\n  LookaheadProductionType,\n  LookaheadSequence,\n  Rule,\n  TokenType,\n} from \"@chevrotain/types\";\n\nexport enum PROD_TYPE {\n  OPTION,\n  REPETITION,\n  REPETITION_MANDATORY,\n  REPETITION_MANDATORY_WITH_SEPARATOR,\n  REPETITION_WITH_SEPARATOR,\n  ALTERNATION,\n}\n\nexport function getProdType(\n  prod: IProduction | LookaheadProductionType,\n): PROD_TYPE {\n  /* istanbul ignore else */\n  if (prod instanceof Option || prod === \"Option\") {\n    return PROD_TYPE.OPTION;\n  } else if (prod instanceof Repetition || prod === \"Repetition\") {\n    return PROD_TYPE.REPETITION;\n  } else if (\n    prod instanceof RepetitionMandatory ||\n    prod === \"RepetitionMandatory\"\n  ) {\n    return PROD_TYPE.REPETITION_MANDATORY;\n  } else if (\n    prod instanceof RepetitionMandatoryWithSeparator ||\n    prod === \"RepetitionMandatoryWithSeparator\"\n  ) {\n    return PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR;\n  } else if (\n    prod instanceof RepetitionWithSeparator ||\n    prod === \"RepetitionWithSeparator\"\n  ) {\n    return PROD_TYPE.REPETITION_WITH_SEPARATOR;\n  } else if (prod instanceof Alternation || prod === \"Alternation\") {\n    return PROD_TYPE.ALTERNATION;\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n\nexport function getLookaheadPaths(options: {\n  occurrence: number;\n  rule: Rule;\n  prodType: LookaheadProductionType;\n  maxLookahead: number;\n}): LookaheadSequence[] {\n  const { occurrence, rule, prodType, maxLookahead } = options;\n  const type = getProdType(prodType);\n  if (type === PROD_TYPE.ALTERNATION) {\n    return getLookaheadPathsForOr(occurrence, rule, maxLookahead);\n  } else {\n    return getLookaheadPathsForOptionalProd(\n      occurrence,\n      rule,\n      type,\n      maxLookahead,\n    );\n  }\n}\n\nexport function buildLookaheadFuncForOr(\n  occurrence: number,\n  ruleGrammar: Rule,\n  maxLookahead: number,\n  hasPredicates: boolean,\n  dynamicTokensEnabled: boolean,\n  laFuncBuilder: Function,\n): (orAlts?: IOrAlt<any>[]) => number | undefined {\n  const lookAheadPaths = getLookaheadPathsForOr(\n    occurrence,\n    ruleGrammar,\n    maxLookahead,\n  );\n\n  const tokenMatcher = areTokenCategoriesNotUsed(lookAheadPaths)\n    ? tokenStructuredMatcherNoCategories\n    : tokenStructuredMatcher;\n\n  return laFuncBuilder(\n    lookAheadPaths,\n    hasPredicates,\n    tokenMatcher,\n    dynamicTokensEnabled,\n  );\n}\n\n/**\n *  When dealing with an Optional production (OPTION/MANY/2nd iteration of AT_LEAST_ONE/...) we need to compare\n *  the lookahead \"inside\" the production and the lookahead immediately \"after\" it in the same top level rule (context free).\n *\n *  Example: given a production:\n *  ABC(DE)?DF\n *\n *  The optional '(DE)?' should only be entered if we see 'DE'. a single Token 'D' is not sufficient to distinguish between the two\n *  alternatives.\n *\n *  @returns A Lookahead function which will return true IFF the parser should parse the Optional production.\n */\nexport function buildLookaheadFuncForOptionalProd(\n  occurrence: number,\n  ruleGrammar: Rule,\n  k: number,\n  dynamicTokensEnabled: boolean,\n  prodType: PROD_TYPE,\n  lookaheadBuilder: (\n    lookAheadSequence: LookaheadSequence,\n    tokenMatcher: TokenMatcher,\n    dynamicTokensEnabled: boolean,\n  ) => () => boolean,\n): () => boolean {\n  const lookAheadPaths = getLookaheadPathsForOptionalProd(\n    occurrence,\n    ruleGrammar,\n    prodType,\n    k,\n  );\n\n  const tokenMatcher = areTokenCategoriesNotUsed(lookAheadPaths)\n    ? tokenStructuredMatcherNoCategories\n    : tokenStructuredMatcher;\n\n  return lookaheadBuilder(\n    lookAheadPaths[0],\n    tokenMatcher,\n    dynamicTokensEnabled,\n  );\n}\n\nexport type Alternative = TokenType[][];\n\nexport function buildAlternativesLookAheadFunc(\n  alts: LookaheadSequence[],\n  hasPredicates: boolean,\n  tokenMatcher: TokenMatcher,\n  dynamicTokensEnabled: boolean,\n): (orAlts: IOrAlt<any>[]) => number | undefined {\n  const numOfAlts = alts.length;\n  const areAllOneTokenLookahead = every(alts, (currAlt) => {\n    return every(currAlt, (currPath) => {\n      return currPath.length === 1;\n    });\n  });\n\n  // This version takes into account the predicates as well.\n  if (hasPredicates) {\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function (\n      this: BaseParser,\n      orAlts: IOrAlt<any>[],\n    ): number | undefined {\n      // unfortunately the predicates must be extracted every single time\n      // as they cannot be cached due to references to parameters(vars) which are no longer valid.\n      // note that in the common case of no predicates, no cpu time will be wasted on this (see else block)\n      const predicates: (Predicate | undefined)[] = map(\n        orAlts,\n        (currAlt) => currAlt.GATE,\n      );\n\n      for (let t = 0; t < numOfAlts; t++) {\n        const currAlt = alts[t];\n        const currNumOfPaths = currAlt.length;\n\n        const currPredicate = predicates[t];\n        if (currPredicate !== undefined && currPredicate.call(this) === false) {\n          // if the predicate does not match there is no point in checking the paths\n          continue;\n        }\n        nextPath: for (let j = 0; j < currNumOfPaths; j++) {\n          const currPath = currAlt[j];\n          const currPathLength = currPath.length;\n          for (let i = 0; i < currPathLength; i++) {\n            const nextToken = this.LA(i + 1);\n            if (tokenMatcher(nextToken, currPath[i]) === false) {\n              // mismatch in current path\n              // try the next pth\n              continue nextPath;\n            }\n          }\n          // found a full path that matches.\n          // this will also work for an empty ALT as the loop will be skipped\n          return t;\n        }\n        // none of the paths for the current alternative matched\n        // try the next alternative\n      }\n      // none of the alternatives could be matched\n      return undefined;\n    };\n  } else if (areAllOneTokenLookahead && !dynamicTokensEnabled) {\n    // optimized (common) case of all the lookaheads paths requiring only\n    // a single token lookahead. These Optimizations cannot work if dynamically defined Tokens are used.\n    const singleTokenAlts = map(alts, (currAlt) => {\n      return flatten(currAlt);\n    });\n\n    const choiceToAlt = reduce(\n      singleTokenAlts,\n      (result, currAlt, idx) => {\n        forEach(currAlt, (currTokType) => {\n          if (!has(result, currTokType.tokenTypeIdx!)) {\n            result[currTokType.tokenTypeIdx!] = idx;\n          }\n          forEach(currTokType.categoryMatches!, (currExtendingType) => {\n            if (!has(result, currExtendingType)) {\n              result[currExtendingType] = idx;\n            }\n          });\n        });\n        return result;\n      },\n      {} as Record<number, number>,\n    );\n\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function (this: BaseParser): number {\n      const nextToken = this.LA(1);\n      return choiceToAlt[nextToken.tokenTypeIdx];\n    };\n  } else {\n    // optimized lookahead without needing to check the predicates at all.\n    // this causes code duplication which is intentional to improve performance.\n    /**\n     * @returns {number} - The chosen alternative index\n     */\n    return function (this: BaseParser): number | undefined {\n      for (let t = 0; t < numOfAlts; t++) {\n        const currAlt = alts[t];\n        const currNumOfPaths = currAlt.length;\n        nextPath: for (let j = 0; j < currNumOfPaths; j++) {\n          const currPath = currAlt[j];\n          const currPathLength = currPath.length;\n          for (let i = 0; i < currPathLength; i++) {\n            const nextToken = this.LA(i + 1);\n            if (tokenMatcher(nextToken, currPath[i]) === false) {\n              // mismatch in current path\n              // try the next pth\n              continue nextPath;\n            }\n          }\n          // found a full path that matches.\n          // this will also work for an empty ALT as the loop will be skipped\n          return t;\n        }\n        // none of the paths for the current alternative matched\n        // try the next alternative\n      }\n      // none of the alternatives could be matched\n      return undefined;\n    };\n  }\n}\n\nexport function buildSingleAlternativeLookaheadFunction(\n  alt: LookaheadSequence,\n  tokenMatcher: TokenMatcher,\n  dynamicTokensEnabled: boolean,\n): () => boolean {\n  const areAllOneTokenLookahead = every(alt, (currPath) => {\n    return currPath.length === 1;\n  });\n\n  const numOfPaths = alt.length;\n\n  // optimized (common) case of all the lookaheads paths requiring only\n  // a single token lookahead.\n  if (areAllOneTokenLookahead && !dynamicTokensEnabled) {\n    const singleTokensTypes = flatten(alt);\n\n    if (\n      singleTokensTypes.length === 1 &&\n      isEmpty((<any>singleTokensTypes[0]).categoryMatches)\n    ) {\n      const expectedTokenType = singleTokensTypes[0];\n      const expectedTokenUniqueKey = (<any>expectedTokenType).tokenTypeIdx;\n\n      return function (this: BaseParser): boolean {\n        return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;\n      };\n    } else {\n      const choiceToAlt = reduce(\n        singleTokensTypes,\n        (result, currTokType, idx) => {\n          result[currTokType.tokenTypeIdx!] = true;\n          forEach(currTokType.categoryMatches!, (currExtendingType) => {\n            result[currExtendingType] = true;\n          });\n          return result;\n        },\n        [] as boolean[],\n      );\n\n      return function (this: BaseParser): boolean {\n        const nextToken = this.LA(1);\n        return choiceToAlt[nextToken.tokenTypeIdx] === true;\n      };\n    }\n  } else {\n    return function (this: BaseParser): boolean {\n      nextPath: for (let j = 0; j < numOfPaths; j++) {\n        const currPath = alt[j];\n        const currPathLength = currPath.length;\n        for (let i = 0; i < currPathLength; i++) {\n          const nextToken = this.LA(i + 1);\n          if (tokenMatcher(nextToken, currPath[i]) === false) {\n            // mismatch in current path\n            // try the next pth\n            continue nextPath;\n          }\n        }\n        // found a full path that matches.\n        return true;\n      }\n\n      // none of the paths matched\n      return false;\n    };\n  }\n}\n\nclass RestDefinitionFinderWalker extends RestWalker {\n  private restDef: IProduction[];\n\n  constructor(\n    private topProd: Rule,\n    private targetOccurrence: number,\n    private targetProdType: PROD_TYPE,\n  ) {\n    super();\n  }\n\n  startWalking(): IProduction[] {\n    this.walk(this.topProd);\n    return this.restDef;\n  }\n\n  private checkIsTarget(\n    node: IProductionWithOccurrence,\n    expectedProdType: PROD_TYPE,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): boolean {\n    if (\n      node.idx === this.targetOccurrence &&\n      this.targetProdType === expectedProdType\n    ) {\n      this.restDef = currRest.concat(prevRest);\n      return true;\n    }\n    // performance optimization, do not iterate over the entire Grammar ast after we have found the target\n    return false;\n  }\n\n  walkOption(\n    optionProd: Option,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (!this.checkIsTarget(optionProd, PROD_TYPE.OPTION, currRest, prevRest)) {\n      super.walkOption(optionProd, currRest, prevRest);\n    }\n  }\n\n  walkAtLeastOne(\n    atLeastOneProd: RepetitionMandatory,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (\n      !this.checkIsTarget(\n        atLeastOneProd,\n        PROD_TYPE.REPETITION_MANDATORY,\n        currRest,\n        prevRest,\n      )\n    ) {\n      super.walkOption(atLeastOneProd, currRest, prevRest);\n    }\n  }\n\n  walkAtLeastOneSep(\n    atLeastOneSepProd: RepetitionMandatoryWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (\n      !this.checkIsTarget(\n        atLeastOneSepProd,\n        PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR,\n        currRest,\n        prevRest,\n      )\n    ) {\n      super.walkOption(atLeastOneSepProd, currRest, prevRest);\n    }\n  }\n\n  walkMany(\n    manyProd: Repetition,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (\n      !this.checkIsTarget(manyProd, PROD_TYPE.REPETITION, currRest, prevRest)\n    ) {\n      super.walkOption(manyProd, currRest, prevRest);\n    }\n  }\n\n  walkManySep(\n    manySepProd: RepetitionWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (\n      !this.checkIsTarget(\n        manySepProd,\n        PROD_TYPE.REPETITION_WITH_SEPARATOR,\n        currRest,\n        prevRest,\n      )\n    ) {\n      super.walkOption(manySepProd, currRest, prevRest);\n    }\n  }\n}\n\n/**\n * Returns the definition of a target production in a top level level rule.\n */\nclass InsideDefinitionFinderVisitor extends GAstVisitor {\n  public result: IProduction[] = [];\n\n  constructor(\n    private targetOccurrence: number,\n    private targetProdType: PROD_TYPE,\n    private targetRef?: any,\n  ) {\n    super();\n  }\n\n  private checkIsTarget(\n    node: { definition: IProduction[] } & IProductionWithOccurrence,\n    expectedProdName: PROD_TYPE,\n  ): void {\n    if (\n      node.idx === this.targetOccurrence &&\n      this.targetProdType === expectedProdName &&\n      (this.targetRef === undefined || node === this.targetRef)\n    ) {\n      this.result = node.definition;\n    }\n  }\n\n  public visitOption(node: Option): void {\n    this.checkIsTarget(node, PROD_TYPE.OPTION);\n  }\n\n  public visitRepetition(node: Repetition): void {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION);\n  }\n\n  public visitRepetitionMandatory(node: RepetitionMandatory): void {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_MANDATORY);\n  }\n\n  public visitRepetitionMandatoryWithSeparator(\n    node: RepetitionMandatoryWithSeparator,\n  ): void {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR);\n  }\n\n  public visitRepetitionWithSeparator(node: RepetitionWithSeparator): void {\n    this.checkIsTarget(node, PROD_TYPE.REPETITION_WITH_SEPARATOR);\n  }\n\n  public visitAlternation(node: Alternation): void {\n    this.checkIsTarget(node, PROD_TYPE.ALTERNATION);\n  }\n}\n\nfunction initializeArrayOfArrays(size: number): any[][] {\n  const result = new Array(size);\n  for (let i = 0; i < size; i++) {\n    result[i] = [];\n  }\n  return result;\n}\n\n/**\n * A sort of hash function between a Path in the grammar and a string.\n * Note that this returns multiple \"hashes\" to support the scenario of token categories.\n * -  A single path with categories may match multiple **actual** paths.\n */\nfunction pathToHashKeys(path: TokenType[]): string[] {\n  let keys = [\"\"];\n  for (let i = 0; i < path.length; i++) {\n    const tokType = path[i];\n    const longerKeys = [];\n    for (let j = 0; j < keys.length; j++) {\n      const currShorterKey = keys[j];\n      longerKeys.push(currShorterKey + \"_\" + tokType.tokenTypeIdx);\n      for (let t = 0; t < tokType.categoryMatches!.length; t++) {\n        const categoriesKeySuffix = \"_\" + tokType.categoryMatches![t];\n        longerKeys.push(currShorterKey + categoriesKeySuffix);\n      }\n    }\n    keys = longerKeys;\n  }\n  return keys;\n}\n\n/**\n * Imperative style due to being called from a hot spot\n */\nfunction isUniquePrefixHash(\n  altKnownPathsKeys: Record<string, boolean>[],\n  searchPathKeys: string[],\n  idx: number,\n): boolean {\n  for (\n    let currAltIdx = 0;\n    currAltIdx < altKnownPathsKeys.length;\n    currAltIdx++\n  ) {\n    // We only want to test vs the other alternatives\n    if (currAltIdx === idx) {\n      continue;\n    }\n    const otherAltKnownPathsKeys = altKnownPathsKeys[currAltIdx];\n    for (let searchIdx = 0; searchIdx < searchPathKeys.length; searchIdx++) {\n      const searchKey = searchPathKeys[searchIdx];\n      if (otherAltKnownPathsKeys[searchKey] === true) {\n        return false;\n      }\n    }\n  }\n  // None of the SearchPathKeys were found in any of the other alternatives\n  return true;\n}\n\nexport function lookAheadSequenceFromAlternatives(\n  altsDefs: IProduction[],\n  k: number,\n): LookaheadSequence[] {\n  const partialAlts = map(altsDefs, (currAlt) =>\n    possiblePathsFrom([currAlt], 1),\n  );\n  const finalResult = initializeArrayOfArrays(partialAlts.length);\n  const altsHashes = map(partialAlts, (currAltPaths) => {\n    const dict: { [key: string]: boolean } = {};\n    forEach(currAltPaths, (item) => {\n      const keys = pathToHashKeys(item.partialPath);\n      forEach(keys, (currKey) => {\n        dict[currKey] = true;\n      });\n    });\n    return dict;\n  });\n  let newData = partialAlts;\n\n  // maxLookahead loop\n  for (let pathLength = 1; pathLength <= k; pathLength++) {\n    const currDataset = newData;\n    newData = initializeArrayOfArrays(currDataset.length);\n\n    // alternatives loop\n    for (let altIdx = 0; altIdx < currDataset.length; altIdx++) {\n      const currAltPathsAndSuffixes = currDataset[altIdx];\n      // paths in current alternative loop\n      for (\n        let currPathIdx = 0;\n        currPathIdx < currAltPathsAndSuffixes.length;\n        currPathIdx++\n      ) {\n        const currPathPrefix = currAltPathsAndSuffixes[currPathIdx].partialPath;\n        const suffixDef = currAltPathsAndSuffixes[currPathIdx].suffixDef;\n        const prefixKeys = pathToHashKeys(currPathPrefix);\n        const isUnique = isUniquePrefixHash(altsHashes, prefixKeys, altIdx);\n        // End of the line for this path.\n        if (isUnique || isEmpty(suffixDef) || currPathPrefix.length === k) {\n          const currAltResult = finalResult[altIdx];\n          // TODO: Can we implement a containsPath using Maps/Dictionaries?\n          if (containsPath(currAltResult, currPathPrefix) === false) {\n            currAltResult.push(currPathPrefix);\n            // Update all new  keys for the current path.\n            for (let j = 0; j < prefixKeys.length; j++) {\n              const currKey = prefixKeys[j];\n              altsHashes[altIdx][currKey] = true;\n            }\n          }\n        }\n        // Expand longer paths\n        else {\n          const newPartialPathsAndSuffixes = possiblePathsFrom(\n            suffixDef,\n            pathLength + 1,\n            currPathPrefix,\n          );\n          newData[altIdx] = newData[altIdx].concat(newPartialPathsAndSuffixes);\n\n          // Update keys for new known paths\n          forEach(newPartialPathsAndSuffixes, (item) => {\n            const prefixKeys = pathToHashKeys(item.partialPath);\n            forEach(prefixKeys, (key) => {\n              altsHashes[altIdx][key] = true;\n            });\n          });\n        }\n      }\n    }\n  }\n\n  return finalResult;\n}\n\nexport function getLookaheadPathsForOr(\n  occurrence: number,\n  ruleGrammar: Rule,\n  k: number,\n  orProd?: Alternation,\n): LookaheadSequence[] {\n  const visitor = new InsideDefinitionFinderVisitor(\n    occurrence,\n    PROD_TYPE.ALTERNATION,\n    orProd,\n  );\n  ruleGrammar.accept(visitor);\n  return lookAheadSequenceFromAlternatives(visitor.result, k);\n}\n\nexport function getLookaheadPathsForOptionalProd(\n  occurrence: number,\n  ruleGrammar: Rule,\n  prodType: PROD_TYPE,\n  k: number,\n): LookaheadSequence[] {\n  const insideDefVisitor = new InsideDefinitionFinderVisitor(\n    occurrence,\n    prodType,\n  );\n  ruleGrammar.accept(insideDefVisitor);\n  const insideDef = insideDefVisitor.result;\n\n  const afterDefWalker = new RestDefinitionFinderWalker(\n    ruleGrammar,\n    occurrence,\n    prodType,\n  );\n  const afterDef = afterDefWalker.startWalking();\n\n  const insideFlat = new AlternativeGAST({ definition: insideDef });\n  const afterFlat = new AlternativeGAST({ definition: afterDef });\n\n  return lookAheadSequenceFromAlternatives([insideFlat, afterFlat], k);\n}\n\nexport function containsPath(\n  alternative: Alternative,\n  searchPath: TokenType[],\n): boolean {\n  compareOtherPath: for (let i = 0; i < alternative.length; i++) {\n    const otherPath = alternative[i];\n    if (otherPath.length !== searchPath.length) {\n      continue;\n    }\n    for (let j = 0; j < otherPath.length; j++) {\n      const searchTok = searchPath[j];\n      const otherTok = otherPath[j];\n\n      const matchingTokens =\n        searchTok === otherTok ||\n        otherTok.categoryMatchesMap![searchTok.tokenTypeIdx!] !== undefined;\n      if (matchingTokens === false) {\n        continue compareOtherPath;\n      }\n    }\n    return true;\n  }\n\n  return false;\n}\n\nexport function isStrictPrefixOfPath(\n  prefix: TokenType[],\n  other: TokenType[],\n): boolean {\n  return (\n    prefix.length < other.length &&\n    every(prefix, (tokType, idx) => {\n      const otherTokType = other[idx];\n      return (\n        tokType === otherTokType ||\n        otherTokType.categoryMatchesMap![tokType.tokenTypeIdx!]\n      );\n    })\n  );\n}\n\nexport function areTokenCategoriesNotUsed(\n  lookAheadPaths: LookaheadSequence[],\n): boolean {\n  return every(lookAheadPaths, (singleAltPaths) =>\n    every(singleAltPaths, (singlePath) =>\n      every(singlePath, (token) => isEmpty(token.categoryMatches!)),\n    ),\n  );\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,WAAW;AAC9E,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,UAAU,QAAQ,WAAW;AAEtC,SACEC,sBAAsB,EACtBC,kCAAkC,QAC7B,sBAAsB;AAC7B,SACEC,WAAW,EACXC,WAAW,IAAIC,eAAe,EAC9BC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,QAClB,kBAAkB;AAYzB,WAAYC,SAOX;AAPD,WAAYA,SAAS;EACnBA,SAAA,CAAAA,SAAA,0BAAM;EACNA,SAAA,CAAAA,SAAA,kCAAU;EACVA,SAAA,CAAAA,SAAA,sDAAoB;EACpBA,SAAA,CAAAA,SAAA,oFAAmC;EACnCA,SAAA,CAAAA,SAAA,gEAAyB;EACzBA,SAAA,CAAAA,SAAA,oCAAW;AACb,CAAC,EAPWA,SAAS,KAATA,SAAS;AASrB,OAAM,SAAUC,WAAWA,CACzBC,IAA2C;EAE3C;EACA,IAAIA,IAAI,YAAYP,MAAM,IAAIO,IAAI,KAAK,QAAQ,EAAE;IAC/C,OAAOF,SAAS,CAACG,MAAM;GACxB,MAAM,IAAID,IAAI,YAAYN,UAAU,IAAIM,IAAI,KAAK,YAAY,EAAE;IAC9D,OAAOF,SAAS,CAACI,UAAU;GAC5B,MAAM,IACLF,IAAI,YAAYL,mBAAmB,IACnCK,IAAI,KAAK,qBAAqB,EAC9B;IACA,OAAOF,SAAS,CAACK,oBAAoB;GACtC,MAAM,IACLH,IAAI,YAAYJ,gCAAgC,IAChDI,IAAI,KAAK,kCAAkC,EAC3C;IACA,OAAOF,SAAS,CAACM,mCAAmC;GACrD,MAAM,IACLJ,IAAI,YAAYH,uBAAuB,IACvCG,IAAI,KAAK,yBAAyB,EAClC;IACA,OAAOF,SAAS,CAACO,yBAAyB;GAC3C,MAAM,IAAIL,IAAI,YAAYX,WAAW,IAAIW,IAAI,KAAK,aAAa,EAAE;IAChE,OAAOF,SAAS,CAACQ,WAAW;GAC7B,MAAM;IACL,MAAMC,KAAK,CAAC,sBAAsB,CAAC;;AAEvC;AAEA,OAAM,SAAUC,iBAAiBA,CAACC,OAKjC;EACC,MAAM;IAAEC,UAAU;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAY,CAAE,GAAGJ,OAAO;EAC5D,MAAMK,IAAI,GAAGf,WAAW,CAACa,QAAQ,CAAC;EAClC,IAAIE,IAAI,KAAKhB,SAAS,CAACQ,WAAW,EAAE;IAClC,OAAOS,sBAAsB,CAACL,UAAU,EAAEC,IAAI,EAAEE,YAAY,CAAC;GAC9D,MAAM;IACL,OAAOG,gCAAgC,CACrCN,UAAU,EACVC,IAAI,EACJG,IAAI,EACJD,YAAY,CACb;;AAEL;AAEA,OAAM,SAAUI,uBAAuBA,CACrCP,UAAkB,EAClBQ,WAAiB,EACjBL,YAAoB,EACpBM,aAAsB,EACtBC,oBAA6B,EAC7BC,aAAuB;EAEvB,MAAMC,cAAc,GAAGP,sBAAsB,CAC3CL,UAAU,EACVQ,WAAW,EACXL,YAAY,CACb;EAED,MAAMU,YAAY,GAAGC,yBAAyB,CAACF,cAAc,CAAC,GAC1DlC,kCAAkC,GAClCD,sBAAsB;EAE1B,OAAOkC,aAAa,CAClBC,cAAc,EACdH,aAAa,EACbI,YAAY,EACZH,oBAAoB,CACrB;AACH;AAEA;;;;;;;;;;;;AAYA,OAAM,SAAUK,iCAAiCA,CAC/Cf,UAAkB,EAClBQ,WAAiB,EACjBQ,CAAS,EACTN,oBAA6B,EAC7BR,QAAmB,EACnBe,gBAIkB;EAElB,MAAML,cAAc,GAAGN,gCAAgC,CACrDN,UAAU,EACVQ,WAAW,EACXN,QAAQ,EACRc,CAAC,CACF;EAED,MAAMH,YAAY,GAAGC,yBAAyB,CAACF,cAAc,CAAC,GAC1DlC,kCAAkC,GAClCD,sBAAsB;EAE1B,OAAOwC,gBAAgB,CACrBL,cAAc,CAAC,CAAC,CAAC,EACjBC,YAAY,EACZH,oBAAoB,CACrB;AACH;AAIA,OAAM,SAAUQ,8BAA8BA,CAC5CC,IAAyB,EACzBV,aAAsB,EACtBI,YAA0B,EAC1BH,oBAA6B;EAE7B,MAAMU,SAAS,GAAGD,IAAI,CAACE,MAAM;EAC7B,MAAMC,uBAAuB,GAAGtD,KAAK,CAACmD,IAAI,EAAGI,OAAO,IAAI;IACtD,OAAOvD,KAAK,CAACuD,OAAO,EAAGC,QAAQ,IAAI;MACjC,OAAOA,QAAQ,CAACH,MAAM,KAAK,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF;EACA,IAAIZ,aAAa,EAAE;IACjB;;;IAGA,OAAO,UAELgB,MAAqB;MAErB;MACA;MACA;MACA,MAAMC,UAAU,GAA8BrD,GAAG,CAC/CoD,MAAM,EACLF,OAAO,IAAKA,OAAO,CAACI,IAAI,CAC1B;MAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,EAAEQ,CAAC,EAAE,EAAE;QAClC,MAAML,OAAO,GAAGJ,IAAI,CAACS,CAAC,CAAC;QACvB,MAAMC,cAAc,GAAGN,OAAO,CAACF,MAAM;QAErC,MAAMS,aAAa,GAAGJ,UAAU,CAACE,CAAC,CAAC;QACnC,IAAIE,aAAa,KAAKC,SAAS,IAAID,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;UACrE;UACA;;QAEFC,QAAQ,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,EAAEK,CAAC,EAAE,EAAE;UACjD,MAAMV,QAAQ,GAAGD,OAAO,CAACW,CAAC,CAAC;UAC3B,MAAMC,cAAc,GAAGX,QAAQ,CAACH,MAAM;UACtC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,EAAEC,CAAC,EAAE,EAAE;YACvC,MAAMC,SAAS,GAAG,IAAI,CAACC,EAAE,CAACF,CAAC,GAAG,CAAC,CAAC;YAChC,IAAIvB,YAAY,CAACwB,SAAS,EAAEb,QAAQ,CAACY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;cAClD;cACA;cACA,SAASH,QAAQ;;;UAGrB;UACA;UACA,OAAOL,CAAC;;QAEV;QACA;;MAEF;MACA,OAAOG,SAAS;IAClB,CAAC;GACF,MAAM,IAAIT,uBAAuB,IAAI,CAACZ,oBAAoB,EAAE;IAC3D;IACA;IACA,MAAM6B,eAAe,GAAGlE,GAAG,CAAC8C,IAAI,EAAGI,OAAO,IAAI;MAC5C,OAAOtD,OAAO,CAACsD,OAAO,CAAC;IACzB,CAAC,CAAC;IAEF,MAAMiB,WAAW,GAAGlE,MAAM,CACxBiE,eAAe,EACf,CAACE,MAAM,EAAElB,OAAO,EAAEmB,GAAG,KAAI;MACvBxE,OAAO,CAACqD,OAAO,EAAGoB,WAAW,IAAI;QAC/B,IAAI,CAACxE,GAAG,CAACsE,MAAM,EAAEE,WAAW,CAACC,YAAa,CAAC,EAAE;UAC3CH,MAAM,CAACE,WAAW,CAACC,YAAa,CAAC,GAAGF,GAAG;;QAEzCxE,OAAO,CAACyE,WAAW,CAACE,eAAgB,EAAGC,iBAAiB,IAAI;UAC1D,IAAI,CAAC3E,GAAG,CAACsE,MAAM,EAAEK,iBAAiB,CAAC,EAAE;YACnCL,MAAM,CAACK,iBAAiB,CAAC,GAAGJ,GAAG;;QAEnC,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOD,MAAM;IACf,CAAC,EACD,EAA4B,CAC7B;IAED;;;IAGA,OAAO;MACL,MAAMJ,SAAS,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;MAC5B,OAAOE,WAAW,CAACH,SAAS,CAACO,YAAY,CAAC;IAC5C,CAAC;GACF,MAAM;IACL;IACA;IACA;;;IAGA,OAAO;MACL,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,EAAEQ,CAAC,EAAE,EAAE;QAClC,MAAML,OAAO,GAAGJ,IAAI,CAACS,CAAC,CAAC;QACvB,MAAMC,cAAc,GAAGN,OAAO,CAACF,MAAM;QACrCY,QAAQ,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,EAAEK,CAAC,EAAE,EAAE;UACjD,MAAMV,QAAQ,GAAGD,OAAO,CAACW,CAAC,CAAC;UAC3B,MAAMC,cAAc,GAAGX,QAAQ,CAACH,MAAM;UACtC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,EAAEC,CAAC,EAAE,EAAE;YACvC,MAAMC,SAAS,GAAG,IAAI,CAACC,EAAE,CAACF,CAAC,GAAG,CAAC,CAAC;YAChC,IAAIvB,YAAY,CAACwB,SAAS,EAAEb,QAAQ,CAACY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;cAClD;cACA;cACA,SAASH,QAAQ;;;UAGrB;UACA;UACA,OAAOL,CAAC;;QAEV;QACA;;MAEF;MACA,OAAOG,SAAS;IAClB,CAAC;;AAEL;AAEA,OAAM,SAAUgB,uCAAuCA,CACrDC,GAAsB,EACtBnC,YAA0B,EAC1BH,oBAA6B;EAE7B,MAAMY,uBAAuB,GAAGtD,KAAK,CAACgF,GAAG,EAAGxB,QAAQ,IAAI;IACtD,OAAOA,QAAQ,CAACH,MAAM,KAAK,CAAC;EAC9B,CAAC,CAAC;EAEF,MAAM4B,UAAU,GAAGD,GAAG,CAAC3B,MAAM;EAE7B;EACA;EACA,IAAIC,uBAAuB,IAAI,CAACZ,oBAAoB,EAAE;IACpD,MAAMwC,iBAAiB,GAAGjF,OAAO,CAAC+E,GAAG,CAAC;IAEtC,IACEE,iBAAiB,CAAC7B,MAAM,KAAK,CAAC,IAC9BjD,OAAO,CAAO8E,iBAAiB,CAAC,CAAC,CAAE,CAACL,eAAe,CAAC,EACpD;MACA,MAAMM,iBAAiB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC9C,MAAME,sBAAsB,GAASD,iBAAkB,CAACP,YAAY;MAEpE,OAAO;QACL,OAAO,IAAI,CAACN,EAAE,CAAC,CAAC,CAAC,CAACM,YAAY,KAAKQ,sBAAsB;MAC3D,CAAC;KACF,MAAM;MACL,MAAMZ,WAAW,GAAGlE,MAAM,CACxB4E,iBAAiB,EACjB,CAACT,MAAM,EAAEE,WAAW,EAAED,GAAG,KAAI;QAC3BD,MAAM,CAACE,WAAW,CAACC,YAAa,CAAC,GAAG,IAAI;QACxC1E,OAAO,CAACyE,WAAW,CAACE,eAAgB,EAAGC,iBAAiB,IAAI;UAC1DL,MAAM,CAACK,iBAAiB,CAAC,GAAG,IAAI;QAClC,CAAC,CAAC;QACF,OAAOL,MAAM;MACf,CAAC,EACD,EAAe,CAChB;MAED,OAAO;QACL,MAAMJ,SAAS,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;QAC5B,OAAOE,WAAW,CAACH,SAAS,CAACO,YAAY,CAAC,KAAK,IAAI;MACrD,CAAC;;GAEJ,MAAM;IACL,OAAO;MACLX,QAAQ,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,UAAU,EAAEf,CAAC,EAAE,EAAE;QAC7C,MAAMV,QAAQ,GAAGwB,GAAG,CAACd,CAAC,CAAC;QACvB,MAAMC,cAAc,GAAGX,QAAQ,CAACH,MAAM;QACtC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,EAAEC,CAAC,EAAE,EAAE;UACvC,MAAMC,SAAS,GAAG,IAAI,CAACC,EAAE,CAACF,CAAC,GAAG,CAAC,CAAC;UAChC,IAAIvB,YAAY,CAACwB,SAAS,EAAEb,QAAQ,CAACY,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;YAClD;YACA;YACA,SAASH,QAAQ;;;QAGrB;QACA,OAAO,IAAI;;MAGb;MACA,OAAO,KAAK;IACd,CAAC;;AAEL;AAEA,MAAMoB,0BAA2B,SAAQ7E,UAAU;EAGjD8E,YACUC,OAAa,EACbC,gBAAwB,EACxBC,cAAyB;IAEjC,KAAK,EAAE;IAJC,KAAAF,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;EAGxB;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACC,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;IACvB,OAAO,IAAI,CAACK,OAAO;EACrB;EAEQC,aAAaA,CACnBC,IAA+B,EAC/BC,gBAA2B,EAC3BC,QAAuB,EACvBC,QAAuB;IAEvB,IACEH,IAAI,CAACpB,GAAG,KAAK,IAAI,CAACc,gBAAgB,IAClC,IAAI,CAACC,cAAc,KAAKM,gBAAgB,EACxC;MACA,IAAI,CAACH,OAAO,GAAGI,QAAQ,CAACE,MAAM,CAACD,QAAQ,CAAC;MACxC,OAAO,IAAI;;IAEb;IACA,OAAO,KAAK;EACd;EAEAE,UAAUA,CACRC,UAAkB,EAClBJ,QAAuB,EACvBC,QAAuB;IAEvB,IAAI,CAAC,IAAI,CAACJ,aAAa,CAACO,UAAU,EAAEhF,SAAS,CAACG,MAAM,EAAEyE,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MACzE,KAAK,CAACE,UAAU,CAACC,UAAU,EAAEJ,QAAQ,EAAEC,QAAQ,CAAC;;EAEpD;EAEAI,cAAcA,CACZC,cAAmC,EACnCN,QAAuB,EACvBC,QAAuB;IAEvB,IACE,CAAC,IAAI,CAACJ,aAAa,CACjBS,cAAc,EACdlF,SAAS,CAACK,oBAAoB,EAC9BuE,QAAQ,EACRC,QAAQ,CACT,EACD;MACA,KAAK,CAACE,UAAU,CAACG,cAAc,EAAEN,QAAQ,EAAEC,QAAQ,CAAC;;EAExD;EAEAM,iBAAiBA,CACfC,iBAAmD,EACnDR,QAAuB,EACvBC,QAAuB;IAEvB,IACE,CAAC,IAAI,CAACJ,aAAa,CACjBW,iBAAiB,EACjBpF,SAAS,CAACM,mCAAmC,EAC7CsE,QAAQ,EACRC,QAAQ,CACT,EACD;MACA,KAAK,CAACE,UAAU,CAACK,iBAAiB,EAAER,QAAQ,EAAEC,QAAQ,CAAC;;EAE3D;EAEAQ,QAAQA,CACNC,QAAoB,EACpBV,QAAuB,EACvBC,QAAuB;IAEvB,IACE,CAAC,IAAI,CAACJ,aAAa,CAACa,QAAQ,EAAEtF,SAAS,CAACI,UAAU,EAAEwE,QAAQ,EAAEC,QAAQ,CAAC,EACvE;MACA,KAAK,CAACE,UAAU,CAACO,QAAQ,EAAEV,QAAQ,EAAEC,QAAQ,CAAC;;EAElD;EAEAU,WAAWA,CACTC,WAAoC,EACpCZ,QAAuB,EACvBC,QAAuB;IAEvB,IACE,CAAC,IAAI,CAACJ,aAAa,CACjBe,WAAW,EACXxF,SAAS,CAACO,yBAAyB,EACnCqE,QAAQ,EACRC,QAAQ,CACT,EACD;MACA,KAAK,CAACE,UAAU,CAACS,WAAW,EAAEZ,QAAQ,EAAEC,QAAQ,CAAC;;EAErD;;AAGF;;;AAGA,MAAMY,6BAA8B,SAAQ/F,WAAW;EAGrDwE,YACUE,gBAAwB,EACxBC,cAAyB,EACzBqB,SAAe;IAEvB,KAAK,EAAE;IAJC,KAAAtB,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAqB,SAAS,GAATA,SAAS;IALZ,KAAArC,MAAM,GAAkB,EAAE;EAQjC;EAEQoB,aAAaA,CACnBC,IAA+D,EAC/DiB,gBAA2B;IAE3B,IACEjB,IAAI,CAACpB,GAAG,KAAK,IAAI,CAACc,gBAAgB,IAClC,IAAI,CAACC,cAAc,KAAKsB,gBAAgB,KACvC,IAAI,CAACD,SAAS,KAAK/C,SAAS,IAAI+B,IAAI,KAAK,IAAI,CAACgB,SAAS,CAAC,EACzD;MACA,IAAI,CAACrC,MAAM,GAAGqB,IAAI,CAACkB,UAAU;;EAEjC;EAEOC,WAAWA,CAACnB,IAAY;IAC7B,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACG,MAAM,CAAC;EAC5C;EAEO2F,eAAeA,CAACpB,IAAgB;IACrC,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACI,UAAU,CAAC;EAChD;EAEO2F,wBAAwBA,CAACrB,IAAyB;IACvD,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACK,oBAAoB,CAAC;EAC1D;EAEO2F,qCAAqCA,CAC1CtB,IAAsC;IAEtC,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACM,mCAAmC,CAAC;EACzE;EAEO2F,4BAA4BA,CAACvB,IAA6B;IAC/D,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACO,yBAAyB,CAAC;EAC/D;EAEO2F,gBAAgBA,CAACxB,IAAiB;IACvC,IAAI,CAACD,aAAa,CAACC,IAAI,EAAE1E,SAAS,CAACQ,WAAW,CAAC;EACjD;;AAGF,SAAS2F,uBAAuBA,CAACC,IAAY;EAC3C,MAAM/C,MAAM,GAAG,IAAIgD,KAAK,CAACD,IAAI,CAAC;EAC9B,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,IAAI,EAAEpD,CAAC,EAAE,EAAE;IAC7BK,MAAM,CAACL,CAAC,CAAC,GAAG,EAAE;;EAEhB,OAAOK,MAAM;AACf;AAEA;;;;;AAKA,SAASiD,cAAcA,CAACC,IAAiB;EACvC,IAAIC,IAAI,GAAG,CAAC,EAAE,CAAC;EACf,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,IAAI,CAACtE,MAAM,EAAEe,CAAC,EAAE,EAAE;IACpC,MAAMyD,OAAO,GAAGF,IAAI,CAACvD,CAAC,CAAC;IACvB,MAAM0D,UAAU,GAAG,EAAE;IACrB,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,IAAI,CAACvE,MAAM,EAAEa,CAAC,EAAE,EAAE;MACpC,MAAM6D,cAAc,GAAGH,IAAI,CAAC1D,CAAC,CAAC;MAC9B4D,UAAU,CAACE,IAAI,CAACD,cAAc,GAAG,GAAG,GAAGF,OAAO,CAACjD,YAAY,CAAC;MAC5D,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,OAAO,CAAChD,eAAgB,CAACxB,MAAM,EAAEO,CAAC,EAAE,EAAE;QACxD,MAAMqE,mBAAmB,GAAG,GAAG,GAAGJ,OAAO,CAAChD,eAAgB,CAACjB,CAAC,CAAC;QAC7DkE,UAAU,CAACE,IAAI,CAACD,cAAc,GAAGE,mBAAmB,CAAC;;;IAGzDL,IAAI,GAAGE,UAAU;;EAEnB,OAAOF,IAAI;AACb;AAEA;;;AAGA,SAASM,kBAAkBA,CACzBC,iBAA4C,EAC5CC,cAAwB,EACxB1D,GAAW;EAEX,KACE,IAAI2D,UAAU,GAAG,CAAC,EAClBA,UAAU,GAAGF,iBAAiB,CAAC9E,MAAM,EACrCgF,UAAU,EAAE,EACZ;IACA;IACA,IAAIA,UAAU,KAAK3D,GAAG,EAAE;MACtB;;IAEF,MAAM4D,sBAAsB,GAAGH,iBAAiB,CAACE,UAAU,CAAC;IAC5D,KAAK,IAAIE,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGH,cAAc,CAAC/E,MAAM,EAAEkF,SAAS,EAAE,EAAE;MACtE,MAAMC,SAAS,GAAGJ,cAAc,CAACG,SAAS,CAAC;MAC3C,IAAID,sBAAsB,CAACE,SAAS,CAAC,KAAK,IAAI,EAAE;QAC9C,OAAO,KAAK;;;;EAIlB;EACA,OAAO,IAAI;AACb;AAEA,OAAM,SAAUC,iCAAiCA,CAC/CC,QAAuB,EACvB1F,CAAS;EAET,MAAM2F,WAAW,GAAGtI,GAAG,CAACqI,QAAQ,EAAGnF,OAAO,IACxChD,iBAAiB,CAAC,CAACgD,OAAO,CAAC,EAAE,CAAC,CAAC,CAChC;EACD,MAAMqF,WAAW,GAAGrB,uBAAuB,CAACoB,WAAW,CAACtF,MAAM,CAAC;EAC/D,MAAMwF,UAAU,GAAGxI,GAAG,CAACsI,WAAW,EAAGG,YAAY,IAAI;IACnD,MAAMC,IAAI,GAA+B,EAAE;IAC3C7I,OAAO,CAAC4I,YAAY,EAAGE,IAAI,IAAI;MAC7B,MAAMpB,IAAI,GAAGF,cAAc,CAACsB,IAAI,CAACC,WAAW,CAAC;MAC7C/I,OAAO,CAAC0H,IAAI,EAAGsB,OAAO,IAAI;QACxBH,IAAI,CAACG,OAAO,CAAC,GAAG,IAAI;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOH,IAAI;EACb,CAAC,CAAC;EACF,IAAII,OAAO,GAAGR,WAAW;EAEzB;EACA,KAAK,IAAIS,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAIpG,CAAC,EAAEoG,UAAU,EAAE,EAAE;IACtD,MAAMC,WAAW,GAAGF,OAAO;IAC3BA,OAAO,GAAG5B,uBAAuB,CAAC8B,WAAW,CAAChG,MAAM,CAAC;IAErD;IACA,KAAK,IAAIiG,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGD,WAAW,CAAChG,MAAM,EAAEiG,MAAM,EAAE,EAAE;MAC1D,MAAMC,uBAAuB,GAAGF,WAAW,CAACC,MAAM,CAAC;MACnD;MACA,KACE,IAAIE,WAAW,GAAG,CAAC,EACnBA,WAAW,GAAGD,uBAAuB,CAAClG,MAAM,EAC5CmG,WAAW,EAAE,EACb;QACA,MAAMC,cAAc,GAAGF,uBAAuB,CAACC,WAAW,CAAC,CAACP,WAAW;QACvE,MAAMS,SAAS,GAAGH,uBAAuB,CAACC,WAAW,CAAC,CAACE,SAAS;QAChE,MAAMC,UAAU,GAAGjC,cAAc,CAAC+B,cAAc,CAAC;QACjD,MAAMG,QAAQ,GAAG1B,kBAAkB,CAACW,UAAU,EAAEc,UAAU,EAAEL,MAAM,CAAC;QACnE;QACA,IAAIM,QAAQ,IAAIxJ,OAAO,CAACsJ,SAAS,CAAC,IAAID,cAAc,CAACpG,MAAM,KAAKL,CAAC,EAAE;UACjE,MAAM6G,aAAa,GAAGjB,WAAW,CAACU,MAAM,CAAC;UACzC;UACA,IAAIQ,YAAY,CAACD,aAAa,EAAEJ,cAAc,CAAC,KAAK,KAAK,EAAE;YACzDI,aAAa,CAAC7B,IAAI,CAACyB,cAAc,CAAC;YAClC;YACA,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyF,UAAU,CAACtG,MAAM,EAAEa,CAAC,EAAE,EAAE;cAC1C,MAAMgF,OAAO,GAAGS,UAAU,CAACzF,CAAC,CAAC;cAC7B2E,UAAU,CAACS,MAAM,CAAC,CAACJ,OAAO,CAAC,GAAG,IAAI;;;;QAIxC;QAAA,KACK;UACH,MAAMa,0BAA0B,GAAGxJ,iBAAiB,CAClDmJ,SAAS,EACTN,UAAU,GAAG,CAAC,EACdK,cAAc,CACf;UACDN,OAAO,CAACG,MAAM,CAAC,GAAGH,OAAO,CAACG,MAAM,CAAC,CAACpD,MAAM,CAAC6D,0BAA0B,CAAC;UAEpE;UACA7J,OAAO,CAAC6J,0BAA0B,EAAGf,IAAI,IAAI;YAC3C,MAAMW,UAAU,GAAGjC,cAAc,CAACsB,IAAI,CAACC,WAAW,CAAC;YACnD/I,OAAO,CAACyJ,UAAU,EAAGK,GAAG,IAAI;cAC1BnB,UAAU,CAACS,MAAM,CAAC,CAACU,GAAG,CAAC,GAAG,IAAI;YAChC,CAAC,CAAC;UACJ,CAAC,CAAC;;;;;EAMV,OAAOpB,WAAW;AACpB;AAEA,OAAM,SAAUvG,sBAAsBA,CACpCL,UAAkB,EAClBQ,WAAiB,EACjBQ,CAAS,EACTiH,MAAoB;EAEpB,MAAMC,OAAO,GAAG,IAAIrD,6BAA6B,CAC/C7E,UAAU,EACVZ,SAAS,CAACQ,WAAW,EACrBqI,MAAM,CACP;EACDzH,WAAW,CAAC2H,MAAM,CAACD,OAAO,CAAC;EAC3B,OAAOzB,iCAAiC,CAACyB,OAAO,CAACzF,MAAM,EAAEzB,CAAC,CAAC;AAC7D;AAEA,OAAM,SAAUV,gCAAgCA,CAC9CN,UAAkB,EAClBQ,WAAiB,EACjBN,QAAmB,EACnBc,CAAS;EAET,MAAMoH,gBAAgB,GAAG,IAAIvD,6BAA6B,CACxD7E,UAAU,EACVE,QAAQ,CACT;EACDM,WAAW,CAAC2H,MAAM,CAACC,gBAAgB,CAAC;EACpC,MAAMC,SAAS,GAAGD,gBAAgB,CAAC3F,MAAM;EAEzC,MAAM6F,cAAc,GAAG,IAAIjF,0BAA0B,CACnD7C,WAAW,EACXR,UAAU,EACVE,QAAQ,CACT;EACD,MAAMqI,QAAQ,GAAGD,cAAc,CAAC5E,YAAY,EAAE;EAE9C,MAAM8E,UAAU,GAAG,IAAI3J,eAAe,CAAC;IAAEmG,UAAU,EAAEqD;EAAS,CAAE,CAAC;EACjE,MAAMI,SAAS,GAAG,IAAI5J,eAAe,CAAC;IAAEmG,UAAU,EAAEuD;EAAQ,CAAE,CAAC;EAE/D,OAAO9B,iCAAiC,CAAC,CAAC+B,UAAU,EAAEC,SAAS,CAAC,EAAEzH,CAAC,CAAC;AACtE;AAEA,OAAM,SAAU8G,YAAYA,CAC1BY,WAAwB,EACxBC,UAAuB;EAEvBC,gBAAgB,EAAE,KAAK,IAAIxG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsG,WAAW,CAACrH,MAAM,EAAEe,CAAC,EAAE,EAAE;IAC7D,MAAMyG,SAAS,GAAGH,WAAW,CAACtG,CAAC,CAAC;IAChC,IAAIyG,SAAS,CAACxH,MAAM,KAAKsH,UAAU,CAACtH,MAAM,EAAE;MAC1C;;IAEF,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2G,SAAS,CAACxH,MAAM,EAAEa,CAAC,EAAE,EAAE;MACzC,MAAM4G,SAAS,GAAGH,UAAU,CAACzG,CAAC,CAAC;MAC/B,MAAM6G,QAAQ,GAAGF,SAAS,CAAC3G,CAAC,CAAC;MAE7B,MAAM8G,cAAc,GAClBF,SAAS,KAAKC,QAAQ,IACtBA,QAAQ,CAACE,kBAAmB,CAACH,SAAS,CAAClG,YAAa,CAAC,KAAKb,SAAS;MACrE,IAAIiH,cAAc,KAAK,KAAK,EAAE;QAC5B,SAASJ,gBAAgB;;;IAG7B,OAAO,IAAI;;EAGb,OAAO,KAAK;AACd;AAEA,OAAM,SAAUM,oBAAoBA,CAClCC,MAAmB,EACnBC,KAAkB;EAElB,OACED,MAAM,CAAC9H,MAAM,GAAG+H,KAAK,CAAC/H,MAAM,IAC5BrD,KAAK,CAACmL,MAAM,EAAE,CAACtD,OAAO,EAAEnD,GAAG,KAAI;IAC7B,MAAM2G,YAAY,GAAGD,KAAK,CAAC1G,GAAG,CAAC;IAC/B,OACEmD,OAAO,KAAKwD,YAAY,IACxBA,YAAY,CAACJ,kBAAmB,CAACpD,OAAO,CAACjD,YAAa,CAAC;EAE3D,CAAC,CAAC;AAEN;AAEA,OAAM,SAAU9B,yBAAyBA,CACvCF,cAAmC;EAEnC,OAAO5C,KAAK,CAAC4C,cAAc,EAAG0I,cAAc,IAC1CtL,KAAK,CAACsL,cAAc,EAAGC,UAAU,IAC/BvL,KAAK,CAACuL,UAAU,EAAGC,KAAK,IAAKpL,OAAO,CAACoL,KAAK,CAAC3G,eAAgB,CAAC,CAAC,CAC9D,CACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}