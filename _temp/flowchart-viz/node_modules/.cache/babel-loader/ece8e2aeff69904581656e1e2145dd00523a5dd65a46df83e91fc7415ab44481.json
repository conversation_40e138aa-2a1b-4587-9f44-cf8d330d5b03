{"ast": null, "code": "/******************************************************************************\n * Copyright 2024 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CompositeCstNodeImpl, LeafCstNodeImpl, RootCstNodeImpl } from '../parser/cst-node-builder.js';\nimport { isAbstractElement } from '../languages/generated/ast.js';\nimport { isRootCstNode, isCompositeCstNode, isLeafCstNode, isAstNode, isReference } from '../syntax-tree.js';\nimport { streamAst } from '../utils/ast-utils.js';\nimport { BiMap } from '../utils/collections.js';\nimport { streamCst } from '../utils/cst-utils.js';\nexport class DefaultHydrator {\n  constructor(services) {\n    this.grammarElementIdMap = new BiMap();\n    this.tokenTypeIdMap = new BiMap();\n    this.grammar = services.Grammar;\n    this.lexer = services.parser.Lexer;\n    this.linker = services.references.Linker;\n  }\n  dehydrate(result) {\n    return {\n      lexerErrors: result.lexerErrors,\n      lexerReport: result.lexerReport ? this.dehydrateLexerReport(result.lexerReport) : undefined,\n      // We need to create shallow copies of the errors\n      // The original errors inherit from the `Error` class, which is not transferable across worker threads\n      parserErrors: result.parserErrors.map(e => Object.assign(Object.assign({}, e), {\n        message: e.message\n      })),\n      value: this.dehydrateAstNode(result.value, this.createDehyrationContext(result.value))\n    };\n  }\n  dehydrateLexerReport(lexerReport) {\n    // By default, lexer reports are serializable\n    return lexerReport;\n  }\n  createDehyrationContext(node) {\n    const astNodes = new Map();\n    const cstNodes = new Map();\n    for (const astNode of streamAst(node)) {\n      astNodes.set(astNode, {});\n    }\n    if (node.$cstNode) {\n      for (const cstNode of streamCst(node.$cstNode)) {\n        cstNodes.set(cstNode, {});\n      }\n    }\n    return {\n      astNodes,\n      cstNodes\n    };\n  }\n  dehydrateAstNode(node, context) {\n    const obj = context.astNodes.get(node);\n    obj.$type = node.$type;\n    obj.$containerIndex = node.$containerIndex;\n    obj.$containerProperty = node.$containerProperty;\n    if (node.$cstNode !== undefined) {\n      obj.$cstNode = this.dehydrateCstNode(node.$cstNode, context);\n    }\n    for (const [name, value] of Object.entries(node)) {\n      if (name.startsWith('$')) {\n        continue;\n      }\n      if (Array.isArray(value)) {\n        const arr = [];\n        obj[name] = arr;\n        for (const item of value) {\n          if (isAstNode(item)) {\n            arr.push(this.dehydrateAstNode(item, context));\n          } else if (isReference(item)) {\n            arr.push(this.dehydrateReference(item, context));\n          } else {\n            arr.push(item);\n          }\n        }\n      } else if (isAstNode(value)) {\n        obj[name] = this.dehydrateAstNode(value, context);\n      } else if (isReference(value)) {\n        obj[name] = this.dehydrateReference(value, context);\n      } else if (value !== undefined) {\n        obj[name] = value;\n      }\n    }\n    return obj;\n  }\n  dehydrateReference(reference, context) {\n    const obj = {};\n    obj.$refText = reference.$refText;\n    if (reference.$refNode) {\n      obj.$refNode = context.cstNodes.get(reference.$refNode);\n    }\n    return obj;\n  }\n  dehydrateCstNode(node, context) {\n    const cstNode = context.cstNodes.get(node);\n    if (isRootCstNode(node)) {\n      cstNode.fullText = node.fullText;\n    } else {\n      // Note: This returns undefined for hidden nodes (i.e. comments)\n      cstNode.grammarSource = this.getGrammarElementId(node.grammarSource);\n    }\n    cstNode.hidden = node.hidden;\n    cstNode.astNode = context.astNodes.get(node.astNode);\n    if (isCompositeCstNode(node)) {\n      cstNode.content = node.content.map(child => this.dehydrateCstNode(child, context));\n    } else if (isLeafCstNode(node)) {\n      cstNode.tokenType = node.tokenType.name;\n      cstNode.offset = node.offset;\n      cstNode.length = node.length;\n      cstNode.startLine = node.range.start.line;\n      cstNode.startColumn = node.range.start.character;\n      cstNode.endLine = node.range.end.line;\n      cstNode.endColumn = node.range.end.character;\n    }\n    return cstNode;\n  }\n  hydrate(result) {\n    const node = result.value;\n    const context = this.createHydrationContext(node);\n    if ('$cstNode' in node) {\n      this.hydrateCstNode(node.$cstNode, context);\n    }\n    return {\n      lexerErrors: result.lexerErrors,\n      lexerReport: result.lexerReport,\n      parserErrors: result.parserErrors,\n      value: this.hydrateAstNode(node, context)\n    };\n  }\n  createHydrationContext(node) {\n    const astNodes = new Map();\n    const cstNodes = new Map();\n    for (const astNode of streamAst(node)) {\n      astNodes.set(astNode, {});\n    }\n    let root;\n    if (node.$cstNode) {\n      for (const cstNode of streamCst(node.$cstNode)) {\n        let cst;\n        if ('fullText' in cstNode) {\n          cst = new RootCstNodeImpl(cstNode.fullText);\n          root = cst;\n        } else if ('content' in cstNode) {\n          cst = new CompositeCstNodeImpl();\n        } else if ('tokenType' in cstNode) {\n          cst = this.hydrateCstLeafNode(cstNode);\n        }\n        if (cst) {\n          cstNodes.set(cstNode, cst);\n          cst.root = root;\n        }\n      }\n    }\n    return {\n      astNodes,\n      cstNodes\n    };\n  }\n  hydrateAstNode(node, context) {\n    const astNode = context.astNodes.get(node);\n    astNode.$type = node.$type;\n    astNode.$containerIndex = node.$containerIndex;\n    astNode.$containerProperty = node.$containerProperty;\n    if (node.$cstNode) {\n      astNode.$cstNode = context.cstNodes.get(node.$cstNode);\n    }\n    for (const [name, value] of Object.entries(node)) {\n      if (name.startsWith('$')) {\n        continue;\n      }\n      if (Array.isArray(value)) {\n        const arr = [];\n        astNode[name] = arr;\n        for (const item of value) {\n          if (isAstNode(item)) {\n            arr.push(this.setParent(this.hydrateAstNode(item, context), astNode));\n          } else if (isReference(item)) {\n            arr.push(this.hydrateReference(item, astNode, name, context));\n          } else {\n            arr.push(item);\n          }\n        }\n      } else if (isAstNode(value)) {\n        astNode[name] = this.setParent(this.hydrateAstNode(value, context), astNode);\n      } else if (isReference(value)) {\n        astNode[name] = this.hydrateReference(value, astNode, name, context);\n      } else if (value !== undefined) {\n        astNode[name] = value;\n      }\n    }\n    return astNode;\n  }\n  setParent(node, parent) {\n    node.$container = parent;\n    return node;\n  }\n  hydrateReference(reference, node, name, context) {\n    return this.linker.buildReference(node, name, context.cstNodes.get(reference.$refNode), reference.$refText);\n  }\n  hydrateCstNode(cstNode, context, num = 0) {\n    const cstNodeObj = context.cstNodes.get(cstNode);\n    if (typeof cstNode.grammarSource === 'number') {\n      cstNodeObj.grammarSource = this.getGrammarElement(cstNode.grammarSource);\n    }\n    cstNodeObj.astNode = context.astNodes.get(cstNode.astNode);\n    if (isCompositeCstNode(cstNodeObj)) {\n      for (const child of cstNode.content) {\n        const hydrated = this.hydrateCstNode(child, context, num++);\n        cstNodeObj.content.push(hydrated);\n      }\n    }\n    return cstNodeObj;\n  }\n  hydrateCstLeafNode(cstNode) {\n    const tokenType = this.getTokenType(cstNode.tokenType);\n    const offset = cstNode.offset;\n    const length = cstNode.length;\n    const startLine = cstNode.startLine;\n    const startColumn = cstNode.startColumn;\n    const endLine = cstNode.endLine;\n    const endColumn = cstNode.endColumn;\n    const hidden = cstNode.hidden;\n    const node = new LeafCstNodeImpl(offset, length, {\n      start: {\n        line: startLine,\n        character: startColumn\n      },\n      end: {\n        line: endLine,\n        character: endColumn\n      }\n    }, tokenType, hidden);\n    return node;\n  }\n  getTokenType(name) {\n    return this.lexer.definition[name];\n  }\n  getGrammarElementId(node) {\n    if (!node) {\n      return undefined;\n    }\n    if (this.grammarElementIdMap.size === 0) {\n      this.createGrammarElementIdMap();\n    }\n    return this.grammarElementIdMap.get(node);\n  }\n  getGrammarElement(id) {\n    if (this.grammarElementIdMap.size === 0) {\n      this.createGrammarElementIdMap();\n    }\n    const element = this.grammarElementIdMap.getKey(id);\n    return element;\n  }\n  createGrammarElementIdMap() {\n    let id = 0;\n    for (const element of streamAst(this.grammar)) {\n      if (isAbstractElement(element)) {\n        this.grammarElementIdMap.set(element, id++);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["CompositeCstNodeImpl", "LeafCstNodeImpl", "RootCstNodeImpl", "isAbstractElement", "isRootCstNode", "isCompositeCstNode", "isLeafCstNode", "isAstNode", "isReference", "streamAst", "BiMap", "streamCst", "DefaultHydrator", "constructor", "services", "grammarElementIdMap", "tokenTypeIdMap", "grammar", "Grammar", "lexer", "parser", "<PERSON><PERSON>", "linker", "references", "<PERSON><PERSON>", "dehydrate", "result", "lexerErrors", "lexerReport", "dehydrateLexerReport", "undefined", "parserErrors", "map", "e", "Object", "assign", "message", "value", "dehydrateAstNode", "createDehyrationContext", "node", "astNodes", "Map", "cstNodes", "astNode", "set", "$cstNode", "cstNode", "context", "obj", "get", "$type", "$containerIndex", "$containerProperty", "dehydrateCstNode", "name", "entries", "startsWith", "Array", "isArray", "arr", "item", "push", "dehydrateReference", "reference", "$refText", "$refNode", "fullText", "grammarSource", "getGrammarElementId", "hidden", "content", "child", "tokenType", "offset", "length", "startLine", "range", "start", "line", "startColumn", "character", "endLine", "end", "endColumn", "hydrate", "createHydrationContext", "hydrateCstNode", "hydrateAstNode", "root", "cst", "hydrateCstLeafNode", "setParent", "hydrateReference", "parent", "$container", "buildReference", "num", "cstNodeObj", "getGrammarElement", "hydrated", "getTokenType", "definition", "size", "createGrammarElementIdMap", "id", "element", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/serializer/hydrator.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2024 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n\r\nimport type { TokenType } from 'chevrotain';\r\nimport { CompositeCstNodeImpl, LeafCstNodeImpl, RootCstNodeImpl } from '../parser/cst-node-builder.js';\r\nimport { isAbstractElement, type AbstractElement, type Grammar } from '../languages/generated/ast.js';\r\nimport type { Linker } from '../references/linker.js';\r\nimport type { Lexer } from '../parser/lexer.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { ParseResult } from '../parser/langium-parser.js';\r\nimport type { Reference, AstNode, CstNode, LeafCstNode, GenericAstNode, Mutable, RootCstNode } from '../syntax-tree.js';\r\nimport { isRootCstNode, isCompositeCstNode, isLeafCstNode, isAstNode, isReference } from '../syntax-tree.js';\r\nimport { streamAst } from '../utils/ast-utils.js';\r\nimport { BiMap } from '../utils/collections.js';\r\nimport { streamCst } from '../utils/cst-utils.js';\r\nimport type { LexingReport } from '../parser/token-builder.js';\r\n\r\n/**\r\n * The hydrator service is responsible for allowing AST parse results to be sent across worker threads.\r\n */\r\nexport interface Hydrator {\r\n    /**\r\n     * Converts a parse result to a plain object. The resulting object can be sent across worker threads.\r\n     */\r\n    dehydrate(result: ParseResult<AstNode>): ParseResult<object>;\r\n    /**\r\n     * Converts a plain object to a parse result. The included AST node can then be used in the main thread.\r\n     * Calling this method on objects that have not been dehydrated first will result in undefined behavior.\r\n     */\r\n    hydrate<T extends AstNode = AstNode>(result: ParseResult<object>): ParseResult<T>;\r\n}\r\n\r\nexport interface DehydrateContext {\r\n    astNodes: Map<AstNode, any>;\r\n    cstNodes: Map<CstNode, any>;\r\n}\r\n\r\nexport interface HydrateContext {\r\n    astNodes: Map<any, AstNode>;\r\n    cstNodes: Map<any, CstNode>;\r\n}\r\n\r\nexport class DefaultHydrator implements Hydrator {\r\n\r\n    protected readonly grammar: Grammar;\r\n    protected readonly lexer: Lexer;\r\n    protected readonly linker: Linker;\r\n\r\n    protected readonly grammarElementIdMap = new BiMap<AbstractElement, number>();\r\n    protected readonly tokenTypeIdMap = new BiMap<number, TokenType>();\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.grammar = services.Grammar;\r\n        this.lexer = services.parser.Lexer;\r\n        this.linker = services.references.Linker;\r\n    }\r\n\r\n    dehydrate(result: ParseResult<AstNode>): ParseResult<object> {\r\n        return {\r\n            lexerErrors: result.lexerErrors,\r\n            lexerReport: result.lexerReport ? this.dehydrateLexerReport(result.lexerReport) : undefined,\r\n            // We need to create shallow copies of the errors\r\n            // The original errors inherit from the `Error` class, which is not transferable across worker threads\r\n            parserErrors: result.parserErrors.map(e => ({ ...e, message: e.message })),\r\n            value: this.dehydrateAstNode(result.value, this.createDehyrationContext(result.value))\r\n        };\r\n    }\r\n\r\n    protected dehydrateLexerReport(lexerReport: LexingReport): LexingReport {\r\n        // By default, lexer reports are serializable\r\n        return lexerReport;\r\n    }\r\n\r\n    protected createDehyrationContext(node: AstNode): DehydrateContext {\r\n        const astNodes = new Map<AstNode, any>();\r\n        const cstNodes = new Map<CstNode, any>();\r\n        for (const astNode of streamAst(node)) {\r\n            astNodes.set(astNode, {});\r\n        }\r\n        if (node.$cstNode) {\r\n            for (const cstNode of streamCst(node.$cstNode)) {\r\n                cstNodes.set(cstNode, {});\r\n            }\r\n        }\r\n        return {\r\n            astNodes,\r\n            cstNodes\r\n        };\r\n    }\r\n\r\n    protected dehydrateAstNode(node: AstNode, context: DehydrateContext): object {\r\n        const obj = context.astNodes.get(node) as Record<string, any>;\r\n        obj.$type = node.$type;\r\n        obj.$containerIndex = node.$containerIndex;\r\n        obj.$containerProperty = node.$containerProperty;\r\n        if (node.$cstNode !== undefined) {\r\n            obj.$cstNode = this.dehydrateCstNode(node.$cstNode, context);\r\n        }\r\n        for (const [name, value] of Object.entries(node)) {\r\n            if (name.startsWith('$')) {\r\n                continue;\r\n            }\r\n            if (Array.isArray(value)) {\r\n                const arr: any[] = [];\r\n                obj[name] = arr;\r\n                for (const item of value) {\r\n                    if (isAstNode(item)) {\r\n                        arr.push(this.dehydrateAstNode(item, context));\r\n                    } else if (isReference(item)) {\r\n                        arr.push(this.dehydrateReference(item, context));\r\n                    } else {\r\n                        arr.push(item);\r\n                    }\r\n                }\r\n            } else if (isAstNode(value)) {\r\n                obj[name] = this.dehydrateAstNode(value, context);\r\n            } else if (isReference(value)) {\r\n                obj[name] = this.dehydrateReference(value, context);\r\n            } else if (value !== undefined) {\r\n                obj[name] = value;\r\n            }\r\n        }\r\n        return obj;\r\n    }\r\n\r\n    protected dehydrateReference(reference: Reference, context: DehydrateContext): any {\r\n        const obj: Record<string, unknown> = {};\r\n        obj.$refText = reference.$refText;\r\n        if (reference.$refNode) {\r\n            obj.$refNode = context.cstNodes.get(reference.$refNode);\r\n        }\r\n        return obj;\r\n    }\r\n\r\n    protected dehydrateCstNode(node: CstNode, context: DehydrateContext): any {\r\n        const cstNode = context.cstNodes.get(node) as Record<string, any>;\r\n        if (isRootCstNode(node)) {\r\n            cstNode.fullText = node.fullText;\r\n        } else {\r\n            // Note: This returns undefined for hidden nodes (i.e. comments)\r\n            cstNode.grammarSource = this.getGrammarElementId(node.grammarSource);\r\n        }\r\n        cstNode.hidden = node.hidden;\r\n        cstNode.astNode = context.astNodes.get(node.astNode);\r\n        if (isCompositeCstNode(node)) {\r\n            cstNode.content = node.content.map(child => this.dehydrateCstNode(child, context));\r\n        } else if (isLeafCstNode(node)) {\r\n            cstNode.tokenType = node.tokenType.name;\r\n            cstNode.offset = node.offset;\r\n            cstNode.length = node.length;\r\n            cstNode.startLine = node.range.start.line;\r\n            cstNode.startColumn = node.range.start.character;\r\n            cstNode.endLine = node.range.end.line;\r\n            cstNode.endColumn = node.range.end.character;\r\n        }\r\n        return cstNode;\r\n    }\r\n\r\n    hydrate<T extends AstNode = AstNode>(result: ParseResult<object>): ParseResult<T> {\r\n        const node = result.value;\r\n        const context = this.createHydrationContext(node);\r\n        if ('$cstNode' in node) {\r\n            this.hydrateCstNode(node.$cstNode, context);\r\n        }\r\n        return {\r\n            lexerErrors: result.lexerErrors,\r\n            lexerReport: result.lexerReport,\r\n            parserErrors: result.parserErrors,\r\n            value: this.hydrateAstNode(node, context) as T\r\n        };\r\n    }\r\n\r\n    protected createHydrationContext(node: any): HydrateContext {\r\n        const astNodes = new Map<any, AstNode>();\r\n        const cstNodes = new Map<any, CstNode>();\r\n        for (const astNode of streamAst(node)) {\r\n            astNodes.set(astNode, {} as AstNode);\r\n        }\r\n        let root: RootCstNode;\r\n        if (node.$cstNode) {\r\n            for (const cstNode of streamCst(node.$cstNode)) {\r\n                let cst: Mutable<CstNode> | undefined;\r\n                if ('fullText' in cstNode) {\r\n                    cst = new RootCstNodeImpl(cstNode.fullText as string);\r\n                    root = cst as RootCstNode;\r\n                } else if ('content' in cstNode) {\r\n                    cst = new CompositeCstNodeImpl();\r\n                } else if ('tokenType' in cstNode) {\r\n                    cst = this.hydrateCstLeafNode(cstNode);\r\n                }\r\n                if (cst) {\r\n                    cstNodes.set(cstNode, cst);\r\n                    cst.root = root!;\r\n                }\r\n            }\r\n        }\r\n        return {\r\n            astNodes,\r\n            cstNodes\r\n        };\r\n    }\r\n\r\n    protected hydrateAstNode(node: any, context: HydrateContext): AstNode {\r\n        const astNode = context.astNodes.get(node) as Mutable<GenericAstNode>;\r\n        astNode.$type = node.$type;\r\n        astNode.$containerIndex = node.$containerIndex;\r\n        astNode.$containerProperty = node.$containerProperty;\r\n        if (node.$cstNode) {\r\n            astNode.$cstNode = context.cstNodes.get(node.$cstNode);\r\n        }\r\n        for (const [name, value] of Object.entries(node)) {\r\n            if (name.startsWith('$')) {\r\n                continue;\r\n            }\r\n            if (Array.isArray(value)) {\r\n                const arr: unknown[] = [];\r\n                astNode[name] = arr;\r\n                for (const item of value) {\r\n                    if (isAstNode(item)) {\r\n                        arr.push(this.setParent(this.hydrateAstNode(item, context), astNode));\r\n                    } else if (isReference(item)) {\r\n                        arr.push(this.hydrateReference(item, astNode, name, context));\r\n                    } else {\r\n                        arr.push(item);\r\n                    }\r\n                }\r\n            } else if (isAstNode(value)) {\r\n                astNode[name] = this.setParent(this.hydrateAstNode(value, context), astNode);\r\n            } else if (isReference(value)) {\r\n                astNode[name] = this.hydrateReference(value, astNode, name, context);\r\n            } else if (value !== undefined) {\r\n                astNode[name] = value;\r\n            }\r\n        }\r\n        return astNode;\r\n    }\r\n\r\n    protected setParent(node: any, parent: any): any {\r\n        node.$container = parent as AstNode;\r\n        return node;\r\n    }\r\n\r\n    protected hydrateReference(reference: any, node: AstNode, name: string, context: HydrateContext): Reference {\r\n        return this.linker.buildReference(node, name, context.cstNodes.get(reference.$refNode)!, reference.$refText);\r\n    }\r\n\r\n    protected hydrateCstNode(cstNode: any, context: HydrateContext, num = 0): CstNode {\r\n        const cstNodeObj = context.cstNodes.get(cstNode) as Mutable<CstNode>;\r\n        if (typeof cstNode.grammarSource === 'number') {\r\n            cstNodeObj.grammarSource = this.getGrammarElement(cstNode.grammarSource);\r\n        }\r\n        cstNodeObj.astNode = context.astNodes.get(cstNode.astNode)!;\r\n        if (isCompositeCstNode(cstNodeObj)) {\r\n            for (const child of cstNode.content) {\r\n                const hydrated = this.hydrateCstNode(child, context, num++);\r\n                cstNodeObj.content.push(hydrated);\r\n            }\r\n        }\r\n        return cstNodeObj;\r\n    }\r\n\r\n    protected hydrateCstLeafNode(cstNode: any): LeafCstNode {\r\n        const tokenType = this.getTokenType(cstNode.tokenType);\r\n        const offset = cstNode.offset;\r\n        const length = cstNode.length;\r\n        const startLine = cstNode.startLine;\r\n        const startColumn = cstNode.startColumn;\r\n        const endLine = cstNode.endLine;\r\n        const endColumn = cstNode.endColumn;\r\n        const hidden = cstNode.hidden;\r\n        const node = new LeafCstNodeImpl(\r\n            offset,\r\n            length,\r\n            {\r\n                start: {\r\n                    line: startLine,\r\n                    character: startColumn\r\n                },\r\n                end: {\r\n                    line: endLine,\r\n                    character: endColumn\r\n                }\r\n            },\r\n            tokenType,\r\n            hidden\r\n        );\r\n        return node;\r\n    }\r\n\r\n    protected getTokenType(name: string): TokenType {\r\n        return this.lexer.definition[name];\r\n    }\r\n\r\n    protected getGrammarElementId(node: AbstractElement | undefined): number | undefined {\r\n        if (!node) {\r\n            return undefined;\r\n        }\r\n        if (this.grammarElementIdMap.size === 0) {\r\n            this.createGrammarElementIdMap();\r\n        }\r\n        return this.grammarElementIdMap.get(node);\r\n    }\r\n\r\n    protected getGrammarElement(id: number): AbstractElement | undefined {\r\n        if (this.grammarElementIdMap.size === 0) {\r\n            this.createGrammarElementIdMap();\r\n        }\r\n        const element = this.grammarElementIdMap.getKey(id);\r\n        return element;\r\n    }\r\n\r\n    protected createGrammarElementIdMap(): void {\r\n        let id = 0;\r\n        for (const element of streamAst(this.grammar)) {\r\n            if (isAbstractElement(element)) {\r\n                this.grammarElementIdMap.set(element, id++);\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AASA,SAASA,oBAAoB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,+BAA+B;AACtG,SAASC,iBAAiB,QAA4C,+BAA+B;AAMrG,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,WAAW,QAAQ,mBAAmB;AAC5G,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,SAAS,QAAQ,uBAAuB;AA4BjD,OAAM,MAAOC,eAAe;EASxBC,YAAYC,QAA6B;IAHtB,KAAAC,mBAAmB,GAAG,IAAIL,KAAK,EAA2B;IAC1D,KAAAM,cAAc,GAAG,IAAIN,KAAK,EAAqB;IAG9D,IAAI,CAACO,OAAO,GAAGH,QAAQ,CAACI,OAAO;IAC/B,IAAI,CAACC,KAAK,GAAGL,QAAQ,CAACM,MAAM,CAACC,KAAK;IAClC,IAAI,CAACC,MAAM,GAAGR,QAAQ,CAACS,UAAU,CAACC,MAAM;EAC5C;EAEAC,SAASA,CAACC,MAA4B;IAClC,OAAO;MACHC,WAAW,EAAED,MAAM,CAACC,WAAW;MAC/BC,WAAW,EAAEF,MAAM,CAACE,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,MAAM,CAACE,WAAW,CAAC,GAAGE,SAAS;MAC3F;MACA;MACAC,YAAY,EAAEL,MAAM,CAACK,YAAY,CAACC,GAAG,CAACC,CAAC,IAAIC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMF,CAAC;QAAEG,OAAO,EAAEH,CAAC,CAACG;MAAO,EAAG,CAAC;MAC1EC,KAAK,EAAE,IAAI,CAACC,gBAAgB,CAACZ,MAAM,CAACW,KAAK,EAAE,IAAI,CAACE,uBAAuB,CAACb,MAAM,CAACW,KAAK,CAAC;KACxF;EACL;EAEUR,oBAAoBA,CAACD,WAAyB;IACpD;IACA,OAAOA,WAAW;EACtB;EAEUW,uBAAuBA,CAACC,IAAa;IAC3C,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAgB;IACxC,MAAMC,QAAQ,GAAG,IAAID,GAAG,EAAgB;IACxC,KAAK,MAAME,OAAO,IAAInC,SAAS,CAAC+B,IAAI,CAAC,EAAE;MACnCC,QAAQ,CAACI,GAAG,CAACD,OAAO,EAAE,EAAE,CAAC;IAC7B;IACA,IAAIJ,IAAI,CAACM,QAAQ,EAAE;MACf,KAAK,MAAMC,OAAO,IAAIpC,SAAS,CAAC6B,IAAI,CAACM,QAAQ,CAAC,EAAE;QAC5CH,QAAQ,CAACE,GAAG,CAACE,OAAO,EAAE,EAAE,CAAC;MAC7B;IACJ;IACA,OAAO;MACHN,QAAQ;MACRE;KACH;EACL;EAEUL,gBAAgBA,CAACE,IAAa,EAAEQ,OAAyB;IAC/D,MAAMC,GAAG,GAAGD,OAAO,CAACP,QAAQ,CAACS,GAAG,CAACV,IAAI,CAAwB;IAC7DS,GAAG,CAACE,KAAK,GAAGX,IAAI,CAACW,KAAK;IACtBF,GAAG,CAACG,eAAe,GAAGZ,IAAI,CAACY,eAAe;IAC1CH,GAAG,CAACI,kBAAkB,GAAGb,IAAI,CAACa,kBAAkB;IAChD,IAAIb,IAAI,CAACM,QAAQ,KAAKhB,SAAS,EAAE;MAC7BmB,GAAG,CAACH,QAAQ,GAAG,IAAI,CAACQ,gBAAgB,CAACd,IAAI,CAACM,QAAQ,EAAEE,OAAO,CAAC;IAChE;IACA,KAAK,MAAM,CAACO,IAAI,EAAElB,KAAK,CAAC,IAAIH,MAAM,CAACsB,OAAO,CAAChB,IAAI,CAAC,EAAE;MAC9C,IAAIe,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QACtB;MACJ;MACA,IAAIC,KAAK,CAACC,OAAO,CAACtB,KAAK,CAAC,EAAE;QACtB,MAAMuB,GAAG,GAAU,EAAE;QACrBX,GAAG,CAACM,IAAI,CAAC,GAAGK,GAAG;QACf,KAAK,MAAMC,IAAI,IAAIxB,KAAK,EAAE;UACtB,IAAI9B,SAAS,CAACsD,IAAI,CAAC,EAAE;YACjBD,GAAG,CAACE,IAAI,CAAC,IAAI,CAACxB,gBAAgB,CAACuB,IAAI,EAAEb,OAAO,CAAC,CAAC;UAClD,CAAC,MAAM,IAAIxC,WAAW,CAACqD,IAAI,CAAC,EAAE;YAC1BD,GAAG,CAACE,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAACF,IAAI,EAAEb,OAAO,CAAC,CAAC;UACpD,CAAC,MAAM;YACHY,GAAG,CAACE,IAAI,CAACD,IAAI,CAAC;UAClB;QACJ;MACJ,CAAC,MAAM,IAAItD,SAAS,CAAC8B,KAAK,CAAC,EAAE;QACzBY,GAAG,CAACM,IAAI,CAAC,GAAG,IAAI,CAACjB,gBAAgB,CAACD,KAAK,EAAEW,OAAO,CAAC;MACrD,CAAC,MAAM,IAAIxC,WAAW,CAAC6B,KAAK,CAAC,EAAE;QAC3BY,GAAG,CAACM,IAAI,CAAC,GAAG,IAAI,CAACQ,kBAAkB,CAAC1B,KAAK,EAAEW,OAAO,CAAC;MACvD,CAAC,MAAM,IAAIX,KAAK,KAAKP,SAAS,EAAE;QAC5BmB,GAAG,CAACM,IAAI,CAAC,GAAGlB,KAAK;MACrB;IACJ;IACA,OAAOY,GAAG;EACd;EAEUc,kBAAkBA,CAACC,SAAoB,EAAEhB,OAAyB;IACxE,MAAMC,GAAG,GAA4B,EAAE;IACvCA,GAAG,CAACgB,QAAQ,GAAGD,SAAS,CAACC,QAAQ;IACjC,IAAID,SAAS,CAACE,QAAQ,EAAE;MACpBjB,GAAG,CAACiB,QAAQ,GAAGlB,OAAO,CAACL,QAAQ,CAACO,GAAG,CAACc,SAAS,CAACE,QAAQ,CAAC;IAC3D;IACA,OAAOjB,GAAG;EACd;EAEUK,gBAAgBA,CAACd,IAAa,EAAEQ,OAAyB;IAC/D,MAAMD,OAAO,GAAGC,OAAO,CAACL,QAAQ,CAACO,GAAG,CAACV,IAAI,CAAwB;IACjE,IAAIpC,aAAa,CAACoC,IAAI,CAAC,EAAE;MACrBO,OAAO,CAACoB,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ;IACpC,CAAC,MAAM;MACH;MACApB,OAAO,CAACqB,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAAC7B,IAAI,CAAC4B,aAAa,CAAC;IACxE;IACArB,OAAO,CAACuB,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;IAC5BvB,OAAO,CAACH,OAAO,GAAGI,OAAO,CAACP,QAAQ,CAACS,GAAG,CAACV,IAAI,CAACI,OAAO,CAAC;IACpD,IAAIvC,kBAAkB,CAACmC,IAAI,CAAC,EAAE;MAC1BO,OAAO,CAACwB,OAAO,GAAG/B,IAAI,CAAC+B,OAAO,CAACvC,GAAG,CAACwC,KAAK,IAAI,IAAI,CAAClB,gBAAgB,CAACkB,KAAK,EAAExB,OAAO,CAAC,CAAC;IACtF,CAAC,MAAM,IAAI1C,aAAa,CAACkC,IAAI,CAAC,EAAE;MAC5BO,OAAO,CAAC0B,SAAS,GAAGjC,IAAI,CAACiC,SAAS,CAAClB,IAAI;MACvCR,OAAO,CAAC2B,MAAM,GAAGlC,IAAI,CAACkC,MAAM;MAC5B3B,OAAO,CAAC4B,MAAM,GAAGnC,IAAI,CAACmC,MAAM;MAC5B5B,OAAO,CAAC6B,SAAS,GAAGpC,IAAI,CAACqC,KAAK,CAACC,KAAK,CAACC,IAAI;MACzChC,OAAO,CAACiC,WAAW,GAAGxC,IAAI,CAACqC,KAAK,CAACC,KAAK,CAACG,SAAS;MAChDlC,OAAO,CAACmC,OAAO,GAAG1C,IAAI,CAACqC,KAAK,CAACM,GAAG,CAACJ,IAAI;MACrChC,OAAO,CAACqC,SAAS,GAAG5C,IAAI,CAACqC,KAAK,CAACM,GAAG,CAACF,SAAS;IAChD;IACA,OAAOlC,OAAO;EAClB;EAEAsC,OAAOA,CAA8B3D,MAA2B;IAC5D,MAAMc,IAAI,GAAGd,MAAM,CAACW,KAAK;IACzB,MAAMW,OAAO,GAAG,IAAI,CAACsC,sBAAsB,CAAC9C,IAAI,CAAC;IACjD,IAAI,UAAU,IAAIA,IAAI,EAAE;MACpB,IAAI,CAAC+C,cAAc,CAAC/C,IAAI,CAACM,QAAQ,EAAEE,OAAO,CAAC;IAC/C;IACA,OAAO;MACHrB,WAAW,EAAED,MAAM,CAACC,WAAW;MAC/BC,WAAW,EAAEF,MAAM,CAACE,WAAW;MAC/BG,YAAY,EAAEL,MAAM,CAACK,YAAY;MACjCM,KAAK,EAAE,IAAI,CAACmD,cAAc,CAAChD,IAAI,EAAEQ,OAAO;KAC3C;EACL;EAEUsC,sBAAsBA,CAAC9C,IAAS;IACtC,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAgB;IACxC,MAAMC,QAAQ,GAAG,IAAID,GAAG,EAAgB;IACxC,KAAK,MAAME,OAAO,IAAInC,SAAS,CAAC+B,IAAI,CAAC,EAAE;MACnCC,QAAQ,CAACI,GAAG,CAACD,OAAO,EAAE,EAAa,CAAC;IACxC;IACA,IAAI6C,IAAiB;IACrB,IAAIjD,IAAI,CAACM,QAAQ,EAAE;MACf,KAAK,MAAMC,OAAO,IAAIpC,SAAS,CAAC6B,IAAI,CAACM,QAAQ,CAAC,EAAE;QAC5C,IAAI4C,GAAiC;QACrC,IAAI,UAAU,IAAI3C,OAAO,EAAE;UACvB2C,GAAG,GAAG,IAAIxF,eAAe,CAAC6C,OAAO,CAACoB,QAAkB,CAAC;UACrDsB,IAAI,GAAGC,GAAkB;QAC7B,CAAC,MAAM,IAAI,SAAS,IAAI3C,OAAO,EAAE;UAC7B2C,GAAG,GAAG,IAAI1F,oBAAoB,EAAE;QACpC,CAAC,MAAM,IAAI,WAAW,IAAI+C,OAAO,EAAE;UAC/B2C,GAAG,GAAG,IAAI,CAACC,kBAAkB,CAAC5C,OAAO,CAAC;QAC1C;QACA,IAAI2C,GAAG,EAAE;UACL/C,QAAQ,CAACE,GAAG,CAACE,OAAO,EAAE2C,GAAG,CAAC;UAC1BA,GAAG,CAACD,IAAI,GAAGA,IAAK;QACpB;MACJ;IACJ;IACA,OAAO;MACHhD,QAAQ;MACRE;KACH;EACL;EAEU6C,cAAcA,CAAChD,IAAS,EAAEQ,OAAuB;IACvD,MAAMJ,OAAO,GAAGI,OAAO,CAACP,QAAQ,CAACS,GAAG,CAACV,IAAI,CAA4B;IACrEI,OAAO,CAACO,KAAK,GAAGX,IAAI,CAACW,KAAK;IAC1BP,OAAO,CAACQ,eAAe,GAAGZ,IAAI,CAACY,eAAe;IAC9CR,OAAO,CAACS,kBAAkB,GAAGb,IAAI,CAACa,kBAAkB;IACpD,IAAIb,IAAI,CAACM,QAAQ,EAAE;MACfF,OAAO,CAACE,QAAQ,GAAGE,OAAO,CAACL,QAAQ,CAACO,GAAG,CAACV,IAAI,CAACM,QAAQ,CAAC;IAC1D;IACA,KAAK,MAAM,CAACS,IAAI,EAAElB,KAAK,CAAC,IAAIH,MAAM,CAACsB,OAAO,CAAChB,IAAI,CAAC,EAAE;MAC9C,IAAIe,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QACtB;MACJ;MACA,IAAIC,KAAK,CAACC,OAAO,CAACtB,KAAK,CAAC,EAAE;QACtB,MAAMuB,GAAG,GAAc,EAAE;QACzBhB,OAAO,CAACW,IAAI,CAAC,GAAGK,GAAG;QACnB,KAAK,MAAMC,IAAI,IAAIxB,KAAK,EAAE;UACtB,IAAI9B,SAAS,CAACsD,IAAI,CAAC,EAAE;YACjBD,GAAG,CAACE,IAAI,CAAC,IAAI,CAAC8B,SAAS,CAAC,IAAI,CAACJ,cAAc,CAAC3B,IAAI,EAAEb,OAAO,CAAC,EAAEJ,OAAO,CAAC,CAAC;UACzE,CAAC,MAAM,IAAIpC,WAAW,CAACqD,IAAI,CAAC,EAAE;YAC1BD,GAAG,CAACE,IAAI,CAAC,IAAI,CAAC+B,gBAAgB,CAAChC,IAAI,EAAEjB,OAAO,EAAEW,IAAI,EAAEP,OAAO,CAAC,CAAC;UACjE,CAAC,MAAM;YACHY,GAAG,CAACE,IAAI,CAACD,IAAI,CAAC;UAClB;QACJ;MACJ,CAAC,MAAM,IAAItD,SAAS,CAAC8B,KAAK,CAAC,EAAE;QACzBO,OAAO,CAACW,IAAI,CAAC,GAAG,IAAI,CAACqC,SAAS,CAAC,IAAI,CAACJ,cAAc,CAACnD,KAAK,EAAEW,OAAO,CAAC,EAAEJ,OAAO,CAAC;MAChF,CAAC,MAAM,IAAIpC,WAAW,CAAC6B,KAAK,CAAC,EAAE;QAC3BO,OAAO,CAACW,IAAI,CAAC,GAAG,IAAI,CAACsC,gBAAgB,CAACxD,KAAK,EAAEO,OAAO,EAAEW,IAAI,EAAEP,OAAO,CAAC;MACxE,CAAC,MAAM,IAAIX,KAAK,KAAKP,SAAS,EAAE;QAC5Bc,OAAO,CAACW,IAAI,CAAC,GAAGlB,KAAK;MACzB;IACJ;IACA,OAAOO,OAAO;EAClB;EAEUgD,SAASA,CAACpD,IAAS,EAAEsD,MAAW;IACtCtD,IAAI,CAACuD,UAAU,GAAGD,MAAiB;IACnC,OAAOtD,IAAI;EACf;EAEUqD,gBAAgBA,CAAC7B,SAAc,EAAExB,IAAa,EAAEe,IAAY,EAAEP,OAAuB;IAC3F,OAAO,IAAI,CAAC1B,MAAM,CAAC0E,cAAc,CAACxD,IAAI,EAAEe,IAAI,EAAEP,OAAO,CAACL,QAAQ,CAACO,GAAG,CAACc,SAAS,CAACE,QAAQ,CAAE,EAAEF,SAAS,CAACC,QAAQ,CAAC;EAChH;EAEUsB,cAAcA,CAACxC,OAAY,EAAEC,OAAuB,EAAEiD,GAAG,GAAG,CAAC;IACnE,MAAMC,UAAU,GAAGlD,OAAO,CAACL,QAAQ,CAACO,GAAG,CAACH,OAAO,CAAqB;IACpE,IAAI,OAAOA,OAAO,CAACqB,aAAa,KAAK,QAAQ,EAAE;MAC3C8B,UAAU,CAAC9B,aAAa,GAAG,IAAI,CAAC+B,iBAAiB,CAACpD,OAAO,CAACqB,aAAa,CAAC;IAC5E;IACA8B,UAAU,CAACtD,OAAO,GAAGI,OAAO,CAACP,QAAQ,CAACS,GAAG,CAACH,OAAO,CAACH,OAAO,CAAE;IAC3D,IAAIvC,kBAAkB,CAAC6F,UAAU,CAAC,EAAE;MAChC,KAAK,MAAM1B,KAAK,IAAIzB,OAAO,CAACwB,OAAO,EAAE;QACjC,MAAM6B,QAAQ,GAAG,IAAI,CAACb,cAAc,CAACf,KAAK,EAAExB,OAAO,EAAEiD,GAAG,EAAE,CAAC;QAC3DC,UAAU,CAAC3B,OAAO,CAACT,IAAI,CAACsC,QAAQ,CAAC;MACrC;IACJ;IACA,OAAOF,UAAU;EACrB;EAEUP,kBAAkBA,CAAC5C,OAAY;IACrC,MAAM0B,SAAS,GAAG,IAAI,CAAC4B,YAAY,CAACtD,OAAO,CAAC0B,SAAS,CAAC;IACtD,MAAMC,MAAM,GAAG3B,OAAO,CAAC2B,MAAM;IAC7B,MAAMC,MAAM,GAAG5B,OAAO,CAAC4B,MAAM;IAC7B,MAAMC,SAAS,GAAG7B,OAAO,CAAC6B,SAAS;IACnC,MAAMI,WAAW,GAAGjC,OAAO,CAACiC,WAAW;IACvC,MAAME,OAAO,GAAGnC,OAAO,CAACmC,OAAO;IAC/B,MAAME,SAAS,GAAGrC,OAAO,CAACqC,SAAS;IACnC,MAAMd,MAAM,GAAGvB,OAAO,CAACuB,MAAM;IAC7B,MAAM9B,IAAI,GAAG,IAAIvC,eAAe,CAC5ByE,MAAM,EACNC,MAAM,EACN;MACIG,KAAK,EAAE;QACHC,IAAI,EAAEH,SAAS;QACfK,SAAS,EAAED;OACd;MACDG,GAAG,EAAE;QACDJ,IAAI,EAAEG,OAAO;QACbD,SAAS,EAAEG;;KAElB,EACDX,SAAS,EACTH,MAAM,CACT;IACD,OAAO9B,IAAI;EACf;EAEU6D,YAAYA,CAAC9C,IAAY;IAC/B,OAAO,IAAI,CAACpC,KAAK,CAACmF,UAAU,CAAC/C,IAAI,CAAC;EACtC;EAEUc,mBAAmBA,CAAC7B,IAAiC;IAC3D,IAAI,CAACA,IAAI,EAAE;MACP,OAAOV,SAAS;IACpB;IACA,IAAI,IAAI,CAACf,mBAAmB,CAACwF,IAAI,KAAK,CAAC,EAAE;MACrC,IAAI,CAACC,yBAAyB,EAAE;IACpC;IACA,OAAO,IAAI,CAACzF,mBAAmB,CAACmC,GAAG,CAACV,IAAI,CAAC;EAC7C;EAEU2D,iBAAiBA,CAACM,EAAU;IAClC,IAAI,IAAI,CAAC1F,mBAAmB,CAACwF,IAAI,KAAK,CAAC,EAAE;MACrC,IAAI,CAACC,yBAAyB,EAAE;IACpC;IACA,MAAME,OAAO,GAAG,IAAI,CAAC3F,mBAAmB,CAAC4F,MAAM,CAACF,EAAE,CAAC;IACnD,OAAOC,OAAO;EAClB;EAEUF,yBAAyBA,CAAA;IAC/B,IAAIC,EAAE,GAAG,CAAC;IACV,KAAK,MAAMC,OAAO,IAAIjG,SAAS,CAAC,IAAI,CAACQ,OAAO,CAAC,EAAE;MAC3C,IAAId,iBAAiB,CAACuG,OAAO,CAAC,EAAE;QAC5B,IAAI,CAAC3F,mBAAmB,CAAC8B,GAAG,CAAC6D,OAAO,EAAED,EAAE,EAAE,CAAC;MAC/C;IACJ;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}