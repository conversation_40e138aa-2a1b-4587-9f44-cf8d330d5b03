{"ast": null, "code": "import { has, isString, isUndefined } from \"lodash-es\";\nimport { Lexer } from \"./lexer_public.js\";\nimport { augmentTokenTypes, tokenStructuredMatcher } from \"./tokens.js\";\nexport function tokenLabel(tokType) {\n  if (hasTokenLabel(tokType)) {\n    return tokType.LABEL;\n  } else {\n    return tokType.name;\n  }\n}\nexport function tokenName(tokType) {\n  return tokType.name;\n}\nexport function hasTokenLabel(obj) {\n  return isString(obj.LABEL) && obj.LABEL !== \"\";\n}\nconst PARENT = \"parent\";\nconst CATEGORIES = \"categories\";\nconst LABEL = \"label\";\nconst GROUP = \"group\";\nconst PUSH_MODE = \"push_mode\";\nconst POP_MODE = \"pop_mode\";\nconst LONGER_ALT = \"longer_alt\";\nconst LINE_BREAKS = \"line_breaks\";\nconst START_CHARS_HINT = \"start_chars_hint\";\nexport function createToken(config) {\n  return createTokenInternal(config);\n}\nfunction createTokenInternal(config) {\n  const pattern = config.pattern;\n  const tokenType = {};\n  tokenType.name = config.name;\n  if (!isUndefined(pattern)) {\n    tokenType.PATTERN = pattern;\n  }\n  if (has(config, PARENT)) {\n    throw \"The parent property is no longer supported.\\n\" + \"See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.\";\n  }\n  if (has(config, CATEGORIES)) {\n    // casting to ANY as this will be fixed inside `augmentTokenTypes``\n    tokenType.CATEGORIES = config[CATEGORIES];\n  }\n  augmentTokenTypes([tokenType]);\n  if (has(config, LABEL)) {\n    tokenType.LABEL = config[LABEL];\n  }\n  if (has(config, GROUP)) {\n    tokenType.GROUP = config[GROUP];\n  }\n  if (has(config, POP_MODE)) {\n    tokenType.POP_MODE = config[POP_MODE];\n  }\n  if (has(config, PUSH_MODE)) {\n    tokenType.PUSH_MODE = config[PUSH_MODE];\n  }\n  if (has(config, LONGER_ALT)) {\n    tokenType.LONGER_ALT = config[LONGER_ALT];\n  }\n  if (has(config, LINE_BREAKS)) {\n    tokenType.LINE_BREAKS = config[LINE_BREAKS];\n  }\n  if (has(config, START_CHARS_HINT)) {\n    tokenType.START_CHARS_HINT = config[START_CHARS_HINT];\n  }\n  return tokenType;\n}\nexport const EOF = createToken({\n  name: \"EOF\",\n  pattern: Lexer.NA\n});\naugmentTokenTypes([EOF]);\nexport function createTokenInstance(tokType, image, startOffset, endOffset, startLine, endLine, startColumn, endColumn) {\n  return {\n    image,\n    startOffset,\n    endOffset,\n    startLine,\n    endLine,\n    startColumn,\n    endColumn,\n    tokenTypeIdx: tokType.tokenTypeIdx,\n    tokenType: tokType\n  };\n}\nexport function tokenMatcher(token, tokType) {\n  return tokenStructuredMatcher(token, tokType);\n}", "map": {"version": 3, "names": ["has", "isString", "isUndefined", "<PERSON><PERSON>", "augmentTokenTypes", "tokenStructuredMatcher", "tokenLabel", "tokType", "hasTokenLabel", "LABEL", "name", "tokenName", "obj", "PARENT", "CATEGORIES", "GROUP", "PUSH_MODE", "POP_MODE", "LONGER_ALT", "LINE_BREAKS", "START_CHARS_HINT", "createToken", "config", "createTokenInternal", "pattern", "tokenType", "PATTERN", "EOF", "NA", "createTokenInstance", "image", "startOffset", "endOffset", "startLine", "endLine", "startColumn", "endColumn", "tokenTypeIdx", "tokenMatcher", "token"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/tokens_public.ts"], "sourcesContent": ["import { has, isString, isUndefined } from \"lodash-es\";\nimport { Lexer } from \"./lexer_public.js\";\nimport { augmentTokenTypes, tokenStructuredMatcher } from \"./tokens.js\";\nimport { IToken, ITokenConfig, TokenType } from \"@chevrotain/types\";\n\nexport function tokenLabel(tokType: TokenType): string {\n  if (hasTokenLabel(tokType)) {\n    return tokType.LABEL;\n  } else {\n    return tokType.name;\n  }\n}\n\nexport function tokenName(tokType: TokenType): string {\n  return tokType.name;\n}\n\nexport function hasTokenLabel(\n  obj: TokenType,\n): obj is TokenType & Pick<Required<TokenType>, \"LABEL\"> {\n  return isString(obj.LABEL) && obj.LABEL !== \"\";\n}\n\nconst PARENT = \"parent\";\nconst CATEGORIES = \"categories\";\nconst LABEL = \"label\";\nconst GROUP = \"group\";\nconst PUSH_MODE = \"push_mode\";\nconst POP_MODE = \"pop_mode\";\nconst LONGER_ALT = \"longer_alt\";\nconst LINE_BREAKS = \"line_breaks\";\nconst START_CHARS_HINT = \"start_chars_hint\";\n\nexport function createToken(config: ITokenConfig): TokenType {\n  return createTokenInternal(config);\n}\n\nfunction createTokenInternal(config: ITokenConfig): TokenType {\n  const pattern = config.pattern;\n\n  const tokenType: TokenType = <any>{};\n  tokenType.name = config.name;\n\n  if (!isUndefined(pattern)) {\n    tokenType.PATTERN = pattern;\n  }\n\n  if (has(config, PARENT)) {\n    throw (\n      \"The parent property is no longer supported.\\n\" +\n      \"See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.\"\n    );\n  }\n\n  if (has(config, CATEGORIES)) {\n    // casting to ANY as this will be fixed inside `augmentTokenTypes``\n    tokenType.CATEGORIES = <any>config[CATEGORIES];\n  }\n\n  augmentTokenTypes([tokenType]);\n\n  if (has(config, LABEL)) {\n    tokenType.LABEL = config[LABEL];\n  }\n\n  if (has(config, GROUP)) {\n    tokenType.GROUP = config[GROUP];\n  }\n\n  if (has(config, POP_MODE)) {\n    tokenType.POP_MODE = config[POP_MODE];\n  }\n\n  if (has(config, PUSH_MODE)) {\n    tokenType.PUSH_MODE = config[PUSH_MODE];\n  }\n\n  if (has(config, LONGER_ALT)) {\n    tokenType.LONGER_ALT = config[LONGER_ALT];\n  }\n\n  if (has(config, LINE_BREAKS)) {\n    tokenType.LINE_BREAKS = config[LINE_BREAKS];\n  }\n\n  if (has(config, START_CHARS_HINT)) {\n    tokenType.START_CHARS_HINT = config[START_CHARS_HINT];\n  }\n\n  return tokenType;\n}\n\nexport const EOF = createToken({ name: \"EOF\", pattern: Lexer.NA });\naugmentTokenTypes([EOF]);\n\nexport function createTokenInstance(\n  tokType: TokenType,\n  image: string,\n  startOffset: number,\n  endOffset: number,\n  startLine: number,\n  endLine: number,\n  startColumn: number,\n  endColumn: number,\n): IToken {\n  return {\n    image,\n    startOffset,\n    endOffset,\n    startLine,\n    endLine,\n    startColumn,\n    endColumn,\n    tokenTypeIdx: (<any>tokType).tokenTypeIdx,\n    tokenType: tokType,\n  };\n}\n\nexport function tokenMatcher(token: IToken, tokType: TokenType): boolean {\n  return tokenStructuredMatcher(token, tokType);\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,WAAW;AACtD,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,aAAa;AAGvE,OAAM,SAAUC,UAAUA,CAACC,OAAkB;EAC3C,IAAIC,aAAa,CAACD,OAAO,CAAC,EAAE;IAC1B,OAAOA,OAAO,CAACE,KAAK;GACrB,MAAM;IACL,OAAOF,OAAO,CAACG,IAAI;;AAEvB;AAEA,OAAM,SAAUC,SAASA,CAACJ,OAAkB;EAC1C,OAAOA,OAAO,CAACG,IAAI;AACrB;AAEA,OAAM,SAAUF,aAAaA,CAC3BI,GAAc;EAEd,OAAOX,QAAQ,CAACW,GAAG,CAACH,KAAK,CAAC,IAAIG,GAAG,CAACH,KAAK,KAAK,EAAE;AAChD;AAEA,MAAMI,MAAM,GAAG,QAAQ;AACvB,MAAMC,UAAU,GAAG,YAAY;AAC/B,MAAML,KAAK,GAAG,OAAO;AACrB,MAAMM,KAAK,GAAG,OAAO;AACrB,MAAMC,SAAS,GAAG,WAAW;AAC7B,MAAMC,QAAQ,GAAG,UAAU;AAC3B,MAAMC,UAAU,GAAG,YAAY;AAC/B,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,gBAAgB,GAAG,kBAAkB;AAE3C,OAAM,SAAUC,WAAWA,CAACC,MAAoB;EAC9C,OAAOC,mBAAmB,CAACD,MAAM,CAAC;AACpC;AAEA,SAASC,mBAAmBA,CAACD,MAAoB;EAC/C,MAAME,OAAO,GAAGF,MAAM,CAACE,OAAO;EAE9B,MAAMC,SAAS,GAAmB,EAAE;EACpCA,SAAS,CAACf,IAAI,GAAGY,MAAM,CAACZ,IAAI;EAE5B,IAAI,CAACR,WAAW,CAACsB,OAAO,CAAC,EAAE;IACzBC,SAAS,CAACC,OAAO,GAAGF,OAAO;;EAG7B,IAAIxB,GAAG,CAACsB,MAAM,EAAET,MAAM,CAAC,EAAE;IACvB,MACE,+CAA+C,GAC/C,8FAA8F;;EAIlG,IAAIb,GAAG,CAACsB,MAAM,EAAER,UAAU,CAAC,EAAE;IAC3B;IACAW,SAAS,CAACX,UAAU,GAAQQ,MAAM,CAACR,UAAU,CAAC;;EAGhDV,iBAAiB,CAAC,CAACqB,SAAS,CAAC,CAAC;EAE9B,IAAIzB,GAAG,CAACsB,MAAM,EAAEb,KAAK,CAAC,EAAE;IACtBgB,SAAS,CAAChB,KAAK,GAAGa,MAAM,CAACb,KAAK,CAAC;;EAGjC,IAAIT,GAAG,CAACsB,MAAM,EAAEP,KAAK,CAAC,EAAE;IACtBU,SAAS,CAACV,KAAK,GAAGO,MAAM,CAACP,KAAK,CAAC;;EAGjC,IAAIf,GAAG,CAACsB,MAAM,EAAEL,QAAQ,CAAC,EAAE;IACzBQ,SAAS,CAACR,QAAQ,GAAGK,MAAM,CAACL,QAAQ,CAAC;;EAGvC,IAAIjB,GAAG,CAACsB,MAAM,EAAEN,SAAS,CAAC,EAAE;IAC1BS,SAAS,CAACT,SAAS,GAAGM,MAAM,CAACN,SAAS,CAAC;;EAGzC,IAAIhB,GAAG,CAACsB,MAAM,EAAEJ,UAAU,CAAC,EAAE;IAC3BO,SAAS,CAACP,UAAU,GAAGI,MAAM,CAACJ,UAAU,CAAC;;EAG3C,IAAIlB,GAAG,CAACsB,MAAM,EAAEH,WAAW,CAAC,EAAE;IAC5BM,SAAS,CAACN,WAAW,GAAGG,MAAM,CAACH,WAAW,CAAC;;EAG7C,IAAInB,GAAG,CAACsB,MAAM,EAAEF,gBAAgB,CAAC,EAAE;IACjCK,SAAS,CAACL,gBAAgB,GAAGE,MAAM,CAACF,gBAAgB,CAAC;;EAGvD,OAAOK,SAAS;AAClB;AAEA,OAAO,MAAME,GAAG,GAAGN,WAAW,CAAC;EAAEX,IAAI,EAAE,KAAK;EAAEc,OAAO,EAAErB,KAAK,CAACyB;AAAE,CAAE,CAAC;AAClExB,iBAAiB,CAAC,CAACuB,GAAG,CAAC,CAAC;AAExB,OAAM,SAAUE,mBAAmBA,CACjCtB,OAAkB,EAClBuB,KAAa,EACbC,WAAmB,EACnBC,SAAiB,EACjBC,SAAiB,EACjBC,OAAe,EACfC,WAAmB,EACnBC,SAAiB;EAEjB,OAAO;IACLN,KAAK;IACLC,WAAW;IACXC,SAAS;IACTC,SAAS;IACTC,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC,YAAY,EAAQ9B,OAAQ,CAAC8B,YAAY;IACzCZ,SAAS,EAAElB;GACZ;AACH;AAEA,OAAM,SAAU+B,YAAYA,CAACC,KAAa,EAAEhC,OAAkB;EAC5D,OAAOF,sBAAsB,CAACkC,KAAK,EAAEhC,OAAO,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}