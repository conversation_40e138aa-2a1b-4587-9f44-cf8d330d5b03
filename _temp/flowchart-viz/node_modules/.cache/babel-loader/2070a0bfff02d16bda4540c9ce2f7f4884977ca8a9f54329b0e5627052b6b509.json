{"ast": null, "code": "import { flatten, map, uniq } from \"lodash-es\";\nimport { isBranchingProd, isOptionalProd, isSequenceProd, NonTerminal, Terminal } from \"@chevrotain/gast\";\nexport function first(prod) {\n  /* istanbul ignore else */\n  if (prod instanceof NonTerminal) {\n    // this could in theory cause infinite loops if\n    // (1) prod A refs prod B.\n    // (2) prod B refs prod A\n    // (3) AB can match the empty set\n    // in other words a cycle where everything is optional so the first will keep\n    // looking ahead for the next optional part and will never exit\n    // currently there is no safeguard for this unique edge case because\n    // (1) not sure a grammar in which this can happen is useful for anything (productive)\n    return first(prod.referencedRule);\n  } else if (prod instanceof Terminal) {\n    return firstForTerminal(prod);\n  } else if (isSequenceProd(prod)) {\n    return firstForSequence(prod);\n  } else if (isBranchingProd(prod)) {\n    return firstForBranching(prod);\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nexport function firstForSequence(prod) {\n  let firstSet = [];\n  const seq = prod.definition;\n  let nextSubProdIdx = 0;\n  let hasInnerProdsRemaining = seq.length > nextSubProdIdx;\n  let currSubProd;\n  // so we enter the loop at least once (if the definition is not empty\n  let isLastInnerProdOptional = true;\n  // scan a sequence until it's end or until we have found a NONE optional production in it\n  while (hasInnerProdsRemaining && isLastInnerProdOptional) {\n    currSubProd = seq[nextSubProdIdx];\n    isLastInnerProdOptional = isOptionalProd(currSubProd);\n    firstSet = firstSet.concat(first(currSubProd));\n    nextSubProdIdx = nextSubProdIdx + 1;\n    hasInnerProdsRemaining = seq.length > nextSubProdIdx;\n  }\n  return uniq(firstSet);\n}\nexport function firstForBranching(prod) {\n  const allAlternativesFirsts = map(prod.definition, innerProd => {\n    return first(innerProd);\n  });\n  return uniq(flatten(allAlternativesFirsts));\n}\nexport function firstForTerminal(terminal) {\n  return [terminal.terminalType];\n}", "map": {"version": 3, "names": ["flatten", "map", "uniq", "isBranchingProd", "isOptionalProd", "isSequenceProd", "NonTerminal", "Terminal", "first", "prod", "referencedRule", "firstForTerminal", "firstForSequence", "firstForBranching", "Error", "firstSet", "seq", "definition", "nextSubProdIdx", "hasInnerProdsRemaining", "length", "currSubProd", "isLastInnerProdOptional", "concat", "allAlternativesFirsts", "innerProd", "terminal", "terminalType"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/first.ts"], "sourcesContent": ["import { flatten, map, uniq } from \"lodash-es\";\nimport {\n  isBranchingProd,\n  isOptionalProd,\n  isSequenceProd,\n  NonTerminal,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport { IProduction, TokenType } from \"@chevrotain/types\";\n\nexport function first(prod: IProduction): TokenType[] {\n  /* istanbul ignore else */\n  if (prod instanceof NonTerminal) {\n    // this could in theory cause infinite loops if\n    // (1) prod A refs prod B.\n    // (2) prod B refs prod A\n    // (3) AB can match the empty set\n    // in other words a cycle where everything is optional so the first will keep\n    // looking ahead for the next optional part and will never exit\n    // currently there is no safeguard for this unique edge case because\n    // (1) not sure a grammar in which this can happen is useful for anything (productive)\n    return first((<NonTerminal>prod).referencedRule);\n  } else if (prod instanceof Terminal) {\n    return firstForTerminal(<Terminal>prod);\n  } else if (isSequenceProd(prod)) {\n    return firstForSequence(prod);\n  } else if (isBranchingProd(prod)) {\n    return firstForBranching(prod);\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n\nexport function firstForSequence(prod: {\n  definition: IProduction[];\n}): TokenType[] {\n  let firstSet: TokenType[] = [];\n  const seq = prod.definition;\n  let nextSubProdIdx = 0;\n  let hasInnerProdsRemaining = seq.length > nextSubProdIdx;\n  let currSubProd;\n  // so we enter the loop at least once (if the definition is not empty\n  let isLastInnerProdOptional = true;\n  // scan a sequence until it's end or until we have found a NONE optional production in it\n  while (hasInnerProdsRemaining && isLastInnerProdOptional) {\n    currSubProd = seq[nextSubProdIdx];\n    isLastInnerProdOptional = isOptionalProd(currSubProd);\n    firstSet = firstSet.concat(first(currSubProd));\n    nextSubProdIdx = nextSubProdIdx + 1;\n    hasInnerProdsRemaining = seq.length > nextSubProdIdx;\n  }\n\n  return uniq(firstSet);\n}\n\nexport function firstForBranching(prod: {\n  definition: IProduction[];\n}): TokenType[] {\n  const allAlternativesFirsts: TokenType[][] = map(\n    prod.definition,\n    (innerProd) => {\n      return first(innerProd);\n    },\n  );\n  return uniq(flatten<TokenType>(allAlternativesFirsts));\n}\n\nexport function firstForTerminal(terminal: Terminal): TokenType[] {\n  return [terminal.terminalType];\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,GAAG,EAAEC,IAAI,QAAQ,WAAW;AAC9C,SACEC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,QAAQ,QACH,kBAAkB;AAGzB,OAAM,SAAUC,KAAKA,CAACC,IAAiB;EACrC;EACA,IAAIA,IAAI,YAAYH,WAAW,EAAE;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOE,KAAK,CAAeC,IAAK,CAACC,cAAc,CAAC;GACjD,MAAM,IAAID,IAAI,YAAYF,QAAQ,EAAE;IACnC,OAAOI,gBAAgB,CAAWF,IAAI,CAAC;GACxC,MAAM,IAAIJ,cAAc,CAACI,IAAI,CAAC,EAAE;IAC/B,OAAOG,gBAAgB,CAACH,IAAI,CAAC;GAC9B,MAAM,IAAIN,eAAe,CAACM,IAAI,CAAC,EAAE;IAChC,OAAOI,iBAAiB,CAACJ,IAAI,CAAC;GAC/B,MAAM;IACL,MAAMK,KAAK,CAAC,sBAAsB,CAAC;;AAEvC;AAEA,OAAM,SAAUF,gBAAgBA,CAACH,IAEhC;EACC,IAAIM,QAAQ,GAAgB,EAAE;EAC9B,MAAMC,GAAG,GAAGP,IAAI,CAACQ,UAAU;EAC3B,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,sBAAsB,GAAGH,GAAG,CAACI,MAAM,GAAGF,cAAc;EACxD,IAAIG,WAAW;EACf;EACA,IAAIC,uBAAuB,GAAG,IAAI;EAClC;EACA,OAAOH,sBAAsB,IAAIG,uBAAuB,EAAE;IACxDD,WAAW,GAAGL,GAAG,CAACE,cAAc,CAAC;IACjCI,uBAAuB,GAAGlB,cAAc,CAACiB,WAAW,CAAC;IACrDN,QAAQ,GAAGA,QAAQ,CAACQ,MAAM,CAACf,KAAK,CAACa,WAAW,CAAC,CAAC;IAC9CH,cAAc,GAAGA,cAAc,GAAG,CAAC;IACnCC,sBAAsB,GAAGH,GAAG,CAACI,MAAM,GAAGF,cAAc;;EAGtD,OAAOhB,IAAI,CAACa,QAAQ,CAAC;AACvB;AAEA,OAAM,SAAUF,iBAAiBA,CAACJ,IAEjC;EACC,MAAMe,qBAAqB,GAAkBvB,GAAG,CAC9CQ,IAAI,CAACQ,UAAU,EACdQ,SAAS,IAAI;IACZ,OAAOjB,KAAK,CAACiB,SAAS,CAAC;EACzB,CAAC,CACF;EACD,OAAOvB,IAAI,CAACF,OAAO,CAAYwB,qBAAqB,CAAC,CAAC;AACxD;AAEA,OAAM,SAAUb,gBAAgBA,CAACe,QAAkB;EACjD,OAAO,CAACA,QAAQ,CAACC,YAAY,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}