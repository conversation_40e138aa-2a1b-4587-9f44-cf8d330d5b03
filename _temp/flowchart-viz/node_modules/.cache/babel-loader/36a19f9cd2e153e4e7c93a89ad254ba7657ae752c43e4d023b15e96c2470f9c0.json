{"ast": null, "code": "/* istanbul ignore file - tricky to import some things from this module during testing */\n// semantic version\nexport { VERSION } from \"./version.js\";\nexport { CstParser, EmbeddedActionsParser, ParserDefinitionErrorType, EMPTY_ALT } from \"./parse/parser/parser.js\";\nexport { Lexer, LexerDefinitionErrorType } from \"./scan/lexer_public.js\";\n// Tokens utilities\nexport { createToken, createTokenInstance, EOF, tokenLabel, tokenMatcher, tokenName } from \"./scan/tokens_public.js\";\n// Lookahead\nexport { getLookaheadPaths } from \"./parse/grammar/lookahead.js\";\nexport { LLkLookaheadStrategy } from \"./parse/grammar/llk_lookahead.js\";\n// Other Utilities\nexport { defaultParserErrorProvider } from \"./parse/errors_public.js\";\nexport { EarlyExitException, isRecognitionException, MismatchedTokenException, NotAllInputParsedException, NoViableAltException } from \"./parse/exceptions_public.js\";\nexport { defaultLexerErrorProvider } from \"./scan/lexer_errors_public.js\";\n// grammar reflection API\nexport { Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Rule, Terminal } from \"@chevrotain/gast\";\n// GAST Utilities\nexport { serializeGrammar, serializeProduction, GAstVisitor } from \"@chevrotain/gast\";\nexport { generateCstDts } from \"@chevrotain/cst-dts-gen\";\n/* istanbul ignore next */\nexport function clearCache() {\n  console.warn(\"The clearCache function was 'soft' removed from the Chevrotain API.\" + \"\\n\\t It performs no action other than printing this message.\" + \"\\n\\t Please avoid using it as it will be completely removed in the future\");\n}\nexport { createSyntaxDiagramsCode } from \"./diagrams/render_public.js\";\nexport class Parser {\n  constructor() {\n    throw new Error(\"The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.\\t\\n\" + \"See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0\");\n  }\n}", "map": {"version": 3, "names": ["VERSION", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmbeddedActions<PERSON><PERSON><PERSON>", "ParserDefinitionErrorType", "EMPTY_ALT", "<PERSON><PERSON>", "LexerDefinitionErrorType", "createToken", "createTokenInstance", "EOF", "tokenLabel", "tokenMatcher", "tokenName", "get<PERSON><PERSON><PERSON>eadPath<PERSON>", "LLkLookaheadStrategy", "defaultParserErrorProvider", "EarlyExitException", "isRecognitionException", "MismatchedTokenException", "NotAllInputParsedException", "NoViableAltException", "defaultLexerErrorProvider", "Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Rule", "Terminal", "serializeGrammar", "serializeProduction", "GAstVisitor", "generateCstDts", "clearCache", "console", "warn", "createSyntaxDiagramsCode", "<PERSON><PERSON><PERSON>", "constructor", "Error"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/api.ts"], "sourcesContent": ["/* istanbul ignore file - tricky to import some things from this module during testing */\n\n// semantic version\nexport { VERSION } from \"./version.js\";\n\nexport {\n  CstParser,\n  EmbeddedActionsParser,\n  ParserDefinitionErrorType,\n  EMPTY_ALT,\n} from \"./parse/parser/parser.js\";\n\nexport { Lexer, LexerDefinitionErrorType } from \"./scan/lexer_public.js\";\n\n// Tokens utilities\nexport {\n  createToken,\n  createTokenInstance,\n  EOF,\n  tokenLabel,\n  tokenMatcher,\n  tokenName,\n} from \"./scan/tokens_public.js\";\n\n// Lookahead\n\nexport { getLookaheadPaths } from \"./parse/grammar/lookahead.js\";\n\nexport { LLkLookaheadStrategy } from \"./parse/grammar/llk_lookahead.js\";\n\n// Other Utilities\n\nexport { defaultParserErrorProvider } from \"./parse/errors_public.js\";\n\nexport {\n  EarlyExitException,\n  isRecognitionException,\n  MismatchedTokenException,\n  NotAllInputParsedException,\n  NoViableAltException,\n} from \"./parse/exceptions_public.js\";\n\nexport { defaultLexerErrorProvider } from \"./scan/lexer_errors_public.js\";\n\n// grammar reflection API\nexport {\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n} from \"@chevrotain/gast\";\n\n// GAST Utilities\n\nexport {\n  serializeGrammar,\n  serializeProduction,\n  GAstVisitor,\n} from \"@chevrotain/gast\";\n\nexport { generateCstDts } from \"@chevrotain/cst-dts-gen\";\n\n/* istanbul ignore next */\nexport function clearCache() {\n  console.warn(\n    \"The clearCache function was 'soft' removed from the Chevrotain API.\" +\n      \"\\n\\t It performs no action other than printing this message.\" +\n      \"\\n\\t Please avoid using it as it will be completely removed in the future\",\n  );\n}\n\nexport { createSyntaxDiagramsCode } from \"./diagrams/render_public.js\";\n\nexport class Parser {\n  constructor() {\n    throw new Error(\n      \"The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.\\t\\n\" +\n        \"See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0\",\n    );\n  }\n}\n"], "mappings": "AAAA;AAEA;AACA,SAASA,OAAO,QAAQ,cAAc;AAEtC,SACEC,SAAS,EACTC,qBAAqB,EACrBC,yBAAyB,EACzBC,SAAS,QACJ,0BAA0B;AAEjC,SAASC,KAAK,EAAEC,wBAAwB,QAAQ,wBAAwB;AAExE;AACA,SACEC,WAAW,EACXC,mBAAmB,EACnBC,GAAG,EACHC,UAAU,EACVC,YAAY,EACZC,SAAS,QACJ,yBAAyB;AAEhC;AAEA,SAASC,iBAAiB,QAAQ,8BAA8B;AAEhE,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE;AAEA,SAASC,0BAA0B,QAAQ,0BAA0B;AAErE,SACEC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,EACxBC,0BAA0B,EAC1BC,oBAAoB,QACf,8BAA8B;AAErC,SAASC,yBAAyB,QAAQ,+BAA+B;AAEzE;AACA,SACEC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,IAAI,EACJC,QAAQ,QACH,kBAAkB;AAEzB;AAEA,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,WAAW,QACN,kBAAkB;AAEzB,SAASC,cAAc,QAAQ,yBAAyB;AAExD;AACA,OAAM,SAAUC,UAAUA,CAAA;EACxBC,OAAO,CAACC,IAAI,CACV,qEAAqE,GACnE,8DAA8D,GAC9D,2EAA2E,CAC9E;AACH;AAEA,SAASC,wBAAwB,QAAQ,6BAA6B;AAEtE,OAAM,MAAOC,MAAM;EACjBC,YAAA;IACE,MAAM,IAAIC,KAAK,CACb,2FAA2F,GACzF,sEAAsE,CACzE;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}