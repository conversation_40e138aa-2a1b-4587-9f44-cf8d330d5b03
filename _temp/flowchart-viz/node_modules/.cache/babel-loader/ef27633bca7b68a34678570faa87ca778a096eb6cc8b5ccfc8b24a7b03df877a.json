{"ast": null, "code": "const EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\nimport { orient2d } from 'robust-predicates';\nexport default class Delaunator {\n  static from(points, getX = defaultGetX, getY = defaultGetY) {\n    const n = points.length;\n    const coords = new Float64Array(n * 2);\n    for (let i = 0; i < n; i++) {\n      const p = points[i];\n      coords[2 * i] = getX(p);\n      coords[2 * i + 1] = getY(p);\n    }\n    return new Delaunator(coords);\n  }\n  constructor(coords) {\n    const n = coords.length >> 1;\n    if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n    this.coords = coords;\n\n    // arrays that will store the triangulation graph\n    const maxTriangles = Math.max(2 * n - 5, 0);\n    this._triangles = new Uint32Array(maxTriangles * 3);\n    this._halfedges = new Int32Array(maxTriangles * 3);\n\n    // temporary arrays for tracking the edges of the advancing convex hull\n    this._hashSize = Math.ceil(Math.sqrt(n));\n    this._hullPrev = new Uint32Array(n); // edge to prev edge\n    this._hullNext = new Uint32Array(n); // edge to next edge\n    this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n    this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n    // temporary arrays for sorting points\n    this._ids = new Uint32Array(n);\n    this._dists = new Float64Array(n);\n    this.update();\n  }\n  update() {\n    const {\n      coords,\n      _hullPrev: hullPrev,\n      _hullNext: hullNext,\n      _hullTri: hullTri,\n      _hullHash: hullHash\n    } = this;\n    const n = coords.length >> 1;\n\n    // populate an array of point indices; calculate input data bbox\n    let minX = Infinity;\n    let minY = Infinity;\n    let maxX = -Infinity;\n    let maxY = -Infinity;\n    for (let i = 0; i < n; i++) {\n      const x = coords[2 * i];\n      const y = coords[2 * i + 1];\n      if (x < minX) minX = x;\n      if (y < minY) minY = y;\n      if (x > maxX) maxX = x;\n      if (y > maxY) maxY = y;\n      this._ids[i] = i;\n    }\n    const cx = (minX + maxX) / 2;\n    const cy = (minY + maxY) / 2;\n    let i0, i1, i2;\n\n    // pick a seed point close to the center\n    for (let i = 0, minDist = Infinity; i < n; i++) {\n      const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n      if (d < minDist) {\n        i0 = i;\n        minDist = d;\n      }\n    }\n    const i0x = coords[2 * i0];\n    const i0y = coords[2 * i0 + 1];\n\n    // find the point closest to the seed\n    for (let i = 0, minDist = Infinity; i < n; i++) {\n      if (i === i0) continue;\n      const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n      if (d < minDist && d > 0) {\n        i1 = i;\n        minDist = d;\n      }\n    }\n    let i1x = coords[2 * i1];\n    let i1y = coords[2 * i1 + 1];\n    let minRadius = Infinity;\n\n    // find the third point which forms the smallest circumcircle with the first two\n    for (let i = 0; i < n; i++) {\n      if (i === i0 || i === i1) continue;\n      const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n      if (r < minRadius) {\n        i2 = i;\n        minRadius = r;\n      }\n    }\n    let i2x = coords[2 * i2];\n    let i2y = coords[2 * i2 + 1];\n    if (minRadius === Infinity) {\n      // order collinear points by dx (or dy if all x are identical)\n      // and return the list as a hull\n      for (let i = 0; i < n; i++) {\n        this._dists[i] = coords[2 * i] - coords[0] || coords[2 * i + 1] - coords[1];\n      }\n      quicksort(this._ids, this._dists, 0, n - 1);\n      const hull = new Uint32Array(n);\n      let j = 0;\n      for (let i = 0, d0 = -Infinity; i < n; i++) {\n        const id = this._ids[i];\n        const d = this._dists[id];\n        if (d > d0) {\n          hull[j++] = id;\n          d0 = d;\n        }\n      }\n      this.hull = hull.subarray(0, j);\n      this.triangles = new Uint32Array(0);\n      this.halfedges = new Uint32Array(0);\n      return;\n    }\n\n    // swap the order of the seed points for counter-clockwise orientation\n    if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n      const i = i1;\n      const x = i1x;\n      const y = i1y;\n      i1 = i2;\n      i1x = i2x;\n      i1y = i2y;\n      i2 = i;\n      i2x = x;\n      i2y = y;\n    }\n    const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n    this._cx = center.x;\n    this._cy = center.y;\n    for (let i = 0; i < n; i++) {\n      this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n    }\n\n    // sort the points by distance from the seed triangle circumcenter\n    quicksort(this._ids, this._dists, 0, n - 1);\n\n    // set up the seed triangle as the starting hull\n    this._hullStart = i0;\n    let hullSize = 3;\n    hullNext[i0] = hullPrev[i2] = i1;\n    hullNext[i1] = hullPrev[i0] = i2;\n    hullNext[i2] = hullPrev[i1] = i0;\n    hullTri[i0] = 0;\n    hullTri[i1] = 1;\n    hullTri[i2] = 2;\n    hullHash.fill(-1);\n    hullHash[this._hashKey(i0x, i0y)] = i0;\n    hullHash[this._hashKey(i1x, i1y)] = i1;\n    hullHash[this._hashKey(i2x, i2y)] = i2;\n    this.trianglesLen = 0;\n    this._addTriangle(i0, i1, i2, -1, -1, -1);\n    for (let k = 0, xp, yp; k < this._ids.length; k++) {\n      const i = this._ids[k];\n      const x = coords[2 * i];\n      const y = coords[2 * i + 1];\n\n      // skip near-duplicate points\n      if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n      xp = x;\n      yp = y;\n\n      // skip seed triangle points\n      if (i === i0 || i === i1 || i === i2) continue;\n\n      // find a visible edge on the convex hull using edge hash\n      let start = 0;\n      for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n        start = hullHash[(key + j) % this._hashSize];\n        if (start !== -1 && start !== hullNext[start]) break;\n      }\n      start = hullPrev[start];\n      let e = start,\n        q;\n      while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n        e = q;\n        if (e === start) {\n          e = -1;\n          break;\n        }\n      }\n      if (e === -1) continue; // likely a near-duplicate point; skip it\n\n      // add the first triangle from the point\n      let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n      // recursively flip triangles from the point until they satisfy the Delaunay condition\n      hullTri[i] = this._legalize(t + 2);\n      hullTri[e] = t; // keep track of boundary triangles on the hull\n      hullSize++;\n\n      // walk forward through the hull, adding more triangles and flipping recursively\n      let n = hullNext[e];\n      while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n        t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n        hullTri[i] = this._legalize(t + 2);\n        hullNext[n] = n; // mark as removed\n        hullSize--;\n        n = q;\n      }\n\n      // walk backward from the other side, adding more triangles and flipping\n      if (e === start) {\n        while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n          t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n          this._legalize(t + 2);\n          hullTri[q] = t;\n          hullNext[e] = e; // mark as removed\n          hullSize--;\n          e = q;\n        }\n      }\n\n      // update the hull indices\n      this._hullStart = hullPrev[i] = e;\n      hullNext[e] = hullPrev[n] = i;\n      hullNext[i] = n;\n\n      // save the two new edges in the hash table\n      hullHash[this._hashKey(x, y)] = i;\n      hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n    }\n    this.hull = new Uint32Array(hullSize);\n    for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n      this.hull[i] = e;\n      e = hullNext[e];\n    }\n\n    // trim typed triangle mesh arrays\n    this.triangles = this._triangles.subarray(0, this.trianglesLen);\n    this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n  }\n  _hashKey(x, y) {\n    return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n  }\n  _legalize(a) {\n    const {\n      _triangles: triangles,\n      _halfedges: halfedges,\n      coords\n    } = this;\n    let i = 0;\n    let ar = 0;\n\n    // recursion eliminated with a fixed-size stack\n    while (true) {\n      const b = halfedges[a];\n\n      /* if the pair of triangles doesn't satisfy the Delaunay condition\n       * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n       * then do the same check/flip recursively for the new pair of triangles\n       *\n       *           pl                    pl\n       *          /||\\                  /  \\\n       *       al/ || \\bl            al/    \\a\n       *        /  ||  \\              /      \\\n       *       /  a||b  \\    flip    /___ar___\\\n       *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n       *        \\  ||  /              \\      /\n       *       ar\\ || /br             b\\    /br\n       *          \\||/                  \\  /\n       *           pr                    pr\n       */\n      const a0 = a - a % 3;\n      ar = a0 + (a + 2) % 3;\n      if (b === -1) {\n        // convex hull edge\n        if (i === 0) break;\n        a = EDGE_STACK[--i];\n        continue;\n      }\n      const b0 = b - b % 3;\n      const al = a0 + (a + 1) % 3;\n      const bl = b0 + (b + 2) % 3;\n      const p0 = triangles[ar];\n      const pr = triangles[a];\n      const pl = triangles[al];\n      const p1 = triangles[bl];\n      const illegal = inCircle(coords[2 * p0], coords[2 * p0 + 1], coords[2 * pr], coords[2 * pr + 1], coords[2 * pl], coords[2 * pl + 1], coords[2 * p1], coords[2 * p1 + 1]);\n      if (illegal) {\n        triangles[a] = p1;\n        triangles[b] = p0;\n        const hbl = halfedges[bl];\n\n        // edge swapped on the other side of the hull (rare); fix the halfedge reference\n        if (hbl === -1) {\n          let e = this._hullStart;\n          do {\n            if (this._hullTri[e] === bl) {\n              this._hullTri[e] = a;\n              break;\n            }\n            e = this._hullPrev[e];\n          } while (e !== this._hullStart);\n        }\n        this._link(a, hbl);\n        this._link(b, halfedges[ar]);\n        this._link(ar, bl);\n        const br = b0 + (b + 1) % 3;\n\n        // don't worry about hitting the cap: it can only happen on extremely degenerate input\n        if (i < EDGE_STACK.length) {\n          EDGE_STACK[i++] = br;\n        }\n      } else {\n        if (i === 0) break;\n        a = EDGE_STACK[--i];\n      }\n    }\n    return ar;\n  }\n  _link(a, b) {\n    this._halfedges[a] = b;\n    if (b !== -1) this._halfedges[b] = a;\n  }\n\n  // add a new triangle given vertex indices and adjacent half-edge ids\n  _addTriangle(i0, i1, i2, a, b, c) {\n    const t = this.trianglesLen;\n    this._triangles[t] = i0;\n    this._triangles[t + 1] = i1;\n    this._triangles[t + 2] = i2;\n    this._link(t, a);\n    this._link(t + 1, b);\n    this._link(t + 2, c);\n    this.trianglesLen += 3;\n    return t;\n  }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n  const p = dx / (Math.abs(dx) + Math.abs(dy));\n  return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\nfunction dist(ax, ay, bx, by) {\n  const dx = ax - bx;\n  const dy = ay - by;\n  return dx * dx + dy * dy;\n}\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n  const dx = ax - px;\n  const dy = ay - py;\n  const ex = bx - px;\n  const ey = by - py;\n  const fx = cx - px;\n  const fy = cy - py;\n  const ap = dx * dx + dy * dy;\n  const bp = ex * ex + ey * ey;\n  const cp = fx * fx + fy * fy;\n  return dx * (ey * cp - bp * fy) - dy * (ex * cp - bp * fx) + ap * (ex * fy - ey * fx) < 0;\n}\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n  const dx = bx - ax;\n  const dy = by - ay;\n  const ex = cx - ax;\n  const ey = cy - ay;\n  const bl = dx * dx + dy * dy;\n  const cl = ex * ex + ey * ey;\n  const d = 0.5 / (dx * ey - dy * ex);\n  const x = (ey * bl - dy * cl) * d;\n  const y = (dx * cl - ex * bl) * d;\n  return x * x + y * y;\n}\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n  const dx = bx - ax;\n  const dy = by - ay;\n  const ex = cx - ax;\n  const ey = cy - ay;\n  const bl = dx * dx + dy * dy;\n  const cl = ex * ex + ey * ey;\n  const d = 0.5 / (dx * ey - dy * ex);\n  const x = ax + (ey * bl - dy * cl) * d;\n  const y = ay + (dx * cl - ex * bl) * d;\n  return {\n    x,\n    y\n  };\n}\nfunction quicksort(ids, dists, left, right) {\n  if (right - left <= 20) {\n    for (let i = left + 1; i <= right; i++) {\n      const temp = ids[i];\n      const tempDist = dists[temp];\n      let j = i - 1;\n      while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n      ids[j + 1] = temp;\n    }\n  } else {\n    const median = left + right >> 1;\n    let i = left + 1;\n    let j = right;\n    swap(ids, median, i);\n    if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n    if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n    if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n    const temp = ids[i];\n    const tempDist = dists[temp];\n    while (true) {\n      do i++; while (dists[ids[i]] < tempDist);\n      do j--; while (dists[ids[j]] > tempDist);\n      if (j < i) break;\n      swap(ids, i, j);\n    }\n    ids[left + 1] = ids[j];\n    ids[j] = temp;\n    if (right - i + 1 >= j - left) {\n      quicksort(ids, dists, i, right);\n      quicksort(ids, dists, left, j - 1);\n    } else {\n      quicksort(ids, dists, left, j - 1);\n      quicksort(ids, dists, i, right);\n    }\n  }\n}\nfunction swap(arr, i, j) {\n  const tmp = arr[i];\n  arr[i] = arr[j];\n  arr[j] = tmp;\n}\nfunction defaultGetX(p) {\n  return p[0];\n}\nfunction defaultGetY(p) {\n  return p[1];\n}", "map": {"version": 3, "names": ["EPSILON", "Math", "pow", "EDGE_STACK", "Uint32Array", "orient2d", "Delaunator", "from", "points", "getX", "defaultGetX", "getY", "defaultGetY", "n", "length", "coords", "Float64Array", "i", "p", "constructor", "Error", "max<PERSON>rian<PERSON>", "max", "_triangles", "_halfedges", "Int32Array", "_hashSize", "ceil", "sqrt", "_hullPrev", "_hullNext", "_hullTri", "_hullHash", "_ids", "_dists", "update", "hullPrev", "hullNext", "hullTri", "hullHash", "minX", "Infinity", "minY", "maxX", "maxY", "x", "y", "cx", "cy", "i0", "i1", "i2", "minDist", "d", "dist", "i0x", "i0y", "i1x", "i1y", "minRadius", "r", "circumradius", "i2x", "i2y", "quicksort", "hull", "j", "d0", "id", "subarray", "triangles", "halfedges", "center", "circumcenter", "_cx", "_cy", "_hullStart", "hullSize", "fill", "_hashKey", "trianglesLen", "_addTriangle", "k", "xp", "yp", "abs", "start", "key", "e", "q", "t", "_legalize", "floor", "pseudoAngle", "a", "ar", "b", "a0", "b0", "al", "bl", "p0", "pr", "pl", "p1", "illegal", "inCircle", "hbl", "_link", "br", "c", "dx", "dy", "ax", "ay", "bx", "by", "px", "py", "ex", "ey", "fx", "fy", "ap", "bp", "cp", "cl", "ids", "dists", "left", "right", "temp", "tempDist", "median", "swap", "arr", "tmp"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/delaunator/index.js"], "sourcesContent": ["\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nimport {orient2d} from 'robust-predicates';\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n"], "mappings": "AACA,MAAMA,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,MAAMC,UAAU,GAAG,IAAIC,WAAW,CAAC,GAAG,CAAC;AAEvC,SAAQC,QAAQ,QAAO,mBAAmB;AAE1C,eAAe,MAAMC,UAAU,CAAC;EAE5B,OAAOC,IAAIA,CAACC,MAAM,EAAEC,IAAI,GAAGC,WAAW,EAAEC,IAAI,GAAGC,WAAW,EAAE;IACxD,MAAMC,CAAC,GAAGL,MAAM,CAACM,MAAM;IACvB,MAAMC,MAAM,GAAG,IAAIC,YAAY,CAACH,CAAC,GAAG,CAAC,CAAC;IAEtC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxB,MAAMC,CAAC,GAAGV,MAAM,CAACS,CAAC,CAAC;MACnBF,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,GAAGR,IAAI,CAACS,CAAC,CAAC;MACvBH,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,GAAGN,IAAI,CAACO,CAAC,CAAC;IAC/B;IAEA,OAAO,IAAIZ,UAAU,CAACS,MAAM,CAAC;EACjC;EAEAI,WAAWA,CAACJ,MAAM,EAAE;IAChB,MAAMF,CAAC,GAAGE,MAAM,CAACD,MAAM,IAAI,CAAC;IAC5B,IAAID,CAAC,GAAG,CAAC,IAAI,OAAOE,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIK,KAAK,CAAC,qCAAqC,CAAC;IAElG,IAAI,CAACL,MAAM,GAAGA,MAAM;;IAEpB;IACA,MAAMM,YAAY,GAAGpB,IAAI,CAACqB,GAAG,CAAC,CAAC,GAAGT,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC3C,IAAI,CAACU,UAAU,GAAG,IAAInB,WAAW,CAACiB,YAAY,GAAG,CAAC,CAAC;IACnD,IAAI,CAACG,UAAU,GAAG,IAAIC,UAAU,CAACJ,YAAY,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI,CAACK,SAAS,GAAGzB,IAAI,CAAC0B,IAAI,CAAC1B,IAAI,CAAC2B,IAAI,CAACf,CAAC,CAAC,CAAC;IACxC,IAAI,CAACgB,SAAS,GAAG,IAAIzB,WAAW,CAACS,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAACiB,SAAS,GAAG,IAAI1B,WAAW,CAACS,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAACkB,QAAQ,GAAG,IAAI3B,WAAW,CAACS,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAACmB,SAAS,GAAG,IAAIP,UAAU,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAI,CAACO,IAAI,GAAG,IAAI7B,WAAW,CAACS,CAAC,CAAC;IAC9B,IAAI,CAACqB,MAAM,GAAG,IAAIlB,YAAY,CAACH,CAAC,CAAC;IAEjC,IAAI,CAACsB,MAAM,CAAC,CAAC;EACjB;EAEAA,MAAMA,CAAA,EAAG;IACL,MAAM;MAACpB,MAAM;MAAEc,SAAS,EAAEO,QAAQ;MAAEN,SAAS,EAAEO,QAAQ;MAAEN,QAAQ,EAAEO,OAAO;MAAEN,SAAS,EAAEO;IAAQ,CAAC,GAAI,IAAI;IACxG,MAAM1B,CAAC,GAAGE,MAAM,CAACD,MAAM,IAAI,CAAC;;IAE5B;IACA,IAAI0B,IAAI,GAAGC,QAAQ;IACnB,IAAIC,IAAI,GAAGD,QAAQ;IACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;IACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;IAEpB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxB,MAAM4B,CAAC,GAAG9B,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC;MACvB,MAAM6B,CAAC,GAAG/B,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC;MAC3B,IAAI4B,CAAC,GAAGL,IAAI,EAAEA,IAAI,GAAGK,CAAC;MACtB,IAAIC,CAAC,GAAGJ,IAAI,EAAEA,IAAI,GAAGI,CAAC;MACtB,IAAID,CAAC,GAAGF,IAAI,EAAEA,IAAI,GAAGE,CAAC;MACtB,IAAIC,CAAC,GAAGF,IAAI,EAAEA,IAAI,GAAGE,CAAC;MACtB,IAAI,CAACb,IAAI,CAAChB,CAAC,CAAC,GAAGA,CAAC;IACpB;IACA,MAAM8B,EAAE,GAAG,CAACP,IAAI,GAAGG,IAAI,IAAI,CAAC;IAC5B,MAAMK,EAAE,GAAG,CAACN,IAAI,GAAGE,IAAI,IAAI,CAAC;IAE5B,IAAIK,EAAE,EAAEC,EAAE,EAAEC,EAAE;;IAEd;IACA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEmC,OAAO,GAAGX,QAAQ,EAAExB,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MAC5C,MAAMoC,CAAC,GAAGC,IAAI,CAACP,EAAE,EAAEC,EAAE,EAAEjC,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,CAAC;MACxD,IAAIoC,CAAC,GAAGD,OAAO,EAAE;QACbH,EAAE,GAAGhC,CAAC;QACNmC,OAAO,GAAGC,CAAC;MACf;IACJ;IACA,MAAME,GAAG,GAAGxC,MAAM,CAAC,CAAC,GAAGkC,EAAE,CAAC;IAC1B,MAAMO,GAAG,GAAGzC,MAAM,CAAC,CAAC,GAAGkC,EAAE,GAAG,CAAC,CAAC;;IAE9B;IACA,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEmC,OAAO,GAAGX,QAAQ,EAAExB,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MAC5C,IAAIA,CAAC,KAAKgC,EAAE,EAAE;MACd,MAAMI,CAAC,GAAGC,IAAI,CAACC,GAAG,EAAEC,GAAG,EAAEzC,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1D,IAAIoC,CAAC,GAAGD,OAAO,IAAIC,CAAC,GAAG,CAAC,EAAE;QACtBH,EAAE,GAAGjC,CAAC;QACNmC,OAAO,GAAGC,CAAC;MACf;IACJ;IACA,IAAII,GAAG,GAAG1C,MAAM,CAAC,CAAC,GAAGmC,EAAE,CAAC;IACxB,IAAIQ,GAAG,GAAG3C,MAAM,CAAC,CAAC,GAAGmC,EAAE,GAAG,CAAC,CAAC;IAE5B,IAAIS,SAAS,GAAGlB,QAAQ;;IAExB;IACA,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxB,IAAIA,CAAC,KAAKgC,EAAE,IAAIhC,CAAC,KAAKiC,EAAE,EAAE;MAC1B,MAAMU,CAAC,GAAGC,YAAY,CAACN,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE3C,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5E,IAAI2C,CAAC,GAAGD,SAAS,EAAE;QACfR,EAAE,GAAGlC,CAAC;QACN0C,SAAS,GAAGC,CAAC;MACjB;IACJ;IACA,IAAIE,GAAG,GAAG/C,MAAM,CAAC,CAAC,GAAGoC,EAAE,CAAC;IACxB,IAAIY,GAAG,GAAGhD,MAAM,CAAC,CAAC,GAAGoC,EAAE,GAAG,CAAC,CAAC;IAE5B,IAAIQ,SAAS,KAAKlB,QAAQ,EAAE;MACxB;MACA;MACA,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;QACxB,IAAI,CAACiB,MAAM,CAACjB,CAAC,CAAC,GAAIF,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,IAAMA,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAE;MACnF;MACAiD,SAAS,CAAC,IAAI,CAAC/B,IAAI,EAAE,IAAI,CAACC,MAAM,EAAE,CAAC,EAAErB,CAAC,GAAG,CAAC,CAAC;MAC3C,MAAMoD,IAAI,GAAG,IAAI7D,WAAW,CAACS,CAAC,CAAC;MAC/B,IAAIqD,CAAC,GAAG,CAAC;MACT,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEkD,EAAE,GAAG,CAAC1B,QAAQ,EAAExB,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;QACxC,MAAMmD,EAAE,GAAG,IAAI,CAACnC,IAAI,CAAChB,CAAC,CAAC;QACvB,MAAMoC,CAAC,GAAG,IAAI,CAACnB,MAAM,CAACkC,EAAE,CAAC;QACzB,IAAIf,CAAC,GAAGc,EAAE,EAAE;UACRF,IAAI,CAACC,CAAC,EAAE,CAAC,GAAGE,EAAE;UACdD,EAAE,GAAGd,CAAC;QACV;MACJ;MACA,IAAI,CAACY,IAAI,GAAGA,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAAC;MAC/B,IAAI,CAACI,SAAS,GAAG,IAAIlE,WAAW,CAAC,CAAC,CAAC;MACnC,IAAI,CAACmE,SAAS,GAAG,IAAInE,WAAW,CAAC,CAAC,CAAC;MACnC;IACJ;;IAEA;IACA,IAAIC,QAAQ,CAACkD,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEI,GAAG,EAAEC,GAAG,CAAC,GAAG,CAAC,EAAE;MAC5C,MAAM9C,CAAC,GAAGiC,EAAE;MACZ,MAAML,CAAC,GAAGY,GAAG;MACb,MAAMX,CAAC,GAAGY,GAAG;MACbR,EAAE,GAAGC,EAAE;MACPM,GAAG,GAAGK,GAAG;MACTJ,GAAG,GAAGK,GAAG;MACTZ,EAAE,GAAGlC,CAAC;MACN6C,GAAG,GAAGjB,CAAC;MACPkB,GAAG,GAAGjB,CAAC;IACX;IAEA,MAAM0B,MAAM,GAAGC,YAAY,CAAClB,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEI,GAAG,EAAEC,GAAG,CAAC;IACzD,IAAI,CAACW,GAAG,GAAGF,MAAM,CAAC3B,CAAC;IACnB,IAAI,CAAC8B,GAAG,GAAGH,MAAM,CAAC1B,CAAC;IAEnB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MACxB,IAAI,CAACiB,MAAM,CAACjB,CAAC,CAAC,GAAGqC,IAAI,CAACvC,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC,EAAEuD,MAAM,CAAC3B,CAAC,EAAE2B,MAAM,CAAC1B,CAAC,CAAC;IAC/E;;IAEA;IACAkB,SAAS,CAAC,IAAI,CAAC/B,IAAI,EAAE,IAAI,CAACC,MAAM,EAAE,CAAC,EAAErB,CAAC,GAAG,CAAC,CAAC;;IAE3C;IACA,IAAI,CAAC+D,UAAU,GAAG3B,EAAE;IACpB,IAAI4B,QAAQ,GAAG,CAAC;IAEhBxC,QAAQ,CAACY,EAAE,CAAC,GAAGb,QAAQ,CAACe,EAAE,CAAC,GAAGD,EAAE;IAChCb,QAAQ,CAACa,EAAE,CAAC,GAAGd,QAAQ,CAACa,EAAE,CAAC,GAAGE,EAAE;IAChCd,QAAQ,CAACc,EAAE,CAAC,GAAGf,QAAQ,CAACc,EAAE,CAAC,GAAGD,EAAE;IAEhCX,OAAO,CAACW,EAAE,CAAC,GAAG,CAAC;IACfX,OAAO,CAACY,EAAE,CAAC,GAAG,CAAC;IACfZ,OAAO,CAACa,EAAE,CAAC,GAAG,CAAC;IAEfZ,QAAQ,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjBvC,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAACxB,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAGP,EAAE;IACtCV,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAACtB,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAGR,EAAE;IACtCX,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAACjB,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAGZ,EAAE;IAEtC,IAAI,CAAC6B,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,YAAY,CAAChC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzC,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEF,CAAC,GAAG,IAAI,CAACjD,IAAI,CAACnB,MAAM,EAAEoE,CAAC,EAAE,EAAE;MAC/C,MAAMjE,CAAC,GAAG,IAAI,CAACgB,IAAI,CAACiD,CAAC,CAAC;MACtB,MAAMrC,CAAC,GAAG9B,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC;MACvB,MAAM6B,CAAC,GAAG/B,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC;;MAE3B;MACA,IAAIiE,CAAC,GAAG,CAAC,IAAIjF,IAAI,CAACoF,GAAG,CAACxC,CAAC,GAAGsC,EAAE,CAAC,IAAInF,OAAO,IAAIC,IAAI,CAACoF,GAAG,CAACvC,CAAC,GAAGsC,EAAE,CAAC,IAAIpF,OAAO,EAAE;MACzEmF,EAAE,GAAGtC,CAAC;MACNuC,EAAE,GAAGtC,CAAC;;MAEN;MACA,IAAI7B,CAAC,KAAKgC,EAAE,IAAIhC,CAAC,KAAKiC,EAAE,IAAIjC,CAAC,KAAKkC,EAAE,EAAE;;MAEtC;MACA,IAAImC,KAAK,GAAG,CAAC;MACb,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEqB,GAAG,GAAG,IAAI,CAACR,QAAQ,CAAClC,CAAC,EAAEC,CAAC,CAAC,EAAEoB,CAAC,GAAG,IAAI,CAACxC,SAAS,EAAEwC,CAAC,EAAE,EAAE;QAChEoB,KAAK,GAAG/C,QAAQ,CAAC,CAACgD,GAAG,GAAGrB,CAAC,IAAI,IAAI,CAACxC,SAAS,CAAC;QAC5C,IAAI4D,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKjD,QAAQ,CAACiD,KAAK,CAAC,EAAE;MACnD;MAEAA,KAAK,GAAGlD,QAAQ,CAACkD,KAAK,CAAC;MACvB,IAAIE,CAAC,GAAGF,KAAK;QAAEG,CAAC;MAChB,OAAOA,CAAC,GAAGpD,QAAQ,CAACmD,CAAC,CAAC,EAAEnF,QAAQ,CAACwC,CAAC,EAAEC,CAAC,EAAE/B,MAAM,CAAC,CAAC,GAAGyE,CAAC,CAAC,EAAEzE,MAAM,CAAC,CAAC,GAAGyE,CAAC,GAAG,CAAC,CAAC,EAAEzE,MAAM,CAAC,CAAC,GAAG0E,CAAC,CAAC,EAAE1E,MAAM,CAAC,CAAC,GAAG0E,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QAC7GD,CAAC,GAAGC,CAAC;QACL,IAAID,CAAC,KAAKF,KAAK,EAAE;UACbE,CAAC,GAAG,CAAC,CAAC;UACN;QACJ;MACJ;MACA,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;;MAExB;MACA,IAAIE,CAAC,GAAG,IAAI,CAACT,YAAY,CAACO,CAAC,EAAEvE,CAAC,EAAEoB,QAAQ,CAACmD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAElD,OAAO,CAACkD,CAAC,CAAC,CAAC;;MAEhE;MACAlD,OAAO,CAACrB,CAAC,CAAC,GAAG,IAAI,CAAC0E,SAAS,CAACD,CAAC,GAAG,CAAC,CAAC;MAClCpD,OAAO,CAACkD,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC;MAChBb,QAAQ,EAAE;;MAEV;MACA,IAAIhE,CAAC,GAAGwB,QAAQ,CAACmD,CAAC,CAAC;MACnB,OAAOC,CAAC,GAAGpD,QAAQ,CAACxB,CAAC,CAAC,EAAER,QAAQ,CAACwC,CAAC,EAAEC,CAAC,EAAE/B,MAAM,CAAC,CAAC,GAAGF,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,GAAGF,CAAC,GAAG,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,GAAG0E,CAAC,CAAC,EAAE1E,MAAM,CAAC,CAAC,GAAG0E,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QAC5GC,CAAC,GAAG,IAAI,CAACT,YAAY,CAACpE,CAAC,EAAEI,CAAC,EAAEwE,CAAC,EAAEnD,OAAO,CAACrB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqB,OAAO,CAACzB,CAAC,CAAC,CAAC;QAC1DyB,OAAO,CAACrB,CAAC,CAAC,GAAG,IAAI,CAAC0E,SAAS,CAACD,CAAC,GAAG,CAAC,CAAC;QAClCrD,QAAQ,CAACxB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;QACjBgE,QAAQ,EAAE;QACVhE,CAAC,GAAG4E,CAAC;MACT;;MAEA;MACA,IAAID,CAAC,KAAKF,KAAK,EAAE;QACb,OAAOG,CAAC,GAAGrD,QAAQ,CAACoD,CAAC,CAAC,EAAEnF,QAAQ,CAACwC,CAAC,EAAEC,CAAC,EAAE/B,MAAM,CAAC,CAAC,GAAG0E,CAAC,CAAC,EAAE1E,MAAM,CAAC,CAAC,GAAG0E,CAAC,GAAG,CAAC,CAAC,EAAE1E,MAAM,CAAC,CAAC,GAAGyE,CAAC,CAAC,EAAEzE,MAAM,CAAC,CAAC,GAAGyE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;UAC5GE,CAAC,GAAG,IAAI,CAACT,YAAY,CAACQ,CAAC,EAAExE,CAAC,EAAEuE,CAAC,EAAE,CAAC,CAAC,EAAElD,OAAO,CAACkD,CAAC,CAAC,EAAElD,OAAO,CAACmD,CAAC,CAAC,CAAC;UAC1D,IAAI,CAACE,SAAS,CAACD,CAAC,GAAG,CAAC,CAAC;UACrBpD,OAAO,CAACmD,CAAC,CAAC,GAAGC,CAAC;UACdrD,QAAQ,CAACmD,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;UACjBX,QAAQ,EAAE;UACVW,CAAC,GAAGC,CAAC;QACT;MACJ;;MAEA;MACA,IAAI,CAACb,UAAU,GAAGxC,QAAQ,CAACnB,CAAC,CAAC,GAAGuE,CAAC;MACjCnD,QAAQ,CAACmD,CAAC,CAAC,GAAGpD,QAAQ,CAACvB,CAAC,CAAC,GAAGI,CAAC;MAC7BoB,QAAQ,CAACpB,CAAC,CAAC,GAAGJ,CAAC;;MAEf;MACA0B,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAAClC,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG7B,CAAC;MACjCsB,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAAChE,MAAM,CAAC,CAAC,GAAGyE,CAAC,CAAC,EAAEzE,MAAM,CAAC,CAAC,GAAGyE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC;IACjE;IAEA,IAAI,CAACvB,IAAI,GAAG,IAAI7D,WAAW,CAACyE,QAAQ,CAAC;IACrC,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEuE,CAAC,GAAG,IAAI,CAACZ,UAAU,EAAE3D,CAAC,GAAG4D,QAAQ,EAAE5D,CAAC,EAAE,EAAE;MACpD,IAAI,CAACgD,IAAI,CAAChD,CAAC,CAAC,GAAGuE,CAAC;MAChBA,CAAC,GAAGnD,QAAQ,CAACmD,CAAC,CAAC;IACnB;;IAEA;IACA,IAAI,CAAClB,SAAS,GAAG,IAAI,CAAC/C,UAAU,CAAC8C,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACW,YAAY,CAAC;IAC/D,IAAI,CAACT,SAAS,GAAG,IAAI,CAAC/C,UAAU,CAAC6C,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACW,YAAY,CAAC;EACnE;EAEAD,QAAQA,CAAClC,CAAC,EAAEC,CAAC,EAAE;IACX,OAAO7C,IAAI,CAAC2F,KAAK,CAACC,WAAW,CAAChD,CAAC,GAAG,IAAI,CAAC6B,GAAG,EAAE5B,CAAC,GAAG,IAAI,CAAC6B,GAAG,CAAC,GAAG,IAAI,CAACjD,SAAS,CAAC,GAAG,IAAI,CAACA,SAAS;EAChG;EAEAiE,SAASA,CAACG,CAAC,EAAE;IACT,MAAM;MAACvE,UAAU,EAAE+C,SAAS;MAAE9C,UAAU,EAAE+C,SAAS;MAAExD;IAAM,CAAC,GAAG,IAAI;IAEnE,IAAIE,CAAC,GAAG,CAAC;IACT,IAAI8E,EAAE,GAAG,CAAC;;IAEV;IACA,OAAO,IAAI,EAAE;MACT,MAAMC,CAAC,GAAGzB,SAAS,CAACuB,CAAC,CAAC;;MAEtB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMG,EAAE,GAAGH,CAAC,GAAGA,CAAC,GAAG,CAAC;MACpBC,EAAE,GAAGE,EAAE,GAAG,CAACH,CAAC,GAAG,CAAC,IAAI,CAAC;MAErB,IAAIE,CAAC,KAAK,CAAC,CAAC,EAAE;QAAE;QACZ,IAAI/E,CAAC,KAAK,CAAC,EAAE;QACb6E,CAAC,GAAG3F,UAAU,CAAC,EAAEc,CAAC,CAAC;QACnB;MACJ;MAEA,MAAMiF,EAAE,GAAGF,CAAC,GAAGA,CAAC,GAAG,CAAC;MACpB,MAAMG,EAAE,GAAGF,EAAE,GAAG,CAACH,CAAC,GAAG,CAAC,IAAI,CAAC;MAC3B,MAAMM,EAAE,GAAGF,EAAE,GAAG,CAACF,CAAC,GAAG,CAAC,IAAI,CAAC;MAE3B,MAAMK,EAAE,GAAG/B,SAAS,CAACyB,EAAE,CAAC;MACxB,MAAMO,EAAE,GAAGhC,SAAS,CAACwB,CAAC,CAAC;MACvB,MAAMS,EAAE,GAAGjC,SAAS,CAAC6B,EAAE,CAAC;MACxB,MAAMK,EAAE,GAAGlC,SAAS,CAAC8B,EAAE,CAAC;MAExB,MAAMK,OAAO,GAAGC,QAAQ,CACpB3F,MAAM,CAAC,CAAC,GAAGsF,EAAE,CAAC,EAAEtF,MAAM,CAAC,CAAC,GAAGsF,EAAE,GAAG,CAAC,CAAC,EAClCtF,MAAM,CAAC,CAAC,GAAGuF,EAAE,CAAC,EAAEvF,MAAM,CAAC,CAAC,GAAGuF,EAAE,GAAG,CAAC,CAAC,EAClCvF,MAAM,CAAC,CAAC,GAAGwF,EAAE,CAAC,EAAExF,MAAM,CAAC,CAAC,GAAGwF,EAAE,GAAG,CAAC,CAAC,EAClCxF,MAAM,CAAC,CAAC,GAAGyF,EAAE,CAAC,EAAEzF,MAAM,CAAC,CAAC,GAAGyF,EAAE,GAAG,CAAC,CAAC,CAAC;MAEvC,IAAIC,OAAO,EAAE;QACTnC,SAAS,CAACwB,CAAC,CAAC,GAAGU,EAAE;QACjBlC,SAAS,CAAC0B,CAAC,CAAC,GAAGK,EAAE;QAEjB,MAAMM,GAAG,GAAGpC,SAAS,CAAC6B,EAAE,CAAC;;QAEzB;QACA,IAAIO,GAAG,KAAK,CAAC,CAAC,EAAE;UACZ,IAAInB,CAAC,GAAG,IAAI,CAACZ,UAAU;UACvB,GAAG;YACC,IAAI,IAAI,CAAC7C,QAAQ,CAACyD,CAAC,CAAC,KAAKY,EAAE,EAAE;cACzB,IAAI,CAACrE,QAAQ,CAACyD,CAAC,CAAC,GAAGM,CAAC;cACpB;YACJ;YACAN,CAAC,GAAG,IAAI,CAAC3D,SAAS,CAAC2D,CAAC,CAAC;UACzB,CAAC,QAAQA,CAAC,KAAK,IAAI,CAACZ,UAAU;QAClC;QACA,IAAI,CAACgC,KAAK,CAACd,CAAC,EAAEa,GAAG,CAAC;QAClB,IAAI,CAACC,KAAK,CAACZ,CAAC,EAAEzB,SAAS,CAACwB,EAAE,CAAC,CAAC;QAC5B,IAAI,CAACa,KAAK,CAACb,EAAE,EAAEK,EAAE,CAAC;QAElB,MAAMS,EAAE,GAAGX,EAAE,GAAG,CAACF,CAAC,GAAG,CAAC,IAAI,CAAC;;QAE3B;QACA,IAAI/E,CAAC,GAAGd,UAAU,CAACW,MAAM,EAAE;UACvBX,UAAU,CAACc,CAAC,EAAE,CAAC,GAAG4F,EAAE;QACxB;MACJ,CAAC,MAAM;QACH,IAAI5F,CAAC,KAAK,CAAC,EAAE;QACb6E,CAAC,GAAG3F,UAAU,CAAC,EAAEc,CAAC,CAAC;MACvB;IACJ;IAEA,OAAO8E,EAAE;EACb;EAEAa,KAAKA,CAACd,CAAC,EAAEE,CAAC,EAAE;IACR,IAAI,CAACxE,UAAU,CAACsE,CAAC,CAAC,GAAGE,CAAC;IACtB,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACxE,UAAU,CAACwE,CAAC,CAAC,GAAGF,CAAC;EACxC;;EAEA;EACAb,YAAYA,CAAChC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE2C,CAAC,EAAEE,CAAC,EAAEc,CAAC,EAAE;IAC9B,MAAMpB,CAAC,GAAG,IAAI,CAACV,YAAY;IAE3B,IAAI,CAACzD,UAAU,CAACmE,CAAC,CAAC,GAAGzC,EAAE;IACvB,IAAI,CAAC1B,UAAU,CAACmE,CAAC,GAAG,CAAC,CAAC,GAAGxC,EAAE;IAC3B,IAAI,CAAC3B,UAAU,CAACmE,CAAC,GAAG,CAAC,CAAC,GAAGvC,EAAE;IAE3B,IAAI,CAACyD,KAAK,CAAClB,CAAC,EAAEI,CAAC,CAAC;IAChB,IAAI,CAACc,KAAK,CAAClB,CAAC,GAAG,CAAC,EAAEM,CAAC,CAAC;IACpB,IAAI,CAACY,KAAK,CAAClB,CAAC,GAAG,CAAC,EAAEoB,CAAC,CAAC;IAEpB,IAAI,CAAC9B,YAAY,IAAI,CAAC;IAEtB,OAAOU,CAAC;EACZ;AACJ;;AAEA;AACA,SAASG,WAAWA,CAACkB,EAAE,EAAEC,EAAE,EAAE;EACzB,MAAM9F,CAAC,GAAG6F,EAAE,IAAI9G,IAAI,CAACoF,GAAG,CAAC0B,EAAE,CAAC,GAAG9G,IAAI,CAACoF,GAAG,CAAC2B,EAAE,CAAC,CAAC;EAC5C,OAAO,CAACA,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG9F,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC;AAEA,SAASoC,IAAIA,CAAC2D,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC1B,MAAML,EAAE,GAAGE,EAAE,GAAGE,EAAE;EAClB,MAAMH,EAAE,GAAGE,EAAE,GAAGE,EAAE;EAClB,OAAOL,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;AAC5B;AAEA,SAASN,QAAQA,CAACO,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAErE,EAAE,EAAEC,EAAE,EAAEqE,EAAE,EAAEC,EAAE,EAAE;EAC9C,MAAMP,EAAE,GAAGE,EAAE,GAAGI,EAAE;EAClB,MAAML,EAAE,GAAGE,EAAE,GAAGI,EAAE;EAClB,MAAMC,EAAE,GAAGJ,EAAE,GAAGE,EAAE;EAClB,MAAMG,EAAE,GAAGJ,EAAE,GAAGE,EAAE;EAClB,MAAMG,EAAE,GAAG1E,EAAE,GAAGsE,EAAE;EAClB,MAAMK,EAAE,GAAG1E,EAAE,GAAGsE,EAAE;EAElB,MAAMK,EAAE,GAAGZ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMY,EAAE,GAAGL,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMK,EAAE,GAAGJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAE5B,OAAOX,EAAE,IAAIS,EAAE,GAAGK,EAAE,GAAGD,EAAE,GAAGF,EAAE,CAAC,GACxBV,EAAE,IAAIO,EAAE,GAAGM,EAAE,GAAGD,EAAE,GAAGH,EAAE,CAAC,GACxBE,EAAE,IAAIJ,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE,CAAC,GAAG,CAAC;AACvC;AAEA,SAAS5D,YAAYA,CAACoD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAErE,EAAE,EAAEC,EAAE,EAAE;EAC1C,MAAM+D,EAAE,GAAGI,EAAE,GAAGF,EAAE;EAClB,MAAMD,EAAE,GAAGI,EAAE,GAAGF,EAAE;EAClB,MAAMK,EAAE,GAAGxE,EAAE,GAAGkE,EAAE;EAClB,MAAMO,EAAE,GAAGxE,EAAE,GAAGkE,EAAE;EAElB,MAAMd,EAAE,GAAGW,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMc,EAAE,GAAGP,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMnE,CAAC,GAAG,GAAG,IAAI0D,EAAE,GAAGS,EAAE,GAAGR,EAAE,GAAGO,EAAE,CAAC;EAEnC,MAAM1E,CAAC,GAAG,CAAC2E,EAAE,GAAGpB,EAAE,GAAGY,EAAE,GAAGc,EAAE,IAAIzE,CAAC;EACjC,MAAMP,CAAC,GAAG,CAACiE,EAAE,GAAGe,EAAE,GAAGP,EAAE,GAAGnB,EAAE,IAAI/C,CAAC;EAEjC,OAAOR,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;AACxB;AAEA,SAAS2B,YAAYA,CAACwC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAErE,EAAE,EAAEC,EAAE,EAAE;EAC1C,MAAM+D,EAAE,GAAGI,EAAE,GAAGF,EAAE;EAClB,MAAMD,EAAE,GAAGI,EAAE,GAAGF,EAAE;EAClB,MAAMK,EAAE,GAAGxE,EAAE,GAAGkE,EAAE;EAClB,MAAMO,EAAE,GAAGxE,EAAE,GAAGkE,EAAE;EAElB,MAAMd,EAAE,GAAGW,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMc,EAAE,GAAGP,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAC5B,MAAMnE,CAAC,GAAG,GAAG,IAAI0D,EAAE,GAAGS,EAAE,GAAGR,EAAE,GAAGO,EAAE,CAAC;EAEnC,MAAM1E,CAAC,GAAGoE,EAAE,GAAG,CAACO,EAAE,GAAGpB,EAAE,GAAGY,EAAE,GAAGc,EAAE,IAAIzE,CAAC;EACtC,MAAMP,CAAC,GAAGoE,EAAE,GAAG,CAACH,EAAE,GAAGe,EAAE,GAAGP,EAAE,GAAGnB,EAAE,IAAI/C,CAAC;EAEtC,OAAO;IAACR,CAAC;IAAEC;EAAC,CAAC;AACjB;AAEA,SAASkB,SAASA,CAAC+D,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACxC,IAAIA,KAAK,GAAGD,IAAI,IAAI,EAAE,EAAE;IACpB,KAAK,IAAIhH,CAAC,GAAGgH,IAAI,GAAG,CAAC,EAAEhH,CAAC,IAAIiH,KAAK,EAAEjH,CAAC,EAAE,EAAE;MACpC,MAAMkH,IAAI,GAAGJ,GAAG,CAAC9G,CAAC,CAAC;MACnB,MAAMmH,QAAQ,GAAGJ,KAAK,CAACG,IAAI,CAAC;MAC5B,IAAIjE,CAAC,GAAGjD,CAAC,GAAG,CAAC;MACb,OAAOiD,CAAC,IAAI+D,IAAI,IAAID,KAAK,CAACD,GAAG,CAAC7D,CAAC,CAAC,CAAC,GAAGkE,QAAQ,EAAEL,GAAG,CAAC7D,CAAC,GAAG,CAAC,CAAC,GAAG6D,GAAG,CAAC7D,CAAC,EAAE,CAAC;MACnE6D,GAAG,CAAC7D,CAAC,GAAG,CAAC,CAAC,GAAGiE,IAAI;IACrB;EACJ,CAAC,MAAM;IACH,MAAME,MAAM,GAAIJ,IAAI,GAAGC,KAAK,IAAK,CAAC;IAClC,IAAIjH,CAAC,GAAGgH,IAAI,GAAG,CAAC;IAChB,IAAI/D,CAAC,GAAGgE,KAAK;IACbI,IAAI,CAACP,GAAG,EAAEM,MAAM,EAAEpH,CAAC,CAAC;IACpB,IAAI+G,KAAK,CAACD,GAAG,CAACE,IAAI,CAAC,CAAC,GAAGD,KAAK,CAACD,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEI,IAAI,CAACP,GAAG,EAAEE,IAAI,EAAEC,KAAK,CAAC;IAChE,IAAIF,KAAK,CAACD,GAAG,CAAC9G,CAAC,CAAC,CAAC,GAAG+G,KAAK,CAACD,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEI,IAAI,CAACP,GAAG,EAAE9G,CAAC,EAAEiH,KAAK,CAAC;IAC1D,IAAIF,KAAK,CAACD,GAAG,CAACE,IAAI,CAAC,CAAC,GAAGD,KAAK,CAACD,GAAG,CAAC9G,CAAC,CAAC,CAAC,EAAEqH,IAAI,CAACP,GAAG,EAAEE,IAAI,EAAEhH,CAAC,CAAC;IAExD,MAAMkH,IAAI,GAAGJ,GAAG,CAAC9G,CAAC,CAAC;IACnB,MAAMmH,QAAQ,GAAGJ,KAAK,CAACG,IAAI,CAAC;IAC5B,OAAO,IAAI,EAAE;MACT,GAAGlH,CAAC,EAAE,CAAC,QAAQ+G,KAAK,CAACD,GAAG,CAAC9G,CAAC,CAAC,CAAC,GAAGmH,QAAQ;MACvC,GAAGlE,CAAC,EAAE,CAAC,QAAQ8D,KAAK,CAACD,GAAG,CAAC7D,CAAC,CAAC,CAAC,GAAGkE,QAAQ;MACvC,IAAIlE,CAAC,GAAGjD,CAAC,EAAE;MACXqH,IAAI,CAACP,GAAG,EAAE9G,CAAC,EAAEiD,CAAC,CAAC;IACnB;IACA6D,GAAG,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGF,GAAG,CAAC7D,CAAC,CAAC;IACtB6D,GAAG,CAAC7D,CAAC,CAAC,GAAGiE,IAAI;IAEb,IAAID,KAAK,GAAGjH,CAAC,GAAG,CAAC,IAAIiD,CAAC,GAAG+D,IAAI,EAAE;MAC3BjE,SAAS,CAAC+D,GAAG,EAAEC,KAAK,EAAE/G,CAAC,EAAEiH,KAAK,CAAC;MAC/BlE,SAAS,CAAC+D,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE/D,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC,MAAM;MACHF,SAAS,CAAC+D,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE/D,CAAC,GAAG,CAAC,CAAC;MAClCF,SAAS,CAAC+D,GAAG,EAAEC,KAAK,EAAE/G,CAAC,EAAEiH,KAAK,CAAC;IACnC;EACJ;AACJ;AAEA,SAASI,IAAIA,CAACC,GAAG,EAAEtH,CAAC,EAAEiD,CAAC,EAAE;EACrB,MAAMsE,GAAG,GAAGD,GAAG,CAACtH,CAAC,CAAC;EAClBsH,GAAG,CAACtH,CAAC,CAAC,GAAGsH,GAAG,CAACrE,CAAC,CAAC;EACfqE,GAAG,CAACrE,CAAC,CAAC,GAAGsE,GAAG;AAChB;AAEA,SAAS9H,WAAWA,CAACQ,CAAC,EAAE;EACpB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACf;AACA,SAASN,WAAWA,CAACM,CAAC,EAAE;EACpB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}