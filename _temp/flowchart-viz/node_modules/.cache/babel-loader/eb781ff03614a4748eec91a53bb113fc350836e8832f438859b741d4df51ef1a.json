{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nimport { durationMinute, durationSecond } from \"./duration.js\";\nexport const timeMinute = timeInterval(date => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, date => {\n  return date.getMinutes();\n});\nexport const timeMinutes = timeMinute.range;\nexport const utcMinute = timeInterval(date => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, date => {\n  return date.getUTCMinutes();\n});\nexport const utcMinutes = utcMinute.range;", "map": {"version": 3, "names": ["timeInterval", "durationMinute", "durationSecond", "timeMinute", "date", "setTime", "getMilliseconds", "getSeconds", "step", "start", "end", "getMinutes", "timeMinutes", "range", "utcMinute", "setUTCSeconds", "getUTCMinutes", "utcMinutes"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-time/src/minute.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAC1C,SAAQC,cAAc,EAAEC,cAAc,QAAO,eAAe;AAE5D,OAAO,MAAMC,UAAU,GAAGH,YAAY,CAAEI,IAAI,IAAK;EAC/CA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,GAAGL,cAAc,CAAC;AAClF,CAAC,EAAE,CAACE,IAAI,EAAEI,IAAI,KAAK;EACjBJ,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGI,IAAI,GAAGP,cAAc,CAAC;AAC7C,CAAC,EAAE,CAACQ,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIR,cAAc;AACvC,CAAC,EAAGG,IAAI,IAAK;EACX,OAAOA,IAAI,CAACO,UAAU,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF,OAAO,MAAMC,WAAW,GAAGT,UAAU,CAACU,KAAK;AAE3C,OAAO,MAAMC,SAAS,GAAGd,YAAY,CAAEI,IAAI,IAAK;EAC9CA,IAAI,CAACW,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC,EAAE,CAACX,IAAI,EAAEI,IAAI,KAAK;EACjBJ,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGI,IAAI,GAAGP,cAAc,CAAC;AAC7C,CAAC,EAAE,CAACQ,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIR,cAAc;AACvC,CAAC,EAAGG,IAAI,IAAK;EACX,OAAOA,IAAI,CAACY,aAAa,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEF,OAAO,MAAMC,UAAU,GAAGH,SAAS,CAACD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}