{"ast": null, "code": "/**\n * This nodeLocation tracking is not efficient and should only be used\n * when error recovery is enabled or the Token Vector contains virtual Tokens\n * (e.g, Python Indent/Outdent)\n * As it executes the calculation for every single terminal/nonTerminal\n * and does not rely on the fact the token vector is **sorted**\n */\nexport function setNodeLocationOnlyOffset(currNodeLocation, newLocationInfo) {\n  // First (valid) update for this cst node\n  if (isNaN(currNodeLocation.startOffset) === true) {\n    // assumption1: Token location information is either NaN or a valid number\n    // assumption2: Token location information is fully valid if it exist\n    // (both start/end offsets exist and are numbers).\n    currNodeLocation.startOffset = newLocationInfo.startOffset;\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n  }\n  // Once the startOffset has been updated with a valid number it should never receive\n  // any farther updates as the Token vector is sorted.\n  // We still have to check this this condition for every new possible location info\n  // because with error recovery enabled we may encounter invalid tokens (NaN location props)\n  else if (currNodeLocation.endOffset < newLocationInfo.endOffset === true) {\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n  }\n}\n/**\n * This nodeLocation tracking is not efficient and should only be used\n * when error recovery is enabled or the Token Vector contains virtual Tokens\n * (e.g, Python Indent/Outdent)\n * As it executes the calculation for every single terminal/nonTerminal\n * and does not rely on the fact the token vector is **sorted**\n */\nexport function setNodeLocationFull(currNodeLocation, newLocationInfo) {\n  // First (valid) update for this cst node\n  if (isNaN(currNodeLocation.startOffset) === true) {\n    // assumption1: Token location information is either NaN or a valid number\n    // assumption2: Token location information is fully valid if it exist\n    // (all start/end props exist and are numbers).\n    currNodeLocation.startOffset = newLocationInfo.startOffset;\n    currNodeLocation.startColumn = newLocationInfo.startColumn;\n    currNodeLocation.startLine = newLocationInfo.startLine;\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n    currNodeLocation.endColumn = newLocationInfo.endColumn;\n    currNodeLocation.endLine = newLocationInfo.endLine;\n  }\n  // Once the start props has been updated with a valid number it should never receive\n  // any farther updates as the Token vector is sorted.\n  // We still have to check this this condition for every new possible location info\n  // because with error recovery enabled we may encounter invalid tokens (NaN location props)\n  else if (currNodeLocation.endOffset < newLocationInfo.endOffset === true) {\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n    currNodeLocation.endColumn = newLocationInfo.endColumn;\n    currNodeLocation.endLine = newLocationInfo.endLine;\n  }\n}\nexport function addTerminalToCst(node, token, tokenTypeName) {\n  if (node.children[tokenTypeName] === undefined) {\n    node.children[tokenTypeName] = [token];\n  } else {\n    node.children[tokenTypeName].push(token);\n  }\n}\nexport function addNoneTerminalToCst(node, ruleName, ruleResult) {\n  if (node.children[ruleName] === undefined) {\n    node.children[ruleName] = [ruleResult];\n  } else {\n    node.children[ruleName].push(ruleResult);\n  }\n}", "map": {"version": 3, "names": ["setNodeLocationOnlyOffset", "currNodeLocation", "newLocationInfo", "isNaN", "startOffset", "endOffset", "setNodeLocationFull", "startColumn", "startLine", "endColumn", "endLine", "addTerminalToCst", "node", "token", "tokenTypeName", "children", "undefined", "push", "addNoneTerminalToCst", "ruleName", "ruleResult"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/cst/cst.ts"], "sourcesContent": ["import { CstNode, CstNodeLocation, IToken } from \"@chevrotain/types\";\n\n/**\n * This nodeLocation tracking is not efficient and should only be used\n * when error recovery is enabled or the Token Vector contains virtual Tokens\n * (e.g, Python Indent/Outdent)\n * As it executes the calculation for every single terminal/nonTerminal\n * and does not rely on the fact the token vector is **sorted**\n */\nexport function setNodeLocationOnlyOffset(\n  currNodeLocation: CstNodeLocation,\n  newLocationInfo: Required<Pick<IToken, \"startOffset\" | \"endOffset\">>,\n): void {\n  // First (valid) update for this cst node\n  if (isNaN(currNodeLocation.startOffset) === true) {\n    // assumption1: Token location information is either NaN or a valid number\n    // assumption2: Token location information is fully valid if it exist\n    // (both start/end offsets exist and are numbers).\n    currNodeLocation.startOffset = newLocationInfo.startOffset;\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n  }\n  // Once the startOffset has been updated with a valid number it should never receive\n  // any farther updates as the Token vector is sorted.\n  // We still have to check this this condition for every new possible location info\n  // because with error recovery enabled we may encounter invalid tokens (NaN location props)\n  else if (currNodeLocation.endOffset! < newLocationInfo.endOffset === true) {\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n  }\n}\n\n/**\n * This nodeLocation tracking is not efficient and should only be used\n * when error recovery is enabled or the Token Vector contains virtual Tokens\n * (e.g, Python Indent/Outdent)\n * As it executes the calculation for every single terminal/nonTerminal\n * and does not rely on the fact the token vector is **sorted**\n */\nexport function setNodeLocationFull(\n  currNodeLocation: CstNodeLocation,\n  newLocationInfo: CstNodeLocation,\n): void {\n  // First (valid) update for this cst node\n  if (isNaN(currNodeLocation.startOffset) === true) {\n    // assumption1: Token location information is either NaN or a valid number\n    // assumption2: Token location information is fully valid if it exist\n    // (all start/end props exist and are numbers).\n    currNodeLocation.startOffset = newLocationInfo.startOffset;\n    currNodeLocation.startColumn = newLocationInfo.startColumn;\n    currNodeLocation.startLine = newLocationInfo.startLine;\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n    currNodeLocation.endColumn = newLocationInfo.endColumn;\n    currNodeLocation.endLine = newLocationInfo.endLine;\n  }\n  // Once the start props has been updated with a valid number it should never receive\n  // any farther updates as the Token vector is sorted.\n  // We still have to check this this condition for every new possible location info\n  // because with error recovery enabled we may encounter invalid tokens (NaN location props)\n  else if (currNodeLocation.endOffset! < newLocationInfo.endOffset! === true) {\n    currNodeLocation.endOffset = newLocationInfo.endOffset;\n    currNodeLocation.endColumn = newLocationInfo.endColumn;\n    currNodeLocation.endLine = newLocationInfo.endLine;\n  }\n}\n\nexport function addTerminalToCst(\n  node: CstNode,\n  token: IToken,\n  tokenTypeName: string,\n): void {\n  if (node.children[tokenTypeName] === undefined) {\n    node.children[tokenTypeName] = [token];\n  } else {\n    node.children[tokenTypeName].push(token);\n  }\n}\n\nexport function addNoneTerminalToCst(\n  node: CstNode,\n  ruleName: string,\n  ruleResult: any,\n): void {\n  if (node.children[ruleName] === undefined) {\n    node.children[ruleName] = [ruleResult];\n  } else {\n    node.children[ruleName].push(ruleResult);\n  }\n}\n"], "mappings": "AAEA;;;;;;;AAOA,OAAM,SAAUA,yBAAyBA,CACvCC,gBAAiC,EACjCC,eAAoE;EAEpE;EACA,IAAIC,KAAK,CAACF,gBAAgB,CAACG,WAAW,CAAC,KAAK,IAAI,EAAE;IAChD;IACA;IACA;IACAH,gBAAgB,CAACG,WAAW,GAAGF,eAAe,CAACE,WAAW;IAC1DH,gBAAgB,CAACI,SAAS,GAAGH,eAAe,CAACG,SAAS;;EAExD;EACA;EACA;EACA;EAAA,KACK,IAAIJ,gBAAgB,CAACI,SAAU,GAAGH,eAAe,CAACG,SAAS,KAAK,IAAI,EAAE;IACzEJ,gBAAgB,CAACI,SAAS,GAAGH,eAAe,CAACG,SAAS;;AAE1D;AAEA;;;;;;;AAOA,OAAM,SAAUC,mBAAmBA,CACjCL,gBAAiC,EACjCC,eAAgC;EAEhC;EACA,IAAIC,KAAK,CAACF,gBAAgB,CAACG,WAAW,CAAC,KAAK,IAAI,EAAE;IAChD;IACA;IACA;IACAH,gBAAgB,CAACG,WAAW,GAAGF,eAAe,CAACE,WAAW;IAC1DH,gBAAgB,CAACM,WAAW,GAAGL,eAAe,CAACK,WAAW;IAC1DN,gBAAgB,CAACO,SAAS,GAAGN,eAAe,CAACM,SAAS;IACtDP,gBAAgB,CAACI,SAAS,GAAGH,eAAe,CAACG,SAAS;IACtDJ,gBAAgB,CAACQ,SAAS,GAAGP,eAAe,CAACO,SAAS;IACtDR,gBAAgB,CAACS,OAAO,GAAGR,eAAe,CAACQ,OAAO;;EAEpD;EACA;EACA;EACA;EAAA,KACK,IAAIT,gBAAgB,CAACI,SAAU,GAAGH,eAAe,CAACG,SAAU,KAAK,IAAI,EAAE;IAC1EJ,gBAAgB,CAACI,SAAS,GAAGH,eAAe,CAACG,SAAS;IACtDJ,gBAAgB,CAACQ,SAAS,GAAGP,eAAe,CAACO,SAAS;IACtDR,gBAAgB,CAACS,OAAO,GAAGR,eAAe,CAACQ,OAAO;;AAEtD;AAEA,OAAM,SAAUC,gBAAgBA,CAC9BC,IAAa,EACbC,KAAa,EACbC,aAAqB;EAErB,IAAIF,IAAI,CAACG,QAAQ,CAACD,aAAa,CAAC,KAAKE,SAAS,EAAE;IAC9CJ,IAAI,CAACG,QAAQ,CAACD,aAAa,CAAC,GAAG,CAACD,KAAK,CAAC;GACvC,MAAM;IACLD,IAAI,CAACG,QAAQ,CAACD,aAAa,CAAC,CAACG,IAAI,CAACJ,KAAK,CAAC;;AAE5C;AAEA,OAAM,SAAUK,oBAAoBA,CAClCN,IAAa,EACbO,QAAgB,EAChBC,UAAe;EAEf,IAAIR,IAAI,CAACG,QAAQ,CAACI,QAAQ,CAAC,KAAKH,SAAS,EAAE;IACzCJ,IAAI,CAACG,QAAQ,CAACI,QAAQ,CAAC,GAAG,CAACC,UAAU,CAAC;GACvC,MAAM;IACLR,IAAI,CAACG,QAAQ,CAACI,QAAQ,CAAC,CAACF,IAAI,CAACG,UAAU,CAAC;;AAE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}