{"ast": null, "code": "import { flatMap, isEmpty } from \"lodash-es\";\nimport { defaultGrammarValidatorErrorProvider } from \"../errors_public.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser/parser.js\";\nimport { validateAmbiguousAlternationAlternatives, validateEmptyOrAlternative, validateNoLeftRecursion, validateSomeNonEmptyLookaheadPath } from \"./checks.js\";\nimport { buildAlternativesLookAheadFunc, buildLookaheadFuncForOptionalProd, buildLookaheadFuncForOr, buildSingleAlternativeLookaheadFunction, getProdType } from \"./lookahead.js\";\nexport class LLkLookaheadStrategy {\n  constructor(options) {\n    var _a;\n    this.maxLookahead = (_a = options === null || options === void 0 ? void 0 : options.maxLookahead) !== null && _a !== void 0 ? _a : DEFAULT_PARSER_CONFIG.maxLookahead;\n  }\n  validate(options) {\n    const leftRecursionErrors = this.validateNoLeftRecursion(options.rules);\n    if (isEmpty(leftRecursionErrors)) {\n      const emptyAltErrors = this.validateEmptyOrAlternatives(options.rules);\n      const ambiguousAltsErrors = this.validateAmbiguousAlternationAlternatives(options.rules, this.maxLookahead);\n      const emptyRepetitionErrors = this.validateSomeNonEmptyLookaheadPath(options.rules, this.maxLookahead);\n      const allErrors = [...leftRecursionErrors, ...emptyAltErrors, ...ambiguousAltsErrors, ...emptyRepetitionErrors];\n      return allErrors;\n    }\n    return leftRecursionErrors;\n  }\n  validateNoLeftRecursion(rules) {\n    return flatMap(rules, currTopRule => validateNoLeftRecursion(currTopRule, currTopRule, defaultGrammarValidatorErrorProvider));\n  }\n  validateEmptyOrAlternatives(rules) {\n    return flatMap(rules, currTopRule => validateEmptyOrAlternative(currTopRule, defaultGrammarValidatorErrorProvider));\n  }\n  validateAmbiguousAlternationAlternatives(rules, maxLookahead) {\n    return flatMap(rules, currTopRule => validateAmbiguousAlternationAlternatives(currTopRule, maxLookahead, defaultGrammarValidatorErrorProvider));\n  }\n  validateSomeNonEmptyLookaheadPath(rules, maxLookahead) {\n    return validateSomeNonEmptyLookaheadPath(rules, maxLookahead, defaultGrammarValidatorErrorProvider);\n  }\n  buildLookaheadForAlternation(options) {\n    return buildLookaheadFuncForOr(options.prodOccurrence, options.rule, options.maxLookahead, options.hasPredicates, options.dynamicTokensEnabled, buildAlternativesLookAheadFunc);\n  }\n  buildLookaheadForOptional(options) {\n    return buildLookaheadFuncForOptionalProd(options.prodOccurrence, options.rule, options.maxLookahead, options.dynamicTokensEnabled, getProdType(options.prodType), buildSingleAlternativeLookaheadFunction);\n  }\n}", "map": {"version": 3, "names": ["flatMap", "isEmpty", "defaultGrammarValidatorErrorProvider", "DEFAULT_PARSER_CONFIG", "validateAmbiguousAlternationAlternatives", "validateEmptyOrAlternative", "validateNoLeftRecursion", "validateSomeNonEmptyLookaheadPath", "buildAlternativesLookAheadFunc", "buildLookaheadFuncForOptionalProd", "buildLookaheadFuncForOr", "buildSingleAlternativeLookaheadFunction", "getProdType", "LLkLookaheadStrategy", "constructor", "options", "max<PERSON><PERSON><PERSON><PERSON>", "_a", "validate", "leftRecursionErrors", "rules", "emptyAltErrors", "validateEmptyOrAlternatives", "ambiguousAltsErrors", "emptyRepetitionErrors", "allErrors", "currTopRule", "buildLookaheadForAlternation", "prodOccurrence", "rule", "hasPredicates", "dynamicTokensEnabled", "buildLookaheadForOptional", "prodType"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/llk_lookahead.ts"], "sourcesContent": ["import {\n  ILookaheadStrategy,\n  ILookaheadValidationError,\n  IOrAlt,\n  OptionalProductionType,\n  Rule,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { flatMap, isEmpty } from \"lodash-es\";\nimport { defaultGrammarValidatorErrorProvider } from \"../errors_public.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser/parser.js\";\nimport {\n  validateAmbiguousAlternationAlternatives,\n  validateEmptyOrAlternative,\n  validateNoLeftRecursion,\n  validateSomeNonEmptyLookaheadPath,\n} from \"./checks.js\";\nimport {\n  buildAlternativesLookAheadFunc,\n  buildLookaheadFuncForOptionalProd,\n  buildLookaheadFuncForOr,\n  buildSingleAlternativeLookaheadFunction,\n  getProdType,\n} from \"./lookahead.js\";\nimport { IParserDefinitionError } from \"./types.js\";\n\nexport class LLkLookaheadStrategy implements ILookaheadStrategy {\n  readonly maxLookahead: number;\n\n  constructor(options?: { maxLookahead?: number }) {\n    this.maxLookahead =\n      options?.maxLookahead ?? DEFAULT_PARSER_CONFIG.maxLookahead;\n  }\n\n  validate(options: {\n    rules: Rule[];\n    tokenTypes: TokenType[];\n    grammarName: string;\n  }): ILookaheadValidationError[] {\n    const leftRecursionErrors = this.validateNoLeftRecursion(options.rules);\n\n    if (isEmpty(leftRecursionErrors)) {\n      const emptyAltErrors = this.validateEmptyOrAlternatives(options.rules);\n      const ambiguousAltsErrors = this.validateAmbiguousAlternationAlternatives(\n        options.rules,\n        this.maxLookahead,\n      );\n      const emptyRepetitionErrors = this.validateSomeNonEmptyLookaheadPath(\n        options.rules,\n        this.maxLookahead,\n      );\n      const allErrors = [\n        ...leftRecursionErrors,\n        ...emptyAltErrors,\n        ...ambiguousAltsErrors,\n        ...emptyRepetitionErrors,\n      ];\n      return allErrors;\n    }\n    return leftRecursionErrors;\n  }\n\n  validateNoLeftRecursion(rules: Rule[]): IParserDefinitionError[] {\n    return flatMap(rules, (currTopRule) =>\n      validateNoLeftRecursion(\n        currTopRule,\n        currTopRule,\n        defaultGrammarValidatorErrorProvider,\n      ),\n    );\n  }\n\n  validateEmptyOrAlternatives(rules: Rule[]): IParserDefinitionError[] {\n    return flatMap(rules, (currTopRule) =>\n      validateEmptyOrAlternative(\n        currTopRule,\n        defaultGrammarValidatorErrorProvider,\n      ),\n    );\n  }\n\n  validateAmbiguousAlternationAlternatives(\n    rules: Rule[],\n    maxLookahead: number,\n  ): IParserDefinitionError[] {\n    return flatMap(rules, (currTopRule) =>\n      validateAmbiguousAlternationAlternatives(\n        currTopRule,\n        maxLookahead,\n        defaultGrammarValidatorErrorProvider,\n      ),\n    );\n  }\n\n  validateSomeNonEmptyLookaheadPath(\n    rules: Rule[],\n    maxLookahead: number,\n  ): IParserDefinitionError[] {\n    return validateSomeNonEmptyLookaheadPath(\n      rules,\n      maxLookahead,\n      defaultGrammarValidatorErrorProvider,\n    );\n  }\n\n  buildLookaheadForAlternation(options: {\n    prodOccurrence: number;\n    rule: Rule;\n    maxLookahead: number;\n    hasPredicates: boolean;\n    dynamicTokensEnabled: boolean;\n  }): (orAlts?: IOrAlt<any>[] | undefined) => number | undefined {\n    return buildLookaheadFuncForOr(\n      options.prodOccurrence,\n      options.rule,\n      options.maxLookahead,\n      options.hasPredicates,\n      options.dynamicTokensEnabled,\n      buildAlternativesLookAheadFunc,\n    );\n  }\n\n  buildLookaheadForOptional(options: {\n    prodOccurrence: number;\n    prodType: OptionalProductionType;\n    rule: Rule;\n    maxLookahead: number;\n    dynamicTokensEnabled: boolean;\n  }): () => boolean {\n    return buildLookaheadFuncForOptionalProd(\n      options.prodOccurrence,\n      options.rule,\n      options.maxLookahead,\n      options.dynamicTokensEnabled,\n      getProdType(options.prodType),\n      buildSingleAlternativeLookaheadFunction,\n    );\n  }\n}\n"], "mappings": "AAQA,SAASA,OAAO,EAAEC,OAAO,QAAQ,WAAW;AAC5C,SAASC,oCAAoC,QAAQ,qBAAqB;AAC1E,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SACEC,wCAAwC,EACxCC,0BAA0B,EAC1BC,uBAAuB,EACvBC,iCAAiC,QAC5B,aAAa;AACpB,SACEC,8BAA8B,EAC9BC,iCAAiC,EACjCC,uBAAuB,EACvBC,uCAAuC,EACvCC,WAAW,QACN,gBAAgB;AAGvB,OAAM,MAAOC,oBAAoB;EAG/BC,YAAYC,OAAmC;;IAC7C,IAAI,CAACC,YAAY,GACf,CAAAC,EAAA,GAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,YAAY,cAAAC,EAAA,cAAAA,EAAA,GAAId,qBAAqB,CAACa,YAAY;EAC/D;EAEAE,QAAQA,CAACH,OAIR;IACC,MAAMI,mBAAmB,GAAG,IAAI,CAACb,uBAAuB,CAACS,OAAO,CAACK,KAAK,CAAC;IAEvE,IAAInB,OAAO,CAACkB,mBAAmB,CAAC,EAAE;MAChC,MAAME,cAAc,GAAG,IAAI,CAACC,2BAA2B,CAACP,OAAO,CAACK,KAAK,CAAC;MACtE,MAAMG,mBAAmB,GAAG,IAAI,CAACnB,wCAAwC,CACvEW,OAAO,CAACK,KAAK,EACb,IAAI,CAACJ,YAAY,CAClB;MACD,MAAMQ,qBAAqB,GAAG,IAAI,CAACjB,iCAAiC,CAClEQ,OAAO,CAACK,KAAK,EACb,IAAI,CAACJ,YAAY,CAClB;MACD,MAAMS,SAAS,GAAG,CAChB,GAAGN,mBAAmB,EACtB,GAAGE,cAAc,EACjB,GAAGE,mBAAmB,EACtB,GAAGC,qBAAqB,CACzB;MACD,OAAOC,SAAS;;IAElB,OAAON,mBAAmB;EAC5B;EAEAb,uBAAuBA,CAACc,KAAa;IACnC,OAAOpB,OAAO,CAACoB,KAAK,EAAGM,WAAW,IAChCpB,uBAAuB,CACrBoB,WAAW,EACXA,WAAW,EACXxB,oCAAoC,CACrC,CACF;EACH;EAEAoB,2BAA2BA,CAACF,KAAa;IACvC,OAAOpB,OAAO,CAACoB,KAAK,EAAGM,WAAW,IAChCrB,0BAA0B,CACxBqB,WAAW,EACXxB,oCAAoC,CACrC,CACF;EACH;EAEAE,wCAAwCA,CACtCgB,KAAa,EACbJ,YAAoB;IAEpB,OAAOhB,OAAO,CAACoB,KAAK,EAAGM,WAAW,IAChCtB,wCAAwC,CACtCsB,WAAW,EACXV,YAAY,EACZd,oCAAoC,CACrC,CACF;EACH;EAEAK,iCAAiCA,CAC/Ba,KAAa,EACbJ,YAAoB;IAEpB,OAAOT,iCAAiC,CACtCa,KAAK,EACLJ,YAAY,EACZd,oCAAoC,CACrC;EACH;EAEAyB,4BAA4BA,CAACZ,OAM5B;IACC,OAAOL,uBAAuB,CAC5BK,OAAO,CAACa,cAAc,EACtBb,OAAO,CAACc,IAAI,EACZd,OAAO,CAACC,YAAY,EACpBD,OAAO,CAACe,aAAa,EACrBf,OAAO,CAACgB,oBAAoB,EAC5BvB,8BAA8B,CAC/B;EACH;EAEAwB,yBAAyBA,CAACjB,OAMzB;IACC,OAAON,iCAAiC,CACtCM,OAAO,CAACa,cAAc,EACtBb,OAAO,CAACc,IAAI,EACZd,OAAO,CAACC,YAAY,EACpBD,OAAO,CAACgB,oBAAoB,EAC5BnB,WAAW,CAACG,OAAO,CAACkB,QAAQ,CAAC,EAC7BtB,uCAAuC,CACxC;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}