{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nexport default (function sourceRandomCauchy(source) {\n  function randomCauchy(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function () {\n      return a + b * Math.tan(Math.PI * source());\n    };\n  }\n  randomCauchy.source = sourceRandomCauchy;\n  return randomCauchy;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "sourceRandomCauchy", "source", "randomCauchy", "a", "b", "Math", "tan", "PI"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-random/src/cauchy.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomCauchy(source) {\n  function randomCauchy(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      return a + b * Math.tan(Math.PI * source());\n    };\n  }\n\n  randomCauchy.source = sourceRandomCauchy;\n\n  return randomCauchy;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAE9C,eAAe,CAAC,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAClD,SAASC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC1BD,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,CAAC;IACtBC,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,CAAC;IACtB,OAAO,YAAW;MAChB,OAAOD,CAAC,GAAGC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC;IAC7C,CAAC;EACH;EAEAC,YAAY,CAACD,MAAM,GAAGD,kBAAkB;EAExC,OAAOE,YAAY;AACrB,CAAC,EAAEH,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}