{"ast": null, "code": "import { forEach, has } from \"lodash-es\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\nimport { AT_LEAST_ONE_IDX, AT_LEAST_ONE_SEP_IDX, getKeyForAutomaticLookahead, MANY_IDX, MANY_SEP_IDX, OPTION_IDX, OR_IDX } from \"../../grammar/keys.js\";\nimport { GAstVisitor, getProductionDslName } from \"@chevrotain/gast\";\nimport { LLkLookaheadStrategy } from \"../../grammar/llk_lookahead.js\";\n/**\n * Trait responsible for the lookahead related utilities and optimizations.\n */\nexport class LooksAhead {\n  initLooksAhead(config) {\n    this.dynamicTokensEnabled = has(config, \"dynamicTokensEnabled\") ? config.dynamicTokensEnabled // assumes end user provides the correct config value/type\n    : DEFAULT_PARSER_CONFIG.dynamicTokensEnabled;\n    this.maxLookahead = has(config, \"maxLookahead\") ? config.maxLookahead // assumes end user provides the correct config value/type\n    : DEFAULT_PARSER_CONFIG.maxLookahead;\n    this.lookaheadStrategy = has(config, \"lookaheadStrategy\") ? config.lookaheadStrategy // assumes end user provides the correct config value/type\n    : new LLkLookaheadStrategy({\n      maxLookahead: this.maxLookahead\n    });\n    this.lookAheadFuncsCache = new Map();\n  }\n  preComputeLookaheadFunctions(rules) {\n    forEach(rules, currRule => {\n      this.TRACE_INIT(`${currRule.name} Rule Lookahead`, () => {\n        const {\n          alternation,\n          repetition,\n          option,\n          repetitionMandatory,\n          repetitionMandatoryWithSeparator,\n          repetitionWithSeparator\n        } = collectMethods(currRule);\n        forEach(alternation, currProd => {\n          const prodIdx = currProd.idx === 0 ? \"\" : currProd.idx;\n          this.TRACE_INIT(`${getProductionDslName(currProd)}${prodIdx}`, () => {\n            const laFunc = this.lookaheadStrategy.buildLookaheadForAlternation({\n              prodOccurrence: currProd.idx,\n              rule: currRule,\n              maxLookahead: currProd.maxLookahead || this.maxLookahead,\n              hasPredicates: currProd.hasPredicates,\n              dynamicTokensEnabled: this.dynamicTokensEnabled\n            });\n            const key = getKeyForAutomaticLookahead(this.fullRuleNameToShort[currRule.name], OR_IDX, currProd.idx);\n            this.setLaFuncCache(key, laFunc);\n          });\n        });\n        forEach(repetition, currProd => {\n          this.computeLookaheadFunc(currRule, currProd.idx, MANY_IDX, \"Repetition\", currProd.maxLookahead, getProductionDslName(currProd));\n        });\n        forEach(option, currProd => {\n          this.computeLookaheadFunc(currRule, currProd.idx, OPTION_IDX, \"Option\", currProd.maxLookahead, getProductionDslName(currProd));\n        });\n        forEach(repetitionMandatory, currProd => {\n          this.computeLookaheadFunc(currRule, currProd.idx, AT_LEAST_ONE_IDX, \"RepetitionMandatory\", currProd.maxLookahead, getProductionDslName(currProd));\n        });\n        forEach(repetitionMandatoryWithSeparator, currProd => {\n          this.computeLookaheadFunc(currRule, currProd.idx, AT_LEAST_ONE_SEP_IDX, \"RepetitionMandatoryWithSeparator\", currProd.maxLookahead, getProductionDslName(currProd));\n        });\n        forEach(repetitionWithSeparator, currProd => {\n          this.computeLookaheadFunc(currRule, currProd.idx, MANY_SEP_IDX, \"RepetitionWithSeparator\", currProd.maxLookahead, getProductionDslName(currProd));\n        });\n      });\n    });\n  }\n  computeLookaheadFunc(rule, prodOccurrence, prodKey, prodType, prodMaxLookahead, dslMethodName) {\n    this.TRACE_INIT(`${dslMethodName}${prodOccurrence === 0 ? \"\" : prodOccurrence}`, () => {\n      const laFunc = this.lookaheadStrategy.buildLookaheadForOptional({\n        prodOccurrence,\n        rule,\n        maxLookahead: prodMaxLookahead || this.maxLookahead,\n        dynamicTokensEnabled: this.dynamicTokensEnabled,\n        prodType\n      });\n      const key = getKeyForAutomaticLookahead(this.fullRuleNameToShort[rule.name], prodKey, prodOccurrence);\n      this.setLaFuncCache(key, laFunc);\n    });\n  }\n  // this actually returns a number, but it is always used as a string (object prop key)\n  getKeyForAutomaticLookahead(dslMethodIdx, occurrence) {\n    const currRuleShortName = this.getLastExplicitRuleShortName();\n    return getKeyForAutomaticLookahead(currRuleShortName, dslMethodIdx, occurrence);\n  }\n  getLaFuncFromCache(key) {\n    return this.lookAheadFuncsCache.get(key);\n  }\n  /* istanbul ignore next */\n  setLaFuncCache(key, value) {\n    this.lookAheadFuncsCache.set(key, value);\n  }\n}\nclass DslMethodsCollectorVisitor extends GAstVisitor {\n  constructor() {\n    super(...arguments);\n    this.dslMethods = {\n      option: [],\n      alternation: [],\n      repetition: [],\n      repetitionWithSeparator: [],\n      repetitionMandatory: [],\n      repetitionMandatoryWithSeparator: []\n    };\n  }\n  reset() {\n    this.dslMethods = {\n      option: [],\n      alternation: [],\n      repetition: [],\n      repetitionWithSeparator: [],\n      repetitionMandatory: [],\n      repetitionMandatoryWithSeparator: []\n    };\n  }\n  visitOption(option) {\n    this.dslMethods.option.push(option);\n  }\n  visitRepetitionWithSeparator(manySep) {\n    this.dslMethods.repetitionWithSeparator.push(manySep);\n  }\n  visitRepetitionMandatory(atLeastOne) {\n    this.dslMethods.repetitionMandatory.push(atLeastOne);\n  }\n  visitRepetitionMandatoryWithSeparator(atLeastOneSep) {\n    this.dslMethods.repetitionMandatoryWithSeparator.push(atLeastOneSep);\n  }\n  visitRepetition(many) {\n    this.dslMethods.repetition.push(many);\n  }\n  visitAlternation(or) {\n    this.dslMethods.alternation.push(or);\n  }\n}\nconst collectorVisitor = new DslMethodsCollectorVisitor();\nexport function collectMethods(rule) {\n  collectorVisitor.reset();\n  rule.accept(collectorVisitor);\n  const dslMethods = collectorVisitor.dslMethods;\n  // avoid uncleaned references\n  collectorVisitor.reset();\n  return dslMethods;\n}", "map": {"version": 3, "names": ["for<PERSON>ach", "has", "DEFAULT_PARSER_CONFIG", "AT_LEAST_ONE_IDX", "AT_LEAST_ONE_SEP_IDX", "getKeyForAutomaticLookahead", "MANY_IDX", "MANY_SEP_IDX", "OPTION_IDX", "OR_IDX", "GAstVisitor", "getProductionDslName", "LLkLookaheadStrategy", "LooksAhead", "initLooksAhead", "config", "dynamicTokensEnabled", "max<PERSON><PERSON><PERSON><PERSON>", "lookaheadStrategy", "lookAheadFuncsCache", "Map", "preComputeLookaheadFunctions", "rules", "currRule", "TRACE_INIT", "name", "alternation", "repetition", "option", "repetitionMandatory", "repetitionMandatoryWithSeparator", "repetitionWithSeparator", "collectMethods", "currProd", "prodIdx", "idx", "laFunc", "buildLookaheadForAlternation", "prodOccurrence", "rule", "hasPredicates", "key", "fullRuleNameToShort", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "computeLookaheadFunc", "prod<PERSON><PERSON>", "prodType", "prodMaxLookahead", "dslMethodName", "buildLookaheadForOptional", "dslMethodIdx", "occurrence", "currRuleShortName", "getLastExplicitRuleShortName", "getLaFuncFromCache", "get", "value", "set", "DslMethodsCollectorVisitor", "constructor", "dslMethods", "reset", "visitOption", "push", "visitRepetitionWithSeparator", "manySep", "visitRepetitionMandatory", "atLeastOne", "visitRepetitionMandatoryWithSeparator", "atLeastOneSep", "visitRepetition", "many", "visitAlternation", "or", "collectorVisitor", "accept"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/looksahead.ts"], "sourcesContent": ["import { forEach, has } from \"lodash-es\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\nimport {\n  ILookaheadStrategy,\n  IParserConfig,\n  OptionalProductionType,\n} from \"@chevrotain/types\";\nimport {\n  AT_LEAST_ONE_IDX,\n  AT_LEAST_ONE_SEP_IDX,\n  getKeyForAutomaticLookahead,\n  MANY_IDX,\n  MANY_SEP_IDX,\n  OPTION_IDX,\n  OR_IDX,\n} from \"../../grammar/keys.js\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport {\n  Alternation,\n  GAstVisitor,\n  getProductionDslName,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n} from \"@chevrotain/gast\";\nimport { LLkLookaheadStrategy } from \"../../grammar/llk_lookahead.js\";\n\n/**\n * Trait responsible for the lookahead related utilities and optimizations.\n */\nexport class LooksAhead {\n  maxLookahead: number;\n  lookAheadFuncsCache: any;\n  dynamicTokensEnabled: boolean;\n  lookaheadStrategy: ILookaheadStrategy;\n\n  initLooksAhead(config: IParserConfig) {\n    this.dynamicTokensEnabled = has(config, \"dynamicTokensEnabled\")\n      ? (config.dynamicTokensEnabled as boolean) // assumes end user provides the correct config value/type\n      : DEFAULT_PARSER_CONFIG.dynamicTokensEnabled;\n\n    this.maxLookahead = has(config, \"maxLookahead\")\n      ? (config.maxLookahead as number) // assumes end user provides the correct config value/type\n      : DEFAULT_PARSER_CONFIG.maxLookahead;\n\n    this.lookaheadStrategy = has(config, \"lookaheadStrategy\")\n      ? (config.lookaheadStrategy as ILookaheadStrategy) // assumes end user provides the correct config value/type\n      : new LLkLookaheadStrategy({ maxLookahead: this.maxLookahead });\n\n    this.lookAheadFuncsCache = new Map();\n  }\n\n  preComputeLookaheadFunctions(this: MixedInParser, rules: Rule[]): void {\n    forEach(rules, (currRule) => {\n      this.TRACE_INIT(`${currRule.name} Rule Lookahead`, () => {\n        const {\n          alternation,\n          repetition,\n          option,\n          repetitionMandatory,\n          repetitionMandatoryWithSeparator,\n          repetitionWithSeparator,\n        } = collectMethods(currRule);\n\n        forEach(alternation, (currProd) => {\n          const prodIdx = currProd.idx === 0 ? \"\" : currProd.idx;\n          this.TRACE_INIT(`${getProductionDslName(currProd)}${prodIdx}`, () => {\n            const laFunc = this.lookaheadStrategy.buildLookaheadForAlternation({\n              prodOccurrence: currProd.idx,\n              rule: currRule,\n              maxLookahead: currProd.maxLookahead || this.maxLookahead,\n              hasPredicates: currProd.hasPredicates,\n              dynamicTokensEnabled: this.dynamicTokensEnabled,\n            });\n\n            const key = getKeyForAutomaticLookahead(\n              this.fullRuleNameToShort[currRule.name],\n              OR_IDX,\n              currProd.idx,\n            );\n            this.setLaFuncCache(key, laFunc);\n          });\n        });\n\n        forEach(repetition, (currProd) => {\n          this.computeLookaheadFunc(\n            currRule,\n            currProd.idx,\n            MANY_IDX,\n            \"Repetition\",\n            currProd.maxLookahead,\n            getProductionDslName(currProd),\n          );\n        });\n\n        forEach(option, (currProd) => {\n          this.computeLookaheadFunc(\n            currRule,\n            currProd.idx,\n            OPTION_IDX,\n            \"Option\",\n            currProd.maxLookahead,\n            getProductionDslName(currProd),\n          );\n        });\n\n        forEach(repetitionMandatory, (currProd) => {\n          this.computeLookaheadFunc(\n            currRule,\n            currProd.idx,\n            AT_LEAST_ONE_IDX,\n            \"RepetitionMandatory\",\n            currProd.maxLookahead,\n            getProductionDslName(currProd),\n          );\n        });\n\n        forEach(repetitionMandatoryWithSeparator, (currProd) => {\n          this.computeLookaheadFunc(\n            currRule,\n            currProd.idx,\n            AT_LEAST_ONE_SEP_IDX,\n            \"RepetitionMandatoryWithSeparator\",\n            currProd.maxLookahead,\n            getProductionDslName(currProd),\n          );\n        });\n\n        forEach(repetitionWithSeparator, (currProd) => {\n          this.computeLookaheadFunc(\n            currRule,\n            currProd.idx,\n            MANY_SEP_IDX,\n            \"RepetitionWithSeparator\",\n            currProd.maxLookahead,\n            getProductionDslName(currProd),\n          );\n        });\n      });\n    });\n  }\n\n  computeLookaheadFunc(\n    this: MixedInParser,\n    rule: Rule,\n    prodOccurrence: number,\n    prodKey: number,\n    prodType: OptionalProductionType,\n    prodMaxLookahead: number | undefined,\n    dslMethodName: string,\n  ): void {\n    this.TRACE_INIT(\n      `${dslMethodName}${prodOccurrence === 0 ? \"\" : prodOccurrence}`,\n      () => {\n        const laFunc = this.lookaheadStrategy.buildLookaheadForOptional({\n          prodOccurrence,\n          rule,\n          maxLookahead: prodMaxLookahead || this.maxLookahead,\n          dynamicTokensEnabled: this.dynamicTokensEnabled,\n          prodType,\n        });\n        const key = getKeyForAutomaticLookahead(\n          this.fullRuleNameToShort[rule.name],\n          prodKey,\n          prodOccurrence,\n        );\n        this.setLaFuncCache(key, laFunc);\n      },\n    );\n  }\n\n  // this actually returns a number, but it is always used as a string (object prop key)\n  getKeyForAutomaticLookahead(\n    this: MixedInParser,\n    dslMethodIdx: number,\n    occurrence: number,\n  ): number {\n    const currRuleShortName: any = this.getLastExplicitRuleShortName();\n    return getKeyForAutomaticLookahead(\n      currRuleShortName,\n      dslMethodIdx,\n      occurrence,\n    );\n  }\n\n  getLaFuncFromCache(this: MixedInParser, key: number): Function {\n    return this.lookAheadFuncsCache.get(key);\n  }\n\n  /* istanbul ignore next */\n  setLaFuncCache(this: MixedInParser, key: number, value: Function): void {\n    this.lookAheadFuncsCache.set(key, value);\n  }\n}\n\nclass DslMethodsCollectorVisitor extends GAstVisitor {\n  public dslMethods: {\n    option: Option[];\n    alternation: Alternation[];\n    repetition: Repetition[];\n    repetitionWithSeparator: RepetitionWithSeparator[];\n    repetitionMandatory: RepetitionMandatory[];\n    repetitionMandatoryWithSeparator: RepetitionMandatoryWithSeparator[];\n  } = {\n    option: [],\n    alternation: [],\n    repetition: [],\n    repetitionWithSeparator: [],\n    repetitionMandatory: [],\n    repetitionMandatoryWithSeparator: [],\n  };\n\n  reset() {\n    this.dslMethods = {\n      option: [],\n      alternation: [],\n      repetition: [],\n      repetitionWithSeparator: [],\n      repetitionMandatory: [],\n      repetitionMandatoryWithSeparator: [],\n    };\n  }\n\n  public visitOption(option: Option): void {\n    this.dslMethods.option.push(option);\n  }\n\n  public visitRepetitionWithSeparator(manySep: RepetitionWithSeparator): void {\n    this.dslMethods.repetitionWithSeparator.push(manySep);\n  }\n\n  public visitRepetitionMandatory(atLeastOne: RepetitionMandatory): void {\n    this.dslMethods.repetitionMandatory.push(atLeastOne);\n  }\n\n  public visitRepetitionMandatoryWithSeparator(\n    atLeastOneSep: RepetitionMandatoryWithSeparator,\n  ): void {\n    this.dslMethods.repetitionMandatoryWithSeparator.push(atLeastOneSep);\n  }\n\n  public visitRepetition(many: Repetition): void {\n    this.dslMethods.repetition.push(many);\n  }\n\n  public visitAlternation(or: Alternation): void {\n    this.dslMethods.alternation.push(or);\n  }\n}\n\nconst collectorVisitor = new DslMethodsCollectorVisitor();\nexport function collectMethods(rule: Rule): {\n  option: Option[];\n  alternation: Alternation[];\n  repetition: Repetition[];\n  repetitionWithSeparator: RepetitionWithSeparator[];\n  repetitionMandatory: RepetitionMandatory[];\n  repetitionMandatoryWithSeparator: RepetitionMandatoryWithSeparator[];\n} {\n  collectorVisitor.reset();\n  rule.accept(collectorVisitor);\n  const dslMethods = collectorVisitor.dslMethods;\n  // avoid uncleaned references\n  collectorVisitor.reset();\n  return <any>dslMethods;\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,GAAG,QAAQ,WAAW;AACxC,SAASC,qBAAqB,QAAQ,cAAc;AAMpD,SACEC,gBAAgB,EAChBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,MAAM,QACD,uBAAuB;AAE9B,SAEEC,WAAW,EACXC,oBAAoB,QAOf,kBAAkB;AACzB,SAASC,oBAAoB,QAAQ,gCAAgC;AAErE;;;AAGA,OAAM,MAAOC,UAAU;EAMrBC,cAAcA,CAACC,MAAqB;IAClC,IAAI,CAACC,oBAAoB,GAAGf,GAAG,CAACc,MAAM,EAAE,sBAAsB,CAAC,GAC1DA,MAAM,CAACC,oBAAgC,CAAC;IAAA,EACzCd,qBAAqB,CAACc,oBAAoB;IAE9C,IAAI,CAACC,YAAY,GAAGhB,GAAG,CAACc,MAAM,EAAE,cAAc,CAAC,GAC1CA,MAAM,CAACE,YAAuB,CAAC;IAAA,EAChCf,qBAAqB,CAACe,YAAY;IAEtC,IAAI,CAACC,iBAAiB,GAAGjB,GAAG,CAACc,MAAM,EAAE,mBAAmB,CAAC,GACpDA,MAAM,CAACG,iBAAwC,CAAC;IAAA,EACjD,IAAIN,oBAAoB,CAAC;MAAEK,YAAY,EAAE,IAAI,CAACA;IAAY,CAAE,CAAC;IAEjE,IAAI,CAACE,mBAAmB,GAAG,IAAIC,GAAG,EAAE;EACtC;EAEAC,4BAA4BA,CAAsBC,KAAa;IAC7DtB,OAAO,CAACsB,KAAK,EAAGC,QAAQ,IAAI;MAC1B,IAAI,CAACC,UAAU,CAAC,GAAGD,QAAQ,CAACE,IAAI,iBAAiB,EAAE,MAAK;QACtD,MAAM;UACJC,WAAW;UACXC,UAAU;UACVC,MAAM;UACNC,mBAAmB;UACnBC,gCAAgC;UAChCC;QAAuB,CACxB,GAAGC,cAAc,CAACT,QAAQ,CAAC;QAE5BvB,OAAO,CAAC0B,WAAW,EAAGO,QAAQ,IAAI;UAChC,MAAMC,OAAO,GAAGD,QAAQ,CAACE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAGF,QAAQ,CAACE,GAAG;UACtD,IAAI,CAACX,UAAU,CAAC,GAAGb,oBAAoB,CAACsB,QAAQ,CAAC,GAAGC,OAAO,EAAE,EAAE,MAAK;YAClE,MAAME,MAAM,GAAG,IAAI,CAAClB,iBAAiB,CAACmB,4BAA4B,CAAC;cACjEC,cAAc,EAAEL,QAAQ,CAACE,GAAG;cAC5BI,IAAI,EAAEhB,QAAQ;cACdN,YAAY,EAAEgB,QAAQ,CAAChB,YAAY,IAAI,IAAI,CAACA,YAAY;cACxDuB,aAAa,EAAEP,QAAQ,CAACO,aAAa;cACrCxB,oBAAoB,EAAE,IAAI,CAACA;aAC5B,CAAC;YAEF,MAAMyB,GAAG,GAAGpC,2BAA2B,CACrC,IAAI,CAACqC,mBAAmB,CAACnB,QAAQ,CAACE,IAAI,CAAC,EACvChB,MAAM,EACNwB,QAAQ,CAACE,GAAG,CACb;YACD,IAAI,CAACQ,cAAc,CAACF,GAAG,EAAEL,MAAM,CAAC;UAClC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFpC,OAAO,CAAC2B,UAAU,EAAGM,QAAQ,IAAI;UAC/B,IAAI,CAACW,oBAAoB,CACvBrB,QAAQ,EACRU,QAAQ,CAACE,GAAG,EACZ7B,QAAQ,EACR,YAAY,EACZ2B,QAAQ,CAAChB,YAAY,EACrBN,oBAAoB,CAACsB,QAAQ,CAAC,CAC/B;QACH,CAAC,CAAC;QAEFjC,OAAO,CAAC4B,MAAM,EAAGK,QAAQ,IAAI;UAC3B,IAAI,CAACW,oBAAoB,CACvBrB,QAAQ,EACRU,QAAQ,CAACE,GAAG,EACZ3B,UAAU,EACV,QAAQ,EACRyB,QAAQ,CAAChB,YAAY,EACrBN,oBAAoB,CAACsB,QAAQ,CAAC,CAC/B;QACH,CAAC,CAAC;QAEFjC,OAAO,CAAC6B,mBAAmB,EAAGI,QAAQ,IAAI;UACxC,IAAI,CAACW,oBAAoB,CACvBrB,QAAQ,EACRU,QAAQ,CAACE,GAAG,EACZhC,gBAAgB,EAChB,qBAAqB,EACrB8B,QAAQ,CAAChB,YAAY,EACrBN,oBAAoB,CAACsB,QAAQ,CAAC,CAC/B;QACH,CAAC,CAAC;QAEFjC,OAAO,CAAC8B,gCAAgC,EAAGG,QAAQ,IAAI;UACrD,IAAI,CAACW,oBAAoB,CACvBrB,QAAQ,EACRU,QAAQ,CAACE,GAAG,EACZ/B,oBAAoB,EACpB,kCAAkC,EAClC6B,QAAQ,CAAChB,YAAY,EACrBN,oBAAoB,CAACsB,QAAQ,CAAC,CAC/B;QACH,CAAC,CAAC;QAEFjC,OAAO,CAAC+B,uBAAuB,EAAGE,QAAQ,IAAI;UAC5C,IAAI,CAACW,oBAAoB,CACvBrB,QAAQ,EACRU,QAAQ,CAACE,GAAG,EACZ5B,YAAY,EACZ,yBAAyB,EACzB0B,QAAQ,CAAChB,YAAY,EACrBN,oBAAoB,CAACsB,QAAQ,CAAC,CAC/B;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAW,oBAAoBA,CAElBL,IAAU,EACVD,cAAsB,EACtBO,OAAe,EACfC,QAAgC,EAChCC,gBAAoC,EACpCC,aAAqB;IAErB,IAAI,CAACxB,UAAU,CACb,GAAGwB,aAAa,GAAGV,cAAc,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc,EAAE,EAC/D,MAAK;MACH,MAAMF,MAAM,GAAG,IAAI,CAAClB,iBAAiB,CAAC+B,yBAAyB,CAAC;QAC9DX,cAAc;QACdC,IAAI;QACJtB,YAAY,EAAE8B,gBAAgB,IAAI,IAAI,CAAC9B,YAAY;QACnDD,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAC/C8B;OACD,CAAC;MACF,MAAML,GAAG,GAAGpC,2BAA2B,CACrC,IAAI,CAACqC,mBAAmB,CAACH,IAAI,CAACd,IAAI,CAAC,EACnCoB,OAAO,EACPP,cAAc,CACf;MACD,IAAI,CAACK,cAAc,CAACF,GAAG,EAAEL,MAAM,CAAC;IAClC,CAAC,CACF;EACH;EAEA;EACA/B,2BAA2BA,CAEzB6C,YAAoB,EACpBC,UAAkB;IAElB,MAAMC,iBAAiB,GAAQ,IAAI,CAACC,4BAA4B,EAAE;IAClE,OAAOhD,2BAA2B,CAChC+C,iBAAiB,EACjBF,YAAY,EACZC,UAAU,CACX;EACH;EAEAG,kBAAkBA,CAAsBb,GAAW;IACjD,OAAO,IAAI,CAACtB,mBAAmB,CAACoC,GAAG,CAACd,GAAG,CAAC;EAC1C;EAEA;EACAE,cAAcA,CAAsBF,GAAW,EAAEe,KAAe;IAC9D,IAAI,CAACrC,mBAAmB,CAACsC,GAAG,CAAChB,GAAG,EAAEe,KAAK,CAAC;EAC1C;;AAGF,MAAME,0BAA2B,SAAQhD,WAAW;EAApDiD,YAAA;;IACS,KAAAC,UAAU,GAOb;MACFhC,MAAM,EAAE,EAAE;MACVF,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdI,uBAAuB,EAAE,EAAE;MAC3BF,mBAAmB,EAAE,EAAE;MACvBC,gCAAgC,EAAE;KACnC;EAsCH;EApCE+B,KAAKA,CAAA;IACH,IAAI,CAACD,UAAU,GAAG;MAChBhC,MAAM,EAAE,EAAE;MACVF,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdI,uBAAuB,EAAE,EAAE;MAC3BF,mBAAmB,EAAE,EAAE;MACvBC,gCAAgC,EAAE;KACnC;EACH;EAEOgC,WAAWA,CAAClC,MAAc;IAC/B,IAAI,CAACgC,UAAU,CAAChC,MAAM,CAACmC,IAAI,CAACnC,MAAM,CAAC;EACrC;EAEOoC,4BAA4BA,CAACC,OAAgC;IAClE,IAAI,CAACL,UAAU,CAAC7B,uBAAuB,CAACgC,IAAI,CAACE,OAAO,CAAC;EACvD;EAEOC,wBAAwBA,CAACC,UAA+B;IAC7D,IAAI,CAACP,UAAU,CAAC/B,mBAAmB,CAACkC,IAAI,CAACI,UAAU,CAAC;EACtD;EAEOC,qCAAqCA,CAC1CC,aAA+C;IAE/C,IAAI,CAACT,UAAU,CAAC9B,gCAAgC,CAACiC,IAAI,CAACM,aAAa,CAAC;EACtE;EAEOC,eAAeA,CAACC,IAAgB;IACrC,IAAI,CAACX,UAAU,CAACjC,UAAU,CAACoC,IAAI,CAACQ,IAAI,CAAC;EACvC;EAEOC,gBAAgBA,CAACC,EAAe;IACrC,IAAI,CAACb,UAAU,CAAClC,WAAW,CAACqC,IAAI,CAACU,EAAE,CAAC;EACtC;;AAGF,MAAMC,gBAAgB,GAAG,IAAIhB,0BAA0B,EAAE;AACzD,OAAM,SAAU1B,cAAcA,CAACO,IAAU;EAQvCmC,gBAAgB,CAACb,KAAK,EAAE;EACxBtB,IAAI,CAACoC,MAAM,CAACD,gBAAgB,CAAC;EAC7B,MAAMd,UAAU,GAAGc,gBAAgB,CAACd,UAAU;EAC9C;EACAc,gBAAgB,CAACb,KAAK,EAAE;EACxB,OAAYD,UAAU;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}