{"ast": null, "code": "export { PRINT_WARNING, PRINT_ERROR } from \"./print.js\";\nexport { timer } from \"./timer.js\";\nexport { toFastProperties } from \"./to-fast-properties.js\";", "map": {"version": 3, "names": ["PRINT_WARNING", "PRINT_ERROR", "timer", "toFastProperties"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/utils/src/api.ts"], "sourcesContent": ["export { PRINT_WARNING, PRINT_ERROR } from \"./print.js\";\nexport { timer } from \"./timer.js\";\nexport { toFastProperties } from \"./to-fast-properties.js\";\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,WAAW,QAAQ,YAAY;AACvD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,gBAAgB,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}