{"ast": null, "code": "import projection from \"./index.js\";\nimport { abs, asin, cos, epsilon2, sin, sqrt } from \"../math.js\";\nvar A1 = 1.340264,\n  A2 = -0.081106,\n  A3 = 0.000893,\n  A4 = 0.003796,\n  M = sqrt(3) / 2,\n  iterations = 12;\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)),\n    l2 = l * l,\n    l6 = l2 * l2 * l2;\n  return [lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))), l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))];\n}\nequalEarthRaw.invert = function (x, y) {\n  var l = y,\n    l2 = l * l,\n    l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l), asin(sin(l) / M)];\n};\nexport default function () {\n  return projection(equalEarthRaw).scale(177.158);\n}", "map": {"version": 3, "names": ["projection", "abs", "asin", "cos", "epsilon2", "sin", "sqrt", "A1", "A2", "A3", "A4", "M", "iterations", "equalEarthRaw", "lambda", "phi", "l", "l2", "l6", "invert", "x", "y", "i", "delta", "fy", "fpy", "scale"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/equalEarth.js"], "sourcesContent": ["import projection from \"./index.js\";\nimport {abs, asin, cos, epsilon2, sin, sqrt} from \"../math.js\";\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = sqrt(3) / 2,\n    iterations = 12;\n\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l),\n    asin(sin(l) / M)\n  ];\n};\n\nexport default function() {\n  return projection(equalEarthRaw)\n      .scale(177.158);\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,SAAQC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAO,YAAY;AAE9D,IAAIC,EAAE,GAAG,QAAQ;EACbC,EAAE,GAAG,CAAC,QAAQ;EACdC,EAAE,GAAG,QAAQ;EACbC,EAAE,GAAG,QAAQ;EACbC,CAAC,GAAGL,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACfM,UAAU,GAAG,EAAE;AAEnB,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACzC,IAAIC,CAAC,GAAGd,IAAI,CAACS,CAAC,GAAGN,GAAG,CAACU,GAAG,CAAC,CAAC;IAAEE,EAAE,GAAGD,CAAC,GAAGA,CAAC;IAAEE,EAAE,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE;EACzD,OAAO,CACLH,MAAM,GAAGX,GAAG,CAACa,CAAC,CAAC,IAAIL,CAAC,IAAIJ,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGS,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGT,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGO,EAAE,CAAC,CAAC,CAAC,EACxED,CAAC,IAAIT,EAAE,GAAGC,EAAE,GAAGS,EAAE,GAAGC,EAAE,IAAIT,EAAE,GAAGC,EAAE,GAAGO,EAAE,CAAC,CAAC,CACzC;AACH;AAEAJ,aAAa,CAACM,MAAM,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAIL,CAAC,GAAGK,CAAC;IAAEJ,EAAE,GAAGD,CAAC,GAAGA,CAAC;IAAEE,EAAE,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE;EACxC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAEH,CAAC,GAAGV,UAAU,EAAE,EAAEU,CAAC,EAAE;IACnDE,EAAE,GAAGR,CAAC,IAAIT,EAAE,GAAGC,EAAE,GAAGS,EAAE,GAAGC,EAAE,IAAIT,EAAE,GAAGC,EAAE,GAAGO,EAAE,CAAC,CAAC,GAAGI,CAAC;IACjDI,GAAG,GAAGlB,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGS,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGT,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGO,EAAE,CAAC;IACpDD,CAAC,IAAIO,KAAK,GAAGC,EAAE,GAAGC,GAAG,EAAER,EAAE,GAAGD,CAAC,GAAGA,CAAC,EAAEE,EAAE,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE;IACpD,IAAIhB,GAAG,CAACsB,KAAK,CAAC,GAAGnB,QAAQ,EAAE;EAC7B;EACA,OAAO,CACLO,CAAC,GAAGS,CAAC,IAAIb,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGS,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGT,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGO,EAAE,CAAC,CAAC,GAAGd,GAAG,CAACa,CAAC,CAAC,EACjEd,IAAI,CAACG,GAAG,CAACW,CAAC,CAAC,GAAGL,CAAC,CAAC,CACjB;AACH,CAAC;AAED,eAAe,YAAW;EACxB,OAAOX,UAAU,CAACa,aAAa,CAAC,CAC3Ba,KAAK,CAAC,OAAO,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}