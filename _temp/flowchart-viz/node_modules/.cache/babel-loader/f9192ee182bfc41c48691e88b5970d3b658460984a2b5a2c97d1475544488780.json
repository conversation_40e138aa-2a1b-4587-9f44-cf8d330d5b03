{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport * from './linker.js';\nexport * from './name-provider.js';\nexport * from './references.js';\nexport * from './scope.js';\nexport * from './scope-computation.js';\nexport * from './scope-provider.js';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/references/index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport * from './linker.js';\r\nexport * from './name-provider.js';\r\nexport * from './references.js';\r\nexport * from './scope.js';\r\nexport * from './scope-computation.js';\r\nexport * from './scope-provider.js';\r\n"], "mappings": "AAAA;;;;;AAMA,cAAc,aAAa;AAC3B,cAAc,oBAAoB;AAClC,cAAc,iBAAiB;AAC/B,cAAc,YAAY;AAC1B,cAAc,wBAAwB;AACtC,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}