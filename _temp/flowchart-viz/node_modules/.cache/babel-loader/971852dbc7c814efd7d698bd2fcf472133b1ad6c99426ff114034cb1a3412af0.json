{"ast": null, "code": "export default function union(...others) {\n  const set = new Set();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}", "map": {"version": 3, "names": ["union", "others", "set", "Set", "other", "o", "add"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/union.js"], "sourcesContent": ["export default function union(...others) {\n  const set = new Set();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAAC,GAAGC,MAAM,EAAE;EACvC,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMI,CAAC,IAAID,KAAK,EAAE;MACrBF,GAAG,CAACI,GAAG,CAACD,CAAC,CAAC;IACZ;EACF;EACA,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}