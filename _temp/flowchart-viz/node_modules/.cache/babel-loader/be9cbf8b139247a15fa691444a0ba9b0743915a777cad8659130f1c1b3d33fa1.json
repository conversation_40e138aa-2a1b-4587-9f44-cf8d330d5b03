{"ast": null, "code": "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport { required } from \"../accessors.js\";\nimport constant, { constantZero } from \"../constant.js\";\nexport default function () {\n  var tile = squarify,\n    round = false,\n    dx = 1,\n    dy = 1,\n    paddingStack = [0],\n    paddingInner = constantZero,\n    paddingTop = constantZero,\n    paddingRight = constantZero,\n    paddingBottom = constantZero,\n    paddingLeft = constantZero;\n  function treemap(root) {\n    root.x0 = root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n      x0 = node.x0 + p,\n      y0 = node.y0 + p,\n      x1 = node.x1 - p,\n      y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n  treemap.round = function (x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n  treemap.size = function (x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n  treemap.tile = function (x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n  treemap.padding = function (x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n  treemap.paddingInner = function (x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n  treemap.paddingOuter = function (x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n  treemap.paddingTop = function (x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n  treemap.paddingRight = function (x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n  treemap.paddingBottom = function (x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n  treemap.paddingLeft = function (x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n  return treemap;\n}", "map": {"version": 3, "names": ["roundNode", "squarify", "required", "constant", "constantZero", "tile", "round", "dx", "dy", "paddingStack", "paddingInner", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "treemap", "root", "x0", "y0", "x1", "y1", "eachBefore", "positionNode", "node", "p", "depth", "children", "x", "arguments", "length", "size", "padding", "paddingOuter"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-hierarchy/src/treemap/index.js"], "sourcesContent": ["import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAAQC,QAAQ,QAAO,iBAAiB;AACxC,OAAOC,QAAQ,IAAGC,YAAY,QAAO,gBAAgB;AAErD,eAAe,YAAW;EACxB,IAAIC,IAAI,GAAGJ,QAAQ;IACfK,KAAK,GAAG,KAAK;IACbC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClBC,YAAY,GAAGN,YAAY;IAC3BO,UAAU,GAAGP,YAAY;IACzBQ,YAAY,GAAGR,YAAY;IAC3BS,aAAa,GAAGT,YAAY;IAC5BU,WAAW,GAAGV,YAAY;EAE9B,SAASW,OAAOA,CAACC,IAAI,EAAE;IACrBA,IAAI,CAACC,EAAE,GACPD,IAAI,CAACE,EAAE,GAAG,CAAC;IACXF,IAAI,CAACG,EAAE,GAAGZ,EAAE;IACZS,IAAI,CAACI,EAAE,GAAGZ,EAAE;IACZQ,IAAI,CAACK,UAAU,CAACC,YAAY,CAAC;IAC7Bb,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,IAAIH,KAAK,EAAEU,IAAI,CAACK,UAAU,CAACrB,SAAS,CAAC;IACrC,OAAOgB,IAAI;EACb;EAEA,SAASM,YAAYA,CAACC,IAAI,EAAE;IAC1B,IAAIC,CAAC,GAAGf,YAAY,CAACc,IAAI,CAACE,KAAK,CAAC;MAC5BR,EAAE,GAAGM,IAAI,CAACN,EAAE,GAAGO,CAAC;MAChBN,EAAE,GAAGK,IAAI,CAACL,EAAE,GAAGM,CAAC;MAChBL,EAAE,GAAGI,IAAI,CAACJ,EAAE,GAAGK,CAAC;MAChBJ,EAAE,GAAGG,IAAI,CAACH,EAAE,GAAGI,CAAC;IACpB,IAAIL,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;IACpC,IAAIC,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;IACpCG,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZM,IAAI,CAACL,EAAE,GAAGA,EAAE;IACZK,IAAI,CAACJ,EAAE,GAAGA,EAAE;IACZI,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAIG,IAAI,CAACG,QAAQ,EAAE;MACjBF,CAAC,GAAGf,YAAY,CAACc,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGf,YAAY,CAACa,IAAI,CAAC,GAAG,CAAC;MACzDN,EAAE,IAAIH,WAAW,CAACS,IAAI,CAAC,GAAGC,CAAC;MAC3BN,EAAE,IAAIP,UAAU,CAACY,IAAI,CAAC,GAAGC,CAAC;MAC1BL,EAAE,IAAIP,YAAY,CAACW,IAAI,CAAC,GAAGC,CAAC;MAC5BJ,EAAE,IAAIP,aAAa,CAACU,IAAI,CAAC,GAAGC,CAAC;MAC7B,IAAIL,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;MACpC,IAAIC,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;MACpCf,IAAI,CAACkB,IAAI,EAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC5B;EACF;EAEAL,OAAO,CAACT,KAAK,GAAG,UAASqB,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIvB,KAAK,GAAG,CAAC,CAACqB,CAAC,EAAEZ,OAAO,IAAIT,KAAK;EAC1D,CAAC;EAEDS,OAAO,CAACe,IAAI,GAAG,UAASH,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAItB,EAAE,GAAG,CAACoB,CAAC,CAAC,CAAC,CAAC,EAAEnB,EAAE,GAAG,CAACmB,CAAC,CAAC,CAAC,CAAC,EAAEZ,OAAO,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACxE,CAAC;EAEDO,OAAO,CAACV,IAAI,GAAG,UAASsB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIxB,IAAI,GAAGH,QAAQ,CAACyB,CAAC,CAAC,EAAEZ,OAAO,IAAIV,IAAI;EAChE,CAAC;EAEDU,OAAO,CAACgB,OAAO,GAAG,UAASJ,CAAC,EAAE;IAC5B,OAAOC,SAAS,CAACC,MAAM,GAAGd,OAAO,CAACL,YAAY,CAACiB,CAAC,CAAC,CAACK,YAAY,CAACL,CAAC,CAAC,GAAGZ,OAAO,CAACL,YAAY,CAAC,CAAC;EAC5F,CAAC;EAEDK,OAAO,CAACL,YAAY,GAAG,UAASiB,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAInB,YAAY,GAAG,OAAOiB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxB,QAAQ,CAAC,CAACwB,CAAC,CAAC,EAAEZ,OAAO,IAAIL,YAAY;EAC/G,CAAC;EAEDK,OAAO,CAACiB,YAAY,GAAG,UAASL,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,GAAGd,OAAO,CAACJ,UAAU,CAACgB,CAAC,CAAC,CAACf,YAAY,CAACe,CAAC,CAAC,CAACd,aAAa,CAACc,CAAC,CAAC,CAACb,WAAW,CAACa,CAAC,CAAC,GAAGZ,OAAO,CAACJ,UAAU,CAAC,CAAC;EACxH,CAAC;EAEDI,OAAO,CAACJ,UAAU,GAAG,UAASgB,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACC,MAAM,IAAIlB,UAAU,GAAG,OAAOgB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxB,QAAQ,CAAC,CAACwB,CAAC,CAAC,EAAEZ,OAAO,IAAIJ,UAAU;EAC3G,CAAC;EAEDI,OAAO,CAACH,YAAY,GAAG,UAASe,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAIjB,YAAY,GAAG,OAAOe,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxB,QAAQ,CAAC,CAACwB,CAAC,CAAC,EAAEZ,OAAO,IAAIH,YAAY;EAC/G,CAAC;EAEDG,OAAO,CAACF,aAAa,GAAG,UAASc,CAAC,EAAE;IAClC,OAAOC,SAAS,CAACC,MAAM,IAAIhB,aAAa,GAAG,OAAOc,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxB,QAAQ,CAAC,CAACwB,CAAC,CAAC,EAAEZ,OAAO,IAAIF,aAAa;EACjH,CAAC;EAEDE,OAAO,CAACD,WAAW,GAAG,UAASa,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIf,WAAW,GAAG,OAAOa,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxB,QAAQ,CAAC,CAACwB,CAAC,CAAC,EAAEZ,OAAO,IAAID,WAAW;EAC7G,CAAC;EAED,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}