{"ast": null, "code": "export default function set(values) {\n  return values instanceof Set ? values : new Set(values);\n}", "map": {"version": 3, "names": ["set", "values", "Set"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/set.js"], "sourcesContent": ["export default function set(values) {\n  return values instanceof Set ? values : new Set(values);\n}\n"], "mappings": "AAAA,eAAe,SAASA,GAAGA,CAACC,MAAM,EAAE;EAClC,OAAOA,MAAM,YAAYC,GAAG,GAAGD,MAAM,GAAG,IAAIC,GAAG,CAACD,MAAM,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}