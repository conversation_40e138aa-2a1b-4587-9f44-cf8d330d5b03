{"ast": null, "code": "import { VERSION } from \"../version.js\";\nexport function createSyntaxDiagramsCode(grammar, {\n  resourceBase = `https://unpkg.com/chevrotain@${VERSION}/diagrams/`,\n  css = `https://unpkg.com/chevrotain@${VERSION}/diagrams/diagrams.css`\n} = {}) {\n  const header = `\n<!-- This is a generated file -->\n<!DOCTYPE html>\n<meta charset=\"utf-8\">\n<style>\n  body {\n    background-color: hsl(30, 20%, 95%)\n  }\n</style>\n\n`;\n  const cssHtml = `\n<link rel='stylesheet' href='${css}'>\n`;\n  const scripts = `\n<script src='${resourceBase}vendor/railroad-diagrams.js'></script>\n<script src='${resourceBase}src/diagrams_builder.js'></script>\n<script src='${resourceBase}src/diagrams_behavior.js'></script>\n<script src='${resourceBase}src/main.js'></script>\n`;\n  const diagramsDiv = `\n<div id=\"diagrams\" align=\"center\"></div>    \n`;\n  const serializedGrammar = `\n<script>\n    window.serializedGrammar = ${JSON.stringify(grammar, null, \"  \")};\n</script>\n`;\n  const initLogic = `\n<script>\n    var diagramsDiv = document.getElementById(\"diagrams\");\n    main.drawDiagramsFromSerializedGrammar(serializedGrammar, diagramsDiv);\n</script>\n`;\n  return header + cssHtml + scripts + diagramsDiv + serializedGrammar + initLogic;\n}", "map": {"version": 3, "names": ["VERSION", "createSyntaxDiagramsCode", "grammar", "resourceBase", "css", "header", "cssHtml", "scripts", "diagramsDiv", "serializedGrammar", "JSON", "stringify", "initLogic"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/diagrams/render_public.ts"], "sourcesContent": ["import { VERSION } from \"../version.js\";\nimport { ISerializedGast } from \"@chevrotain/types\";\n\nexport function createSyntaxDiagramsCode(\n  grammar: ISerializedGast[],\n  {\n    resourceBase = `https://unpkg.com/chevrotain@${VERSION}/diagrams/`,\n    css = `https://unpkg.com/chevrotain@${VERSION}/diagrams/diagrams.css`,\n  }: {\n    resourceBase?: string;\n    css?: string;\n  } = {},\n) {\n  const header = `\n<!-- This is a generated file -->\n<!DOCTYPE html>\n<meta charset=\"utf-8\">\n<style>\n  body {\n    background-color: hsl(30, 20%, 95%)\n  }\n</style>\n\n`;\n  const cssHtml = `\n<link rel='stylesheet' href='${css}'>\n`;\n\n  const scripts = `\n<script src='${resourceBase}vendor/railroad-diagrams.js'></script>\n<script src='${resourceBase}src/diagrams_builder.js'></script>\n<script src='${resourceBase}src/diagrams_behavior.js'></script>\n<script src='${resourceBase}src/main.js'></script>\n`;\n  const diagramsDiv = `\n<div id=\"diagrams\" align=\"center\"></div>    \n`;\n  const serializedGrammar = `\n<script>\n    window.serializedGrammar = ${JSON.stringify(grammar, null, \"  \")};\n</script>\n`;\n\n  const initLogic = `\n<script>\n    var diagramsDiv = document.getElementById(\"diagrams\");\n    main.drawDiagramsFromSerializedGrammar(serializedGrammar, diagramsDiv);\n</script>\n`;\n  return (\n    header + cssHtml + scripts + diagramsDiv + serializedGrammar + initLogic\n  );\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AAGvC,OAAM,SAAUC,wBAAwBA,CACtCC,OAA0B,EAC1B;EACEC,YAAY,GAAG,gCAAgCH,OAAO,YAAY;EAClEI,GAAG,GAAG,gCAAgCJ,OAAO;AAAwB,IAInE,EAAE;EAEN,MAAMK,MAAM,GAAG;;;;;;;;;;CAUhB;EACC,MAAMC,OAAO,GAAG;+BACaF,GAAG;CACjC;EAEC,MAAMG,OAAO,GAAG;eACHJ,YAAY;eACZA,YAAY;eACZA,YAAY;eACZA,YAAY;CAC1B;EACC,MAAMK,WAAW,GAAG;;CAErB;EACC,MAAMC,iBAAiB,GAAG;;iCAEKC,IAAI,CAACC,SAAS,CAACT,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;;CAEnE;EAEC,MAAMU,SAAS,GAAG;;;;;CAKnB;EACC,OACEP,MAAM,GAAGC,OAAO,GAAGC,OAAO,GAAGC,WAAW,GAAGC,iBAAiB,GAAGG,SAAS;AAE5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}