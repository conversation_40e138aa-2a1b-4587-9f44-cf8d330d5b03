{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f = ascending] = F;\n  if (f.length === 1 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascending(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascending(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(f);\n}", "map": {"version": 3, "names": ["ascending", "permute", "sort", "values", "F", "Symbol", "iterator", "TypeError", "Array", "from", "f", "length", "index", "Uint32Array", "d", "i", "map", "j", "c"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/sort.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f = ascending] = F;\n  if (f.length === 1 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascending(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascending(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(f);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,OAAO,MAAM,cAAc;AAElC,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAE,GAAGC,CAAC,EAAE;EACzC,IAAI,OAAOD,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChGJ,MAAM,GAAGK,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC;EAC3B,IAAI,CAACO,CAAC,GAAGV,SAAS,CAAC,GAAGI,CAAC;EACvB,IAAIM,CAAC,CAACC,MAAM,KAAK,CAAC,IAAIP,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;IAClC,MAAMC,KAAK,GAAGC,WAAW,CAACJ,IAAI,CAACN,MAAM,EAAE,CAACW,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IACnD,IAAIX,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;MAChBP,CAAC,GAAGA,CAAC,CAACY,GAAG,CAACN,CAAC,IAAIP,MAAM,CAACa,GAAG,CAACN,CAAC,CAAC,CAAC;MAC7BE,KAAK,CAACV,IAAI,CAAC,CAACa,CAAC,EAAEE,CAAC,KAAK;QACnB,KAAK,MAAMP,CAAC,IAAIN,CAAC,EAAE;UACjB,MAAMc,CAAC,GAAGlB,SAAS,CAACU,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAACO,CAAC,CAAC,CAAC;UAC/B,IAAIC,CAAC,EAAE,OAAOA,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLR,CAAC,GAAGP,MAAM,CAACa,GAAG,CAACN,CAAC,CAAC;MACjBE,KAAK,CAACV,IAAI,CAAC,CAACa,CAAC,EAAEE,CAAC,KAAKjB,SAAS,CAACU,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAACO,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,OAAOhB,OAAO,CAACE,MAAM,EAAES,KAAK,CAAC;EAC/B;EACA,OAAOT,MAAM,CAACD,IAAI,CAACQ,CAAC,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}