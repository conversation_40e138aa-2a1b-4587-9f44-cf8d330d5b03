{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport { LLStarLookaheadStrategy } from './all-star-lookahead.js';", "map": {"version": 3, "names": ["LLStarLookaheadStrategy"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain-allstar/src/index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport {\r\n    AmbiguityReport,\r\n    LLStarLookaheadOptions,\r\n    LLStarLookaheadStrategy\r\n} from './all-star-lookahead.js';\r\n"], "mappings": "AAAA;;;;;AAMA,SAGIA,uBAAuB,QACpB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}