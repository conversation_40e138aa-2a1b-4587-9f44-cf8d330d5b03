{"ast": null, "code": "// needs a separate module as this is required inside chevrotain productive code\n// and also in the entry point for webpack(api.ts).\n// A separate file avoids cyclic dependencies and webpack errors.\nexport const VERSION = \"11.0.3\";", "map": {"version": 3, "names": ["VERSION"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/version.ts"], "sourcesContent": ["// needs a separate module as this is required inside chevrotain productive code\n// and also in the entry point for webpack(api.ts).\n// A separate file avoids cyclic dependencies and webpack errors.\nexport const VERSION = \"11.0.3\";\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,OAAO,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}