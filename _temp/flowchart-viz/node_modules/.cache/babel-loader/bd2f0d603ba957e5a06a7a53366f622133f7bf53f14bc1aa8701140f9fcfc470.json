{"ast": null, "code": "export function defaultX(d) {\n  return d[0];\n}\nexport default function (_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}", "map": {"version": 3, "names": ["defaultX", "d", "_", "arguments", "length", "_x"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-quadtree/src/x.js"], "sourcesContent": ["export function defaultX(d) {\n  return d[0];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,eAAe,UAASC,CAAC,EAAE;EACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,IAAI,CAACC,EAAE,GAAGH,CAAC,EAAE,IAAI,IAAI,IAAI,CAACG,EAAE;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}