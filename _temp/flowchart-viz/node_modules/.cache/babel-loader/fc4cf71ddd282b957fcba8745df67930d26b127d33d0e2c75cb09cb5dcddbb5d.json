{"ast": null, "code": "import clipRectangle from \"../clip/rectangle.js\";\nimport identity from \"../identity.js\";\nimport { transformer } from \"../transform.js\";\nimport { fitExtent, fitSize, fitWidth, fitHeight } from \"./fit.js\";\nimport { cos, degrees, radians, sin } from \"../math.js\";\nexport default function () {\n  var k = 1,\n    tx = 0,\n    ty = 0,\n    sx = 1,\n    sy = 1,\n    // scale, translate and reflect\n    alpha = 0,\n    ca,\n    sa,\n    // angle\n    x0 = null,\n    y0,\n    x1,\n    y1,\n    // clip extent\n    kx = 1,\n    ky = 1,\n    transform = transformer({\n      point: function (x, y) {\n        var p = projection([x, y]);\n        this.stream.point(p[0], p[1]);\n      }\n    }),\n    postclip = identity,\n    cache,\n    cacheStream;\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n  function projection(p) {\n    var x = p[0] * kx,\n      y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }\n    return [x + tx, y + ty];\n  }\n  projection.invert = function (p) {\n    var x = p[0] - tx,\n      y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function (stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function (_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function (_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function (_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function (_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  };\n  projection.angle = function (_) {\n    return arguments.length ? (alpha = _ % 360 * radians, sa = sin(alpha), ca = cos(alpha), reset()) : alpha * degrees;\n  };\n  projection.reflectX = function (_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function (_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function (extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function (size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function (width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function (height, object) {\n    return fitHeight(projection, height, object);\n  };\n  return projection;\n}", "map": {"version": 3, "names": ["clipRectangle", "identity", "transformer", "fitExtent", "fitSize", "fit<PERSON><PERSON><PERSON>", "fitHeight", "cos", "degrees", "radians", "sin", "k", "tx", "ty", "sx", "sy", "alpha", "ca", "sa", "x0", "y0", "x1", "y1", "kx", "ky", "transform", "point", "x", "y", "p", "projection", "stream", "postclip", "cache", "cacheStream", "reset", "t", "invert", "_", "arguments", "length", "clipExtent", "scale", "translate", "angle", "reflectX", "reflectY", "extent", "object", "size", "width", "height"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-geo/src/projection/identity.js"], "sourcesContent": ["import clipRectangle from \"../clip/rectangle.js\";\nimport identity from \"../identity.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport {cos, degrees, radians, sin} from \"../math.js\";\n\nexport default function() {\n  var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, // scale, translate and reflect\n      alpha = 0, ca, sa, // angle\n      x0 = null, y0, x1, y1, // clip extent\n      kx = 1, ky = 1,\n      transform = transformer({\n        point: function(x, y) {\n          var p = projection([x, y])\n          this.stream.point(p[0], p[1]);\n        }\n      }),\n      postclip = identity,\n      cache,\n      cacheStream;\n\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  function projection (p) {\n    var x = p[0] * kx, y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }    \n    return [x + tx, y + ty];\n  }\n  projection.invert = function(p) {\n    var x = p[0] - tx, y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function(_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  }\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, sa = sin(alpha), ca = cos(alpha), reset()) : alpha * degrees;\n  };\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  return projection;\n}\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sBAAsB;AAChD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAAQC,WAAW,QAAO,iBAAiB;AAC3C,SAAQC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAO,UAAU;AAChE,SAAQC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,QAAO,YAAY;AAErD,eAAe,YAAW;EACxB,IAAIC,CAAC,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAE;IACvCC,KAAK,GAAG,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAE;IACnBC,EAAE,GAAG,IAAI;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAE;IACvBC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IACdC,SAAS,GAAGvB,WAAW,CAAC;MACtBwB,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;QACpB,IAAIC,CAAC,GAAGC,UAAU,CAAC,CAACH,CAAC,EAAEC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAACG,MAAM,CAACL,KAAK,CAACG,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;IACFG,QAAQ,GAAG/B,QAAQ;IACnBgC,KAAK;IACLC,WAAW;EAEf,SAASC,KAAKA,CAAA,EAAG;IACfZ,EAAE,GAAGZ,CAAC,GAAGG,EAAE;IACXU,EAAE,GAAGb,CAAC,GAAGI,EAAE;IACXkB,KAAK,GAAGC,WAAW,GAAG,IAAI;IAC1B,OAAOJ,UAAU;EACnB;EAEA,SAASA,UAAUA,CAAED,CAAC,EAAE;IACtB,IAAIF,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAAGN,EAAE;MAAEK,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGL,EAAE;IAChC,IAAIR,KAAK,EAAE;MACT,IAAIoB,CAAC,GAAGR,CAAC,GAAGX,EAAE,GAAGU,CAAC,GAAGT,EAAE;MACvBS,CAAC,GAAGA,CAAC,GAAGV,EAAE,GAAGW,CAAC,GAAGV,EAAE;MACnBU,CAAC,GAAGQ,CAAC;IACP;IACA,OAAO,CAACT,CAAC,GAAGf,EAAE,EAAEgB,CAAC,GAAGf,EAAE,CAAC;EACzB;EACAiB,UAAU,CAACO,MAAM,GAAG,UAASR,CAAC,EAAE;IAC9B,IAAIF,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAAGjB,EAAE;MAAEgB,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGhB,EAAE;IAChC,IAAIG,KAAK,EAAE;MACT,IAAIoB,CAAC,GAAGR,CAAC,GAAGX,EAAE,GAAGU,CAAC,GAAGT,EAAE;MACvBS,CAAC,GAAGA,CAAC,GAAGV,EAAE,GAAGW,CAAC,GAAGV,EAAE;MACnBU,CAAC,GAAGQ,CAAC;IACP;IACA,OAAO,CAACT,CAAC,GAAGJ,EAAE,EAAEK,CAAC,GAAGJ,EAAE,CAAC;EACzB,CAAC;EACDM,UAAU,CAACC,MAAM,GAAG,UAASA,MAAM,EAAE;IACnC,OAAOE,KAAK,IAAIC,WAAW,KAAKH,MAAM,GAAGE,KAAK,GAAGA,KAAK,GAAGR,SAAS,CAACO,QAAQ,CAACE,WAAW,GAAGH,MAAM,CAAC,CAAC;EACpG,CAAC;EACDD,UAAU,CAACE,QAAQ,GAAG,UAASM,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIR,QAAQ,GAAGM,CAAC,EAAEnB,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,IAAI,EAAEa,KAAK,CAAC,CAAC,IAAIH,QAAQ;EACxF,CAAC;EACDF,UAAU,CAACW,UAAU,GAAG,UAASH,CAAC,EAAE;IAClC,OAAOC,SAAS,CAACC,MAAM,IAAIR,QAAQ,GAAGM,CAAC,IAAI,IAAI,IAAInB,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,IAAI,EAAErB,QAAQ,IAAID,aAAa,CAACmB,EAAE,GAAG,CAACmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAElB,EAAE,GAAG,CAACkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,EAAE,GAAG,CAACiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEhB,EAAE,GAAG,CAACgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC,IAAIhB,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAACA,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;EACzN,CAAC;EACDQ,UAAU,CAACY,KAAK,GAAG,UAASJ,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACC,MAAM,IAAI7B,CAAC,GAAG,CAAC2B,CAAC,EAAEH,KAAK,CAAC,CAAC,IAAIxB,CAAC;EACjD,CAAC;EACDmB,UAAU,CAACa,SAAS,GAAG,UAASL,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAI5B,EAAE,GAAG,CAAC0B,CAAC,CAAC,CAAC,CAAC,EAAEzB,EAAE,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC,IAAI,CAACvB,EAAE,EAAEC,EAAE,CAAC;EACxE,CAAC;EACDiB,UAAU,CAACc,KAAK,GAAG,UAASN,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACC,MAAM,IAAIxB,KAAK,GAAGsB,CAAC,GAAG,GAAG,GAAG7B,OAAO,EAAES,EAAE,GAAGR,GAAG,CAACM,KAAK,CAAC,EAAEC,EAAE,GAAGV,GAAG,CAACS,KAAK,CAAC,EAAEmB,KAAK,CAAC,CAAC,IAAInB,KAAK,GAAGR,OAAO;EACpH,CAAC;EACDsB,UAAU,CAACe,QAAQ,GAAG,UAASP,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAI1B,EAAE,GAAGwB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEH,KAAK,CAAC,CAAC,IAAIrB,EAAE,GAAG,CAAC;EAC/D,CAAC;EACDgB,UAAU,CAACgB,QAAQ,GAAG,UAASR,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIzB,EAAE,GAAGuB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEH,KAAK,CAAC,CAAC,IAAIpB,EAAE,GAAG,CAAC;EAC/D,CAAC;EACDe,UAAU,CAAC3B,SAAS,GAAG,UAAS4C,MAAM,EAAEC,MAAM,EAAE;IAC9C,OAAO7C,SAAS,CAAC2B,UAAU,EAAEiB,MAAM,EAAEC,MAAM,CAAC;EAC9C,CAAC;EACDlB,UAAU,CAAC1B,OAAO,GAAG,UAAS6C,IAAI,EAAED,MAAM,EAAE;IAC1C,OAAO5C,OAAO,CAAC0B,UAAU,EAAEmB,IAAI,EAAED,MAAM,CAAC;EAC1C,CAAC;EACDlB,UAAU,CAACzB,QAAQ,GAAG,UAAS6C,KAAK,EAAEF,MAAM,EAAE;IAC5C,OAAO3C,QAAQ,CAACyB,UAAU,EAAEoB,KAAK,EAAEF,MAAM,CAAC;EAC5C,CAAC;EACDlB,UAAU,CAACxB,SAAS,GAAG,UAAS6C,MAAM,EAAEH,MAAM,EAAE;IAC9C,OAAO1C,SAAS,CAACwB,UAAU,EAAEqB,MAAM,EAAEH,MAAM,CAAC;EAC9C,CAAC;EAED,OAAOlB,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}