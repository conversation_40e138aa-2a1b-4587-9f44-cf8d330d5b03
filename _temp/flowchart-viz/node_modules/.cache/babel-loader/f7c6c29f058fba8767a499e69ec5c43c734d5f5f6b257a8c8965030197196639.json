{"ast": null, "code": "import { dispatch } from \"d3-dispatch\";\nimport { timer, timeout } from \"d3-timer\";\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\nexport default function (node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index,\n    // For context during callback.\n    group: group,\n    // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n    tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function () {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n      i = -1,\n      n = tween.length;\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}", "map": {"version": 3, "names": ["dispatch", "timer", "timeout", "emptyOn", "emptyTween", "CREATED", "SCHEDULED", "STARTING", "STARTED", "RUNNING", "ENDING", "ENDED", "node", "name", "id", "index", "group", "timing", "schedules", "__transition", "create", "on", "tween", "time", "delay", "duration", "ease", "state", "init", "schedule", "get", "Error", "set", "self", "elapsed", "restart", "start", "i", "j", "n", "o", "stop", "call", "__data__", "tick", "Array", "length", "value", "t"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-transition/src/transition/schedule.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,SAAQC,KAAK,EAAEC,OAAO,QAAO,UAAU;AAEvC,IAAIC,OAAO,GAAGH,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC;AAC7D,IAAII,UAAU,GAAG,EAAE;AAEnB,OAAO,IAAIC,OAAO,GAAG,CAAC;AACtB,OAAO,IAAIC,SAAS,GAAG,CAAC;AACxB,OAAO,IAAIC,QAAQ,GAAG,CAAC;AACvB,OAAO,IAAIC,OAAO,GAAG,CAAC;AACtB,OAAO,IAAIC,OAAO,GAAG,CAAC;AACtB,OAAO,IAAIC,MAAM,GAAG,CAAC;AACrB,OAAO,IAAIC,KAAK,GAAG,CAAC;AAEpB,eAAe,UAASC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC5D,IAAIC,SAAS,GAAGN,IAAI,CAACO,YAAY;EACjC,IAAI,CAACD,SAAS,EAAEN,IAAI,CAACO,YAAY,GAAG,CAAC,CAAC,CAAC,KAClC,IAAIL,EAAE,IAAII,SAAS,EAAE;EAC1BE,MAAM,CAACR,IAAI,EAAEE,EAAE,EAAE;IACfD,IAAI,EAAEA,IAAI;IACVE,KAAK,EAAEA,KAAK;IAAE;IACdC,KAAK,EAAEA,KAAK;IAAE;IACdK,EAAE,EAAElB,OAAO;IACXmB,KAAK,EAAElB,UAAU;IACjBmB,IAAI,EAAEN,MAAM,CAACM,IAAI;IACjBC,KAAK,EAAEP,MAAM,CAACO,KAAK;IACnBC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;IACzBC,IAAI,EAAET,MAAM,CAACS,IAAI;IACjBzB,KAAK,EAAE,IAAI;IACX0B,KAAK,EAAEtB;EACT,CAAC,CAAC;AACJ;AAEA,OAAO,SAASuB,IAAIA,CAAChB,IAAI,EAAEE,EAAE,EAAE;EAC7B,IAAIe,QAAQ,GAAGC,GAAG,CAAClB,IAAI,EAAEE,EAAE,CAAC;EAC5B,IAAIe,QAAQ,CAACF,KAAK,GAAGtB,OAAO,EAAE,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;EAC5E,OAAOF,QAAQ;AACjB;AAEA,OAAO,SAASG,GAAGA,CAACpB,IAAI,EAAEE,EAAE,EAAE;EAC5B,IAAIe,QAAQ,GAAGC,GAAG,CAAClB,IAAI,EAAEE,EAAE,CAAC;EAC5B,IAAIe,QAAQ,CAACF,KAAK,GAAGnB,OAAO,EAAE,MAAM,IAAIuB,KAAK,CAAC,2BAA2B,CAAC;EAC1E,OAAOF,QAAQ;AACjB;AAEA,OAAO,SAASC,GAAGA,CAAClB,IAAI,EAAEE,EAAE,EAAE;EAC5B,IAAIe,QAAQ,GAAGjB,IAAI,CAACO,YAAY;EAChC,IAAI,CAACU,QAAQ,IAAI,EAAEA,QAAQ,GAAGA,QAAQ,CAACf,EAAE,CAAC,CAAC,EAAE,MAAM,IAAIiB,KAAK,CAAC,sBAAsB,CAAC;EACpF,OAAOF,QAAQ;AACjB;AAEA,SAAST,MAAMA,CAACR,IAAI,EAAEE,EAAE,EAAEmB,IAAI,EAAE;EAC9B,IAAIf,SAAS,GAAGN,IAAI,CAACO,YAAY;IAC7BG,KAAK;;EAET;EACA;EACAJ,SAAS,CAACJ,EAAE,CAAC,GAAGmB,IAAI;EACpBA,IAAI,CAAChC,KAAK,GAAGA,KAAK,CAAC4B,QAAQ,EAAE,CAAC,EAAEI,IAAI,CAACV,IAAI,CAAC;EAE1C,SAASM,QAAQA,CAACK,OAAO,EAAE;IACzBD,IAAI,CAACN,KAAK,GAAGrB,SAAS;IACtB2B,IAAI,CAAChC,KAAK,CAACkC,OAAO,CAACC,KAAK,EAAEH,IAAI,CAACT,KAAK,EAAES,IAAI,CAACV,IAAI,CAAC;;IAEhD;IACA,IAAIU,IAAI,CAACT,KAAK,IAAIU,OAAO,EAAEE,KAAK,CAACF,OAAO,GAAGD,IAAI,CAACT,KAAK,CAAC;EACxD;EAEA,SAASY,KAAKA,CAACF,OAAO,EAAE;IACtB,IAAIG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC;;IAEd;IACA,IAAIP,IAAI,CAACN,KAAK,KAAKrB,SAAS,EAAE,OAAOmC,IAAI,CAAC,CAAC;IAE3C,KAAKJ,CAAC,IAAInB,SAAS,EAAE;MACnBsB,CAAC,GAAGtB,SAAS,CAACmB,CAAC,CAAC;MAChB,IAAIG,CAAC,CAAC3B,IAAI,KAAKoB,IAAI,CAACpB,IAAI,EAAE;;MAE1B;MACA;MACA;MACA,IAAI2B,CAAC,CAACb,KAAK,KAAKnB,OAAO,EAAE,OAAON,OAAO,CAACkC,KAAK,CAAC;;MAE9C;MACA,IAAII,CAAC,CAACb,KAAK,KAAKlB,OAAO,EAAE;QACvB+B,CAAC,CAACb,KAAK,GAAGhB,KAAK;QACf6B,CAAC,CAACvC,KAAK,CAACwC,IAAI,CAAC,CAAC;QACdD,CAAC,CAACnB,EAAE,CAACqB,IAAI,CAAC,WAAW,EAAE9B,IAAI,EAAEA,IAAI,CAAC+B,QAAQ,EAAEH,CAAC,CAACzB,KAAK,EAAEyB,CAAC,CAACxB,KAAK,CAAC;QAC7D,OAAOE,SAAS,CAACmB,CAAC,CAAC;MACrB;;MAEA;MAAA,KACK,IAAI,CAACA,CAAC,GAAGvB,EAAE,EAAE;QAChB0B,CAAC,CAACb,KAAK,GAAGhB,KAAK;QACf6B,CAAC,CAACvC,KAAK,CAACwC,IAAI,CAAC,CAAC;QACdD,CAAC,CAACnB,EAAE,CAACqB,IAAI,CAAC,QAAQ,EAAE9B,IAAI,EAAEA,IAAI,CAAC+B,QAAQ,EAAEH,CAAC,CAACzB,KAAK,EAAEyB,CAAC,CAACxB,KAAK,CAAC;QAC1D,OAAOE,SAAS,CAACmB,CAAC,CAAC;MACrB;IACF;;IAEA;IACA;IACA;IACA;IACAnC,OAAO,CAAC,YAAW;MACjB,IAAI+B,IAAI,CAACN,KAAK,KAAKnB,OAAO,EAAE;QAC1ByB,IAAI,CAACN,KAAK,GAAGlB,OAAO;QACpBwB,IAAI,CAAChC,KAAK,CAACkC,OAAO,CAACS,IAAI,EAAEX,IAAI,CAACT,KAAK,EAAES,IAAI,CAACV,IAAI,CAAC;QAC/CqB,IAAI,CAACV,OAAO,CAAC;MACf;IACF,CAAC,CAAC;;IAEF;IACA;IACAD,IAAI,CAACN,KAAK,GAAGpB,QAAQ;IACrB0B,IAAI,CAACZ,EAAE,CAACqB,IAAI,CAAC,OAAO,EAAE9B,IAAI,EAAEA,IAAI,CAAC+B,QAAQ,EAAEV,IAAI,CAAClB,KAAK,EAAEkB,IAAI,CAACjB,KAAK,CAAC;IAClE,IAAIiB,IAAI,CAACN,KAAK,KAAKpB,QAAQ,EAAE,OAAO,CAAC;IACrC0B,IAAI,CAACN,KAAK,GAAGnB,OAAO;;IAEpB;IACAc,KAAK,GAAG,IAAIuB,KAAK,CAACN,CAAC,GAAGN,IAAI,CAACX,KAAK,CAACwB,MAAM,CAAC;IACxC,KAAKT,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAED,CAAC,GAAGE,CAAC,EAAE,EAAEF,CAAC,EAAE;MAC9B,IAAIG,CAAC,GAAGP,IAAI,CAACX,KAAK,CAACe,CAAC,CAAC,CAACU,KAAK,CAACL,IAAI,CAAC9B,IAAI,EAAEA,IAAI,CAAC+B,QAAQ,EAAEV,IAAI,CAAClB,KAAK,EAAEkB,IAAI,CAACjB,KAAK,CAAC,EAAE;QAC7EM,KAAK,CAAC,EAAEgB,CAAC,CAAC,GAAGE,CAAC;MAChB;IACF;IACAlB,KAAK,CAACwB,MAAM,GAAGR,CAAC,GAAG,CAAC;EACtB;EAEA,SAASM,IAAIA,CAACV,OAAO,EAAE;IACrB,IAAIc,CAAC,GAAGd,OAAO,GAAGD,IAAI,CAACR,QAAQ,GAAGQ,IAAI,CAACP,IAAI,CAACgB,IAAI,CAAC,IAAI,EAAER,OAAO,GAAGD,IAAI,CAACR,QAAQ,CAAC,IAAIQ,IAAI,CAAChC,KAAK,CAACkC,OAAO,CAACM,IAAI,CAAC,EAAER,IAAI,CAACN,KAAK,GAAGjB,MAAM,EAAE,CAAC,CAAC;MAChI2B,CAAC,GAAG,CAAC,CAAC;MACNE,CAAC,GAAGjB,KAAK,CAACwB,MAAM;IAEpB,OAAO,EAAET,CAAC,GAAGE,CAAC,EAAE;MACdjB,KAAK,CAACe,CAAC,CAAC,CAACK,IAAI,CAAC9B,IAAI,EAAEoC,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIf,IAAI,CAACN,KAAK,KAAKjB,MAAM,EAAE;MACzBuB,IAAI,CAACZ,EAAE,CAACqB,IAAI,CAAC,KAAK,EAAE9B,IAAI,EAAEA,IAAI,CAAC+B,QAAQ,EAAEV,IAAI,CAAClB,KAAK,EAAEkB,IAAI,CAACjB,KAAK,CAAC;MAChEyB,IAAI,CAAC,CAAC;IACR;EACF;EAEA,SAASA,IAAIA,CAAA,EAAG;IACdR,IAAI,CAACN,KAAK,GAAGhB,KAAK;IAClBsB,IAAI,CAAChC,KAAK,CAACwC,IAAI,CAAC,CAAC;IACjB,OAAOvB,SAAS,CAACJ,EAAE,CAAC;IACpB,KAAK,IAAIuB,CAAC,IAAInB,SAAS,EAAE,OAAO,CAAC;IACjC,OAAON,IAAI,CAACO,YAAY;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}