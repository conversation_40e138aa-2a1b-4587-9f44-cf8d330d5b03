{"ast": null, "code": "export const defaultLexerErrorProvider = {\n  buildUnableToPopLexerModeMessage(token) {\n    return `Unable to pop Lexer Mode after encountering Token ->${token.image}<- The Mode Stack is empty`;\n  },\n  buildUnexpectedCharactersMessage(fullText, startOffset, length, line, column) {\n    return `unexpected character: ->${fullText.charAt(startOffset)}<- at offset: ${startOffset},` + ` skipped ${length} characters.`;\n  }\n};", "map": {"version": 3, "names": ["defaultLexerErrorProvider", "buildUnableToPopLexerModeMessage", "token", "image", "buildUnexpectedCharactersMessage", "fullText", "startOffset", "length", "line", "column", "char<PERSON>t"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/lexer_errors_public.ts"], "sourcesContent": ["import { ILexerErrorMessageProvider, IToken } from \"@chevrotain/types\";\n\nexport const defaultLexerErrorProvider: ILexerErrorMessageProvider = {\n  buildUnableToPopLexerModeMessage(token: IToken): string {\n    return `Unable to pop Lexer Mode after encountering Token ->${token.image}<- The Mode Stack is empty`;\n  },\n\n  buildUnexpectedCharactersMessage(\n    fullText: string,\n    startOffset: number,\n    length: number,\n    line?: number,\n    column?: number,\n  ): string {\n    return (\n      `unexpected character: ->${fullText.charAt(\n        startOffset,\n      )}<- at offset: ${startOffset},` + ` skipped ${length} characters.`\n    );\n  },\n};\n"], "mappings": "AAEA,OAAO,MAAMA,yBAAyB,GAA+B;EACnEC,gCAAgCA,CAACC,KAAa;IAC5C,OAAO,uDAAuDA,KAAK,CAACC,KAAK,4BAA4B;EACvG,CAAC;EAEDC,gCAAgCA,CAC9BC,QAAgB,EAChBC,WAAmB,EACnBC,MAAc,EACdC,IAAa,EACbC,MAAe;IAEf,OACE,2BAA2BJ,QAAQ,CAACK,MAAM,CACxCJ,WAAW,CACZ,iBAAiBA,WAAW,GAAG,GAAG,YAAYC,MAAM,cAAc;EAEvE;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}