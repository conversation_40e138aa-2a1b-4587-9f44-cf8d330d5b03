{"ast": null, "code": "/* IMPORT */\nimport _ from './utils/index.js';\n/* MAIN */\nconst DEC2HEX = {};\nfor (let i = 0; i <= 255; i++) DEC2HEX[i] = _.unit.dec2hex(i); // Populating dynamically, striking a balance between code size and performance\nconst TYPE = {\n  ALL: 0,\n  RGB: 1,\n  HSL: 2\n};\n/* EXPORT */\nexport { DEC2HEX, TYPE };", "map": {"version": 3, "names": ["_", "DEC2HEX", "i", "unit", "dec2hex", "TYPE", "ALL", "RGB", "HSL"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/constants.js"], "sourcesContent": ["/* IMPORT */\nimport _ from './utils/index.js';\n/* MAIN */\nconst DEC2HEX = {};\nfor (let i = 0; i <= 255; i++)\n    DEC2HEX[i] = _.unit.dec2hex(i); // Populating dynamically, striking a balance between code size and performance\nconst TYPE = {\n    ALL: 0,\n    RGB: 1,\n    HSL: 2\n};\n/* EXPORT */\nexport { DEC2HEX, TYPE };\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,kBAAkB;AAChC;AACA,MAAMC,OAAO,GAAG,CAAC,CAAC;AAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,GAAG,EAAEA,CAAC,EAAE,EACzBD,OAAO,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACG,IAAI,CAACC,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC;AACpC,MAAMG,IAAI,GAAG;EACTC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE;AACT,CAAC;AACD;AACA,SAASP,OAAO,EAAEI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}