{"ast": null, "code": "import { END_OF_FILE } from \"../parser.js\";\n/**\n * Trait responsible abstracting over the interaction with <PERSON>er output (Token vector).\n *\n * This could be generalized to support other kinds of lexers, e.g.\n * - Just in Time Lexing / Lexer-Less parsing.\n * - Streaming Lexer.\n */\nexport class LexerAdapter {\n  initLexerAdapter() {\n    this.tokVector = [];\n    this.tokVectorLength = 0;\n    this.currIdx = -1;\n  }\n  set input(newInput) {\n    // @ts-ignore - `this parameter` not supported in setters/getters\n    //   - https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\n    if (this.selfAnalysisDone !== true) {\n      throw Error(`Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.`);\n    }\n    // @ts-ignore - `this parameter` not supported in setters/getters\n    //   - https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\n    this.reset();\n    this.tokVector = newInput;\n    this.tokVectorLength = newInput.length;\n  }\n  get input() {\n    return this.tokVector;\n  }\n  // skips a token and returns the next token\n  SKIP_TOKEN() {\n    if (this.currIdx <= this.tokVector.length - 2) {\n      this.consumeToken();\n      return this.LA(1);\n    } else {\n      return END_OF_FILE;\n    }\n  }\n  // Lexer (accessing Token vector) related methods which can be overridden to implement lazy lexers\n  // or lexers dependent on parser context.\n  LA(howMuch) {\n    const soughtIdx = this.currIdx + howMuch;\n    if (soughtIdx < 0 || this.tokVectorLength <= soughtIdx) {\n      return END_OF_FILE;\n    } else {\n      return this.tokVector[soughtIdx];\n    }\n  }\n  consumeToken() {\n    this.currIdx++;\n  }\n  exportLexerState() {\n    return this.currIdx;\n  }\n  importLexerState(newState) {\n    this.currIdx = newState;\n  }\n  resetLexerState() {\n    this.currIdx = -1;\n  }\n  moveToTerminatedState() {\n    this.currIdx = this.tokVector.length - 1;\n  }\n  getLexerPosition() {\n    return this.exportLexerState();\n  }\n}", "map": {"version": 3, "names": ["END_OF_FILE", "LexerAdapter", "initLexerAdapter", "tokVector", "tokVectorLength", "currIdx", "input", "newInput", "selfAnalysisDone", "Error", "reset", "length", "SKIP_TOKEN", "consumeToken", "LA", "<PERSON><PERSON><PERSON>", "soughtIdx", "exportLexerState", "importLexerState", "newState", "resetLexerState", "moveToTerminatedState", "getLexerPosition"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/lexer_adapter.ts"], "sourcesContent": ["import { END_OF_FILE } from \"../parser.js\";\nimport { IToken } from \"@chevrotain/types\";\nimport { MixedInParser } from \"./parser_traits.js\";\n\n/**\n * Trait responsible abstracting over the interaction with Lexer output (Token vector).\n *\n * This could be generalized to support other kinds of lexers, e.g.\n * - Just in Time Lexing / Lexer-Less parsing.\n * - Streaming Lexer.\n */\nexport class LexerAdapter {\n  tokVector: IToken[];\n  tokVectorLength: number;\n  currIdx: number;\n\n  initLexerAdapter() {\n    this.tokVector = [];\n    this.tokVectorLength = 0;\n    this.currIdx = -1;\n  }\n\n  set input(newInput: IToken[]) {\n    // @ts-ignore - `this parameter` not supported in setters/getters\n    //   - https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\n    if (this.selfAnalysisDone !== true) {\n      throw Error(\n        `Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.`,\n      );\n    }\n    // @ts-ignore - `this parameter` not supported in setters/getters\n    //   - https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\n    this.reset();\n    this.tokVector = newInput;\n    this.tokVectorLength = newInput.length;\n  }\n\n  get input(): IToken[] {\n    return this.tokVector;\n  }\n\n  // skips a token and returns the next token\n  SKIP_TOKEN(this: MixedInParser): IToken {\n    if (this.currIdx <= this.tokVector.length - 2) {\n      this.consumeToken();\n      return this.LA(1);\n    } else {\n      return END_OF_FILE;\n    }\n  }\n\n  // Lexer (accessing Token vector) related methods which can be overridden to implement lazy lexers\n  // or lexers dependent on parser context.\n  LA(this: MixedInParser, howMuch: number): IToken {\n    const soughtIdx = this.currIdx + howMuch;\n    if (soughtIdx < 0 || this.tokVectorLength <= soughtIdx) {\n      return END_OF_FILE;\n    } else {\n      return this.tokVector[soughtIdx];\n    }\n  }\n\n  consumeToken(this: MixedInParser) {\n    this.currIdx++;\n  }\n\n  exportLexerState(this: MixedInParser): number {\n    return this.currIdx;\n  }\n\n  importLexerState(this: MixedInParser, newState: number) {\n    this.currIdx = newState;\n  }\n\n  resetLexerState(this: MixedInParser): void {\n    this.currIdx = -1;\n  }\n\n  moveToTerminatedState(this: MixedInParser): void {\n    this.currIdx = this.tokVector.length - 1;\n  }\n\n  getLexerPosition(this: MixedInParser): number {\n    return this.exportLexerState();\n  }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,cAAc;AAI1C;;;;;;;AAOA,OAAM,MAAOC,YAAY;EAKvBC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEA,IAAIC,KAAKA,CAACC,QAAkB;IAC1B;IACA;IACA,IAAI,IAAI,CAACC,gBAAgB,KAAK,IAAI,EAAE;MAClC,MAAMC,KAAK,CACT,kFAAkF,CACnF;;IAEH;IACA;IACA,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACP,SAAS,GAAGI,QAAQ;IACzB,IAAI,CAACH,eAAe,GAAGG,QAAQ,CAACI,MAAM;EACxC;EAEA,IAAIL,KAAKA,CAAA;IACP,OAAO,IAAI,CAACH,SAAS;EACvB;EAEA;EACAS,UAAUA,CAAA;IACR,IAAI,IAAI,CAACP,OAAO,IAAI,IAAI,CAACF,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACE,YAAY,EAAE;MACnB,OAAO,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;KAClB,MAAM;MACL,OAAOd,WAAW;;EAEtB;EAEA;EACA;EACAc,EAAEA,CAAsBC,OAAe;IACrC,MAAMC,SAAS,GAAG,IAAI,CAACX,OAAO,GAAGU,OAAO;IACxC,IAAIC,SAAS,GAAG,CAAC,IAAI,IAAI,CAACZ,eAAe,IAAIY,SAAS,EAAE;MACtD,OAAOhB,WAAW;KACnB,MAAM;MACL,OAAO,IAAI,CAACG,SAAS,CAACa,SAAS,CAAC;;EAEpC;EAEAH,YAAYA,CAAA;IACV,IAAI,CAACR,OAAO,EAAE;EAChB;EAEAY,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACZ,OAAO;EACrB;EAEAa,gBAAgBA,CAAsBC,QAAgB;IACpD,IAAI,CAACd,OAAO,GAAGc,QAAQ;EACzB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACf,OAAO,GAAG,CAAC,CAAC;EACnB;EAEAgB,qBAAqBA,CAAA;IACnB,IAAI,CAAChB,OAAO,GAAG,IAAI,CAACF,SAAS,CAACQ,MAAM,GAAG,CAAC;EAC1C;EAEAW,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACL,gBAAgB,EAAE;EAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}