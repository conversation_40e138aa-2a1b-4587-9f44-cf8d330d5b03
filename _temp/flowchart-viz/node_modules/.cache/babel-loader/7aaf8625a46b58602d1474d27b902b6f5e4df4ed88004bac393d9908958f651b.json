{"ast": null, "code": "import max from \"./max.js\";\nimport min from \"./min.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, { numbers } from \"./number.js\";\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n    value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = +valueof(values[i0], i0, values),\n    value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}", "map": {"version": 3, "names": ["max", "min", "quickselect", "number", "numbers", "quantile", "values", "p", "valueof", "Float64Array", "from", "n", "length", "i", "i0", "Math", "floor", "value0", "subarray", "value1", "quantileSorted"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/quantile.js"], "sourcesContent": ["import max from \"./max.js\";\nimport min from \"./min.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,GAAG,MAAM,UAAU;AAC1B,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,MAAM,IAAGC,OAAO,QAAO,aAAa;AAE3C,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACnDF,MAAM,GAAGG,YAAY,CAACC,IAAI,CAACN,OAAO,CAACE,MAAM,EAAEE,OAAO,CAAC,CAAC;EACpD,IAAI,EAAEG,CAAC,GAAGL,MAAM,CAACM,MAAM,CAAC,EAAE;EAC1B,IAAI,CAACL,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE,OAAOV,GAAG,CAACK,MAAM,CAAC;EAC9C,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAOP,GAAG,CAACM,MAAM,CAAC;EAC9B,IAAIK,CAAC;IACDE,CAAC,GAAG,CAACF,CAAC,GAAG,CAAC,IAAIJ,CAAC;IACfO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAGjB,GAAG,CAACE,WAAW,CAACI,MAAM,EAAEQ,EAAE,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAEJ,EAAE,GAAG,CAAC,CAAC,CAAC;IACzDK,MAAM,GAAGlB,GAAG,CAACK,MAAM,CAACY,QAAQ,CAACJ,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,OAAOG,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C;AAEA,OAAO,SAASM,cAAcA,CAACd,MAAM,EAAEC,CAAC,EAAEC,OAAO,GAAGL,MAAM,EAAE;EAC1D,IAAI,EAAEQ,CAAC,GAAGL,MAAM,CAACM,MAAM,CAAC,EAAE;EAC1B,IAAI,CAACL,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE,OAAO,CAACH,OAAO,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC;EACjE,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAO,CAACC,OAAO,CAACF,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEL,MAAM,CAAC;EACzD,IAAIK,CAAC;IACDE,CAAC,GAAG,CAACF,CAAC,GAAG,CAAC,IAAIJ,CAAC;IACfO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAG,CAACT,OAAO,CAACF,MAAM,CAACQ,EAAE,CAAC,EAAEA,EAAE,EAAER,MAAM,CAAC;IACzCa,MAAM,GAAG,CAACX,OAAO,CAACF,MAAM,CAACQ,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAER,MAAM,CAAC;EACrD,OAAOW,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}