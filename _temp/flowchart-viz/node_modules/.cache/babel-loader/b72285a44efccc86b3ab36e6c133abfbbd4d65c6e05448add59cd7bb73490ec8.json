{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"deebf79ecae13182bd\", \"eff3ffbdd7e76baed62171b5\", \"eff3ffbdd7e76baed63182bd08519c\", \"eff3ffc6dbef9ecae16baed63182bd08519c\", \"eff3ffc6dbef9ecae16baed64292c62171b5084594\", \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594\", \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-scale-chromatic/src/sequential-single/Blues.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"deebf79ecae13182bd\",\n  \"eff3ffbdd7e76baed62171b5\",\n  \"eff3ffbdd7e76baed63182bd08519c\",\n  \"eff3ffc6dbef9ecae16baed63182bd08519c\",\n  \"eff3ffc6dbef9ecae16baed64292c62171b5084594\",\n  \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594\",\n  \"f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}