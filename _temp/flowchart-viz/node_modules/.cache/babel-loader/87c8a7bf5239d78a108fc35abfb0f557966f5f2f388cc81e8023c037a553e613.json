{"ast": null, "code": "import { analyzeTokenTypes, charCodeToOptimizedIndex, cloneEmptyGroups, DEFAULT_MODE, LineTerminatorOptimizedTester, performRuntimeChecks, performWarningRuntimeChecks, SUPPORT_STICKY, validatePatterns } from \"./lexer.js\";\nimport { assign, clone, forEach, identity, isArray, isEmpty, isUndefined, keys, last, map, noop, reduce, reject } from \"lodash-es\";\nimport { PRINT_WARNING, timer, toFastProperties } from \"@chevrotain/utils\";\nimport { augmentTokenTypes } from \"./tokens.js\";\nimport { defaultLexerErrorProvider } from \"./lexer_errors_public.js\";\nimport { clearRegExpParserCache } from \"./reg_exp_parser.js\";\nexport var LexerDefinitionErrorType;\n(function (LexerDefinitionErrorType) {\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"MISSING_PATTERN\"] = 0] = \"MISSING_PATTERN\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"INVALID_PATTERN\"] = 1] = \"INVALID_PATTERN\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"EOI_ANCHOR_FOUND\"] = 2] = \"EOI_ANCHOR_FOUND\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"UNSUPPORTED_FLAGS_FOUND\"] = 3] = \"UNSUPPORTED_FLAGS_FOUND\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"DUPLICATE_PATTERNS_FOUND\"] = 4] = \"DUPLICATE_PATTERNS_FOUND\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"INVALID_GROUP_TYPE_FOUND\"] = 5] = \"INVALID_GROUP_TYPE_FOUND\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"PUSH_MODE_DOES_NOT_EXIST\"] = 6] = \"PUSH_MODE_DOES_NOT_EXIST\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE\"] = 7] = \"MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY\"] = 8] = \"MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST\"] = 9] = \"MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED\"] = 10] = \"LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"SOI_ANCHOR_FOUND\"] = 11] = \"SOI_ANCHOR_FOUND\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"EMPTY_MATCH_PATTERN\"] = 12] = \"EMPTY_MATCH_PATTERN\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"NO_LINE_BREAKS_FLAGS\"] = 13] = \"NO_LINE_BREAKS_FLAGS\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"UNREACHABLE_PATTERN\"] = 14] = \"UNREACHABLE_PATTERN\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"IDENTIFY_TERMINATOR\"] = 15] = \"IDENTIFY_TERMINATOR\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"CUSTOM_LINE_BREAK\"] = 16] = \"CUSTOM_LINE_BREAK\";\n  LexerDefinitionErrorType[LexerDefinitionErrorType[\"MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE\"] = 17] = \"MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE\";\n})(LexerDefinitionErrorType || (LexerDefinitionErrorType = {}));\nconst DEFAULT_LEXER_CONFIG = {\n  deferDefinitionErrorsHandling: false,\n  positionTracking: \"full\",\n  lineTerminatorsPattern: /\\n|\\r\\n?/g,\n  lineTerminatorCharacters: [\"\\n\", \"\\r\"],\n  ensureOptimizations: false,\n  safeMode: false,\n  errorMessageProvider: defaultLexerErrorProvider,\n  traceInitPerf: false,\n  skipValidations: false,\n  recoveryEnabled: true\n};\nObject.freeze(DEFAULT_LEXER_CONFIG);\nexport class Lexer {\n  constructor(lexerDefinition, config = DEFAULT_LEXER_CONFIG) {\n    this.lexerDefinition = lexerDefinition;\n    this.lexerDefinitionErrors = [];\n    this.lexerDefinitionWarning = [];\n    this.patternIdxToConfig = {};\n    this.charCodeToPatternIdxToConfig = {};\n    this.modes = [];\n    this.emptyGroups = {};\n    this.trackStartLines = true;\n    this.trackEndLines = true;\n    this.hasCustom = false;\n    this.canModeBeOptimized = {};\n    // Duplicated from the parser's perf trace trait to allow future extraction\n    // of the lexer to a separate package.\n    this.TRACE_INIT = (phaseDesc, phaseImpl) => {\n      // No need to optimize this using NOOP pattern because\n      // It is not called in a hot spot...\n      if (this.traceInitPerf === true) {\n        this.traceInitIndent++;\n        const indent = new Array(this.traceInitIndent + 1).join(\"\\t\");\n        if (this.traceInitIndent < this.traceInitMaxIdent) {\n          console.log(`${indent}--> <${phaseDesc}>`);\n        }\n        const {\n          time,\n          value\n        } = timer(phaseImpl);\n        /* istanbul ignore next - Difficult to reproduce specific performance behavior (>10ms) in tests */\n        const traceMethod = time > 10 ? console.warn : console.log;\n        if (this.traceInitIndent < this.traceInitMaxIdent) {\n          traceMethod(`${indent}<-- <${phaseDesc}> time: ${time}ms`);\n        }\n        this.traceInitIndent--;\n        return value;\n      } else {\n        return phaseImpl();\n      }\n    };\n    if (typeof config === \"boolean\") {\n      throw Error(\"The second argument to the Lexer constructor is now an ILexerConfig Object.\\n\" + \"a boolean 2nd argument is no longer supported\");\n    }\n    // todo: defaults func?\n    this.config = assign({}, DEFAULT_LEXER_CONFIG, config);\n    const traceInitVal = this.config.traceInitPerf;\n    if (traceInitVal === true) {\n      this.traceInitMaxIdent = Infinity;\n      this.traceInitPerf = true;\n    } else if (typeof traceInitVal === \"number\") {\n      this.traceInitMaxIdent = traceInitVal;\n      this.traceInitPerf = true;\n    }\n    this.traceInitIndent = -1;\n    this.TRACE_INIT(\"Lexer Constructor\", () => {\n      let actualDefinition;\n      let hasOnlySingleMode = true;\n      this.TRACE_INIT(\"Lexer Config handling\", () => {\n        if (this.config.lineTerminatorsPattern === DEFAULT_LEXER_CONFIG.lineTerminatorsPattern) {\n          // optimized built-in implementation for the defaults definition of lineTerminators\n          this.config.lineTerminatorsPattern = LineTerminatorOptimizedTester;\n        } else {\n          if (this.config.lineTerminatorCharacters === DEFAULT_LEXER_CONFIG.lineTerminatorCharacters) {\n            throw Error(\"Error: Missing <lineTerminatorCharacters> property on the Lexer config.\\n\" + \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS\");\n          }\n        }\n        if (config.safeMode && config.ensureOptimizations) {\n          throw Error('\"safeMode\" and \"ensureOptimizations\" flags are mutually exclusive.');\n        }\n        this.trackStartLines = /full|onlyStart/i.test(this.config.positionTracking);\n        this.trackEndLines = /full/i.test(this.config.positionTracking);\n        // Convert SingleModeLexerDefinition into a IMultiModeLexerDefinition.\n        if (isArray(lexerDefinition)) {\n          actualDefinition = {\n            modes: {\n              defaultMode: clone(lexerDefinition)\n            },\n            defaultMode: DEFAULT_MODE\n          };\n        } else {\n          // no conversion needed, input should already be a IMultiModeLexerDefinition\n          hasOnlySingleMode = false;\n          actualDefinition = clone(lexerDefinition);\n        }\n      });\n      if (this.config.skipValidations === false) {\n        this.TRACE_INIT(\"performRuntimeChecks\", () => {\n          this.lexerDefinitionErrors = this.lexerDefinitionErrors.concat(performRuntimeChecks(actualDefinition, this.trackStartLines, this.config.lineTerminatorCharacters));\n        });\n        this.TRACE_INIT(\"performWarningRuntimeChecks\", () => {\n          this.lexerDefinitionWarning = this.lexerDefinitionWarning.concat(performWarningRuntimeChecks(actualDefinition, this.trackStartLines, this.config.lineTerminatorCharacters));\n        });\n      }\n      // for extra robustness to avoid throwing an none informative error message\n      actualDefinition.modes = actualDefinition.modes ? actualDefinition.modes : {};\n      // an error of undefined TokenTypes will be detected in \"performRuntimeChecks\" above.\n      // this transformation is to increase robustness in the case of partially invalid lexer definition.\n      forEach(actualDefinition.modes, (currModeValue, currModeName) => {\n        actualDefinition.modes[currModeName] = reject(currModeValue, currTokType => isUndefined(currTokType));\n      });\n      const allModeNames = keys(actualDefinition.modes);\n      forEach(actualDefinition.modes, (currModDef, currModName) => {\n        this.TRACE_INIT(`Mode: <${currModName}> processing`, () => {\n          this.modes.push(currModName);\n          if (this.config.skipValidations === false) {\n            this.TRACE_INIT(`validatePatterns`, () => {\n              this.lexerDefinitionErrors = this.lexerDefinitionErrors.concat(validatePatterns(currModDef, allModeNames));\n            });\n          }\n          // If definition errors were encountered, the analysis phase may fail unexpectedly/\n          // Considering a lexer with definition errors may never be used, there is no point\n          // to performing the analysis anyhow...\n          if (isEmpty(this.lexerDefinitionErrors)) {\n            augmentTokenTypes(currModDef);\n            let currAnalyzeResult;\n            this.TRACE_INIT(`analyzeTokenTypes`, () => {\n              currAnalyzeResult = analyzeTokenTypes(currModDef, {\n                lineTerminatorCharacters: this.config.lineTerminatorCharacters,\n                positionTracking: config.positionTracking,\n                ensureOptimizations: config.ensureOptimizations,\n                safeMode: config.safeMode,\n                tracer: this.TRACE_INIT\n              });\n            });\n            this.patternIdxToConfig[currModName] = currAnalyzeResult.patternIdxToConfig;\n            this.charCodeToPatternIdxToConfig[currModName] = currAnalyzeResult.charCodeToPatternIdxToConfig;\n            this.emptyGroups = assign({}, this.emptyGroups, currAnalyzeResult.emptyGroups);\n            this.hasCustom = currAnalyzeResult.hasCustom || this.hasCustom;\n            this.canModeBeOptimized[currModName] = currAnalyzeResult.canBeOptimized;\n          }\n        });\n      });\n      this.defaultMode = actualDefinition.defaultMode;\n      if (!isEmpty(this.lexerDefinitionErrors) && !this.config.deferDefinitionErrorsHandling) {\n        const allErrMessages = map(this.lexerDefinitionErrors, error => {\n          return error.message;\n        });\n        const allErrMessagesString = allErrMessages.join(\"-----------------------\\n\");\n        throw new Error(\"Errors detected in definition of Lexer:\\n\" + allErrMessagesString);\n      }\n      // Only print warning if there are no errors, This will avoid pl\n      forEach(this.lexerDefinitionWarning, warningDescriptor => {\n        PRINT_WARNING(warningDescriptor.message);\n      });\n      this.TRACE_INIT(\"Choosing sub-methods implementations\", () => {\n        // Choose the relevant internal implementations for this specific parser.\n        // These implementations should be in-lined by the JavaScript engine\n        // to provide optimal performance in each scenario.\n        if (SUPPORT_STICKY) {\n          this.chopInput = identity;\n          this.match = this.matchWithTest;\n        } else {\n          this.updateLastIndex = noop;\n          this.match = this.matchWithExec;\n        }\n        if (hasOnlySingleMode) {\n          this.handleModes = noop;\n        }\n        if (this.trackStartLines === false) {\n          this.computeNewColumn = identity;\n        }\n        if (this.trackEndLines === false) {\n          this.updateTokenEndLineColumnLocation = noop;\n        }\n        if (/full/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createFullToken;\n        } else if (/onlyStart/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createStartOnlyToken;\n        } else if (/onlyOffset/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createOffsetOnlyToken;\n        } else {\n          throw Error(`Invalid <positionTracking> config option: \"${this.config.positionTracking}\"`);\n        }\n        if (this.hasCustom) {\n          this.addToken = this.addTokenUsingPush;\n          this.handlePayload = this.handlePayloadWithCustom;\n        } else {\n          this.addToken = this.addTokenUsingMemberAccess;\n          this.handlePayload = this.handlePayloadNoCustom;\n        }\n      });\n      this.TRACE_INIT(\"Failed Optimization Warnings\", () => {\n        const unOptimizedModes = reduce(this.canModeBeOptimized, (cannotBeOptimized, canBeOptimized, modeName) => {\n          if (canBeOptimized === false) {\n            cannotBeOptimized.push(modeName);\n          }\n          return cannotBeOptimized;\n        }, []);\n        if (config.ensureOptimizations && !isEmpty(unOptimizedModes)) {\n          throw Error(`Lexer Modes: < ${unOptimizedModes.join(\", \")} > cannot be optimized.\\n` + '\\t Disable the \"ensureOptimizations\" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.\\n' + \"\\t Or inspect the console log for details on how to resolve these issues.\");\n        }\n      });\n      this.TRACE_INIT(\"clearRegExpParserCache\", () => {\n        clearRegExpParserCache();\n      });\n      this.TRACE_INIT(\"toFastProperties\", () => {\n        toFastProperties(this);\n      });\n    });\n  }\n  tokenize(text, initialMode = this.defaultMode) {\n    if (!isEmpty(this.lexerDefinitionErrors)) {\n      const allErrMessages = map(this.lexerDefinitionErrors, error => {\n        return error.message;\n      });\n      const allErrMessagesString = allErrMessages.join(\"-----------------------\\n\");\n      throw new Error(\"Unable to Tokenize because Errors detected in definition of Lexer:\\n\" + allErrMessagesString);\n    }\n    return this.tokenizeInternal(text, initialMode);\n  }\n  // There is quite a bit of duplication between this and \"tokenizeInternalLazy\"\n  // This is intentional due to performance considerations.\n  // this method also used quite a bit of `!` none null assertions because it is too optimized\n  // for `tsc` to always understand it is \"safe\"\n  tokenizeInternal(text, initialMode) {\n    let i, j, k, matchAltImage, longerAlt, matchedImage, payload, altPayload, imageLength, group, tokType, newToken, errLength, droppedChar, msg, match;\n    const orgText = text;\n    const orgLength = orgText.length;\n    let offset = 0;\n    let matchedTokensIndex = 0;\n    // initializing the tokensArray to the \"guessed\" size.\n    // guessing too little will still reduce the number of array re-sizes on pushes.\n    // guessing too large (Tested by guessing x4 too large) may cost a bit more of memory\n    // but would still have a faster runtime by avoiding (All but one) array resizing.\n    const guessedNumberOfTokens = this.hasCustom ? 0 // will break custom token pattern APIs the matchedTokens array will contain undefined elements.\n    : Math.floor(text.length / 10);\n    const matchedTokens = new Array(guessedNumberOfTokens);\n    const errors = [];\n    let line = this.trackStartLines ? 1 : undefined;\n    let column = this.trackStartLines ? 1 : undefined;\n    const groups = cloneEmptyGroups(this.emptyGroups);\n    const trackLines = this.trackStartLines;\n    const lineTerminatorPattern = this.config.lineTerminatorsPattern;\n    let currModePatternsLength = 0;\n    let patternIdxToConfig = [];\n    let currCharCodeToPatternIdxToConfig = [];\n    const modeStack = [];\n    const emptyArray = [];\n    Object.freeze(emptyArray);\n    let getPossiblePatterns;\n    function getPossiblePatternsSlow() {\n      return patternIdxToConfig;\n    }\n    function getPossiblePatternsOptimized(charCode) {\n      const optimizedCharIdx = charCodeToOptimizedIndex(charCode);\n      const possiblePatterns = currCharCodeToPatternIdxToConfig[optimizedCharIdx];\n      if (possiblePatterns === undefined) {\n        return emptyArray;\n      } else {\n        return possiblePatterns;\n      }\n    }\n    const pop_mode = popToken => {\n      // TODO: perhaps avoid this error in the edge case there is no more input?\n      if (modeStack.length === 1 &&\n      // if we have both a POP_MODE and a PUSH_MODE this is in-fact a \"transition\"\n      // So no error should occur.\n      popToken.tokenType.PUSH_MODE === undefined) {\n        // if we try to pop the last mode there lexer will no longer have ANY mode.\n        // thus the pop is ignored, an error will be created and the lexer will continue parsing in the previous mode.\n        const msg = this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(popToken);\n        errors.push({\n          offset: popToken.startOffset,\n          line: popToken.startLine,\n          column: popToken.startColumn,\n          length: popToken.image.length,\n          message: msg\n        });\n      } else {\n        modeStack.pop();\n        const newMode = last(modeStack);\n        patternIdxToConfig = this.patternIdxToConfig[newMode];\n        currCharCodeToPatternIdxToConfig = this.charCodeToPatternIdxToConfig[newMode];\n        currModePatternsLength = patternIdxToConfig.length;\n        const modeCanBeOptimized = this.canModeBeOptimized[newMode] && this.config.safeMode === false;\n        if (currCharCodeToPatternIdxToConfig && modeCanBeOptimized) {\n          getPossiblePatterns = getPossiblePatternsOptimized;\n        } else {\n          getPossiblePatterns = getPossiblePatternsSlow;\n        }\n      }\n    };\n    function push_mode(newMode) {\n      modeStack.push(newMode);\n      currCharCodeToPatternIdxToConfig = this.charCodeToPatternIdxToConfig[newMode];\n      patternIdxToConfig = this.patternIdxToConfig[newMode];\n      currModePatternsLength = patternIdxToConfig.length;\n      currModePatternsLength = patternIdxToConfig.length;\n      const modeCanBeOptimized = this.canModeBeOptimized[newMode] && this.config.safeMode === false;\n      if (currCharCodeToPatternIdxToConfig && modeCanBeOptimized) {\n        getPossiblePatterns = getPossiblePatternsOptimized;\n      } else {\n        getPossiblePatterns = getPossiblePatternsSlow;\n      }\n    }\n    // this pattern seems to avoid a V8 de-optimization, although that de-optimization does not\n    // seem to matter performance wise.\n    push_mode.call(this, initialMode);\n    let currConfig;\n    const recoveryEnabled = this.config.recoveryEnabled;\n    while (offset < orgLength) {\n      matchedImage = null;\n      const nextCharCode = orgText.charCodeAt(offset);\n      const chosenPatternIdxToConfig = getPossiblePatterns(nextCharCode);\n      const chosenPatternsLength = chosenPatternIdxToConfig.length;\n      for (i = 0; i < chosenPatternsLength; i++) {\n        currConfig = chosenPatternIdxToConfig[i];\n        const currPattern = currConfig.pattern;\n        payload = null;\n        // manually in-lined because > 600 chars won't be in-lined in V8\n        const singleCharCode = currConfig.short;\n        if (singleCharCode !== false) {\n          if (nextCharCode === singleCharCode) {\n            // single character string\n            matchedImage = currPattern;\n          }\n        } else if (currConfig.isCustom === true) {\n          match = currPattern.exec(orgText, offset, matchedTokens, groups);\n          if (match !== null) {\n            matchedImage = match[0];\n            if (match.payload !== undefined) {\n              payload = match.payload;\n            }\n          } else {\n            matchedImage = null;\n          }\n        } else {\n          this.updateLastIndex(currPattern, offset);\n          matchedImage = this.match(currPattern, text, offset);\n        }\n        if (matchedImage !== null) {\n          // even though this pattern matched we must try a another longer alternative.\n          // this can be used to prioritize keywords over identifiers\n          longerAlt = currConfig.longerAlt;\n          if (longerAlt !== undefined) {\n            // TODO: micro optimize, avoid extra prop access\n            // by saving/linking longerAlt on the original config?\n            const longerAltLength = longerAlt.length;\n            for (k = 0; k < longerAltLength; k++) {\n              const longerAltConfig = patternIdxToConfig[longerAlt[k]];\n              const longerAltPattern = longerAltConfig.pattern;\n              altPayload = null;\n              // single Char can never be a longer alt so no need to test it.\n              // manually in-lined because > 600 chars won't be in-lined in V8\n              if (longerAltConfig.isCustom === true) {\n                match = longerAltPattern.exec(orgText, offset, matchedTokens, groups);\n                if (match !== null) {\n                  matchAltImage = match[0];\n                  if (match.payload !== undefined) {\n                    altPayload = match.payload;\n                  }\n                } else {\n                  matchAltImage = null;\n                }\n              } else {\n                this.updateLastIndex(longerAltPattern, offset);\n                matchAltImage = this.match(longerAltPattern, text, offset);\n              }\n              if (matchAltImage && matchAltImage.length > matchedImage.length) {\n                matchedImage = matchAltImage;\n                payload = altPayload;\n                currConfig = longerAltConfig;\n                // Exit the loop early after matching one of the longer alternatives\n                // The first matched alternative takes precedence\n                break;\n              }\n            }\n          }\n          break;\n        }\n      }\n      // successful match\n      if (matchedImage !== null) {\n        imageLength = matchedImage.length;\n        group = currConfig.group;\n        if (group !== undefined) {\n          tokType = currConfig.tokenTypeIdx;\n          // TODO: \"offset + imageLength\" and the new column may be computed twice in case of \"full\" location information inside\n          // createFullToken method\n          newToken = this.createTokenInstance(matchedImage, offset, tokType, currConfig.tokenType, line, column, imageLength);\n          this.handlePayload(newToken, payload);\n          // TODO: optimize NOOP in case there are no special groups?\n          if (group === false) {\n            matchedTokensIndex = this.addToken(matchedTokens, matchedTokensIndex, newToken);\n          } else {\n            groups[group].push(newToken);\n          }\n        }\n        text = this.chopInput(text, imageLength);\n        offset = offset + imageLength;\n        // TODO: with newlines the column may be assigned twice\n        column = this.computeNewColumn(column, imageLength);\n        if (trackLines === true && currConfig.canLineTerminator === true) {\n          let numOfLTsInMatch = 0;\n          let foundTerminator;\n          let lastLTEndOffset;\n          lineTerminatorPattern.lastIndex = 0;\n          do {\n            foundTerminator = lineTerminatorPattern.test(matchedImage);\n            if (foundTerminator === true) {\n              lastLTEndOffset = lineTerminatorPattern.lastIndex - 1;\n              numOfLTsInMatch++;\n            }\n          } while (foundTerminator === true);\n          if (numOfLTsInMatch !== 0) {\n            line = line + numOfLTsInMatch;\n            column = imageLength - lastLTEndOffset;\n            this.updateTokenEndLineColumnLocation(newToken, group, lastLTEndOffset, numOfLTsInMatch, line, column, imageLength);\n          }\n        }\n        // will be NOOP if no modes present\n        this.handleModes(currConfig, pop_mode, push_mode, newToken);\n      } else {\n        // error recovery, drop characters until we identify a valid token's start point\n        const errorStartOffset = offset;\n        const errorLine = line;\n        const errorColumn = column;\n        let foundResyncPoint = recoveryEnabled === false;\n        while (foundResyncPoint === false && offset < orgLength) {\n          // Identity Func (when sticky flag is enabled)\n          text = this.chopInput(text, 1);\n          offset++;\n          for (j = 0; j < currModePatternsLength; j++) {\n            const currConfig = patternIdxToConfig[j];\n            const currPattern = currConfig.pattern;\n            // manually in-lined because > 600 chars won't be in-lined in V8\n            const singleCharCode = currConfig.short;\n            if (singleCharCode !== false) {\n              if (orgText.charCodeAt(offset) === singleCharCode) {\n                // single character string\n                foundResyncPoint = true;\n              }\n            } else if (currConfig.isCustom === true) {\n              foundResyncPoint = currPattern.exec(orgText, offset, matchedTokens, groups) !== null;\n            } else {\n              this.updateLastIndex(currPattern, offset);\n              foundResyncPoint = currPattern.exec(text) !== null;\n            }\n            if (foundResyncPoint === true) {\n              break;\n            }\n          }\n        }\n        errLength = offset - errorStartOffset;\n        column = this.computeNewColumn(column, errLength);\n        // at this point we either re-synced or reached the end of the input text\n        msg = this.config.errorMessageProvider.buildUnexpectedCharactersMessage(orgText, errorStartOffset, errLength, errorLine, errorColumn);\n        errors.push({\n          offset: errorStartOffset,\n          line: errorLine,\n          column: errorColumn,\n          length: errLength,\n          message: msg\n        });\n        if (recoveryEnabled === false) {\n          break;\n        }\n      }\n    }\n    // if we do have custom patterns which push directly into the\n    // TODO: custom tokens should not push directly??\n    if (!this.hasCustom) {\n      // if we guessed a too large size for the tokens array this will shrink it to the right size.\n      matchedTokens.length = matchedTokensIndex;\n    }\n    return {\n      tokens: matchedTokens,\n      groups: groups,\n      errors: errors\n    };\n  }\n  handleModes(config, pop_mode, push_mode, newToken) {\n    if (config.pop === true) {\n      // need to save the PUSH_MODE property as if the mode is popped\n      // patternIdxToPopMode is updated to reflect the new mode after popping the stack\n      const pushMode = config.push;\n      pop_mode(newToken);\n      if (pushMode !== undefined) {\n        push_mode.call(this, pushMode);\n      }\n    } else if (config.push !== undefined) {\n      push_mode.call(this, config.push);\n    }\n  }\n  chopInput(text, length) {\n    return text.substring(length);\n  }\n  updateLastIndex(regExp, newLastIndex) {\n    regExp.lastIndex = newLastIndex;\n  }\n  // TODO: decrease this under 600 characters? inspect stripping comments option in TSC compiler\n  updateTokenEndLineColumnLocation(newToken, group, lastLTIdx, numOfLTsInMatch, line, column, imageLength) {\n    let lastCharIsLT, fixForEndingInLT;\n    if (group !== undefined) {\n      // a none skipped multi line Token, need to update endLine/endColumn\n      lastCharIsLT = lastLTIdx === imageLength - 1;\n      fixForEndingInLT = lastCharIsLT ? -1 : 0;\n      if (!(numOfLTsInMatch === 1 && lastCharIsLT === true)) {\n        // if a token ends in a LT that last LT only affects the line numbering of following Tokens\n        newToken.endLine = line + fixForEndingInLT;\n        // the last LT in a token does not affect the endColumn either as the [columnStart ... columnEnd)\n        // inclusive to exclusive range.\n        newToken.endColumn = column - 1 + -fixForEndingInLT;\n      }\n      // else single LT in the last character of a token, no need to modify the endLine/EndColumn\n    }\n  }\n  computeNewColumn(oldColumn, imageLength) {\n    return oldColumn + imageLength;\n  }\n  createOffsetOnlyToken(image, startOffset, tokenTypeIdx, tokenType) {\n    return {\n      image,\n      startOffset,\n      tokenTypeIdx,\n      tokenType\n    };\n  }\n  createStartOnlyToken(image, startOffset, tokenTypeIdx, tokenType, startLine, startColumn) {\n    return {\n      image,\n      startOffset,\n      startLine,\n      startColumn,\n      tokenTypeIdx,\n      tokenType\n    };\n  }\n  createFullToken(image, startOffset, tokenTypeIdx, tokenType, startLine, startColumn, imageLength) {\n    return {\n      image,\n      startOffset,\n      endOffset: startOffset + imageLength - 1,\n      startLine,\n      endLine: startLine,\n      startColumn,\n      endColumn: startColumn + imageLength - 1,\n      tokenTypeIdx,\n      tokenType\n    };\n  }\n  addTokenUsingPush(tokenVector, index, tokenToAdd) {\n    tokenVector.push(tokenToAdd);\n    return index;\n  }\n  addTokenUsingMemberAccess(tokenVector, index, tokenToAdd) {\n    tokenVector[index] = tokenToAdd;\n    index++;\n    return index;\n  }\n  handlePayloadNoCustom(token, payload) {}\n  handlePayloadWithCustom(token, payload) {\n    if (payload !== null) {\n      token.payload = payload;\n    }\n  }\n  matchWithTest(pattern, text, offset) {\n    const found = pattern.test(text);\n    if (found === true) {\n      return text.substring(offset, pattern.lastIndex);\n    }\n    return null;\n  }\n  matchWithExec(pattern, text) {\n    const regExpArray = pattern.exec(text);\n    return regExpArray !== null ? regExpArray[0] : null;\n  }\n}\nLexer.SKIPPED = \"This marks a skipped Token pattern, this means each token identified by it will\" + \"be consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.\";\nLexer.NA = /NOT_APPLICABLE/;", "map": {"version": 3, "names": ["analyzeTokenTypes", "charCodeToOptimizedIndex", "cloneEmptyGroups", "DEFAULT_MODE", "LineTerminatorOptimizedTester", "performRuntimeChecks", "performWarningRuntimeChecks", "SUPPORT_STICKY", "validatePatterns", "assign", "clone", "for<PERSON>ach", "identity", "isArray", "isEmpty", "isUndefined", "keys", "last", "map", "noop", "reduce", "reject", "PRINT_WARNING", "timer", "toFastProperties", "augmentTokenTypes", "defaultLexerErrorProvider", "clearRegExpParserCache", "LexerDefinitionErrorType", "DEFAULT_LEXER_CONFIG", "deferDefinitionErrorsHandling", "positionTracking", "lineTerminatorsPattern", "lineTerminatorCharacters", "ensureOptimizations", "safeMode", "errorMessageProvider", "traceInitPerf", "skipValidations", "recoveryEnabled", "Object", "freeze", "<PERSON><PERSON>", "constructor", "lexerDefinition", "config", "lexerDefinitionErrors", "lexerDefinitionWarning", "patternIdxToConfig", "charCodeToPatternIdxToConfig", "modes", "emptyGroups", "trackStartLines", "trackEndLines", "hasCustom", "canModeBeOptimized", "TRACE_INIT", "phaseDesc", "phaseImpl", "traceInitIndent", "indent", "Array", "join", "traceInitMaxIdent", "console", "log", "time", "value", "traceMethod", "warn", "Error", "traceInitVal", "Infinity", "actualDefinition", "hasOnlySingleMode", "test", "defaultMode", "concat", "currModeValue", "currModeName", "currTokType", "allModeNames", "currModDef", "currModName", "push", "currAnalyzeResult", "tracer", "canBeOptimized", "allErrMessages", "error", "message", "allErrMessagesString", "warningDescriptor", "chopInput", "match", "matchWithTest", "updateLastIndex", "matchWithExec", "handleModes", "computeNewColumn", "updateTokenEndLineColumnLocation", "createTokenInstance", "createFullToken", "createStartOnlyToken", "createOffsetOnlyToken", "addToken", "addTokenUsingPush", "handlePayload", "handlePayloadWithCustom", "addTokenUsingMemberAccess", "handlePayloadNoCustom", "unOptimizedModes", "cannotBeOptimized", "modeName", "tokenize", "text", "initialMode", "tokenizeInternal", "i", "j", "k", "matchAltImage", "longerAlt", "matchedImage", "payload", "altPayload", "imageLength", "group", "tokType", "newToken", "err<PERSON><PERSON><PERSON>", "droppedChar", "msg", "orgText", "orgLength", "length", "offset", "matchedTokensIndex", "guessedNumberOfTokens", "Math", "floor", "matchedTokens", "errors", "line", "undefined", "column", "groups", "trackLines", "lineTerminatorPattern", "currModePatternsLength", "currCharCodeToPatternIdxToConfig", "modeStack", "emptyArray", "getPossiblePatterns", "getPossiblePatternsSlow", "getPossiblePatternsOptimized", "charCode", "optimizedCharIdx", "possiblePatterns", "pop_mode", "popToken", "tokenType", "PUSH_MODE", "buildUnableToPopLexerModeMessage", "startOffset", "startLine", "startColumn", "image", "pop", "newMode", "modeCanBeOptimized", "push_mode", "call", "currConfig", "nextCharCode", "charCodeAt", "chosenPatternIdxToConfig", "chosenPatterns<PERSON>ength", "currPattern", "pattern", "singleCharCode", "short", "isCustom", "exec", "longerAltLength", "longerAltConfig", "longerAltPattern", "tokenTypeIdx", "canLineTerminator", "numOfLTsInMatch", "foundTerminator", "lastLTEndOffset", "lastIndex", "errorStartOffset", "errorLine", "errorColumn", "foundResyncPoint", "buildUnexpectedCharactersMessage", "tokens", "pushMode", "substring", "regExp", "newLastIndex", "lastLTIdx", "lastCharIsLT", "fixForEndingInLT", "endLine", "endColumn", "oldColumn", "endOffset", "tokenVector", "index", "tokenToAdd", "token", "found", "regExpArray", "SKIPPED", "NA"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/scan/lexer_public.ts"], "sourcesContent": ["import {\n  analyzeTokenTypes,\n  charCodeToOptimizedIndex,\n  cloneEmptyGroups,\n  DEFAULT_MODE,\n  IAnalyzeResult,\n  IPatternConfig,\n  LineTerminatorOptimizedTester,\n  performRuntimeChecks,\n  performWarningRuntimeChecks,\n  SUPPORT_STICKY,\n  validatePatterns,\n} from \"./lexer.js\";\nimport {\n  assign,\n  clone,\n  forEach,\n  identity,\n  isArray,\n  isEmpty,\n  isUndefined,\n  keys,\n  last,\n  map,\n  noop,\n  reduce,\n  reject,\n} from \"lodash-es\";\nimport { PRINT_WARNING, timer, toFastProperties } from \"@chevrotain/utils\";\nimport { augmentTokenTypes } from \"./tokens.js\";\nimport {\n  CustomPatternMatcherFunc,\n  CustomPatternMatcherReturn,\n  ILexerConfig,\n  ILexerDefinitionError,\n  ILexingError,\n  IMultiModeLexerDefinition,\n  IToken,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { defaultLexerErrorProvider } from \"./lexer_errors_public.js\";\nimport { clearRegExpParserCache } from \"./reg_exp_parser.js\";\n\nexport interface ILexingResult {\n  tokens: IToken[];\n  groups: { [groupName: string]: IToken[] };\n  errors: ILexingError[];\n}\n\nexport enum LexerDefinitionErrorType {\n  MISSING_PATTERN,\n  INVALID_PATTERN,\n  EOI_ANCHOR_FOUND,\n  UNSUPPORTED_FLAGS_FOUND,\n  DUPLICATE_PATTERNS_FOUND,\n  INVALID_GROUP_TYPE_FOUND,\n  PUSH_MODE_DOES_NOT_EXIST,\n  MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE,\n  MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY,\n  MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST,\n  LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED,\n  SOI_ANCHOR_FOUND,\n  EMPTY_MATCH_PATTERN,\n  NO_LINE_BREAKS_FLAGS,\n  UNREACHABLE_PATTERN,\n  IDENTIFY_TERMINATOR,\n  CUSTOM_LINE_BREAK,\n  MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE,\n}\n\nexport interface IRegExpExec {\n  exec: CustomPatternMatcherFunc;\n}\n\nconst DEFAULT_LEXER_CONFIG: Required<ILexerConfig> = {\n  deferDefinitionErrorsHandling: false,\n  positionTracking: \"full\",\n  lineTerminatorsPattern: /\\n|\\r\\n?/g,\n  lineTerminatorCharacters: [\"\\n\", \"\\r\"],\n  ensureOptimizations: false,\n  safeMode: false,\n  errorMessageProvider: defaultLexerErrorProvider,\n  traceInitPerf: false,\n  skipValidations: false,\n  recoveryEnabled: true,\n};\n\nObject.freeze(DEFAULT_LEXER_CONFIG);\n\nexport class Lexer {\n  public static SKIPPED =\n    \"This marks a skipped Token pattern, this means each token identified by it will\" +\n    \"be consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.\";\n\n  public static NA = /NOT_APPLICABLE/;\n  public lexerDefinitionErrors: ILexerDefinitionError[] = [];\n  public lexerDefinitionWarning: ILexerDefinitionError[] = [];\n\n  protected patternIdxToConfig: Record<string, IPatternConfig[]> = {};\n  protected charCodeToPatternIdxToConfig: {\n    [modeName: string]: { [charCode: number]: IPatternConfig[] };\n  } = {};\n\n  protected modes: string[] = [];\n  protected defaultMode!: string;\n  protected emptyGroups: { [groupName: string]: IToken } = {};\n\n  private config: Required<ILexerConfig>;\n  private trackStartLines: boolean = true;\n  private trackEndLines: boolean = true;\n  private hasCustom: boolean = false;\n  private canModeBeOptimized: Record<string, boolean> = {};\n\n  private traceInitPerf!: boolean | number;\n  private traceInitMaxIdent!: number;\n  private traceInitIndent: number;\n\n  constructor(\n    protected lexerDefinition: TokenType[] | IMultiModeLexerDefinition,\n    config: ILexerConfig = DEFAULT_LEXER_CONFIG,\n  ) {\n    if (typeof config === \"boolean\") {\n      throw Error(\n        \"The second argument to the Lexer constructor is now an ILexerConfig Object.\\n\" +\n          \"a boolean 2nd argument is no longer supported\",\n      );\n    }\n\n    // todo: defaults func?\n    this.config = assign({}, DEFAULT_LEXER_CONFIG, config) as any;\n\n    const traceInitVal = this.config.traceInitPerf;\n    if (traceInitVal === true) {\n      this.traceInitMaxIdent = Infinity;\n      this.traceInitPerf = true;\n    } else if (typeof traceInitVal === \"number\") {\n      this.traceInitMaxIdent = traceInitVal;\n      this.traceInitPerf = true;\n    }\n    this.traceInitIndent = -1;\n\n    this.TRACE_INIT(\"Lexer Constructor\", () => {\n      let actualDefinition!: IMultiModeLexerDefinition;\n      let hasOnlySingleMode = true;\n      this.TRACE_INIT(\"Lexer Config handling\", () => {\n        if (\n          this.config.lineTerminatorsPattern ===\n          DEFAULT_LEXER_CONFIG.lineTerminatorsPattern\n        ) {\n          // optimized built-in implementation for the defaults definition of lineTerminators\n          this.config.lineTerminatorsPattern = LineTerminatorOptimizedTester;\n        } else {\n          if (\n            this.config.lineTerminatorCharacters ===\n            DEFAULT_LEXER_CONFIG.lineTerminatorCharacters\n          ) {\n            throw Error(\n              \"Error: Missing <lineTerminatorCharacters> property on the Lexer config.\\n\" +\n                \"\\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS\",\n            );\n          }\n        }\n\n        if (config.safeMode && config.ensureOptimizations) {\n          throw Error(\n            '\"safeMode\" and \"ensureOptimizations\" flags are mutually exclusive.',\n          );\n        }\n\n        this.trackStartLines = /full|onlyStart/i.test(\n          this.config.positionTracking,\n        );\n        this.trackEndLines = /full/i.test(this.config.positionTracking);\n\n        // Convert SingleModeLexerDefinition into a IMultiModeLexerDefinition.\n        if (isArray(lexerDefinition)) {\n          actualDefinition = {\n            modes: { defaultMode: clone(lexerDefinition) },\n            defaultMode: DEFAULT_MODE,\n          };\n        } else {\n          // no conversion needed, input should already be a IMultiModeLexerDefinition\n          hasOnlySingleMode = false;\n          actualDefinition = clone(<IMultiModeLexerDefinition>lexerDefinition);\n        }\n      });\n\n      if (this.config.skipValidations === false) {\n        this.TRACE_INIT(\"performRuntimeChecks\", () => {\n          this.lexerDefinitionErrors = this.lexerDefinitionErrors.concat(\n            performRuntimeChecks(\n              actualDefinition,\n              this.trackStartLines,\n              this.config.lineTerminatorCharacters,\n            ),\n          );\n        });\n\n        this.TRACE_INIT(\"performWarningRuntimeChecks\", () => {\n          this.lexerDefinitionWarning = this.lexerDefinitionWarning.concat(\n            performWarningRuntimeChecks(\n              actualDefinition,\n              this.trackStartLines,\n              this.config.lineTerminatorCharacters,\n            ),\n          );\n        });\n      }\n\n      // for extra robustness to avoid throwing an none informative error message\n      actualDefinition.modes = actualDefinition.modes\n        ? actualDefinition.modes\n        : {};\n\n      // an error of undefined TokenTypes will be detected in \"performRuntimeChecks\" above.\n      // this transformation is to increase robustness in the case of partially invalid lexer definition.\n      forEach(actualDefinition.modes, (currModeValue, currModeName) => {\n        actualDefinition.modes[currModeName] = reject<TokenType>(\n          currModeValue,\n          (currTokType) => isUndefined(currTokType),\n        );\n      });\n\n      const allModeNames = keys(actualDefinition.modes);\n\n      forEach(\n        actualDefinition.modes,\n        (currModDef: TokenType[], currModName) => {\n          this.TRACE_INIT(`Mode: <${currModName}> processing`, () => {\n            this.modes.push(currModName);\n\n            if (this.config.skipValidations === false) {\n              this.TRACE_INIT(`validatePatterns`, () => {\n                this.lexerDefinitionErrors = this.lexerDefinitionErrors.concat(\n                  validatePatterns(currModDef, allModeNames),\n                );\n              });\n            }\n\n            // If definition errors were encountered, the analysis phase may fail unexpectedly/\n            // Considering a lexer with definition errors may never be used, there is no point\n            // to performing the analysis anyhow...\n            if (isEmpty(this.lexerDefinitionErrors)) {\n              augmentTokenTypes(currModDef);\n\n              let currAnalyzeResult!: IAnalyzeResult;\n              this.TRACE_INIT(`analyzeTokenTypes`, () => {\n                currAnalyzeResult = analyzeTokenTypes(currModDef, {\n                  lineTerminatorCharacters:\n                    this.config.lineTerminatorCharacters,\n                  positionTracking: config.positionTracking,\n                  ensureOptimizations: config.ensureOptimizations,\n                  safeMode: config.safeMode,\n                  tracer: this.TRACE_INIT,\n                });\n              });\n\n              this.patternIdxToConfig[currModName] =\n                currAnalyzeResult.patternIdxToConfig;\n\n              this.charCodeToPatternIdxToConfig[currModName] =\n                currAnalyzeResult.charCodeToPatternIdxToConfig;\n\n              this.emptyGroups = assign(\n                {},\n                this.emptyGroups,\n                currAnalyzeResult.emptyGroups,\n              ) as any;\n\n              this.hasCustom = currAnalyzeResult.hasCustom || this.hasCustom;\n\n              this.canModeBeOptimized[currModName] =\n                currAnalyzeResult.canBeOptimized;\n            }\n          });\n        },\n      );\n\n      this.defaultMode = actualDefinition.defaultMode;\n\n      if (\n        !isEmpty(this.lexerDefinitionErrors) &&\n        !this.config.deferDefinitionErrorsHandling\n      ) {\n        const allErrMessages = map(this.lexerDefinitionErrors, (error) => {\n          return error.message;\n        });\n        const allErrMessagesString = allErrMessages.join(\n          \"-----------------------\\n\",\n        );\n        throw new Error(\n          \"Errors detected in definition of Lexer:\\n\" + allErrMessagesString,\n        );\n      }\n\n      // Only print warning if there are no errors, This will avoid pl\n      forEach(this.lexerDefinitionWarning, (warningDescriptor) => {\n        PRINT_WARNING(warningDescriptor.message);\n      });\n\n      this.TRACE_INIT(\"Choosing sub-methods implementations\", () => {\n        // Choose the relevant internal implementations for this specific parser.\n        // These implementations should be in-lined by the JavaScript engine\n        // to provide optimal performance in each scenario.\n        if (SUPPORT_STICKY) {\n          this.chopInput = <any>identity;\n          this.match = this.matchWithTest;\n        } else {\n          this.updateLastIndex = noop;\n          this.match = this.matchWithExec;\n        }\n\n        if (hasOnlySingleMode) {\n          this.handleModes = noop;\n        }\n\n        if (this.trackStartLines === false) {\n          this.computeNewColumn = identity;\n        }\n\n        if (this.trackEndLines === false) {\n          this.updateTokenEndLineColumnLocation = noop;\n        }\n\n        if (/full/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createFullToken;\n        } else if (/onlyStart/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createStartOnlyToken;\n        } else if (/onlyOffset/i.test(this.config.positionTracking)) {\n          this.createTokenInstance = this.createOffsetOnlyToken;\n        } else {\n          throw Error(\n            `Invalid <positionTracking> config option: \"${this.config.positionTracking}\"`,\n          );\n        }\n\n        if (this.hasCustom) {\n          this.addToken = this.addTokenUsingPush;\n          this.handlePayload = this.handlePayloadWithCustom;\n        } else {\n          this.addToken = this.addTokenUsingMemberAccess;\n          this.handlePayload = this.handlePayloadNoCustom;\n        }\n      });\n\n      this.TRACE_INIT(\"Failed Optimization Warnings\", () => {\n        const unOptimizedModes = reduce(\n          this.canModeBeOptimized,\n          (cannotBeOptimized, canBeOptimized, modeName) => {\n            if (canBeOptimized === false) {\n              cannotBeOptimized.push(modeName);\n            }\n            return cannotBeOptimized;\n          },\n          [] as string[],\n        );\n\n        if (config.ensureOptimizations && !isEmpty(unOptimizedModes)) {\n          throw Error(\n            `Lexer Modes: < ${unOptimizedModes.join(\n              \", \",\n            )} > cannot be optimized.\\n` +\n              '\\t Disable the \"ensureOptimizations\" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.\\n' +\n              \"\\t Or inspect the console log for details on how to resolve these issues.\",\n          );\n        }\n      });\n\n      this.TRACE_INIT(\"clearRegExpParserCache\", () => {\n        clearRegExpParserCache();\n      });\n\n      this.TRACE_INIT(\"toFastProperties\", () => {\n        toFastProperties(this);\n      });\n    });\n  }\n\n  public tokenize(\n    text: string,\n    initialMode: string = this.defaultMode,\n  ): ILexingResult {\n    if (!isEmpty(this.lexerDefinitionErrors)) {\n      const allErrMessages = map(this.lexerDefinitionErrors, (error) => {\n        return error.message;\n      });\n      const allErrMessagesString = allErrMessages.join(\n        \"-----------------------\\n\",\n      );\n      throw new Error(\n        \"Unable to Tokenize because Errors detected in definition of Lexer:\\n\" +\n          allErrMessagesString,\n      );\n    }\n\n    return this.tokenizeInternal(text, initialMode);\n  }\n\n  // There is quite a bit of duplication between this and \"tokenizeInternalLazy\"\n  // This is intentional due to performance considerations.\n  // this method also used quite a bit of `!` none null assertions because it is too optimized\n  // for `tsc` to always understand it is \"safe\"\n  private tokenizeInternal(text: string, initialMode: string): ILexingResult {\n    let i,\n      j,\n      k,\n      matchAltImage,\n      longerAlt,\n      matchedImage: string | null,\n      payload,\n      altPayload,\n      imageLength,\n      group,\n      tokType,\n      newToken: IToken,\n      errLength,\n      droppedChar,\n      msg,\n      match;\n    const orgText = text;\n    const orgLength = orgText.length;\n    let offset = 0;\n    let matchedTokensIndex = 0;\n    // initializing the tokensArray to the \"guessed\" size.\n    // guessing too little will still reduce the number of array re-sizes on pushes.\n    // guessing too large (Tested by guessing x4 too large) may cost a bit more of memory\n    // but would still have a faster runtime by avoiding (All but one) array resizing.\n    const guessedNumberOfTokens = this.hasCustom\n      ? 0 // will break custom token pattern APIs the matchedTokens array will contain undefined elements.\n      : Math.floor(text.length / 10);\n    const matchedTokens = new Array(guessedNumberOfTokens);\n    const errors: ILexingError[] = [];\n    let line = this.trackStartLines ? 1 : undefined;\n    let column = this.trackStartLines ? 1 : undefined;\n    const groups: any = cloneEmptyGroups(this.emptyGroups);\n    const trackLines = this.trackStartLines;\n    const lineTerminatorPattern = this.config.lineTerminatorsPattern;\n\n    let currModePatternsLength = 0;\n    let patternIdxToConfig: IPatternConfig[] = [];\n    let currCharCodeToPatternIdxToConfig: {\n      [charCode: number]: IPatternConfig[];\n    } = [];\n\n    const modeStack: string[] = [];\n\n    const emptyArray: IPatternConfig[] = [];\n    Object.freeze(emptyArray);\n    let getPossiblePatterns!: (charCode: number) => IPatternConfig[];\n\n    function getPossiblePatternsSlow() {\n      return patternIdxToConfig;\n    }\n\n    function getPossiblePatternsOptimized(charCode: number): IPatternConfig[] {\n      const optimizedCharIdx = charCodeToOptimizedIndex(charCode);\n      const possiblePatterns =\n        currCharCodeToPatternIdxToConfig[optimizedCharIdx];\n      if (possiblePatterns === undefined) {\n        return emptyArray;\n      } else {\n        return possiblePatterns;\n      }\n    }\n\n    const pop_mode = (popToken: IToken) => {\n      // TODO: perhaps avoid this error in the edge case there is no more input?\n      if (\n        modeStack.length === 1 &&\n        // if we have both a POP_MODE and a PUSH_MODE this is in-fact a \"transition\"\n        // So no error should occur.\n        popToken.tokenType.PUSH_MODE === undefined\n      ) {\n        // if we try to pop the last mode there lexer will no longer have ANY mode.\n        // thus the pop is ignored, an error will be created and the lexer will continue parsing in the previous mode.\n        const msg =\n          this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(\n            popToken,\n          );\n\n        errors.push({\n          offset: popToken.startOffset,\n          line: popToken.startLine,\n          column: popToken.startColumn,\n          length: popToken.image.length,\n          message: msg,\n        });\n      } else {\n        modeStack.pop();\n        const newMode = last(modeStack)!;\n        patternIdxToConfig = this.patternIdxToConfig[newMode];\n        currCharCodeToPatternIdxToConfig =\n          this.charCodeToPatternIdxToConfig[newMode];\n        currModePatternsLength = patternIdxToConfig.length;\n        const modeCanBeOptimized =\n          this.canModeBeOptimized[newMode] && this.config.safeMode === false;\n\n        if (currCharCodeToPatternIdxToConfig && modeCanBeOptimized) {\n          getPossiblePatterns = getPossiblePatternsOptimized;\n        } else {\n          getPossiblePatterns = getPossiblePatternsSlow;\n        }\n      }\n    };\n\n    function push_mode(this: Lexer, newMode: string) {\n      modeStack.push(newMode);\n      currCharCodeToPatternIdxToConfig =\n        this.charCodeToPatternIdxToConfig[newMode];\n\n      patternIdxToConfig = this.patternIdxToConfig[newMode];\n      currModePatternsLength = patternIdxToConfig.length;\n\n      currModePatternsLength = patternIdxToConfig.length;\n      const modeCanBeOptimized =\n        this.canModeBeOptimized[newMode] && this.config.safeMode === false;\n\n      if (currCharCodeToPatternIdxToConfig && modeCanBeOptimized) {\n        getPossiblePatterns = getPossiblePatternsOptimized;\n      } else {\n        getPossiblePatterns = getPossiblePatternsSlow;\n      }\n    }\n\n    // this pattern seems to avoid a V8 de-optimization, although that de-optimization does not\n    // seem to matter performance wise.\n    push_mode.call(this, initialMode);\n\n    let currConfig!: IPatternConfig;\n\n    const recoveryEnabled = this.config.recoveryEnabled;\n\n    while (offset < orgLength) {\n      matchedImage = null;\n\n      const nextCharCode = orgText.charCodeAt(offset);\n      const chosenPatternIdxToConfig = getPossiblePatterns(nextCharCode);\n      const chosenPatternsLength = chosenPatternIdxToConfig.length;\n\n      for (i = 0; i < chosenPatternsLength; i++) {\n        currConfig = chosenPatternIdxToConfig[i];\n        const currPattern = currConfig.pattern;\n        payload = null;\n\n        // manually in-lined because > 600 chars won't be in-lined in V8\n        const singleCharCode = currConfig.short;\n        if (singleCharCode !== false) {\n          if (nextCharCode === singleCharCode) {\n            // single character string\n            matchedImage = currPattern as string;\n          }\n        } else if (currConfig.isCustom === true) {\n          match = (currPattern as IRegExpExec).exec(\n            orgText,\n            offset,\n            matchedTokens,\n            groups,\n          );\n          if (match !== null) {\n            matchedImage = match[0];\n            if ((match as CustomPatternMatcherReturn).payload !== undefined) {\n              payload = (match as CustomPatternMatcherReturn).payload;\n            }\n          } else {\n            matchedImage = null;\n          }\n        } else {\n          this.updateLastIndex(currPattern as RegExp, offset);\n          matchedImage = this.match(currPattern as RegExp, text, offset);\n        }\n\n        if (matchedImage !== null) {\n          // even though this pattern matched we must try a another longer alternative.\n          // this can be used to prioritize keywords over identifiers\n          longerAlt = currConfig.longerAlt;\n          if (longerAlt !== undefined) {\n            // TODO: micro optimize, avoid extra prop access\n            // by saving/linking longerAlt on the original config?\n            const longerAltLength = longerAlt.length;\n            for (k = 0; k < longerAltLength; k++) {\n              const longerAltConfig = patternIdxToConfig[longerAlt[k]];\n              const longerAltPattern = longerAltConfig.pattern;\n              altPayload = null;\n\n              // single Char can never be a longer alt so no need to test it.\n              // manually in-lined because > 600 chars won't be in-lined in V8\n              if (longerAltConfig.isCustom === true) {\n                match = (longerAltPattern as IRegExpExec).exec(\n                  orgText,\n                  offset,\n                  matchedTokens,\n                  groups,\n                );\n                if (match !== null) {\n                  matchAltImage = match[0];\n                  if (\n                    (match as CustomPatternMatcherReturn).payload !== undefined\n                  ) {\n                    altPayload = (match as CustomPatternMatcherReturn).payload;\n                  }\n                } else {\n                  matchAltImage = null;\n                }\n              } else {\n                this.updateLastIndex(longerAltPattern as RegExp, offset);\n                matchAltImage = this.match(\n                  longerAltPattern as RegExp,\n                  text,\n                  offset,\n                );\n              }\n\n              if (matchAltImage && matchAltImage.length > matchedImage.length) {\n                matchedImage = matchAltImage;\n                payload = altPayload;\n                currConfig = longerAltConfig;\n                // Exit the loop early after matching one of the longer alternatives\n                // The first matched alternative takes precedence\n                break;\n              }\n            }\n          }\n          break;\n        }\n      }\n\n      // successful match\n      if (matchedImage !== null) {\n        imageLength = matchedImage.length;\n        group = currConfig.group;\n        if (group !== undefined) {\n          tokType = currConfig.tokenTypeIdx;\n          // TODO: \"offset + imageLength\" and the new column may be computed twice in case of \"full\" location information inside\n          // createFullToken method\n          newToken = this.createTokenInstance(\n            matchedImage,\n            offset,\n            tokType,\n            currConfig.tokenType,\n            line,\n            column,\n            imageLength,\n          );\n\n          this.handlePayload(newToken, payload);\n\n          // TODO: optimize NOOP in case there are no special groups?\n          if (group === false) {\n            matchedTokensIndex = this.addToken(\n              matchedTokens,\n              matchedTokensIndex,\n              newToken,\n            );\n          } else {\n            groups[group].push(newToken);\n          }\n        }\n        text = this.chopInput(text, imageLength);\n        offset = offset + imageLength;\n\n        // TODO: with newlines the column may be assigned twice\n        column = this.computeNewColumn(column!, imageLength);\n\n        if (trackLines === true && currConfig.canLineTerminator === true) {\n          let numOfLTsInMatch = 0;\n          let foundTerminator;\n          let lastLTEndOffset: number;\n          lineTerminatorPattern.lastIndex = 0;\n          do {\n            foundTerminator = lineTerminatorPattern.test(matchedImage);\n            if (foundTerminator === true) {\n              lastLTEndOffset = lineTerminatorPattern.lastIndex - 1;\n              numOfLTsInMatch++;\n            }\n          } while (foundTerminator === true);\n\n          if (numOfLTsInMatch !== 0) {\n            line = line! + numOfLTsInMatch;\n            column = imageLength - lastLTEndOffset!;\n            this.updateTokenEndLineColumnLocation(\n              newToken!,\n              group!,\n              lastLTEndOffset!,\n              numOfLTsInMatch,\n              line,\n              column,\n              imageLength,\n            );\n          }\n        }\n        // will be NOOP if no modes present\n        this.handleModes(currConfig, pop_mode, push_mode, newToken!);\n      } else {\n        // error recovery, drop characters until we identify a valid token's start point\n        const errorStartOffset = offset;\n        const errorLine = line;\n        const errorColumn = column;\n        let foundResyncPoint = recoveryEnabled === false;\n\n        while (foundResyncPoint === false && offset < orgLength) {\n          // Identity Func (when sticky flag is enabled)\n          text = this.chopInput(text, 1);\n          offset++;\n          for (j = 0; j < currModePatternsLength; j++) {\n            const currConfig = patternIdxToConfig[j];\n            const currPattern = currConfig.pattern;\n\n            // manually in-lined because > 600 chars won't be in-lined in V8\n            const singleCharCode = currConfig.short;\n            if (singleCharCode !== false) {\n              if (orgText.charCodeAt(offset) === singleCharCode) {\n                // single character string\n                foundResyncPoint = true;\n              }\n            } else if (currConfig.isCustom === true) {\n              foundResyncPoint =\n                (currPattern as IRegExpExec).exec(\n                  orgText,\n                  offset,\n                  matchedTokens,\n                  groups,\n                ) !== null;\n            } else {\n              this.updateLastIndex(currPattern as RegExp, offset);\n              foundResyncPoint = (currPattern as RegExp).exec(text) !== null;\n            }\n\n            if (foundResyncPoint === true) {\n              break;\n            }\n          }\n        }\n\n        errLength = offset - errorStartOffset;\n        column = this.computeNewColumn(column!, errLength);\n        // at this point we either re-synced or reached the end of the input text\n        msg = this.config.errorMessageProvider.buildUnexpectedCharactersMessage(\n          orgText,\n          errorStartOffset,\n          errLength,\n          errorLine,\n          errorColumn,\n        );\n        errors.push({\n          offset: errorStartOffset,\n          line: errorLine,\n          column: errorColumn,\n          length: errLength,\n          message: msg,\n        });\n\n        if (recoveryEnabled === false) {\n          break;\n        }\n      }\n    }\n\n    // if we do have custom patterns which push directly into the\n    // TODO: custom tokens should not push directly??\n    if (!this.hasCustom) {\n      // if we guessed a too large size for the tokens array this will shrink it to the right size.\n      matchedTokens.length = matchedTokensIndex;\n    }\n\n    return {\n      tokens: matchedTokens,\n      groups: groups,\n      errors: errors,\n    };\n  }\n\n  private handleModes(\n    config: IPatternConfig,\n    pop_mode: (tok: IToken) => void,\n    push_mode: (this: Lexer, pushMode: string) => void,\n    newToken: IToken,\n  ) {\n    if (config.pop === true) {\n      // need to save the PUSH_MODE property as if the mode is popped\n      // patternIdxToPopMode is updated to reflect the new mode after popping the stack\n      const pushMode = config.push;\n      pop_mode(newToken);\n      if (pushMode !== undefined) {\n        push_mode.call(this, pushMode);\n      }\n    } else if (config.push !== undefined) {\n      push_mode.call(this, config.push);\n    }\n  }\n\n  private chopInput(text: string, length: number): string {\n    return text.substring(length);\n  }\n\n  private updateLastIndex(regExp: RegExp, newLastIndex: number): void {\n    regExp.lastIndex = newLastIndex;\n  }\n\n  // TODO: decrease this under 600 characters? inspect stripping comments option in TSC compiler\n  private updateTokenEndLineColumnLocation(\n    newToken: IToken,\n    group: string | false,\n    lastLTIdx: number,\n    numOfLTsInMatch: number,\n    line: number,\n    column: number,\n    imageLength: number,\n  ): void {\n    let lastCharIsLT, fixForEndingInLT;\n    if (group !== undefined) {\n      // a none skipped multi line Token, need to update endLine/endColumn\n      lastCharIsLT = lastLTIdx === imageLength - 1;\n      fixForEndingInLT = lastCharIsLT ? -1 : 0;\n      if (!(numOfLTsInMatch === 1 && lastCharIsLT === true)) {\n        // if a token ends in a LT that last LT only affects the line numbering of following Tokens\n        newToken.endLine = line + fixForEndingInLT;\n        // the last LT in a token does not affect the endColumn either as the [columnStart ... columnEnd)\n        // inclusive to exclusive range.\n        newToken.endColumn = column - 1 + -fixForEndingInLT;\n      }\n      // else single LT in the last character of a token, no need to modify the endLine/EndColumn\n    }\n  }\n\n  private computeNewColumn(oldColumn: number, imageLength: number) {\n    return oldColumn + imageLength;\n  }\n\n  // Place holder, will be replaced by the correct variant according to the locationTracking option at runtime.\n  /* istanbul ignore next - place holder */\n  private createTokenInstance!: (...args: any[]) => IToken;\n\n  private createOffsetOnlyToken(\n    image: string,\n    startOffset: number,\n    tokenTypeIdx: number,\n    tokenType: TokenType,\n  ) {\n    return {\n      image,\n      startOffset,\n      tokenTypeIdx,\n      tokenType,\n    };\n  }\n\n  private createStartOnlyToken(\n    image: string,\n    startOffset: number,\n    tokenTypeIdx: number,\n    tokenType: TokenType,\n    startLine: number,\n    startColumn: number,\n  ) {\n    return {\n      image,\n      startOffset,\n      startLine,\n      startColumn,\n      tokenTypeIdx,\n      tokenType,\n    };\n  }\n\n  private createFullToken(\n    image: string,\n    startOffset: number,\n    tokenTypeIdx: number,\n    tokenType: TokenType,\n    startLine: number,\n    startColumn: number,\n    imageLength: number,\n  ): IToken {\n    return {\n      image,\n      startOffset,\n      endOffset: startOffset + imageLength - 1,\n      startLine,\n      endLine: startLine,\n      startColumn,\n      endColumn: startColumn + imageLength - 1,\n      tokenTypeIdx,\n      tokenType,\n    };\n  }\n\n  // Place holder, will be replaced by the correct variant according to the locationTracking option at runtime.\n  /* istanbul ignore next - place holder */\n  private addToken!: (\n    tokenVector: IToken[],\n    index: number,\n    tokenToAdd: IToken,\n  ) => number;\n\n  private addTokenUsingPush(\n    tokenVector: IToken[],\n    index: number,\n    tokenToAdd: IToken,\n  ): number {\n    tokenVector.push(tokenToAdd);\n    return index;\n  }\n\n  private addTokenUsingMemberAccess(\n    tokenVector: IToken[],\n    index: number,\n    tokenToAdd: IToken,\n  ): number {\n    tokenVector[index] = tokenToAdd;\n    index++;\n    return index;\n  }\n\n  // Place holder, will be replaced by the correct variant according to the hasCustom flag option at runtime.\n  private handlePayload: (token: IToken, payload: any) => void;\n\n  private handlePayloadNoCustom(token: IToken, payload: any): void {}\n\n  private handlePayloadWithCustom(token: IToken, payload: any): void {\n    if (payload !== null) {\n      token.payload = payload;\n    }\n  }\n\n  // place holder to be replaced with chosen alternative at runtime\n  private match!: (\n    pattern: RegExp,\n    text: string,\n    offset: number,\n  ) => string | null;\n\n  private matchWithTest(\n    pattern: RegExp,\n    text: string,\n    offset: number,\n  ): string | null {\n    const found = pattern.test(text);\n    if (found === true) {\n      return text.substring(offset, pattern.lastIndex);\n    }\n    return null;\n  }\n\n  private matchWithExec(pattern: RegExp, text: string): string | null {\n    const regExpArray = pattern.exec(text);\n    return regExpArray !== null ? regExpArray[0] : null;\n  }\n\n  // Duplicated from the parser's perf trace trait to allow future extraction\n  // of the lexer to a separate package.\n  TRACE_INIT = <T>(phaseDesc: string, phaseImpl: () => T): T => {\n    // No need to optimize this using NOOP pattern because\n    // It is not called in a hot spot...\n    if (this.traceInitPerf === true) {\n      this.traceInitIndent++;\n      const indent = new Array(this.traceInitIndent + 1).join(\"\\t\");\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        console.log(`${indent}--> <${phaseDesc}>`);\n      }\n      const { time, value } = timer(phaseImpl);\n      /* istanbul ignore next - Difficult to reproduce specific performance behavior (>10ms) in tests */\n      const traceMethod = time > 10 ? console.warn : console.log;\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        traceMethod(`${indent}<-- <${phaseDesc}> time: ${time}ms`);\n      }\n      this.traceInitIndent--;\n      return value;\n    } else {\n      return phaseImpl();\n    }\n  };\n}\n"], "mappings": "AAAA,SACEA,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB,EAChBC,YAAY,EAGZC,6BAA6B,EAC7BC,oBAAoB,EACpBC,2BAA2B,EAC3BC,cAAc,EACdC,gBAAgB,QACX,YAAY;AACnB,SACEC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,MAAM,QACD,WAAW;AAClB,SAASC,aAAa,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC1E,SAASC,iBAAiB,QAAQ,aAAa;AAW/C,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,sBAAsB,QAAQ,qBAAqB;AAQ5D,WAAYC,wBAmBX;AAnBD,WAAYA,wBAAwB;EAClCA,wBAAA,CAAAA,wBAAA,4CAAe;EACfA,wBAAA,CAAAA,wBAAA,4CAAe;EACfA,wBAAA,CAAAA,wBAAA,8CAAgB;EAChBA,wBAAA,CAAAA,wBAAA,4DAAuB;EACvBA,wBAAA,CAAAA,wBAAA,8DAAwB;EACxBA,wBAAA,CAAAA,wBAAA,8DAAwB;EACxBA,wBAAA,CAAAA,wBAAA,8DAAwB;EACxBA,wBAAA,CAAAA,wBAAA,wFAAqC;EACrCA,wBAAA,CAAAA,wBAAA,4FAAuC;EACvCA,wBAAA,CAAAA,wBAAA,kHAAkD;EAClDA,wBAAA,CAAAA,wBAAA,iGAAyC;EACzCA,wBAAA,CAAAA,wBAAA,+CAAgB;EAChBA,wBAAA,CAAAA,wBAAA,qDAAmB;EACnBA,wBAAA,CAAAA,wBAAA,uDAAoB;EACpBA,wBAAA,CAAAA,wBAAA,qDAAmB;EACnBA,wBAAA,CAAAA,wBAAA,qDAAmB;EACnBA,wBAAA,CAAAA,wBAAA,iDAAiB;EACjBA,wBAAA,CAAAA,wBAAA,6GAA+C;AACjD,CAAC,EAnBWA,wBAAwB,KAAxBA,wBAAwB;AAyBpC,MAAMC,oBAAoB,GAA2B;EACnDC,6BAA6B,EAAE,KAAK;EACpCC,gBAAgB,EAAE,MAAM;EACxBC,sBAAsB,EAAE,WAAW;EACnCC,wBAAwB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACtCC,mBAAmB,EAAE,KAAK;EAC1BC,QAAQ,EAAE,KAAK;EACfC,oBAAoB,EAAEV,yBAAyB;EAC/CW,aAAa,EAAE,KAAK;EACpBC,eAAe,EAAE,KAAK;EACtBC,eAAe,EAAE;CAClB;AAEDC,MAAM,CAACC,MAAM,CAACZ,oBAAoB,CAAC;AAEnC,OAAM,MAAOa,KAAK;EA4BhBC,YACYC,eAAwD,EAClEC,MAAA,GAAuBhB,oBAAoB;IADjC,KAAAe,eAAe,GAAfA,eAAe;IAvBpB,KAAAE,qBAAqB,GAA4B,EAAE;IACnD,KAAAC,sBAAsB,GAA4B,EAAE;IAEjD,KAAAC,kBAAkB,GAAqC,EAAE;IACzD,KAAAC,4BAA4B,GAElC,EAAE;IAEI,KAAAC,KAAK,GAAa,EAAE;IAEpB,KAAAC,WAAW,GAAoC,EAAE;IAGnD,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,aAAa,GAAY,IAAI;IAC7B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,kBAAkB,GAA4B,EAAE;IAq0BxD;IACA;IACA,KAAAC,UAAU,GAAG,CAAIC,SAAiB,EAAEC,SAAkB,KAAO;MAC3D;MACA;MACA,IAAI,IAAI,CAACrB,aAAa,KAAK,IAAI,EAAE;QAC/B,IAAI,CAACsB,eAAe,EAAE;QACtB,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAAC,IAAI,CAACF,eAAe,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;QAC7D,IAAI,IAAI,CAACH,eAAe,GAAG,IAAI,CAACI,iBAAiB,EAAE;UACjDC,OAAO,CAACC,GAAG,CAAC,GAAGL,MAAM,QAAQH,SAAS,GAAG,CAAC;;QAE5C,MAAM;UAAES,IAAI;UAAEC;QAAK,CAAE,GAAG5C,KAAK,CAACmC,SAAS,CAAC;QACxC;QACA,MAAMU,WAAW,GAAGF,IAAI,GAAG,EAAE,GAAGF,OAAO,CAACK,IAAI,GAAGL,OAAO,CAACC,GAAG;QAC1D,IAAI,IAAI,CAACN,eAAe,GAAG,IAAI,CAACI,iBAAiB,EAAE;UACjDK,WAAW,CAAC,GAAGR,MAAM,QAAQH,SAAS,WAAWS,IAAI,IAAI,CAAC;;QAE5D,IAAI,CAACP,eAAe,EAAE;QACtB,OAAOQ,KAAK;OACb,MAAM;QACL,OAAOT,SAAS,EAAE;;IAEtB,CAAC;IAj1BC,IAAI,OAAOb,MAAM,KAAK,SAAS,EAAE;MAC/B,MAAMyB,KAAK,CACT,+EAA+E,GAC7E,+CAA+C,CAClD;;IAGH;IACA,IAAI,CAACzB,MAAM,GAAGpC,MAAM,CAAC,EAAE,EAAEoB,oBAAoB,EAAEgB,MAAM,CAAQ;IAE7D,MAAM0B,YAAY,GAAG,IAAI,CAAC1B,MAAM,CAACR,aAAa;IAC9C,IAAIkC,YAAY,KAAK,IAAI,EAAE;MACzB,IAAI,CAACR,iBAAiB,GAAGS,QAAQ;MACjC,IAAI,CAACnC,aAAa,GAAG,IAAI;KAC1B,MAAM,IAAI,OAAOkC,YAAY,KAAK,QAAQ,EAAE;MAC3C,IAAI,CAACR,iBAAiB,GAAGQ,YAAY;MACrC,IAAI,CAAClC,aAAa,GAAG,IAAI;;IAE3B,IAAI,CAACsB,eAAe,GAAG,CAAC,CAAC;IAEzB,IAAI,CAACH,UAAU,CAAC,mBAAmB,EAAE,MAAK;MACxC,IAAIiB,gBAA4C;MAChD,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,IAAI,CAAClB,UAAU,CAAC,uBAAuB,EAAE,MAAK;QAC5C,IACE,IAAI,CAACX,MAAM,CAACb,sBAAsB,KAClCH,oBAAoB,CAACG,sBAAsB,EAC3C;UACA;UACA,IAAI,CAACa,MAAM,CAACb,sBAAsB,GAAG5B,6BAA6B;SACnE,MAAM;UACL,IACE,IAAI,CAACyC,MAAM,CAACZ,wBAAwB,KACpCJ,oBAAoB,CAACI,wBAAwB,EAC7C;YACA,MAAMqC,KAAK,CACT,2EAA2E,GACzE,yGAAyG,CAC5G;;;QAIL,IAAIzB,MAAM,CAACV,QAAQ,IAAIU,MAAM,CAACX,mBAAmB,EAAE;UACjD,MAAMoC,KAAK,CACT,oEAAoE,CACrE;;QAGH,IAAI,CAAClB,eAAe,GAAG,iBAAiB,CAACuB,IAAI,CAC3C,IAAI,CAAC9B,MAAM,CAACd,gBAAgB,CAC7B;QACD,IAAI,CAACsB,aAAa,GAAG,OAAO,CAACsB,IAAI,CAAC,IAAI,CAAC9B,MAAM,CAACd,gBAAgB,CAAC;QAE/D;QACA,IAAIlB,OAAO,CAAC+B,eAAe,CAAC,EAAE;UAC5B6B,gBAAgB,GAAG;YACjBvB,KAAK,EAAE;cAAE0B,WAAW,EAAElE,KAAK,CAACkC,eAAe;YAAC,CAAE;YAC9CgC,WAAW,EAAEzE;WACd;SACF,MAAM;UACL;UACAuE,iBAAiB,GAAG,KAAK;UACzBD,gBAAgB,GAAG/D,KAAK,CAA4BkC,eAAe,CAAC;;MAExE,CAAC,CAAC;MAEF,IAAI,IAAI,CAACC,MAAM,CAACP,eAAe,KAAK,KAAK,EAAE;QACzC,IAAI,CAACkB,UAAU,CAAC,sBAAsB,EAAE,MAAK;UAC3C,IAAI,CAACV,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAAC+B,MAAM,CAC5DxE,oBAAoB,CAClBoE,gBAAgB,EAChB,IAAI,CAACrB,eAAe,EACpB,IAAI,CAACP,MAAM,CAACZ,wBAAwB,CACrC,CACF;QACH,CAAC,CAAC;QAEF,IAAI,CAACuB,UAAU,CAAC,6BAA6B,EAAE,MAAK;UAClD,IAAI,CAACT,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC8B,MAAM,CAC9DvE,2BAA2B,CACzBmE,gBAAgB,EAChB,IAAI,CAACrB,eAAe,EACpB,IAAI,CAACP,MAAM,CAACZ,wBAAwB,CACrC,CACF;QACH,CAAC,CAAC;;MAGJ;MACAwC,gBAAgB,CAACvB,KAAK,GAAGuB,gBAAgB,CAACvB,KAAK,GAC3CuB,gBAAgB,CAACvB,KAAK,GACtB,EAAE;MAEN;MACA;MACAvC,OAAO,CAAC8D,gBAAgB,CAACvB,KAAK,EAAE,CAAC4B,aAAa,EAAEC,YAAY,KAAI;QAC9DN,gBAAgB,CAACvB,KAAK,CAAC6B,YAAY,CAAC,GAAG1D,MAAM,CAC3CyD,aAAa,EACZE,WAAW,IAAKjE,WAAW,CAACiE,WAAW,CAAC,CAC1C;MACH,CAAC,CAAC;MAEF,MAAMC,YAAY,GAAGjE,IAAI,CAACyD,gBAAgB,CAACvB,KAAK,CAAC;MAEjDvC,OAAO,CACL8D,gBAAgB,CAACvB,KAAK,EACtB,CAACgC,UAAuB,EAAEC,WAAW,KAAI;QACvC,IAAI,CAAC3B,UAAU,CAAC,UAAU2B,WAAW,cAAc,EAAE,MAAK;UACxD,IAAI,CAACjC,KAAK,CAACkC,IAAI,CAACD,WAAW,CAAC;UAE5B,IAAI,IAAI,CAACtC,MAAM,CAACP,eAAe,KAAK,KAAK,EAAE;YACzC,IAAI,CAACkB,UAAU,CAAC,kBAAkB,EAAE,MAAK;cACvC,IAAI,CAACV,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAAC+B,MAAM,CAC5DrE,gBAAgB,CAAC0E,UAAU,EAAED,YAAY,CAAC,CAC3C;YACH,CAAC,CAAC;;UAGJ;UACA;UACA;UACA,IAAInE,OAAO,CAAC,IAAI,CAACgC,qBAAqB,CAAC,EAAE;YACvCrB,iBAAiB,CAACyD,UAAU,CAAC;YAE7B,IAAIG,iBAAkC;YACtC,IAAI,CAAC7B,UAAU,CAAC,mBAAmB,EAAE,MAAK;cACxC6B,iBAAiB,GAAGrF,iBAAiB,CAACkF,UAAU,EAAE;gBAChDjD,wBAAwB,EACtB,IAAI,CAACY,MAAM,CAACZ,wBAAwB;gBACtCF,gBAAgB,EAAEc,MAAM,CAACd,gBAAgB;gBACzCG,mBAAmB,EAAEW,MAAM,CAACX,mBAAmB;gBAC/CC,QAAQ,EAAEU,MAAM,CAACV,QAAQ;gBACzBmD,MAAM,EAAE,IAAI,CAAC9B;eACd,CAAC;YACJ,CAAC,CAAC;YAEF,IAAI,CAACR,kBAAkB,CAACmC,WAAW,CAAC,GAClCE,iBAAiB,CAACrC,kBAAkB;YAEtC,IAAI,CAACC,4BAA4B,CAACkC,WAAW,CAAC,GAC5CE,iBAAiB,CAACpC,4BAA4B;YAEhD,IAAI,CAACE,WAAW,GAAG1C,MAAM,CACvB,EAAE,EACF,IAAI,CAAC0C,WAAW,EAChBkC,iBAAiB,CAAClC,WAAW,CACvB;YAER,IAAI,CAACG,SAAS,GAAG+B,iBAAiB,CAAC/B,SAAS,IAAI,IAAI,CAACA,SAAS;YAE9D,IAAI,CAACC,kBAAkB,CAAC4B,WAAW,CAAC,GAClCE,iBAAiB,CAACE,cAAc;;QAEtC,CAAC,CAAC;MACJ,CAAC,CACF;MAED,IAAI,CAACX,WAAW,GAAGH,gBAAgB,CAACG,WAAW;MAE/C,IACE,CAAC9D,OAAO,CAAC,IAAI,CAACgC,qBAAqB,CAAC,IACpC,CAAC,IAAI,CAACD,MAAM,CAACf,6BAA6B,EAC1C;QACA,MAAM0D,cAAc,GAAGtE,GAAG,CAAC,IAAI,CAAC4B,qBAAqB,EAAG2C,KAAK,IAAI;UAC/D,OAAOA,KAAK,CAACC,OAAO;QACtB,CAAC,CAAC;QACF,MAAMC,oBAAoB,GAAGH,cAAc,CAAC1B,IAAI,CAC9C,2BAA2B,CAC5B;QACD,MAAM,IAAIQ,KAAK,CACb,2CAA2C,GAAGqB,oBAAoB,CACnE;;MAGH;MACAhF,OAAO,CAAC,IAAI,CAACoC,sBAAsB,EAAG6C,iBAAiB,IAAI;QACzDtE,aAAa,CAACsE,iBAAiB,CAACF,OAAO,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAAClC,UAAU,CAAC,sCAAsC,EAAE,MAAK;QAC3D;QACA;QACA;QACA,IAAIjD,cAAc,EAAE;UAClB,IAAI,CAACsF,SAAS,GAAQjF,QAAQ;UAC9B,IAAI,CAACkF,KAAK,GAAG,IAAI,CAACC,aAAa;SAChC,MAAM;UACL,IAAI,CAACC,eAAe,GAAG7E,IAAI;UAC3B,IAAI,CAAC2E,KAAK,GAAG,IAAI,CAACG,aAAa;;QAGjC,IAAIvB,iBAAiB,EAAE;UACrB,IAAI,CAACwB,WAAW,GAAG/E,IAAI;;QAGzB,IAAI,IAAI,CAACiC,eAAe,KAAK,KAAK,EAAE;UAClC,IAAI,CAAC+C,gBAAgB,GAAGvF,QAAQ;;QAGlC,IAAI,IAAI,CAACyC,aAAa,KAAK,KAAK,EAAE;UAChC,IAAI,CAAC+C,gCAAgC,GAAGjF,IAAI;;QAG9C,IAAI,OAAO,CAACwD,IAAI,CAAC,IAAI,CAAC9B,MAAM,CAACd,gBAAgB,CAAC,EAAE;UAC9C,IAAI,CAACsE,mBAAmB,GAAG,IAAI,CAACC,eAAe;SAChD,MAAM,IAAI,YAAY,CAAC3B,IAAI,CAAC,IAAI,CAAC9B,MAAM,CAACd,gBAAgB,CAAC,EAAE;UAC1D,IAAI,CAACsE,mBAAmB,GAAG,IAAI,CAACE,oBAAoB;SACrD,MAAM,IAAI,aAAa,CAAC5B,IAAI,CAAC,IAAI,CAAC9B,MAAM,CAACd,gBAAgB,CAAC,EAAE;UAC3D,IAAI,CAACsE,mBAAmB,GAAG,IAAI,CAACG,qBAAqB;SACtD,MAAM;UACL,MAAMlC,KAAK,CACT,8CAA8C,IAAI,CAACzB,MAAM,CAACd,gBAAgB,GAAG,CAC9E;;QAGH,IAAI,IAAI,CAACuB,SAAS,EAAE;UAClB,IAAI,CAACmD,QAAQ,GAAG,IAAI,CAACC,iBAAiB;UACtC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,uBAAuB;SAClD,MAAM;UACL,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACI,yBAAyB;UAC9C,IAAI,CAACF,aAAa,GAAG,IAAI,CAACG,qBAAqB;;MAEnD,CAAC,CAAC;MAEF,IAAI,CAACtD,UAAU,CAAC,8BAA8B,EAAE,MAAK;QACnD,MAAMuD,gBAAgB,GAAG3F,MAAM,CAC7B,IAAI,CAACmC,kBAAkB,EACvB,CAACyD,iBAAiB,EAAEzB,cAAc,EAAE0B,QAAQ,KAAI;UAC9C,IAAI1B,cAAc,KAAK,KAAK,EAAE;YAC5ByB,iBAAiB,CAAC5B,IAAI,CAAC6B,QAAQ,CAAC;;UAElC,OAAOD,iBAAiB;QAC1B,CAAC,EACD,EAAc,CACf;QAED,IAAInE,MAAM,CAACX,mBAAmB,IAAI,CAACpB,OAAO,CAACiG,gBAAgB,CAAC,EAAE;UAC5D,MAAMzC,KAAK,CACT,kBAAkByC,gBAAgB,CAACjD,IAAI,CACrC,IAAI,CACL,2BAA2B,GAC1B,6HAA6H,GAC7H,2EAA2E,CAC9E;;MAEL,CAAC,CAAC;MAEF,IAAI,CAACN,UAAU,CAAC,wBAAwB,EAAE,MAAK;QAC7C7B,sBAAsB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAAC6B,UAAU,CAAC,kBAAkB,EAAE,MAAK;QACvChC,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEO0F,QAAQA,CACbC,IAAY,EACZC,WAAA,GAAsB,IAAI,CAACxC,WAAW;IAEtC,IAAI,CAAC9D,OAAO,CAAC,IAAI,CAACgC,qBAAqB,CAAC,EAAE;MACxC,MAAM0C,cAAc,GAAGtE,GAAG,CAAC,IAAI,CAAC4B,qBAAqB,EAAG2C,KAAK,IAAI;QAC/D,OAAOA,KAAK,CAACC,OAAO;MACtB,CAAC,CAAC;MACF,MAAMC,oBAAoB,GAAGH,cAAc,CAAC1B,IAAI,CAC9C,2BAA2B,CAC5B;MACD,MAAM,IAAIQ,KAAK,CACb,sEAAsE,GACpEqB,oBAAoB,CACvB;;IAGH,OAAO,IAAI,CAAC0B,gBAAgB,CAACF,IAAI,EAAEC,WAAW,CAAC;EACjD;EAEA;EACA;EACA;EACA;EACQC,gBAAgBA,CAACF,IAAY,EAAEC,WAAmB;IACxD,IAAIE,CAAC,EACHC,CAAC,EACDC,CAAC,EACDC,aAAa,EACbC,SAAS,EACTC,YAA2B,EAC3BC,OAAO,EACPC,UAAU,EACVC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,QAAgB,EAChBC,SAAS,EACTC,WAAW,EACXC,GAAG,EACHtC,KAAK;IACP,MAAMuC,OAAO,GAAGlB,IAAI;IACpB,MAAMmB,SAAS,GAAGD,OAAO,CAACE,MAAM;IAChC,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,kBAAkB,GAAG,CAAC;IAC1B;IACA;IACA;IACA;IACA,MAAMC,qBAAqB,GAAG,IAAI,CAACpF,SAAS,GACxC,CAAC,CAAC;IAAA,EACFqF,IAAI,CAACC,KAAK,CAACzB,IAAI,CAACoB,MAAM,GAAG,EAAE,CAAC;IAChC,MAAMM,aAAa,GAAG,IAAIhF,KAAK,CAAC6E,qBAAqB,CAAC;IACtD,MAAMI,MAAM,GAAmB,EAAE;IACjC,IAAIC,IAAI,GAAG,IAAI,CAAC3F,eAAe,GAAG,CAAC,GAAG4F,SAAS;IAC/C,IAAIC,MAAM,GAAG,IAAI,CAAC7F,eAAe,GAAG,CAAC,GAAG4F,SAAS;IACjD,MAAME,MAAM,GAAQhJ,gBAAgB,CAAC,IAAI,CAACiD,WAAW,CAAC;IACtD,MAAMgG,UAAU,GAAG,IAAI,CAAC/F,eAAe;IACvC,MAAMgG,qBAAqB,GAAG,IAAI,CAACvG,MAAM,CAACb,sBAAsB;IAEhE,IAAIqH,sBAAsB,GAAG,CAAC;IAC9B,IAAIrG,kBAAkB,GAAqB,EAAE;IAC7C,IAAIsG,gCAAgC,GAEhC,EAAE;IAEN,MAAMC,SAAS,GAAa,EAAE;IAE9B,MAAMC,UAAU,GAAqB,EAAE;IACvChH,MAAM,CAACC,MAAM,CAAC+G,UAAU,CAAC;IACzB,IAAIC,mBAA4D;IAEhE,SAASC,uBAAuBA,CAAA;MAC9B,OAAO1G,kBAAkB;IAC3B;IAEA,SAAS2G,4BAA4BA,CAACC,QAAgB;MACpD,MAAMC,gBAAgB,GAAG5J,wBAAwB,CAAC2J,QAAQ,CAAC;MAC3D,MAAME,gBAAgB,GACpBR,gCAAgC,CAACO,gBAAgB,CAAC;MACpD,IAAIC,gBAAgB,KAAKd,SAAS,EAAE;QAClC,OAAOQ,UAAU;OAClB,MAAM;QACL,OAAOM,gBAAgB;;IAE3B;IAEA,MAAMC,QAAQ,GAAIC,QAAgB,IAAI;MACpC;MACA,IACET,SAAS,CAAChB,MAAM,KAAK,CAAC;MACtB;MACA;MACAyB,QAAQ,CAACC,SAAS,CAACC,SAAS,KAAKlB,SAAS,EAC1C;QACA;QACA;QACA,MAAMZ,GAAG,GACP,IAAI,CAACvF,MAAM,CAACT,oBAAoB,CAAC+H,gCAAgC,CAC/DH,QAAQ,CACT;QAEHlB,MAAM,CAAC1D,IAAI,CAAC;UACVoD,MAAM,EAAEwB,QAAQ,CAACI,WAAW;UAC5BrB,IAAI,EAAEiB,QAAQ,CAACK,SAAS;UACxBpB,MAAM,EAAEe,QAAQ,CAACM,WAAW;UAC5B/B,MAAM,EAAEyB,QAAQ,CAACO,KAAK,CAAChC,MAAM;UAC7B7C,OAAO,EAAE0C;SACV,CAAC;OACH,MAAM;QACLmB,SAAS,CAACiB,GAAG,EAAE;QACf,MAAMC,OAAO,GAAGxJ,IAAI,CAACsI,SAAS,CAAE;QAChCvG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACyH,OAAO,CAAC;QACrDnB,gCAAgC,GAC9B,IAAI,CAACrG,4BAA4B,CAACwH,OAAO,CAAC;QAC5CpB,sBAAsB,GAAGrG,kBAAkB,CAACuF,MAAM;QAClD,MAAMmC,kBAAkB,GACtB,IAAI,CAACnH,kBAAkB,CAACkH,OAAO,CAAC,IAAI,IAAI,CAAC5H,MAAM,CAACV,QAAQ,KAAK,KAAK;QAEpE,IAAImH,gCAAgC,IAAIoB,kBAAkB,EAAE;UAC1DjB,mBAAmB,GAAGE,4BAA4B;SACnD,MAAM;UACLF,mBAAmB,GAAGC,uBAAuB;;;IAGnD,CAAC;IAED,SAASiB,SAASA,CAAcF,OAAe;MAC7ClB,SAAS,CAACnE,IAAI,CAACqF,OAAO,CAAC;MACvBnB,gCAAgC,GAC9B,IAAI,CAACrG,4BAA4B,CAACwH,OAAO,CAAC;MAE5CzH,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACyH,OAAO,CAAC;MACrDpB,sBAAsB,GAAGrG,kBAAkB,CAACuF,MAAM;MAElDc,sBAAsB,GAAGrG,kBAAkB,CAACuF,MAAM;MAClD,MAAMmC,kBAAkB,GACtB,IAAI,CAACnH,kBAAkB,CAACkH,OAAO,CAAC,IAAI,IAAI,CAAC5H,MAAM,CAACV,QAAQ,KAAK,KAAK;MAEpE,IAAImH,gCAAgC,IAAIoB,kBAAkB,EAAE;QAC1DjB,mBAAmB,GAAGE,4BAA4B;OACnD,MAAM;QACLF,mBAAmB,GAAGC,uBAAuB;;IAEjD;IAEA;IACA;IACAiB,SAAS,CAACC,IAAI,CAAC,IAAI,EAAExD,WAAW,CAAC;IAEjC,IAAIyD,UAA2B;IAE/B,MAAMtI,eAAe,GAAG,IAAI,CAACM,MAAM,CAACN,eAAe;IAEnD,OAAOiG,MAAM,GAAGF,SAAS,EAAE;MACzBX,YAAY,GAAG,IAAI;MAEnB,MAAMmD,YAAY,GAAGzC,OAAO,CAAC0C,UAAU,CAACvC,MAAM,CAAC;MAC/C,MAAMwC,wBAAwB,GAAGvB,mBAAmB,CAACqB,YAAY,CAAC;MAClE,MAAMG,oBAAoB,GAAGD,wBAAwB,CAACzC,MAAM;MAE5D,KAAKjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,oBAAoB,EAAE3D,CAAC,EAAE,EAAE;QACzCuD,UAAU,GAAGG,wBAAwB,CAAC1D,CAAC,CAAC;QACxC,MAAM4D,WAAW,GAAGL,UAAU,CAACM,OAAO;QACtCvD,OAAO,GAAG,IAAI;QAEd;QACA,MAAMwD,cAAc,GAAGP,UAAU,CAACQ,KAAK;QACvC,IAAID,cAAc,KAAK,KAAK,EAAE;UAC5B,IAAIN,YAAY,KAAKM,cAAc,EAAE;YACnC;YACAzD,YAAY,GAAGuD,WAAqB;;SAEvC,MAAM,IAAIL,UAAU,CAACS,QAAQ,KAAK,IAAI,EAAE;UACvCxF,KAAK,GAAIoF,WAA2B,CAACK,IAAI,CACvClD,OAAO,EACPG,MAAM,EACNK,aAAa,EACbK,MAAM,CACP;UACD,IAAIpD,KAAK,KAAK,IAAI,EAAE;YAClB6B,YAAY,GAAG7B,KAAK,CAAC,CAAC,CAAC;YACvB,IAAKA,KAAoC,CAAC8B,OAAO,KAAKoB,SAAS,EAAE;cAC/DpB,OAAO,GAAI9B,KAAoC,CAAC8B,OAAO;;WAE1D,MAAM;YACLD,YAAY,GAAG,IAAI;;SAEtB,MAAM;UACL,IAAI,CAAC3B,eAAe,CAACkF,WAAqB,EAAE1C,MAAM,CAAC;UACnDb,YAAY,GAAG,IAAI,CAAC7B,KAAK,CAACoF,WAAqB,EAAE/D,IAAI,EAAEqB,MAAM,CAAC;;QAGhE,IAAIb,YAAY,KAAK,IAAI,EAAE;UACzB;UACA;UACAD,SAAS,GAAGmD,UAAU,CAACnD,SAAS;UAChC,IAAIA,SAAS,KAAKsB,SAAS,EAAE;YAC3B;YACA;YACA,MAAMwC,eAAe,GAAG9D,SAAS,CAACa,MAAM;YACxC,KAAKf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,eAAe,EAAEhE,CAAC,EAAE,EAAE;cACpC,MAAMiE,eAAe,GAAGzI,kBAAkB,CAAC0E,SAAS,CAACF,CAAC,CAAC,CAAC;cACxD,MAAMkE,gBAAgB,GAAGD,eAAe,CAACN,OAAO;cAChDtD,UAAU,GAAG,IAAI;cAEjB;cACA;cACA,IAAI4D,eAAe,CAACH,QAAQ,KAAK,IAAI,EAAE;gBACrCxF,KAAK,GAAI4F,gBAAgC,CAACH,IAAI,CAC5ClD,OAAO,EACPG,MAAM,EACNK,aAAa,EACbK,MAAM,CACP;gBACD,IAAIpD,KAAK,KAAK,IAAI,EAAE;kBAClB2B,aAAa,GAAG3B,KAAK,CAAC,CAAC,CAAC;kBACxB,IACGA,KAAoC,CAAC8B,OAAO,KAAKoB,SAAS,EAC3D;oBACAnB,UAAU,GAAI/B,KAAoC,CAAC8B,OAAO;;iBAE7D,MAAM;kBACLH,aAAa,GAAG,IAAI;;eAEvB,MAAM;gBACL,IAAI,CAACzB,eAAe,CAAC0F,gBAA0B,EAAElD,MAAM,CAAC;gBACxDf,aAAa,GAAG,IAAI,CAAC3B,KAAK,CACxB4F,gBAA0B,EAC1BvE,IAAI,EACJqB,MAAM,CACP;;cAGH,IAAIf,aAAa,IAAIA,aAAa,CAACc,MAAM,GAAGZ,YAAY,CAACY,MAAM,EAAE;gBAC/DZ,YAAY,GAAGF,aAAa;gBAC5BG,OAAO,GAAGC,UAAU;gBACpBgD,UAAU,GAAGY,eAAe;gBAC5B;gBACA;gBACA;;;;UAIN;;;MAIJ;MACA,IAAI9D,YAAY,KAAK,IAAI,EAAE;QACzBG,WAAW,GAAGH,YAAY,CAACY,MAAM;QACjCR,KAAK,GAAG8C,UAAU,CAAC9C,KAAK;QACxB,IAAIA,KAAK,KAAKiB,SAAS,EAAE;UACvBhB,OAAO,GAAG6C,UAAU,CAACc,YAAY;UACjC;UACA;UACA1D,QAAQ,GAAG,IAAI,CAAC5B,mBAAmB,CACjCsB,YAAY,EACZa,MAAM,EACNR,OAAO,EACP6C,UAAU,CAACZ,SAAS,EACpBlB,IAAI,EACJE,MAAM,EACNnB,WAAW,CACZ;UAED,IAAI,CAACnB,aAAa,CAACsB,QAAQ,EAAEL,OAAO,CAAC;UAErC;UACA,IAAIG,KAAK,KAAK,KAAK,EAAE;YACnBU,kBAAkB,GAAG,IAAI,CAAChC,QAAQ,CAChCoC,aAAa,EACbJ,kBAAkB,EAClBR,QAAQ,CACT;WACF,MAAM;YACLiB,MAAM,CAACnB,KAAK,CAAC,CAAC3C,IAAI,CAAC6C,QAAQ,CAAC;;;QAGhCd,IAAI,GAAG,IAAI,CAACtB,SAAS,CAACsB,IAAI,EAAEW,WAAW,CAAC;QACxCU,MAAM,GAAGA,MAAM,GAAGV,WAAW;QAE7B;QACAmB,MAAM,GAAG,IAAI,CAAC9C,gBAAgB,CAAC8C,MAAO,EAAEnB,WAAW,CAAC;QAEpD,IAAIqB,UAAU,KAAK,IAAI,IAAI0B,UAAU,CAACe,iBAAiB,KAAK,IAAI,EAAE;UAChE,IAAIC,eAAe,GAAG,CAAC;UACvB,IAAIC,eAAe;UACnB,IAAIC,eAAuB;UAC3B3C,qBAAqB,CAAC4C,SAAS,GAAG,CAAC;UACnC,GAAG;YACDF,eAAe,GAAG1C,qBAAqB,CAACzE,IAAI,CAACgD,YAAY,CAAC;YAC1D,IAAImE,eAAe,KAAK,IAAI,EAAE;cAC5BC,eAAe,GAAG3C,qBAAqB,CAAC4C,SAAS,GAAG,CAAC;cACrDH,eAAe,EAAE;;WAEpB,QAAQC,eAAe,KAAK,IAAI;UAEjC,IAAID,eAAe,KAAK,CAAC,EAAE;YACzB9C,IAAI,GAAGA,IAAK,GAAG8C,eAAe;YAC9B5C,MAAM,GAAGnB,WAAW,GAAGiE,eAAgB;YACvC,IAAI,CAAC3F,gCAAgC,CACnC6B,QAAS,EACTF,KAAM,EACNgE,eAAgB,EAChBF,eAAe,EACf9C,IAAI,EACJE,MAAM,EACNnB,WAAW,CACZ;;;QAGL;QACA,IAAI,CAAC5B,WAAW,CAAC2E,UAAU,EAAEd,QAAQ,EAAEY,SAAS,EAAE1C,QAAS,CAAC;OAC7D,MAAM;QACL;QACA,MAAMgE,gBAAgB,GAAGzD,MAAM;QAC/B,MAAM0D,SAAS,GAAGnD,IAAI;QACtB,MAAMoD,WAAW,GAAGlD,MAAM;QAC1B,IAAImD,gBAAgB,GAAG7J,eAAe,KAAK,KAAK;QAEhD,OAAO6J,gBAAgB,KAAK,KAAK,IAAI5D,MAAM,GAAGF,SAAS,EAAE;UACvD;UACAnB,IAAI,GAAG,IAAI,CAACtB,SAAS,CAACsB,IAAI,EAAE,CAAC,CAAC;UAC9BqB,MAAM,EAAE;UACR,KAAKjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,sBAAsB,EAAE9B,CAAC,EAAE,EAAE;YAC3C,MAAMsD,UAAU,GAAG7H,kBAAkB,CAACuE,CAAC,CAAC;YACxC,MAAM2D,WAAW,GAAGL,UAAU,CAACM,OAAO;YAEtC;YACA,MAAMC,cAAc,GAAGP,UAAU,CAACQ,KAAK;YACvC,IAAID,cAAc,KAAK,KAAK,EAAE;cAC5B,IAAI/C,OAAO,CAAC0C,UAAU,CAACvC,MAAM,CAAC,KAAK4C,cAAc,EAAE;gBACjD;gBACAgB,gBAAgB,GAAG,IAAI;;aAE1B,MAAM,IAAIvB,UAAU,CAACS,QAAQ,KAAK,IAAI,EAAE;cACvCc,gBAAgB,GACblB,WAA2B,CAACK,IAAI,CAC/BlD,OAAO,EACPG,MAAM,EACNK,aAAa,EACbK,MAAM,CACP,KAAK,IAAI;aACb,MAAM;cACL,IAAI,CAAClD,eAAe,CAACkF,WAAqB,EAAE1C,MAAM,CAAC;cACnD4D,gBAAgB,GAAIlB,WAAsB,CAACK,IAAI,CAACpE,IAAI,CAAC,KAAK,IAAI;;YAGhE,IAAIiF,gBAAgB,KAAK,IAAI,EAAE;cAC7B;;;;QAKNlE,SAAS,GAAGM,MAAM,GAAGyD,gBAAgB;QACrChD,MAAM,GAAG,IAAI,CAAC9C,gBAAgB,CAAC8C,MAAO,EAAEf,SAAS,CAAC;QAClD;QACAE,GAAG,GAAG,IAAI,CAACvF,MAAM,CAACT,oBAAoB,CAACiK,gCAAgC,CACrEhE,OAAO,EACP4D,gBAAgB,EAChB/D,SAAS,EACTgE,SAAS,EACTC,WAAW,CACZ;QACDrD,MAAM,CAAC1D,IAAI,CAAC;UACVoD,MAAM,EAAEyD,gBAAgB;UACxBlD,IAAI,EAAEmD,SAAS;UACfjD,MAAM,EAAEkD,WAAW;UACnB5D,MAAM,EAAEL,SAAS;UACjBxC,OAAO,EAAE0C;SACV,CAAC;QAEF,IAAI7F,eAAe,KAAK,KAAK,EAAE;UAC7B;;;;IAKN;IACA;IACA,IAAI,CAAC,IAAI,CAACe,SAAS,EAAE;MACnB;MACAuF,aAAa,CAACN,MAAM,GAAGE,kBAAkB;;IAG3C,OAAO;MACL6D,MAAM,EAAEzD,aAAa;MACrBK,MAAM,EAAEA,MAAM;MACdJ,MAAM,EAAEA;KACT;EACH;EAEQ5C,WAAWA,CACjBrD,MAAsB,EACtBkH,QAA+B,EAC/BY,SAAkD,EAClD1C,QAAgB;IAEhB,IAAIpF,MAAM,CAAC2H,GAAG,KAAK,IAAI,EAAE;MACvB;MACA;MACA,MAAM+B,QAAQ,GAAG1J,MAAM,CAACuC,IAAI;MAC5B2E,QAAQ,CAAC9B,QAAQ,CAAC;MAClB,IAAIsE,QAAQ,KAAKvD,SAAS,EAAE;QAC1B2B,SAAS,CAACC,IAAI,CAAC,IAAI,EAAE2B,QAAQ,CAAC;;KAEjC,MAAM,IAAI1J,MAAM,CAACuC,IAAI,KAAK4D,SAAS,EAAE;MACpC2B,SAAS,CAACC,IAAI,CAAC,IAAI,EAAE/H,MAAM,CAACuC,IAAI,CAAC;;EAErC;EAEQS,SAASA,CAACsB,IAAY,EAAEoB,MAAc;IAC5C,OAAOpB,IAAI,CAACqF,SAAS,CAACjE,MAAM,CAAC;EAC/B;EAEQvC,eAAeA,CAACyG,MAAc,EAAEC,YAAoB;IAC1DD,MAAM,CAACT,SAAS,GAAGU,YAAY;EACjC;EAEA;EACQtG,gCAAgCA,CACtC6B,QAAgB,EAChBF,KAAqB,EACrB4E,SAAiB,EACjBd,eAAuB,EACvB9C,IAAY,EACZE,MAAc,EACdnB,WAAmB;IAEnB,IAAI8E,YAAY,EAAEC,gBAAgB;IAClC,IAAI9E,KAAK,KAAKiB,SAAS,EAAE;MACvB;MACA4D,YAAY,GAAGD,SAAS,KAAK7E,WAAW,GAAG,CAAC;MAC5C+E,gBAAgB,GAAGD,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;MACxC,IAAI,EAAEf,eAAe,KAAK,CAAC,IAAIe,YAAY,KAAK,IAAI,CAAC,EAAE;QACrD;QACA3E,QAAQ,CAAC6E,OAAO,GAAG/D,IAAI,GAAG8D,gBAAgB;QAC1C;QACA;QACA5E,QAAQ,CAAC8E,SAAS,GAAG9D,MAAM,GAAG,CAAC,GAAG,CAAC4D,gBAAgB;;MAErD;;EAEJ;EAEQ1G,gBAAgBA,CAAC6G,SAAiB,EAAElF,WAAmB;IAC7D,OAAOkF,SAAS,GAAGlF,WAAW;EAChC;EAMQtB,qBAAqBA,CAC3B+D,KAAa,EACbH,WAAmB,EACnBuB,YAAoB,EACpB1B,SAAoB;IAEpB,OAAO;MACLM,KAAK;MACLH,WAAW;MACXuB,YAAY;MACZ1B;KACD;EACH;EAEQ1D,oBAAoBA,CAC1BgE,KAAa,EACbH,WAAmB,EACnBuB,YAAoB,EACpB1B,SAAoB,EACpBI,SAAiB,EACjBC,WAAmB;IAEnB,OAAO;MACLC,KAAK;MACLH,WAAW;MACXC,SAAS;MACTC,WAAW;MACXqB,YAAY;MACZ1B;KACD;EACH;EAEQ3D,eAAeA,CACrBiE,KAAa,EACbH,WAAmB,EACnBuB,YAAoB,EACpB1B,SAAoB,EACpBI,SAAiB,EACjBC,WAAmB,EACnBxC,WAAmB;IAEnB,OAAO;MACLyC,KAAK;MACLH,WAAW;MACX6C,SAAS,EAAE7C,WAAW,GAAGtC,WAAW,GAAG,CAAC;MACxCuC,SAAS;MACTyC,OAAO,EAAEzC,SAAS;MAClBC,WAAW;MACXyC,SAAS,EAAEzC,WAAW,GAAGxC,WAAW,GAAG,CAAC;MACxC6D,YAAY;MACZ1B;KACD;EACH;EAUQvD,iBAAiBA,CACvBwG,WAAqB,EACrBC,KAAa,EACbC,UAAkB;IAElBF,WAAW,CAAC9H,IAAI,CAACgI,UAAU,CAAC;IAC5B,OAAOD,KAAK;EACd;EAEQtG,yBAAyBA,CAC/BqG,WAAqB,EACrBC,KAAa,EACbC,UAAkB;IAElBF,WAAW,CAACC,KAAK,CAAC,GAAGC,UAAU;IAC/BD,KAAK,EAAE;IACP,OAAOA,KAAK;EACd;EAKQrG,qBAAqBA,CAACuG,KAAa,EAAEzF,OAAY,GAAS;EAE1DhB,uBAAuBA,CAACyG,KAAa,EAAEzF,OAAY;IACzD,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpByF,KAAK,CAACzF,OAAO,GAAGA,OAAO;;EAE3B;EASQ7B,aAAaA,CACnBoF,OAAe,EACfhE,IAAY,EACZqB,MAAc;IAEd,MAAM8E,KAAK,GAAGnC,OAAO,CAACxG,IAAI,CAACwC,IAAI,CAAC;IAChC,IAAImG,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOnG,IAAI,CAACqF,SAAS,CAAChE,MAAM,EAAE2C,OAAO,CAACa,SAAS,CAAC;;IAElD,OAAO,IAAI;EACb;EAEQ/F,aAAaA,CAACkF,OAAe,EAAEhE,IAAY;IACjD,MAAMoG,WAAW,GAAGpC,OAAO,CAACI,IAAI,CAACpE,IAAI,CAAC;IACtC,OAAOoG,WAAW,KAAK,IAAI,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;EACrD;;AAx1Bc7K,KAAA,CAAA8K,OAAO,GACnB,iFAAiF,GACjF,6GAA6G;AAEjG9K,KAAA,CAAA+K,EAAE,GAAG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}