{"ast": null, "code": "import { clone, compact, difference, drop, dropRight, filter, first, flatMap, flatten, forEach, groupBy, includes, isEmpty, map, pickBy, reduce, reject, values } from \"lodash-es\";\nimport { ParserDefinitionErrorType } from \"../parser/parser.js\";\nimport { Alternation, Alternative as AlternativeGAST, GAstVisitor, getProductionDslName, isOptionalProd, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Terminal } from \"@chevrotain/gast\";\nimport { containsPath, getLookaheadPathsForOptionalProd, getLookaheadPathsForOr, getProdType, isStrictPrefixOfPath } from \"./lookahead.js\";\nimport { nextPossibleTokensAfter } from \"./interpreter.js\";\nimport { tokenStructuredMatcher } from \"../../scan/tokens.js\";\nexport function validateLookahead(options) {\n  const lookaheadValidationErrorMessages = options.lookaheadStrategy.validate({\n    rules: options.rules,\n    tokenTypes: options.tokenTypes,\n    grammarName: options.grammarName\n  });\n  return map(lookaheadValidationErrorMessages, errorMessage => Object.assign({\n    type: ParserDefinitionErrorType.CUSTOM_LOOKAHEAD_VALIDATION\n  }, errorMessage));\n}\nexport function validateGrammar(topLevels, tokenTypes, errMsgProvider, grammarName) {\n  const duplicateErrors = flatMap(topLevels, currTopLevel => validateDuplicateProductions(currTopLevel, errMsgProvider));\n  const termsNamespaceConflictErrors = checkTerminalAndNoneTerminalsNameSpace(topLevels, tokenTypes, errMsgProvider);\n  const tooManyAltsErrors = flatMap(topLevels, curRule => validateTooManyAlts(curRule, errMsgProvider));\n  const duplicateRulesError = flatMap(topLevels, curRule => validateRuleDoesNotAlreadyExist(curRule, topLevels, grammarName, errMsgProvider));\n  return duplicateErrors.concat(termsNamespaceConflictErrors, tooManyAltsErrors, duplicateRulesError);\n}\nfunction validateDuplicateProductions(topLevelRule, errMsgProvider) {\n  const collectorVisitor = new OccurrenceValidationCollector();\n  topLevelRule.accept(collectorVisitor);\n  const allRuleProductions = collectorVisitor.allProductions;\n  const productionGroups = groupBy(allRuleProductions, identifyProductionForDuplicates);\n  const duplicates = pickBy(productionGroups, currGroup => {\n    return currGroup.length > 1;\n  });\n  const errors = map(values(duplicates), currDuplicates => {\n    const firstProd = first(currDuplicates);\n    const msg = errMsgProvider.buildDuplicateFoundError(topLevelRule, currDuplicates);\n    const dslName = getProductionDslName(firstProd);\n    const defError = {\n      message: msg,\n      type: ParserDefinitionErrorType.DUPLICATE_PRODUCTIONS,\n      ruleName: topLevelRule.name,\n      dslName: dslName,\n      occurrence: firstProd.idx\n    };\n    const param = getExtraProductionArgument(firstProd);\n    if (param) {\n      defError.parameter = param;\n    }\n    return defError;\n  });\n  return errors;\n}\nexport function identifyProductionForDuplicates(prod) {\n  return `${getProductionDslName(prod)}_#_${prod.idx}_#_${getExtraProductionArgument(prod)}`;\n}\nfunction getExtraProductionArgument(prod) {\n  if (prod instanceof Terminal) {\n    return prod.terminalType.name;\n  } else if (prod instanceof NonTerminal) {\n    return prod.nonTerminalName;\n  } else {\n    return \"\";\n  }\n}\nexport class OccurrenceValidationCollector extends GAstVisitor {\n  constructor() {\n    super(...arguments);\n    this.allProductions = [];\n  }\n  visitNonTerminal(subrule) {\n    this.allProductions.push(subrule);\n  }\n  visitOption(option) {\n    this.allProductions.push(option);\n  }\n  visitRepetitionWithSeparator(manySep) {\n    this.allProductions.push(manySep);\n  }\n  visitRepetitionMandatory(atLeastOne) {\n    this.allProductions.push(atLeastOne);\n  }\n  visitRepetitionMandatoryWithSeparator(atLeastOneSep) {\n    this.allProductions.push(atLeastOneSep);\n  }\n  visitRepetition(many) {\n    this.allProductions.push(many);\n  }\n  visitAlternation(or) {\n    this.allProductions.push(or);\n  }\n  visitTerminal(terminal) {\n    this.allProductions.push(terminal);\n  }\n}\nexport function validateRuleDoesNotAlreadyExist(rule, allRules, className, errMsgProvider) {\n  const errors = [];\n  const occurrences = reduce(allRules, (result, curRule) => {\n    if (curRule.name === rule.name) {\n      return result + 1;\n    }\n    return result;\n  }, 0);\n  if (occurrences > 1) {\n    const errMsg = errMsgProvider.buildDuplicateRuleNameError({\n      topLevelRule: rule,\n      grammarName: className\n    });\n    errors.push({\n      message: errMsg,\n      type: ParserDefinitionErrorType.DUPLICATE_RULE_NAME,\n      ruleName: rule.name\n    });\n  }\n  return errors;\n}\n// TODO: is there anyway to get only the rule names of rules inherited from the super grammars?\n// This is not part of the IGrammarErrorProvider because the validation cannot be performed on\n// The grammar structure, only at runtime.\nexport function validateRuleIsOverridden(ruleName, definedRulesNames, className) {\n  const errors = [];\n  let errMsg;\n  if (!includes(definedRulesNames, ruleName)) {\n    errMsg = `Invalid rule override, rule: ->${ruleName}<- cannot be overridden in the grammar: ->${className}<-` + `as it is not defined in any of the super grammars `;\n    errors.push({\n      message: errMsg,\n      type: ParserDefinitionErrorType.INVALID_RULE_OVERRIDE,\n      ruleName: ruleName\n    });\n  }\n  return errors;\n}\nexport function validateNoLeftRecursion(topRule, currRule, errMsgProvider, path = []) {\n  const errors = [];\n  const nextNonTerminals = getFirstNoneTerminal(currRule.definition);\n  if (isEmpty(nextNonTerminals)) {\n    return [];\n  } else {\n    const ruleName = topRule.name;\n    const foundLeftRecursion = includes(nextNonTerminals, topRule);\n    if (foundLeftRecursion) {\n      errors.push({\n        message: errMsgProvider.buildLeftRecursionError({\n          topLevelRule: topRule,\n          leftRecursionPath: path\n        }),\n        type: ParserDefinitionErrorType.LEFT_RECURSION,\n        ruleName: ruleName\n      });\n    }\n    // we are only looking for cyclic paths leading back to the specific topRule\n    // other cyclic paths are ignored, we still need this difference to avoid infinite loops...\n    const validNextSteps = difference(nextNonTerminals, path.concat([topRule]));\n    const errorsFromNextSteps = flatMap(validNextSteps, currRefRule => {\n      const newPath = clone(path);\n      newPath.push(currRefRule);\n      return validateNoLeftRecursion(topRule, currRefRule, errMsgProvider, newPath);\n    });\n    return errors.concat(errorsFromNextSteps);\n  }\n}\nexport function getFirstNoneTerminal(definition) {\n  let result = [];\n  if (isEmpty(definition)) {\n    return result;\n  }\n  const firstProd = first(definition);\n  /* istanbul ignore else */\n  if (firstProd instanceof NonTerminal) {\n    result.push(firstProd.referencedRule);\n  } else if (firstProd instanceof AlternativeGAST || firstProd instanceof Option || firstProd instanceof RepetitionMandatory || firstProd instanceof RepetitionMandatoryWithSeparator || firstProd instanceof RepetitionWithSeparator || firstProd instanceof Repetition) {\n    result = result.concat(getFirstNoneTerminal(firstProd.definition));\n  } else if (firstProd instanceof Alternation) {\n    // each sub definition in alternation is a FLAT\n    result = flatten(map(firstProd.definition, currSubDef => getFirstNoneTerminal(currSubDef.definition)));\n  } else if (firstProd instanceof Terminal) {\n    // nothing to see, move along\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n  const isFirstOptional = isOptionalProd(firstProd);\n  const hasMore = definition.length > 1;\n  if (isFirstOptional && hasMore) {\n    const rest = drop(definition);\n    return result.concat(getFirstNoneTerminal(rest));\n  } else {\n    return result;\n  }\n}\nclass OrCollector extends GAstVisitor {\n  constructor() {\n    super(...arguments);\n    this.alternations = [];\n  }\n  visitAlternation(node) {\n    this.alternations.push(node);\n  }\n}\nexport function validateEmptyOrAlternative(topLevelRule, errMsgProvider) {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  const ors = orCollector.alternations;\n  const errors = flatMap(ors, currOr => {\n    const exceptLast = dropRight(currOr.definition);\n    return flatMap(exceptLast, (currAlternative, currAltIdx) => {\n      const possibleFirstInAlt = nextPossibleTokensAfter([currAlternative], [], tokenStructuredMatcher, 1);\n      if (isEmpty(possibleFirstInAlt)) {\n        return [{\n          message: errMsgProvider.buildEmptyAlternationError({\n            topLevelRule: topLevelRule,\n            alternation: currOr,\n            emptyChoiceIdx: currAltIdx\n          }),\n          type: ParserDefinitionErrorType.NONE_LAST_EMPTY_ALT,\n          ruleName: topLevelRule.name,\n          occurrence: currOr.idx,\n          alternative: currAltIdx + 1\n        }];\n      } else {\n        return [];\n      }\n    });\n  });\n  return errors;\n}\nexport function validateAmbiguousAlternationAlternatives(topLevelRule, globalMaxLookahead, errMsgProvider) {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  let ors = orCollector.alternations;\n  // New Handling of ignoring ambiguities\n  // - https://github.com/chevrotain/chevrotain/issues/869\n  ors = reject(ors, currOr => currOr.ignoreAmbiguities === true);\n  const errors = flatMap(ors, currOr => {\n    const currOccurrence = currOr.idx;\n    const actualMaxLookahead = currOr.maxLookahead || globalMaxLookahead;\n    const alternatives = getLookaheadPathsForOr(currOccurrence, topLevelRule, actualMaxLookahead, currOr);\n    const altsAmbiguityErrors = checkAlternativesAmbiguities(alternatives, currOr, topLevelRule, errMsgProvider);\n    const altsPrefixAmbiguityErrors = checkPrefixAlternativesAmbiguities(alternatives, currOr, topLevelRule, errMsgProvider);\n    return altsAmbiguityErrors.concat(altsPrefixAmbiguityErrors);\n  });\n  return errors;\n}\nexport class RepetitionCollector extends GAstVisitor {\n  constructor() {\n    super(...arguments);\n    this.allProductions = [];\n  }\n  visitRepetitionWithSeparator(manySep) {\n    this.allProductions.push(manySep);\n  }\n  visitRepetitionMandatory(atLeastOne) {\n    this.allProductions.push(atLeastOne);\n  }\n  visitRepetitionMandatoryWithSeparator(atLeastOneSep) {\n    this.allProductions.push(atLeastOneSep);\n  }\n  visitRepetition(many) {\n    this.allProductions.push(many);\n  }\n}\nexport function validateTooManyAlts(topLevelRule, errMsgProvider) {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  const ors = orCollector.alternations;\n  const errors = flatMap(ors, currOr => {\n    if (currOr.definition.length > 255) {\n      return [{\n        message: errMsgProvider.buildTooManyAlternativesError({\n          topLevelRule: topLevelRule,\n          alternation: currOr\n        }),\n        type: ParserDefinitionErrorType.TOO_MANY_ALTS,\n        ruleName: topLevelRule.name,\n        occurrence: currOr.idx\n      }];\n    } else {\n      return [];\n    }\n  });\n  return errors;\n}\nexport function validateSomeNonEmptyLookaheadPath(topLevelRules, maxLookahead, errMsgProvider) {\n  const errors = [];\n  forEach(topLevelRules, currTopRule => {\n    const collectorVisitor = new RepetitionCollector();\n    currTopRule.accept(collectorVisitor);\n    const allRuleProductions = collectorVisitor.allProductions;\n    forEach(allRuleProductions, currProd => {\n      const prodType = getProdType(currProd);\n      const actualMaxLookahead = currProd.maxLookahead || maxLookahead;\n      const currOccurrence = currProd.idx;\n      const paths = getLookaheadPathsForOptionalProd(currOccurrence, currTopRule, prodType, actualMaxLookahead);\n      const pathsInsideProduction = paths[0];\n      if (isEmpty(flatten(pathsInsideProduction))) {\n        const errMsg = errMsgProvider.buildEmptyRepetitionError({\n          topLevelRule: currTopRule,\n          repetition: currProd\n        });\n        errors.push({\n          message: errMsg,\n          type: ParserDefinitionErrorType.NO_NON_EMPTY_LOOKAHEAD,\n          ruleName: currTopRule.name\n        });\n      }\n    });\n  });\n  return errors;\n}\nfunction checkAlternativesAmbiguities(alternatives, alternation, rule, errMsgProvider) {\n  const foundAmbiguousPaths = [];\n  const identicalAmbiguities = reduce(alternatives, (result, currAlt, currAltIdx) => {\n    // ignore (skip) ambiguities with this alternative\n    if (alternation.definition[currAltIdx].ignoreAmbiguities === true) {\n      return result;\n    }\n    forEach(currAlt, currPath => {\n      const altsCurrPathAppearsIn = [currAltIdx];\n      forEach(alternatives, (currOtherAlt, currOtherAltIdx) => {\n        if (currAltIdx !== currOtherAltIdx && containsPath(currOtherAlt, currPath) &&\n        // ignore (skip) ambiguities with this \"other\" alternative\n        alternation.definition[currOtherAltIdx].ignoreAmbiguities !== true) {\n          altsCurrPathAppearsIn.push(currOtherAltIdx);\n        }\n      });\n      if (altsCurrPathAppearsIn.length > 1 && !containsPath(foundAmbiguousPaths, currPath)) {\n        foundAmbiguousPaths.push(currPath);\n        result.push({\n          alts: altsCurrPathAppearsIn,\n          path: currPath\n        });\n      }\n    });\n    return result;\n  }, []);\n  const currErrors = map(identicalAmbiguities, currAmbDescriptor => {\n    const ambgIndices = map(currAmbDescriptor.alts, currAltIdx => currAltIdx + 1);\n    const currMessage = errMsgProvider.buildAlternationAmbiguityError({\n      topLevelRule: rule,\n      alternation: alternation,\n      ambiguityIndices: ambgIndices,\n      prefixPath: currAmbDescriptor.path\n    });\n    return {\n      message: currMessage,\n      type: ParserDefinitionErrorType.AMBIGUOUS_ALTS,\n      ruleName: rule.name,\n      occurrence: alternation.idx,\n      alternatives: currAmbDescriptor.alts\n    };\n  });\n  return currErrors;\n}\nexport function checkPrefixAlternativesAmbiguities(alternatives, alternation, rule, errMsgProvider) {\n  // flatten\n  const pathsAndIndices = reduce(alternatives, (result, currAlt, idx) => {\n    const currPathsAndIdx = map(currAlt, currPath => {\n      return {\n        idx: idx,\n        path: currPath\n      };\n    });\n    return result.concat(currPathsAndIdx);\n  }, []);\n  const errors = compact(flatMap(pathsAndIndices, currPathAndIdx => {\n    const alternativeGast = alternation.definition[currPathAndIdx.idx];\n    // ignore (skip) ambiguities with this alternative\n    if (alternativeGast.ignoreAmbiguities === true) {\n      return [];\n    }\n    const targetIdx = currPathAndIdx.idx;\n    const targetPath = currPathAndIdx.path;\n    const prefixAmbiguitiesPathsAndIndices = filter(pathsAndIndices, searchPathAndIdx => {\n      // prefix ambiguity can only be created from lower idx (higher priority) path\n      return (\n        // ignore (skip) ambiguities with this \"other\" alternative\n        alternation.definition[searchPathAndIdx.idx].ignoreAmbiguities !== true && searchPathAndIdx.idx < targetIdx &&\n        // checking for strict prefix because identical lookaheads\n        // will be be detected using a different validation.\n        isStrictPrefixOfPath(searchPathAndIdx.path, targetPath)\n      );\n    });\n    const currPathPrefixErrors = map(prefixAmbiguitiesPathsAndIndices, currAmbPathAndIdx => {\n      const ambgIndices = [currAmbPathAndIdx.idx + 1, targetIdx + 1];\n      const occurrence = alternation.idx === 0 ? \"\" : alternation.idx;\n      const message = errMsgProvider.buildAlternationPrefixAmbiguityError({\n        topLevelRule: rule,\n        alternation: alternation,\n        ambiguityIndices: ambgIndices,\n        prefixPath: currAmbPathAndIdx.path\n      });\n      return {\n        message: message,\n        type: ParserDefinitionErrorType.AMBIGUOUS_PREFIX_ALTS,\n        ruleName: rule.name,\n        occurrence: occurrence,\n        alternatives: ambgIndices\n      };\n    });\n    return currPathPrefixErrors;\n  }));\n  return errors;\n}\nfunction checkTerminalAndNoneTerminalsNameSpace(topLevels, tokenTypes, errMsgProvider) {\n  const errors = [];\n  const tokenNames = map(tokenTypes, currToken => currToken.name);\n  forEach(topLevels, currRule => {\n    const currRuleName = currRule.name;\n    if (includes(tokenNames, currRuleName)) {\n      const errMsg = errMsgProvider.buildNamespaceConflictError(currRule);\n      errors.push({\n        message: errMsg,\n        type: ParserDefinitionErrorType.CONFLICT_TOKENS_RULES_NAMESPACE,\n        ruleName: currRuleName\n      });\n    }\n  });\n  return errors;\n}", "map": {"version": 3, "names": ["clone", "compact", "difference", "drop", "dropRight", "filter", "first", "flatMap", "flatten", "for<PERSON>ach", "groupBy", "includes", "isEmpty", "map", "pickBy", "reduce", "reject", "values", "ParserDefinitionErrorType", "Alternation", "Alternative", "AlternativeGAST", "GAstVisitor", "getProductionDslName", "isOptionalProd", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Terminal", "containsPath", "getLookaheadPathsForOptionalProd", "getLookaheadPathsForOr", "getProdType", "isStrictPrefixOfPath", "nextPossibleTokensAfter", "tokenStructuredMatcher", "validate<PERSON><PERSON><PERSON><PERSON>", "options", "lookaheadValidationErrorMessages", "lookaheadStrategy", "validate", "rules", "tokenTypes", "grammarName", "errorMessage", "Object", "assign", "type", "CUSTOM_LOOKAHEAD_VALIDATION", "validate<PERSON>rammar", "topLevels", "err<PERSON><PERSON><PERSON><PERSON><PERSON>", "duplicateErrors", "currTopLevel", "validateDuplicateProductions", "termsNamespaceConflictErrors", "checkTerminalAndNoneTerminalsNameSpace", "tooManyAltsErrors", "curRule", "validateTooManyAlts", "duplicateRulesError", "validateRuleDoesNotAlreadyExist", "concat", "topLevelRule", "collectorVisitor", "OccurrenceValidationCollector", "accept", "allRuleProductions", "allProductions", "productionGroups", "identifyProductionForDuplicates", "duplicates", "currGroup", "length", "errors", "currDuplicates", "firstProd", "msg", "buildDuplicateFoundError", "dslName", "defError", "message", "DUPLICATE_PRODUCTIONS", "ruleName", "name", "occurrence", "idx", "param", "getExtraProductionArgument", "parameter", "prod", "terminalType", "nonTerminalName", "constructor", "visitNonTerminal", "subrule", "push", "visitOption", "option", "visitRepetitionWithSeparator", "manySep", "visitRepetitionMandatory", "atLeastOne", "visitRepetitionMandatoryWithSeparator", "atLeastOneSep", "visitRepetition", "many", "visitAlternation", "or", "visitTerminal", "terminal", "rule", "allRules", "className", "occurrences", "result", "errMsg", "buildDuplicateRuleNameError", "DUPLICATE_RULE_NAME", "validateRuleIsOverridden", "definedRulesNames", "INVALID_RULE_OVERRIDE", "validateNoLeftRecursion", "topRule", "currRule", "path", "nextNonTerminals", "getFirstNoneTerminal", "definition", "foundLeftRecursion", "buildLeftRecursionError", "leftRecursionPath", "LEFT_RECURSION", "validNextSteps", "errorsFromNextSteps", "currRefRule", "newPath", "referencedRule", "currSubDef", "Error", "isFirstOptional", "hasMore", "rest", "OrCollector", "alternations", "node", "validateEmptyOrAlternative", "orCollector", "ors", "currOr", "exceptLast", "currAlternative", "currAltIdx", "possibleFirstInAlt", "buildEmptyAlternationError", "alternation", "emptyChoiceIdx", "NONE_LAST_EMPTY_ALT", "alternative", "validateAmbiguousAlternationAlternatives", "globalMaxLookahead", "ignoreAmbiguities", "currOccurrence", "actualMaxLookahead", "max<PERSON><PERSON><PERSON><PERSON>", "alternatives", "altsAmbiguityErrors", "checkAlternativesAmbiguities", "altsPrefixAmbiguityErrors", "checkPrefixAlternativesAmbiguities", "RepetitionCollector", "buildTooManyAlternativesError", "TOO_MANY_ALTS", "validateSomeNonEmptyLookaheadPath", "topLevelRules", "currTopRule", "currProd", "prodType", "paths", "pathsInsideProduction", "buildEmptyRepetitionError", "repetition", "NO_NON_EMPTY_LOOKAHEAD", "foundAmbiguousPaths", "identicalAmbiguities", "currAlt", "currPath", "altsCurrPathAppearsIn", "currOtherAlt", "currOtherAltIdx", "alts", "currErrors", "currAmbDescriptor", "ambgIndices", "currMessage", "buildAlternationAmbiguityError", "ambiguityIndices", "prefixPath", "AMBIGUOUS_ALTS", "pathsAndIndices", "currPathsAndIdx", "currPathAndIdx", "alternativeGast", "targetIdx", "targetPath", "prefixAmbiguitiesPathsAndIndices", "searchPathAndIdx", "currPathPrefixErrors", "currAmbPathAndIdx", "buildAlternationPrefixAmbiguityError", "AMBIGUOUS_PREFIX_ALTS", "tokenNames", "currToken", "currRuleName", "buildNamespaceConflictError", "CONFLICT_TOKENS_RULES_NAMESPACE"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/checks.ts"], "sourcesContent": ["import {\n  clone,\n  compact,\n  difference,\n  drop,\n  dropRight,\n  filter,\n  first,\n  flatMap,\n  flatten,\n  forEach,\n  groupBy,\n  includes,\n  isEmpty,\n  map,\n  pickBy,\n  reduce,\n  reject,\n  values,\n} from \"lodash-es\";\nimport {\n  IParserAmbiguousAlternativesDefinitionError,\n  IParserDuplicatesDefinitionError,\n  IParserEmptyAlternativeDefinitionError,\n  ParserDefinitionErrorType,\n} from \"../parser/parser.js\";\nimport {\n  Alternation,\n  Alternative as AlternativeGAST,\n  GAstVisitor,\n  getProductionDslName,\n  isOptionalProd,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport {\n  Alternative,\n  containsPath,\n  getLookaheadPathsForOptionalProd,\n  getLookaheadPathsForOr,\n  getProdType,\n  isStrictPrefixOfPath,\n} from \"./lookahead.js\";\nimport { nextPossibleTokensAfter } from \"./interpreter.js\";\nimport {\n  ILookaheadStrategy,\n  IProduction,\n  IProductionWithOccurrence,\n  Rule,\n  TokenType,\n} from \"@chevrotain/types\";\nimport {\n  IGrammarValidatorErrorMessageProvider,\n  IParserDefinitionError,\n} from \"./types.js\";\nimport { tokenStructuredMatcher } from \"../../scan/tokens.js\";\n\nexport function validateLookahead(options: {\n  lookaheadStrategy: ILookaheadStrategy;\n  rules: Rule[];\n  tokenTypes: TokenType[];\n  grammarName: string;\n}): IParserDefinitionError[] {\n  const lookaheadValidationErrorMessages = options.lookaheadStrategy.validate({\n    rules: options.rules,\n    tokenTypes: options.tokenTypes,\n    grammarName: options.grammarName,\n  });\n  return map(lookaheadValidationErrorMessages, (errorMessage) => ({\n    type: ParserDefinitionErrorType.CUSTOM_LOOKAHEAD_VALIDATION,\n    ...errorMessage,\n  }));\n}\n\nexport function validateGrammar(\n  topLevels: Rule[],\n  tokenTypes: TokenType[],\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n  grammarName: string,\n): IParserDefinitionError[] {\n  const duplicateErrors: IParserDefinitionError[] = flatMap(\n    topLevels,\n    (currTopLevel) =>\n      validateDuplicateProductions(currTopLevel, errMsgProvider),\n  );\n\n  const termsNamespaceConflictErrors = checkTerminalAndNoneTerminalsNameSpace(\n    topLevels,\n    tokenTypes,\n    errMsgProvider,\n  );\n\n  const tooManyAltsErrors = flatMap(topLevels, (curRule) =>\n    validateTooManyAlts(curRule, errMsgProvider),\n  );\n\n  const duplicateRulesError = flatMap(topLevels, (curRule) =>\n    validateRuleDoesNotAlreadyExist(\n      curRule,\n      topLevels,\n      grammarName,\n      errMsgProvider,\n    ),\n  );\n\n  return duplicateErrors.concat(\n    termsNamespaceConflictErrors,\n    tooManyAltsErrors,\n    duplicateRulesError,\n  );\n}\n\nfunction validateDuplicateProductions(\n  topLevelRule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserDuplicatesDefinitionError[] {\n  const collectorVisitor = new OccurrenceValidationCollector();\n  topLevelRule.accept(collectorVisitor);\n  const allRuleProductions = collectorVisitor.allProductions;\n\n  const productionGroups = groupBy(\n    allRuleProductions,\n    identifyProductionForDuplicates,\n  );\n\n  const duplicates: any = pickBy(productionGroups, (currGroup) => {\n    return currGroup.length > 1;\n  });\n\n  const errors = map(values(duplicates), (currDuplicates: any) => {\n    const firstProd: any = first(currDuplicates);\n    const msg = errMsgProvider.buildDuplicateFoundError(\n      topLevelRule,\n      currDuplicates,\n    );\n    const dslName = getProductionDslName(firstProd);\n    const defError: IParserDuplicatesDefinitionError = {\n      message: msg,\n      type: ParserDefinitionErrorType.DUPLICATE_PRODUCTIONS,\n      ruleName: topLevelRule.name,\n      dslName: dslName,\n      occurrence: firstProd.idx,\n    };\n\n    const param = getExtraProductionArgument(firstProd);\n    if (param) {\n      defError.parameter = param;\n    }\n\n    return defError;\n  });\n  return errors;\n}\n\nexport function identifyProductionForDuplicates(\n  prod: IProductionWithOccurrence,\n): string {\n  return `${getProductionDslName(prod)}_#_${\n    prod.idx\n  }_#_${getExtraProductionArgument(prod)}`;\n}\n\nfunction getExtraProductionArgument(prod: IProductionWithOccurrence): string {\n  if (prod instanceof Terminal) {\n    return prod.terminalType.name;\n  } else if (prod instanceof NonTerminal) {\n    return prod.nonTerminalName;\n  } else {\n    return \"\";\n  }\n}\n\nexport class OccurrenceValidationCollector extends GAstVisitor {\n  public allProductions: IProductionWithOccurrence[] = [];\n\n  public visitNonTerminal(subrule: NonTerminal): void {\n    this.allProductions.push(subrule);\n  }\n\n  public visitOption(option: Option): void {\n    this.allProductions.push(option);\n  }\n\n  public visitRepetitionWithSeparator(manySep: RepetitionWithSeparator): void {\n    this.allProductions.push(manySep);\n  }\n\n  public visitRepetitionMandatory(atLeastOne: RepetitionMandatory): void {\n    this.allProductions.push(atLeastOne);\n  }\n\n  public visitRepetitionMandatoryWithSeparator(\n    atLeastOneSep: RepetitionMandatoryWithSeparator,\n  ): void {\n    this.allProductions.push(atLeastOneSep);\n  }\n\n  public visitRepetition(many: Repetition): void {\n    this.allProductions.push(many);\n  }\n\n  public visitAlternation(or: Alternation): void {\n    this.allProductions.push(or);\n  }\n\n  public visitTerminal(terminal: Terminal): void {\n    this.allProductions.push(terminal);\n  }\n}\n\nexport function validateRuleDoesNotAlreadyExist(\n  rule: Rule,\n  allRules: Rule[],\n  className: string,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserDefinitionError[] {\n  const errors = [];\n  const occurrences = reduce(\n    allRules,\n    (result, curRule) => {\n      if (curRule.name === rule.name) {\n        return result + 1;\n      }\n      return result;\n    },\n    0,\n  );\n  if (occurrences > 1) {\n    const errMsg = errMsgProvider.buildDuplicateRuleNameError({\n      topLevelRule: rule,\n      grammarName: className,\n    });\n    errors.push({\n      message: errMsg,\n      type: ParserDefinitionErrorType.DUPLICATE_RULE_NAME,\n      ruleName: rule.name,\n    });\n  }\n\n  return errors;\n}\n\n// TODO: is there anyway to get only the rule names of rules inherited from the super grammars?\n// This is not part of the IGrammarErrorProvider because the validation cannot be performed on\n// The grammar structure, only at runtime.\nexport function validateRuleIsOverridden(\n  ruleName: string,\n  definedRulesNames: string[],\n  className: string,\n): IParserDefinitionError[] {\n  const errors = [];\n  let errMsg;\n\n  if (!includes(definedRulesNames, ruleName)) {\n    errMsg =\n      `Invalid rule override, rule: ->${ruleName}<- cannot be overridden in the grammar: ->${className}<-` +\n      `as it is not defined in any of the super grammars `;\n    errors.push({\n      message: errMsg,\n      type: ParserDefinitionErrorType.INVALID_RULE_OVERRIDE,\n      ruleName: ruleName,\n    });\n  }\n\n  return errors;\n}\n\nexport function validateNoLeftRecursion(\n  topRule: Rule,\n  currRule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n  path: Rule[] = [],\n): IParserDefinitionError[] {\n  const errors: IParserDefinitionError[] = [];\n  const nextNonTerminals = getFirstNoneTerminal(currRule.definition);\n  if (isEmpty(nextNonTerminals)) {\n    return [];\n  } else {\n    const ruleName = topRule.name;\n    const foundLeftRecursion = includes(nextNonTerminals, topRule);\n    if (foundLeftRecursion) {\n      errors.push({\n        message: errMsgProvider.buildLeftRecursionError({\n          topLevelRule: topRule,\n          leftRecursionPath: path,\n        }),\n        type: ParserDefinitionErrorType.LEFT_RECURSION,\n        ruleName: ruleName,\n      });\n    }\n\n    // we are only looking for cyclic paths leading back to the specific topRule\n    // other cyclic paths are ignored, we still need this difference to avoid infinite loops...\n    const validNextSteps = difference(nextNonTerminals, path.concat([topRule]));\n    const errorsFromNextSteps = flatMap(validNextSteps, (currRefRule) => {\n      const newPath = clone(path);\n      newPath.push(currRefRule);\n      return validateNoLeftRecursion(\n        topRule,\n        currRefRule,\n        errMsgProvider,\n        newPath,\n      );\n    });\n\n    return errors.concat(errorsFromNextSteps);\n  }\n}\n\nexport function getFirstNoneTerminal(definition: IProduction[]): Rule[] {\n  let result: Rule[] = [];\n  if (isEmpty(definition)) {\n    return result;\n  }\n  const firstProd = first(definition);\n\n  /* istanbul ignore else */\n  if (firstProd instanceof NonTerminal) {\n    result.push(firstProd.referencedRule);\n  } else if (\n    firstProd instanceof AlternativeGAST ||\n    firstProd instanceof Option ||\n    firstProd instanceof RepetitionMandatory ||\n    firstProd instanceof RepetitionMandatoryWithSeparator ||\n    firstProd instanceof RepetitionWithSeparator ||\n    firstProd instanceof Repetition\n  ) {\n    result = result.concat(\n      getFirstNoneTerminal(<IProduction[]>firstProd.definition),\n    );\n  } else if (firstProd instanceof Alternation) {\n    // each sub definition in alternation is a FLAT\n    result = flatten(\n      map(firstProd.definition, (currSubDef) =>\n        getFirstNoneTerminal((<AlternativeGAST>currSubDef).definition),\n      ),\n    );\n  } else if (firstProd instanceof Terminal) {\n    // nothing to see, move along\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n\n  const isFirstOptional = isOptionalProd(firstProd);\n  const hasMore = definition.length > 1;\n  if (isFirstOptional && hasMore) {\n    const rest = drop(definition);\n    return result.concat(getFirstNoneTerminal(rest));\n  } else {\n    return result;\n  }\n}\n\nclass OrCollector extends GAstVisitor {\n  public alternations: Alternation[] = [];\n\n  public visitAlternation(node: Alternation): void {\n    this.alternations.push(node);\n  }\n}\n\nexport function validateEmptyOrAlternative(\n  topLevelRule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserEmptyAlternativeDefinitionError[] {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  const ors = orCollector.alternations;\n\n  const errors = flatMap<Alternation, IParserEmptyAlternativeDefinitionError>(\n    ors,\n    (currOr) => {\n      const exceptLast = dropRight(currOr.definition);\n      return flatMap(exceptLast, (currAlternative, currAltIdx) => {\n        const possibleFirstInAlt = nextPossibleTokensAfter(\n          [currAlternative],\n          [],\n          tokenStructuredMatcher,\n          1,\n        );\n        if (isEmpty(possibleFirstInAlt)) {\n          return [\n            {\n              message: errMsgProvider.buildEmptyAlternationError({\n                topLevelRule: topLevelRule,\n                alternation: currOr,\n                emptyChoiceIdx: currAltIdx,\n              }),\n              type: ParserDefinitionErrorType.NONE_LAST_EMPTY_ALT,\n              ruleName: topLevelRule.name,\n              occurrence: currOr.idx,\n              alternative: currAltIdx + 1,\n            },\n          ];\n        } else {\n          return [];\n        }\n      });\n    },\n  );\n\n  return errors;\n}\n\nexport function validateAmbiguousAlternationAlternatives(\n  topLevelRule: Rule,\n  globalMaxLookahead: number,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserAmbiguousAlternativesDefinitionError[] {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  let ors = orCollector.alternations;\n\n  // New Handling of ignoring ambiguities\n  // - https://github.com/chevrotain/chevrotain/issues/869\n  ors = reject(ors, (currOr) => currOr.ignoreAmbiguities === true);\n\n  const errors = flatMap(ors, (currOr: Alternation) => {\n    const currOccurrence = currOr.idx;\n    const actualMaxLookahead = currOr.maxLookahead || globalMaxLookahead;\n    const alternatives = getLookaheadPathsForOr(\n      currOccurrence,\n      topLevelRule,\n      actualMaxLookahead,\n      currOr,\n    );\n    const altsAmbiguityErrors = checkAlternativesAmbiguities(\n      alternatives,\n      currOr,\n      topLevelRule,\n      errMsgProvider,\n    );\n    const altsPrefixAmbiguityErrors = checkPrefixAlternativesAmbiguities(\n      alternatives,\n      currOr,\n      topLevelRule,\n      errMsgProvider,\n    );\n\n    return altsAmbiguityErrors.concat(altsPrefixAmbiguityErrors);\n  });\n\n  return errors;\n}\n\nexport class RepetitionCollector extends GAstVisitor {\n  public allProductions: (IProductionWithOccurrence & {\n    maxLookahead?: number;\n  })[] = [];\n\n  public visitRepetitionWithSeparator(manySep: RepetitionWithSeparator): void {\n    this.allProductions.push(manySep);\n  }\n\n  public visitRepetitionMandatory(atLeastOne: RepetitionMandatory): void {\n    this.allProductions.push(atLeastOne);\n  }\n\n  public visitRepetitionMandatoryWithSeparator(\n    atLeastOneSep: RepetitionMandatoryWithSeparator,\n  ): void {\n    this.allProductions.push(atLeastOneSep);\n  }\n\n  public visitRepetition(many: Repetition): void {\n    this.allProductions.push(many);\n  }\n}\n\nexport function validateTooManyAlts(\n  topLevelRule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserDefinitionError[] {\n  const orCollector = new OrCollector();\n  topLevelRule.accept(orCollector);\n  const ors = orCollector.alternations;\n\n  const errors = flatMap(ors, (currOr) => {\n    if (currOr.definition.length > 255) {\n      return [\n        {\n          message: errMsgProvider.buildTooManyAlternativesError({\n            topLevelRule: topLevelRule,\n            alternation: currOr,\n          }),\n          type: ParserDefinitionErrorType.TOO_MANY_ALTS,\n          ruleName: topLevelRule.name,\n          occurrence: currOr.idx,\n        },\n      ];\n    } else {\n      return [];\n    }\n  });\n\n  return errors;\n}\n\nexport function validateSomeNonEmptyLookaheadPath(\n  topLevelRules: Rule[],\n  maxLookahead: number,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserDefinitionError[] {\n  const errors: IParserDefinitionError[] = [];\n  forEach(topLevelRules, (currTopRule) => {\n    const collectorVisitor = new RepetitionCollector();\n    currTopRule.accept(collectorVisitor);\n    const allRuleProductions = collectorVisitor.allProductions;\n    forEach(allRuleProductions, (currProd) => {\n      const prodType = getProdType(currProd);\n      const actualMaxLookahead = currProd.maxLookahead || maxLookahead;\n      const currOccurrence = currProd.idx;\n      const paths = getLookaheadPathsForOptionalProd(\n        currOccurrence,\n        currTopRule,\n        prodType,\n        actualMaxLookahead,\n      );\n      const pathsInsideProduction = paths[0];\n      if (isEmpty(flatten(pathsInsideProduction))) {\n        const errMsg = errMsgProvider.buildEmptyRepetitionError({\n          topLevelRule: currTopRule,\n          repetition: currProd,\n        });\n        errors.push({\n          message: errMsg,\n          type: ParserDefinitionErrorType.NO_NON_EMPTY_LOOKAHEAD,\n          ruleName: currTopRule.name,\n        });\n      }\n    });\n  });\n\n  return errors;\n}\n\nexport interface IAmbiguityDescriptor {\n  alts: number[];\n  path: TokenType[];\n}\n\nfunction checkAlternativesAmbiguities(\n  alternatives: Alternative[],\n  alternation: Alternation,\n  rule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserAmbiguousAlternativesDefinitionError[] {\n  const foundAmbiguousPaths: Alternative = [];\n  const identicalAmbiguities = reduce(\n    alternatives,\n    (result, currAlt, currAltIdx) => {\n      // ignore (skip) ambiguities with this alternative\n      if (alternation.definition[currAltIdx].ignoreAmbiguities === true) {\n        return result;\n      }\n\n      forEach(currAlt, (currPath) => {\n        const altsCurrPathAppearsIn = [currAltIdx];\n        forEach(alternatives, (currOtherAlt, currOtherAltIdx) => {\n          if (\n            currAltIdx !== currOtherAltIdx &&\n            containsPath(currOtherAlt, currPath) &&\n            // ignore (skip) ambiguities with this \"other\" alternative\n            alternation.definition[currOtherAltIdx].ignoreAmbiguities !== true\n          ) {\n            altsCurrPathAppearsIn.push(currOtherAltIdx);\n          }\n        });\n\n        if (\n          altsCurrPathAppearsIn.length > 1 &&\n          !containsPath(foundAmbiguousPaths, currPath)\n        ) {\n          foundAmbiguousPaths.push(currPath);\n          result.push({\n            alts: altsCurrPathAppearsIn,\n            path: currPath,\n          });\n        }\n      });\n      return result;\n    },\n    [] as { alts: number[]; path: TokenType[] }[],\n  );\n\n  const currErrors = map(identicalAmbiguities, (currAmbDescriptor) => {\n    const ambgIndices = map(\n      currAmbDescriptor.alts,\n      (currAltIdx) => currAltIdx + 1,\n    );\n\n    const currMessage = errMsgProvider.buildAlternationAmbiguityError({\n      topLevelRule: rule,\n      alternation: alternation,\n      ambiguityIndices: ambgIndices,\n      prefixPath: currAmbDescriptor.path,\n    });\n\n    return {\n      message: currMessage,\n      type: ParserDefinitionErrorType.AMBIGUOUS_ALTS,\n      ruleName: rule.name,\n      occurrence: alternation.idx,\n      alternatives: currAmbDescriptor.alts,\n    };\n  });\n\n  return currErrors;\n}\n\nexport function checkPrefixAlternativesAmbiguities(\n  alternatives: Alternative[],\n  alternation: Alternation,\n  rule: Rule,\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserAmbiguousAlternativesDefinitionError[] {\n  // flatten\n  const pathsAndIndices = reduce(\n    alternatives,\n    (result, currAlt, idx) => {\n      const currPathsAndIdx = map(currAlt, (currPath) => {\n        return { idx: idx, path: currPath };\n      });\n      return result.concat(currPathsAndIdx);\n    },\n    [] as { idx: number; path: TokenType[] }[],\n  );\n\n  const errors = compact(\n    flatMap(pathsAndIndices, (currPathAndIdx) => {\n      const alternativeGast = alternation.definition[currPathAndIdx.idx];\n      // ignore (skip) ambiguities with this alternative\n      if (alternativeGast.ignoreAmbiguities === true) {\n        return [];\n      }\n      const targetIdx = currPathAndIdx.idx;\n      const targetPath = currPathAndIdx.path;\n\n      const prefixAmbiguitiesPathsAndIndices = filter(\n        pathsAndIndices,\n        (searchPathAndIdx) => {\n          // prefix ambiguity can only be created from lower idx (higher priority) path\n          return (\n            // ignore (skip) ambiguities with this \"other\" alternative\n            alternation.definition[searchPathAndIdx.idx].ignoreAmbiguities !==\n              true &&\n            searchPathAndIdx.idx < targetIdx &&\n            // checking for strict prefix because identical lookaheads\n            // will be be detected using a different validation.\n            isStrictPrefixOfPath(searchPathAndIdx.path, targetPath)\n          );\n        },\n      );\n\n      const currPathPrefixErrors = map(\n        prefixAmbiguitiesPathsAndIndices,\n        (currAmbPathAndIdx): IParserAmbiguousAlternativesDefinitionError => {\n          const ambgIndices = [currAmbPathAndIdx.idx + 1, targetIdx + 1];\n          const occurrence = alternation.idx === 0 ? \"\" : alternation.idx;\n\n          const message = errMsgProvider.buildAlternationPrefixAmbiguityError({\n            topLevelRule: rule,\n            alternation: alternation,\n            ambiguityIndices: ambgIndices,\n            prefixPath: currAmbPathAndIdx.path,\n          });\n          return {\n            message: message,\n            type: ParserDefinitionErrorType.AMBIGUOUS_PREFIX_ALTS,\n            ruleName: rule.name,\n            occurrence: occurrence,\n            alternatives: ambgIndices,\n          };\n        },\n      );\n\n      return currPathPrefixErrors;\n    }),\n  );\n\n  return errors;\n}\n\nfunction checkTerminalAndNoneTerminalsNameSpace(\n  topLevels: Rule[],\n  tokenTypes: TokenType[],\n  errMsgProvider: IGrammarValidatorErrorMessageProvider,\n): IParserDefinitionError[] {\n  const errors: IParserDefinitionError[] = [];\n\n  const tokenNames = map(tokenTypes, (currToken) => currToken.name);\n\n  forEach(topLevels, (currRule) => {\n    const currRuleName = currRule.name;\n    if (includes(tokenNames, currRuleName)) {\n      const errMsg = errMsgProvider.buildNamespaceConflictError(currRule);\n\n      errors.push({\n        message: errMsg,\n        type: ParserDefinitionErrorType.CONFLICT_TOKENS_RULES_NAMESPACE,\n        ruleName: currRuleName,\n      });\n    }\n  });\n\n  return errors;\n}\n"], "mappings": "AAAA,SACEA,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,QACD,WAAW;AAClB,SAIEC,yBAAyB,QACpB,qBAAqB;AAC5B,SACEC,WAAW,EACXC,WAAW,IAAIC,eAAe,EAC9BC,WAAW,EACXC,oBAAoB,EACpBC,cAAc,EACdC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,QAAQ,QACH,kBAAkB;AACzB,SAEEC,YAAY,EACZC,gCAAgC,EAChCC,sBAAsB,EACtBC,WAAW,EACXC,oBAAoB,QACf,gBAAgB;AACvB,SAASC,uBAAuB,QAAQ,kBAAkB;AAY1D,SAASC,sBAAsB,QAAQ,sBAAsB;AAE7D,OAAM,SAAUC,iBAAiBA,CAACC,OAKjC;EACC,MAAMC,gCAAgC,GAAGD,OAAO,CAACE,iBAAiB,CAACC,QAAQ,CAAC;IAC1EC,KAAK,EAAEJ,OAAO,CAACI,KAAK;IACpBC,UAAU,EAAEL,OAAO,CAACK,UAAU;IAC9BC,WAAW,EAAEN,OAAO,CAACM;GACtB,CAAC;EACF,OAAOjC,GAAG,CAAC4B,gCAAgC,EAAGM,YAAY,IAAKC,MAAA,CAAAC,MAAA;IAC7DC,IAAI,EAAEhC,yBAAyB,CAACiC;EAA2B,GACxDJ,YAAY,CACf,CAAC;AACL;AAEA,OAAM,SAAUK,eAAeA,CAC7BC,SAAiB,EACjBR,UAAuB,EACvBS,cAAqD,EACrDR,WAAmB;EAEnB,MAAMS,eAAe,GAA6BhD,OAAO,CACvD8C,SAAS,EACRG,YAAY,IACXC,4BAA4B,CAACD,YAAY,EAAEF,cAAc,CAAC,CAC7D;EAED,MAAMI,4BAA4B,GAAGC,sCAAsC,CACzEN,SAAS,EACTR,UAAU,EACVS,cAAc,CACf;EAED,MAAMM,iBAAiB,GAAGrD,OAAO,CAAC8C,SAAS,EAAGQ,OAAO,IACnDC,mBAAmB,CAACD,OAAO,EAAEP,cAAc,CAAC,CAC7C;EAED,MAAMS,mBAAmB,GAAGxD,OAAO,CAAC8C,SAAS,EAAGQ,OAAO,IACrDG,+BAA+B,CAC7BH,OAAO,EACPR,SAAS,EACTP,WAAW,EACXQ,cAAc,CACf,CACF;EAED,OAAOC,eAAe,CAACU,MAAM,CAC3BP,4BAA4B,EAC5BE,iBAAiB,EACjBG,mBAAmB,CACpB;AACH;AAEA,SAASN,4BAA4BA,CACnCS,YAAkB,EAClBZ,cAAqD;EAErD,MAAMa,gBAAgB,GAAG,IAAIC,6BAA6B,EAAE;EAC5DF,YAAY,CAACG,MAAM,CAACF,gBAAgB,CAAC;EACrC,MAAMG,kBAAkB,GAAGH,gBAAgB,CAACI,cAAc;EAE1D,MAAMC,gBAAgB,GAAG9D,OAAO,CAC9B4D,kBAAkB,EAClBG,+BAA+B,CAChC;EAED,MAAMC,UAAU,GAAQ5D,MAAM,CAAC0D,gBAAgB,EAAGG,SAAS,IAAI;IAC7D,OAAOA,SAAS,CAACC,MAAM,GAAG,CAAC;EAC7B,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAGhE,GAAG,CAACI,MAAM,CAACyD,UAAU,CAAC,EAAGI,cAAmB,IAAI;IAC7D,MAAMC,SAAS,GAAQzE,KAAK,CAACwE,cAAc,CAAC;IAC5C,MAAME,GAAG,GAAG1B,cAAc,CAAC2B,wBAAwB,CACjDf,YAAY,EACZY,cAAc,CACf;IACD,MAAMI,OAAO,GAAG3D,oBAAoB,CAACwD,SAAS,CAAC;IAC/C,MAAMI,QAAQ,GAAqC;MACjDC,OAAO,EAAEJ,GAAG;MACZ9B,IAAI,EAAEhC,yBAAyB,CAACmE,qBAAqB;MACrDC,QAAQ,EAAEpB,YAAY,CAACqB,IAAI;MAC3BL,OAAO,EAAEA,OAAO;MAChBM,UAAU,EAAET,SAAS,CAACU;KACvB;IAED,MAAMC,KAAK,GAAGC,0BAA0B,CAACZ,SAAS,CAAC;IACnD,IAAIW,KAAK,EAAE;MACTP,QAAQ,CAACS,SAAS,GAAGF,KAAK;;IAG5B,OAAOP,QAAQ;EACjB,CAAC,CAAC;EACF,OAAON,MAAM;AACf;AAEA,OAAM,SAAUJ,+BAA+BA,CAC7CoB,IAA+B;EAE/B,OAAO,GAAGtE,oBAAoB,CAACsE,IAAI,CAAC,MAClCA,IAAI,CAACJ,GACP,MAAME,0BAA0B,CAACE,IAAI,CAAC,EAAE;AAC1C;AAEA,SAASF,0BAA0BA,CAACE,IAA+B;EACjE,IAAIA,IAAI,YAAY9D,QAAQ,EAAE;IAC5B,OAAO8D,IAAI,CAACC,YAAY,CAACP,IAAI;GAC9B,MAAM,IAAIM,IAAI,YAAYpE,WAAW,EAAE;IACtC,OAAOoE,IAAI,CAACE,eAAe;GAC5B,MAAM;IACL,OAAO,EAAE;;AAEb;AAEA,OAAM,MAAO3B,6BAA8B,SAAQ9C,WAAW;EAA9D0E,YAAA;;IACS,KAAAzB,cAAc,GAAgC,EAAE;EAmCzD;EAjCS0B,gBAAgBA,CAACC,OAAoB;IAC1C,IAAI,CAAC3B,cAAc,CAAC4B,IAAI,CAACD,OAAO,CAAC;EACnC;EAEOE,WAAWA,CAACC,MAAc;IAC/B,IAAI,CAAC9B,cAAc,CAAC4B,IAAI,CAACE,MAAM,CAAC;EAClC;EAEOC,4BAA4BA,CAACC,OAAgC;IAClE,IAAI,CAAChC,cAAc,CAAC4B,IAAI,CAACI,OAAO,CAAC;EACnC;EAEOC,wBAAwBA,CAACC,UAA+B;IAC7D,IAAI,CAAClC,cAAc,CAAC4B,IAAI,CAACM,UAAU,CAAC;EACtC;EAEOC,qCAAqCA,CAC1CC,aAA+C;IAE/C,IAAI,CAACpC,cAAc,CAAC4B,IAAI,CAACQ,aAAa,CAAC;EACzC;EAEOC,eAAeA,CAACC,IAAgB;IACrC,IAAI,CAACtC,cAAc,CAAC4B,IAAI,CAACU,IAAI,CAAC;EAChC;EAEOC,gBAAgBA,CAACC,EAAe;IACrC,IAAI,CAACxC,cAAc,CAAC4B,IAAI,CAACY,EAAE,CAAC;EAC9B;EAEOC,aAAaA,CAACC,QAAkB;IACrC,IAAI,CAAC1C,cAAc,CAAC4B,IAAI,CAACc,QAAQ,CAAC;EACpC;;AAGF,OAAM,SAAUjD,+BAA+BA,CAC7CkD,IAAU,EACVC,QAAgB,EAChBC,SAAiB,EACjB9D,cAAqD;EAErD,MAAMuB,MAAM,GAAG,EAAE;EACjB,MAAMwC,WAAW,GAAGtG,MAAM,CACxBoG,QAAQ,EACR,CAACG,MAAM,EAAEzD,OAAO,KAAI;IAClB,IAAIA,OAAO,CAAC0B,IAAI,KAAK2B,IAAI,CAAC3B,IAAI,EAAE;MAC9B,OAAO+B,MAAM,GAAG,CAAC;;IAEnB,OAAOA,MAAM;EACf,CAAC,EACD,CAAC,CACF;EACD,IAAID,WAAW,GAAG,CAAC,EAAE;IACnB,MAAME,MAAM,GAAGjE,cAAc,CAACkE,2BAA2B,CAAC;MACxDtD,YAAY,EAAEgD,IAAI;MAClBpE,WAAW,EAAEsE;KACd,CAAC;IACFvC,MAAM,CAACsB,IAAI,CAAC;MACVf,OAAO,EAAEmC,MAAM;MACfrE,IAAI,EAAEhC,yBAAyB,CAACuG,mBAAmB;MACnDnC,QAAQ,EAAE4B,IAAI,CAAC3B;KAChB,CAAC;;EAGJ,OAAOV,MAAM;AACf;AAEA;AACA;AACA;AACA,OAAM,SAAU6C,wBAAwBA,CACtCpC,QAAgB,EAChBqC,iBAA2B,EAC3BP,SAAiB;EAEjB,MAAMvC,MAAM,GAAG,EAAE;EACjB,IAAI0C,MAAM;EAEV,IAAI,CAAC5G,QAAQ,CAACgH,iBAAiB,EAAErC,QAAQ,CAAC,EAAE;IAC1CiC,MAAM,GACJ,kCAAkCjC,QAAQ,6CAA6C8B,SAAS,IAAI,GACpG,oDAAoD;IACtDvC,MAAM,CAACsB,IAAI,CAAC;MACVf,OAAO,EAAEmC,MAAM;MACfrE,IAAI,EAAEhC,yBAAyB,CAAC0G,qBAAqB;MACrDtC,QAAQ,EAAEA;KACX,CAAC;;EAGJ,OAAOT,MAAM;AACf;AAEA,OAAM,SAAUgD,uBAAuBA,CACrCC,OAAa,EACbC,QAAc,EACdzE,cAAqD,EACrD0E,IAAA,GAAe,EAAE;EAEjB,MAAMnD,MAAM,GAA6B,EAAE;EAC3C,MAAMoD,gBAAgB,GAAGC,oBAAoB,CAACH,QAAQ,CAACI,UAAU,CAAC;EAClE,IAAIvH,OAAO,CAACqH,gBAAgB,CAAC,EAAE;IAC7B,OAAO,EAAE;GACV,MAAM;IACL,MAAM3C,QAAQ,GAAGwC,OAAO,CAACvC,IAAI;IAC7B,MAAM6C,kBAAkB,GAAGzH,QAAQ,CAACsH,gBAAgB,EAAEH,OAAO,CAAC;IAC9D,IAAIM,kBAAkB,EAAE;MACtBvD,MAAM,CAACsB,IAAI,CAAC;QACVf,OAAO,EAAE9B,cAAc,CAAC+E,uBAAuB,CAAC;UAC9CnE,YAAY,EAAE4D,OAAO;UACrBQ,iBAAiB,EAAEN;SACpB,CAAC;QACF9E,IAAI,EAAEhC,yBAAyB,CAACqH,cAAc;QAC9CjD,QAAQ,EAAEA;OACX,CAAC;;IAGJ;IACA;IACA,MAAMkD,cAAc,GAAGtI,UAAU,CAAC+H,gBAAgB,EAAED,IAAI,CAAC/D,MAAM,CAAC,CAAC6D,OAAO,CAAC,CAAC,CAAC;IAC3E,MAAMW,mBAAmB,GAAGlI,OAAO,CAACiI,cAAc,EAAGE,WAAW,IAAI;MAClE,MAAMC,OAAO,GAAG3I,KAAK,CAACgI,IAAI,CAAC;MAC3BW,OAAO,CAACxC,IAAI,CAACuC,WAAW,CAAC;MACzB,OAAOb,uBAAuB,CAC5BC,OAAO,EACPY,WAAW,EACXpF,cAAc,EACdqF,OAAO,CACR;IACH,CAAC,CAAC;IAEF,OAAO9D,MAAM,CAACZ,MAAM,CAACwE,mBAAmB,CAAC;;AAE7C;AAEA,OAAM,SAAUP,oBAAoBA,CAACC,UAAyB;EAC5D,IAAIb,MAAM,GAAW,EAAE;EACvB,IAAI1G,OAAO,CAACuH,UAAU,CAAC,EAAE;IACvB,OAAOb,MAAM;;EAEf,MAAMvC,SAAS,GAAGzE,KAAK,CAAC6H,UAAU,CAAC;EAEnC;EACA,IAAIpD,SAAS,YAAYtD,WAAW,EAAE;IACpC6F,MAAM,CAACnB,IAAI,CAACpB,SAAS,CAAC6D,cAAc,CAAC;GACtC,MAAM,IACL7D,SAAS,YAAY1D,eAAe,IACpC0D,SAAS,YAAYrD,MAAM,IAC3BqD,SAAS,YAAYnD,mBAAmB,IACxCmD,SAAS,YAAYlD,gCAAgC,IACrDkD,SAAS,YAAYjD,uBAAuB,IAC5CiD,SAAS,YAAYpD,UAAU,EAC/B;IACA2F,MAAM,GAAGA,MAAM,CAACrD,MAAM,CACpBiE,oBAAoB,CAAgBnD,SAAS,CAACoD,UAAU,CAAC,CAC1D;GACF,MAAM,IAAIpD,SAAS,YAAY5D,WAAW,EAAE;IAC3C;IACAmG,MAAM,GAAG9G,OAAO,CACdK,GAAG,CAACkE,SAAS,CAACoD,UAAU,EAAGU,UAAU,IACnCX,oBAAoB,CAAmBW,UAAW,CAACV,UAAU,CAAC,CAC/D,CACF;GACF,MAAM,IAAIpD,SAAS,YAAYhD,QAAQ,EAAE;IACxC;EAAA,CACD,MAAM;IACL,MAAM+G,KAAK,CAAC,sBAAsB,CAAC;;EAGrC,MAAMC,eAAe,GAAGvH,cAAc,CAACuD,SAAS,CAAC;EACjD,MAAMiE,OAAO,GAAGb,UAAU,CAACvD,MAAM,GAAG,CAAC;EACrC,IAAImE,eAAe,IAAIC,OAAO,EAAE;IAC9B,MAAMC,IAAI,GAAG9I,IAAI,CAACgI,UAAU,CAAC;IAC7B,OAAOb,MAAM,CAACrD,MAAM,CAACiE,oBAAoB,CAACe,IAAI,CAAC,CAAC;GACjD,MAAM;IACL,OAAO3B,MAAM;;AAEjB;AAEA,MAAM4B,WAAY,SAAQ5H,WAAW;EAArC0E,YAAA;;IACS,KAAAmD,YAAY,GAAkB,EAAE;EAKzC;EAHSrC,gBAAgBA,CAACsC,IAAiB;IACvC,IAAI,CAACD,YAAY,CAAChD,IAAI,CAACiD,IAAI,CAAC;EAC9B;;AAGF,OAAM,SAAUC,0BAA0BA,CACxCnF,YAAkB,EAClBZ,cAAqD;EAErD,MAAMgG,WAAW,GAAG,IAAIJ,WAAW,EAAE;EACrChF,YAAY,CAACG,MAAM,CAACiF,WAAW,CAAC;EAChC,MAAMC,GAAG,GAAGD,WAAW,CAACH,YAAY;EAEpC,MAAMtE,MAAM,GAAGtE,OAAO,CACpBgJ,GAAG,EACFC,MAAM,IAAI;IACT,MAAMC,UAAU,GAAGrJ,SAAS,CAACoJ,MAAM,CAACrB,UAAU,CAAC;IAC/C,OAAO5H,OAAO,CAACkJ,UAAU,EAAE,CAACC,eAAe,EAAEC,UAAU,KAAI;MACzD,MAAMC,kBAAkB,GAAGvH,uBAAuB,CAChD,CAACqH,eAAe,CAAC,EACjB,EAAE,EACFpH,sBAAsB,EACtB,CAAC,CACF;MACD,IAAI1B,OAAO,CAACgJ,kBAAkB,CAAC,EAAE;QAC/B,OAAO,CACL;UACExE,OAAO,EAAE9B,cAAc,CAACuG,0BAA0B,CAAC;YACjD3F,YAAY,EAAEA,YAAY;YAC1B4F,WAAW,EAAEN,MAAM;YACnBO,cAAc,EAAEJ;WACjB,CAAC;UACFzG,IAAI,EAAEhC,yBAAyB,CAAC8I,mBAAmB;UACnD1E,QAAQ,EAAEpB,YAAY,CAACqB,IAAI;UAC3BC,UAAU,EAAEgE,MAAM,CAAC/D,GAAG;UACtBwE,WAAW,EAAEN,UAAU,GAAG;SAC3B,CACF;OACF,MAAM;QACL,OAAO,EAAE;;IAEb,CAAC,CAAC;EACJ,CAAC,CACF;EAED,OAAO9E,MAAM;AACf;AAEA,OAAM,SAAUqF,wCAAwCA,CACtDhG,YAAkB,EAClBiG,kBAA0B,EAC1B7G,cAAqD;EAErD,MAAMgG,WAAW,GAAG,IAAIJ,WAAW,EAAE;EACrChF,YAAY,CAACG,MAAM,CAACiF,WAAW,CAAC;EAChC,IAAIC,GAAG,GAAGD,WAAW,CAACH,YAAY;EAElC;EACA;EACAI,GAAG,GAAGvI,MAAM,CAACuI,GAAG,EAAGC,MAAM,IAAKA,MAAM,CAACY,iBAAiB,KAAK,IAAI,CAAC;EAEhE,MAAMvF,MAAM,GAAGtE,OAAO,CAACgJ,GAAG,EAAGC,MAAmB,IAAI;IAClD,MAAMa,cAAc,GAAGb,MAAM,CAAC/D,GAAG;IACjC,MAAM6E,kBAAkB,GAAGd,MAAM,CAACe,YAAY,IAAIJ,kBAAkB;IACpE,MAAMK,YAAY,GAAGtI,sBAAsB,CACzCmI,cAAc,EACdnG,YAAY,EACZoG,kBAAkB,EAClBd,MAAM,CACP;IACD,MAAMiB,mBAAmB,GAAGC,4BAA4B,CACtDF,YAAY,EACZhB,MAAM,EACNtF,YAAY,EACZZ,cAAc,CACf;IACD,MAAMqH,yBAAyB,GAAGC,kCAAkC,CAClEJ,YAAY,EACZhB,MAAM,EACNtF,YAAY,EACZZ,cAAc,CACf;IAED,OAAOmH,mBAAmB,CAACxG,MAAM,CAAC0G,yBAAyB,CAAC;EAC9D,CAAC,CAAC;EAEF,OAAO9F,MAAM;AACf;AAEA,OAAM,MAAOgG,mBAAoB,SAAQvJ,WAAW;EAApD0E,YAAA;;IACS,KAAAzB,cAAc,GAEd,EAAE;EAmBX;EAjBS+B,4BAA4BA,CAACC,OAAgC;IAClE,IAAI,CAAChC,cAAc,CAAC4B,IAAI,CAACI,OAAO,CAAC;EACnC;EAEOC,wBAAwBA,CAACC,UAA+B;IAC7D,IAAI,CAAClC,cAAc,CAAC4B,IAAI,CAACM,UAAU,CAAC;EACtC;EAEOC,qCAAqCA,CAC1CC,aAA+C;IAE/C,IAAI,CAACpC,cAAc,CAAC4B,IAAI,CAACQ,aAAa,CAAC;EACzC;EAEOC,eAAeA,CAACC,IAAgB;IACrC,IAAI,CAACtC,cAAc,CAAC4B,IAAI,CAACU,IAAI,CAAC;EAChC;;AAGF,OAAM,SAAU/C,mBAAmBA,CACjCI,YAAkB,EAClBZ,cAAqD;EAErD,MAAMgG,WAAW,GAAG,IAAIJ,WAAW,EAAE;EACrChF,YAAY,CAACG,MAAM,CAACiF,WAAW,CAAC;EAChC,MAAMC,GAAG,GAAGD,WAAW,CAACH,YAAY;EAEpC,MAAMtE,MAAM,GAAGtE,OAAO,CAACgJ,GAAG,EAAGC,MAAM,IAAI;IACrC,IAAIA,MAAM,CAACrB,UAAU,CAACvD,MAAM,GAAG,GAAG,EAAE;MAClC,OAAO,CACL;QACEQ,OAAO,EAAE9B,cAAc,CAACwH,6BAA6B,CAAC;UACpD5G,YAAY,EAAEA,YAAY;UAC1B4F,WAAW,EAAEN;SACd,CAAC;QACFtG,IAAI,EAAEhC,yBAAyB,CAAC6J,aAAa;QAC7CzF,QAAQ,EAAEpB,YAAY,CAACqB,IAAI;QAC3BC,UAAU,EAAEgE,MAAM,CAAC/D;OACpB,CACF;KACF,MAAM;MACL,OAAO,EAAE;;EAEb,CAAC,CAAC;EAEF,OAAOZ,MAAM;AACf;AAEA,OAAM,SAAUmG,iCAAiCA,CAC/CC,aAAqB,EACrBV,YAAoB,EACpBjH,cAAqD;EAErD,MAAMuB,MAAM,GAA6B,EAAE;EAC3CpE,OAAO,CAACwK,aAAa,EAAGC,WAAW,IAAI;IACrC,MAAM/G,gBAAgB,GAAG,IAAI0G,mBAAmB,EAAE;IAClDK,WAAW,CAAC7G,MAAM,CAACF,gBAAgB,CAAC;IACpC,MAAMG,kBAAkB,GAAGH,gBAAgB,CAACI,cAAc;IAC1D9D,OAAO,CAAC6D,kBAAkB,EAAG6G,QAAQ,IAAI;MACvC,MAAMC,QAAQ,GAAGjJ,WAAW,CAACgJ,QAAQ,CAAC;MACtC,MAAMb,kBAAkB,GAAGa,QAAQ,CAACZ,YAAY,IAAIA,YAAY;MAChE,MAAMF,cAAc,GAAGc,QAAQ,CAAC1F,GAAG;MACnC,MAAM4F,KAAK,GAAGpJ,gCAAgC,CAC5CoI,cAAc,EACda,WAAW,EACXE,QAAQ,EACRd,kBAAkB,CACnB;MACD,MAAMgB,qBAAqB,GAAGD,KAAK,CAAC,CAAC,CAAC;MACtC,IAAIzK,OAAO,CAACJ,OAAO,CAAC8K,qBAAqB,CAAC,CAAC,EAAE;QAC3C,MAAM/D,MAAM,GAAGjE,cAAc,CAACiI,yBAAyB,CAAC;UACtDrH,YAAY,EAAEgH,WAAW;UACzBM,UAAU,EAAEL;SACb,CAAC;QACFtG,MAAM,CAACsB,IAAI,CAAC;UACVf,OAAO,EAAEmC,MAAM;UACfrE,IAAI,EAAEhC,yBAAyB,CAACuK,sBAAsB;UACtDnG,QAAQ,EAAE4F,WAAW,CAAC3F;SACvB,CAAC;;IAEN,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOV,MAAM;AACf;AAOA,SAAS6F,4BAA4BA,CACnCF,YAA2B,EAC3BV,WAAwB,EACxB5C,IAAU,EACV5D,cAAqD;EAErD,MAAMoI,mBAAmB,GAAgB,EAAE;EAC3C,MAAMC,oBAAoB,GAAG5K,MAAM,CACjCyJ,YAAY,EACZ,CAAClD,MAAM,EAAEsE,OAAO,EAAEjC,UAAU,KAAI;IAC9B;IACA,IAAIG,WAAW,CAAC3B,UAAU,CAACwB,UAAU,CAAC,CAACS,iBAAiB,KAAK,IAAI,EAAE;MACjE,OAAO9C,MAAM;;IAGf7G,OAAO,CAACmL,OAAO,EAAGC,QAAQ,IAAI;MAC5B,MAAMC,qBAAqB,GAAG,CAACnC,UAAU,CAAC;MAC1ClJ,OAAO,CAAC+J,YAAY,EAAE,CAACuB,YAAY,EAAEC,eAAe,KAAI;QACtD,IACErC,UAAU,KAAKqC,eAAe,IAC9BhK,YAAY,CAAC+J,YAAY,EAAEF,QAAQ,CAAC;QACpC;QACA/B,WAAW,CAAC3B,UAAU,CAAC6D,eAAe,CAAC,CAAC5B,iBAAiB,KAAK,IAAI,EAClE;UACA0B,qBAAqB,CAAC3F,IAAI,CAAC6F,eAAe,CAAC;;MAE/C,CAAC,CAAC;MAEF,IACEF,qBAAqB,CAAClH,MAAM,GAAG,CAAC,IAChC,CAAC5C,YAAY,CAAC0J,mBAAmB,EAAEG,QAAQ,CAAC,EAC5C;QACAH,mBAAmB,CAACvF,IAAI,CAAC0F,QAAQ,CAAC;QAClCvE,MAAM,CAACnB,IAAI,CAAC;UACV8F,IAAI,EAAEH,qBAAqB;UAC3B9D,IAAI,EAAE6D;SACP,CAAC;;IAEN,CAAC,CAAC;IACF,OAAOvE,MAAM;EACf,CAAC,EACD,EAA6C,CAC9C;EAED,MAAM4E,UAAU,GAAGrL,GAAG,CAAC8K,oBAAoB,EAAGQ,iBAAiB,IAAI;IACjE,MAAMC,WAAW,GAAGvL,GAAG,CACrBsL,iBAAiB,CAACF,IAAI,EACrBtC,UAAU,IAAKA,UAAU,GAAG,CAAC,CAC/B;IAED,MAAM0C,WAAW,GAAG/I,cAAc,CAACgJ,8BAA8B,CAAC;MAChEpI,YAAY,EAAEgD,IAAI;MAClB4C,WAAW,EAAEA,WAAW;MACxByC,gBAAgB,EAAEH,WAAW;MAC7BI,UAAU,EAAEL,iBAAiB,CAACnE;KAC/B,CAAC;IAEF,OAAO;MACL5C,OAAO,EAAEiH,WAAW;MACpBnJ,IAAI,EAAEhC,yBAAyB,CAACuL,cAAc;MAC9CnH,QAAQ,EAAE4B,IAAI,CAAC3B,IAAI;MACnBC,UAAU,EAAEsE,WAAW,CAACrE,GAAG;MAC3B+E,YAAY,EAAE2B,iBAAiB,CAACF;KACjC;EACH,CAAC,CAAC;EAEF,OAAOC,UAAU;AACnB;AAEA,OAAM,SAAUtB,kCAAkCA,CAChDJ,YAA2B,EAC3BV,WAAwB,EACxB5C,IAAU,EACV5D,cAAqD;EAErD;EACA,MAAMoJ,eAAe,GAAG3L,MAAM,CAC5ByJ,YAAY,EACZ,CAAClD,MAAM,EAAEsE,OAAO,EAAEnG,GAAG,KAAI;IACvB,MAAMkH,eAAe,GAAG9L,GAAG,CAAC+K,OAAO,EAAGC,QAAQ,IAAI;MAChD,OAAO;QAAEpG,GAAG,EAAEA,GAAG;QAAEuC,IAAI,EAAE6D;MAAQ,CAAE;IACrC,CAAC,CAAC;IACF,OAAOvE,MAAM,CAACrD,MAAM,CAAC0I,eAAe,CAAC;EACvC,CAAC,EACD,EAA0C,CAC3C;EAED,MAAM9H,MAAM,GAAG5E,OAAO,CACpBM,OAAO,CAACmM,eAAe,EAAGE,cAAc,IAAI;IAC1C,MAAMC,eAAe,GAAG/C,WAAW,CAAC3B,UAAU,CAACyE,cAAc,CAACnH,GAAG,CAAC;IAClE;IACA,IAAIoH,eAAe,CAACzC,iBAAiB,KAAK,IAAI,EAAE;MAC9C,OAAO,EAAE;;IAEX,MAAM0C,SAAS,GAAGF,cAAc,CAACnH,GAAG;IACpC,MAAMsH,UAAU,GAAGH,cAAc,CAAC5E,IAAI;IAEtC,MAAMgF,gCAAgC,GAAG3M,MAAM,CAC7CqM,eAAe,EACdO,gBAAgB,IAAI;MACnB;MACA;QACE;QACAnD,WAAW,CAAC3B,UAAU,CAAC8E,gBAAgB,CAACxH,GAAG,CAAC,CAAC2E,iBAAiB,KAC5D,IAAI,IACN6C,gBAAgB,CAACxH,GAAG,GAAGqH,SAAS;QAChC;QACA;QACA1K,oBAAoB,CAAC6K,gBAAgB,CAACjF,IAAI,EAAE+E,UAAU;MAAC;IAE3D,CAAC,CACF;IAED,MAAMG,oBAAoB,GAAGrM,GAAG,CAC9BmM,gCAAgC,EAC/BG,iBAAiB,IAAiD;MACjE,MAAMf,WAAW,GAAG,CAACe,iBAAiB,CAAC1H,GAAG,GAAG,CAAC,EAAEqH,SAAS,GAAG,CAAC,CAAC;MAC9D,MAAMtH,UAAU,GAAGsE,WAAW,CAACrE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAGqE,WAAW,CAACrE,GAAG;MAE/D,MAAML,OAAO,GAAG9B,cAAc,CAAC8J,oCAAoC,CAAC;QAClElJ,YAAY,EAAEgD,IAAI;QAClB4C,WAAW,EAAEA,WAAW;QACxByC,gBAAgB,EAAEH,WAAW;QAC7BI,UAAU,EAAEW,iBAAiB,CAACnF;OAC/B,CAAC;MACF,OAAO;QACL5C,OAAO,EAAEA,OAAO;QAChBlC,IAAI,EAAEhC,yBAAyB,CAACmM,qBAAqB;QACrD/H,QAAQ,EAAE4B,IAAI,CAAC3B,IAAI;QACnBC,UAAU,EAAEA,UAAU;QACtBgF,YAAY,EAAE4B;OACf;IACH,CAAC,CACF;IAED,OAAOc,oBAAoB;EAC7B,CAAC,CAAC,CACH;EAED,OAAOrI,MAAM;AACf;AAEA,SAASlB,sCAAsCA,CAC7CN,SAAiB,EACjBR,UAAuB,EACvBS,cAAqD;EAErD,MAAMuB,MAAM,GAA6B,EAAE;EAE3C,MAAMyI,UAAU,GAAGzM,GAAG,CAACgC,UAAU,EAAG0K,SAAS,IAAKA,SAAS,CAAChI,IAAI,CAAC;EAEjE9E,OAAO,CAAC4C,SAAS,EAAG0E,QAAQ,IAAI;IAC9B,MAAMyF,YAAY,GAAGzF,QAAQ,CAACxC,IAAI;IAClC,IAAI5E,QAAQ,CAAC2M,UAAU,EAAEE,YAAY,CAAC,EAAE;MACtC,MAAMjG,MAAM,GAAGjE,cAAc,CAACmK,2BAA2B,CAAC1F,QAAQ,CAAC;MAEnElD,MAAM,CAACsB,IAAI,CAAC;QACVf,OAAO,EAAEmC,MAAM;QACfrE,IAAI,EAAEhC,yBAAyB,CAACwM,+BAA+B;QAC/DpI,QAAQ,EAAEkI;OACX,CAAC;;EAEN,CAAC,CAAC;EAEF,OAAO3I,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}