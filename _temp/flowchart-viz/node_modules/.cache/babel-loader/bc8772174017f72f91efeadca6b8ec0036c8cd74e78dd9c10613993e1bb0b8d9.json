{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken, CancellationTokenSource } from '../utils/cancellation.js';\n/**\n * Delays the execution of the current code to the next tick of the event loop.\n * Don't call this method directly in a tight loop to prevent too many promises from being created.\n */\nexport function delayNextTick() {\n  return new Promise(resolve => {\n    // In case we are running in a non-node environment, `setImmediate` isn't available.\n    // Using `setTimeout` of the browser API accomplishes the same result.\n    if (typeof setImmediate === 'undefined') {\n      setTimeout(resolve, 0);\n    } else {\n      setImmediate(resolve);\n    }\n  });\n}\nlet lastTick = 0;\nlet globalInterruptionPeriod = 10;\n/**\n * Reset the global interruption period and create a cancellation token source.\n */\nexport function startCancelableOperation() {\n  lastTick = performance.now();\n  return new CancellationTokenSource();\n}\n/**\n * Change the period duration for `interruptAndCheck` to the given number of milliseconds.\n * The default value is 10ms.\n */\nexport function setInterruptionPeriod(period) {\n  globalInterruptionPeriod = period;\n}\n/**\n * This symbol may be thrown in an asynchronous context by any Langium service that receives\n * a `CancellationToken`. This means that the promise returned by such a service is rejected with\n * this symbol as rejection reason.\n */\nexport const OperationCancelled = Symbol('OperationCancelled');\n/**\n * Use this in a `catch` block to check whether the thrown object indicates that the operation\n * has been cancelled.\n */\nexport function isOperationCancelled(err) {\n  return err === OperationCancelled;\n}\n/**\n * This function does two things:\n *  1. Check the elapsed time since the last call to this function or to `startCancelableOperation`. If the predefined\n *     period (configured with `setInterruptionPeriod`) is exceeded, execution is delayed with `delayNextTick`.\n *  2. If the predefined period is not met yet or execution is resumed after an interruption, the given cancellation\n *     token is checked, and if cancellation is requested, `OperationCanceled` is thrown.\n *\n * All services in Langium that receive a `CancellationToken` may potentially call this function, so the\n * `CancellationToken` must be caught (with an `async` try-catch block or a `catch` callback attached to\n * the promise) to avoid that event being exposed as an error.\n */\nexport async function interruptAndCheck(token) {\n  if (token === CancellationToken.None) {\n    // Early exit in case cancellation was disabled by the caller\n    return;\n  }\n  const current = performance.now();\n  if (current - lastTick >= globalInterruptionPeriod) {\n    lastTick = current;\n    await delayNextTick();\n    // prevent calling delayNextTick every iteration of loop\n    // where delayNextTick takes up the majority or all of the\n    // globalInterruptionPeriod itself\n    lastTick = performance.now();\n  }\n  if (token.isCancellationRequested) {\n    throw OperationCancelled;\n  }\n}\n/**\n * Simple implementation of the deferred pattern.\n * An object that exposes a promise and functions to resolve and reject it.\n */\nexport class Deferred {\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = arg => {\n        resolve(arg);\n        return this;\n      };\n      this.reject = err => {\n        reject(err);\n        return this;\n      };\n    });\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "CancellationTokenSource", "delayNextTick", "Promise", "resolve", "setImmediate", "setTimeout", "lastTick", "globalInterruptionPeriod", "startCancelableOperation", "performance", "now", "setInterruptionPeriod", "period", "OperationCancelled", "Symbol", "isOperationCancelled", "err", "interruptAndCheck", "token", "None", "current", "isCancellationRequested", "Deferred", "constructor", "promise", "reject", "arg"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/promise-utils.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { CancellationToken, CancellationTokenSource, type AbstractCancellationTokenSource } from '../utils/cancellation.js';\r\n\r\nexport type MaybePromise<T> = T | Promise<T>\r\n\r\n/**\r\n * Delays the execution of the current code to the next tick of the event loop.\r\n * Don't call this method directly in a tight loop to prevent too many promises from being created.\r\n */\r\nexport function delayNextTick(): Promise<void> {\r\n    return new Promise(resolve => {\r\n        // In case we are running in a non-node environment, `setImmediate` isn't available.\r\n        // Using `setTimeout` of the browser API accomplishes the same result.\r\n        if (typeof setImmediate === 'undefined') {\r\n            setTimeout(resolve, 0);\r\n        } else {\r\n            setImmediate(resolve);\r\n        }\r\n    });\r\n}\r\n\r\nlet lastTick = 0;\r\nlet globalInterruptionPeriod = 10;\r\n\r\n/**\r\n * Reset the global interruption period and create a cancellation token source.\r\n */\r\nexport function startCancelableOperation(): AbstractCancellationTokenSource {\r\n    lastTick = performance.now();\r\n    return new CancellationTokenSource();\r\n}\r\n\r\n/**\r\n * Change the period duration for `interruptAndCheck` to the given number of milliseconds.\r\n * The default value is 10ms.\r\n */\r\nexport function setInterruptionPeriod(period: number): void {\r\n    globalInterruptionPeriod = period;\r\n}\r\n\r\n/**\r\n * This symbol may be thrown in an asynchronous context by any Langium service that receives\r\n * a `CancellationToken`. This means that the promise returned by such a service is rejected with\r\n * this symbol as rejection reason.\r\n */\r\nexport const OperationCancelled = Symbol('OperationCancelled');\r\n\r\n/**\r\n * Use this in a `catch` block to check whether the thrown object indicates that the operation\r\n * has been cancelled.\r\n */\r\nexport function isOperationCancelled(err: unknown): err is typeof OperationCancelled {\r\n    return err === OperationCancelled;\r\n}\r\n\r\n/**\r\n * This function does two things:\r\n *  1. Check the elapsed time since the last call to this function or to `startCancelableOperation`. If the predefined\r\n *     period (configured with `setInterruptionPeriod`) is exceeded, execution is delayed with `delayNextTick`.\r\n *  2. If the predefined period is not met yet or execution is resumed after an interruption, the given cancellation\r\n *     token is checked, and if cancellation is requested, `OperationCanceled` is thrown.\r\n *\r\n * All services in Langium that receive a `CancellationToken` may potentially call this function, so the\r\n * `CancellationToken` must be caught (with an `async` try-catch block or a `catch` callback attached to\r\n * the promise) to avoid that event being exposed as an error.\r\n */\r\nexport async function interruptAndCheck(token: CancellationToken): Promise<void> {\r\n    if (token === CancellationToken.None) {\r\n        // Early exit in case cancellation was disabled by the caller\r\n        return;\r\n    }\r\n    const current = performance.now();\r\n    if (current - lastTick >= globalInterruptionPeriod) {\r\n        lastTick = current;\r\n        await delayNextTick();\r\n        // prevent calling delayNextTick every iteration of loop\r\n        // where delayNextTick takes up the majority or all of the\r\n        // globalInterruptionPeriod itself\r\n        lastTick = performance.now();\r\n    }\r\n    if (token.isCancellationRequested) {\r\n        throw OperationCancelled;\r\n    }\r\n}\r\n\r\n/**\r\n * Simple implementation of the deferred pattern.\r\n * An object that exposes a promise and functions to resolve and reject it.\r\n */\r\nexport class Deferred<T = void> {\r\n    resolve: (value: T) => this;\r\n    reject: (err?: unknown) => this;\r\n\r\n    promise = new Promise<T>((resolve, reject) => {\r\n        this.resolve = (arg) => {\r\n            resolve(arg);\r\n            return this;\r\n        };\r\n        this.reject = (err) => {\r\n            reject(err);\r\n            return this;\r\n        };\r\n    });\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAASA,iBAAiB,EAAEC,uBAAuB,QAA8C,0BAA0B;AAI3H;;;;AAIA,OAAM,SAAUC,aAAaA,CAAA;EACzB,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAG;IACzB;IACA;IACA,IAAI,OAAOC,YAAY,KAAK,WAAW,EAAE;MACrCC,UAAU,CAACF,OAAO,EAAE,CAAC,CAAC;IAC1B,CAAC,MAAM;MACHC,YAAY,CAACD,OAAO,CAAC;IACzB;EACJ,CAAC,CAAC;AACN;AAEA,IAAIG,QAAQ,GAAG,CAAC;AAChB,IAAIC,wBAAwB,GAAG,EAAE;AAEjC;;;AAGA,OAAM,SAAUC,wBAAwBA,CAAA;EACpCF,QAAQ,GAAGG,WAAW,CAACC,GAAG,EAAE;EAC5B,OAAO,IAAIV,uBAAuB,EAAE;AACxC;AAEA;;;;AAIA,OAAM,SAAUW,qBAAqBA,CAACC,MAAc;EAChDL,wBAAwB,GAAGK,MAAM;AACrC;AAEA;;;;;AAKA,OAAO,MAAMC,kBAAkB,GAAGC,MAAM,CAAC,oBAAoB,CAAC;AAE9D;;;;AAIA,OAAM,SAAUC,oBAAoBA,CAACC,GAAY;EAC7C,OAAOA,GAAG,KAAKH,kBAAkB;AACrC;AAEA;;;;;;;;;;;AAWA,OAAO,eAAeI,iBAAiBA,CAACC,KAAwB;EAC5D,IAAIA,KAAK,KAAKnB,iBAAiB,CAACoB,IAAI,EAAE;IAClC;IACA;EACJ;EACA,MAAMC,OAAO,GAAGX,WAAW,CAACC,GAAG,EAAE;EACjC,IAAIU,OAAO,GAAGd,QAAQ,IAAIC,wBAAwB,EAAE;IAChDD,QAAQ,GAAGc,OAAO;IAClB,MAAMnB,aAAa,EAAE;IACrB;IACA;IACA;IACAK,QAAQ,GAAGG,WAAW,CAACC,GAAG,EAAE;EAChC;EACA,IAAIQ,KAAK,CAACG,uBAAuB,EAAE;IAC/B,MAAMR,kBAAkB;EAC5B;AACJ;AAEA;;;;AAIA,OAAM,MAAOS,QAAQ;EAArBC,YAAA;IAII,KAAAC,OAAO,GAAG,IAAItB,OAAO,CAAI,CAACC,OAAO,EAAEsB,MAAM,KAAI;MACzC,IAAI,CAACtB,OAAO,GAAIuB,GAAG,IAAI;QACnBvB,OAAO,CAACuB,GAAG,CAAC;QACZ,OAAO,IAAI;MACf,CAAC;MACD,IAAI,CAACD,MAAM,GAAIT,GAAG,IAAI;QAClBS,MAAM,CAACT,GAAG,CAAC;QACX,OAAO,IAAI;MACf,CAAC;IACL,CAAC,CAAC;EACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}