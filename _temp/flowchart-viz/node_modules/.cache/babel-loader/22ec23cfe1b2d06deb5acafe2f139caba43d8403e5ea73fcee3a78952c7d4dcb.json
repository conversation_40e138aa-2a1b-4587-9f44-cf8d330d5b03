{"ast": null, "code": "import { RestWalker } from \"./rest.js\";\nimport { first } from \"./first.js\";\nimport { assign, forEach } from \"lodash-es\";\nimport { IN } from \"../constants.js\";\nimport { Alternative } from \"@chevrotain/gast\";\n// This ResyncFollowsWalker computes all of the follows required for RESYNC\n// (skipping reference production).\nexport class ResyncFollowsWalker extends RestWalker {\n  constructor(topProd) {\n    super();\n    this.topProd = topProd;\n    this.follows = {};\n  }\n  startWalking() {\n    this.walk(this.topProd);\n    return this.follows;\n  }\n  walkTerminal(terminal, currRest, prevRest) {\n    // do nothing! just like in the public sector after 13:00\n  }\n  walkProdRef(refProd, currRest, prevRest) {\n    const followName = buildBetweenProdsFollowPrefix(refProd.referencedRule, refProd.idx) + this.topProd.name;\n    const fullRest = currRest.concat(prevRest);\n    const restProd = new Alternative({\n      definition: fullRest\n    });\n    const t_in_topProd_follows = first(restProd);\n    this.follows[followName] = t_in_topProd_follows;\n  }\n}\nexport function computeAllProdsFollows(topProductions) {\n  const reSyncFollows = {};\n  forEach(topProductions, topProd => {\n    const currRefsFollow = new ResyncFollowsWalker(topProd).startWalking();\n    assign(reSyncFollows, currRefsFollow);\n  });\n  return reSyncFollows;\n}\nexport function buildBetweenProdsFollowPrefix(inner, occurenceInParent) {\n  return inner.name + occurenceInParent + IN;\n}\nexport function buildInProdFollowPrefix(terminal) {\n  const terminalName = terminal.terminalType.name;\n  return terminalName + terminal.idx + IN;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "first", "assign", "for<PERSON>ach", "IN", "Alternative", "ResyncFollowsWalker", "constructor", "topProd", "follows", "startWalking", "walk", "walkTerminal", "terminal", "currRest", "prevRest", "walkProdRef", "refProd", "follow<PERSON>ame", "buildBetweenProdsFollowPrefix", "referencedRule", "idx", "name", "fullRest", "concat", "restProd", "definition", "t_in_topProd_follows", "computeAllProdsFollows", "topProductions", "reSyncFollows", "currRefsFollow", "inner", "occurenceInParent", "buildInProdFollowPrefix", "terminalName", "terminalType"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/follow.ts"], "sourcesContent": ["import { RestWalker } from \"./rest.js\";\nimport { first } from \"./first.js\";\nimport { assign, forEach } from \"lodash-es\";\nimport { IN } from \"../constants.js\";\nimport { Alternative, NonTerminal, Rule, Terminal } from \"@chevrotain/gast\";\nimport { IProduction, TokenType } from \"@chevrotain/types\";\n\n// This ResyncFollowsWalker computes all of the follows required for RESYNC\n// (skipping reference production).\nexport class ResyncFollowsWalker extends RestWalker {\n  public follows: Record<string, TokenType[]> = {};\n\n  constructor(private topProd: Rule) {\n    super();\n  }\n\n  startWalking(): Record<string, TokenType[]> {\n    this.walk(this.topProd);\n    return this.follows;\n  }\n\n  walkTerminal(\n    terminal: Terminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // do nothing! just like in the public sector after 13:00\n  }\n\n  walkProdRef(\n    refProd: NonTerminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    const followName =\n      buildBetweenProdsFollowPrefix(refProd.referencedRule, refProd.idx) +\n      this.topProd.name;\n    const fullRest: IProduction[] = currRest.concat(prevRest);\n    const restProd = new Alternative({ definition: fullRest });\n    const t_in_topProd_follows = first(restProd);\n    this.follows[followName] = t_in_topProd_follows;\n  }\n}\n\nexport function computeAllProdsFollows(\n  topProductions: Rule[],\n): Record<string, TokenType[]> {\n  const reSyncFollows = {};\n\n  forEach(topProductions, (topProd) => {\n    const currRefsFollow = new ResyncFollowsWalker(topProd).startWalking();\n    assign(reSyncFollows, currRefsFollow);\n  });\n  return reSyncFollows;\n}\n\nexport function buildBetweenProdsFollowPrefix(\n  inner: Rule,\n  occurenceInParent: number,\n): string {\n  return inner.name + occurenceInParent + IN;\n}\n\nexport function buildInProdFollowPrefix(terminal: Terminal): string {\n  const terminalName = terminal.terminalType.name;\n  return terminalName + terminal.idx + IN;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,WAAW;AACtC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,MAAM,EAAEC,OAAO,QAAQ,WAAW;AAC3C,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,WAAW,QAAqC,kBAAkB;AAG3E;AACA;AACA,OAAM,MAAOC,mBAAoB,SAAQN,UAAU;EAGjDO,YAAoBC,OAAa;IAC/B,KAAK,EAAE;IADW,KAAAA,OAAO,GAAPA,OAAO;IAFpB,KAAAC,OAAO,GAAgC,EAAE;EAIhD;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC;IACvB,OAAO,IAAI,CAACC,OAAO;EACrB;EAEAG,YAAYA,CACVC,QAAkB,EAClBC,QAAuB,EACvBC,QAAuB;IAEvB;EAAA;EAGFC,WAAWA,CACTC,OAAoB,EACpBH,QAAuB,EACvBC,QAAuB;IAEvB,MAAMG,UAAU,GACdC,6BAA6B,CAACF,OAAO,CAACG,cAAc,EAAEH,OAAO,CAACI,GAAG,CAAC,GAClE,IAAI,CAACb,OAAO,CAACc,IAAI;IACnB,MAAMC,QAAQ,GAAkBT,QAAQ,CAACU,MAAM,CAACT,QAAQ,CAAC;IACzD,MAAMU,QAAQ,GAAG,IAAIpB,WAAW,CAAC;MAAEqB,UAAU,EAAEH;IAAQ,CAAE,CAAC;IAC1D,MAAMI,oBAAoB,GAAG1B,KAAK,CAACwB,QAAQ,CAAC;IAC5C,IAAI,CAAChB,OAAO,CAACS,UAAU,CAAC,GAAGS,oBAAoB;EACjD;;AAGF,OAAM,SAAUC,sBAAsBA,CACpCC,cAAsB;EAEtB,MAAMC,aAAa,GAAG,EAAE;EAExB3B,OAAO,CAAC0B,cAAc,EAAGrB,OAAO,IAAI;IAClC,MAAMuB,cAAc,GAAG,IAAIzB,mBAAmB,CAACE,OAAO,CAAC,CAACE,YAAY,EAAE;IACtER,MAAM,CAAC4B,aAAa,EAAEC,cAAc,CAAC;EACvC,CAAC,CAAC;EACF,OAAOD,aAAa;AACtB;AAEA,OAAM,SAAUX,6BAA6BA,CAC3Ca,KAAW,EACXC,iBAAyB;EAEzB,OAAOD,KAAK,CAACV,IAAI,GAAGW,iBAAiB,GAAG7B,EAAE;AAC5C;AAEA,OAAM,SAAU8B,uBAAuBA,CAACrB,QAAkB;EACxD,MAAMsB,YAAY,GAAGtB,QAAQ,CAACuB,YAAY,CAACd,IAAI;EAC/C,OAAOa,YAAY,GAAGtB,QAAQ,CAACQ,GAAG,GAAGjB,EAAE;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}