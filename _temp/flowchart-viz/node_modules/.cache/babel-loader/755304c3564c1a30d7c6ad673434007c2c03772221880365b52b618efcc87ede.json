{"ast": null, "code": "export function cc(char) {\n  return char.charCodeAt(0);\n}\nexport function insertToSet(item, set) {\n  if (Array.isArray(item)) {\n    item.forEach(function (subItem) {\n      set.push(subItem);\n    });\n  } else {\n    set.push(item);\n  }\n}\nexport function addFlag(flagObj, flagKey) {\n  if (flagObj[flagKey] === true) {\n    throw \"duplicate flag \" + flagKey;\n  }\n  const x = flagObj[flagKey];\n  flagObj[flagKey] = true;\n}\nexport function ASSERT_EXISTS(obj) {\n  // istanbul ignore next\n  if (obj === undefined) {\n    throw Error(\"Internal Error - Should never get here!\");\n  }\n  return true;\n}\n// istanbul ignore next\nexport function ASSERT_NEVER_REACH_HERE() {\n  throw Error(\"Internal Error - Should never get here!\");\n}\nexport function isCharacter(obj) {\n  return obj[\"type\"] === \"Character\";\n}", "map": {"version": 3, "names": ["cc", "char", "charCodeAt", "insertToSet", "item", "set", "Array", "isArray", "for<PERSON>ach", "subItem", "push", "addFlag", "flagObj", "<PERSON><PERSON><PERSON>", "x", "ASSERT_EXISTS", "obj", "undefined", "Error", "ASSERT_NEVER_REACH_HERE", "isCharacter"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/regexp-to-ast/src/utils.ts"], "sourcesContent": ["import type { Character, IRegExpAST, RegExpFlags } from \"../types\";\n\nexport function cc(char: string): number {\n  return char.charCodeAt(0);\n}\n\nexport function insertToSet<T>(item: T | T[], set: T[]) {\n  if (Array.isArray(item)) {\n    item.forEach(function (subItem) {\n      set.push(subItem);\n    });\n  } else {\n    set.push(item);\n  }\n}\n\nexport function addFlag(\n  flagObj: RegExpFlags,\n  flagKey: keyof Omit<RegExpFlags, keyof IRegExpAST>,\n) {\n  if (flagObj[flagKey] === true) {\n    throw \"duplicate flag \" + flagKey;\n  }\n\n  const x: boolean = flagObj[flagKey];\n  flagObj[flagKey] = true;\n}\n\nexport function ASSERT_EXISTS<T = Object>(obj: any): obj is T {\n  // istanbul ignore next\n  if (obj === undefined) {\n    throw Error(\"Internal Error - Should never get here!\");\n  }\n  return true;\n}\n\n// istanbul ignore next\nexport function ASSERT_NEVER_REACH_HERE(): any {\n  throw Error(\"Internal Error - Should never get here!\");\n}\n\nexport function isCharacter(obj: { type: string }): obj is Character {\n  return obj[\"type\"] === \"Character\";\n}\n"], "mappings": "AAEA,OAAM,SAAUA,EAAEA,CAACC,IAAY;EAC7B,OAAOA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;AAC3B;AAEA,OAAM,SAAUC,WAAWA,CAAIC,IAAa,EAAEC,GAAQ;EACpD,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IACvBA,IAAI,CAACI,OAAO,CAAC,UAAUC,OAAO;MAC5BJ,GAAG,CAACK,IAAI,CAACD,OAAO,CAAC;IACnB,CAAC,CAAC;GACH,MAAM;IACLJ,GAAG,CAACK,IAAI,CAACN,IAAI,CAAC;;AAElB;AAEA,OAAM,SAAUO,OAAOA,CACrBC,OAAoB,EACpBC,OAAkD;EAElD,IAAID,OAAO,CAACC,OAAO,CAAC,KAAK,IAAI,EAAE;IAC7B,MAAM,iBAAiB,GAAGA,OAAO;;EAGnC,MAAMC,CAAC,GAAYF,OAAO,CAACC,OAAO,CAAC;EACnCD,OAAO,CAACC,OAAO,CAAC,GAAG,IAAI;AACzB;AAEA,OAAM,SAAUE,aAAaA,CAAaC,GAAQ;EAChD;EACA,IAAIA,GAAG,KAAKC,SAAS,EAAE;IACrB,MAAMC,KAAK,CAAC,yCAAyC,CAAC;;EAExD,OAAO,IAAI;AACb;AAEA;AACA,OAAM,SAAUC,uBAAuBA,CAAA;EACrC,MAAMD,KAAK,CAAC,yCAAyC,CAAC;AACxD;AAEA,OAAM,SAAUE,WAAWA,CAACJ,GAAqB;EAC/C,OAAOA,GAAG,CAAC,MAAM,CAAC,KAAK,WAAW;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}