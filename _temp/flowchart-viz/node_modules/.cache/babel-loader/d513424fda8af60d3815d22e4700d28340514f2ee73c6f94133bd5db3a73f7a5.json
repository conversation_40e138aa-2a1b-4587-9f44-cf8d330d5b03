{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport class DisposableCache {\n  constructor() {\n    this.toDispose = [];\n    this.isDisposed = false;\n  }\n  onDispose(disposable) {\n    this.toDispose.push(disposable);\n  }\n  dispose() {\n    this.throwIfDisposed();\n    this.clear();\n    this.isDisposed = true;\n    this.toDispose.forEach(disposable => disposable.dispose());\n  }\n  throwIfDisposed() {\n    if (this.isDisposed) {\n      throw new Error('This cache has already been disposed');\n    }\n  }\n}\nexport class SimpleCache extends DisposableCache {\n  constructor() {\n    super(...arguments);\n    this.cache = new Map();\n  }\n  has(key) {\n    this.throwIfDisposed();\n    return this.cache.has(key);\n  }\n  set(key, value) {\n    this.throwIfDisposed();\n    this.cache.set(key, value);\n  }\n  get(key, provider) {\n    this.throwIfDisposed();\n    if (this.cache.has(key)) {\n      return this.cache.get(key);\n    } else if (provider) {\n      const value = provider();\n      this.cache.set(key, value);\n      return value;\n    } else {\n      return undefined;\n    }\n  }\n  delete(key) {\n    this.throwIfDisposed();\n    return this.cache.delete(key);\n  }\n  clear() {\n    this.throwIfDisposed();\n    this.cache.clear();\n  }\n}\nexport class ContextCache extends DisposableCache {\n  constructor(converter) {\n    super();\n    this.cache = new Map();\n    this.converter = converter !== null && converter !== void 0 ? converter : value => value;\n  }\n  has(contextKey, key) {\n    this.throwIfDisposed();\n    return this.cacheForContext(contextKey).has(key);\n  }\n  set(contextKey, key, value) {\n    this.throwIfDisposed();\n    this.cacheForContext(contextKey).set(key, value);\n  }\n  get(contextKey, key, provider) {\n    this.throwIfDisposed();\n    const contextCache = this.cacheForContext(contextKey);\n    if (contextCache.has(key)) {\n      return contextCache.get(key);\n    } else if (provider) {\n      const value = provider();\n      contextCache.set(key, value);\n      return value;\n    } else {\n      return undefined;\n    }\n  }\n  delete(contextKey, key) {\n    this.throwIfDisposed();\n    return this.cacheForContext(contextKey).delete(key);\n  }\n  clear(contextKey) {\n    this.throwIfDisposed();\n    if (contextKey) {\n      const mapKey = this.converter(contextKey);\n      this.cache.delete(mapKey);\n    } else {\n      this.cache.clear();\n    }\n  }\n  cacheForContext(contextKey) {\n    const mapKey = this.converter(contextKey);\n    let documentCache = this.cache.get(mapKey);\n    if (!documentCache) {\n      documentCache = new Map();\n      this.cache.set(mapKey, documentCache);\n    }\n    return documentCache;\n  }\n}\n/**\n * Every key/value pair in this cache is scoped to a document.\n * If this document is changed or deleted, all associated key/value pairs are deleted.\n */\nexport class DocumentCache extends ContextCache {\n  /**\n   * Creates a new document cache.\n   *\n   * @param sharedServices Service container instance to hook into document lifecycle events.\n   * @param state Optional document state on which the cache should evict.\n   * If not provided, the cache will evict on `DocumentBuilder#onUpdate`.\n   * *Deleted* documents are considered in both cases.\n   *\n   * Providing a state here will use `DocumentBuilder#onDocumentPhase` instead,\n   * which triggers on all documents that have been affected by this change, assuming that the\n   * state is `DocumentState.Linked` or a later state.\n   */\n  constructor(sharedServices, state) {\n    super(uri => uri.toString());\n    if (state) {\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onDocumentPhase(state, document => {\n        this.clear(document.uri.toString());\n      }));\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((_changed, deleted) => {\n        for (const uri of deleted) {\n          // react only on deleted documents\n          this.clear(uri);\n        }\n      }));\n    } else {\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((changed, deleted) => {\n        const allUris = changed.concat(deleted); // react on both changed and deleted documents\n        for (const uri of allUris) {\n          this.clear(uri);\n        }\n      }));\n    }\n  }\n}\n/**\n * Every key/value pair in this cache is scoped to the whole workspace.\n * If any document in the workspace is added, changed or deleted, the whole cache is evicted.\n */\nexport class WorkspaceCache extends SimpleCache {\n  /**\n   * Creates a new workspace cache.\n   *\n   * @param sharedServices Service container instance to hook into document lifecycle events.\n   * @param state Optional document state on which the cache should evict.\n   * If not provided, the cache will evict on `DocumentBuilder#onUpdate`.\n   * *Deleted* documents are considered in both cases.\n   */\n  constructor(sharedServices, state) {\n    super();\n    if (state) {\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onBuildPhase(state, () => {\n        this.clear();\n      }));\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((_changed, deleted) => {\n        if (deleted.length > 0) {\n          // react only on deleted documents\n          this.clear();\n        }\n      }));\n    } else {\n      this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate(() => {\n        this.clear();\n      }));\n    }\n  }\n}", "map": {"version": 3, "names": ["DisposableCache", "constructor", "toDispose", "isDisposed", "onDispose", "disposable", "push", "dispose", "throwIfDisposed", "clear", "for<PERSON>ach", "Error", "SimpleCache", "cache", "Map", "has", "key", "set", "value", "get", "provider", "undefined", "delete", "ContextCache", "converter", "<PERSON><PERSON>ey", "cacheForContext", "contextCache", "mapKey", "documentCache", "DocumentCache", "sharedServices", "state", "uri", "toString", "workspace", "DocumentBuilder", "onDocumentPhase", "document", "onUpdate", "_changed", "deleted", "changed", "allUris", "concat", "WorkspaceCache", "onBuildPhase", "length"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/caching.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { Disposable } from './disposable.js';\r\nimport type { URI } from './uri-utils.js';\r\nimport type { LangiumSharedCoreServices } from '../services.js';\r\nimport type { DocumentState } from '../workspace/documents.js';\r\n\r\nexport abstract class DisposableCache implements Disposable {\r\n\r\n    protected toDispose: Disposable[] = [];\r\n    protected isDisposed = false;\r\n\r\n    onDispose(disposable: Disposable): void {\r\n        this.toDispose.push(disposable);\r\n    }\r\n\r\n    dispose(): void {\r\n        this.throwIfDisposed();\r\n        this.clear();\r\n        this.isDisposed = true;\r\n        this.toDispose.forEach(disposable => disposable.dispose());\r\n    }\r\n\r\n    protected throwIfDisposed(): void {\r\n        if (this.isDisposed) {\r\n            throw new Error('This cache has already been disposed');\r\n        }\r\n    }\r\n\r\n    abstract clear(): void;\r\n}\r\n\r\nexport class SimpleCache<K, V> extends DisposableCache {\r\n    protected readonly cache = new Map<K, V>();\r\n\r\n    has(key: K): boolean {\r\n        this.throwIfDisposed();\r\n        return this.cache.has(key);\r\n    }\r\n\r\n    set(key: K, value: V): void {\r\n        this.throwIfDisposed();\r\n        this.cache.set(key, value);\r\n    }\r\n\r\n    get(key: K): V | undefined;\r\n    get(key: K, provider: () => V): V;\r\n    get(key: K, provider?: () => V): V | undefined {\r\n        this.throwIfDisposed();\r\n        if (this.cache.has(key)) {\r\n            return this.cache.get(key);\r\n        } else if (provider) {\r\n            const value = provider();\r\n            this.cache.set(key, value);\r\n            return value;\r\n        } else {\r\n            return undefined;\r\n        }\r\n    }\r\n\r\n    delete(key: K): boolean {\r\n        this.throwIfDisposed();\r\n        return this.cache.delete(key);\r\n    }\r\n\r\n    clear(): void {\r\n        this.throwIfDisposed();\r\n        this.cache.clear();\r\n    }\r\n}\r\n\r\nexport class ContextCache<Context, Key, Value, ContextKey = Context> extends DisposableCache {\r\n\r\n    private readonly cache = new Map<ContextKey | Context, Map<Key, Value>>();\r\n    private readonly converter: (input: Context) => ContextKey | Context;\r\n\r\n    constructor(converter?: (input: Context) => ContextKey) {\r\n        super();\r\n        this.converter = converter ?? (value => value);\r\n    }\r\n\r\n    has(contextKey: Context, key: Key): boolean {\r\n        this.throwIfDisposed();\r\n        return this.cacheForContext(contextKey).has(key);\r\n    }\r\n\r\n    set(contextKey: Context, key: Key, value: Value): void {\r\n        this.throwIfDisposed();\r\n        this.cacheForContext(contextKey).set(key, value);\r\n    }\r\n\r\n    get(contextKey: Context, key: Key): Value | undefined;\r\n    get(contextKey: Context, key: Key, provider: () => Value): Value;\r\n    get(contextKey: Context, key: Key, provider?: () => Value): Value | undefined {\r\n        this.throwIfDisposed();\r\n        const contextCache = this.cacheForContext(contextKey);\r\n        if (contextCache.has(key)) {\r\n            return contextCache.get(key);\r\n        } else if (provider) {\r\n            const value = provider();\r\n            contextCache.set(key, value);\r\n            return value;\r\n        } else {\r\n            return undefined;\r\n        }\r\n    }\r\n\r\n    delete(contextKey: Context, key: Key): boolean {\r\n        this.throwIfDisposed();\r\n        return this.cacheForContext(contextKey).delete(key);\r\n    }\r\n\r\n    clear(): void;\r\n    clear(contextKey: Context): void;\r\n    clear(contextKey?: Context): void {\r\n        this.throwIfDisposed();\r\n        if (contextKey) {\r\n            const mapKey = this.converter(contextKey);\r\n            this.cache.delete(mapKey);\r\n        } else {\r\n            this.cache.clear();\r\n        }\r\n    }\r\n\r\n    protected cacheForContext(contextKey: Context): Map<Key, Value> {\r\n        const mapKey = this.converter(contextKey);\r\n        let documentCache = this.cache.get(mapKey);\r\n        if (!documentCache) {\r\n            documentCache = new Map();\r\n            this.cache.set(mapKey, documentCache);\r\n        }\r\n        return documentCache;\r\n    }\r\n}\r\n\r\n/**\r\n * Every key/value pair in this cache is scoped to a document.\r\n * If this document is changed or deleted, all associated key/value pairs are deleted.\r\n */\r\nexport class DocumentCache<K, V> extends ContextCache<URI | string, K, V, string> {\r\n\r\n    /**\r\n     * Creates a new document cache.\r\n     *\r\n     * @param sharedServices Service container instance to hook into document lifecycle events.\r\n     * @param state Optional document state on which the cache should evict.\r\n     * If not provided, the cache will evict on `DocumentBuilder#onUpdate`.\r\n     * *Deleted* documents are considered in both cases.\r\n     *\r\n     * Providing a state here will use `DocumentBuilder#onDocumentPhase` instead,\r\n     * which triggers on all documents that have been affected by this change, assuming that the\r\n     * state is `DocumentState.Linked` or a later state.\r\n     */\r\n    constructor(sharedServices: LangiumSharedCoreServices, state?: DocumentState) {\r\n        super(uri => uri.toString());\r\n        if (state) {\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onDocumentPhase(state, document => {\r\n                this.clear(document.uri.toString());\r\n            }));\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((_changed, deleted) => {\r\n                for (const uri of deleted) { // react only on deleted documents\r\n                    this.clear(uri);\r\n                }\r\n            }));\r\n        } else {\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((changed, deleted) => {\r\n                const allUris = changed.concat(deleted); // react on both changed and deleted documents\r\n                for (const uri of allUris) {\r\n                    this.clear(uri);\r\n                }\r\n            }));\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Every key/value pair in this cache is scoped to the whole workspace.\r\n * If any document in the workspace is added, changed or deleted, the whole cache is evicted.\r\n */\r\nexport class WorkspaceCache<K, V> extends SimpleCache<K, V> {\r\n\r\n    /**\r\n     * Creates a new workspace cache.\r\n     *\r\n     * @param sharedServices Service container instance to hook into document lifecycle events.\r\n     * @param state Optional document state on which the cache should evict.\r\n     * If not provided, the cache will evict on `DocumentBuilder#onUpdate`.\r\n     * *Deleted* documents are considered in both cases.\r\n     */\r\n    constructor(sharedServices: LangiumSharedCoreServices, state?: DocumentState) {\r\n        super();\r\n        if (state) {\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onBuildPhase(state, () => {\r\n                this.clear();\r\n            }));\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate((_changed, deleted) => {\r\n                if (deleted.length > 0) { // react only on deleted documents\r\n                    this.clear();\r\n                }\r\n            }));\r\n        } else {\r\n            this.toDispose.push(sharedServices.workspace.DocumentBuilder.onUpdate(() => { // react on both changed and deleted documents\r\n                this.clear();\r\n            }));\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAWA,OAAM,MAAgBA,eAAe;EAArCC,YAAA;IAEc,KAAAC,SAAS,GAAiB,EAAE;IAC5B,KAAAC,UAAU,GAAG,KAAK;EAoBhC;EAlBIC,SAASA,CAACC,UAAsB;IAC5B,IAAI,CAACH,SAAS,CAACI,IAAI,CAACD,UAAU,CAAC;EACnC;EAEAE,OAAOA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACN,UAAU,GAAG,IAAI;IACtB,IAAI,CAACD,SAAS,CAACQ,OAAO,CAACL,UAAU,IAAIA,UAAU,CAACE,OAAO,EAAE,CAAC;EAC9D;EAEUC,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACL,UAAU,EAAE;MACjB,MAAM,IAAIQ,KAAK,CAAC,sCAAsC,CAAC;IAC3D;EACJ;;AAKJ,OAAM,MAAOC,WAAkB,SAAQZ,eAAe;EAAtDC,YAAA;;IACuB,KAAAY,KAAK,GAAG,IAAIC,GAAG,EAAQ;EAoC9C;EAlCIC,GAAGA,CAACC,GAAM;IACN,IAAI,CAACR,eAAe,EAAE;IACtB,OAAO,IAAI,CAACK,KAAK,CAACE,GAAG,CAACC,GAAG,CAAC;EAC9B;EAEAC,GAAGA,CAACD,GAAM,EAAEE,KAAQ;IAChB,IAAI,CAACV,eAAe,EAAE;IACtB,IAAI,CAACK,KAAK,CAACI,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;EAC9B;EAIAC,GAAGA,CAACH,GAAM,EAAEI,QAAkB;IAC1B,IAAI,CAACZ,eAAe,EAAE;IACtB,IAAI,IAAI,CAACK,KAAK,CAACE,GAAG,CAACC,GAAG,CAAC,EAAE;MACrB,OAAO,IAAI,CAACH,KAAK,CAACM,GAAG,CAACH,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAII,QAAQ,EAAE;MACjB,MAAMF,KAAK,GAAGE,QAAQ,EAAE;MACxB,IAAI,CAACP,KAAK,CAACI,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;MAC1B,OAAOA,KAAK;IAChB,CAAC,MAAM;MACH,OAAOG,SAAS;IACpB;EACJ;EAEAC,MAAMA,CAACN,GAAM;IACT,IAAI,CAACR,eAAe,EAAE;IACtB,OAAO,IAAI,CAACK,KAAK,CAACS,MAAM,CAACN,GAAG,CAAC;EACjC;EAEAP,KAAKA,CAAA;IACD,IAAI,CAACD,eAAe,EAAE;IACtB,IAAI,CAACK,KAAK,CAACJ,KAAK,EAAE;EACtB;;AAGJ,OAAM,MAAOc,YAAwD,SAAQvB,eAAe;EAKxFC,YAAYuB,SAA0C;IAClD,KAAK,EAAE;IAJM,KAAAX,KAAK,GAAG,IAAIC,GAAG,EAAyC;IAKrE,IAAI,CAACU,SAAS,GAAGA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAKN,KAAK,IAAIA,KAAM;EAClD;EAEAH,GAAGA,CAACU,UAAmB,EAAET,GAAQ;IAC7B,IAAI,CAACR,eAAe,EAAE;IACtB,OAAO,IAAI,CAACkB,eAAe,CAACD,UAAU,CAAC,CAACV,GAAG,CAACC,GAAG,CAAC;EACpD;EAEAC,GAAGA,CAACQ,UAAmB,EAAET,GAAQ,EAAEE,KAAY;IAC3C,IAAI,CAACV,eAAe,EAAE;IACtB,IAAI,CAACkB,eAAe,CAACD,UAAU,CAAC,CAACR,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;EACpD;EAIAC,GAAGA,CAACM,UAAmB,EAAET,GAAQ,EAAEI,QAAsB;IACrD,IAAI,CAACZ,eAAe,EAAE;IACtB,MAAMmB,YAAY,GAAG,IAAI,CAACD,eAAe,CAACD,UAAU,CAAC;IACrD,IAAIE,YAAY,CAACZ,GAAG,CAACC,GAAG,CAAC,EAAE;MACvB,OAAOW,YAAY,CAACR,GAAG,CAACH,GAAG,CAAC;IAChC,CAAC,MAAM,IAAII,QAAQ,EAAE;MACjB,MAAMF,KAAK,GAAGE,QAAQ,EAAE;MACxBO,YAAY,CAACV,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;MAC5B,OAAOA,KAAK;IAChB,CAAC,MAAM;MACH,OAAOG,SAAS;IACpB;EACJ;EAEAC,MAAMA,CAACG,UAAmB,EAAET,GAAQ;IAChC,IAAI,CAACR,eAAe,EAAE;IACtB,OAAO,IAAI,CAACkB,eAAe,CAACD,UAAU,CAAC,CAACH,MAAM,CAACN,GAAG,CAAC;EACvD;EAIAP,KAAKA,CAACgB,UAAoB;IACtB,IAAI,CAACjB,eAAe,EAAE;IACtB,IAAIiB,UAAU,EAAE;MACZ,MAAMG,MAAM,GAAG,IAAI,CAACJ,SAAS,CAACC,UAAU,CAAC;MACzC,IAAI,CAACZ,KAAK,CAACS,MAAM,CAACM,MAAM,CAAC;IAC7B,CAAC,MAAM;MACH,IAAI,CAACf,KAAK,CAACJ,KAAK,EAAE;IACtB;EACJ;EAEUiB,eAAeA,CAACD,UAAmB;IACzC,MAAMG,MAAM,GAAG,IAAI,CAACJ,SAAS,CAACC,UAAU,CAAC;IACzC,IAAII,aAAa,GAAG,IAAI,CAAChB,KAAK,CAACM,GAAG,CAACS,MAAM,CAAC;IAC1C,IAAI,CAACC,aAAa,EAAE;MAChBA,aAAa,GAAG,IAAIf,GAAG,EAAE;MACzB,IAAI,CAACD,KAAK,CAACI,GAAG,CAACW,MAAM,EAAEC,aAAa,CAAC;IACzC;IACA,OAAOA,aAAa;EACxB;;AAGJ;;;;AAIA,OAAM,MAAOC,aAAoB,SAAQP,YAAwC;EAE7E;;;;;;;;;;;;EAYAtB,YAAY8B,cAAyC,EAAEC,KAAqB;IACxE,KAAK,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,EAAE,CAAC;IAC5B,IAAIF,KAAK,EAAE;MACP,IAAI,CAAC9B,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACC,eAAe,CAACL,KAAK,EAAEM,QAAQ,IAAG;QAC3F,IAAI,CAAC7B,KAAK,CAAC6B,QAAQ,CAACL,GAAG,CAACC,QAAQ,EAAE,CAAC;MACvC,CAAC,CAAC,CAAC;MACH,IAAI,CAAChC,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACG,QAAQ,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAI;QACxF,KAAK,MAAMR,GAAG,IAAIQ,OAAO,EAAE;UAAE;UACzB,IAAI,CAAChC,KAAK,CAACwB,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MACH,IAAI,CAAC/B,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACG,QAAQ,CAAC,CAACG,OAAO,EAAED,OAAO,KAAI;QACvF,MAAME,OAAO,GAAGD,OAAO,CAACE,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC;QACzC,KAAK,MAAMR,GAAG,IAAIU,OAAO,EAAE;UACvB,IAAI,CAAClC,KAAK,CAACwB,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC,CAAC;IACP;EACJ;;AAGJ;;;;AAIA,OAAM,MAAOY,cAAqB,SAAQjC,WAAiB;EAEvD;;;;;;;;EAQAX,YAAY8B,cAAyC,EAAEC,KAAqB;IACxE,KAAK,EAAE;IACP,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC9B,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACU,YAAY,CAACd,KAAK,EAAE,MAAK;QAClF,IAAI,CAACvB,KAAK,EAAE;MAChB,CAAC,CAAC,CAAC;MACH,IAAI,CAACP,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACG,QAAQ,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAI;QACxF,IAAIA,OAAO,CAACM,MAAM,GAAG,CAAC,EAAE;UAAE;UACtB,IAAI,CAACtC,KAAK,EAAE;QAChB;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MACH,IAAI,CAACP,SAAS,CAACI,IAAI,CAACyB,cAAc,CAACI,SAAS,CAACC,eAAe,CAACG,QAAQ,CAAC,MAAK;QACvE,IAAI,CAAC9B,KAAK,EAAE;MAChB,CAAC,CAAC,CAAC;IACP;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}