{"ast": null, "code": "export function PRINT_ERROR(msg) {\n  /* istanbul ignore else - can't override global.console in node.js */\n  if (console && console.error) {\n    console.error(`Error: ${msg}`);\n  }\n}\nexport function PRINT_WARNING(msg) {\n  /* istanbul ignore else - can't override global.console in node.js*/\n  if (console && console.warn) {\n    // TODO: modify docs accordingly\n    console.warn(`Warning: ${msg}`);\n  }\n}", "map": {"version": 3, "names": ["PRINT_ERROR", "msg", "console", "error", "PRINT_WARNING", "warn"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/utils/src/print.ts"], "sourcesContent": ["export function PRINT_ERROR(msg: string) {\n  /* istanbul ignore else - can't override global.console in node.js */\n  if (console && console.error) {\n    console.error(`Error: ${msg}`);\n  }\n}\n\nexport function PRINT_WARNING(msg: string) {\n  /* istanbul ignore else - can't override global.console in node.js*/\n  if (console && console.warn) {\n    // TODO: modify docs accordingly\n    console.warn(`Warning: ${msg}`);\n  }\n}\n"], "mappings": "AAAA,OAAM,SAAUA,WAAWA,CAACC,GAAW;EACrC;EACA,IAAIC,OAAO,IAAIA,OAAO,CAACC,KAAK,EAAE;IAC5BD,OAAO,CAACC,KAAK,CAAC,UAAUF,GAAG,EAAE,CAAC;;AAElC;AAEA,OAAM,SAAUG,aAAaA,CAACH,GAAW;EACvC;EACA,IAAIC,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;IAC3B;IACAH,OAAO,CAACG,IAAI,CAAC,YAAYJ,GAAG,EAAE,CAAC;;AAEnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}