{"ast": null, "code": "export default function (callback, that) {\n  var node = this,\n    nodes = [node],\n    next = [],\n    children,\n    i,\n    n,\n    index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["callback", "that", "node", "nodes", "next", "children", "i", "n", "index", "pop", "push", "length", "call"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-hierarchy/src/hierarchy/eachAfter.js"], "sourcesContent": ["export default function(callback, that) {\n  var node = this, nodes = [node], next = [], children, i, n, index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAEC,IAAI,EAAE;EACtC,IAAIC,IAAI,GAAG,IAAI;IAAEC,KAAK,GAAG,CAACD,IAAI,CAAC;IAAEE,IAAI,GAAG,EAAE;IAAEC,QAAQ;IAAEC,CAAC;IAAEC,CAAC;IAAEC,KAAK,GAAG,CAAC,CAAC;EACtE,OAAON,IAAI,GAAGC,KAAK,CAACM,GAAG,CAAC,CAAC,EAAE;IACzBL,IAAI,CAACM,IAAI,CAACR,IAAI,CAAC;IACf,IAAIG,QAAQ,GAAGH,IAAI,CAACG,QAAQ,EAAE;MAC5B,KAAKC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAEL,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC3CH,KAAK,CAACO,IAAI,CAACL,QAAQ,CAACC,CAAC,CAAC,CAAC;MACzB;IACF;EACF;EACA,OAAOJ,IAAI,GAAGE,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;IACxBT,QAAQ,CAACY,IAAI,CAACX,IAAI,EAAEC,IAAI,EAAE,EAAEM,KAAK,EAAE,IAAI,CAAC;EAC1C;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}