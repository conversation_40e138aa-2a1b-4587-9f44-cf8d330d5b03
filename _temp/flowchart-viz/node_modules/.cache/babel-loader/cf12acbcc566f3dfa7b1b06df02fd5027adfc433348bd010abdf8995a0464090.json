{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({\n      compound: true\n    }).setGraph({\n      root: root\n    }).setDefaultNodeLabel(function (v) {\n      return g.node(v);\n    });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n    if (node.rank === rank || node.minRank <= rank && rank <= node.maxRank) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, {\n          weight: g.edge(e).weight + weight\n        });\n      });\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank]\n        });\n      }\n    }\n  });\n  return result;\n}\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode(v = _.uniqueId('_root')));\n  return v;\n}", "map": {"version": 3, "names": ["_", "Graph", "buildLayerGraph", "g", "rank", "relationship", "root", "createRootNode", "result", "compound", "setGraph", "setDefaultNodeLabel", "v", "node", "for<PERSON>ach", "nodes", "parent", "minRank", "maxRank", "setNode", "setParent", "e", "u", "w", "edge", "weight", "isUndefined", "setEdge", "Object", "prototype", "hasOwnProperty", "call", "borderLeft", "borderRight", "hasNode", "uniqueId"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,KAAK,QAAQ,yBAAyB;AAE/C,SAASC,eAAe;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,CAAC,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC9C,IAAIC,IAAI,GAAGC,cAAc,CAACJ,CAAC,CAAC;IAC1BK,MAAM,GAAG,IAAIP,KAAK,CAAC;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC,CACnCC,QAAQ,CAAC;MAAEJ,IAAI,EAAEA;IAAK,CAAC,CAAC,CACxBK,mBAAmB,CAAC,UAAUC,CAAC,EAAE;MAChC,OAAOT,CAAC,CAACU,IAAI,CAACD,CAAC,CAAC;IAClB,CAAC,CAAC;EAENZ,CAAC,CAACc,OAAO,CAACX,CAAC,CAACY,KAAK,CAAC,CAAC,EAAE,UAAUH,CAAC,EAAE;IAChC,IAAIC,IAAI,GAAGV,CAAC,CAACU,IAAI,CAACD,CAAC,CAAC;MAClBI,MAAM,GAAGb,CAAC,CAACa,MAAM,CAACJ,CAAC,CAAC;IAEtB,IAAIC,IAAI,CAACT,IAAI,KAAKA,IAAI,IAAKS,IAAI,CAACI,OAAO,IAAIb,IAAI,IAAIA,IAAI,IAAIS,IAAI,CAACK,OAAQ,EAAE;MACxEV,MAAM,CAACW,OAAO,CAACP,CAAC,CAAC;MACjBJ,MAAM,CAACY,SAAS,CAACR,CAAC,EAAEI,MAAM,IAAIV,IAAI,CAAC;;MAEnC;MACAN,CAAC,CAACc,OAAO,CAACX,CAAC,CAACE,YAAY,CAAC,CAACO,CAAC,CAAC,EAAE,UAAUS,CAAC,EAAE;QACzC,IAAIC,CAAC,GAAGD,CAAC,CAACT,CAAC,KAAKA,CAAC,GAAGS,CAAC,CAACE,CAAC,GAAGF,CAAC,CAACT,CAAC;UAC3BY,IAAI,GAAGhB,MAAM,CAACgB,IAAI,CAACF,CAAC,EAAEV,CAAC,CAAC;UACxBa,MAAM,GAAG,CAACzB,CAAC,CAAC0B,WAAW,CAACF,IAAI,CAAC,GAAGA,IAAI,CAACC,MAAM,GAAG,CAAC;QACjDjB,MAAM,CAACmB,OAAO,CAACL,CAAC,EAAEV,CAAC,EAAE;UAAEa,MAAM,EAAEtB,CAAC,CAACqB,IAAI,CAACH,CAAC,CAAC,CAACI,MAAM,GAAGA;QAAO,CAAC,CAAC;MAC7D,CAAC,CAAC;MAEF,IAAIG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAClB,IAAI,EAAE,SAAS,CAAC,EAAE;QACzDL,MAAM,CAACW,OAAO,CAACP,CAAC,EAAE;UAChBoB,UAAU,EAAEnB,IAAI,CAACmB,UAAU,CAAC5B,IAAI,CAAC;UACjC6B,WAAW,EAAEpB,IAAI,CAACoB,WAAW,CAAC7B,IAAI;QACpC,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EAEF,OAAOI,MAAM;AACf;AAEA,SAASD,cAAcA,CAACJ,CAAC,EAAE;EACzB,IAAIS,CAAC;EACL,OAAOT,CAAC,CAAC+B,OAAO,CAAEtB,CAAC,GAAGZ,CAAC,CAACmC,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAC;EAC5C,OAAOvB,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}