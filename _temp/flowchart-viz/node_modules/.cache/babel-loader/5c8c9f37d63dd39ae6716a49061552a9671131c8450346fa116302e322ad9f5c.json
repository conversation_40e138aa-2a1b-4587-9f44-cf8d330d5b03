{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n/**\n * The default implementation of `Stream` works with two input functions:\n *  - The first function creates the initial state of an iteration.\n *  - The second function gets the current state as argument and returns an `IteratorResult`.\n */\nexport class StreamImpl {\n  constructor(startFn, nextFn) {\n    this.startFn = startFn;\n    this.nextFn = nextFn;\n  }\n  iterator() {\n    const iterator = {\n      state: this.startFn(),\n      next: () => this.nextFn(iterator.state),\n      [Symbol.iterator]: () => iterator\n    };\n    return iterator;\n  }\n  [Symbol.iterator]() {\n    return this.iterator();\n  }\n  isEmpty() {\n    const iterator = this.iterator();\n    return Boolean(iterator.next().done);\n  }\n  count() {\n    const iterator = this.iterator();\n    let count = 0;\n    let next = iterator.next();\n    while (!next.done) {\n      count++;\n      next = iterator.next();\n    }\n    return count;\n  }\n  toArray() {\n    const result = [];\n    const iterator = this.iterator();\n    let next;\n    do {\n      next = iterator.next();\n      if (next.value !== undefined) {\n        result.push(next.value);\n      }\n    } while (!next.done);\n    return result;\n  }\n  toSet() {\n    return new Set(this);\n  }\n  toMap(keyFn, valueFn) {\n    const entryStream = this.map(element => [keyFn ? keyFn(element) : element, valueFn ? valueFn(element) : element]);\n    return new Map(entryStream);\n  }\n  toString() {\n    return this.join();\n  }\n  concat(other) {\n    return new StreamImpl(() => ({\n      first: this.startFn(),\n      firstDone: false,\n      iterator: other[Symbol.iterator]()\n    }), state => {\n      let result;\n      if (!state.firstDone) {\n        do {\n          result = this.nextFn(state.first);\n          if (!result.done) {\n            return result;\n          }\n        } while (!result.done);\n        state.firstDone = true;\n      }\n      do {\n        result = state.iterator.next();\n        if (!result.done) {\n          return result;\n        }\n      } while (!result.done);\n      return DONE_RESULT;\n    });\n  }\n  join(separator = ',') {\n    const iterator = this.iterator();\n    let value = '';\n    let result;\n    let addSeparator = false;\n    do {\n      result = iterator.next();\n      if (!result.done) {\n        if (addSeparator) {\n          value += separator;\n        }\n        value += toString(result.value);\n      }\n      addSeparator = true;\n    } while (!result.done);\n    return value;\n  }\n  indexOf(searchElement, fromIndex = 0) {\n    const iterator = this.iterator();\n    let index = 0;\n    let next = iterator.next();\n    while (!next.done) {\n      if (index >= fromIndex && next.value === searchElement) {\n        return index;\n      }\n      next = iterator.next();\n      index++;\n    }\n    return -1;\n  }\n  every(predicate) {\n    const iterator = this.iterator();\n    let next = iterator.next();\n    while (!next.done) {\n      if (!predicate(next.value)) {\n        return false;\n      }\n      next = iterator.next();\n    }\n    return true;\n  }\n  some(predicate) {\n    const iterator = this.iterator();\n    let next = iterator.next();\n    while (!next.done) {\n      if (predicate(next.value)) {\n        return true;\n      }\n      next = iterator.next();\n    }\n    return false;\n  }\n  forEach(callbackfn) {\n    const iterator = this.iterator();\n    let index = 0;\n    let next = iterator.next();\n    while (!next.done) {\n      callbackfn(next.value, index);\n      next = iterator.next();\n      index++;\n    }\n  }\n  map(callbackfn) {\n    return new StreamImpl(this.startFn, state => {\n      const {\n        done,\n        value\n      } = this.nextFn(state);\n      if (done) {\n        return DONE_RESULT;\n      } else {\n        return {\n          done: false,\n          value: callbackfn(value)\n        };\n      }\n    });\n  }\n  filter(predicate) {\n    return new StreamImpl(this.startFn, state => {\n      let result;\n      do {\n        result = this.nextFn(state);\n        if (!result.done && predicate(result.value)) {\n          return result;\n        }\n      } while (!result.done);\n      return DONE_RESULT;\n    });\n  }\n  nonNullable() {\n    return this.filter(e => e !== undefined && e !== null);\n  }\n  reduce(callbackfn, initialValue) {\n    const iterator = this.iterator();\n    let previousValue = initialValue;\n    let next = iterator.next();\n    while (!next.done) {\n      if (previousValue === undefined) {\n        previousValue = next.value;\n      } else {\n        previousValue = callbackfn(previousValue, next.value);\n      }\n      next = iterator.next();\n    }\n    return previousValue;\n  }\n  reduceRight(callbackfn, initialValue) {\n    return this.recursiveReduce(this.iterator(), callbackfn, initialValue);\n  }\n  recursiveReduce(iterator, callbackfn, initialValue) {\n    const next = iterator.next();\n    if (next.done) {\n      return initialValue;\n    }\n    const previousValue = this.recursiveReduce(iterator, callbackfn, initialValue);\n    if (previousValue === undefined) {\n      return next.value;\n    }\n    return callbackfn(previousValue, next.value);\n  }\n  find(predicate) {\n    const iterator = this.iterator();\n    let next = iterator.next();\n    while (!next.done) {\n      if (predicate(next.value)) {\n        return next.value;\n      }\n      next = iterator.next();\n    }\n    return undefined;\n  }\n  findIndex(predicate) {\n    const iterator = this.iterator();\n    let index = 0;\n    let next = iterator.next();\n    while (!next.done) {\n      if (predicate(next.value)) {\n        return index;\n      }\n      next = iterator.next();\n      index++;\n    }\n    return -1;\n  }\n  includes(searchElement) {\n    const iterator = this.iterator();\n    let next = iterator.next();\n    while (!next.done) {\n      if (next.value === searchElement) {\n        return true;\n      }\n      next = iterator.next();\n    }\n    return false;\n  }\n  flatMap(callbackfn) {\n    return new StreamImpl(() => ({\n      this: this.startFn()\n    }), state => {\n      do {\n        if (state.iterator) {\n          const next = state.iterator.next();\n          if (next.done) {\n            state.iterator = undefined;\n          } else {\n            return next;\n          }\n        }\n        const {\n          done,\n          value\n        } = this.nextFn(state.this);\n        if (!done) {\n          const mapped = callbackfn(value);\n          if (isIterable(mapped)) {\n            state.iterator = mapped[Symbol.iterator]();\n          } else {\n            return {\n              done: false,\n              value: mapped\n            };\n          }\n        }\n      } while (state.iterator);\n      return DONE_RESULT;\n    });\n  }\n  flat(depth) {\n    if (depth === undefined) {\n      depth = 1;\n    }\n    if (depth <= 0) {\n      return this;\n    }\n    const stream = depth > 1 ? this.flat(depth - 1) : this;\n    return new StreamImpl(() => ({\n      this: stream.startFn()\n    }), state => {\n      do {\n        if (state.iterator) {\n          const next = state.iterator.next();\n          if (next.done) {\n            state.iterator = undefined;\n          } else {\n            return next;\n          }\n        }\n        const {\n          done,\n          value\n        } = stream.nextFn(state.this);\n        if (!done) {\n          if (isIterable(value)) {\n            state.iterator = value[Symbol.iterator]();\n          } else {\n            return {\n              done: false,\n              value: value\n            };\n          }\n        }\n      } while (state.iterator);\n      return DONE_RESULT;\n    });\n  }\n  head() {\n    const iterator = this.iterator();\n    const result = iterator.next();\n    if (result.done) {\n      return undefined;\n    }\n    return result.value;\n  }\n  tail(skipCount = 1) {\n    return new StreamImpl(() => {\n      const state = this.startFn();\n      for (let i = 0; i < skipCount; i++) {\n        const next = this.nextFn(state);\n        if (next.done) {\n          return state;\n        }\n      }\n      return state;\n    }, this.nextFn);\n  }\n  limit(maxSize) {\n    return new StreamImpl(() => ({\n      size: 0,\n      state: this.startFn()\n    }), state => {\n      state.size++;\n      if (state.size > maxSize) {\n        return DONE_RESULT;\n      }\n      return this.nextFn(state.state);\n    });\n  }\n  distinct(by) {\n    return new StreamImpl(() => ({\n      set: new Set(),\n      internalState: this.startFn()\n    }), state => {\n      let result;\n      do {\n        result = this.nextFn(state.internalState);\n        if (!result.done) {\n          const value = by ? by(result.value) : result.value;\n          if (!state.set.has(value)) {\n            state.set.add(value);\n            return result;\n          }\n        }\n      } while (!result.done);\n      return DONE_RESULT;\n    });\n  }\n  exclude(other, key) {\n    const otherKeySet = new Set();\n    for (const item of other) {\n      const value = key ? key(item) : item;\n      otherKeySet.add(value);\n    }\n    return this.filter(e => {\n      const ownKey = key ? key(e) : e;\n      return !otherKeySet.has(ownKey);\n    });\n  }\n}\nfunction toString(item) {\n  if (typeof item === 'string') {\n    return item;\n  }\n  if (typeof item === 'undefined') {\n    return 'undefined';\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (typeof item.toString === 'function') {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return item.toString();\n  }\n  return Object.prototype.toString.call(item);\n}\nfunction isIterable(obj) {\n  return !!obj && typeof obj[Symbol.iterator] === 'function';\n}\n/**\n * An empty stream of any type.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const EMPTY_STREAM = new StreamImpl(() => undefined, () => DONE_RESULT);\n/**\n * Use this `IteratorResult` when implementing a `StreamImpl` to indicate that there are no more elements in the stream.\n */\nexport const DONE_RESULT = Object.freeze({\n  done: true,\n  value: undefined\n});\n/**\n * Create a stream from one or more iterables or array-likes.\n */\nexport function stream(...collections) {\n  if (collections.length === 1) {\n    const collection = collections[0];\n    if (collection instanceof StreamImpl) {\n      return collection;\n    }\n    if (isIterable(collection)) {\n      return new StreamImpl(() => collection[Symbol.iterator](), iterator => iterator.next());\n    }\n    if (typeof collection.length === 'number') {\n      return new StreamImpl(() => ({\n        index: 0\n      }), state => {\n        if (state.index < collection.length) {\n          return {\n            done: false,\n            value: collection[state.index++]\n          };\n        } else {\n          return DONE_RESULT;\n        }\n      });\n    }\n  }\n  if (collections.length > 1) {\n    return new StreamImpl(() => ({\n      collIndex: 0,\n      arrIndex: 0\n    }), state => {\n      do {\n        if (state.iterator) {\n          const next = state.iterator.next();\n          if (!next.done) {\n            return next;\n          }\n          state.iterator = undefined;\n        }\n        if (state.array) {\n          if (state.arrIndex < state.array.length) {\n            return {\n              done: false,\n              value: state.array[state.arrIndex++]\n            };\n          }\n          state.array = undefined;\n          state.arrIndex = 0;\n        }\n        if (state.collIndex < collections.length) {\n          const collection = collections[state.collIndex++];\n          if (isIterable(collection)) {\n            state.iterator = collection[Symbol.iterator]();\n          } else if (collection && typeof collection.length === 'number') {\n            state.array = collection;\n          }\n        }\n      } while (state.iterator || state.array || state.collIndex < collections.length);\n      return DONE_RESULT;\n    });\n  }\n  return EMPTY_STREAM;\n}\n/**\n * The default implementation of `TreeStream` takes a root element and a function that computes the\n * children of its argument. Whether the root node included in the stream is controlled with the\n * `includeRoot` option, which defaults to `false`.\n */\nexport class TreeStreamImpl extends StreamImpl {\n  constructor(root, children, options) {\n    super(() => ({\n      iterators: (options === null || options === void 0 ? void 0 : options.includeRoot) ? [[root][Symbol.iterator]()] : [children(root)[Symbol.iterator]()],\n      pruned: false\n    }), state => {\n      if (state.pruned) {\n        state.iterators.pop();\n        state.pruned = false;\n      }\n      while (state.iterators.length > 0) {\n        const iterator = state.iterators[state.iterators.length - 1];\n        const next = iterator.next();\n        if (next.done) {\n          state.iterators.pop();\n        } else {\n          state.iterators.push(children(next.value)[Symbol.iterator]());\n          return next;\n        }\n      }\n      return DONE_RESULT;\n    });\n  }\n  iterator() {\n    const iterator = {\n      state: this.startFn(),\n      next: () => this.nextFn(iterator.state),\n      prune: () => {\n        iterator.state.pruned = true;\n      },\n      [Symbol.iterator]: () => iterator\n    };\n    return iterator;\n  }\n}\n/**\n * A set of utility functions that reduce a stream to a single value.\n */\nexport var Reduction;\n(function (Reduction) {\n  /**\n   * Compute the sum of a number stream.\n   */\n  function sum(stream) {\n    return stream.reduce((a, b) => a + b, 0);\n  }\n  Reduction.sum = sum;\n  /**\n   * Compute the product of a number stream.\n   */\n  function product(stream) {\n    return stream.reduce((a, b) => a * b, 0);\n  }\n  Reduction.product = product;\n  /**\n   * Compute the minimum of a number stream. Returns `undefined` if the stream is empty.\n   */\n  function min(stream) {\n    return stream.reduce((a, b) => Math.min(a, b));\n  }\n  Reduction.min = min;\n  /**\n   * Compute the maximum of a number stream. Returns `undefined` if the stream is empty.\n   */\n  function max(stream) {\n    return stream.reduce((a, b) => Math.max(a, b));\n  }\n  Reduction.max = max;\n})(Reduction || (Reduction = {}));", "map": {"version": 3, "names": ["StreamImpl", "constructor", "startFn", "nextFn", "iterator", "state", "next", "Symbol", "isEmpty", "Boolean", "done", "count", "toArray", "result", "value", "undefined", "push", "toSet", "Set", "toMap", "keyFn", "valueFn", "entryStream", "map", "element", "Map", "toString", "join", "concat", "other", "first", "firstDone", "DONE_RESULT", "separator", "addSeparator", "indexOf", "searchElement", "fromIndex", "index", "every", "predicate", "some", "for<PERSON>ach", "callbackfn", "filter", "nonNullable", "e", "reduce", "initialValue", "previousValue", "reduceRight", "recursiveReduce", "find", "findIndex", "includes", "flatMap", "this", "mapped", "isIterable", "flat", "depth", "stream", "head", "tail", "skip<PERSON><PERSON>nt", "i", "limit", "maxSize", "size", "distinct", "by", "set", "internalState", "has", "add", "exclude", "key", "otherKeySet", "item", "own<PERSON>ey", "Object", "prototype", "call", "obj", "EMPTY_STREAM", "freeze", "collections", "length", "collection", "collIndex", "arrIndex", "array", "TreeStreamImpl", "root", "children", "options", "iterators", "includeRoot", "pruned", "pop", "prune", "Reduction", "sum", "a", "b", "product", "min", "Math", "max"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/stream.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/**\r\n * A stream is a read-only sequence of values. While the contents of an array can be accessed\r\n * both sequentially and randomly (via index), a stream allows only sequential access.\r\n *\r\n * The advantage of this is that a stream can be evaluated lazily, so it does not require\r\n * to store intermediate values. This can boost performance when a large sequence is\r\n * processed via filtering, mapping etc. and accessed at most once. However, lazy\r\n * evaluation means that all processing is repeated when you access the sequence multiple\r\n * times; in such a case, it may be better to store the resulting sequence into an array.\r\n */\r\nexport interface Stream<T> extends Iterable<T> {\r\n\r\n    /**\r\n     * Returns an iterator for this stream. This is the same as calling the `Symbol.iterator` function property.\r\n     */\r\n    iterator(): IterableIterator<T>;\r\n\r\n    /**\r\n     * Determines whether this stream contains no elements.\r\n     */\r\n    isEmpty(): boolean;\r\n\r\n    /**\r\n     * Determines the number of elements in this stream.\r\n     */\r\n    count(): number;\r\n\r\n    /**\r\n     * Collects all elements of this stream into an array.\r\n     */\r\n    toArray(): T[];\r\n\r\n    /**\r\n     * Collects all elements of this stream into a Set.\r\n     */\r\n    toSet(): Set<T>;\r\n\r\n    /**\r\n     * Collects all elements of this stream into a Map, applying the provided functions to determine keys and values.\r\n     *\r\n     * @param keyFn The function to derive map keys. If omitted, the stream elements are used as keys.\r\n     * @param valueFn The function to derive map values. If omitted, the stream elements are used as values.\r\n     */\r\n    toMap<K = T, V = T>(keyFn?: (e: T) => K, valueFn?: (e: T) => V): Map<K, V>;\r\n\r\n    /**\r\n     * Returns a string representation of a stream.\r\n     */\r\n    toString(): string;\r\n\r\n    /**\r\n     * Combines two streams by returning a new stream that yields all elements of this stream and the other stream.\r\n     *\r\n     * @param other Stream to be concatenated with this one.\r\n     */\r\n    concat<T2>(other: Iterable<T2>): Stream<T | T2>;\r\n\r\n    /**\r\n     * Adds all elements of the stream into a string, separated by the specified separator string.\r\n     *\r\n     * @param separator A string used to separate one element of the stream from the next in the resulting string.\r\n     *        If omitted, the steam elements are separated with a comma.\r\n     */\r\n    join(separator?: string): string\r\n\r\n    /**\r\n     * Returns the index of the first occurrence of a value in the stream, or -1 if it is not present.\r\n     *\r\n     * @param searchElement The value to locate in the array.\r\n     * @param fromIndex The stream index at which to begin the search. If fromIndex is omitted, the search\r\n     *        starts at index 0.\r\n     */\r\n    indexOf(searchElement: T, fromIndex?: number): number;\r\n\r\n    /**\r\n     * Determines whether all members of the stream satisfy the specified test.\r\n     *\r\n     * @param predicate This method calls the predicate function for each element in the stream until the\r\n     *        predicate returns a value which is coercible to the Boolean value `false`, or until the end\r\n     *        of the stream.\r\n     */\r\n    every<S extends T>(predicate: (value: T) => value is S): this is Stream<S>;\r\n    every(predicate: (value: T) => unknown): boolean;\r\n\r\n    /**\r\n     * Determines whether any member of the stream satisfies the specified test.\r\n     *\r\n     * @param predicate This method calls the predicate function for each element in the stream until the\r\n     *        predicate returns a value which is coercible to the Boolean value `true`, or until the end\r\n     *        of the stream.\r\n     */\r\n    some(predicate: (value: T) => unknown): boolean;\r\n\r\n    /**\r\n     * Performs the specified action for each element in the stream.\r\n     *\r\n     * @param callbackfn Function called once for each element in the stream.\r\n     */\r\n    forEach(callbackfn: (value: T, index: number) => void): void;\r\n\r\n    /**\r\n     * Returns a stream that yields the results of calling the specified callback function on each element\r\n     * of the stream. The function is called when the resulting stream elements are actually accessed, so\r\n     * accessing the resulting stream multiple times means the function is also called multiple times for\r\n     * each element of the stream.\r\n     *\r\n     * @param callbackfn Lazily evaluated function mapping stream elements.\r\n     */\r\n    map<U>(callbackfn: (value: T) => U): Stream<U>;\r\n\r\n    /**\r\n     * Returns the elements of the stream that meet the condition specified in a callback function.\r\n     * The function is called when the resulting stream elements are actually accessed, so accessing the\r\n     * resulting stream multiple times means the function is also called multiple times for each element\r\n     * of the stream.\r\n     *\r\n     * @param predicate Lazily evaluated function checking a condition on stream elements.\r\n     */\r\n    filter<S extends T>(predicate: (value: T) => value is S): Stream<S>;\r\n    filter(predicate: (value: T) => unknown): Stream<T>;\r\n\r\n    /**\r\n     * Returns the elements of the stream that are _non-nullable_, which means they are neither `undefined`\r\n     * nor `null`.\r\n     */\r\n    nonNullable(): Stream<NonNullable<T>>;\r\n\r\n    /**\r\n     * Calls the specified callback function for all elements in the stream. The return value of the\r\n     * callback function is the accumulated result, and is provided as an argument in the next call to\r\n     * the callback function.\r\n     *\r\n     * @param callbackfn This method calls the function once for each element in the stream, providing\r\n     *        the previous and current values of the reduction.\r\n     * @param initialValue If specified, `initialValue` is used as the initial value to start the\r\n     *        accumulation. The first call to the function provides this value as an argument instead\r\n     *        of a stream value.\r\n     */\r\n    reduce(callbackfn: (previousValue: T, currentValue: T) => T): T | undefined;\r\n    reduce<U = T>(callbackfn: (previousValue: U, currentValue: T) => U, initialValue: U): U;\r\n\r\n    /**\r\n     * Calls the specified callback function for all elements in the stream, in descending order.\r\n     * The return value of the callback function is the accumulated result, and is provided as an\r\n     * argument in the next call to the callback function.\r\n     *\r\n     * @param callbackfn This method calls the function once for each element in the stream, providing\r\n     *        the previous and current values of the reduction.\r\n     * @param initialValue If specified, `initialValue` is used as the initial value to start the\r\n     *        accumulation. The first call to the function provides this value as an argument instead\r\n     *        of an array value.\r\n     */\r\n    reduceRight(callbackfn: (previousValue: T, currentValue: T) => T): T | undefined;\r\n    reduceRight<U = T>(callbackfn: (previousValue: U, currentValue: T) => U, initialValue: U): U;\r\n\r\n    /**\r\n     * Returns the value of the first element in the stream that meets the condition, or `undefined`\r\n     * if there is no such element.\r\n     *\r\n     * @param predicate This method calls `predicate` once for each element of the stream, in ascending\r\n     *        order, until it finds one where `predicate` returns a value which is coercible to the\r\n     *        Boolean value `true`.\r\n     */\r\n    find<S extends T>(predicate: (value: T) => value is S): S | undefined;\r\n    find(predicate: (value: T) => unknown): T | undefined;\r\n\r\n    /**\r\n     * Returns the index of the first element in the stream that meets the condition, or `-1`\r\n     * if there is no such element.\r\n     *\r\n     * @param predicate This method calls `predicate` once for each element of the stream, in ascending\r\n     *        order, until it finds one where `predicate` returns a value which is coercible to the\r\n     *        Boolean value `true`.\r\n     */\r\n    findIndex(predicate: (value: T) => unknown): number;\r\n\r\n    /**\r\n     * Determines whether the stream includes a certain element, returning `true` or `false` as appropriate.\r\n     *\r\n     * @param searchElement The element to search for.\r\n     */\r\n    includes(searchElement: T): boolean;\r\n\r\n    /**\r\n     * Calls a defined callback function on each element of the stream and then flattens the result into\r\n     * a new stream. This is identical to a `map` followed by `flat` with depth 1.\r\n     *\r\n     * @param callbackfn Lazily evaluated function mapping stream elements.\r\n     */\r\n    flatMap<U>(callbackfn: (value: T) => U | Iterable<U>): Stream<U>;\r\n\r\n    /**\r\n     * Returns a new stream with all sub-stream or sub-array elements concatenated into it recursively up\r\n     * to the specified depth.\r\n     *\r\n     * @param depth The maximum recursion depth. Defaults to 1.\r\n     */\r\n    flat<D extends number = 1>(depth?: D): FlatStream<T, D>;\r\n\r\n    /**\r\n     * Returns the first element in the stream, or `undefined` if the stream is empty.\r\n     */\r\n    head(): T | undefined;\r\n\r\n    /**\r\n     * Returns a stream that skips the first `skipCount` elements from this stream.\r\n     *\r\n     * @param skipCount The number of elements to skip. If this is larger than the number of elements in\r\n     *        the stream, an empty stream is returned. Defaults to 1.\r\n     */\r\n    tail(skipCount?: number): Stream<T>;\r\n\r\n    /**\r\n     * Returns a stream consisting of the elements of this stream, truncated to be no longer than `maxSize`\r\n     * in length.\r\n     *\r\n     * @param maxSize The number of elements the stream should be limited to\r\n     */\r\n    limit(maxSize: number): Stream<T>;\r\n\r\n    /**\r\n     * Returns a stream containing only the distinct elements from this stream.\r\n     * Equality is determined with the same rules as a standard `Set`.\r\n     *\r\n     * @param by A function returning the key used to check equality with a previous stream element.\r\n     *        If omitted, the stream elements themselves are used for comparison.\r\n     */\r\n    distinct<Key = T>(by?: (element: T) => Key): Stream<T>;\r\n\r\n    /**\r\n     * Returns a stream that contains all elements that don't exist in the {@link other} iterable.\r\n     * Equality is determined with the same rules as a standard `Set`.\r\n     * @param other The elements that should be exluded from this stream.\r\n     * @param key A function returning the key used to check quality.\r\n     *        If omitted, the stream elements themselves are used for comparison.\r\n     */\r\n    exclude<Key = T>(other: Iterable<T>, key?: (element: T) => Key): Stream<T>;\r\n\r\n}\r\n\r\nexport type FlatStream<T, Depth extends number> = {\r\n    'done': Stream<T>,\r\n    'recur': T extends Iterable<infer Content>\r\n        ? FlatStream<Content, MinusOne<Depth>>\r\n        : Stream<T>\r\n}[Depth extends 0 ? 'done' : 'recur'];\r\n\r\nexport type MinusOne<N extends number> = [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20][N];\r\n\r\n/**\r\n * The default implementation of `Stream` works with two input functions:\r\n *  - The first function creates the initial state of an iteration.\r\n *  - The second function gets the current state as argument and returns an `IteratorResult`.\r\n */\r\nexport class StreamImpl<S, T> implements Stream<T> {\r\n    protected readonly startFn: () => S;\r\n    protected readonly nextFn: (state: S) => IteratorResult<T>;\r\n\r\n    constructor(startFn: () => S, nextFn: (state: S) => IteratorResult<T, undefined>) {\r\n        this.startFn = startFn;\r\n        this.nextFn = nextFn;\r\n    }\r\n\r\n    iterator(): IterableIterator<T> {\r\n        const iterator = {\r\n            state: this.startFn(),\r\n            next: () => this.nextFn(iterator.state),\r\n            [Symbol.iterator]: () => iterator\r\n        };\r\n        return iterator;\r\n    }\r\n\r\n    [Symbol.iterator](): Iterator<T> {\r\n        return this.iterator();\r\n    }\r\n\r\n    isEmpty(): boolean {\r\n        const iterator = this.iterator();\r\n        return Boolean(iterator.next().done);\r\n    }\r\n\r\n    count(): number {\r\n        const iterator = this.iterator();\r\n        let count = 0;\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            count++;\r\n            next = iterator.next();\r\n        }\r\n        return count;\r\n    }\r\n\r\n    toArray(): T[] {\r\n        const result: T[] = [];\r\n        const iterator = this.iterator();\r\n        let next: IteratorResult<T>;\r\n        do {\r\n            next = iterator.next();\r\n            if (next.value !== undefined) {\r\n                result.push(next.value);\r\n            }\r\n        } while (!next.done);\r\n        return result;\r\n    }\r\n\r\n    toSet(): Set<T> {\r\n        return new Set(this);\r\n    }\r\n\r\n    toMap<K = T, V = T>(keyFn?: (e: T) => K, valueFn?: (e: T) => V): Map<K, V> {\r\n        const entryStream = this.map(element => <[K, V]>[\r\n            keyFn ? keyFn(element) : element,\r\n            valueFn ? valueFn(element) : element\r\n        ]);\r\n        return new Map(entryStream);\r\n    }\r\n\r\n    toString(): string {\r\n        return this.join();\r\n    }\r\n\r\n    concat<T2>(other: Iterable<T2>): Stream<T | T2> {\r\n        return new StreamImpl<{ first: S, firstDone: boolean, iterator: Iterator<T2, unknown, undefined> }, T | T2>(\r\n            () => ({ first: this.startFn(), firstDone: false, iterator: other[Symbol.iterator]() }),\r\n            state => {\r\n                let result: IteratorResult<T | T2>;\r\n                if (!state.firstDone) {\r\n                    do {\r\n                        result = this.nextFn(state.first);\r\n                        if (!result.done) {\r\n                            return result;\r\n                        }\r\n                    } while (!result.done);\r\n                    state.firstDone = true;\r\n                }\r\n                do {\r\n                    result = state.iterator.next();\r\n                    if (!result.done) {\r\n                        return result;\r\n                    }\r\n                } while (!result.done);\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n\r\n    join(separator = ','): string {\r\n        const iterator = this.iterator();\r\n        let value = '';\r\n        let result: IteratorResult<T>;\r\n        let addSeparator = false;\r\n        do {\r\n            result = iterator.next();\r\n            if (!result.done) {\r\n                if (addSeparator) {\r\n                    value += separator;\r\n                }\r\n                value += toString(result.value);\r\n            }\r\n            addSeparator = true;\r\n        } while (!result.done);\r\n        return value;\r\n    }\r\n\r\n    indexOf(searchElement: T, fromIndex = 0): number {\r\n        const iterator = this.iterator();\r\n        let index = 0;\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (index >= fromIndex && next.value === searchElement) {\r\n                return index;\r\n            }\r\n            next = iterator.next();\r\n            index++;\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    // In the following definition the '& this' part in the return type is important\r\n    // _and_ the order within 'Stream<U> & this' is crucial!\r\n    // Otherwise Typescript would infer the type of 'this' as 'StreamImpl<S, T> & Stream<U>'\r\n    // (or '<subClass of StreamImpl<S, T> & Stream<U>') and usages like\r\n    // ```\r\n    //  const stream = new StreamImpl(...);\r\n    //  ... stream.every(<typeGuard>) & stream....\r\n    // ```\r\n    // cannot benefit from '<typeGuard>', as Typescript would priorize the signatures\r\n    // of 'StreamImpl<S, T>' (i.e. those of 'Stream<T>') over those of 'Stream<U>'.\r\n    // With the order of 'Stream<U> & this' the signatures of 'Stream<U>' get precedence.\r\n    every<U extends T>(predicate: (value: T) => value is U): this is Stream<U> & this;\r\n    every(predicate: (value: T) => unknown): boolean;\r\n    every(predicate: (value: T) => unknown): boolean {\r\n        const iterator = this.iterator();\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (!predicate(next.value)) {\r\n                return false;\r\n            }\r\n            next = iterator.next();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    some(predicate: (value: T) => unknown): boolean {\r\n        const iterator = this.iterator();\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (predicate(next.value)) {\r\n                return true;\r\n            }\r\n            next = iterator.next();\r\n        }\r\n        return false;\r\n    }\r\n\r\n    forEach(callbackfn: (value: T, index: number) => void): void {\r\n        const iterator = this.iterator();\r\n        let index = 0;\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            callbackfn(next.value, index);\r\n            next = iterator.next();\r\n            index++;\r\n        }\r\n    }\r\n\r\n    map<U>(callbackfn: (value: T) => U): Stream<U> {\r\n        return new StreamImpl<S, U>(\r\n            this.startFn,\r\n            (state) => {\r\n                const { done, value } = this.nextFn(state);\r\n                if (done) {\r\n                    return DONE_RESULT;\r\n                } else {\r\n                    return { done: false, value: callbackfn(value) };\r\n                }\r\n            }\r\n        );\r\n    }\r\n\r\n    // for remarks on the return type definition refer to 'every<U extends T>(...)'\r\n    filter<U extends T>(predicate: (value: T) => value is U): Stream<U> & this;\r\n    filter(predicate: (value: T) => unknown): Stream<T> & this;\r\n    filter(predicate: (value: T) => unknown): Stream<T> {\r\n        return new StreamImpl<S, T>(\r\n            this.startFn,\r\n            state => {\r\n                let result: IteratorResult<T>;\r\n                do {\r\n                    result = this.nextFn(state);\r\n                    if (!result.done && predicate(result.value)) {\r\n                        return result;\r\n                    }\r\n                } while (!result.done);\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n\r\n    nonNullable(): Stream<NonNullable<T>> {\r\n        return this.filter(e => e !== undefined && e !== null) as Stream<NonNullable<T>>;\r\n    }\r\n\r\n    reduce(callbackfn: (previousValue: T, currentValue: T) => T): T | undefined;\r\n    reduce<U = T>(callbackfn: (previousValue: U, currentValue: T) => U, initialValue: U): U;\r\n    reduce<U>(callbackfn: (previousValue: U | T, currentValue: T) => U, initialValue?: U): U | T | undefined {\r\n        const iterator = this.iterator();\r\n        let previousValue: U | T | undefined = initialValue;\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (previousValue === undefined) {\r\n                previousValue = next.value;\r\n            } else {\r\n                previousValue = callbackfn(previousValue, next.value);\r\n            }\r\n            next = iterator.next();\r\n        }\r\n        return previousValue;\r\n    }\r\n\r\n    reduceRight(callbackfn: (previousValue: T, currentValue: T) => T): T | undefined;\r\n    reduceRight<U = T>(callbackfn: (previousValue: U, currentValue: T) => U, initialValue: U): U;\r\n    reduceRight<U>(callbackfn: (previousValue: U | T, currentValue: T) => U, initialValue?: U): U | T | undefined {\r\n        return this.recursiveReduce(this.iterator(), callbackfn, initialValue);\r\n    }\r\n\r\n    protected recursiveReduce<U>(iterator: Iterator<T>, callbackfn: (previousValue: U | T, currentValue: T) => U, initialValue?: U): U | T | undefined {\r\n        const next = iterator.next();\r\n        if (next.done) {\r\n            return initialValue;\r\n        }\r\n        const previousValue = this.recursiveReduce(iterator, callbackfn, initialValue);\r\n        if (previousValue === undefined) {\r\n            return next.value;\r\n        }\r\n        return callbackfn(previousValue, next.value);\r\n    }\r\n\r\n    find<S extends T>(predicate: (value: T) => value is S): S | undefined;\r\n    find(predicate: (value: T) => unknown): T | undefined;\r\n    find(predicate: (value: T) => unknown): T | undefined {\r\n        const iterator = this.iterator();\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (predicate(next.value)) {\r\n                return next.value;\r\n            }\r\n            next = iterator.next();\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    findIndex(predicate: (value: T) => unknown): number {\r\n        const iterator = this.iterator();\r\n        let index = 0;\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (predicate(next.value)) {\r\n                return index;\r\n            }\r\n            next = iterator.next();\r\n            index++;\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    includes(searchElement: T): boolean {\r\n        const iterator = this.iterator();\r\n        let next = iterator.next();\r\n        while (!next.done) {\r\n            if (next.value === searchElement) {\r\n                return true;\r\n            }\r\n            next = iterator.next();\r\n        }\r\n        return false;\r\n    }\r\n\r\n    flatMap<U>(callbackfn: (value: T) => U | Iterable<U>): Stream<U> {\r\n        type FlatMapState = { this: S, iterator?: Iterator<U, undefined> }\r\n        return new StreamImpl<FlatMapState, U>(\r\n            () => ({ this: this.startFn() }),\r\n            (state) => {\r\n                do {\r\n                    if (state.iterator) {\r\n                        const next = state.iterator.next();\r\n                        if (next.done) {\r\n                            state.iterator = undefined;\r\n                        } else {\r\n                            return next;\r\n                        }\r\n                    }\r\n                    const { done, value } = this.nextFn(state.this);\r\n                    if (!done) {\r\n                        const mapped = callbackfn(value);\r\n                        if (isIterable(mapped)) {\r\n                            state.iterator = mapped[Symbol.iterator]();\r\n                        } else {\r\n                            return { done: false, value: mapped };\r\n                        }\r\n                    }\r\n                } while (state.iterator);\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n\r\n    flat<D extends number = 1>(depth?: D): FlatStream<T, D> {\r\n        if (depth === undefined) {\r\n            depth = 1 as D;\r\n        }\r\n        if (depth <= 0) {\r\n            return this as unknown as FlatStream<T, D>;\r\n        }\r\n        const stream = depth > 1 ? this.flat(depth - 1) as unknown as StreamImpl<S, T> : this;\r\n        type FlatMapState = { this: S, iterator?: Iterator<T, undefined> }\r\n        return new StreamImpl<FlatMapState, T>(\r\n            () => ({ this: stream.startFn() }),\r\n            (state) => {\r\n                do {\r\n                    if (state.iterator) {\r\n                        const next = state.iterator.next();\r\n                        if (next.done) {\r\n                            state.iterator = undefined;\r\n                        } else {\r\n                            return next;\r\n                        }\r\n                    }\r\n                    const { done, value } = stream.nextFn(state.this);\r\n                    if (!done) {\r\n                        if (isIterable(value)) {\r\n                            state.iterator = value[Symbol.iterator]() as Iterator<T>;\r\n                        } else {\r\n                            return { done: false, value: value };\r\n                        }\r\n                    }\r\n                } while (state.iterator);\r\n                return DONE_RESULT;\r\n            }\r\n        ) as unknown as FlatStream<T, D>;\r\n    }\r\n\r\n    head(): T | undefined {\r\n        const iterator = this.iterator();\r\n        const result = iterator.next();\r\n        if (result.done) {\r\n            return undefined;\r\n        }\r\n        return result.value;\r\n    }\r\n\r\n    tail(skipCount = 1): Stream<T> {\r\n        return new StreamImpl<S, T>(\r\n            () => {\r\n                const state = this.startFn();\r\n                for (let i = 0; i < skipCount; i++) {\r\n                    const next = this.nextFn(state);\r\n                    if (next.done) {\r\n                        return state;\r\n                    }\r\n                }\r\n                return state;\r\n            },\r\n            this.nextFn\r\n        );\r\n    }\r\n\r\n    limit(maxSize: number): Stream<T> {\r\n        return new StreamImpl<{ size: number, state: S }, T>(\r\n            () => ({ size: 0, state: this.startFn() }),\r\n            state => {\r\n                state.size++;\r\n                if (state.size > maxSize) {\r\n                    return DONE_RESULT;\r\n                }\r\n                return this.nextFn(state.state);\r\n            }\r\n        );\r\n    }\r\n\r\n    distinct<Key = T>(by?: (element: T) => Key): Stream<T> {\r\n        return new StreamImpl<{ set: Set<Key | T>, internalState: S }, T>(\r\n            () => ({ set: new Set<Key | T>(), internalState: this.startFn() }),\r\n            state => {\r\n                let result: IteratorResult<T>;\r\n                do {\r\n                    result = this.nextFn(state.internalState);\r\n                    if (!result.done) {\r\n                        const value = by ? by(result.value) : result.value;\r\n                        if (!state.set.has(value)) {\r\n                            state.set.add(value);\r\n                            return result;\r\n                        }\r\n                    }\r\n                } while (!result.done);\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n\r\n    exclude<Key = T>(other: Iterable<T>, key?: (element: T) => Key): Stream<T> {\r\n        const otherKeySet = new Set<Key | T>();\r\n        for (const item of other) {\r\n            const value = key ? key(item) : item;\r\n            otherKeySet.add(value);\r\n        }\r\n        return this.filter(e => {\r\n            const ownKey = key ? key(e) : e;\r\n            return !otherKeySet.has(ownKey);\r\n        });\r\n    }\r\n}\r\n\r\nfunction toString(item: unknown): string {\r\n    if (typeof item === 'string') {\r\n        return item as string;\r\n    }\r\n    if (typeof item === 'undefined') {\r\n        return 'undefined';\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    if (typeof (item as any).toString === 'function') {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        return (item as any).toString();\r\n    }\r\n    return Object.prototype.toString.call(item);\r\n}\r\n\r\nfunction isIterable<T>(obj: unknown): obj is Iterable<T> {\r\n    return !!obj && typeof (obj as Iterable<T>)[Symbol.iterator] === 'function';\r\n}\r\n\r\n/**\r\n * An empty stream of any type.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport const EMPTY_STREAM: Stream<any> = new StreamImpl<undefined, any>(() => undefined, () => DONE_RESULT);\r\n\r\n/**\r\n * Use this `IteratorResult` when implementing a `StreamImpl` to indicate that there are no more elements in the stream.\r\n */\r\nexport const DONE_RESULT: IteratorReturnResult<undefined> = Object.freeze({ done: true, value: undefined });\r\n\r\n/**\r\n * Create a stream from one or more iterables or array-likes.\r\n */\r\nexport function stream<T>(...collections: Array<Iterable<T> | ArrayLike<T>>): Stream<T> {\r\n    if (collections.length === 1) {\r\n        const collection = collections[0];\r\n        if (collection instanceof StreamImpl) {\r\n            return collection as Stream<T>;\r\n        }\r\n        if (isIterable(collection)) {\r\n            return new StreamImpl<Iterator<T, undefined>, T>(\r\n                () => collection[Symbol.iterator](),\r\n                (iterator) => iterator.next()\r\n            );\r\n        }\r\n        if (typeof collection.length === 'number') {\r\n            return new StreamImpl<{ index: number }, T>(\r\n                () => ({ index: 0 }),\r\n                (state) => {\r\n                    if (state.index < collection.length) {\r\n                        return { done: false, value: collection[state.index++] };\r\n                    } else {\r\n                        return DONE_RESULT;\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    }\r\n    if (collections.length > 1) {\r\n        type State = { collIndex: number, iterator?: Iterator<T, undefined>, array?: ArrayLike<T>, arrIndex: number };\r\n        return new StreamImpl<State, T>(\r\n            () => ({ collIndex: 0, arrIndex: 0 }),\r\n            (state) => {\r\n                do {\r\n                    if (state.iterator) {\r\n                        const next = state.iterator.next();\r\n                        if (!next.done) {\r\n                            return next;\r\n                        }\r\n                        state.iterator = undefined;\r\n                    }\r\n                    if (state.array) {\r\n                        if (state.arrIndex < state.array.length) {\r\n                            return { done: false, value: state.array[state.arrIndex++] };\r\n                        }\r\n                        state.array = undefined;\r\n                        state.arrIndex = 0;\r\n                    }\r\n                    if (state.collIndex < collections.length) {\r\n                        const collection = collections[state.collIndex++];\r\n                        if (isIterable(collection)) {\r\n                            state.iterator = collection[Symbol.iterator]();\r\n                        } else if (collection && typeof collection.length === 'number') {\r\n                            state.array = collection;\r\n                        }\r\n                    }\r\n                } while (state.iterator || state.array || state.collIndex < collections.length);\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n    return EMPTY_STREAM;\r\n}\r\n\r\n/**\r\n * A tree iterator adds the ability to prune the current iteration.\r\n */\r\nexport interface TreeIterator<T> extends IterableIterator<T> {\r\n    /**\r\n     * Skip the whole subtree below the last returned element. The iteration continues as if that\r\n     * element had no children.\r\n     */\r\n    prune(): void\r\n}\r\n\r\n/**\r\n * A tree stream is used to stream the elements of a tree, for example an AST or CST.\r\n */\r\nexport interface TreeStream<T> extends Stream<T> {\r\n    iterator(): TreeIterator<T>\r\n}\r\n\r\n/**\r\n * The default implementation of `TreeStream` takes a root element and a function that computes the\r\n * children of its argument. Whether the root node included in the stream is controlled with the\r\n * `includeRoot` option, which defaults to `false`.\r\n */\r\nexport class TreeStreamImpl<T>\r\n    extends StreamImpl<{ iterators: Array<Iterator<T>>, pruned: boolean }, T>\r\n    implements TreeStream<T> {\r\n\r\n    constructor(root: T, children: (node: T) => Iterable<T>, options?: { includeRoot?: boolean }) {\r\n        super(\r\n            () => ({\r\n                iterators: options?.includeRoot ? [[root][Symbol.iterator]()] : [children(root)[Symbol.iterator]()],\r\n                pruned: false\r\n            }),\r\n            state => {\r\n                if (state.pruned) {\r\n                    state.iterators.pop();\r\n                    state.pruned = false;\r\n                }\r\n                while (state.iterators.length > 0) {\r\n                    const iterator = state.iterators[state.iterators.length - 1];\r\n                    const next = iterator.next();\r\n                    if (next.done) {\r\n                        state.iterators.pop();\r\n                    } else {\r\n                        state.iterators.push(children(next.value)[Symbol.iterator]());\r\n                        return next;\r\n                    }\r\n                }\r\n                return DONE_RESULT;\r\n            }\r\n        );\r\n    }\r\n\r\n    override iterator(): TreeIterator<T> {\r\n        const iterator = {\r\n            state: this.startFn(),\r\n            next: () => this.nextFn(iterator.state),\r\n            prune: () => {\r\n                iterator.state.pruned = true;\r\n            },\r\n            [Symbol.iterator]: () => iterator\r\n        };\r\n        return iterator;\r\n    }\r\n}\r\n\r\n/**\r\n * A set of utility functions that reduce a stream to a single value.\r\n */\r\nexport namespace Reduction {\r\n\r\n    /**\r\n     * Compute the sum of a number stream.\r\n     */\r\n    export function sum(stream: Stream<number>): number {\r\n        return stream.reduce((a, b) => a + b, 0);\r\n    }\r\n\r\n    /**\r\n     * Compute the product of a number stream.\r\n     */\r\n    export function product(stream: Stream<number>): number {\r\n        return stream.reduce((a, b) => a * b, 0);\r\n    }\r\n\r\n    /**\r\n     * Compute the minimum of a number stream. Returns `undefined` if the stream is empty.\r\n     */\r\n    export function min(stream: Stream<number>): number | undefined {\r\n        return stream.reduce((a, b) => Math.min(a, b));\r\n    }\r\n\r\n    /**\r\n     * Compute the maximum of a number stream. Returns `undefined` if the stream is empty.\r\n     */\r\n    export function max(stream: Stream<number>): number | undefined {\r\n        return stream.reduce((a, b) => Math.max(a, b));\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AA+PA;;;;;AAKA,OAAM,MAAOA,UAAU;EAInBC,YAAYC,OAAgB,EAAEC,MAAkD;IAC5E,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EAEAC,QAAQA,CAAA;IACJ,MAAMA,QAAQ,GAAG;MACbC,KAAK,EAAE,IAAI,CAACH,OAAO,EAAE;MACrBI,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACH,MAAM,CAACC,QAAQ,CAACC,KAAK,CAAC;MACvC,CAACE,MAAM,CAACH,QAAQ,GAAG,MAAMA;KAC5B;IACD,OAAOA,QAAQ;EACnB;EAEA,CAACG,MAAM,CAACH,QAAQ,IAAC;IACb,OAAO,IAAI,CAACA,QAAQ,EAAE;EAC1B;EAEAI,OAAOA,CAAA;IACH,MAAMJ,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,OAAOK,OAAO,CAACL,QAAQ,CAACE,IAAI,EAAE,CAACI,IAAI,CAAC;EACxC;EAEAC,KAAKA,CAAA;IACD,MAAMP,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIO,KAAK,GAAG,CAAC;IACb,IAAIL,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACfC,KAAK,EAAE;MACPL,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAOK,KAAK;EAChB;EAEAC,OAAOA,CAAA;IACH,MAAMC,MAAM,GAAQ,EAAE;IACtB,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIE,IAAuB;IAC3B,GAAG;MACCA,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;MACtB,IAAIA,IAAI,CAACQ,KAAK,KAAKC,SAAS,EAAE;QAC1BF,MAAM,CAACG,IAAI,CAACV,IAAI,CAACQ,KAAK,CAAC;MAC3B;IACJ,CAAC,QAAQ,CAACR,IAAI,CAACI,IAAI;IACnB,OAAOG,MAAM;EACjB;EAEAI,KAAKA,CAAA;IACD,OAAO,IAAIC,GAAG,CAAC,IAAI,CAAC;EACxB;EAEAC,KAAKA,CAAeC,KAAmB,EAAEC,OAAqB;IAC1D,MAAMC,WAAW,GAAG,IAAI,CAACC,GAAG,CAACC,OAAO,IAAY,CAC5CJ,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,GAAGA,OAAO,EAChCH,OAAO,GAAGA,OAAO,CAACG,OAAO,CAAC,GAAGA,OAAO,CACvC,CAAC;IACF,OAAO,IAAIC,GAAG,CAACH,WAAW,CAAC;EAC/B;EAEAI,QAAQA,CAAA;IACJ,OAAO,IAAI,CAACC,IAAI,EAAE;EACtB;EAEAC,MAAMA,CAAKC,KAAmB;IAC1B,OAAO,IAAI7B,UAAU,CACjB,OAAO;MAAE8B,KAAK,EAAE,IAAI,CAAC5B,OAAO,EAAE;MAAE6B,SAAS,EAAE,KAAK;MAAE3B,QAAQ,EAAEyB,KAAK,CAACtB,MAAM,CAACH,QAAQ,CAAC;IAAE,CAAE,CAAC,EACvFC,KAAK,IAAG;MACJ,IAAIQ,MAA8B;MAClC,IAAI,CAACR,KAAK,CAAC0B,SAAS,EAAE;QAClB,GAAG;UACClB,MAAM,GAAG,IAAI,CAACV,MAAM,CAACE,KAAK,CAACyB,KAAK,CAAC;UACjC,IAAI,CAACjB,MAAM,CAACH,IAAI,EAAE;YACd,OAAOG,MAAM;UACjB;QACJ,CAAC,QAAQ,CAACA,MAAM,CAACH,IAAI;QACrBL,KAAK,CAAC0B,SAAS,GAAG,IAAI;MAC1B;MACA,GAAG;QACClB,MAAM,GAAGR,KAAK,CAACD,QAAQ,CAACE,IAAI,EAAE;QAC9B,IAAI,CAACO,MAAM,CAACH,IAAI,EAAE;UACd,OAAOG,MAAM;QACjB;MACJ,CAAC,QAAQ,CAACA,MAAM,CAACH,IAAI;MACrB,OAAOsB,WAAW;IACtB,CAAC,CACJ;EACL;EAEAL,IAAIA,CAACM,SAAS,GAAG,GAAG;IAChB,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIU,KAAK,GAAG,EAAE;IACd,IAAID,MAAyB;IAC7B,IAAIqB,YAAY,GAAG,KAAK;IACxB,GAAG;MACCrB,MAAM,GAAGT,QAAQ,CAACE,IAAI,EAAE;MACxB,IAAI,CAACO,MAAM,CAACH,IAAI,EAAE;QACd,IAAIwB,YAAY,EAAE;UACdpB,KAAK,IAAImB,SAAS;QACtB;QACAnB,KAAK,IAAIY,QAAQ,CAACb,MAAM,CAACC,KAAK,CAAC;MACnC;MACAoB,YAAY,GAAG,IAAI;IACvB,CAAC,QAAQ,CAACrB,MAAM,CAACH,IAAI;IACrB,OAAOI,KAAK;EAChB;EAEAqB,OAAOA,CAACC,aAAgB,EAAEC,SAAS,GAAG,CAAC;IACnC,MAAMjC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIkC,KAAK,GAAG,CAAC;IACb,IAAIhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAI4B,KAAK,IAAID,SAAS,IAAI/B,IAAI,CAACQ,KAAK,KAAKsB,aAAa,EAAE;QACpD,OAAOE,KAAK;MAChB;MACAhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;MACtBgC,KAAK,EAAE;IACX;IACA,OAAO,CAAC,CAAC;EACb;EAeAC,KAAKA,CAACC,SAAgC;IAClC,MAAMpC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIE,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAI,CAAC8B,SAAS,CAAClC,IAAI,CAACQ,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK;MAChB;MACAR,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAO,IAAI;EACf;EAEAmC,IAAIA,CAACD,SAAgC;IACjC,MAAMpC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIE,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAI8B,SAAS,CAAClC,IAAI,CAACQ,KAAK,CAAC,EAAE;QACvB,OAAO,IAAI;MACf;MACAR,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAO,KAAK;EAChB;EAEAoC,OAAOA,CAACC,UAA6C;IACjD,MAAMvC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIkC,KAAK,GAAG,CAAC;IACb,IAAIhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACfiC,UAAU,CAACrC,IAAI,CAACQ,KAAK,EAAEwB,KAAK,CAAC;MAC7BhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;MACtBgC,KAAK,EAAE;IACX;EACJ;EAEAf,GAAGA,CAAIoB,UAA2B;IAC9B,OAAO,IAAI3C,UAAU,CACjB,IAAI,CAACE,OAAO,EACXG,KAAK,IAAI;MACN,MAAM;QAAEK,IAAI;QAAEI;MAAK,CAAE,GAAG,IAAI,CAACX,MAAM,CAACE,KAAK,CAAC;MAC1C,IAAIK,IAAI,EAAE;QACN,OAAOsB,WAAW;MACtB,CAAC,MAAM;QACH,OAAO;UAAEtB,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE6B,UAAU,CAAC7B,KAAK;QAAC,CAAE;MACpD;IACJ,CAAC,CACJ;EACL;EAKA8B,MAAMA,CAACJ,SAAgC;IACnC,OAAO,IAAIxC,UAAU,CACjB,IAAI,CAACE,OAAO,EACZG,KAAK,IAAG;MACJ,IAAIQ,MAAyB;MAC7B,GAAG;QACCA,MAAM,GAAG,IAAI,CAACV,MAAM,CAACE,KAAK,CAAC;QAC3B,IAAI,CAACQ,MAAM,CAACH,IAAI,IAAI8B,SAAS,CAAC3B,MAAM,CAACC,KAAK,CAAC,EAAE;UACzC,OAAOD,MAAM;QACjB;MACJ,CAAC,QAAQ,CAACA,MAAM,CAACH,IAAI;MACrB,OAAOsB,WAAW;IACtB,CAAC,CACJ;EACL;EAEAa,WAAWA,CAAA;IACP,OAAO,IAAI,CAACD,MAAM,CAACE,CAAC,IAAIA,CAAC,KAAK/B,SAAS,IAAI+B,CAAC,KAAK,IAAI,CAA2B;EACpF;EAIAC,MAAMA,CAAIJ,UAAwD,EAAEK,YAAgB;IAChF,MAAM5C,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAI6C,aAAa,GAAsBD,YAAY;IACnD,IAAI1C,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAIuC,aAAa,KAAKlC,SAAS,EAAE;QAC7BkC,aAAa,GAAG3C,IAAI,CAACQ,KAAK;MAC9B,CAAC,MAAM;QACHmC,aAAa,GAAGN,UAAU,CAACM,aAAa,EAAE3C,IAAI,CAACQ,KAAK,CAAC;MACzD;MACAR,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAO2C,aAAa;EACxB;EAIAC,WAAWA,CAAIP,UAAwD,EAAEK,YAAgB;IACrF,OAAO,IAAI,CAACG,eAAe,CAAC,IAAI,CAAC/C,QAAQ,EAAE,EAAEuC,UAAU,EAAEK,YAAY,CAAC;EAC1E;EAEUG,eAAeA,CAAI/C,QAAqB,EAAEuC,UAAwD,EAAEK,YAAgB;IAC1H,MAAM1C,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC5B,IAAIA,IAAI,CAACI,IAAI,EAAE;MACX,OAAOsC,YAAY;IACvB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACE,eAAe,CAAC/C,QAAQ,EAAEuC,UAAU,EAAEK,YAAY,CAAC;IAC9E,IAAIC,aAAa,KAAKlC,SAAS,EAAE;MAC7B,OAAOT,IAAI,CAACQ,KAAK;IACrB;IACA,OAAO6B,UAAU,CAACM,aAAa,EAAE3C,IAAI,CAACQ,KAAK,CAAC;EAChD;EAIAsC,IAAIA,CAACZ,SAAgC;IACjC,MAAMpC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIE,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAI8B,SAAS,CAAClC,IAAI,CAACQ,KAAK,CAAC,EAAE;QACvB,OAAOR,IAAI,CAACQ,KAAK;MACrB;MACAR,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAOS,SAAS;EACpB;EAEAsC,SAASA,CAACb,SAAgC;IACtC,MAAMpC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIkC,KAAK,GAAG,CAAC;IACb,IAAIhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAI8B,SAAS,CAAClC,IAAI,CAACQ,KAAK,CAAC,EAAE;QACvB,OAAOwB,KAAK;MAChB;MACAhC,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;MACtBgC,KAAK,EAAE;IACX;IACA,OAAO,CAAC,CAAC;EACb;EAEAgB,QAAQA,CAAClB,aAAgB;IACrB,MAAMhC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,IAAIE,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B,OAAO,CAACA,IAAI,CAACI,IAAI,EAAE;MACf,IAAIJ,IAAI,CAACQ,KAAK,KAAKsB,aAAa,EAAE;QAC9B,OAAO,IAAI;MACf;MACA9B,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;IAC1B;IACA,OAAO,KAAK;EAChB;EAEAiD,OAAOA,CAAIZ,UAAyC;IAEhD,OAAO,IAAI3C,UAAU,CACjB,OAAO;MAAEwD,IAAI,EAAE,IAAI,CAACtD,OAAO;IAAE,CAAE,CAAC,EAC/BG,KAAK,IAAI;MACN,GAAG;QACC,IAAIA,KAAK,CAACD,QAAQ,EAAE;UAChB,MAAME,IAAI,GAAGD,KAAK,CAACD,QAAQ,CAACE,IAAI,EAAE;UAClC,IAAIA,IAAI,CAACI,IAAI,EAAE;YACXL,KAAK,CAACD,QAAQ,GAAGW,SAAS;UAC9B,CAAC,MAAM;YACH,OAAOT,IAAI;UACf;QACJ;QACA,MAAM;UAAEI,IAAI;UAAEI;QAAK,CAAE,GAAG,IAAI,CAACX,MAAM,CAACE,KAAK,CAACmD,IAAI,CAAC;QAC/C,IAAI,CAAC9C,IAAI,EAAE;UACP,MAAM+C,MAAM,GAAGd,UAAU,CAAC7B,KAAK,CAAC;UAChC,IAAI4C,UAAU,CAACD,MAAM,CAAC,EAAE;YACpBpD,KAAK,CAACD,QAAQ,GAAGqD,MAAM,CAAClD,MAAM,CAACH,QAAQ,CAAC,EAAE;UAC9C,CAAC,MAAM;YACH,OAAO;cAAEM,IAAI,EAAE,KAAK;cAAEI,KAAK,EAAE2C;YAAM,CAAE;UACzC;QACJ;MACJ,CAAC,QAAQpD,KAAK,CAACD,QAAQ;MACvB,OAAO4B,WAAW;IACtB,CAAC,CACJ;EACL;EAEA2B,IAAIA,CAAuBC,KAAS;IAChC,IAAIA,KAAK,KAAK7C,SAAS,EAAE;MACrB6C,KAAK,GAAG,CAAM;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACZ,OAAO,IAAmC;IAC9C;IACA,MAAMC,MAAM,GAAGD,KAAK,GAAG,CAAC,GAAG,IAAI,CAACD,IAAI,CAACC,KAAK,GAAG,CAAC,CAAgC,GAAG,IAAI;IAErF,OAAO,IAAI5D,UAAU,CACjB,OAAO;MAAEwD,IAAI,EAAEK,MAAM,CAAC3D,OAAO;IAAE,CAAE,CAAC,EACjCG,KAAK,IAAI;MACN,GAAG;QACC,IAAIA,KAAK,CAACD,QAAQ,EAAE;UAChB,MAAME,IAAI,GAAGD,KAAK,CAACD,QAAQ,CAACE,IAAI,EAAE;UAClC,IAAIA,IAAI,CAACI,IAAI,EAAE;YACXL,KAAK,CAACD,QAAQ,GAAGW,SAAS;UAC9B,CAAC,MAAM;YACH,OAAOT,IAAI;UACf;QACJ;QACA,MAAM;UAAEI,IAAI;UAAEI;QAAK,CAAE,GAAG+C,MAAM,CAAC1D,MAAM,CAACE,KAAK,CAACmD,IAAI,CAAC;QACjD,IAAI,CAAC9C,IAAI,EAAE;UACP,IAAIgD,UAAU,CAAC5C,KAAK,CAAC,EAAE;YACnBT,KAAK,CAACD,QAAQ,GAAGU,KAAK,CAACP,MAAM,CAACH,QAAQ,CAAC,EAAiB;UAC5D,CAAC,MAAM;YACH,OAAO;cAAEM,IAAI,EAAE,KAAK;cAAEI,KAAK,EAAEA;YAAK,CAAE;UACxC;QACJ;MACJ,CAAC,QAAQT,KAAK,CAACD,QAAQ;MACvB,OAAO4B,WAAW;IACtB,CAAC,CAC2B;EACpC;EAEA8B,IAAIA,CAAA;IACA,MAAM1D,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAChC,MAAMS,MAAM,GAAGT,QAAQ,CAACE,IAAI,EAAE;IAC9B,IAAIO,MAAM,CAACH,IAAI,EAAE;MACb,OAAOK,SAAS;IACpB;IACA,OAAOF,MAAM,CAACC,KAAK;EACvB;EAEAiD,IAAIA,CAACC,SAAS,GAAG,CAAC;IACd,OAAO,IAAIhE,UAAU,CACjB,MAAK;MACD,MAAMK,KAAK,GAAG,IAAI,CAACH,OAAO,EAAE;MAC5B,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;QAChC,MAAM3D,IAAI,GAAG,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC;QAC/B,IAAIC,IAAI,CAACI,IAAI,EAAE;UACX,OAAOL,KAAK;QAChB;MACJ;MACA,OAAOA,KAAK;IAChB,CAAC,EACD,IAAI,CAACF,MAAM,CACd;EACL;EAEA+D,KAAKA,CAACC,OAAe;IACjB,OAAO,IAAInE,UAAU,CACjB,OAAO;MAAEoE,IAAI,EAAE,CAAC;MAAE/D,KAAK,EAAE,IAAI,CAACH,OAAO;IAAE,CAAE,CAAC,EAC1CG,KAAK,IAAG;MACJA,KAAK,CAAC+D,IAAI,EAAE;MACZ,IAAI/D,KAAK,CAAC+D,IAAI,GAAGD,OAAO,EAAE;QACtB,OAAOnC,WAAW;MACtB;MACA,OAAO,IAAI,CAAC7B,MAAM,CAACE,KAAK,CAACA,KAAK,CAAC;IACnC,CAAC,CACJ;EACL;EAEAgE,QAAQA,CAAUC,EAAwB;IACtC,OAAO,IAAItE,UAAU,CACjB,OAAO;MAAEuE,GAAG,EAAE,IAAIrD,GAAG,EAAW;MAAEsD,aAAa,EAAE,IAAI,CAACtE,OAAO;IAAE,CAAE,CAAC,EAClEG,KAAK,IAAG;MACJ,IAAIQ,MAAyB;MAC7B,GAAG;QACCA,MAAM,GAAG,IAAI,CAACV,MAAM,CAACE,KAAK,CAACmE,aAAa,CAAC;QACzC,IAAI,CAAC3D,MAAM,CAACH,IAAI,EAAE;UACd,MAAMI,KAAK,GAAGwD,EAAE,GAAGA,EAAE,CAACzD,MAAM,CAACC,KAAK,CAAC,GAAGD,MAAM,CAACC,KAAK;UAClD,IAAI,CAACT,KAAK,CAACkE,GAAG,CAACE,GAAG,CAAC3D,KAAK,CAAC,EAAE;YACvBT,KAAK,CAACkE,GAAG,CAACG,GAAG,CAAC5D,KAAK,CAAC;YACpB,OAAOD,MAAM;UACjB;QACJ;MACJ,CAAC,QAAQ,CAACA,MAAM,CAACH,IAAI;MACrB,OAAOsB,WAAW;IACtB,CAAC,CACJ;EACL;EAEA2C,OAAOA,CAAU9C,KAAkB,EAAE+C,GAAyB;IAC1D,MAAMC,WAAW,GAAG,IAAI3D,GAAG,EAAW;IACtC,KAAK,MAAM4D,IAAI,IAAIjD,KAAK,EAAE;MACtB,MAAMf,KAAK,GAAG8D,GAAG,GAAGA,GAAG,CAACE,IAAI,CAAC,GAAGA,IAAI;MACpCD,WAAW,CAACH,GAAG,CAAC5D,KAAK,CAAC;IAC1B;IACA,OAAO,IAAI,CAAC8B,MAAM,CAACE,CAAC,IAAG;MACnB,MAAMiC,MAAM,GAAGH,GAAG,GAAGA,GAAG,CAAC9B,CAAC,CAAC,GAAGA,CAAC;MAC/B,OAAO,CAAC+B,WAAW,CAACJ,GAAG,CAACM,MAAM,CAAC;IACnC,CAAC,CAAC;EACN;;AAGJ,SAASrD,QAAQA,CAACoD,IAAa;EAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAc;EACzB;EACA,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAO,WAAW;EACtB;EACA;EACA,IAAI,OAAQA,IAAY,CAACpD,QAAQ,KAAK,UAAU,EAAE;IAC9C;IACA,OAAQoD,IAAY,CAACpD,QAAQ,EAAE;EACnC;EACA,OAAOsD,MAAM,CAACC,SAAS,CAACvD,QAAQ,CAACwD,IAAI,CAACJ,IAAI,CAAC;AAC/C;AAEA,SAASpB,UAAUA,CAAIyB,GAAY;EAC/B,OAAO,CAAC,CAACA,GAAG,IAAI,OAAQA,GAAmB,CAAC5E,MAAM,CAACH,QAAQ,CAAC,KAAK,UAAU;AAC/E;AAEA;;;AAGA;AACA,OAAO,MAAMgF,YAAY,GAAgB,IAAIpF,UAAU,CAAiB,MAAMe,SAAS,EAAE,MAAMiB,WAAW,CAAC;AAE3G;;;AAGA,OAAO,MAAMA,WAAW,GAAoCgD,MAAM,CAACK,MAAM,CAAC;EAAE3E,IAAI,EAAE,IAAI;EAAEI,KAAK,EAAEC;AAAS,CAAE,CAAC;AAE3G;;;AAGA,OAAM,SAAU8C,MAAMA,CAAI,GAAGyB,WAA8C;EACvE,IAAIA,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,MAAMC,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;IACjC,IAAIE,UAAU,YAAYxF,UAAU,EAAE;MAClC,OAAOwF,UAAuB;IAClC;IACA,IAAI9B,UAAU,CAAC8B,UAAU,CAAC,EAAE;MACxB,OAAO,IAAIxF,UAAU,CACjB,MAAMwF,UAAU,CAACjF,MAAM,CAACH,QAAQ,CAAC,EAAE,EAClCA,QAAQ,IAAKA,QAAQ,CAACE,IAAI,EAAE,CAChC;IACL;IACA,IAAI,OAAOkF,UAAU,CAACD,MAAM,KAAK,QAAQ,EAAE;MACvC,OAAO,IAAIvF,UAAU,CACjB,OAAO;QAAEsC,KAAK,EAAE;MAAC,CAAE,CAAC,EACnBjC,KAAK,IAAI;QACN,IAAIA,KAAK,CAACiC,KAAK,GAAGkD,UAAU,CAACD,MAAM,EAAE;UACjC,OAAO;YAAE7E,IAAI,EAAE,KAAK;YAAEI,KAAK,EAAE0E,UAAU,CAACnF,KAAK,CAACiC,KAAK,EAAE;UAAC,CAAE;QAC5D,CAAC,MAAM;UACH,OAAON,WAAW;QACtB;MACJ,CAAC,CACJ;IACL;EACJ;EACA,IAAIsD,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;IAExB,OAAO,IAAIvF,UAAU,CACjB,OAAO;MAAEyF,SAAS,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAC,CAAE,CAAC,EACpCrF,KAAK,IAAI;MACN,GAAG;QACC,IAAIA,KAAK,CAACD,QAAQ,EAAE;UAChB,MAAME,IAAI,GAAGD,KAAK,CAACD,QAAQ,CAACE,IAAI,EAAE;UAClC,IAAI,CAACA,IAAI,CAACI,IAAI,EAAE;YACZ,OAAOJ,IAAI;UACf;UACAD,KAAK,CAACD,QAAQ,GAAGW,SAAS;QAC9B;QACA,IAAIV,KAAK,CAACsF,KAAK,EAAE;UACb,IAAItF,KAAK,CAACqF,QAAQ,GAAGrF,KAAK,CAACsF,KAAK,CAACJ,MAAM,EAAE;YACrC,OAAO;cAAE7E,IAAI,EAAE,KAAK;cAAEI,KAAK,EAAET,KAAK,CAACsF,KAAK,CAACtF,KAAK,CAACqF,QAAQ,EAAE;YAAC,CAAE;UAChE;UACArF,KAAK,CAACsF,KAAK,GAAG5E,SAAS;UACvBV,KAAK,CAACqF,QAAQ,GAAG,CAAC;QACtB;QACA,IAAIrF,KAAK,CAACoF,SAAS,GAAGH,WAAW,CAACC,MAAM,EAAE;UACtC,MAAMC,UAAU,GAAGF,WAAW,CAACjF,KAAK,CAACoF,SAAS,EAAE,CAAC;UACjD,IAAI/B,UAAU,CAAC8B,UAAU,CAAC,EAAE;YACxBnF,KAAK,CAACD,QAAQ,GAAGoF,UAAU,CAACjF,MAAM,CAACH,QAAQ,CAAC,EAAE;UAClD,CAAC,MAAM,IAAIoF,UAAU,IAAI,OAAOA,UAAU,CAACD,MAAM,KAAK,QAAQ,EAAE;YAC5DlF,KAAK,CAACsF,KAAK,GAAGH,UAAU;UAC5B;QACJ;MACJ,CAAC,QAAQnF,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACsF,KAAK,IAAItF,KAAK,CAACoF,SAAS,GAAGH,WAAW,CAACC,MAAM;MAC9E,OAAOvD,WAAW;IACtB,CAAC,CACJ;EACL;EACA,OAAOoD,YAAY;AACvB;AAoBA;;;;;AAKA,OAAM,MAAOQ,cACT,SAAQ5F,UAAiE;EAGzEC,YAAY4F,IAAO,EAAEC,QAAkC,EAAEC,OAAmC;IACxF,KAAK,CACD,OAAO;MACHC,SAAS,EAAE,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,WAAW,IAAG,CAAC,CAACJ,IAAI,CAAC,CAACtF,MAAM,CAACH,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC0F,QAAQ,CAACD,IAAI,CAAC,CAACtF,MAAM,CAACH,QAAQ,CAAC,EAAE,CAAC;MACnG8F,MAAM,EAAE;KACX,CAAC,EACF7F,KAAK,IAAG;MACJ,IAAIA,KAAK,CAAC6F,MAAM,EAAE;QACd7F,KAAK,CAAC2F,SAAS,CAACG,GAAG,EAAE;QACrB9F,KAAK,CAAC6F,MAAM,GAAG,KAAK;MACxB;MACA,OAAO7F,KAAK,CAAC2F,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMnF,QAAQ,GAAGC,KAAK,CAAC2F,SAAS,CAAC3F,KAAK,CAAC2F,SAAS,CAACT,MAAM,GAAG,CAAC,CAAC;QAC5D,MAAMjF,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;QAC5B,IAAIA,IAAI,CAACI,IAAI,EAAE;UACXL,KAAK,CAAC2F,SAAS,CAACG,GAAG,EAAE;QACzB,CAAC,MAAM;UACH9F,KAAK,CAAC2F,SAAS,CAAChF,IAAI,CAAC8E,QAAQ,CAACxF,IAAI,CAACQ,KAAK,CAAC,CAACP,MAAM,CAACH,QAAQ,CAAC,EAAE,CAAC;UAC7D,OAAOE,IAAI;QACf;MACJ;MACA,OAAO0B,WAAW;IACtB,CAAC,CACJ;EACL;EAES5B,QAAQA,CAAA;IACb,MAAMA,QAAQ,GAAG;MACbC,KAAK,EAAE,IAAI,CAACH,OAAO,EAAE;MACrBI,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACH,MAAM,CAACC,QAAQ,CAACC,KAAK,CAAC;MACvC+F,KAAK,EAAEA,CAAA,KAAK;QACRhG,QAAQ,CAACC,KAAK,CAAC6F,MAAM,GAAG,IAAI;MAChC,CAAC;MACD,CAAC3F,MAAM,CAACH,QAAQ,GAAG,MAAMA;KAC5B;IACD,OAAOA,QAAQ;EACnB;;AAGJ;;;AAGA,OAAM,IAAWiG,SAAS;AAA1B,WAAiBA,SAAS;EAEtB;;;EAGA,SAAgBC,GAAGA,CAACzC,MAAsB;IACtC,OAAOA,MAAM,CAACd,MAAM,CAAC,CAACwD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;EAC5C;EAFgBH,SAAA,CAAAC,GAAG,GAAAA,GAElB;EAED;;;EAGA,SAAgBG,OAAOA,CAAC5C,MAAsB;IAC1C,OAAOA,MAAM,CAACd,MAAM,CAAC,CAACwD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;EAC5C;EAFgBH,SAAA,CAAAI,OAAO,GAAAA,OAEtB;EAED;;;EAGA,SAAgBC,GAAGA,CAAC7C,MAAsB;IACtC,OAAOA,MAAM,CAACd,MAAM,CAAC,CAACwD,CAAC,EAAEC,CAAC,KAAKG,IAAI,CAACD,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,CAAC;EAClD;EAFgBH,SAAA,CAAAK,GAAG,GAAAA,GAElB;EAED;;;EAGA,SAAgBE,GAAGA,CAAC/C,MAAsB;IACtC,OAAOA,MAAM,CAACd,MAAM,CAAC,CAACwD,CAAC,EAAEC,CAAC,KAAKG,IAAI,CAACC,GAAG,CAACL,CAAC,EAAEC,CAAC,CAAC,CAAC;EAClD;EAFgBH,SAAA,CAAAO,GAAG,GAAAA,GAElB;AAEL,CAAC,EA9BgBP,SAAS,KAATA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}