{"ast": null, "code": "import { compact, filter, forEach, isArray, isEmpty, isFunction, isUndefined, keys, map } from \"lodash-es\";\nimport { defineNameProp } from \"../../lang/lang_extensions.js\";\nexport function defaultVisit(ctx, param) {\n  const childrenNames = keys(ctx);\n  const childrenNamesLength = childrenNames.length;\n  for (let i = 0; i < childrenNamesLength; i++) {\n    const currChildName = childrenNames[i];\n    const currChildArray = ctx[currChildName];\n    const currChildArrayLength = currChildArray.length;\n    for (let j = 0; j < currChildArrayLength; j++) {\n      const currChild = currChildArray[j];\n      // distinction between Tokens Children and CstNode children\n      if (currChild.tokenTypeIdx === undefined) {\n        this[currChild.name](currChild.children, param);\n      }\n    }\n  }\n  // defaultVisit does not support generic out param\n}\nexport function createBaseSemanticVisitorConstructor(grammarName, ruleNames) {\n  const derivedConstructor = function () {};\n  // can be overwritten according to:\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/\n  // name?redirectlocale=en-US&redirectslug=JavaScript%2FReference%2FGlobal_Objects%2FFunction%2Fname\n  defineNameProp(derivedConstructor, grammarName + \"BaseSemantics\");\n  const semanticProto = {\n    visit: function (cstNode, param) {\n      // enables writing more concise visitor methods when CstNode has only a single child\n      if (isArray(cstNode)) {\n        // A CST Node's children dictionary can never have empty arrays as values\n        // If a key is defined there will be at least one element in the corresponding value array.\n        cstNode = cstNode[0];\n      }\n      // enables passing optional CstNodes concisely.\n      if (isUndefined(cstNode)) {\n        return undefined;\n      }\n      return this[cstNode.name](cstNode.children, param);\n    },\n    validateVisitor: function () {\n      const semanticDefinitionErrors = validateVisitor(this, ruleNames);\n      if (!isEmpty(semanticDefinitionErrors)) {\n        const errorMessages = map(semanticDefinitionErrors, currDefError => currDefError.msg);\n        throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:\\n\\t` + `${errorMessages.join(\"\\n\\n\").replace(/\\n/g, \"\\n\\t\")}`);\n      }\n    }\n  };\n  derivedConstructor.prototype = semanticProto;\n  derivedConstructor.prototype.constructor = derivedConstructor;\n  derivedConstructor._RULE_NAMES = ruleNames;\n  return derivedConstructor;\n}\nexport function createBaseVisitorConstructorWithDefaults(grammarName, ruleNames, baseConstructor) {\n  const derivedConstructor = function () {};\n  // can be overwritten according to:\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/\n  // name?redirectlocale=en-US&redirectslug=JavaScript%2FReference%2FGlobal_Objects%2FFunction%2Fname\n  defineNameProp(derivedConstructor, grammarName + \"BaseSemanticsWithDefaults\");\n  const withDefaultsProto = Object.create(baseConstructor.prototype);\n  forEach(ruleNames, ruleName => {\n    withDefaultsProto[ruleName] = defaultVisit;\n  });\n  derivedConstructor.prototype = withDefaultsProto;\n  derivedConstructor.prototype.constructor = derivedConstructor;\n  return derivedConstructor;\n}\nexport var CstVisitorDefinitionError;\n(function (CstVisitorDefinitionError) {\n  CstVisitorDefinitionError[CstVisitorDefinitionError[\"REDUNDANT_METHOD\"] = 0] = \"REDUNDANT_METHOD\";\n  CstVisitorDefinitionError[CstVisitorDefinitionError[\"MISSING_METHOD\"] = 1] = \"MISSING_METHOD\";\n})(CstVisitorDefinitionError || (CstVisitorDefinitionError = {}));\nexport function validateVisitor(visitorInstance, ruleNames) {\n  const missingErrors = validateMissingCstMethods(visitorInstance, ruleNames);\n  return missingErrors;\n}\nexport function validateMissingCstMethods(visitorInstance, ruleNames) {\n  const missingRuleNames = filter(ruleNames, currRuleName => {\n    return isFunction(visitorInstance[currRuleName]) === false;\n  });\n  const errors = map(missingRuleNames, currRuleName => {\n    return {\n      msg: `Missing visitor method: <${currRuleName}> on ${visitorInstance.constructor.name} CST Visitor.`,\n      type: CstVisitorDefinitionError.MISSING_METHOD,\n      methodName: currRuleName\n    };\n  });\n  return compact(errors);\n}", "map": {"version": 3, "names": ["compact", "filter", "for<PERSON>ach", "isArray", "isEmpty", "isFunction", "isUndefined", "keys", "map", "defineNameProp", "defaultVisit", "ctx", "param", "childrenNames", "children<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "length", "i", "curr<PERSON><PERSON><PERSON><PERSON><PERSON>", "curr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "curr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "j", "curr<PERSON><PERSON><PERSON>", "tokenTypeIdx", "undefined", "name", "children", "createBaseSemanticVisitorConstructor", "grammarName", "ruleNames", "derivedConstructor", "semanticProto", "visit", "cstNode", "validateVisitor", "semanticDefinitionErrors", "errorMessages", "currDefError", "msg", "Error", "constructor", "join", "replace", "prototype", "_RULE_NAMES", "createBaseVisitorConstructorWithDefaults", "baseConstructor", "withDefaultsProto", "Object", "create", "ruleName", "CstVisitorDefinitionError", "visitorInstance", "missingErrors", "validateMissingCstMethods", "missingRuleNames", "currRuleName", "errors", "type", "MISSING_METHOD", "methodName"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/cst/cst_visitor.ts"], "sourcesContent": ["import {\n  compact,\n  filter,\n  forEach,\n  isArray,\n  isEmpty,\n  isFunction,\n  isUndefined,\n  keys,\n  map,\n} from \"lodash-es\";\nimport { defineNameProp } from \"../../lang/lang_extensions.js\";\nimport { CstNode, ICstVisitor } from \"@chevrotain/types\";\n\nexport function defaultVisit<IN>(ctx: any, param: IN): void {\n  const childrenNames = keys(ctx);\n  const childrenNamesLength = childrenNames.length;\n  for (let i = 0; i < childrenNamesLength; i++) {\n    const currChildName = childrenNames[i];\n    const currChildArray = ctx[currChildName];\n    const currChildArrayLength = currChildArray.length;\n    for (let j = 0; j < currChildArrayLength; j++) {\n      const currChild: any = currChildArray[j];\n      // distinction between Tokens Children and CstNode children\n      if (currChild.tokenTypeIdx === undefined) {\n        this[currChild.name](currChild.children, param);\n      }\n    }\n  }\n  // defaultVisit does not support generic out param\n}\n\nexport function createBaseSemanticVisitorConstructor(\n  grammarName: string,\n  ruleNames: string[],\n): {\n  new (...args: any[]): ICstVisitor<any, any>;\n} {\n  const derivedConstructor: any = function () {};\n\n  // can be overwritten according to:\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/\n  // name?redirectlocale=en-US&redirectslug=JavaScript%2FReference%2FGlobal_Objects%2FFunction%2Fname\n  defineNameProp(derivedConstructor, grammarName + \"BaseSemantics\");\n\n  const semanticProto = {\n    visit: function (cstNode: CstNode | CstNode[], param: any) {\n      // enables writing more concise visitor methods when CstNode has only a single child\n      if (isArray(cstNode)) {\n        // A CST Node's children dictionary can never have empty arrays as values\n        // If a key is defined there will be at least one element in the corresponding value array.\n        cstNode = cstNode[0];\n      }\n\n      // enables passing optional CstNodes concisely.\n      if (isUndefined(cstNode)) {\n        return undefined;\n      }\n\n      return this[cstNode.name](cstNode.children, param);\n    },\n\n    validateVisitor: function () {\n      const semanticDefinitionErrors = validateVisitor(this, ruleNames);\n      if (!isEmpty(semanticDefinitionErrors)) {\n        const errorMessages = map(\n          semanticDefinitionErrors,\n          (currDefError) => currDefError.msg,\n        );\n        throw Error(\n          `Errors Detected in CST Visitor <${this.constructor.name}>:\\n\\t` +\n            `${errorMessages.join(\"\\n\\n\").replace(/\\n/g, \"\\n\\t\")}`,\n        );\n      }\n    },\n  };\n\n  derivedConstructor.prototype = semanticProto;\n  derivedConstructor.prototype.constructor = derivedConstructor;\n\n  derivedConstructor._RULE_NAMES = ruleNames;\n\n  return derivedConstructor;\n}\n\nexport function createBaseVisitorConstructorWithDefaults(\n  grammarName: string,\n  ruleNames: string[],\n  baseConstructor: Function,\n): {\n  new (...args: any[]): ICstVisitor<any, any>;\n} {\n  const derivedConstructor: any = function () {};\n\n  // can be overwritten according to:\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/\n  // name?redirectlocale=en-US&redirectslug=JavaScript%2FReference%2FGlobal_Objects%2FFunction%2Fname\n  defineNameProp(derivedConstructor, grammarName + \"BaseSemanticsWithDefaults\");\n\n  const withDefaultsProto = Object.create(baseConstructor.prototype);\n  forEach(ruleNames, (ruleName) => {\n    withDefaultsProto[ruleName] = defaultVisit;\n  });\n\n  derivedConstructor.prototype = withDefaultsProto;\n  derivedConstructor.prototype.constructor = derivedConstructor;\n\n  return derivedConstructor;\n}\n\nexport enum CstVisitorDefinitionError {\n  REDUNDANT_METHOD,\n  MISSING_METHOD,\n}\n\nexport interface IVisitorDefinitionError {\n  msg: string;\n  type: CstVisitorDefinitionError;\n  methodName: string;\n}\n\nexport function validateVisitor(\n  visitorInstance: ICstVisitor<unknown, unknown>,\n  ruleNames: string[],\n): IVisitorDefinitionError[] {\n  const missingErrors = validateMissingCstMethods(visitorInstance, ruleNames);\n\n  return missingErrors;\n}\n\nexport function validateMissingCstMethods(\n  visitorInstance: ICstVisitor<unknown, unknown>,\n  ruleNames: string[],\n): IVisitorDefinitionError[] {\n  const missingRuleNames = filter(ruleNames, (currRuleName) => {\n    return isFunction((visitorInstance as any)[currRuleName]) === false;\n  });\n\n  const errors: IVisitorDefinitionError[] = map(\n    missingRuleNames,\n    (currRuleName) => {\n      return {\n        msg: `Missing visitor method: <${currRuleName}> on ${<any>(\n          visitorInstance.constructor.name\n        )} CST Visitor.`,\n        type: CstVisitorDefinitionError.MISSING_METHOD,\n        methodName: currRuleName,\n      };\n    },\n  );\n\n  return compact<IVisitorDefinitionError>(errors);\n}\n"], "mappings": "AAAA,SACEA,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,WAAW,EACXC,IAAI,EACJC,GAAG,QACE,WAAW;AAClB,SAASC,cAAc,QAAQ,+BAA+B;AAG9D,OAAM,SAAUC,YAAYA,CAAKC,GAAQ,EAAEC,KAAS;EAClD,MAAMC,aAAa,GAAGN,IAAI,CAACI,GAAG,CAAC;EAC/B,MAAMG,mBAAmB,GAAGD,aAAa,CAACE,MAAM;EAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,mBAAmB,EAAEE,CAAC,EAAE,EAAE;IAC5C,MAAMC,aAAa,GAAGJ,aAAa,CAACG,CAAC,CAAC;IACtC,MAAME,cAAc,GAAGP,GAAG,CAACM,aAAa,CAAC;IACzC,MAAME,oBAAoB,GAAGD,cAAc,CAACH,MAAM;IAClD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,EAAEC,CAAC,EAAE,EAAE;MAC7C,MAAMC,SAAS,GAAQH,cAAc,CAACE,CAAC,CAAC;MACxC;MACA,IAAIC,SAAS,CAACC,YAAY,KAAKC,SAAS,EAAE;QACxC,IAAI,CAACF,SAAS,CAACG,IAAI,CAAC,CAACH,SAAS,CAACI,QAAQ,EAAEb,KAAK,CAAC;;;;EAIrD;AACF;AAEA,OAAM,SAAUc,oCAAoCA,CAClDC,WAAmB,EACnBC,SAAmB;EAInB,MAAMC,kBAAkB,GAAQ,SAAAA,CAAA,GAAa,CAAC;EAE9C;EACA;EACA;EACApB,cAAc,CAACoB,kBAAkB,EAAEF,WAAW,GAAG,eAAe,CAAC;EAEjE,MAAMG,aAAa,GAAG;IACpBC,KAAK,EAAE,SAAAA,CAAUC,OAA4B,EAAEpB,KAAU;MACvD;MACA,IAAIT,OAAO,CAAC6B,OAAO,CAAC,EAAE;QACpB;QACA;QACAA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;;MAGtB;MACA,IAAI1B,WAAW,CAAC0B,OAAO,CAAC,EAAE;QACxB,OAAOT,SAAS;;MAGlB,OAAO,IAAI,CAACS,OAAO,CAACR,IAAI,CAAC,CAACQ,OAAO,CAACP,QAAQ,EAAEb,KAAK,CAAC;IACpD,CAAC;IAEDqB,eAAe,EAAE,SAAAA,CAAA;MACf,MAAMC,wBAAwB,GAAGD,eAAe,CAAC,IAAI,EAAEL,SAAS,CAAC;MACjE,IAAI,CAACxB,OAAO,CAAC8B,wBAAwB,CAAC,EAAE;QACtC,MAAMC,aAAa,GAAG3B,GAAG,CACvB0B,wBAAwB,EACvBE,YAAY,IAAKA,YAAY,CAACC,GAAG,CACnC;QACD,MAAMC,KAAK,CACT,mCAAmC,IAAI,CAACC,WAAW,CAACf,IAAI,QAAQ,GAC9D,GAAGW,aAAa,CAACK,IAAI,CAAC,MAAM,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CACzD;;IAEL;GACD;EAEDZ,kBAAkB,CAACa,SAAS,GAAGZ,aAAa;EAC5CD,kBAAkB,CAACa,SAAS,CAACH,WAAW,GAAGV,kBAAkB;EAE7DA,kBAAkB,CAACc,WAAW,GAAGf,SAAS;EAE1C,OAAOC,kBAAkB;AAC3B;AAEA,OAAM,SAAUe,wCAAwCA,CACtDjB,WAAmB,EACnBC,SAAmB,EACnBiB,eAAyB;EAIzB,MAAMhB,kBAAkB,GAAQ,SAAAA,CAAA,GAAa,CAAC;EAE9C;EACA;EACA;EACApB,cAAc,CAACoB,kBAAkB,EAAEF,WAAW,GAAG,2BAA2B,CAAC;EAE7E,MAAMmB,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAACH,eAAe,CAACH,SAAS,CAAC;EAClExC,OAAO,CAAC0B,SAAS,EAAGqB,QAAQ,IAAI;IAC9BH,iBAAiB,CAACG,QAAQ,CAAC,GAAGvC,YAAY;EAC5C,CAAC,CAAC;EAEFmB,kBAAkB,CAACa,SAAS,GAAGI,iBAAiB;EAChDjB,kBAAkB,CAACa,SAAS,CAACH,WAAW,GAAGV,kBAAkB;EAE7D,OAAOA,kBAAkB;AAC3B;AAEA,WAAYqB,yBAGX;AAHD,WAAYA,yBAAyB;EACnCA,yBAAA,CAAAA,yBAAA,8CAAgB;EAChBA,yBAAA,CAAAA,yBAAA,0CAAc;AAChB,CAAC,EAHWA,yBAAyB,KAAzBA,yBAAyB;AAWrC,OAAM,SAAUjB,eAAeA,CAC7BkB,eAA8C,EAC9CvB,SAAmB;EAEnB,MAAMwB,aAAa,GAAGC,yBAAyB,CAACF,eAAe,EAAEvB,SAAS,CAAC;EAE3E,OAAOwB,aAAa;AACtB;AAEA,OAAM,SAAUC,yBAAyBA,CACvCF,eAA8C,EAC9CvB,SAAmB;EAEnB,MAAM0B,gBAAgB,GAAGrD,MAAM,CAAC2B,SAAS,EAAG2B,YAAY,IAAI;IAC1D,OAAOlD,UAAU,CAAE8C,eAAuB,CAACI,YAAY,CAAC,CAAC,KAAK,KAAK;EACrE,CAAC,CAAC;EAEF,MAAMC,MAAM,GAA8BhD,GAAG,CAC3C8C,gBAAgB,EACfC,YAAY,IAAI;IACf,OAAO;MACLlB,GAAG,EAAE,4BAA4BkB,YAAY,QAC3CJ,eAAe,CAACZ,WAAW,CAACf,IAAI,eAClB;MAChBiC,IAAI,EAAEP,yBAAyB,CAACQ,cAAc;MAC9CC,UAAU,EAAEJ;KACb;EACH,CAAC,CACF;EAED,OAAOvD,OAAO,CAA0BwD,MAAM,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}