{"ast": null, "code": "import { path } from \"d3-path\";\nimport { slice } from \"../array.js\";\nimport constant from \"../constant.js\";\nimport { x as pointX, y as pointY } from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\nfunction linkSource(d) {\n  return d.source;\n}\nfunction linkTarget(d) {\n  return d.target;\n}\nfunction link(curve) {\n  var source = linkSource,\n    target = linkTarget,\n    x = pointX,\n    y = pointY,\n    context = null;\n  function link() {\n    var buffer,\n      argv = slice.call(arguments),\n      s = source.apply(this, argv),\n      t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n  link.source = function (_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n  link.target = function (_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n  link.x = function (_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n  link.y = function (_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n  link.context = function (_) {\n    return arguments.length ? (context = _ == null ? null : _, link) : context;\n  };\n  return link;\n}\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n    p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n    p2 = pointRadial(x1, y0),\n    p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\nexport function linkVertical() {\n  return link(curveVertical);\n}\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}", "map": {"version": 3, "names": ["path", "slice", "constant", "x", "pointX", "y", "pointY", "pointRadial", "linkSource", "d", "source", "linkTarget", "target", "link", "curve", "context", "buffer", "argv", "call", "arguments", "s", "apply", "t", "_", "length", "curveHorizontal", "x0", "y0", "x1", "y1", "moveTo", "bezierCurveTo", "curveVertical", "curveRadial", "p0", "p1", "p2", "p3", "linkHorizontal", "linkVertical", "linkRadial", "l", "angle", "radius"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js"], "sourcesContent": ["import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,SAAS;AAC5B,SAAQC,KAAK,QAAO,aAAa;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAAQC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,MAAM,QAAO,aAAa;AACpD,OAAOC,WAAW,MAAM,mBAAmB;AAE3C,SAASC,UAAUA,CAACC,CAAC,EAAE;EACrB,OAAOA,CAAC,CAACC,MAAM;AACjB;AAEA,SAASC,UAAUA,CAACF,CAAC,EAAE;EACrB,OAAOA,CAAC,CAACG,MAAM;AACjB;AAEA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIJ,MAAM,GAAGF,UAAU;IACnBI,MAAM,GAAGD,UAAU;IACnBR,CAAC,GAAGC,MAAM;IACVC,CAAC,GAAGC,MAAM;IACVS,OAAO,GAAG,IAAI;EAElB,SAASF,IAAIA,CAAA,EAAG;IACd,IAAIG,MAAM;MAAEC,IAAI,GAAGhB,KAAK,CAACiB,IAAI,CAACC,SAAS,CAAC;MAAEC,CAAC,GAAGV,MAAM,CAACW,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;MAAEK,CAAC,GAAGV,MAAM,CAACS,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IACpG,IAAI,CAACF,OAAO,EAAEA,OAAO,GAAGC,MAAM,GAAGhB,IAAI,CAAC,CAAC;IACvCc,KAAK,CAACC,OAAO,EAAE,CAACZ,CAAC,CAACkB,KAAK,CAAC,IAAI,GAAGJ,IAAI,CAAC,CAAC,CAAC,GAAGG,CAAC,EAAEH,IAAI,CAAC,CAAC,EAAE,CAACZ,CAAC,CAACgB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,EAAE,CAACd,CAAC,CAACkB,KAAK,CAAC,IAAI,GAAGJ,IAAI,CAAC,CAAC,CAAC,GAAGK,CAAC,EAAEL,IAAI,CAAC,CAAC,EAAE,CAACZ,CAAC,CAACgB,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CAAC;IACpI,IAAID,MAAM,EAAE,OAAOD,OAAO,GAAG,IAAI,EAAEC,MAAM,GAAG,EAAE,IAAI,IAAI;EACxD;EAEAH,IAAI,CAACH,MAAM,GAAG,UAASa,CAAC,EAAE;IACxB,OAAOJ,SAAS,CAACK,MAAM,IAAId,MAAM,GAAGa,CAAC,EAAEV,IAAI,IAAIH,MAAM;EACvD,CAAC;EAEDG,IAAI,CAACD,MAAM,GAAG,UAASW,CAAC,EAAE;IACxB,OAAOJ,SAAS,CAACK,MAAM,IAAIZ,MAAM,GAAGW,CAAC,EAAEV,IAAI,IAAID,MAAM;EACvD,CAAC;EAEDC,IAAI,CAACV,CAAC,GAAG,UAASoB,CAAC,EAAE;IACnB,OAAOJ,SAAS,CAACK,MAAM,IAAIrB,CAAC,GAAG,OAAOoB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrB,QAAQ,CAAC,CAACqB,CAAC,CAAC,EAAEV,IAAI,IAAIV,CAAC;EACtF,CAAC;EAEDU,IAAI,CAACR,CAAC,GAAG,UAASkB,CAAC,EAAE;IACnB,OAAOJ,SAAS,CAACK,MAAM,IAAInB,CAAC,GAAG,OAAOkB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrB,QAAQ,CAAC,CAACqB,CAAC,CAAC,EAAEV,IAAI,IAAIR,CAAC;EACtF,CAAC;EAEDQ,IAAI,CAACE,OAAO,GAAG,UAASQ,CAAC,EAAE;IACzB,OAAOJ,SAAS,CAACK,MAAM,IAAKT,OAAO,GAAGQ,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,CAAC,EAAGV,IAAI,IAAIE,OAAO;EAC9E,CAAC;EAED,OAAOF,IAAI;AACb;AAEA,SAASY,eAAeA,CAACV,OAAO,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAChDd,OAAO,CAACe,MAAM,CAACJ,EAAE,EAAEC,EAAE,CAAC;EACtBZ,OAAO,CAACgB,aAAa,CAACL,EAAE,GAAG,CAACA,EAAE,GAAGE,EAAE,IAAI,CAAC,EAAED,EAAE,EAAED,EAAE,EAAEG,EAAE,EAAED,EAAE,EAAEC,EAAE,CAAC;AAC/D;AAEA,SAASG,aAAaA,CAACjB,OAAO,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC9Cd,OAAO,CAACe,MAAM,CAACJ,EAAE,EAAEC,EAAE,CAAC;EACtBZ,OAAO,CAACgB,aAAa,CAACL,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,GAAGE,EAAE,IAAI,CAAC,EAAED,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC/D;AAEA,SAASI,WAAWA,CAAClB,OAAO,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5C,IAAIK,EAAE,GAAG3B,WAAW,CAACmB,EAAE,EAAEC,EAAE,CAAC;IACxBQ,EAAE,GAAG5B,WAAW,CAACmB,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,GAAGE,EAAE,IAAI,CAAC,CAAC;IACxCO,EAAE,GAAG7B,WAAW,CAACqB,EAAE,EAAED,EAAE,CAAC;IACxBU,EAAE,GAAG9B,WAAW,CAACqB,EAAE,EAAEC,EAAE,CAAC;EAC5Bd,OAAO,CAACe,MAAM,CAACI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5BnB,OAAO,CAACgB,aAAa,CAACI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;AACjE;AAEA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOzB,IAAI,CAACY,eAAe,CAAC;AAC9B;AAEA,OAAO,SAASc,YAAYA,CAAA,EAAG;EAC7B,OAAO1B,IAAI,CAACmB,aAAa,CAAC;AAC5B;AAEA,OAAO,SAASQ,UAAUA,CAAA,EAAG;EAC3B,IAAIC,CAAC,GAAG5B,IAAI,CAACoB,WAAW,CAAC;EACzBQ,CAAC,CAACC,KAAK,GAAGD,CAAC,CAACtC,CAAC,EAAE,OAAOsC,CAAC,CAACtC,CAAC;EACzBsC,CAAC,CAACE,MAAM,GAAGF,CAAC,CAACpC,CAAC,EAAE,OAAOoC,CAAC,CAACpC,CAAC;EAC1B,OAAOoC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}