{"ast": null, "code": "import { epsilon, splitter, resulterrbound, estimate, vec, sum, scale } from './util.js';\nconst o3derrboundA = (7 + 56 * epsilon) * epsilon;\nconst o3derrboundB = (3 + 28 * epsilon) * epsilon;\nconst o3derrboundC = (26 + 288 * epsilon) * epsilon * epsilon;\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst at_b = vec(4);\nconst at_c = vec(4);\nconst bt_c = vec(4);\nconst bt_a = vec(4);\nconst ct_a = vec(4);\nconst ct_b = vec(4);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abt = vec(8);\nconst u = vec(4);\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _16 = vec(8);\nconst _12 = vec(12);\nlet fin = vec(192);\nlet fin2 = vec(192);\nfunction finadd(finlen, alen, a) {\n  finlen = sum(finlen, fin, alen, a, fin2);\n  const tmp = fin;\n  fin = fin2;\n  fin2 = tmp;\n  return finlen;\n}\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n  let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n  if (xtail === 0) {\n    if (ytail === 0) {\n      a[0] = 0;\n      b[0] = 0;\n      return 1;\n    } else {\n      negate = -ytail;\n      s1 = negate * ax;\n      c = splitter * negate;\n      ahi = c - (c - negate);\n      alo = negate - ahi;\n      c = splitter * ax;\n      bhi = c - (c - ax);\n      blo = ax - bhi;\n      a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      a[1] = s1;\n      s1 = ytail * bx;\n      c = splitter * ytail;\n      ahi = c - (c - ytail);\n      alo = ytail - ahi;\n      c = splitter * bx;\n      bhi = c - (c - bx);\n      blo = bx - bhi;\n      b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      b[1] = s1;\n      return 2;\n    }\n  } else {\n    if (ytail === 0) {\n      s1 = xtail * ay;\n      c = splitter * xtail;\n      ahi = c - (c - xtail);\n      alo = xtail - ahi;\n      c = splitter * ay;\n      bhi = c - (c - ay);\n      blo = ay - bhi;\n      a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      a[1] = s1;\n      negate = -xtail;\n      s1 = negate * by;\n      c = splitter * negate;\n      ahi = c - (c - negate);\n      alo = negate - ahi;\n      c = splitter * by;\n      bhi = c - (c - by);\n      blo = by - bhi;\n      b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      b[1] = s1;\n      return 2;\n    } else {\n      s1 = xtail * ay;\n      c = splitter * xtail;\n      ahi = c - (c - xtail);\n      alo = xtail - ahi;\n      c = splitter * ay;\n      bhi = c - (c - ay);\n      blo = ay - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = ytail * ax;\n      c = splitter * ytail;\n      ahi = c - (c - ytail);\n      alo = ytail - ahi;\n      c = splitter * ax;\n      bhi = c - (c - ax);\n      blo = ax - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 - t0;\n      bvirt = s0 - _i;\n      a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 - t1;\n      bvirt = _0 - _i;\n      a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      a[3] = u3;\n      s1 = ytail * bx;\n      c = splitter * ytail;\n      ahi = c - (c - ytail);\n      alo = ytail - ahi;\n      c = splitter * bx;\n      bhi = c - (c - bx);\n      blo = bx - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = xtail * by;\n      c = splitter * xtail;\n      ahi = c - (c - xtail);\n      alo = xtail - ahi;\n      c = splitter * by;\n      bhi = c - (c - by);\n      blo = by - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 - t0;\n      bvirt = s0 - _i;\n      b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 - t1;\n      bvirt = _0 - _i;\n      b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      b[3] = u3;\n      return 4;\n    }\n  }\n}\nfunction tailadd(finlen, a, b, k, z) {\n  let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n  s1 = a * b;\n  c = splitter * a;\n  ahi = c - (c - a);\n  alo = a - ahi;\n  c = splitter * b;\n  bhi = c - (c - b);\n  blo = b - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  c = splitter * k;\n  bhi = c - (c - k);\n  blo = k - bhi;\n  _i = s0 * k;\n  c = splitter * s0;\n  ahi = c - (c - s0);\n  alo = s0 - ahi;\n  u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n  _j = s1 * k;\n  c = splitter * s1;\n  ahi = c - (c - s1);\n  alo = s1 - ahi;\n  _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n  _k = _i + _0;\n  bvirt = _k - _i;\n  u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n  u3 = _j + _k;\n  u[2] = _k - (u3 - _j);\n  u[3] = u3;\n  finlen = finadd(finlen, 4, u);\n  if (z !== 0) {\n    c = splitter * z;\n    bhi = c - (c - z);\n    blo = z - bhi;\n    _i = s0 * z;\n    c = splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * z;\n    c = splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n  }\n  return finlen;\n}\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n  let finlen;\n  let adxtail, bdxtail, cdxtail;\n  let adytail, bdytail, cdytail;\n  let adztail, bdztail, cdztail;\n  let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n  const adx = ax - dx;\n  const bdx = bx - dx;\n  const cdx = cx - dx;\n  const ady = ay - dy;\n  const bdy = by - dy;\n  const cdy = cy - dy;\n  const adz = az - dz;\n  const bdz = bz - dz;\n  const cdz = cz - dz;\n  s1 = bdx * cdy;\n  c = splitter * bdx;\n  ahi = c - (c - bdx);\n  alo = bdx - ahi;\n  c = splitter * cdy;\n  bhi = c - (c - cdy);\n  blo = cdy - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = cdx * bdy;\n  c = splitter * cdx;\n  ahi = c - (c - cdx);\n  alo = cdx - ahi;\n  c = splitter * bdy;\n  bhi = c - (c - bdy);\n  blo = bdy - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  bc[3] = u3;\n  s1 = cdx * ady;\n  c = splitter * cdx;\n  ahi = c - (c - cdx);\n  alo = cdx - ahi;\n  c = splitter * ady;\n  bhi = c - (c - ady);\n  blo = ady - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = adx * cdy;\n  c = splitter * adx;\n  ahi = c - (c - adx);\n  alo = adx - ahi;\n  c = splitter * cdy;\n  bhi = c - (c - cdy);\n  blo = cdy - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  ca[3] = u3;\n  s1 = adx * bdy;\n  c = splitter * adx;\n  ahi = c - (c - adx);\n  alo = adx - ahi;\n  c = splitter * bdy;\n  bhi = c - (c - bdy);\n  blo = bdy - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = bdx * ady;\n  c = splitter * bdx;\n  ahi = c - (c - bdx);\n  alo = bdx - ahi;\n  c = splitter * ady;\n  bhi = c - (c - ady);\n  blo = ady - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  ab[3] = u3;\n  finlen = sum(sum(scale(4, bc, adz, _8), _8, scale(4, ca, bdz, _8b), _8b, _16), _16, scale(4, ab, cdz, _8), _8, fin);\n  let det = estimate(finlen, fin);\n  let errbound = o3derrboundB * permanent;\n  if (det >= errbound || -det >= errbound) {\n    return det;\n  }\n  bvirt = ax - adx;\n  adxtail = ax - (adx + bvirt) + (bvirt - dx);\n  bvirt = bx - bdx;\n  bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n  bvirt = cx - cdx;\n  cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n  bvirt = ay - ady;\n  adytail = ay - (ady + bvirt) + (bvirt - dy);\n  bvirt = by - bdy;\n  bdytail = by - (bdy + bvirt) + (bvirt - dy);\n  bvirt = cy - cdy;\n  cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n  bvirt = az - adz;\n  adztail = az - (adz + bvirt) + (bvirt - dz);\n  bvirt = bz - bdz;\n  bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n  bvirt = cz - cdz;\n  cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n  if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0 && adztail === 0 && bdztail === 0 && cdztail === 0) {\n    return det;\n  }\n  errbound = o3derrboundC * permanent + resulterrbound * Math.abs(det);\n  det += adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) + bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) + cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n  if (det >= errbound || -det >= errbound) {\n    return det;\n  }\n  const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n  const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n  const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n  const bctlen = sum(bt_len, bt_c, ct_len, ct_b, bct);\n  finlen = finadd(finlen, scale(bctlen, bct, adz, _16), _16);\n  const catlen = sum(ct_len, ct_a, at_len, at_c, cat);\n  finlen = finadd(finlen, scale(catlen, cat, bdz, _16), _16);\n  const abtlen = sum(at_len, at_b, bt_len, bt_a, abt);\n  finlen = finadd(finlen, scale(abtlen, abt, cdz, _16), _16);\n  if (adztail !== 0) {\n    finlen = finadd(finlen, scale(4, bc, adztail, _12), _12);\n    finlen = finadd(finlen, scale(bctlen, bct, adztail, _16), _16);\n  }\n  if (bdztail !== 0) {\n    finlen = finadd(finlen, scale(4, ca, bdztail, _12), _12);\n    finlen = finadd(finlen, scale(catlen, cat, bdztail, _16), _16);\n  }\n  if (cdztail !== 0) {\n    finlen = finadd(finlen, scale(4, ab, cdztail, _12), _12);\n    finlen = finadd(finlen, scale(abtlen, abt, cdztail, _16), _16);\n  }\n  if (adxtail !== 0) {\n    if (bdytail !== 0) {\n      finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n    }\n    if (cdytail !== 0) {\n      finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n    }\n  }\n  if (bdxtail !== 0) {\n    if (cdytail !== 0) {\n      finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n    }\n    if (adytail !== 0) {\n      finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n    }\n  }\n  if (cdxtail !== 0) {\n    if (adytail !== 0) {\n      finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n    }\n    if (bdytail !== 0) {\n      finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n    }\n  }\n  return fin[finlen - 1];\n}\nexport function orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n  const adx = ax - dx;\n  const bdx = bx - dx;\n  const cdx = cx - dx;\n  const ady = ay - dy;\n  const bdy = by - dy;\n  const cdy = cy - dy;\n  const adz = az - dz;\n  const bdz = bz - dz;\n  const cdz = cz - dz;\n  const bdxcdy = bdx * cdy;\n  const cdxbdy = cdx * bdy;\n  const cdxady = cdx * ady;\n  const adxcdy = adx * cdy;\n  const adxbdy = adx * bdy;\n  const bdxady = bdx * ady;\n  const det = adz * (bdxcdy - cdxbdy) + bdz * (cdxady - adxcdy) + cdz * (adxbdy - bdxady);\n  const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) + (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) + (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n  const errbound = o3derrboundA * permanent;\n  if (det > errbound || -det > errbound) {\n    return det;\n  }\n  return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\nexport function orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n  const adx = ax - dx;\n  const bdx = bx - dx;\n  const cdx = cx - dx;\n  const ady = ay - dy;\n  const bdy = by - dy;\n  const cdy = cy - dy;\n  const adz = az - dz;\n  const bdz = bz - dz;\n  const cdz = cz - dz;\n  return adx * (bdy * cdz - bdz * cdy) + bdx * (cdy * adz - cdz * ady) + cdx * (ady * bdz - adz * bdy);\n}", "map": {"version": 3, "names": ["epsilon", "splitter", "resulterrbound", "estimate", "vec", "sum", "scale", "o3derrboundA", "o3derrboundB", "o3derrboundC", "bc", "ca", "ab", "at_b", "at_c", "bt_c", "bt_a", "ct_a", "ct_b", "bct", "cat", "abt", "u", "_8", "_8b", "_16", "_12", "fin", "fin2", "finadd", "finlen", "alen", "a", "tmp", "tailinit", "xtail", "ytail", "ax", "ay", "bx", "by", "b", "bvirt", "c", "ahi", "alo", "bhi", "blo", "_i", "_j", "_k", "_0", "s1", "s0", "t1", "t0", "u3", "negate", "tailadd", "k", "z", "orient3dadapt", "az", "bz", "cx", "cy", "cz", "dx", "dy", "dz", "permanent", "adxtail", "bdxtail", "cdxtail", "adytail", "bdytail", "cdytail", "adztail", "bdztail", "cdztail", "adx", "bdx", "cdx", "ady", "bdy", "cdy", "adz", "bdz", "cdz", "det", "errbound", "Math", "abs", "at_len", "bt_len", "ct_len", "bctlen", "catlen", "abtlen", "orient3d", "bdxcdy", "cdxbdy", "cdxady", "adxcdy", "adxbdy", "bdxady", "orient3dfast"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/robust-predicates/esm/orient3d.js"], "sourcesContent": ["import {epsilon, splitter, resulterrbound, estimate, vec, sum, scale} from './util.js';\n\nconst o3derrboundA = (7 + 56 * epsilon) * epsilon;\nconst o3derrboundB = (3 + 28 * epsilon) * epsilon;\nconst o3derrboundC = (26 + 288 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst at_b = vec(4);\nconst at_c = vec(4);\nconst bt_c = vec(4);\nconst bt_a = vec(4);\nconst ct_a = vec(4);\nconst ct_b = vec(4);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abt = vec(8);\nconst u = vec(4);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _16 = vec(8);\nconst _12 = vec(12);\n\nlet fin = vec(192);\nlet fin2 = vec(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = sum(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            scale(4, bc, adz, _8), _8,\n            scale(4, ca, bdz, _8b), _8b, _16), _16,\n        scale(4, ab, cdz, _8), _8, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = sum(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, scale(bctlen, bct, adz, _16), _16);\n\n    const catlen = sum(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, scale(catlen, cat, bdz, _16), _16);\n\n    const abtlen = sum(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, scale(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, scale(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, scale(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, scale(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, scale(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nexport function orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAO,WAAW;AAEtF,MAAMC,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGP,OAAO,IAAIA,OAAO;AACjD,MAAMQ,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGR,OAAO,IAAIA,OAAO;AACjD,MAAMS,YAAY,GAAG,CAAC,EAAE,GAAG,GAAG,GAAGT,OAAO,IAAIA,OAAO,GAAGA,OAAO;AAE7D,MAAMU,EAAE,GAAGN,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMO,EAAE,GAAGP,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMQ,EAAE,GAAGR,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMS,IAAI,GAAGT,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMU,IAAI,GAAGV,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMW,IAAI,GAAGX,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMY,IAAI,GAAGZ,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMa,IAAI,GAAGb,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMc,IAAI,GAAGd,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMe,GAAG,GAAGf,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMgB,GAAG,GAAGhB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMiB,GAAG,GAAGjB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMkB,CAAC,GAAGlB,GAAG,CAAC,CAAC,CAAC;AAEhB,MAAMmB,EAAE,GAAGnB,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMoB,GAAG,GAAGpB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMqB,GAAG,GAAGrB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMsB,GAAG,GAAGtB,GAAG,CAAC,EAAE,CAAC;AAEnB,IAAIuB,GAAG,GAAGvB,GAAG,CAAC,GAAG,CAAC;AAClB,IAAIwB,IAAI,GAAGxB,GAAG,CAAC,GAAG,CAAC;AAEnB,SAASyB,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAEC,CAAC,EAAE;EAC7BF,MAAM,GAAGzB,GAAG,CAACyB,MAAM,EAAEH,GAAG,EAAEI,IAAI,EAAEC,CAAC,EAAEJ,IAAI,CAAC;EACxC,MAAMK,GAAG,GAAGN,GAAG;EAAEA,GAAG,GAAGC,IAAI;EAAEA,IAAI,GAAGK,GAAG;EACvC,OAAOH,MAAM;AACjB;AAEA,SAASI,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAER,CAAC,EAAES,CAAC,EAAE;EAClD,IAAIC,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,MAAM;EAC5E,IAAItB,KAAK,KAAK,CAAC,EAAE;IACb,IAAIC,KAAK,KAAK,CAAC,EAAE;MACbJ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACRS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACR,OAAO,CAAC;IACZ,CAAC,MAAM;MACHgB,MAAM,GAAG,CAACrB,KAAK;MACfgB,EAAE,GAAGK,MAAM,GAAGpB,EAAE;MAChBM,CAAC,GAAG1C,QAAQ,GAAGwD,MAAM;MACrBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,MAAM,CAAC;MACtBZ,GAAG,GAAGY,MAAM,GAAGb,GAAG;MAClBD,CAAC,GAAG1C,QAAQ,GAAGoC,EAAE;MACjBS,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGN,EAAE,CAAC;MAClBU,GAAG,GAAGV,EAAE,GAAGS,GAAG;MACdd,CAAC,CAAC,CAAC,CAAC,GAAGa,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MAC3Df,CAAC,CAAC,CAAC,CAAC,GAAGoB,EAAE;MACTA,EAAE,GAAGhB,KAAK,GAAGG,EAAE;MACfI,CAAC,GAAG1C,QAAQ,GAAGmC,KAAK;MACpBQ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGP,KAAK,CAAC;MACrBS,GAAG,GAAGT,KAAK,GAAGQ,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGsC,EAAE;MACjBO,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGJ,EAAE,CAAC;MAClBQ,GAAG,GAAGR,EAAE,GAAGO,GAAG;MACdL,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MAC3DN,CAAC,CAAC,CAAC,CAAC,GAAGW,EAAE;MACT,OAAO,CAAC;IACZ;EACJ,CAAC,MAAM;IACH,IAAIhB,KAAK,KAAK,CAAC,EAAE;MACbgB,EAAE,GAAGjB,KAAK,GAAGG,EAAE;MACfK,CAAC,GAAG1C,QAAQ,GAAGkC,KAAK;MACpBS,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGR,KAAK,CAAC;MACrBU,GAAG,GAAGV,KAAK,GAAGS,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGqC,EAAE;MACjBQ,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGL,EAAE,CAAC;MAClBS,GAAG,GAAGT,EAAE,GAAGQ,GAAG;MACdd,CAAC,CAAC,CAAC,CAAC,GAAGa,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MAC3Df,CAAC,CAAC,CAAC,CAAC,GAAGoB,EAAE;MACTK,MAAM,GAAG,CAACtB,KAAK;MACfiB,EAAE,GAAGK,MAAM,GAAGjB,EAAE;MAChBG,CAAC,GAAG1C,QAAQ,GAAGwD,MAAM;MACrBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,MAAM,CAAC;MACtBZ,GAAG,GAAGY,MAAM,GAAGb,GAAG;MAClBD,CAAC,GAAG1C,QAAQ,GAAGuC,EAAE;MACjBM,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGH,EAAE,CAAC;MAClBO,GAAG,GAAGP,EAAE,GAAGM,GAAG;MACdL,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MAC3DN,CAAC,CAAC,CAAC,CAAC,GAAGW,EAAE;MACT,OAAO,CAAC;IACZ,CAAC,MAAM;MACHA,EAAE,GAAGjB,KAAK,GAAGG,EAAE;MACfK,CAAC,GAAG1C,QAAQ,GAAGkC,KAAK;MACpBS,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGR,KAAK,CAAC;MACrBU,GAAG,GAAGV,KAAK,GAAGS,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGqC,EAAE;MACjBQ,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGL,EAAE,CAAC;MAClBS,GAAG,GAAGT,EAAE,GAAGQ,GAAG;MACdO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDO,EAAE,GAAGlB,KAAK,GAAGC,EAAE;MACfM,CAAC,GAAG1C,QAAQ,GAAGmC,KAAK;MACpBQ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGP,KAAK,CAAC;MACrBS,GAAG,GAAGT,KAAK,GAAGQ,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGoC,EAAE;MACjBS,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGN,EAAE,CAAC;MAClBU,GAAG,GAAGV,EAAE,GAAGS,GAAG;MACdS,EAAE,GAAGV,GAAG,GAAGE,GAAG,IAAIO,EAAE,GAAGV,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGK,EAAE,GAAGE,EAAE;MACZb,KAAK,GAAGW,EAAE,GAAGL,EAAE;MACfhB,CAAC,CAAC,CAAC,CAAC,GAAGqB,EAAE,IAAIL,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGa,EAAE,CAAC;MACvCN,EAAE,GAAGG,EAAE,GAAGJ,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGG,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIH,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGG,EAAE,GAAGG,EAAE;MACZZ,KAAK,GAAGS,EAAE,GAAGH,EAAE;MACfhB,CAAC,CAAC,CAAC,CAAC,GAAGmB,EAAE,IAAIH,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;MACvCE,EAAE,GAAGP,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGc,EAAE,GAAGP,EAAE;MACfjB,CAAC,CAAC,CAAC,CAAC,GAAGiB,EAAE,IAAIO,EAAE,GAAGd,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvCV,CAAC,CAAC,CAAC,CAAC,GAAGwB,EAAE;MACTJ,EAAE,GAAGhB,KAAK,GAAGG,EAAE;MACfI,CAAC,GAAG1C,QAAQ,GAAGmC,KAAK;MACpBQ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGP,KAAK,CAAC;MACrBS,GAAG,GAAGT,KAAK,GAAGQ,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGsC,EAAE;MACjBO,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGJ,EAAE,CAAC;MAClBQ,GAAG,GAAGR,EAAE,GAAGO,GAAG;MACdO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDO,EAAE,GAAGnB,KAAK,GAAGK,EAAE;MACfG,CAAC,GAAG1C,QAAQ,GAAGkC,KAAK;MACpBS,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGR,KAAK,CAAC;MACrBU,GAAG,GAAGV,KAAK,GAAGS,GAAG;MACjBD,CAAC,GAAG1C,QAAQ,GAAGuC,EAAE;MACjBM,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGH,EAAE,CAAC;MAClBO,GAAG,GAAGP,EAAE,GAAGM,GAAG;MACdS,EAAE,GAAGV,GAAG,GAAGE,GAAG,IAAIO,EAAE,GAAGV,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGK,EAAE,GAAGE,EAAE;MACZb,KAAK,GAAGW,EAAE,GAAGL,EAAE;MACfP,CAAC,CAAC,CAAC,CAAC,GAAGY,EAAE,IAAIL,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGa,EAAE,CAAC;MACvCN,EAAE,GAAGG,EAAE,GAAGJ,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGG,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIH,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGG,EAAE,GAAGG,EAAE;MACZZ,KAAK,GAAGS,EAAE,GAAGH,EAAE;MACfP,CAAC,CAAC,CAAC,CAAC,GAAGU,EAAE,IAAIH,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;MACvCE,EAAE,GAAGP,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGc,EAAE,GAAGP,EAAE;MACfR,CAAC,CAAC,CAAC,CAAC,GAAGQ,EAAE,IAAIO,EAAE,GAAGd,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvCD,CAAC,CAAC,CAAC,CAAC,GAAGe,EAAE;MACT,OAAO,CAAC;IACZ;EACJ;AACJ;AAEA,SAASE,OAAOA,CAAC5B,MAAM,EAAEE,CAAC,EAAES,CAAC,EAAEkB,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAIlB,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;EAC5DJ,EAAE,GAAGpB,CAAC,GAAGS,CAAC;EACVE,CAAC,GAAG1C,QAAQ,GAAG+B,CAAC;EAChBY,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGX,CAAC,CAAC;EACjBa,GAAG,GAAGb,CAAC,GAAGY,GAAG;EACbD,CAAC,GAAG1C,QAAQ,GAAGwC,CAAC;EAChBK,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGF,CAAC,CAAC;EACjBM,GAAG,GAAGN,CAAC,GAAGK,GAAG;EACbO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDJ,CAAC,GAAG1C,QAAQ,GAAG0D,CAAC;EAChBb,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGgB,CAAC,CAAC;EACjBZ,GAAG,GAAGY,CAAC,GAAGb,GAAG;EACbE,EAAE,GAAGK,EAAE,GAAGM,CAAC;EACXhB,CAAC,GAAG1C,QAAQ,GAAGoD,EAAE;EACjBT,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGU,EAAE,CAAC;EAClBR,GAAG,GAAGQ,EAAE,GAAGT,GAAG;EACdtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,GAAG,GAAGE,GAAG,IAAIC,EAAE,GAAGJ,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EAC3DE,EAAE,GAAGG,EAAE,GAAGO,CAAC;EACXhB,CAAC,GAAG1C,QAAQ,GAAGmD,EAAE;EACjBR,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGS,EAAE,CAAC;EAClBP,GAAG,GAAGO,EAAE,GAAGR,GAAG;EACdO,EAAE,GAAGN,GAAG,GAAGE,GAAG,IAAIE,EAAE,GAAGL,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDG,EAAE,GAAGF,EAAE,GAAGG,EAAE;EACZT,KAAK,GAAGQ,EAAE,GAAGF,EAAE;EACf1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,EAAE,IAAIE,EAAE,GAAGR,KAAK,CAAC,IAAIS,EAAE,GAAGT,KAAK,CAAC;EACvCc,EAAE,GAAGP,EAAE,GAAGC,EAAE;EACZ5B,CAAC,CAAC,CAAC,CAAC,GAAG4B,EAAE,IAAIM,EAAE,GAAGP,EAAE,CAAC;EACrB3B,CAAC,CAAC,CAAC,CAAC,GAAGkC,EAAE;EACT1B,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAE,CAAC,EAAER,CAAC,CAAC;EAC7B,IAAIsC,CAAC,KAAK,CAAC,EAAE;IACTjB,CAAC,GAAG1C,QAAQ,GAAG2D,CAAC;IAChBd,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGiB,CAAC,CAAC;IACjBb,GAAG,GAAGa,CAAC,GAAGd,GAAG;IACbE,EAAE,GAAGK,EAAE,GAAGO,CAAC;IACXjB,CAAC,GAAG1C,QAAQ,GAAGoD,EAAE;IACjBT,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGU,EAAE,CAAC;IAClBR,GAAG,GAAGQ,EAAE,GAAGT,GAAG;IACdtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,GAAG,GAAGE,GAAG,IAAIC,EAAE,GAAGJ,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;IAC3DE,EAAE,GAAGG,EAAE,GAAGQ,CAAC;IACXjB,CAAC,GAAG1C,QAAQ,GAAGmD,EAAE;IACjBR,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGS,EAAE,CAAC;IAClBP,GAAG,GAAGO,EAAE,GAAGR,GAAG;IACdO,EAAE,GAAGN,GAAG,GAAGE,GAAG,IAAIE,EAAE,GAAGL,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;IACzDG,EAAE,GAAGF,EAAE,GAAGG,EAAE;IACZT,KAAK,GAAGQ,EAAE,GAAGF,EAAE;IACf1B,CAAC,CAAC,CAAC,CAAC,GAAG0B,EAAE,IAAIE,EAAE,GAAGR,KAAK,CAAC,IAAIS,EAAE,GAAGT,KAAK,CAAC;IACvCc,EAAE,GAAGP,EAAE,GAAGC,EAAE;IACZ5B,CAAC,CAAC,CAAC,CAAC,GAAG4B,EAAE,IAAIM,EAAE,GAAGP,EAAE,CAAC;IACrB3B,CAAC,CAAC,CAAC,CAAC,GAAGkC,EAAE;IACT1B,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAE,CAAC,EAAER,CAAC,CAAC;EACjC;EACA,OAAOQ,MAAM;AACjB;AAEA,SAAS+B,aAAaA,CAACxB,EAAE,EAAEC,EAAE,EAAEwB,EAAE,EAAEvB,EAAE,EAAEC,EAAE,EAAEuB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;EAC9E,IAAIxC,MAAM;EACV,IAAIyC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EAC7B,IAAIC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EAC7B,IAAIC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EAC7B,IAAIrC,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAEpE,MAAMwB,GAAG,GAAG3C,EAAE,GAAG8B,EAAE;EACnB,MAAMc,GAAG,GAAG1C,EAAE,GAAG4B,EAAE;EACnB,MAAMe,GAAG,GAAGlB,EAAE,GAAGG,EAAE;EACnB,MAAMgB,GAAG,GAAG7C,EAAE,GAAG8B,EAAE;EACnB,MAAMgB,GAAG,GAAG5C,EAAE,GAAG4B,EAAE;EACnB,MAAMiB,GAAG,GAAGpB,EAAE,GAAGG,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGO,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGM,EAAE;EACnB,MAAMmB,GAAG,GAAGtB,EAAE,GAAGG,EAAE;EAEnBjB,EAAE,GAAG6B,GAAG,GAAGI,GAAG;EACd1C,CAAC,GAAG1C,QAAQ,GAAGgF,GAAG;EAClBrC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGsC,GAAG,CAAC;EACnBpC,GAAG,GAAGoC,GAAG,GAAGrC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGoF,GAAG;EAClBvC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAG0C,GAAG,CAAC;EACnBtC,GAAG,GAAGsC,GAAG,GAAGvC,GAAG;EACfO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDO,EAAE,GAAG4B,GAAG,GAAGE,GAAG;EACdzC,CAAC,GAAG1C,QAAQ,GAAGiF,GAAG;EAClBtC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGuC,GAAG,CAAC;EACnBrC,GAAG,GAAGqC,GAAG,GAAGtC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGmF,GAAG;EAClBtC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGyC,GAAG,CAAC;EACnBrC,GAAG,GAAGqC,GAAG,GAAGtC,GAAG;EACfS,EAAE,GAAGV,GAAG,GAAGE,GAAG,IAAIO,EAAE,GAAGV,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGK,EAAE,GAAGE,EAAE;EACZb,KAAK,GAAGW,EAAE,GAAGL,EAAE;EACftC,EAAE,CAAC,CAAC,CAAC,GAAG2C,EAAE,IAAIL,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGa,EAAE,CAAC;EACxCN,EAAE,GAAGG,EAAE,GAAGJ,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGG,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIH,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGG,EAAE,GAAGG,EAAE;EACZZ,KAAK,GAAGS,EAAE,GAAGH,EAAE;EACftC,EAAE,CAAC,CAAC,CAAC,GAAGyC,EAAE,IAAIH,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCE,EAAE,GAAGP,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGc,EAAE,GAAGP,EAAE;EACfvC,EAAE,CAAC,CAAC,CAAC,GAAGuC,EAAE,IAAIO,EAAE,GAAGd,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxChC,EAAE,CAAC,CAAC,CAAC,GAAG8C,EAAE;EACVJ,EAAE,GAAG8B,GAAG,GAAGC,GAAG;EACdxC,CAAC,GAAG1C,QAAQ,GAAGiF,GAAG;EAClBtC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGuC,GAAG,CAAC;EACnBrC,GAAG,GAAGqC,GAAG,GAAGtC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGkF,GAAG;EAClBrC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGwC,GAAG,CAAC;EACnBpC,GAAG,GAAGoC,GAAG,GAAGrC,GAAG;EACfO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDO,EAAE,GAAG0B,GAAG,GAAGK,GAAG;EACd1C,CAAC,GAAG1C,QAAQ,GAAG+E,GAAG;EAClBpC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGqC,GAAG,CAAC;EACnBnC,GAAG,GAAGmC,GAAG,GAAGpC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGoF,GAAG;EAClBvC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAG0C,GAAG,CAAC;EACnBtC,GAAG,GAAGsC,GAAG,GAAGvC,GAAG;EACfS,EAAE,GAAGV,GAAG,GAAGE,GAAG,IAAIO,EAAE,GAAGV,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGK,EAAE,GAAGE,EAAE;EACZb,KAAK,GAAGW,EAAE,GAAGL,EAAE;EACfrC,EAAE,CAAC,CAAC,CAAC,GAAG0C,EAAE,IAAIL,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGa,EAAE,CAAC;EACxCN,EAAE,GAAGG,EAAE,GAAGJ,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGG,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIH,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGG,EAAE,GAAGG,EAAE;EACZZ,KAAK,GAAGS,EAAE,GAAGH,EAAE;EACfrC,EAAE,CAAC,CAAC,CAAC,GAAGwC,EAAE,IAAIH,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCE,EAAE,GAAGP,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGc,EAAE,GAAGP,EAAE;EACftC,EAAE,CAAC,CAAC,CAAC,GAAGsC,EAAE,IAAIO,EAAE,GAAGd,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxC/B,EAAE,CAAC,CAAC,CAAC,GAAG6C,EAAE;EACVJ,EAAE,GAAG4B,GAAG,GAAGI,GAAG;EACdzC,CAAC,GAAG1C,QAAQ,GAAG+E,GAAG;EAClBpC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGqC,GAAG,CAAC;EACnBnC,GAAG,GAAGmC,GAAG,GAAGpC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGmF,GAAG;EAClBtC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGyC,GAAG,CAAC;EACnBrC,GAAG,GAAGqC,GAAG,GAAGtC,GAAG;EACfO,EAAE,GAAGR,GAAG,GAAGE,GAAG,IAAIK,EAAE,GAAGR,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDO,EAAE,GAAG2B,GAAG,GAAGE,GAAG;EACdxC,CAAC,GAAG1C,QAAQ,GAAGgF,GAAG;EAClBrC,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGsC,GAAG,CAAC;EACnBpC,GAAG,GAAGoC,GAAG,GAAGrC,GAAG;EACfD,CAAC,GAAG1C,QAAQ,GAAGkF,GAAG;EAClBrC,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGwC,GAAG,CAAC;EACnBpC,GAAG,GAAGoC,GAAG,GAAGrC,GAAG;EACfS,EAAE,GAAGV,GAAG,GAAGE,GAAG,IAAIO,EAAE,GAAGV,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGK,EAAE,GAAGE,EAAE;EACZb,KAAK,GAAGW,EAAE,GAAGL,EAAE;EACfpC,EAAE,CAAC,CAAC,CAAC,GAAGyC,EAAE,IAAIL,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGa,EAAE,CAAC;EACxCN,EAAE,GAAGG,EAAE,GAAGJ,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGG,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIH,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGG,EAAE,GAAGG,EAAE;EACZZ,KAAK,GAAGS,EAAE,GAAGH,EAAE;EACfpC,EAAE,CAAC,CAAC,CAAC,GAAGuC,EAAE,IAAIH,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCE,EAAE,GAAGP,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGc,EAAE,GAAGP,EAAE;EACfrC,EAAE,CAAC,CAAC,CAAC,GAAGqC,EAAE,IAAIO,EAAE,GAAGd,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxC9B,EAAE,CAAC,CAAC,CAAC,GAAG4C,EAAE;EAEV1B,MAAM,GAAGzB,GAAG,CACRA,GAAG,CACCC,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAE4E,GAAG,EAAE/D,EAAE,CAAC,EAAEA,EAAE,EACzBjB,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAE4E,GAAG,EAAE/D,GAAG,CAAC,EAAEA,GAAG,EAAEC,GAAG,CAAC,EAAEA,GAAG,EAC1CnB,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAE4E,GAAG,EAAEjE,EAAE,CAAC,EAAEA,EAAE,EAAEI,GAAG,CAAC;EAEnC,IAAI8D,GAAG,GAAGtF,QAAQ,CAAC2B,MAAM,EAAEH,GAAG,CAAC;EAC/B,IAAI+D,QAAQ,GAAGlF,YAAY,GAAG8D,SAAS;EACvC,IAAImB,GAAG,IAAIC,QAAQ,IAAI,CAACD,GAAG,IAAIC,QAAQ,EAAE;IACrC,OAAOD,GAAG;EACd;EAEA/C,KAAK,GAAGL,EAAE,GAAG2C,GAAG;EAChBT,OAAO,GAAGlC,EAAE,IAAI2C,GAAG,GAAGtC,KAAK,CAAC,IAAIA,KAAK,GAAGyB,EAAE,CAAC;EAC3CzB,KAAK,GAAGH,EAAE,GAAG0C,GAAG;EAChBT,OAAO,GAAGjC,EAAE,IAAI0C,GAAG,GAAGvC,KAAK,CAAC,IAAIA,KAAK,GAAGyB,EAAE,CAAC;EAC3CzB,KAAK,GAAGsB,EAAE,GAAGkB,GAAG;EAChBT,OAAO,GAAGT,EAAE,IAAIkB,GAAG,GAAGxC,KAAK,CAAC,IAAIA,KAAK,GAAGyB,EAAE,CAAC;EAC3CzB,KAAK,GAAGJ,EAAE,GAAG6C,GAAG;EAChBT,OAAO,GAAGpC,EAAE,IAAI6C,GAAG,GAAGzC,KAAK,CAAC,IAAIA,KAAK,GAAG0B,EAAE,CAAC;EAC3C1B,KAAK,GAAGF,EAAE,GAAG4C,GAAG;EAChBT,OAAO,GAAGnC,EAAE,IAAI4C,GAAG,GAAG1C,KAAK,CAAC,IAAIA,KAAK,GAAG0B,EAAE,CAAC;EAC3C1B,KAAK,GAAGuB,EAAE,GAAGoB,GAAG;EAChBT,OAAO,GAAGX,EAAE,IAAIoB,GAAG,GAAG3C,KAAK,CAAC,IAAIA,KAAK,GAAG0B,EAAE,CAAC;EAC3C1B,KAAK,GAAGoB,EAAE,GAAGwB,GAAG;EAChBT,OAAO,GAAGf,EAAE,IAAIwB,GAAG,GAAG5C,KAAK,CAAC,IAAIA,KAAK,GAAG2B,EAAE,CAAC;EAC3C3B,KAAK,GAAGqB,EAAE,GAAGwB,GAAG;EAChBT,OAAO,GAAGf,EAAE,IAAIwB,GAAG,GAAG7C,KAAK,CAAC,IAAIA,KAAK,GAAG2B,EAAE,CAAC;EAC3C3B,KAAK,GAAGwB,EAAE,GAAGsB,GAAG;EAChBT,OAAO,GAAGb,EAAE,IAAIsB,GAAG,GAAG9C,KAAK,CAAC,IAAIA,KAAK,GAAG2B,EAAE,CAAC;EAE3C,IAAIE,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAC/CC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAC/CC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,EAAE;IACjD,OAAOU,GAAG;EACd;EAEAC,QAAQ,GAAGjF,YAAY,GAAG6D,SAAS,GAAGpE,cAAc,GAAGyF,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC;EACpEA,GAAG,IACCH,GAAG,IAAIL,GAAG,GAAGL,OAAO,GAAGS,GAAG,GAAGb,OAAO,IAAIY,GAAG,GAAGX,OAAO,GAAGS,GAAG,GAAGP,OAAO,CAAC,CAAC,GAAGE,OAAO,IAAII,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC,GAC3GK,GAAG,IAAIL,GAAG,GAAGR,OAAO,GAAGS,GAAG,GAAGV,OAAO,IAAIY,GAAG,GAAGd,OAAO,GAAGS,GAAG,GAAGJ,OAAO,CAAC,CAAC,GAAGE,OAAO,IAAII,GAAG,GAAGC,GAAG,GAAGE,GAAG,GAAGL,GAAG,CAAC,GAC3GQ,GAAG,IAAIR,GAAG,GAAGL,OAAO,GAAGS,GAAG,GAAGb,OAAO,IAAIY,GAAG,GAAGX,OAAO,GAAGS,GAAG,GAAGP,OAAO,CAAC,CAAC,GAAGK,OAAO,IAAIC,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC;EAC/G,IAAIQ,GAAG,IAAIC,QAAQ,IAAI,CAACD,GAAG,IAAIC,QAAQ,EAAE;IACrC,OAAOD,GAAG;EACd;EAEA,MAAMI,MAAM,GAAG3D,QAAQ,CAACqC,OAAO,EAAEG,OAAO,EAAEO,GAAG,EAAEG,GAAG,EAAEF,GAAG,EAAEG,GAAG,EAAExE,IAAI,EAAEC,IAAI,CAAC;EACzE,MAAMgF,MAAM,GAAG5D,QAAQ,CAACsC,OAAO,EAAEG,OAAO,EAAEO,GAAG,EAAEG,GAAG,EAAEL,GAAG,EAAEG,GAAG,EAAEpE,IAAI,EAAEC,IAAI,CAAC;EACzE,MAAM+E,MAAM,GAAG7D,QAAQ,CAACuC,OAAO,EAAEG,OAAO,EAAEI,GAAG,EAAEG,GAAG,EAAEF,GAAG,EAAEG,GAAG,EAAEnE,IAAI,EAAEC,IAAI,CAAC;EAEzE,MAAM8E,MAAM,GAAG3F,GAAG,CAACyF,MAAM,EAAE/E,IAAI,EAAEgF,MAAM,EAAE7E,IAAI,EAAEC,GAAG,CAAC;EACnDW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC0F,MAAM,EAAE7E,GAAG,EAAEmE,GAAG,EAAE7D,GAAG,CAAC,EAAEA,GAAG,CAAC;EAE1D,MAAMwE,MAAM,GAAG5F,GAAG,CAAC0F,MAAM,EAAE9E,IAAI,EAAE4E,MAAM,EAAE/E,IAAI,EAAEM,GAAG,CAAC;EACnDU,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC2F,MAAM,EAAE7E,GAAG,EAAEmE,GAAG,EAAE9D,GAAG,CAAC,EAAEA,GAAG,CAAC;EAE1D,MAAMyE,MAAM,GAAG7F,GAAG,CAACwF,MAAM,EAAEhF,IAAI,EAAEiF,MAAM,EAAE9E,IAAI,EAAEK,GAAG,CAAC;EACnDS,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC4F,MAAM,EAAE7E,GAAG,EAAEmE,GAAG,EAAE/D,GAAG,CAAC,EAAEA,GAAG,CAAC;EAE1D,IAAIoD,OAAO,KAAK,CAAC,EAAE;IACf/C,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAEmE,OAAO,EAAEnD,GAAG,CAAC,EAAEA,GAAG,CAAC;IACxDI,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC0F,MAAM,EAAE7E,GAAG,EAAE0D,OAAO,EAAEpD,GAAG,CAAC,EAAEA,GAAG,CAAC;EAClE;EACA,IAAIqD,OAAO,KAAK,CAAC,EAAE;IACfhD,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAEmE,OAAO,EAAEpD,GAAG,CAAC,EAAEA,GAAG,CAAC;IACxDI,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC2F,MAAM,EAAE7E,GAAG,EAAE0D,OAAO,EAAErD,GAAG,CAAC,EAAEA,GAAG,CAAC;EAClE;EACA,IAAIsD,OAAO,KAAK,CAAC,EAAE;IACfjD,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAEmE,OAAO,EAAErD,GAAG,CAAC,EAAEA,GAAG,CAAC;IACxDI,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAExB,KAAK,CAAC4F,MAAM,EAAE7E,GAAG,EAAE0D,OAAO,EAAEtD,GAAG,CAAC,EAAEA,GAAG,CAAC;EAClE;EAEA,IAAI8C,OAAO,KAAK,CAAC,EAAE;IACf,IAAII,OAAO,KAAK,CAAC,EAAE;MACf7C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAEyC,OAAO,EAAEI,OAAO,EAAEa,GAAG,EAAET,OAAO,CAAC;IAC5D;IACA,IAAIH,OAAO,KAAK,CAAC,EAAE;MACf9C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAE,CAACyC,OAAO,EAAEK,OAAO,EAAEW,GAAG,EAAET,OAAO,CAAC;IAC7D;EACJ;EACA,IAAIN,OAAO,KAAK,CAAC,EAAE;IACf,IAAII,OAAO,KAAK,CAAC,EAAE;MACf9C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAE0C,OAAO,EAAEI,OAAO,EAAEU,GAAG,EAAET,OAAO,CAAC;IAC5D;IACA,IAAIH,OAAO,KAAK,CAAC,EAAE;MACf5C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAE,CAAC0C,OAAO,EAAEE,OAAO,EAAEc,GAAG,EAAET,OAAO,CAAC;IAC7D;EACJ;EACA,IAAIN,OAAO,KAAK,CAAC,EAAE;IACf,IAAIC,OAAO,KAAK,CAAC,EAAE;MACf5C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAE2C,OAAO,EAAEC,OAAO,EAAEa,GAAG,EAAET,OAAO,CAAC;IAC5D;IACA,IAAIH,OAAO,KAAK,CAAC,EAAE;MACf7C,MAAM,GAAG4B,OAAO,CAAC5B,MAAM,EAAE,CAAC2C,OAAO,EAAEE,OAAO,EAAEW,GAAG,EAAET,OAAO,CAAC;IAC7D;EACJ;EAEA,OAAOlD,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;AAC1B;AAEA,OAAO,SAASqE,QAAQA,CAAC9D,EAAE,EAAEC,EAAE,EAAEwB,EAAE,EAAEvB,EAAE,EAAEC,EAAE,EAAEuB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACrE,MAAMW,GAAG,GAAG3C,EAAE,GAAG8B,EAAE;EACnB,MAAMc,GAAG,GAAG1C,EAAE,GAAG4B,EAAE;EACnB,MAAMe,GAAG,GAAGlB,EAAE,GAAGG,EAAE;EACnB,MAAMgB,GAAG,GAAG7C,EAAE,GAAG8B,EAAE;EACnB,MAAMgB,GAAG,GAAG5C,EAAE,GAAG4B,EAAE;EACnB,MAAMiB,GAAG,GAAGpB,EAAE,GAAGG,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGO,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGM,EAAE;EACnB,MAAMmB,GAAG,GAAGtB,EAAE,GAAGG,EAAE;EAEnB,MAAM+B,MAAM,GAAGnB,GAAG,GAAGI,GAAG;EACxB,MAAMgB,MAAM,GAAGnB,GAAG,GAAGE,GAAG;EAExB,MAAMkB,MAAM,GAAGpB,GAAG,GAAGC,GAAG;EACxB,MAAMoB,MAAM,GAAGvB,GAAG,GAAGK,GAAG;EAExB,MAAMmB,MAAM,GAAGxB,GAAG,GAAGI,GAAG;EACxB,MAAMqB,MAAM,GAAGxB,GAAG,GAAGE,GAAG;EAExB,MAAMM,GAAG,GACLH,GAAG,IAAIc,MAAM,GAAGC,MAAM,CAAC,GACvBd,GAAG,IAAIe,MAAM,GAAGC,MAAM,CAAC,GACvBf,GAAG,IAAIgB,MAAM,GAAGC,MAAM,CAAC;EAE3B,MAAMnC,SAAS,GACX,CAACqB,IAAI,CAACC,GAAG,CAACQ,MAAM,CAAC,GAAGT,IAAI,CAACC,GAAG,CAACS,MAAM,CAAC,IAAIV,IAAI,CAACC,GAAG,CAACN,GAAG,CAAC,GACrD,CAACK,IAAI,CAACC,GAAG,CAACU,MAAM,CAAC,GAAGX,IAAI,CAACC,GAAG,CAACW,MAAM,CAAC,IAAIZ,IAAI,CAACC,GAAG,CAACL,GAAG,CAAC,GACrD,CAACI,IAAI,CAACC,GAAG,CAACY,MAAM,CAAC,GAAGb,IAAI,CAACC,GAAG,CAACa,MAAM,CAAC,IAAId,IAAI,CAACC,GAAG,CAACJ,GAAG,CAAC;EAEzD,MAAME,QAAQ,GAAGnF,YAAY,GAAG+D,SAAS;EACzC,IAAImB,GAAG,GAAGC,QAAQ,IAAI,CAACD,GAAG,GAAGC,QAAQ,EAAE;IACnC,OAAOD,GAAG;EACd;EAEA,OAAO5B,aAAa,CAACxB,EAAE,EAAEC,EAAE,EAAEwB,EAAE,EAAEvB,EAAE,EAAEC,EAAE,EAAEuB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,CAAC;AACnF;AAEA,OAAO,SAASoC,YAAYA,CAACrE,EAAE,EAAEC,EAAE,EAAEwB,EAAE,EAAEvB,EAAE,EAAEC,EAAE,EAAEuB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACzE,MAAMW,GAAG,GAAG3C,EAAE,GAAG8B,EAAE;EACnB,MAAMc,GAAG,GAAG1C,EAAE,GAAG4B,EAAE;EACnB,MAAMe,GAAG,GAAGlB,EAAE,GAAGG,EAAE;EACnB,MAAMgB,GAAG,GAAG7C,EAAE,GAAG8B,EAAE;EACnB,MAAMgB,GAAG,GAAG5C,EAAE,GAAG4B,EAAE;EACnB,MAAMiB,GAAG,GAAGpB,EAAE,GAAGG,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGO,EAAE;EACnB,MAAMkB,GAAG,GAAGxB,EAAE,GAAGM,EAAE;EACnB,MAAMmB,GAAG,GAAGtB,EAAE,GAAGG,EAAE;EAEnB,OAAOW,GAAG,IAAII,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC,GAChCJ,GAAG,IAAII,GAAG,GAAGC,GAAG,GAAGE,GAAG,GAAGL,GAAG,CAAC,GAC7BD,GAAG,IAAIC,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}