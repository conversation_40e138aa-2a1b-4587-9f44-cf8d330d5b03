{"ast": null, "code": "/* IMPORT */\nimport adjustChannel from './adjust_channel.js';\n/* MAIN */\nconst transparentize = (color, amount) => {\n  return adjustChannel(color, 'a', -amount);\n};\n/* EXPORT */\nexport default transparentize;", "map": {"version": 3, "names": ["adjustChannel", "transparentize", "color", "amount"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/transparentize.js"], "sourcesContent": ["/* IMPORT */\nimport adjustChannel from './adjust_channel.js';\n/* MAIN */\nconst transparentize = (color, amount) => {\n    return adjustChannel(color, 'a', -amount);\n};\n/* EXPORT */\nexport default transparentize;\n"], "mappings": "AAAA;AACA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C;AACA,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACtC,OAAOH,aAAa,CAACE,KAAK,EAAE,GAAG,EAAE,CAACC,MAAM,CAAC;AAC7C,CAAC;AACD;AACA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}