{"ast": null, "code": "/* IMPORT */\nimport Hex from './hex.js';\n/* MAIN */\nconst Keyword = {\n  /* VARIABLES */\n  colors: {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyanaqua: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    gold: '#ffd700',\n    goldenrod: '#daa520',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavender: '#e6e6fa',\n    lavenderblush: '#fff0f5',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    transparent: '#00000000',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32'\n  },\n  /* API */\n  parse: color => {\n    color = color.toLowerCase();\n    const hex = Keyword.colors[color];\n    if (!hex) return;\n    return Hex.parse(hex);\n  },\n  stringify: channels => {\n    const hex = Hex.stringify(channels);\n    for (const name in Keyword.colors) {\n      if (Keyword.colors[name] === hex) return name;\n    }\n    return;\n  }\n};\n/* EXPORT */\nexport default Keyword;", "map": {"version": 3, "names": ["Hex", "Keyword", "colors", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyana<PERSON>", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "tan", "teal", "thistle", "transparent", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "parse", "color", "toLowerCase", "hex", "stringify", "channels", "name"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/color/keyword.js"], "sourcesContent": ["/* IMPORT */\nimport Hex from './hex.js';\n/* MAIN */\nconst Keyword = {\n    /* VARIABLES */\n    colors: {\n        aliceblue: '#f0f8ff',\n        antiquewhite: '#faebd7',\n        aqua: '#00ffff',\n        aquamarine: '#7fffd4',\n        azure: '#f0ffff',\n        beige: '#f5f5dc',\n        bisque: '#ffe4c4',\n        black: '#000000',\n        blanchedalmond: '#ffebcd',\n        blue: '#0000ff',\n        blueviolet: '#8a2be2',\n        brown: '#a52a2a',\n        burlywood: '#deb887',\n        cadetblue: '#5f9ea0',\n        chartreuse: '#7fff00',\n        chocolate: '#d2691e',\n        coral: '#ff7f50',\n        cornflowerblue: '#6495ed',\n        cornsilk: '#fff8dc',\n        crimson: '#dc143c',\n        cyanaqua: '#00ffff',\n        darkblue: '#00008b',\n        darkcyan: '#008b8b',\n        darkgoldenrod: '#b8860b',\n        darkgray: '#a9a9a9',\n        darkgreen: '#006400',\n        darkgrey: '#a9a9a9',\n        darkkhaki: '#bdb76b',\n        darkmagenta: '#8b008b',\n        darkolivegreen: '#556b2f',\n        darkorange: '#ff8c00',\n        darkorchid: '#9932cc',\n        darkred: '#8b0000',\n        darksalmon: '#e9967a',\n        darkseagreen: '#8fbc8f',\n        darkslateblue: '#483d8b',\n        darkslategray: '#2f4f4f',\n        darkslategrey: '#2f4f4f',\n        darkturquoise: '#00ced1',\n        darkviolet: '#9400d3',\n        deeppink: '#ff1493',\n        deepskyblue: '#00bfff',\n        dimgray: '#696969',\n        dimgrey: '#696969',\n        dodgerblue: '#1e90ff',\n        firebrick: '#b22222',\n        floralwhite: '#fffaf0',\n        forestgreen: '#228b22',\n        fuchsia: '#ff00ff',\n        gainsboro: '#dcdcdc',\n        ghostwhite: '#f8f8ff',\n        gold: '#ffd700',\n        goldenrod: '#daa520',\n        gray: '#808080',\n        green: '#008000',\n        greenyellow: '#adff2f',\n        grey: '#808080',\n        honeydew: '#f0fff0',\n        hotpink: '#ff69b4',\n        indianred: '#cd5c5c',\n        indigo: '#4b0082',\n        ivory: '#fffff0',\n        khaki: '#f0e68c',\n        lavender: '#e6e6fa',\n        lavenderblush: '#fff0f5',\n        lawngreen: '#7cfc00',\n        lemonchiffon: '#fffacd',\n        lightblue: '#add8e6',\n        lightcoral: '#f08080',\n        lightcyan: '#e0ffff',\n        lightgoldenrodyellow: '#fafad2',\n        lightgray: '#d3d3d3',\n        lightgreen: '#90ee90',\n        lightgrey: '#d3d3d3',\n        lightpink: '#ffb6c1',\n        lightsalmon: '#ffa07a',\n        lightseagreen: '#20b2aa',\n        lightskyblue: '#87cefa',\n        lightslategray: '#778899',\n        lightslategrey: '#778899',\n        lightsteelblue: '#b0c4de',\n        lightyellow: '#ffffe0',\n        lime: '#00ff00',\n        limegreen: '#32cd32',\n        linen: '#faf0e6',\n        magenta: '#ff00ff',\n        maroon: '#800000',\n        mediumaquamarine: '#66cdaa',\n        mediumblue: '#0000cd',\n        mediumorchid: '#ba55d3',\n        mediumpurple: '#9370db',\n        mediumseagreen: '#3cb371',\n        mediumslateblue: '#7b68ee',\n        mediumspringgreen: '#00fa9a',\n        mediumturquoise: '#48d1cc',\n        mediumvioletred: '#c71585',\n        midnightblue: '#191970',\n        mintcream: '#f5fffa',\n        mistyrose: '#ffe4e1',\n        moccasin: '#ffe4b5',\n        navajowhite: '#ffdead',\n        navy: '#000080',\n        oldlace: '#fdf5e6',\n        olive: '#808000',\n        olivedrab: '#6b8e23',\n        orange: '#ffa500',\n        orangered: '#ff4500',\n        orchid: '#da70d6',\n        palegoldenrod: '#eee8aa',\n        palegreen: '#98fb98',\n        paleturquoise: '#afeeee',\n        palevioletred: '#db7093',\n        papayawhip: '#ffefd5',\n        peachpuff: '#ffdab9',\n        peru: '#cd853f',\n        pink: '#ffc0cb',\n        plum: '#dda0dd',\n        powderblue: '#b0e0e6',\n        purple: '#800080',\n        rebeccapurple: '#663399',\n        red: '#ff0000',\n        rosybrown: '#bc8f8f',\n        royalblue: '#4169e1',\n        saddlebrown: '#8b4513',\n        salmon: '#fa8072',\n        sandybrown: '#f4a460',\n        seagreen: '#2e8b57',\n        seashell: '#fff5ee',\n        sienna: '#a0522d',\n        silver: '#c0c0c0',\n        skyblue: '#87ceeb',\n        slateblue: '#6a5acd',\n        slategray: '#708090',\n        slategrey: '#708090',\n        snow: '#fffafa',\n        springgreen: '#00ff7f',\n        tan: '#d2b48c',\n        teal: '#008080',\n        thistle: '#d8bfd8',\n        transparent: '#00000000',\n        turquoise: '#40e0d0',\n        violet: '#ee82ee',\n        wheat: '#f5deb3',\n        white: '#ffffff',\n        whitesmoke: '#f5f5f5',\n        yellow: '#ffff00',\n        yellowgreen: '#9acd32'\n    },\n    /* API */\n    parse: (color) => {\n        color = color.toLowerCase();\n        const hex = Keyword.colors[color];\n        if (!hex)\n            return;\n        return Hex.parse(hex);\n    },\n    stringify: (channels) => {\n        const hex = Hex.stringify(channels);\n        for (const name in Keyword.colors) {\n            if (Keyword.colors[name] === hex)\n                return name;\n        }\n        return;\n    }\n};\n/* EXPORT */\nexport default Keyword;\n"], "mappings": "AAAA;AACA,OAAOA,GAAG,MAAM,UAAU;AAC1B;AACA,MAAMC,OAAO,GAAG;EACZ;EACAC,MAAM,EAAE;IACJC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,SAAS;IACvBC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBC,cAAc,EAAE,SAAS;IACzBC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,SAAS;IAChBC,cAAc,EAAE,SAAS;IACzBC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,cAAc,EAAE,SAAS;IACzBC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,SAAS;IACxBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,oBAAoB,EAAE,SAAS;IAC/BC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,SAAS;IACvBC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,gBAAgB,EAAE,SAAS;IAC3BC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,SAAS;IACvBC,cAAc,EAAE,SAAS;IACzBC,eAAe,EAAE,SAAS;IAC1BC,iBAAiB,EAAE,SAAS;IAC5BC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,SAAS;IACpBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,SAAS;IACxBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,SAAS;IACxBC,GAAG,EAAE,SAAS;IACdC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,SAAS;IACtBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,WAAW;IACxBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACjB,CAAC;EACD;EACAC,KAAK,EAAGC,KAAK,IAAK;IACdA,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC3B,MAAMC,GAAG,GAAGxJ,OAAO,CAACC,MAAM,CAACqJ,KAAK,CAAC;IACjC,IAAI,CAACE,GAAG,EACJ;IACJ,OAAOzJ,GAAG,CAACsJ,KAAK,CAACG,GAAG,CAAC;EACzB,CAAC;EACDC,SAAS,EAAGC,QAAQ,IAAK;IACrB,MAAMF,GAAG,GAAGzJ,GAAG,CAAC0J,SAAS,CAACC,QAAQ,CAAC;IACnC,KAAK,MAAMC,IAAI,IAAI3J,OAAO,CAACC,MAAM,EAAE;MAC/B,IAAID,OAAO,CAACC,MAAM,CAAC0J,IAAI,CAAC,KAAKH,GAAG,EAC5B,OAAOG,IAAI;IACnB;IACA;EACJ;AACJ,CAAC;AACD;AACA,eAAe3J,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}