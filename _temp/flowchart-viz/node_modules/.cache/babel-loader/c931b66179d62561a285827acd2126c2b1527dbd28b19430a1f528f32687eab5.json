{"ast": null, "code": "import { forEach, has, isArray, isFunction, last as peek, some } from \"lodash-es\";\nimport { Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Rule, Terminal } from \"@chevrotain/gast\";\nimport { Lexer } from \"../../../scan/lexer_public.js\";\nimport { augmentTokenTypes, hasShortKeyProperty } from \"../../../scan/tokens.js\";\nimport { createToken, createTokenInstance } from \"../../../scan/tokens_public.js\";\nimport { END_OF_FILE } from \"../parser.js\";\nimport { BITS_FOR_OCCURRENCE_IDX } from \"../../grammar/keys.js\";\nconst RECORDING_NULL_OBJECT = {\n  description: \"This Object indicates the Parser is during Recording Phase\"\n};\nObject.freeze(RECORDING_NULL_OBJECT);\nconst HANDLE_SEPARATOR = true;\nconst MAX_METHOD_IDX = Math.pow(2, BITS_FOR_OCCURRENCE_IDX) - 1;\nconst RFT = createToken({\n  name: \"RECORDING_PHASE_TOKEN\",\n  pattern: Lexer.NA\n});\naugmentTokenTypes([RFT]);\nconst RECORDING_PHASE_TOKEN = createTokenInstance(RFT, \"This IToken indicates the Parser is in Recording Phase\\n\\t\" + \"\" + \"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details\",\n// Using \"-1\" instead of NaN (as in EOF) because an actual number is less likely to\n// cause errors if the output of LA or CONSUME would be (incorrectly) used during the recording phase.\n-1, -1, -1, -1, -1, -1);\nObject.freeze(RECORDING_PHASE_TOKEN);\nconst RECORDING_PHASE_CSTNODE = {\n  name: \"This CSTNode indicates the Parser is in Recording Phase\\n\\t\" + \"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details\",\n  children: {}\n};\n/**\n * This trait handles the creation of the GAST structure for Chevrotain Grammars\n */\nexport class GastRecorder {\n  initGastRecorder(config) {\n    this.recordingProdStack = [];\n    this.RECORDING_PHASE = false;\n  }\n  enableRecording() {\n    this.RECORDING_PHASE = true;\n    this.TRACE_INIT(\"Enable Recording\", () => {\n      /**\n       * Warning Dark Voodoo Magic upcoming!\n       * We are \"replacing\" the public parsing DSL methods API\n       * With **new** alternative implementations on the Parser **instance**\n       *\n       * So far this is the only way I've found to avoid performance regressions during parsing time.\n       * - Approx 30% performance regression was measured on Chrome 75 Canary when attempting to replace the \"internal\"\n       *   implementations directly instead.\n       */\n      for (let i = 0; i < 10; i++) {\n        const idx = i > 0 ? i : \"\";\n        this[`CONSUME${idx}`] = function (arg1, arg2) {\n          return this.consumeInternalRecord(arg1, i, arg2);\n        };\n        this[`SUBRULE${idx}`] = function (arg1, arg2) {\n          return this.subruleInternalRecord(arg1, i, arg2);\n        };\n        this[`OPTION${idx}`] = function (arg1) {\n          return this.optionInternalRecord(arg1, i);\n        };\n        this[`OR${idx}`] = function (arg1) {\n          return this.orInternalRecord(arg1, i);\n        };\n        this[`MANY${idx}`] = function (arg1) {\n          this.manyInternalRecord(i, arg1);\n        };\n        this[`MANY_SEP${idx}`] = function (arg1) {\n          this.manySepFirstInternalRecord(i, arg1);\n        };\n        this[`AT_LEAST_ONE${idx}`] = function (arg1) {\n          this.atLeastOneInternalRecord(i, arg1);\n        };\n        this[`AT_LEAST_ONE_SEP${idx}`] = function (arg1) {\n          this.atLeastOneSepFirstInternalRecord(i, arg1);\n        };\n      }\n      // DSL methods with the idx(suffix) as an argument\n      this[`consume`] = function (idx, arg1, arg2) {\n        return this.consumeInternalRecord(arg1, idx, arg2);\n      };\n      this[`subrule`] = function (idx, arg1, arg2) {\n        return this.subruleInternalRecord(arg1, idx, arg2);\n      };\n      this[`option`] = function (idx, arg1) {\n        return this.optionInternalRecord(arg1, idx);\n      };\n      this[`or`] = function (idx, arg1) {\n        return this.orInternalRecord(arg1, idx);\n      };\n      this[`many`] = function (idx, arg1) {\n        this.manyInternalRecord(idx, arg1);\n      };\n      this[`atLeastOne`] = function (idx, arg1) {\n        this.atLeastOneInternalRecord(idx, arg1);\n      };\n      this.ACTION = this.ACTION_RECORD;\n      this.BACKTRACK = this.BACKTRACK_RECORD;\n      this.LA = this.LA_RECORD;\n    });\n  }\n  disableRecording() {\n    this.RECORDING_PHASE = false;\n    // By deleting these **instance** properties, any future invocation\n    // will be deferred to the original methods on the **prototype** object\n    // This seems to get rid of any incorrect optimizations that V8 may\n    // do during the recording phase.\n    this.TRACE_INIT(\"Deleting Recording methods\", () => {\n      const that = this;\n      for (let i = 0; i < 10; i++) {\n        const idx = i > 0 ? i : \"\";\n        delete that[`CONSUME${idx}`];\n        delete that[`SUBRULE${idx}`];\n        delete that[`OPTION${idx}`];\n        delete that[`OR${idx}`];\n        delete that[`MANY${idx}`];\n        delete that[`MANY_SEP${idx}`];\n        delete that[`AT_LEAST_ONE${idx}`];\n        delete that[`AT_LEAST_ONE_SEP${idx}`];\n      }\n      delete that[`consume`];\n      delete that[`subrule`];\n      delete that[`option`];\n      delete that[`or`];\n      delete that[`many`];\n      delete that[`atLeastOne`];\n      delete that.ACTION;\n      delete that.BACKTRACK;\n      delete that.LA;\n    });\n  }\n  //   Parser methods are called inside an ACTION?\n  //   Maybe try/catch/finally on ACTIONS while disabling the recorders state changes?\n  // @ts-expect-error -- noop place holder\n  ACTION_RECORD(impl) {\n    // NO-OP during recording\n  }\n  // Executing backtracking logic will break our recording logic assumptions\n  BACKTRACK_RECORD(grammarRule, args) {\n    return () => true;\n  }\n  // LA is part of the official API and may be used for custom lookahead logic\n  // by end users who may forget to wrap it in ACTION or inside a GATE\n  LA_RECORD(howMuch) {\n    // We cannot use the RECORD_PHASE_TOKEN here because someone may depend\n    // On LA return EOF at the end of the input so an infinite loop may occur.\n    return END_OF_FILE;\n  }\n  topLevelRuleRecord(name, def) {\n    try {\n      const newTopLevelRule = new Rule({\n        definition: [],\n        name: name\n      });\n      newTopLevelRule.name = name;\n      this.recordingProdStack.push(newTopLevelRule);\n      def.call(this);\n      this.recordingProdStack.pop();\n      return newTopLevelRule;\n    } catch (originalError) {\n      if (originalError.KNOWN_RECORDER_ERROR !== true) {\n        try {\n          originalError.message = originalError.message + '\\n\\t This error was thrown during the \"grammar recording phase\" For more info see:\\n\\t' + \"https://chevrotain.io/docs/guide/internals.html#grammar-recording\";\n        } catch (mutabilityError) {\n          // We may not be able to modify the original error object\n          throw originalError;\n        }\n      }\n      throw originalError;\n    }\n  }\n  // Implementation of parsing DSL\n  optionInternalRecord(actionORMethodDef, occurrence) {\n    return recordProd.call(this, Option, actionORMethodDef, occurrence);\n  }\n  atLeastOneInternalRecord(occurrence, actionORMethodDef) {\n    recordProd.call(this, RepetitionMandatory, actionORMethodDef, occurrence);\n  }\n  atLeastOneSepFirstInternalRecord(occurrence, options) {\n    recordProd.call(this, RepetitionMandatoryWithSeparator, options, occurrence, HANDLE_SEPARATOR);\n  }\n  manyInternalRecord(occurrence, actionORMethodDef) {\n    recordProd.call(this, Repetition, actionORMethodDef, occurrence);\n  }\n  manySepFirstInternalRecord(occurrence, options) {\n    recordProd.call(this, RepetitionWithSeparator, options, occurrence, HANDLE_SEPARATOR);\n  }\n  orInternalRecord(altsOrOpts, occurrence) {\n    return recordOrProd.call(this, altsOrOpts, occurrence);\n  }\n  subruleInternalRecord(ruleToCall, occurrence, options) {\n    assertMethodIdxIsValid(occurrence);\n    if (!ruleToCall || has(ruleToCall, \"ruleName\") === false) {\n      const error = new Error(`<SUBRULE${getIdxSuffix(occurrence)}> argument is invalid` + ` expecting a Parser method reference but got: <${JSON.stringify(ruleToCall)}>` + `\\n inside top level rule: <${this.recordingProdStack[0].name}>`);\n      error.KNOWN_RECORDER_ERROR = true;\n      throw error;\n    }\n    const prevProd = peek(this.recordingProdStack);\n    const ruleName = ruleToCall.ruleName;\n    const newNoneTerminal = new NonTerminal({\n      idx: occurrence,\n      nonTerminalName: ruleName,\n      label: options === null || options === void 0 ? void 0 : options.LABEL,\n      // The resolving of the `referencedRule` property will be done once all the Rule's GASTs have been created\n      referencedRule: undefined\n    });\n    prevProd.definition.push(newNoneTerminal);\n    return this.outputCst ? RECORDING_PHASE_CSTNODE : RECORDING_NULL_OBJECT;\n  }\n  consumeInternalRecord(tokType, occurrence, options) {\n    assertMethodIdxIsValid(occurrence);\n    if (!hasShortKeyProperty(tokType)) {\n      const error = new Error(`<CONSUME${getIdxSuffix(occurrence)}> argument is invalid` + ` expecting a TokenType reference but got: <${JSON.stringify(tokType)}>` + `\\n inside top level rule: <${this.recordingProdStack[0].name}>`);\n      error.KNOWN_RECORDER_ERROR = true;\n      throw error;\n    }\n    const prevProd = peek(this.recordingProdStack);\n    const newNoneTerminal = new Terminal({\n      idx: occurrence,\n      terminalType: tokType,\n      label: options === null || options === void 0 ? void 0 : options.LABEL\n    });\n    prevProd.definition.push(newNoneTerminal);\n    return RECORDING_PHASE_TOKEN;\n  }\n}\nfunction recordProd(prodConstructor, mainProdArg, occurrence, handleSep = false) {\n  assertMethodIdxIsValid(occurrence);\n  const prevProd = peek(this.recordingProdStack);\n  const grammarAction = isFunction(mainProdArg) ? mainProdArg : mainProdArg.DEF;\n  const newProd = new prodConstructor({\n    definition: [],\n    idx: occurrence\n  });\n  if (handleSep) {\n    newProd.separator = mainProdArg.SEP;\n  }\n  if (has(mainProdArg, \"MAX_LOOKAHEAD\")) {\n    newProd.maxLookahead = mainProdArg.MAX_LOOKAHEAD;\n  }\n  this.recordingProdStack.push(newProd);\n  grammarAction.call(this);\n  prevProd.definition.push(newProd);\n  this.recordingProdStack.pop();\n  return RECORDING_NULL_OBJECT;\n}\nfunction recordOrProd(mainProdArg, occurrence) {\n  assertMethodIdxIsValid(occurrence);\n  const prevProd = peek(this.recordingProdStack);\n  // Only an array of alternatives\n  const hasOptions = isArray(mainProdArg) === false;\n  const alts = hasOptions === false ? mainProdArg : mainProdArg.DEF;\n  const newOrProd = new Alternation({\n    definition: [],\n    idx: occurrence,\n    ignoreAmbiguities: hasOptions && mainProdArg.IGNORE_AMBIGUITIES === true\n  });\n  if (has(mainProdArg, \"MAX_LOOKAHEAD\")) {\n    newOrProd.maxLookahead = mainProdArg.MAX_LOOKAHEAD;\n  }\n  const hasPredicates = some(alts, currAlt => isFunction(currAlt.GATE));\n  newOrProd.hasPredicates = hasPredicates;\n  prevProd.definition.push(newOrProd);\n  forEach(alts, currAlt => {\n    const currAltFlat = new Alternative({\n      definition: []\n    });\n    newOrProd.definition.push(currAltFlat);\n    if (has(currAlt, \"IGNORE_AMBIGUITIES\")) {\n      currAltFlat.ignoreAmbiguities = currAlt.IGNORE_AMBIGUITIES; // assumes end user provides the correct config value/type\n    }\n    // **implicit** ignoreAmbiguities due to usage of gate\n    else if (has(currAlt, \"GATE\")) {\n      currAltFlat.ignoreAmbiguities = true;\n    }\n    this.recordingProdStack.push(currAltFlat);\n    currAlt.ALT.call(this);\n    this.recordingProdStack.pop();\n  });\n  return RECORDING_NULL_OBJECT;\n}\nfunction getIdxSuffix(idx) {\n  return idx === 0 ? \"\" : `${idx}`;\n}\nfunction assertMethodIdxIsValid(idx) {\n  if (idx < 0 || idx > MAX_METHOD_IDX) {\n    const error = new Error(\n    // The stack trace will contain all the needed details\n    `Invalid DSL Method idx value: <${idx}>\\n\\t` + `Idx value must be a none negative value smaller than ${MAX_METHOD_IDX + 1}`);\n    error.KNOWN_RECORDER_ERROR = true;\n    throw error;\n  }\n}", "map": {"version": 3, "names": ["for<PERSON>ach", "has", "isArray", "isFunction", "last", "peek", "some", "Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Rule", "Terminal", "<PERSON><PERSON>", "augmentTokenTypes", "hasShortKeyProperty", "createToken", "createTokenInstance", "END_OF_FILE", "BITS_FOR_OCCURRENCE_IDX", "RECORDING_NULL_OBJECT", "description", "Object", "freeze", "HANDLE_SEPARATOR", "MAX_METHOD_IDX", "Math", "pow", "RFT", "name", "pattern", "NA", "RECORDING_PHASE_TOKEN", "RECORDING_PHASE_CSTNODE", "children", "GastRecorder", "initGastRecorder", "config", "recordingProdStack", "RECORDING_PHASE", "enableRecording", "TRACE_INIT", "i", "idx", "arg1", "arg2", "consumeInternalRecord", "subruleInternalRecord", "optionInternalRecord", "orInternalRecord", "manyInternalRecord", "manySepFirstInternalRecord", "atLeastOneInternalRecord", "atLeastOneSepFirstInternalRecord", "ACTION", "ACTION_RECORD", "BACKTRACK", "BACKTRACK_RECORD", "LA", "LA_RECORD", "disableRecording", "that", "impl", "grammarRule", "args", "<PERSON><PERSON><PERSON>", "topLevelRuleRecord", "def", "newTopLevelRule", "definition", "push", "call", "pop", "originalError", "KNOWN_RECORDER_ERROR", "message", "mutabilityError", "actionORMethodDef", "occurrence", "recordProd", "options", "altsOrOpts", "recordOrProd", "ruleToCall", "assertMethodIdxIsValid", "error", "Error", "getIdxSuffix", "JSON", "stringify", "prevProd", "ruleName", "newNoneTerminal", "nonTerminalName", "label", "LABEL", "referencedRule", "undefined", "outputCst", "tokType", "terminalType", "prodConstructor", "mainProdArg", "handleSep", "grammarAction", "DEF", "newProd", "separator", "SEP", "max<PERSON><PERSON><PERSON><PERSON>", "MAX_LOOKAHEAD", "hasOptions", "alts", "newOrProd", "ignoreAmbiguities", "IGNORE_AMBIGUITIES", "hasPredicates", "currAlt", "GATE", "currAltFlat", "ALT"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/gast_recorder.ts"], "sourcesContent": ["import {\n  AtLeastOneSepMethodOpts,\n  ConsumeMethodOpts,\n  CstNode,\n  DSLMethodOpts,\n  DSLMethodOptsWithErr,\n  GrammarAction,\n  IOrAlt,\n  IParserConfig,\n  IProduction,\n  IToken,\n  ManySepMethodOpts,\n  OrMethodOpts,\n  SubruleMethodOpts,\n  TokenType,\n} from \"@chevrotain/types\";\nimport {\n  forEach,\n  has,\n  isArray,\n  isFunction,\n  last as peek,\n  some,\n} from \"lodash-es\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport {\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport { Lexer } from \"../../../scan/lexer_public.js\";\nimport {\n  augmentTokenTypes,\n  hasShortKeyProperty,\n} from \"../../../scan/tokens.js\";\nimport {\n  createToken,\n  createTokenInstance,\n} from \"../../../scan/tokens_public.js\";\nimport { END_OF_FILE } from \"../parser.js\";\nimport { BITS_FOR_OCCURRENCE_IDX } from \"../../grammar/keys.js\";\nimport { ParserMethodInternal } from \"../types.js\";\n\ntype ProdWithDef = IProduction & { definition?: IProduction[] };\nconst RECORDING_NULL_OBJECT = {\n  description: \"This Object indicates the Parser is during Recording Phase\",\n};\nObject.freeze(RECORDING_NULL_OBJECT);\n\nconst HANDLE_SEPARATOR = true;\nconst MAX_METHOD_IDX = Math.pow(2, BITS_FOR_OCCURRENCE_IDX) - 1;\n\nconst RFT = createToken({ name: \"RECORDING_PHASE_TOKEN\", pattern: Lexer.NA });\naugmentTokenTypes([RFT]);\nconst RECORDING_PHASE_TOKEN = createTokenInstance(\n  RFT,\n  \"This IToken indicates the Parser is in Recording Phase\\n\\t\" +\n    \"\" +\n    \"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details\",\n  // Using \"-1\" instead of NaN (as in EOF) because an actual number is less likely to\n  // cause errors if the output of LA or CONSUME would be (incorrectly) used during the recording phase.\n  -1,\n  -1,\n  -1,\n  -1,\n  -1,\n  -1,\n);\nObject.freeze(RECORDING_PHASE_TOKEN);\n\nconst RECORDING_PHASE_CSTNODE: CstNode = {\n  name:\n    \"This CSTNode indicates the Parser is in Recording Phase\\n\\t\" +\n    \"See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details\",\n  children: {},\n};\n\n/**\n * This trait handles the creation of the GAST structure for Chevrotain Grammars\n */\nexport class GastRecorder {\n  recordingProdStack: ProdWithDef[];\n  RECORDING_PHASE: boolean;\n\n  initGastRecorder(this: MixedInParser, config: IParserConfig): void {\n    this.recordingProdStack = [];\n    this.RECORDING_PHASE = false;\n  }\n\n  enableRecording(this: MixedInParser): void {\n    this.RECORDING_PHASE = true;\n\n    this.TRACE_INIT(\"Enable Recording\", () => {\n      /**\n       * Warning Dark Voodoo Magic upcoming!\n       * We are \"replacing\" the public parsing DSL methods API\n       * With **new** alternative implementations on the Parser **instance**\n       *\n       * So far this is the only way I've found to avoid performance regressions during parsing time.\n       * - Approx 30% performance regression was measured on Chrome 75 Canary when attempting to replace the \"internal\"\n       *   implementations directly instead.\n       */\n      for (let i = 0; i < 10; i++) {\n        const idx = i > 0 ? i : \"\";\n        this[`CONSUME${idx}` as \"CONSUME\"] = function (arg1, arg2) {\n          return this.consumeInternalRecord(arg1, i, arg2);\n        };\n        this[`SUBRULE${idx}` as \"SUBRULE\"] = function (arg1, arg2) {\n          return this.subruleInternalRecord(arg1, i, arg2) as any;\n        };\n        this[`OPTION${idx}` as \"OPTION\"] = function (arg1) {\n          return this.optionInternalRecord(arg1, i);\n        };\n        this[`OR${idx}` as \"OR\"] = function (arg1) {\n          return this.orInternalRecord(arg1, i);\n        };\n        this[`MANY${idx}` as \"MANY\"] = function (arg1) {\n          this.manyInternalRecord(i, arg1);\n        };\n        this[`MANY_SEP${idx}` as \"MANY_SEP\"] = function (arg1) {\n          this.manySepFirstInternalRecord(i, arg1);\n        };\n        this[`AT_LEAST_ONE${idx}` as \"AT_LEAST_ONE\"] = function (arg1) {\n          this.atLeastOneInternalRecord(i, arg1);\n        };\n        this[`AT_LEAST_ONE_SEP${idx}` as \"AT_LEAST_ONE_SEP\"] = function (arg1) {\n          this.atLeastOneSepFirstInternalRecord(i, arg1);\n        };\n      }\n\n      // DSL methods with the idx(suffix) as an argument\n      this[`consume`] = function (idx, arg1, arg2) {\n        return this.consumeInternalRecord(arg1, idx, arg2);\n      };\n      this[`subrule`] = function (idx, arg1, arg2) {\n        return this.subruleInternalRecord(arg1, idx, arg2) as any;\n      };\n      this[`option`] = function (idx, arg1) {\n        return this.optionInternalRecord(arg1, idx);\n      };\n      this[`or`] = function (idx, arg1) {\n        return this.orInternalRecord(arg1, idx);\n      };\n      this[`many`] = function (idx, arg1) {\n        this.manyInternalRecord(idx, arg1);\n      };\n      this[`atLeastOne`] = function (idx, arg1) {\n        this.atLeastOneInternalRecord(idx, arg1);\n      };\n\n      this.ACTION = this.ACTION_RECORD;\n      this.BACKTRACK = this.BACKTRACK_RECORD;\n      this.LA = this.LA_RECORD;\n    });\n  }\n\n  disableRecording(this: MixedInParser) {\n    this.RECORDING_PHASE = false;\n    // By deleting these **instance** properties, any future invocation\n    // will be deferred to the original methods on the **prototype** object\n    // This seems to get rid of any incorrect optimizations that V8 may\n    // do during the recording phase.\n    this.TRACE_INIT(\"Deleting Recording methods\", () => {\n      const that: any = this;\n\n      for (let i = 0; i < 10; i++) {\n        const idx = i > 0 ? i : \"\";\n        delete that[`CONSUME${idx}`];\n        delete that[`SUBRULE${idx}`];\n        delete that[`OPTION${idx}`];\n        delete that[`OR${idx}`];\n        delete that[`MANY${idx}`];\n        delete that[`MANY_SEP${idx}`];\n        delete that[`AT_LEAST_ONE${idx}`];\n        delete that[`AT_LEAST_ONE_SEP${idx}`];\n      }\n\n      delete that[`consume`];\n      delete that[`subrule`];\n      delete that[`option`];\n      delete that[`or`];\n      delete that[`many`];\n      delete that[`atLeastOne`];\n\n      delete that.ACTION;\n      delete that.BACKTRACK;\n      delete that.LA;\n    });\n  }\n\n  //   Parser methods are called inside an ACTION?\n  //   Maybe try/catch/finally on ACTIONS while disabling the recorders state changes?\n  // @ts-expect-error -- noop place holder\n  ACTION_RECORD<T>(this: MixedInParser, impl: () => T): T {\n    // NO-OP during recording\n  }\n\n  // Executing backtracking logic will break our recording logic assumptions\n  BACKTRACK_RECORD<T>(\n    grammarRule: (...args: any[]) => T,\n    args?: any[],\n  ): () => boolean {\n    return () => true;\n  }\n\n  // LA is part of the official API and may be used for custom lookahead logic\n  // by end users who may forget to wrap it in ACTION or inside a GATE\n  LA_RECORD(howMuch: number): IToken {\n    // We cannot use the RECORD_PHASE_TOKEN here because someone may depend\n    // On LA return EOF at the end of the input so an infinite loop may occur.\n    return END_OF_FILE;\n  }\n\n  topLevelRuleRecord(name: string, def: Function): Rule {\n    try {\n      const newTopLevelRule = new Rule({ definition: [], name: name });\n      newTopLevelRule.name = name;\n      this.recordingProdStack.push(newTopLevelRule);\n      def.call(this);\n      this.recordingProdStack.pop();\n      return newTopLevelRule;\n    } catch (originalError) {\n      if (originalError.KNOWN_RECORDER_ERROR !== true) {\n        try {\n          originalError.message =\n            originalError.message +\n            '\\n\\t This error was thrown during the \"grammar recording phase\" For more info see:\\n\\t' +\n            \"https://chevrotain.io/docs/guide/internals.html#grammar-recording\";\n        } catch (mutabilityError) {\n          // We may not be able to modify the original error object\n          throw originalError;\n        }\n      }\n      throw originalError;\n    }\n  }\n\n  // Implementation of parsing DSL\n  optionInternalRecord<OUT>(\n    this: MixedInParser,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n    occurrence: number,\n  ): OUT {\n    return recordProd.call(this, Option, actionORMethodDef, occurrence);\n  }\n\n  atLeastOneInternalRecord<OUT>(\n    this: MixedInParser,\n    occurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOptsWithErr<OUT>,\n  ): void {\n    recordProd.call(this, RepetitionMandatory, actionORMethodDef, occurrence);\n  }\n\n  atLeastOneSepFirstInternalRecord<OUT>(\n    this: MixedInParser,\n    occurrence: number,\n    options: AtLeastOneSepMethodOpts<OUT>,\n  ): void {\n    recordProd.call(\n      this,\n      RepetitionMandatoryWithSeparator,\n      options,\n      occurrence,\n      HANDLE_SEPARATOR,\n    );\n  }\n\n  manyInternalRecord<OUT>(\n    this: MixedInParser,\n    occurrence: number,\n    actionORMethodDef: GrammarAction<OUT> | DSLMethodOpts<OUT>,\n  ): void {\n    recordProd.call(this, Repetition, actionORMethodDef, occurrence);\n  }\n\n  manySepFirstInternalRecord<OUT>(\n    this: MixedInParser,\n    occurrence: number,\n    options: ManySepMethodOpts<OUT>,\n  ): void {\n    recordProd.call(\n      this,\n      RepetitionWithSeparator,\n      options,\n      occurrence,\n      HANDLE_SEPARATOR,\n    );\n  }\n\n  orInternalRecord<T>(\n    this: MixedInParser,\n    altsOrOpts: IOrAlt<any>[] | OrMethodOpts<unknown>,\n    occurrence: number,\n  ): T {\n    return recordOrProd.call(this, altsOrOpts, occurrence);\n  }\n\n  subruleInternalRecord<ARGS extends unknown[], R>(\n    this: MixedInParser,\n    ruleToCall: ParserMethodInternal<ARGS, R>,\n    occurrence: number,\n    options?: SubruleMethodOpts<ARGS>,\n  ): R | CstNode {\n    assertMethodIdxIsValid(occurrence);\n    if (!ruleToCall || has(ruleToCall, \"ruleName\") === false) {\n      const error: any = new Error(\n        `<SUBRULE${getIdxSuffix(occurrence)}> argument is invalid` +\n          ` expecting a Parser method reference but got: <${JSON.stringify(\n            ruleToCall,\n          )}>` +\n          `\\n inside top level rule: <${\n            (<Rule>this.recordingProdStack[0]).name\n          }>`,\n      );\n      error.KNOWN_RECORDER_ERROR = true;\n      throw error;\n    }\n\n    const prevProd: any = peek(this.recordingProdStack);\n    const ruleName = ruleToCall.ruleName;\n    const newNoneTerminal = new NonTerminal({\n      idx: occurrence,\n      nonTerminalName: ruleName,\n      label: options?.LABEL,\n      // The resolving of the `referencedRule` property will be done once all the Rule's GASTs have been created\n      referencedRule: undefined,\n    });\n    prevProd.definition.push(newNoneTerminal);\n\n    return this.outputCst\n      ? RECORDING_PHASE_CSTNODE\n      : <any>RECORDING_NULL_OBJECT;\n  }\n\n  consumeInternalRecord(\n    this: MixedInParser,\n    tokType: TokenType,\n    occurrence: number,\n    options?: ConsumeMethodOpts,\n  ): IToken {\n    assertMethodIdxIsValid(occurrence);\n    if (!hasShortKeyProperty(tokType)) {\n      const error: any = new Error(\n        `<CONSUME${getIdxSuffix(occurrence)}> argument is invalid` +\n          ` expecting a TokenType reference but got: <${JSON.stringify(\n            tokType,\n          )}>` +\n          `\\n inside top level rule: <${\n            (<Rule>this.recordingProdStack[0]).name\n          }>`,\n      );\n      error.KNOWN_RECORDER_ERROR = true;\n      throw error;\n    }\n    const prevProd: any = peek(this.recordingProdStack);\n    const newNoneTerminal = new Terminal({\n      idx: occurrence,\n      terminalType: tokType,\n      label: options?.LABEL,\n    });\n    prevProd.definition.push(newNoneTerminal);\n\n    return RECORDING_PHASE_TOKEN;\n  }\n}\n\nfunction recordProd(\n  prodConstructor: any,\n  mainProdArg: any,\n  occurrence: number,\n  handleSep: boolean = false,\n): any {\n  assertMethodIdxIsValid(occurrence);\n  const prevProd: any = peek(this.recordingProdStack);\n  const grammarAction = isFunction(mainProdArg) ? mainProdArg : mainProdArg.DEF;\n\n  const newProd = new prodConstructor({ definition: [], idx: occurrence });\n  if (handleSep) {\n    newProd.separator = mainProdArg.SEP;\n  }\n  if (has(mainProdArg, \"MAX_LOOKAHEAD\")) {\n    newProd.maxLookahead = mainProdArg.MAX_LOOKAHEAD;\n  }\n\n  this.recordingProdStack.push(newProd);\n  grammarAction.call(this);\n  prevProd.definition.push(newProd);\n  this.recordingProdStack.pop();\n\n  return RECORDING_NULL_OBJECT;\n}\n\nfunction recordOrProd(mainProdArg: any, occurrence: number): any {\n  assertMethodIdxIsValid(occurrence);\n  const prevProd: any = peek(this.recordingProdStack);\n  // Only an array of alternatives\n  const hasOptions = isArray(mainProdArg) === false;\n  const alts: IOrAlt<unknown>[] =\n    hasOptions === false ? mainProdArg : mainProdArg.DEF;\n\n  const newOrProd = new Alternation({\n    definition: [],\n    idx: occurrence,\n    ignoreAmbiguities: hasOptions && mainProdArg.IGNORE_AMBIGUITIES === true,\n  });\n  if (has(mainProdArg, \"MAX_LOOKAHEAD\")) {\n    newOrProd.maxLookahead = mainProdArg.MAX_LOOKAHEAD;\n  }\n\n  const hasPredicates = some(alts, (currAlt: any) => isFunction(currAlt.GATE));\n  newOrProd.hasPredicates = hasPredicates;\n\n  prevProd.definition.push(newOrProd);\n\n  forEach(alts, (currAlt) => {\n    const currAltFlat = new Alternative({ definition: [] });\n    newOrProd.definition.push(currAltFlat);\n    if (has(currAlt, \"IGNORE_AMBIGUITIES\")) {\n      currAltFlat.ignoreAmbiguities = currAlt.IGNORE_AMBIGUITIES as boolean; // assumes end user provides the correct config value/type\n    }\n    // **implicit** ignoreAmbiguities due to usage of gate\n    else if (has(currAlt, \"GATE\")) {\n      currAltFlat.ignoreAmbiguities = true;\n    }\n    this.recordingProdStack.push(currAltFlat);\n    currAlt.ALT.call(this);\n    this.recordingProdStack.pop();\n  });\n  return RECORDING_NULL_OBJECT;\n}\n\nfunction getIdxSuffix(idx: number): string {\n  return idx === 0 ? \"\" : `${idx}`;\n}\n\nfunction assertMethodIdxIsValid(idx: number): void {\n  if (idx < 0 || idx > MAX_METHOD_IDX) {\n    const error: any = new Error(\n      // The stack trace will contain all the needed details\n      `Invalid DSL Method idx value: <${idx}>\\n\\t` +\n        `Idx value must be a none negative value smaller than ${\n          MAX_METHOD_IDX + 1\n        }`,\n    );\n    error.KNOWN_RECORDER_ERROR = true;\n    throw error;\n  }\n}\n"], "mappings": "AAgBA,SACEA,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,UAAU,EACVC,IAAI,IAAIC,IAAI,EACZC,IAAI,QACC,WAAW;AAElB,SACEC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,IAAI,EACJC,QAAQ,QACH,kBAAkB;AACzB,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SACEC,iBAAiB,EACjBC,mBAAmB,QACd,yBAAyB;AAChC,SACEC,WAAW,EACXC,mBAAmB,QACd,gCAAgC;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,uBAAuB,QAAQ,uBAAuB;AAI/D,MAAMC,qBAAqB,GAAG;EAC5BC,WAAW,EAAE;CACd;AACDC,MAAM,CAACC,MAAM,CAACH,qBAAqB,CAAC;AAEpC,MAAMI,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,uBAAuB,CAAC,GAAG,CAAC;AAE/D,MAAMS,GAAG,GAAGZ,WAAW,CAAC;EAAEa,IAAI,EAAE,uBAAuB;EAAEC,OAAO,EAAEjB,KAAK,CAACkB;AAAE,CAAE,CAAC;AAC7EjB,iBAAiB,CAAC,CAACc,GAAG,CAAC,CAAC;AACxB,MAAMI,qBAAqB,GAAGf,mBAAmB,CAC/CW,GAAG,EACH,4DAA4D,GAC1D,EAAE,GACF,oFAAoF;AACtF;AACA;AACA,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,CACH;AACDN,MAAM,CAACC,MAAM,CAACS,qBAAqB,CAAC;AAEpC,MAAMC,uBAAuB,GAAY;EACvCJ,IAAI,EACF,6DAA6D,GAC7D,oFAAoF;EACtFK,QAAQ,EAAE;CACX;AAED;;;AAGA,OAAM,MAAOC,YAAY;EAIvBC,gBAAgBA,CAAsBC,MAAqB;IACzD,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;EAC9B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACD,eAAe,GAAG,IAAI;IAE3B,IAAI,CAACE,UAAU,CAAC,kBAAkB,EAAE,MAAK;MACvC;;;;;;;;;MASA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMC,GAAG,GAAGD,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE;QAC1B,IAAI,CAAC,UAAUC,GAAG,EAAe,CAAC,GAAG,UAAUC,IAAI,EAAEC,IAAI;UACvD,OAAO,IAAI,CAACC,qBAAqB,CAACF,IAAI,EAAEF,CAAC,EAAEG,IAAI,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,UAAUF,GAAG,EAAe,CAAC,GAAG,UAAUC,IAAI,EAAEC,IAAI;UACvD,OAAO,IAAI,CAACE,qBAAqB,CAACH,IAAI,EAAEF,CAAC,EAAEG,IAAI,CAAQ;QACzD,CAAC;QACD,IAAI,CAAC,SAASF,GAAG,EAAc,CAAC,GAAG,UAAUC,IAAI;UAC/C,OAAO,IAAI,CAACI,oBAAoB,CAACJ,IAAI,EAAEF,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,KAAKC,GAAG,EAAU,CAAC,GAAG,UAAUC,IAAI;UACvC,OAAO,IAAI,CAACK,gBAAgB,CAACL,IAAI,EAAEF,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,OAAOC,GAAG,EAAY,CAAC,GAAG,UAAUC,IAAI;UAC3C,IAAI,CAACM,kBAAkB,CAACR,CAAC,EAAEE,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,WAAWD,GAAG,EAAgB,CAAC,GAAG,UAAUC,IAAI;UACnD,IAAI,CAACO,0BAA0B,CAACT,CAAC,EAAEE,IAAI,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,eAAeD,GAAG,EAAoB,CAAC,GAAG,UAAUC,IAAI;UAC3D,IAAI,CAACQ,wBAAwB,CAACV,CAAC,EAAEE,IAAI,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,mBAAmBD,GAAG,EAAwB,CAAC,GAAG,UAAUC,IAAI;UACnE,IAAI,CAACS,gCAAgC,CAACX,CAAC,EAAEE,IAAI,CAAC;QAChD,CAAC;;MAGH;MACA,IAAI,CAAC,SAAS,CAAC,GAAG,UAAUD,GAAG,EAAEC,IAAI,EAAEC,IAAI;QACzC,OAAO,IAAI,CAACC,qBAAqB,CAACF,IAAI,EAAED,GAAG,EAAEE,IAAI,CAAC;MACpD,CAAC;MACD,IAAI,CAAC,SAAS,CAAC,GAAG,UAAUF,GAAG,EAAEC,IAAI,EAAEC,IAAI;QACzC,OAAO,IAAI,CAACE,qBAAqB,CAACH,IAAI,EAAED,GAAG,EAAEE,IAAI,CAAQ;MAC3D,CAAC;MACD,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAUF,GAAG,EAAEC,IAAI;QAClC,OAAO,IAAI,CAACI,oBAAoB,CAACJ,IAAI,EAAED,GAAG,CAAC;MAC7C,CAAC;MACD,IAAI,CAAC,IAAI,CAAC,GAAG,UAAUA,GAAG,EAAEC,IAAI;QAC9B,OAAO,IAAI,CAACK,gBAAgB,CAACL,IAAI,EAAED,GAAG,CAAC;MACzC,CAAC;MACD,IAAI,CAAC,MAAM,CAAC,GAAG,UAAUA,GAAG,EAAEC,IAAI;QAChC,IAAI,CAACM,kBAAkB,CAACP,GAAG,EAAEC,IAAI,CAAC;MACpC,CAAC;MACD,IAAI,CAAC,YAAY,CAAC,GAAG,UAAUD,GAAG,EAAEC,IAAI;QACtC,IAAI,CAACQ,wBAAwB,CAACT,GAAG,EAAEC,IAAI,CAAC;MAC1C,CAAC;MAED,IAAI,CAACU,MAAM,GAAG,IAAI,CAACC,aAAa;MAChC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,gBAAgB;MACtC,IAAI,CAACC,EAAE,GAAG,IAAI,CAACC,SAAS;IAC1B,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACrB,eAAe,GAAG,KAAK;IAC5B;IACA;IACA;IACA;IACA,IAAI,CAACE,UAAU,CAAC,4BAA4B,EAAE,MAAK;MACjD,MAAMoB,IAAI,GAAQ,IAAI;MAEtB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMC,GAAG,GAAGD,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE;QAC1B,OAAOmB,IAAI,CAAC,UAAUlB,GAAG,EAAE,CAAC;QAC5B,OAAOkB,IAAI,CAAC,UAAUlB,GAAG,EAAE,CAAC;QAC5B,OAAOkB,IAAI,CAAC,SAASlB,GAAG,EAAE,CAAC;QAC3B,OAAOkB,IAAI,CAAC,KAAKlB,GAAG,EAAE,CAAC;QACvB,OAAOkB,IAAI,CAAC,OAAOlB,GAAG,EAAE,CAAC;QACzB,OAAOkB,IAAI,CAAC,WAAWlB,GAAG,EAAE,CAAC;QAC7B,OAAOkB,IAAI,CAAC,eAAelB,GAAG,EAAE,CAAC;QACjC,OAAOkB,IAAI,CAAC,mBAAmBlB,GAAG,EAAE,CAAC;;MAGvC,OAAOkB,IAAI,CAAC,SAAS,CAAC;MACtB,OAAOA,IAAI,CAAC,SAAS,CAAC;MACtB,OAAOA,IAAI,CAAC,QAAQ,CAAC;MACrB,OAAOA,IAAI,CAAC,IAAI,CAAC;MACjB,OAAOA,IAAI,CAAC,MAAM,CAAC;MACnB,OAAOA,IAAI,CAAC,YAAY,CAAC;MAEzB,OAAOA,IAAI,CAACP,MAAM;MAClB,OAAOO,IAAI,CAACL,SAAS;MACrB,OAAOK,IAAI,CAACH,EAAE;IAChB,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EACAH,aAAaA,CAAyBO,IAAa;IACjD;EAAA;EAGF;EACAL,gBAAgBA,CACdM,WAAkC,EAClCC,IAAY;IAEZ,OAAO,MAAM,IAAI;EACnB;EAEA;EACA;EACAL,SAASA,CAACM,OAAe;IACvB;IACA;IACA,OAAO/C,WAAW;EACpB;EAEAgD,kBAAkBA,CAACrC,IAAY,EAAEsC,GAAa;IAC5C,IAAI;MACF,MAAMC,eAAe,GAAG,IAAIzD,IAAI,CAAC;QAAE0D,UAAU,EAAE,EAAE;QAAExC,IAAI,EAAEA;MAAI,CAAE,CAAC;MAChEuC,eAAe,CAACvC,IAAI,GAAGA,IAAI;MAC3B,IAAI,CAACS,kBAAkB,CAACgC,IAAI,CAACF,eAAe,CAAC;MAC7CD,GAAG,CAACI,IAAI,CAAC,IAAI,CAAC;MACd,IAAI,CAACjC,kBAAkB,CAACkC,GAAG,EAAE;MAC7B,OAAOJ,eAAe;KACvB,CAAC,OAAOK,aAAa,EAAE;MACtB,IAAIA,aAAa,CAACC,oBAAoB,KAAK,IAAI,EAAE;QAC/C,IAAI;UACFD,aAAa,CAACE,OAAO,GACnBF,aAAa,CAACE,OAAO,GACrB,wFAAwF,GACxF,mEAAmE;SACtE,CAAC,OAAOC,eAAe,EAAE;UACxB;UACA,MAAMH,aAAa;;;MAGvB,MAAMA,aAAa;;EAEvB;EAEA;EACAzB,oBAAoBA,CAElB6B,iBAA0D,EAC1DC,UAAkB;IAElB,OAAOC,UAAU,CAACR,IAAI,CAAC,IAAI,EAAEjE,MAAM,EAAEuE,iBAAiB,EAAEC,UAAU,CAAC;EACrE;EAEA1B,wBAAwBA,CAEtB0B,UAAkB,EAClBD,iBAAiE;IAEjEE,UAAU,CAACR,IAAI,CAAC,IAAI,EAAE/D,mBAAmB,EAAEqE,iBAAiB,EAAEC,UAAU,CAAC;EAC3E;EAEAzB,gCAAgCA,CAE9ByB,UAAkB,EAClBE,OAAqC;IAErCD,UAAU,CAACR,IAAI,CACb,IAAI,EACJ9D,gCAAgC,EAChCuE,OAAO,EACPF,UAAU,EACVtD,gBAAgB,CACjB;EACH;EAEA0B,kBAAkBA,CAEhB4B,UAAkB,EAClBD,iBAA0D;IAE1DE,UAAU,CAACR,IAAI,CAAC,IAAI,EAAEhE,UAAU,EAAEsE,iBAAiB,EAAEC,UAAU,CAAC;EAClE;EAEA3B,0BAA0BA,CAExB2B,UAAkB,EAClBE,OAA+B;IAE/BD,UAAU,CAACR,IAAI,CACb,IAAI,EACJ7D,uBAAuB,EACvBsE,OAAO,EACPF,UAAU,EACVtD,gBAAgB,CACjB;EACH;EAEAyB,gBAAgBA,CAEdgC,UAAiD,EACjDH,UAAkB;IAElB,OAAOI,YAAY,CAACX,IAAI,CAAC,IAAI,EAAEU,UAAU,EAAEH,UAAU,CAAC;EACxD;EAEA/B,qBAAqBA,CAEnBoC,UAAyC,EACzCL,UAAkB,EAClBE,OAAiC;IAEjCI,sBAAsB,CAACN,UAAU,CAAC;IAClC,IAAI,CAACK,UAAU,IAAItF,GAAG,CAACsF,UAAU,EAAE,UAAU,CAAC,KAAK,KAAK,EAAE;MACxD,MAAME,KAAK,GAAQ,IAAIC,KAAK,CAC1B,WAAWC,YAAY,CAACT,UAAU,CAAC,uBAAuB,GACxD,kDAAkDU,IAAI,CAACC,SAAS,CAC9DN,UAAU,CACX,GAAG,GACJ,8BACS,IAAI,CAAC7C,kBAAkB,CAAC,CAAC,CAAE,CAACT,IACrC,GAAG,CACN;MACDwD,KAAK,CAACX,oBAAoB,GAAG,IAAI;MACjC,MAAMW,KAAK;;IAGb,MAAMK,QAAQ,GAAQzF,IAAI,CAAC,IAAI,CAACqC,kBAAkB,CAAC;IACnD,MAAMqD,QAAQ,GAAGR,UAAU,CAACQ,QAAQ;IACpC,MAAMC,eAAe,GAAG,IAAIvF,WAAW,CAAC;MACtCsC,GAAG,EAAEmC,UAAU;MACfe,eAAe,EAAEF,QAAQ;MACzBG,KAAK,EAAEd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,KAAK;MACrB;MACAC,cAAc,EAAEC;KACjB,CAAC;IACFP,QAAQ,CAACrB,UAAU,CAACC,IAAI,CAACsB,eAAe,CAAC;IAEzC,OAAO,IAAI,CAACM,SAAS,GACjBjE,uBAAuB,GAClBb,qBAAqB;EAChC;EAEA0B,qBAAqBA,CAEnBqD,OAAkB,EAClBrB,UAAkB,EAClBE,OAA2B;IAE3BI,sBAAsB,CAACN,UAAU,CAAC;IAClC,IAAI,CAAC/D,mBAAmB,CAACoF,OAAO,CAAC,EAAE;MACjC,MAAMd,KAAK,GAAQ,IAAIC,KAAK,CAC1B,WAAWC,YAAY,CAACT,UAAU,CAAC,uBAAuB,GACxD,8CAA8CU,IAAI,CAACC,SAAS,CAC1DU,OAAO,CACR,GAAG,GACJ,8BACS,IAAI,CAAC7D,kBAAkB,CAAC,CAAC,CAAE,CAACT,IACrC,GAAG,CACN;MACDwD,KAAK,CAACX,oBAAoB,GAAG,IAAI;MACjC,MAAMW,KAAK;;IAEb,MAAMK,QAAQ,GAAQzF,IAAI,CAAC,IAAI,CAACqC,kBAAkB,CAAC;IACnD,MAAMsD,eAAe,GAAG,IAAIhF,QAAQ,CAAC;MACnC+B,GAAG,EAAEmC,UAAU;MACfsB,YAAY,EAAED,OAAO;MACrBL,KAAK,EAAEd,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe;KACjB,CAAC;IACFL,QAAQ,CAACrB,UAAU,CAACC,IAAI,CAACsB,eAAe,CAAC;IAEzC,OAAO5D,qBAAqB;EAC9B;;AAGF,SAAS+C,UAAUA,CACjBsB,eAAoB,EACpBC,WAAgB,EAChBxB,UAAkB,EAClByB,SAAA,GAAqB,KAAK;EAE1BnB,sBAAsB,CAACN,UAAU,CAAC;EAClC,MAAMY,QAAQ,GAAQzF,IAAI,CAAC,IAAI,CAACqC,kBAAkB,CAAC;EACnD,MAAMkE,aAAa,GAAGzG,UAAU,CAACuG,WAAW,CAAC,GAAGA,WAAW,GAAGA,WAAW,CAACG,GAAG;EAE7E,MAAMC,OAAO,GAAG,IAAIL,eAAe,CAAC;IAAEhC,UAAU,EAAE,EAAE;IAAE1B,GAAG,EAAEmC;EAAU,CAAE,CAAC;EACxE,IAAIyB,SAAS,EAAE;IACbG,OAAO,CAACC,SAAS,GAAGL,WAAW,CAACM,GAAG;;EAErC,IAAI/G,GAAG,CAACyG,WAAW,EAAE,eAAe,CAAC,EAAE;IACrCI,OAAO,CAACG,YAAY,GAAGP,WAAW,CAACQ,aAAa;;EAGlD,IAAI,CAACxE,kBAAkB,CAACgC,IAAI,CAACoC,OAAO,CAAC;EACrCF,aAAa,CAACjC,IAAI,CAAC,IAAI,CAAC;EACxBmB,QAAQ,CAACrB,UAAU,CAACC,IAAI,CAACoC,OAAO,CAAC;EACjC,IAAI,CAACpE,kBAAkB,CAACkC,GAAG,EAAE;EAE7B,OAAOpD,qBAAqB;AAC9B;AAEA,SAAS8D,YAAYA,CAACoB,WAAgB,EAAExB,UAAkB;EACxDM,sBAAsB,CAACN,UAAU,CAAC;EAClC,MAAMY,QAAQ,GAAQzF,IAAI,CAAC,IAAI,CAACqC,kBAAkB,CAAC;EACnD;EACA,MAAMyE,UAAU,GAAGjH,OAAO,CAACwG,WAAW,CAAC,KAAK,KAAK;EACjD,MAAMU,IAAI,GACRD,UAAU,KAAK,KAAK,GAAGT,WAAW,GAAGA,WAAW,CAACG,GAAG;EAEtD,MAAMQ,SAAS,GAAG,IAAI9G,WAAW,CAAC;IAChCkE,UAAU,EAAE,EAAE;IACd1B,GAAG,EAAEmC,UAAU;IACfoC,iBAAiB,EAAEH,UAAU,IAAIT,WAAW,CAACa,kBAAkB,KAAK;GACrE,CAAC;EACF,IAAItH,GAAG,CAACyG,WAAW,EAAE,eAAe,CAAC,EAAE;IACrCW,SAAS,CAACJ,YAAY,GAAGP,WAAW,CAACQ,aAAa;;EAGpD,MAAMM,aAAa,GAAGlH,IAAI,CAAC8G,IAAI,EAAGK,OAAY,IAAKtH,UAAU,CAACsH,OAAO,CAACC,IAAI,CAAC,CAAC;EAC5EL,SAAS,CAACG,aAAa,GAAGA,aAAa;EAEvC1B,QAAQ,CAACrB,UAAU,CAACC,IAAI,CAAC2C,SAAS,CAAC;EAEnCrH,OAAO,CAACoH,IAAI,EAAGK,OAAO,IAAI;IACxB,MAAME,WAAW,GAAG,IAAInH,WAAW,CAAC;MAAEiE,UAAU,EAAE;IAAE,CAAE,CAAC;IACvD4C,SAAS,CAAC5C,UAAU,CAACC,IAAI,CAACiD,WAAW,CAAC;IACtC,IAAI1H,GAAG,CAACwH,OAAO,EAAE,oBAAoB,CAAC,EAAE;MACtCE,WAAW,CAACL,iBAAiB,GAAGG,OAAO,CAACF,kBAA6B,CAAC,CAAC;;IAEzE;IAAA,KACK,IAAItH,GAAG,CAACwH,OAAO,EAAE,MAAM,CAAC,EAAE;MAC7BE,WAAW,CAACL,iBAAiB,GAAG,IAAI;;IAEtC,IAAI,CAAC5E,kBAAkB,CAACgC,IAAI,CAACiD,WAAW,CAAC;IACzCF,OAAO,CAACG,GAAG,CAACjD,IAAI,CAAC,IAAI,CAAC;IACtB,IAAI,CAACjC,kBAAkB,CAACkC,GAAG,EAAE;EAC/B,CAAC,CAAC;EACF,OAAOpD,qBAAqB;AAC9B;AAEA,SAASmE,YAAYA,CAAC5C,GAAW;EAC/B,OAAOA,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,GAAG,EAAE;AAClC;AAEA,SAASyC,sBAAsBA,CAACzC,GAAW;EACzC,IAAIA,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAGlB,cAAc,EAAE;IACnC,MAAM4D,KAAK,GAAQ,IAAIC,KAAK;IAC1B;IACA,kCAAkC3C,GAAG,OAAO,GAC1C,wDACElB,cAAc,GAAG,CACnB,EAAE,CACL;IACD4D,KAAK,CAACX,oBAAoB,GAAG,IAAI;IACjC,MAAMW,KAAK;;AAEf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}