{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { assertUnreachable } from '../index.js';\nimport { MultiMap } from '../utils/collections.js';\nimport { isOperationCancelled } from '../utils/promise-utils.js';\nimport { stream } from '../utils/stream.js';\n/**\n * Create DiagnosticData for a given diagnostic code. The result can be put into the `data` field of a DiagnosticInfo.\n */\nexport function diagnosticData(code) {\n  return {\n    code\n  };\n}\nexport var ValidationCategory;\n(function (ValidationCategory) {\n  ValidationCategory.all = ['fast', 'slow', 'built-in'];\n})(ValidationCategory || (ValidationCategory = {}));\n/**\n * Manages a set of `ValidationCheck`s to be applied when documents are validated.\n */\nexport class ValidationRegistry {\n  constructor(services) {\n    this.entries = new MultiMap();\n    this.entriesBefore = [];\n    this.entriesAfter = [];\n    this.reflection = services.shared.AstReflection;\n  }\n  /**\n   * Register a set of validation checks. Each value in the record can be either a single validation check (i.e. a function)\n   * or an array of validation checks.\n   *\n   * @param checksRecord Set of validation checks to register.\n   * @param category Optional category for the validation checks (defaults to `'fast'`).\n   * @param thisObj Optional object to be used as `this` when calling the validation check functions.\n   */\n  register(checksRecord, thisObj = this, category = 'fast') {\n    if (category === 'built-in') {\n      throw new Error(\"The 'built-in' category is reserved for lexer, parser, and linker errors.\");\n    }\n    for (const [type, ch] of Object.entries(checksRecord)) {\n      const callbacks = ch;\n      if (Array.isArray(callbacks)) {\n        for (const check of callbacks) {\n          const entry = {\n            check: this.wrapValidationException(check, thisObj),\n            category\n          };\n          this.addEntry(type, entry);\n        }\n      } else if (typeof callbacks === 'function') {\n        const entry = {\n          check: this.wrapValidationException(callbacks, thisObj),\n          category\n        };\n        this.addEntry(type, entry);\n      } else {\n        assertUnreachable(callbacks);\n      }\n    }\n  }\n  wrapValidationException(check, thisObj) {\n    return async (node, accept, cancelToken) => {\n      await this.handleException(() => check.call(thisObj, node, accept, cancelToken), 'An error occurred during validation', accept, node);\n    };\n  }\n  async handleException(functionality, messageContext, accept, node) {\n    try {\n      await functionality();\n    } catch (err) {\n      if (isOperationCancelled(err)) {\n        throw err;\n      }\n      console.error(`${messageContext}:`, err);\n      if (err instanceof Error && err.stack) {\n        console.error(err.stack);\n      }\n      const messageDetails = err instanceof Error ? err.message : String(err);\n      accept('error', `${messageContext}: ${messageDetails}`, {\n        node\n      });\n    }\n  }\n  addEntry(type, entry) {\n    if (type === 'AstNode') {\n      this.entries.add('AstNode', entry);\n      return;\n    }\n    for (const subtype of this.reflection.getAllSubTypes(type)) {\n      this.entries.add(subtype, entry);\n    }\n  }\n  getChecks(type, categories) {\n    let checks = stream(this.entries.get(type)).concat(this.entries.get('AstNode'));\n    if (categories) {\n      checks = checks.filter(entry => categories.includes(entry.category));\n    }\n    return checks.map(entry => entry.check);\n  }\n  /**\n   * Register logic which will be executed once before validating all the nodes of an AST/Langium document.\n   * This helps to prepare or initialize some information which are required or reusable for the following checks on the AstNodes.\n   *\n   * As an example, for validating unique fully-qualified names of nodes in the AST,\n   * here the map for mapping names to nodes could be established.\n   * During the usual checks on the nodes, they are put into this map with their name.\n   *\n   * Note that this approach makes validations stateful, which is relevant e.g. when cancelling the validation.\n   * Therefore it is recommended to clear stored information\n   * _before_ validating an AST to validate each AST unaffected from other ASTs\n   * AND _after_ validating the AST to free memory by information which are no longer used.\n   *\n   * @param checkBefore a set-up function which will be called once before actually validating an AST\n   * @param thisObj Optional object to be used as `this` when calling the validation check functions.\n   */\n  registerBeforeDocument(checkBefore, thisObj = this) {\n    this.entriesBefore.push(this.wrapPreparationException(checkBefore, 'An error occurred during set-up of the validation', thisObj));\n  }\n  /**\n   * Register logic which will be executed once after validating all the nodes of an AST/Langium document.\n   * This helps to finally evaluate information which are collected during the checks on the AstNodes.\n   *\n   * As an example, for validating unique fully-qualified names of nodes in the AST,\n   * here the map with all the collected nodes and their names is checked\n   * and validation hints are created for all nodes with the same name.\n   *\n   * Note that this approach makes validations stateful, which is relevant e.g. when cancelling the validation.\n   * Therefore it is recommended to clear stored information\n   * _before_ validating an AST to validate each AST unaffected from other ASTs\n   * AND _after_ validating the AST to free memory by information which are no longer used.\n   *\n   * @param checkBefore a set-up function which will be called once before actually validating an AST\n   * @param thisObj Optional object to be used as `this` when calling the validation check functions.\n   */\n  registerAfterDocument(checkAfter, thisObj = this) {\n    this.entriesAfter.push(this.wrapPreparationException(checkAfter, 'An error occurred during tear-down of the validation', thisObj));\n  }\n  wrapPreparationException(check, messageContext, thisObj) {\n    return async (rootNode, accept, categories, cancelToken) => {\n      await this.handleException(() => check.call(thisObj, rootNode, accept, categories, cancelToken), messageContext, accept, rootNode);\n    };\n  }\n  get checksBefore() {\n    return this.entriesBefore;\n  }\n  get checksAfter() {\n    return this.entriesAfter;\n  }\n}", "map": {"version": 3, "names": ["assertUnreachable", "MultiMap", "isOperationCancelled", "stream", "diagnosticData", "code", "ValidationCategory", "all", "ValidationRegistry", "constructor", "services", "entries", "entriesBefore", "entriesAfter", "reflection", "shared", "AstReflection", "register", "checksRecord", "thisObj", "category", "Error", "type", "ch", "Object", "callbacks", "Array", "isArray", "check", "entry", "wrapValidationException", "addEntry", "node", "accept", "cancelToken", "handleException", "call", "functionality", "messageContext", "err", "console", "error", "stack", "messageDetails", "message", "String", "add", "subtype", "getAllSubTypes", "getChecks", "categories", "checks", "get", "concat", "filter", "includes", "map", "registerBeforeDocument", "checkBefore", "push", "wrapPreparationException", "registerAfterDocument", "checkAfter", "rootNode", "checksBefore", "checksAfter"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/validation/validation-registry.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { CodeDescription, DiagnosticRelatedInformation, DiagnosticTag, integer, Range } from 'vscode-languageserver-types';\r\nimport { assertUnreachable } from '../index.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, AstReflection, Properties } from '../syntax-tree.js';\r\nimport type { CancellationToken } from '../utils/cancellation.js';\r\nimport { MultiMap } from '../utils/collections.js';\r\nimport type { MaybePromise } from '../utils/promise-utils.js';\r\nimport { isOperationCancelled } from '../utils/promise-utils.js';\r\nimport type { Stream } from '../utils/stream.js';\r\nimport { stream } from '../utils/stream.js';\r\nimport type { DocumentSegment } from '../workspace/documents.js';\r\n\r\nexport type DiagnosticInfo<N extends AstNode, P extends string = Properties<N>> = {\r\n    /** The AST node to which the diagnostic is attached. */\r\n    node: N;\r\n    /** If a property name is given, the diagnostic is restricted to the corresponding text region. */\r\n    property?: P;\r\n    /** If the value of a keyword is given, the diagnostic will appear at its corresponding text region */\r\n    keyword?: string;\r\n    /** In case of a multi-value property (array), an index can be given to select a specific element. */\r\n    index?: number;\r\n    /** If you want to create a diagnostic independent to any property, use the range property. */\r\n    range?: Range;\r\n    /** The diagnostic's code, which usually appear in the user interface. */\r\n    code?: integer | string;\r\n    /** An optional property to describe the error code. */\r\n    codeDescription?: CodeDescription;\r\n    /** Additional metadata about the diagnostic. */\r\n    tags?: DiagnosticTag[];\r\n    /** An array of related diagnostic information, e.g. when symbol-names within a scope collide all definitions can be marked via this property. */\r\n    relatedInformation?: DiagnosticRelatedInformation[];\r\n    /** A data entry field that is preserved between a `textDocument/publishDiagnostics` notification and `textDocument/codeAction` request. */\r\n    data?: unknown;\r\n}\r\n\r\n/**\r\n * Shape of information commonly used in the `data` field of diagnostics.\r\n */\r\nexport interface DiagnosticData {\r\n    /** Diagnostic code for identifying which code action to apply. This code is _not_ shown in the user interface. */\r\n    code: string\r\n    /** Specifies where to apply the code action in the form of a `DocumentSegment`. */\r\n    actionSegment?: DocumentSegment\r\n    /** Specifies where to apply the code action in the form of a `Range`. */\r\n    actionRange?: Range\r\n}\r\n\r\n/**\r\n * Create DiagnosticData for a given diagnostic code. The result can be put into the `data` field of a DiagnosticInfo.\r\n */\r\nexport function diagnosticData(code: string): DiagnosticData {\r\n    return { code };\r\n}\r\n\r\nexport type ValidationSeverity = 'error' | 'warning' | 'info' | 'hint';\r\n\r\nexport type ValidationAcceptor = <N extends AstNode>(severity: ValidationSeverity, message: string, info: DiagnosticInfo<N>) => void\r\n\r\nexport type ValidationCheck<T extends AstNode = AstNode> = (node: T, accept: ValidationAcceptor, cancelToken: CancellationToken) => MaybePromise<void>;\r\n\r\n/**\r\n * A utility type for describing functions which will be called once before or after all the AstNodes of an AST/Langium document are validated.\r\n *\r\n * The AST is represented by its root AstNode.\r\n *\r\n * The given validation acceptor helps to report some early or lately detected issues.\r\n *\r\n * The 'categories' indicate, which validation categories are executed for all the AstNodes.\r\n * This helps to tailor the preparations/tear-down logic to the actually executed checks on the nodes.\r\n *\r\n * It is recommended to support interrupts during long-running logic with 'interruptAndCheck(cancelToken)'.\r\n */\r\nexport type ValidationPreparation = (rootNode: AstNode, accept: ValidationAcceptor, categories: ValidationCategory[], cancelToken: CancellationToken) => MaybePromise<void>;\r\n\r\n/**\r\n * A utility type for associating non-primitive AST types to corresponding validation checks. For example:\r\n *\r\n * ```ts\r\n *   const checks: ValidationChecks<StatemachineAstType> = {\r\n *       State: validator.checkStateNameStartsWithCapital\r\n *    };\r\n * ```\r\n *\r\n * If an AST type does not extend AstNode, e.g. if it describes a union of string literals, that type's name must not occur as a key in objects of type `ValidationCheck<...>`.\r\n *\r\n * @param T a type definition mapping language specific type names (keys) to the corresponding types (values)\r\n */\r\nexport type ValidationChecks<T> = {\r\n    [K in keyof T]?: T[K] extends AstNode ? ValidationCheck<T[K]> | Array<ValidationCheck<T[K]>> : never\r\n} & {\r\n    AstNode?: ValidationCheck<AstNode> | Array<ValidationCheck<AstNode>>;\r\n}\r\n\r\n/**\r\n * `fast` checks can be executed after every document change (i.e. as the user is typing). If a check\r\n * is too slow it can delay the response to document changes, yielding bad user experience. By marking\r\n * it as `slow`, it will be skipped for normal as-you-type validation. Then it's up to you when to\r\n * schedule these long-running checks: after the fast checks are done, or after saving a document,\r\n * or with an explicit command, etc.\r\n *\r\n * `built-in` checks are errors produced by the lexer, the parser, or the linker. They cannot be used\r\n * for custom validation checks.\r\n */\r\nexport type ValidationCategory = 'fast' | 'slow' | 'built-in'\r\n\r\nexport namespace ValidationCategory {\r\n    export const all: readonly ValidationCategory[] = ['fast', 'slow', 'built-in'];\r\n}\r\n\r\ntype ValidationCheckEntry = {\r\n    check: ValidationCheck\r\n    category: ValidationCategory\r\n}\r\n\r\n/**\r\n * Manages a set of `ValidationCheck`s to be applied when documents are validated.\r\n */\r\nexport class ValidationRegistry {\r\n    private readonly entries = new MultiMap<string, ValidationCheckEntry>();\r\n    private readonly reflection: AstReflection;\r\n\r\n    private entriesBefore: ValidationPreparation[] = [];\r\n    private entriesAfter: ValidationPreparation[] = [];\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.reflection = services.shared.AstReflection;\r\n    }\r\n\r\n    /**\r\n     * Register a set of validation checks. Each value in the record can be either a single validation check (i.e. a function)\r\n     * or an array of validation checks.\r\n     *\r\n     * @param checksRecord Set of validation checks to register.\r\n     * @param category Optional category for the validation checks (defaults to `'fast'`).\r\n     * @param thisObj Optional object to be used as `this` when calling the validation check functions.\r\n     */\r\n    register<T>(checksRecord: ValidationChecks<T>, thisObj: ThisParameterType<unknown> = this, category: ValidationCategory = 'fast'): void {\r\n        if (category === 'built-in') {\r\n            throw new Error(\"The 'built-in' category is reserved for lexer, parser, and linker errors.\");\r\n        }\r\n        for (const [type, ch] of Object.entries(checksRecord)) {\r\n            const callbacks = ch as ValidationCheck | ValidationCheck[];\r\n            if (Array.isArray(callbacks)) {\r\n                for (const check of callbacks) {\r\n                    const entry: ValidationCheckEntry = {\r\n                        check: this.wrapValidationException(check, thisObj),\r\n                        category\r\n                    };\r\n                    this.addEntry(type, entry);\r\n                }\r\n            } else if (typeof callbacks === 'function') {\r\n                const entry: ValidationCheckEntry = {\r\n                    check: this.wrapValidationException(callbacks, thisObj),\r\n                    category\r\n                };\r\n                this.addEntry(type, entry);\r\n            } else {\r\n                assertUnreachable(callbacks);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected wrapValidationException(check: ValidationCheck, thisObj: unknown): ValidationCheck {\r\n        return async (node, accept, cancelToken) => {\r\n            await this.handleException(() => check.call(thisObj, node, accept, cancelToken), 'An error occurred during validation', accept, node);\r\n        };\r\n    }\r\n\r\n    protected async handleException(functionality: () => MaybePromise<void>, messageContext: string, accept: ValidationAcceptor, node: AstNode): Promise<void> {\r\n        try {\r\n            await functionality();\r\n        } catch (err) {\r\n            if (isOperationCancelled(err)) {\r\n                throw err;\r\n            }\r\n            console.error(`${messageContext}:`, err);\r\n            if (err instanceof Error && err.stack) {\r\n                console.error(err.stack);\r\n            }\r\n            const messageDetails = err instanceof Error ? err.message : String(err);\r\n            accept('error', `${messageContext}: ${messageDetails}`, { node });\r\n        }\r\n    }\r\n\r\n    protected addEntry(type: string, entry: ValidationCheckEntry): void {\r\n        if (type === 'AstNode') {\r\n            this.entries.add('AstNode', entry);\r\n            return;\r\n        }\r\n        for (const subtype of this.reflection.getAllSubTypes(type)) {\r\n            this.entries.add(subtype, entry);\r\n        }\r\n    }\r\n\r\n    getChecks(type: string, categories?: ValidationCategory[]): Stream<ValidationCheck> {\r\n        let checks = stream(this.entries.get(type))\r\n            .concat(this.entries.get('AstNode'));\r\n        if (categories) {\r\n            checks = checks.filter(entry => categories.includes(entry.category));\r\n        }\r\n        return checks.map(entry => entry.check);\r\n    }\r\n\r\n    /**\r\n     * Register logic which will be executed once before validating all the nodes of an AST/Langium document.\r\n     * This helps to prepare or initialize some information which are required or reusable for the following checks on the AstNodes.\r\n     *\r\n     * As an example, for validating unique fully-qualified names of nodes in the AST,\r\n     * here the map for mapping names to nodes could be established.\r\n     * During the usual checks on the nodes, they are put into this map with their name.\r\n     *\r\n     * Note that this approach makes validations stateful, which is relevant e.g. when cancelling the validation.\r\n     * Therefore it is recommended to clear stored information\r\n     * _before_ validating an AST to validate each AST unaffected from other ASTs\r\n     * AND _after_ validating the AST to free memory by information which are no longer used.\r\n     *\r\n     * @param checkBefore a set-up function which will be called once before actually validating an AST\r\n     * @param thisObj Optional object to be used as `this` when calling the validation check functions.\r\n     */\r\n    registerBeforeDocument(checkBefore: ValidationPreparation, thisObj: ThisParameterType<unknown> = this): void {\r\n        this.entriesBefore.push(this.wrapPreparationException(checkBefore, 'An error occurred during set-up of the validation', thisObj));\r\n    }\r\n\r\n    /**\r\n     * Register logic which will be executed once after validating all the nodes of an AST/Langium document.\r\n     * This helps to finally evaluate information which are collected during the checks on the AstNodes.\r\n     *\r\n     * As an example, for validating unique fully-qualified names of nodes in the AST,\r\n     * here the map with all the collected nodes and their names is checked\r\n     * and validation hints are created for all nodes with the same name.\r\n     *\r\n     * Note that this approach makes validations stateful, which is relevant e.g. when cancelling the validation.\r\n     * Therefore it is recommended to clear stored information\r\n     * _before_ validating an AST to validate each AST unaffected from other ASTs\r\n     * AND _after_ validating the AST to free memory by information which are no longer used.\r\n     *\r\n     * @param checkBefore a set-up function which will be called once before actually validating an AST\r\n     * @param thisObj Optional object to be used as `this` when calling the validation check functions.\r\n     */\r\n    registerAfterDocument(checkAfter: ValidationPreparation, thisObj: ThisParameterType<unknown> = this): void {\r\n        this.entriesAfter.push(this.wrapPreparationException(checkAfter, 'An error occurred during tear-down of the validation', thisObj));\r\n    }\r\n\r\n    protected wrapPreparationException(check: ValidationPreparation, messageContext: string, thisObj: unknown): ValidationPreparation {\r\n        return async (rootNode, accept, categories, cancelToken) => {\r\n            await this.handleException(() => check.call(thisObj, rootNode, accept, categories, cancelToken), messageContext, accept, rootNode);\r\n        };\r\n    }\r\n\r\n    get checksBefore(): ValidationPreparation[] {\r\n        return this.entriesBefore;\r\n    }\r\n\r\n    get checksAfter(): ValidationPreparation[] {\r\n        return this.entriesAfter;\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAOA,SAASA,iBAAiB,QAAQ,aAAa;AAI/C,SAASC,QAAQ,QAAQ,yBAAyB;AAElD,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,SAASC,MAAM,QAAQ,oBAAoB;AAsC3C;;;AAGA,OAAM,SAAUC,cAAcA,CAACC,IAAY;EACvC,OAAO;IAAEA;EAAI,CAAE;AACnB;AAqDA,OAAM,IAAWC,kBAAkB;AAAnC,WAAiBA,kBAAkB;EAClBA,kBAAA,CAAAC,GAAG,GAAkC,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC;AAClF,CAAC,EAFgBD,kBAAkB,KAAlBA,kBAAkB;AASnC;;;AAGA,OAAM,MAAOE,kBAAkB;EAO3BC,YAAYC,QAA6B;IANxB,KAAAC,OAAO,GAAG,IAAIV,QAAQ,EAAgC;IAG/D,KAAAW,aAAa,GAA4B,EAAE;IAC3C,KAAAC,YAAY,GAA4B,EAAE;IAG9C,IAAI,CAACC,UAAU,GAAGJ,QAAQ,CAACK,MAAM,CAACC,aAAa;EACnD;EAEA;;;;;;;;EAQAC,QAAQA,CAAIC,YAAiC,EAAEC,OAAA,GAAsC,IAAI,EAAEC,QAAA,GAA+B,MAAM;IAC5H,IAAIA,QAAQ,KAAK,UAAU,EAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,2EAA2E,CAAC;IAChG;IACA,KAAK,MAAM,CAACC,IAAI,EAAEC,EAAE,CAAC,IAAIC,MAAM,CAACb,OAAO,CAACO,YAAY,CAAC,EAAE;MACnD,MAAMO,SAAS,GAAGF,EAAyC;MAC3D,IAAIG,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;QAC1B,KAAK,MAAMG,KAAK,IAAIH,SAAS,EAAE;UAC3B,MAAMI,KAAK,GAAyB;YAChCD,KAAK,EAAE,IAAI,CAACE,uBAAuB,CAACF,KAAK,EAAET,OAAO,CAAC;YACnDC;WACH;UACD,IAAI,CAACW,QAAQ,CAACT,IAAI,EAAEO,KAAK,CAAC;QAC9B;MACJ,CAAC,MAAM,IAAI,OAAOJ,SAAS,KAAK,UAAU,EAAE;QACxC,MAAMI,KAAK,GAAyB;UAChCD,KAAK,EAAE,IAAI,CAACE,uBAAuB,CAACL,SAAS,EAAEN,OAAO,CAAC;UACvDC;SACH;QACD,IAAI,CAACW,QAAQ,CAACT,IAAI,EAAEO,KAAK,CAAC;MAC9B,CAAC,MAAM;QACH7B,iBAAiB,CAACyB,SAAS,CAAC;MAChC;IACJ;EACJ;EAEUK,uBAAuBA,CAACF,KAAsB,EAAET,OAAgB;IACtE,OAAO,OAAOa,IAAI,EAAEC,MAAM,EAAEC,WAAW,KAAI;MACvC,MAAM,IAAI,CAACC,eAAe,CAAC,MAAMP,KAAK,CAACQ,IAAI,CAACjB,OAAO,EAAEa,IAAI,EAAEC,MAAM,EAAEC,WAAW,CAAC,EAAE,qCAAqC,EAAED,MAAM,EAAED,IAAI,CAAC;IACzI,CAAC;EACL;EAEU,MAAMG,eAAeA,CAACE,aAAuC,EAAEC,cAAsB,EAAEL,MAA0B,EAAED,IAAa;IACtI,IAAI;MACA,MAAMK,aAAa,EAAE;IACzB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACV,IAAIrC,oBAAoB,CAACqC,GAAG,CAAC,EAAE;QAC3B,MAAMA,GAAG;MACb;MACAC,OAAO,CAACC,KAAK,CAAC,GAAGH,cAAc,GAAG,EAAEC,GAAG,CAAC;MACxC,IAAIA,GAAG,YAAYlB,KAAK,IAAIkB,GAAG,CAACG,KAAK,EAAE;QACnCF,OAAO,CAACC,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;MAC5B;MACA,MAAMC,cAAc,GAAGJ,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACK,OAAO,GAAGC,MAAM,CAACN,GAAG,CAAC;MACvEN,MAAM,CAAC,OAAO,EAAE,GAAGK,cAAc,KAAKK,cAAc,EAAE,EAAE;QAAEX;MAAI,CAAE,CAAC;IACrE;EACJ;EAEUD,QAAQA,CAACT,IAAY,EAAEO,KAA2B;IACxD,IAAIP,IAAI,KAAK,SAAS,EAAE;MACpB,IAAI,CAACX,OAAO,CAACmC,GAAG,CAAC,SAAS,EAAEjB,KAAK,CAAC;MAClC;IACJ;IACA,KAAK,MAAMkB,OAAO,IAAI,IAAI,CAACjC,UAAU,CAACkC,cAAc,CAAC1B,IAAI,CAAC,EAAE;MACxD,IAAI,CAACX,OAAO,CAACmC,GAAG,CAACC,OAAO,EAAElB,KAAK,CAAC;IACpC;EACJ;EAEAoB,SAASA,CAAC3B,IAAY,EAAE4B,UAAiC;IACrD,IAAIC,MAAM,GAAGhD,MAAM,CAAC,IAAI,CAACQ,OAAO,CAACyC,GAAG,CAAC9B,IAAI,CAAC,CAAC,CACtC+B,MAAM,CAAC,IAAI,CAAC1C,OAAO,CAACyC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,IAAIF,UAAU,EAAE;MACZC,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACzB,KAAK,IAAIqB,UAAU,CAACK,QAAQ,CAAC1B,KAAK,CAACT,QAAQ,CAAC,CAAC;IACxE;IACA,OAAO+B,MAAM,CAACK,GAAG,CAAC3B,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAC3C;EAEA;;;;;;;;;;;;;;;;EAgBA6B,sBAAsBA,CAACC,WAAkC,EAAEvC,OAAA,GAAsC,IAAI;IACjG,IAAI,CAACP,aAAa,CAAC+C,IAAI,CAAC,IAAI,CAACC,wBAAwB,CAACF,WAAW,EAAE,mDAAmD,EAAEvC,OAAO,CAAC,CAAC;EACrI;EAEA;;;;;;;;;;;;;;;;EAgBA0C,qBAAqBA,CAACC,UAAiC,EAAE3C,OAAA,GAAsC,IAAI;IAC/F,IAAI,CAACN,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAACC,wBAAwB,CAACE,UAAU,EAAE,sDAAsD,EAAE3C,OAAO,CAAC,CAAC;EACtI;EAEUyC,wBAAwBA,CAAChC,KAA4B,EAAEU,cAAsB,EAAEnB,OAAgB;IACrG,OAAO,OAAO4C,QAAQ,EAAE9B,MAAM,EAAEiB,UAAU,EAAEhB,WAAW,KAAI;MACvD,MAAM,IAAI,CAACC,eAAe,CAAC,MAAMP,KAAK,CAACQ,IAAI,CAACjB,OAAO,EAAE4C,QAAQ,EAAE9B,MAAM,EAAEiB,UAAU,EAAEhB,WAAW,CAAC,EAAEI,cAAc,EAAEL,MAAM,EAAE8B,QAAQ,CAAC;IACtI,CAAC;EACL;EAEA,IAAIC,YAAYA,CAAA;IACZ,OAAO,IAAI,CAACpD,aAAa;EAC7B;EAEA,IAAIqD,WAAWA,CAAA;IACX,OAAO,IAAI,CAACpD,YAAY;EAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}