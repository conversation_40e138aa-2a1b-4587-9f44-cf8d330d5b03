{"ast": null, "code": "import { pi, tau } from \"../math.js\";\nvar ka = 0.89081309152928522810,\n  kr = Math.sin(pi / 10) / Math.sin(7 * pi / 10),\n  kx = Math.sin(tau / 10) * kr,\n  ky = -Math.cos(tau / 10) * kr;\nexport default {\n  draw: function (context, size) {\n    var r = Math.sqrt(size * ka),\n      x = kx * r,\n      y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (var i = 1; i < 5; ++i) {\n      var a = tau * i / 5,\n        c = Math.cos(a),\n        s = Math.sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["pi", "tau", "ka", "kr", "Math", "sin", "kx", "ky", "cos", "draw", "context", "size", "r", "sqrt", "x", "y", "moveTo", "lineTo", "i", "a", "c", "s", "closePath"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/symbol/star.js"], "sourcesContent": ["import {pi, tau} from \"../math.js\";\n\nvar ka = 0.89081309152928522810,\n    kr = Math.sin(pi / 10) / Math.sin(7 * pi / 10),\n    kx = Math.sin(tau / 10) * kr,\n    ky = -Math.cos(tau / 10) * kr;\n\nexport default {\n  draw: function(context, size) {\n    var r = Math.sqrt(size * ka),\n        x = kx * r,\n        y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (var i = 1; i < 5; ++i) {\n      var a = tau * i / 5,\n          c = Math.cos(a),\n          s = Math.sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,EAAE,EAAEC,GAAG,QAAO,YAAY;AAElC,IAAIC,EAAE,GAAG,sBAAsB;EAC3BC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,EAAE,GAAG,EAAE,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,EAAE,GAAG,EAAE,CAAC;EAC9CM,EAAE,GAAGF,IAAI,CAACC,GAAG,CAACJ,GAAG,GAAG,EAAE,CAAC,GAAGE,EAAE;EAC5BI,EAAE,GAAG,CAACH,IAAI,CAACI,GAAG,CAACP,GAAG,GAAG,EAAE,CAAC,GAAGE,EAAE;AAEjC,eAAe;EACbM,IAAI,EAAE,SAAAA,CAASC,OAAO,EAAEC,IAAI,EAAE;IAC5B,IAAIC,CAAC,GAAGR,IAAI,CAACS,IAAI,CAACF,IAAI,GAAGT,EAAE,CAAC;MACxBY,CAAC,GAAGR,EAAE,GAAGM,CAAC;MACVG,CAAC,GAAGR,EAAE,GAAGK,CAAC;IACdF,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAACJ,CAAC,CAAC;IACrBF,OAAO,CAACO,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;IACpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAIC,CAAC,GAAGlB,GAAG,GAAGiB,CAAC,GAAG,CAAC;QACfE,CAAC,GAAGhB,IAAI,CAACI,GAAG,CAACW,CAAC,CAAC;QACfE,CAAC,GAAGjB,IAAI,CAACC,GAAG,CAACc,CAAC,CAAC;MACnBT,OAAO,CAACO,MAAM,CAACI,CAAC,GAAGT,CAAC,EAAE,CAACQ,CAAC,GAAGR,CAAC,CAAC;MAC7BF,OAAO,CAACO,MAAM,CAACG,CAAC,GAAGN,CAAC,GAAGO,CAAC,GAAGN,CAAC,EAAEM,CAAC,GAAGP,CAAC,GAAGM,CAAC,GAAGL,CAAC,CAAC;IAC9C;IACAL,OAAO,CAACY,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}