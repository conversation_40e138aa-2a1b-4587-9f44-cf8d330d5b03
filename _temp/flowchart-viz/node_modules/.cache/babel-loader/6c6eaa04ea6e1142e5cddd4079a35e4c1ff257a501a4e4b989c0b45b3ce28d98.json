{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport * from './grammar-config.js';\nexport * from './language-meta-data.js';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/languages/index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport * from './grammar-config.js';\r\nexport * from './language-meta-data.js';\r\n"], "mappings": "AAAA;;;;;AAMA,cAAc,qBAAqB;AACnC,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}