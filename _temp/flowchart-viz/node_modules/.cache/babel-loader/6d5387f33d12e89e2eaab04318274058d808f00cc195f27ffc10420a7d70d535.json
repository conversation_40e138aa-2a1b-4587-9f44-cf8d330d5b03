{"ast": null, "code": "import * as _ from 'lodash-es';\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i\n    };\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n  return doResolveConflicts(sourceSet);\n}\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (_.isUndefined(uEntry.barycenter) || _.isUndefined(vEntry.barycenter) || uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n  return _.map(_.filter(entries, function (entry) {\n    return !entry.merged;\n  }), function (entry) {\n    return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n  });\n}\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}", "map": {"version": 3, "names": ["_", "resolveConflicts", "entries", "cg", "mappedEntries", "for<PERSON>ach", "entry", "i", "tmp", "v", "indegree", "in", "out", "vs", "isUndefined", "barycenter", "weight", "edges", "e", "entryV", "entryW", "w", "push", "sourceSet", "filter", "doResolveConflicts", "handleIn", "vEntry", "uEntry", "merged", "mergeEntries", "handleOut", "wEntry", "length", "pop", "reverse", "map", "pick", "target", "source", "sum", "concat", "Math", "min"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAE9B,SAASC,gBAAgB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgBA,CAACC,OAAO,EAAEC,EAAE,EAAE;EACrC,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtBJ,CAAC,CAACK,OAAO,CAACH,OAAO,EAAE,UAAUI,KAAK,EAAEC,CAAC,EAAE;IACrC,IAAIC,GAAG,GAAIJ,aAAa,CAACE,KAAK,CAACG,CAAC,CAAC,GAAG;MAClCC,QAAQ,EAAE,CAAC;MACXC,EAAE,EAAE,EAAE;MACNC,GAAG,EAAE,EAAE;MACPC,EAAE,EAAE,CAACP,KAAK,CAACG,CAAC,CAAC;MACbF,CAAC,EAAEA;IACL,CAAE;IACF,IAAI,CAACP,CAAC,CAACc,WAAW,CAACR,KAAK,CAACS,UAAU,CAAC,EAAE;MACpC;MACAP,GAAG,CAACO,UAAU,GAAGT,KAAK,CAACS,UAAU;MACjC;MACAP,GAAG,CAACQ,MAAM,GAAGV,KAAK,CAACU,MAAM;IAC3B;EACF,CAAC,CAAC;EAEFhB,CAAC,CAACK,OAAO,CAACF,EAAE,CAACc,KAAK,CAAC,CAAC,EAAE,UAAUC,CAAC,EAAE;IACjC,IAAIC,MAAM,GAAGf,aAAa,CAACc,CAAC,CAACT,CAAC,CAAC;IAC/B,IAAIW,MAAM,GAAGhB,aAAa,CAACc,CAAC,CAACG,CAAC,CAAC;IAC/B,IAAI,CAACrB,CAAC,CAACc,WAAW,CAACK,MAAM,CAAC,IAAI,CAACnB,CAAC,CAACc,WAAW,CAACM,MAAM,CAAC,EAAE;MACpDA,MAAM,CAACV,QAAQ,EAAE;MACjBS,MAAM,CAACP,GAAG,CAACU,IAAI,CAAClB,aAAa,CAACc,CAAC,CAACG,CAAC,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,IAAIE,SAAS,GAAGvB,CAAC,CAACwB,MAAM,CAACpB,aAAa,EAAE,UAAUE,KAAK,EAAE;IACvD;IACA,OAAO,CAACA,KAAK,CAACI,QAAQ;EACxB,CAAC,CAAC;EAEF,OAAOe,kBAAkB,CAACF,SAAS,CAAC;AACtC;AAEA,SAASE,kBAAkBA,CAACF,SAAS,EAAE;EACrC,IAAIrB,OAAO,GAAG,EAAE;EAEhB,SAASwB,QAAQA,CAACC,MAAM,EAAE;IACxB,OAAO,UAAUC,MAAM,EAAE;MACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;QACjB;MACF;MACA,IACE7B,CAAC,CAACc,WAAW,CAACc,MAAM,CAACb,UAAU,CAAC,IAChCf,CAAC,CAACc,WAAW,CAACa,MAAM,CAACZ,UAAU,CAAC,IAChCa,MAAM,CAACb,UAAU,IAAIY,MAAM,CAACZ,UAAU,EACtC;QACAe,YAAY,CAACH,MAAM,EAAEC,MAAM,CAAC;MAC9B;IACF,CAAC;EACH;EAEA,SAASG,SAASA,CAACJ,MAAM,EAAE;IACzB,OAAO,UAAUK,MAAM,EAAE;MACvBA,MAAM,CAAC,IAAI,CAAC,CAACV,IAAI,CAACK,MAAM,CAAC;MACzB,IAAI,EAAEK,MAAM,CAACtB,QAAQ,KAAK,CAAC,EAAE;QAC3Ba,SAAS,CAACD,IAAI,CAACU,MAAM,CAAC;MACxB;IACF,CAAC;EACH;EAEA,OAAOT,SAAS,CAACU,MAAM,EAAE;IACvB,IAAI3B,KAAK,GAAGiB,SAAS,CAACW,GAAG,CAAC,CAAC;IAC3BhC,OAAO,CAACoB,IAAI,CAAChB,KAAK,CAAC;IACnBN,CAAC,CAACK,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC6B,OAAO,CAAC,CAAC,EAAET,QAAQ,CAACpB,KAAK,CAAC,CAAC;IACjDN,CAAC,CAACK,OAAO,CAACC,KAAK,CAACM,GAAG,EAAEmB,SAAS,CAACzB,KAAK,CAAC,CAAC;EACxC;EAEA,OAAON,CAAC,CAACoC,GAAG,CACVpC,CAAC,CAACwB,MAAM,CAACtB,OAAO,EAAE,UAAUI,KAAK,EAAE;IACjC,OAAO,CAACA,KAAK,CAACuB,MAAM;EACtB,CAAC,CAAC,EACF,UAAUvB,KAAK,EAAE;IACf,OAAON,CAAC,CAACqC,IAAI,CAAC/B,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;EAC3D,CACF,CAAC;AACH;AAEA,SAASwB,YAAYA,CAACQ,MAAM,EAAEC,MAAM,EAAE;EACpC,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIxB,MAAM,GAAG,CAAC;EAEd,IAAIsB,MAAM,CAACtB,MAAM,EAAE;IACjBwB,GAAG,IAAIF,MAAM,CAACvB,UAAU,GAAGuB,MAAM,CAACtB,MAAM;IACxCA,MAAM,IAAIsB,MAAM,CAACtB,MAAM;EACzB;EAEA,IAAIuB,MAAM,CAACvB,MAAM,EAAE;IACjBwB,GAAG,IAAID,MAAM,CAACxB,UAAU,GAAGwB,MAAM,CAACvB,MAAM;IACxCA,MAAM,IAAIuB,MAAM,CAACvB,MAAM;EACzB;EAEAsB,MAAM,CAACzB,EAAE,GAAG0B,MAAM,CAAC1B,EAAE,CAAC4B,MAAM,CAACH,MAAM,CAACzB,EAAE,CAAC;EACvCyB,MAAM,CAACvB,UAAU,GAAGyB,GAAG,GAAGxB,MAAM;EAChCsB,MAAM,CAACtB,MAAM,GAAGA,MAAM;EACtBsB,MAAM,CAAC/B,CAAC,GAAGmC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAChC,CAAC,EAAE+B,MAAM,CAAC/B,CAAC,CAAC;EACvCgC,MAAM,CAACV,MAAM,GAAG,IAAI;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}