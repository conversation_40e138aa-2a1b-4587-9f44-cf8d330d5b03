{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { RegExpParser, BaseRegExpVisitor } from '@chevrotain/regexp-to-ast';\nexport const NEWLINE_REGEXP = /\\r?\\n/gm;\nconst regexpParser = new RegExpParser();\n/**\n * This class is in charge of heuristically identifying start/end tokens of terminals.\n *\n * The way this works is by doing the following:\n * 1. Traverse the regular expression in the \"start state\"\n * 2. Add any encountered sets/single characters to the \"start regexp\"\n * 3. Once we encounter any variable-length content (i.e. with quantifiers such as +/?/*), we enter the \"end state\"\n * 4. In the end state, any sets/single characters are added to an \"end stack\".\n * 5. If we re-encounter any variable-length content we reset the end stack\n * 6. We continue visiting the regex until the end, reseting the end stack and rebuilding it as necessary\n *\n * After traversing a regular expression the `startRegexp/endRegexp` properties allow access to the stored start/end of the terminal\n */\nclass TerminalRegExpVisitor extends BaseRegExpVisitor {\n  constructor() {\n    super(...arguments);\n    this.isStarting = true;\n    this.endRegexpStack = [];\n    this.multiline = false;\n  }\n  get endRegex() {\n    return this.endRegexpStack.join('');\n  }\n  reset(regex) {\n    this.multiline = false;\n    this.regex = regex;\n    this.startRegexp = '';\n    this.isStarting = true;\n    this.endRegexpStack = [];\n  }\n  visitGroup(node) {\n    if (node.quantifier) {\n      this.isStarting = false;\n      this.endRegexpStack = [];\n    }\n  }\n  visitCharacter(node) {\n    const char = String.fromCharCode(node.value);\n    if (!this.multiline && char === '\\n') {\n      this.multiline = true;\n    }\n    if (node.quantifier) {\n      this.isStarting = false;\n      this.endRegexpStack = [];\n    } else {\n      const escapedChar = escapeRegExp(char);\n      this.endRegexpStack.push(escapedChar);\n      if (this.isStarting) {\n        this.startRegexp += escapedChar;\n      }\n    }\n  }\n  visitSet(node) {\n    if (!this.multiline) {\n      const set = this.regex.substring(node.loc.begin, node.loc.end);\n      const regex = new RegExp(set);\n      this.multiline = Boolean('\\n'.match(regex));\n    }\n    if (node.quantifier) {\n      this.isStarting = false;\n      this.endRegexpStack = [];\n    } else {\n      const set = this.regex.substring(node.loc.begin, node.loc.end);\n      this.endRegexpStack.push(set);\n      if (this.isStarting) {\n        this.startRegexp += set;\n      }\n    }\n  }\n  visitChildren(node) {\n    if (node.type === 'Group') {\n      // Ignore children of groups with quantifier (+/*/?)\n      // These groups are unrelated to start/end tokens of terminals\n      const group = node;\n      if (group.quantifier) {\n        return;\n      }\n    }\n    super.visitChildren(node);\n  }\n}\nconst visitor = new TerminalRegExpVisitor();\nexport function getTerminalParts(regexp) {\n  try {\n    if (typeof regexp !== 'string') {\n      regexp = regexp.source;\n    }\n    regexp = `/${regexp}/`;\n    const pattern = regexpParser.pattern(regexp);\n    const parts = [];\n    for (const alternative of pattern.value.value) {\n      visitor.reset(regexp);\n      visitor.visit(alternative);\n      parts.push({\n        start: visitor.startRegexp,\n        end: visitor.endRegex\n      });\n    }\n    return parts;\n  } catch (_a) {\n    return [];\n  }\n}\nexport function isMultilineComment(regexp) {\n  try {\n    if (typeof regexp === 'string') {\n      regexp = new RegExp(regexp);\n    }\n    regexp = regexp.toString();\n    visitor.reset(regexp);\n    // Parsing the pattern might fail (since it's user code)\n    visitor.visit(regexpParser.pattern(regexp));\n    return visitor.multiline;\n  } catch (_a) {\n    return false;\n  }\n}\n/**\n * A set of all characters that are considered whitespace by the '\\s' RegExp character class.\n * Taken from [MDN](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_expressions/Character_classes).\n */\nexport const whitespaceCharacters = ('\\f\\n\\r\\t\\v\\u0020\\u00a0\\u1680\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007' + '\\u2008\\u2009\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff').split('');\nexport function isWhitespace(value) {\n  const regexp = typeof value === 'string' ? new RegExp(value) : value;\n  return whitespaceCharacters.some(ws => regexp.test(ws));\n}\nexport function escapeRegExp(value) {\n  return value.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\nexport function getCaseInsensitivePattern(keyword) {\n  return Array.prototype.map.call(keyword, letter => /\\w/.test(letter) ? `[${letter.toLowerCase()}${letter.toUpperCase()}]` : escapeRegExp(letter)).join('');\n}\n/**\n * Determines whether the given input has a partial match with the specified regex.\n * @param regex The regex to partially match against\n * @param input The input string\n * @returns Whether any match exists.\n */\nexport function partialMatches(regex, input) {\n  const partial = partialRegExp(regex);\n  const match = input.match(partial);\n  return !!match && match[0].length > 0;\n}\n/**\n * Builds a partial regex from the input regex. A partial regex is able to match incomplete input strings. E.g.\n * a partial regex constructed from `/ab/` is able to match the string `a` without needing a following `b` character. However it won't match `b` alone.\n * @param regex The input regex to be converted.\n * @returns A partial regex constructed from the input regex.\n */\nexport function partialRegExp(regex) {\n  if (typeof regex === 'string') {\n    regex = new RegExp(regex);\n  }\n  const re = regex,\n    source = regex.source;\n  let i = 0;\n  function process() {\n    let result = '',\n      tmp;\n    function appendRaw(nbChars) {\n      result += source.substr(i, nbChars);\n      i += nbChars;\n    }\n    function appendOptional(nbChars) {\n      result += '(?:' + source.substr(i, nbChars) + '|$)';\n      i += nbChars;\n    }\n    while (i < source.length) {\n      switch (source[i]) {\n        case '\\\\':\n          switch (source[i + 1]) {\n            case 'c':\n              appendOptional(3);\n              break;\n            case 'x':\n              appendOptional(4);\n              break;\n            case 'u':\n              if (re.unicode) {\n                if (source[i + 2] === '{') {\n                  appendOptional(source.indexOf('}', i) - i + 1);\n                } else {\n                  appendOptional(6);\n                }\n              } else {\n                appendOptional(2);\n              }\n              break;\n            case 'p':\n            case 'P':\n              if (re.unicode) {\n                appendOptional(source.indexOf('}', i) - i + 1);\n              } else {\n                appendOptional(2);\n              }\n              break;\n            case 'k':\n              appendOptional(source.indexOf('>', i) - i + 1);\n              break;\n            default:\n              appendOptional(2);\n              break;\n          }\n          break;\n        case '[':\n          tmp = /\\[(?:\\\\.|.)*?\\]/g;\n          tmp.lastIndex = i;\n          tmp = tmp.exec(source) || [];\n          appendOptional(tmp[0].length);\n          break;\n        case '|':\n        case '^':\n        case '$':\n        case '*':\n        case '+':\n        case '?':\n          appendRaw(1);\n          break;\n        case '{':\n          tmp = /\\{\\d+,?\\d*\\}/g;\n          tmp.lastIndex = i;\n          tmp = tmp.exec(source);\n          if (tmp) {\n            appendRaw(tmp[0].length);\n          } else {\n            appendOptional(1);\n          }\n          break;\n        case '(':\n          if (source[i + 1] === '?') {\n            switch (source[i + 2]) {\n              case ':':\n                result += '(?:';\n                i += 3;\n                result += process() + '|$)';\n                break;\n              case '=':\n                result += '(?=';\n                i += 3;\n                result += process() + ')';\n                break;\n              case '!':\n                tmp = i;\n                i += 3;\n                process();\n                result += source.substr(tmp, i - tmp);\n                break;\n              case '<':\n                switch (source[i + 3]) {\n                  case '=':\n                  case '!':\n                    tmp = i;\n                    i += 4;\n                    process();\n                    result += source.substr(tmp, i - tmp);\n                    break;\n                  default:\n                    appendRaw(source.indexOf('>', i) - i + 1);\n                    result += process() + '|$)';\n                    break;\n                }\n                break;\n            }\n          } else {\n            appendRaw(1);\n            result += process() + '|$)';\n          }\n          break;\n        case ')':\n          ++i;\n          return result;\n        default:\n          appendOptional(1);\n          break;\n      }\n    }\n    return result;\n  }\n  return new RegExp(process(), regex.flags);\n}", "map": {"version": 3, "names": ["Reg<PERSON>xp<PERSON><PERSON><PERSON>", "BaseRegExpVisitor", "NEWLINE_REGEXP", "rege<PERSON><PERSON><PERSON><PERSON><PERSON>", "TerminalRegExpVisitor", "constructor", "isStarting", "endRegexpStack", "multiline", "endRegex", "join", "reset", "regex", "startRegexp", "visitGroup", "node", "quantifier", "visitCharacter", "char", "String", "fromCharCode", "value", "escapedChar", "escapeRegExp", "push", "visitSet", "set", "substring", "loc", "begin", "end", "RegExp", "Boolean", "match", "visit<PERSON><PERSON><PERSON><PERSON>", "type", "group", "visitor", "getTerminalParts", "regexp", "source", "pattern", "parts", "alternative", "visit", "start", "_a", "isMultilineComment", "toString", "whitespaceCharacters", "split", "isWhitespace", "some", "ws", "test", "replace", "getCaseInsensitivePattern", "keyword", "Array", "prototype", "map", "call", "letter", "toLowerCase", "toUpperCase", "partialMatch<PERSON>", "input", "partial", "partialRegExp", "length", "re", "i", "process", "result", "tmp", "appendRaw", "nbChars", "substr", "appendOptional", "unicode", "indexOf", "lastIndex", "exec", "flags"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/regexp-utils.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { Set, Group, Character, IRegExpAST } from '@chevrotain/regexp-to-ast';\r\nimport { RegExpParser, BaseRegExpVisitor } from '@chevrotain/regexp-to-ast';\r\n\r\nexport const NEWLINE_REGEXP = /\\r?\\n/gm;\r\n\r\nconst regexpParser = new RegExpParser();\r\n\r\n/**\r\n * This class is in charge of heuristically identifying start/end tokens of terminals.\r\n *\r\n * The way this works is by doing the following:\r\n * 1. Traverse the regular expression in the \"start state\"\r\n * 2. Add any encountered sets/single characters to the \"start regexp\"\r\n * 3. Once we encounter any variable-length content (i.e. with quantifiers such as +/?/*), we enter the \"end state\"\r\n * 4. In the end state, any sets/single characters are added to an \"end stack\".\r\n * 5. If we re-encounter any variable-length content we reset the end stack\r\n * 6. We continue visiting the regex until the end, reseting the end stack and rebuilding it as necessary\r\n *\r\n * After traversing a regular expression the `startRegexp/endRegexp` properties allow access to the stored start/end of the terminal\r\n */\r\nclass TerminalRegExpVisitor extends BaseRegExpVisitor {\r\n\r\n    private isStarting = true;\r\n    startRegexp: string;\r\n    private endRegexpStack: string[] = [];\r\n    multiline = false;\r\n    regex: string;\r\n\r\n    get endRegex(): string {\r\n        return this.endRegexpStack.join('');\r\n    }\r\n\r\n    reset(regex: string): void {\r\n        this.multiline = false;\r\n        this.regex = regex;\r\n        this.startRegexp = '';\r\n        this.isStarting = true;\r\n        this.endRegexpStack = [];\r\n    }\r\n\r\n    override visitGroup(node: Group) {\r\n        if (node.quantifier) {\r\n            this.isStarting = false;\r\n            this.endRegexpStack = [];\r\n        }\r\n    }\r\n\r\n    override visitCharacter(node: Character): void {\r\n        const char = String.fromCharCode(node.value);\r\n        if (!this.multiline && char === '\\n') {\r\n            this.multiline = true;\r\n        }\r\n        if (node.quantifier) {\r\n            this.isStarting = false;\r\n            this.endRegexpStack = [];\r\n        } else {\r\n            const escapedChar = escapeRegExp(char);\r\n            this.endRegexpStack.push(escapedChar);\r\n            if (this.isStarting) {\r\n                this.startRegexp += escapedChar;\r\n            }\r\n        }\r\n    }\r\n\r\n    override visitSet(node: Set): void {\r\n        if (!this.multiline) {\r\n            const set = this.regex.substring(node.loc.begin, node.loc.end);\r\n            const regex = new RegExp(set);\r\n            this.multiline = Boolean('\\n'.match(regex));\r\n        }\r\n        if (node.quantifier) {\r\n            this.isStarting = false;\r\n            this.endRegexpStack = [];\r\n        } else {\r\n            const set = this.regex.substring(node.loc.begin, node.loc.end);\r\n            this.endRegexpStack.push(set);\r\n            if (this.isStarting) {\r\n                this.startRegexp += set;\r\n            }\r\n        }\r\n    }\r\n\r\n    override visitChildren(node: IRegExpAST): void {\r\n        if (node.type === 'Group') {\r\n            // Ignore children of groups with quantifier (+/*/?)\r\n            // These groups are unrelated to start/end tokens of terminals\r\n            const group = node as Group;\r\n            if (group.quantifier) {\r\n                return;\r\n            }\r\n        }\r\n        super.visitChildren(node);\r\n    }\r\n}\r\n\r\nconst visitor = new TerminalRegExpVisitor();\r\n\r\nexport function getTerminalParts(regexp: RegExp | string): Array<{ start: string, end: string }> {\r\n    try {\r\n        if (typeof regexp !== 'string') {\r\n            regexp = regexp.source;\r\n        }\r\n        regexp = `/${regexp}/`;\r\n        const pattern = regexpParser.pattern(regexp);\r\n        const parts: Array<{ start: string, end: string }> = [];\r\n        for (const alternative of pattern.value.value) {\r\n            visitor.reset(regexp);\r\n            visitor.visit(alternative);\r\n            parts.push({\r\n                start: visitor.startRegexp,\r\n                end: visitor.endRegex\r\n            });\r\n        }\r\n        return parts;\r\n    } catch {\r\n        return [];\r\n    }\r\n}\r\n\r\nexport function isMultilineComment(regexp: RegExp | string): boolean {\r\n    try {\r\n        if (typeof regexp === 'string') {\r\n            regexp = new RegExp(regexp);\r\n        }\r\n        regexp = regexp.toString();\r\n        visitor.reset(regexp);\r\n        // Parsing the pattern might fail (since it's user code)\r\n        visitor.visit(regexpParser.pattern(regexp));\r\n        return visitor.multiline;\r\n    } catch {\r\n        return false;\r\n    }\r\n}\r\n\r\n/**\r\n * A set of all characters that are considered whitespace by the '\\s' RegExp character class.\r\n * Taken from [MDN](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_expressions/Character_classes).\r\n */\r\nexport const whitespaceCharacters = (\r\n    '\\f\\n\\r\\t\\v\\u0020\\u00a0\\u1680\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007' +\r\n    '\\u2008\\u2009\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff').split('');\r\n\r\nexport function isWhitespace(value: RegExp | string): boolean {\r\n    const regexp = typeof value === 'string' ? new RegExp(value) : value;\r\n    return whitespaceCharacters.some((ws) => regexp.test(ws));\r\n}\r\n\r\nexport function escapeRegExp(value: string): string {\r\n    return value.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\n\r\nexport function getCaseInsensitivePattern(keyword: string): string {\r\n    return Array.prototype.map.call(keyword, letter =>\r\n        /\\w/.test(letter) ? `[${letter.toLowerCase()}${letter.toUpperCase()}]` : escapeRegExp(letter)\r\n    ).join('');\r\n}\r\n\r\n/**\r\n * Determines whether the given input has a partial match with the specified regex.\r\n * @param regex The regex to partially match against\r\n * @param input The input string\r\n * @returns Whether any match exists.\r\n */\r\nexport function partialMatches(regex: RegExp | string, input: string): boolean {\r\n    const partial = partialRegExp(regex);\r\n    const match = input.match(partial);\r\n    return !!match && match[0].length > 0;\r\n}\r\n\r\n/**\r\n * Builds a partial regex from the input regex. A partial regex is able to match incomplete input strings. E.g.\r\n * a partial regex constructed from `/ab/` is able to match the string `a` without needing a following `b` character. However it won't match `b` alone.\r\n * @param regex The input regex to be converted.\r\n * @returns A partial regex constructed from the input regex.\r\n */\r\nexport function partialRegExp(regex: RegExp | string): RegExp {\r\n    if (typeof regex === 'string') {\r\n        regex = new RegExp(regex);\r\n    }\r\n    const re = regex, source = regex.source;\r\n    let i = 0;\r\n\r\n    function process() {\r\n        let result = '',\r\n            tmp;\r\n\r\n        function appendRaw(nbChars: number) {\r\n            result += source.substr(i, nbChars);\r\n            i += nbChars;\r\n        }\r\n\r\n        function appendOptional(nbChars: number) {\r\n            result += '(?:' + source.substr(i, nbChars) + '|$)';\r\n            i += nbChars;\r\n        }\r\n\r\n        while (i < source.length) {\r\n            switch (source[i]) {\r\n                case '\\\\':\r\n                    switch (source[i + 1]) {\r\n                        case 'c':\r\n                            appendOptional(3);\r\n                            break;\r\n                        case 'x':\r\n                            appendOptional(4);\r\n                            break;\r\n                        case 'u':\r\n                            if (re.unicode) {\r\n                                if (source[i + 2] === '{') {\r\n                                    appendOptional(source.indexOf('}', i) - i + 1);\r\n                                } else {\r\n                                    appendOptional(6);\r\n                                }\r\n                            } else {\r\n                                appendOptional(2);\r\n                            }\r\n                            break;\r\n                        case 'p':\r\n                        case 'P':\r\n                            if (re.unicode) {\r\n                                appendOptional(source.indexOf('}', i) - i + 1);\r\n                            } else {\r\n                                appendOptional(2);\r\n                            }\r\n                            break;\r\n                        case 'k':\r\n                            appendOptional(source.indexOf('>', i) - i + 1);\r\n                            break;\r\n                        default:\r\n                            appendOptional(2);\r\n                            break;\r\n                    }\r\n                    break;\r\n\r\n                case '[':\r\n                    tmp = /\\[(?:\\\\.|.)*?\\]/g;\r\n                    tmp.lastIndex = i;\r\n                    tmp = tmp.exec(source) || [];\r\n                    appendOptional(tmp[0].length);\r\n                    break;\r\n\r\n                case '|':\r\n                case '^':\r\n                case '$':\r\n                case '*':\r\n                case '+':\r\n                case '?':\r\n                    appendRaw(1);\r\n                    break;\r\n                case '{':\r\n                    tmp = /\\{\\d+,?\\d*\\}/g;\r\n                    tmp.lastIndex = i;\r\n                    tmp = tmp.exec(source);\r\n                    if (tmp) {\r\n                        appendRaw(tmp[0].length);\r\n                    } else {\r\n                        appendOptional(1);\r\n                    }\r\n                    break;\r\n                case '(':\r\n                    if (source[i + 1] === '?') {\r\n                        switch (source[i + 2]) {\r\n                            case ':':\r\n                                result += '(?:';\r\n                                i += 3;\r\n                                result += process() + '|$)';\r\n                                break;\r\n                            case '=':\r\n                                result += '(?=';\r\n                                i += 3;\r\n                                result += process() + ')';\r\n                                break;\r\n                            case '!':\r\n                                tmp = i;\r\n                                i += 3;\r\n                                process();\r\n                                result += source.substr(tmp, i - tmp);\r\n                                break;\r\n                            case '<':\r\n                                switch (source[i + 3]) {\r\n                                    case '=':\r\n                                    case '!':\r\n                                        tmp = i;\r\n                                        i += 4;\r\n                                        process();\r\n                                        result += source.substr(tmp, i - tmp);\r\n                                        break;\r\n                                    default:\r\n                                        appendRaw(source.indexOf('>', i) - i + 1);\r\n                                        result += process() + '|$)';\r\n                                        break;\r\n                                }\r\n                                break;\r\n                        }\r\n                    } else {\r\n                        appendRaw(1);\r\n                        result += process() + '|$)';\r\n                    }\r\n                    break;\r\n                case ')':\r\n                    ++i;\r\n                    return result;\r\n                default:\r\n                    appendOptional(1);\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    return new RegExp(process(), regex.flags);\r\n}\r\n"], "mappings": "AAAA;;;;;AAOA,SAASA,YAAY,EAAEC,iBAAiB,QAAQ,2BAA2B;AAE3E,OAAO,MAAMC,cAAc,GAAG,SAAS;AAEvC,MAAMC,YAAY,GAAG,IAAIH,YAAY,EAAE;AAEvC;;;;;;;;;;;;;AAaA,MAAMI,qBAAsB,SAAQH,iBAAiB;EAArDI,YAAA;;IAEY,KAAAC,UAAU,GAAG,IAAI;IAEjB,KAAAC,cAAc,GAAa,EAAE;IACrC,KAAAC,SAAS,GAAG,KAAK;EAoErB;EAjEI,IAAIC,QAAQA,CAAA;IACR,OAAO,IAAI,CAACF,cAAc,CAACG,IAAI,CAAC,EAAE,CAAC;EACvC;EAEAC,KAAKA,CAACC,KAAa;IACf,IAAI,CAACJ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACP,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC5B;EAESO,UAAUA,CAACC,IAAW;IAC3B,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACV,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IAC5B;EACJ;EAESU,cAAcA,CAACF,IAAe;IACnC,MAAMG,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACL,IAAI,CAACM,KAAK,CAAC;IAC5C,IAAI,CAAC,IAAI,CAACb,SAAS,IAAIU,IAAI,KAAK,IAAI,EAAE;MAClC,IAAI,CAACV,SAAS,GAAG,IAAI;IACzB;IACA,IAAIO,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACV,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IAC5B,CAAC,MAAM;MACH,MAAMe,WAAW,GAAGC,YAAY,CAACL,IAAI,CAAC;MACtC,IAAI,CAACX,cAAc,CAACiB,IAAI,CAACF,WAAW,CAAC;MACrC,IAAI,IAAI,CAAChB,UAAU,EAAE;QACjB,IAAI,CAACO,WAAW,IAAIS,WAAW;MACnC;IACJ;EACJ;EAESG,QAAQA,CAACV,IAAS;IACvB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;MACjB,MAAMkB,GAAG,GAAG,IAAI,CAACd,KAAK,CAACe,SAAS,CAACZ,IAAI,CAACa,GAAG,CAACC,KAAK,EAAEd,IAAI,CAACa,GAAG,CAACE,GAAG,CAAC;MAC9D,MAAMlB,KAAK,GAAG,IAAImB,MAAM,CAACL,GAAG,CAAC;MAC7B,IAAI,CAAClB,SAAS,GAAGwB,OAAO,CAAC,IAAI,CAACC,KAAK,CAACrB,KAAK,CAAC,CAAC;IAC/C;IACA,IAAIG,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACV,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IAC5B,CAAC,MAAM;MACH,MAAMmB,GAAG,GAAG,IAAI,CAACd,KAAK,CAACe,SAAS,CAACZ,IAAI,CAACa,GAAG,CAACC,KAAK,EAAEd,IAAI,CAACa,GAAG,CAACE,GAAG,CAAC;MAC9D,IAAI,CAACvB,cAAc,CAACiB,IAAI,CAACE,GAAG,CAAC;MAC7B,IAAI,IAAI,CAACpB,UAAU,EAAE;QACjB,IAAI,CAACO,WAAW,IAAIa,GAAG;MAC3B;IACJ;EACJ;EAESQ,aAAaA,CAACnB,IAAgB;IACnC,IAAIA,IAAI,CAACoB,IAAI,KAAK,OAAO,EAAE;MACvB;MACA;MACA,MAAMC,KAAK,GAAGrB,IAAa;MAC3B,IAAIqB,KAAK,CAACpB,UAAU,EAAE;QAClB;MACJ;IACJ;IACA,KAAK,CAACkB,aAAa,CAACnB,IAAI,CAAC;EAC7B;;AAGJ,MAAMsB,OAAO,GAAG,IAAIjC,qBAAqB,EAAE;AAE3C,OAAM,SAAUkC,gBAAgBA,CAACC,MAAuB;EACpD,IAAI;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGA,MAAM,CAACC,MAAM;IAC1B;IACAD,MAAM,GAAG,IAAIA,MAAM,GAAG;IACtB,MAAME,OAAO,GAAGtC,YAAY,CAACsC,OAAO,CAACF,MAAM,CAAC;IAC5C,MAAMG,KAAK,GAA0C,EAAE;IACvD,KAAK,MAAMC,WAAW,IAAIF,OAAO,CAACpB,KAAK,CAACA,KAAK,EAAE;MAC3CgB,OAAO,CAAC1B,KAAK,CAAC4B,MAAM,CAAC;MACrBF,OAAO,CAACO,KAAK,CAACD,WAAW,CAAC;MAC1BD,KAAK,CAAClB,IAAI,CAAC;QACPqB,KAAK,EAAER,OAAO,CAACxB,WAAW;QAC1BiB,GAAG,EAAEO,OAAO,CAAC5B;OAChB,CAAC;IACN;IACA,OAAOiC,KAAK;EAChB,CAAC,CAAC,OAAAI,EAAA,EAAM;IACJ,OAAO,EAAE;EACb;AACJ;AAEA,OAAM,SAAUC,kBAAkBA,CAACR,MAAuB;EACtD,IAAI;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAG,IAAIR,MAAM,CAACQ,MAAM,CAAC;IAC/B;IACAA,MAAM,GAAGA,MAAM,CAACS,QAAQ,EAAE;IAC1BX,OAAO,CAAC1B,KAAK,CAAC4B,MAAM,CAAC;IACrB;IACAF,OAAO,CAACO,KAAK,CAACzC,YAAY,CAACsC,OAAO,CAACF,MAAM,CAAC,CAAC;IAC3C,OAAOF,OAAO,CAAC7B,SAAS;EAC5B,CAAC,CAAC,OAAAsC,EAAA,EAAM;IACJ,OAAO,KAAK;EAChB;AACJ;AAEA;;;;AAIA,OAAO,MAAMG,oBAAoB,GAAG,CAChC,8EAA8E,GAC9E,wDAAwD,EAAEC,KAAK,CAAC,EAAE,CAAC;AAEvE,OAAM,SAAUC,YAAYA,CAAC9B,KAAsB;EAC/C,MAAMkB,MAAM,GAAG,OAAOlB,KAAK,KAAK,QAAQ,GAAG,IAAIU,MAAM,CAACV,KAAK,CAAC,GAAGA,KAAK;EACpE,OAAO4B,oBAAoB,CAACG,IAAI,CAAEC,EAAE,IAAKd,MAAM,CAACe,IAAI,CAACD,EAAE,CAAC,CAAC;AAC7D;AAEA,OAAM,SAAU9B,YAAYA,CAACF,KAAa;EACtC,OAAOA,KAAK,CAACkC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACvD;AAEA,OAAM,SAAUC,yBAAyBA,CAACC,OAAe;EACrD,OAAOC,KAAK,CAACC,SAAS,CAACC,GAAG,CAACC,IAAI,CAACJ,OAAO,EAAEK,MAAM,IAC3C,IAAI,CAACR,IAAI,CAACQ,MAAM,CAAC,GAAG,IAAIA,MAAM,CAACC,WAAW,EAAE,GAAGD,MAAM,CAACE,WAAW,EAAE,GAAG,GAAGzC,YAAY,CAACuC,MAAM,CAAC,CAChG,CAACpD,IAAI,CAAC,EAAE,CAAC;AACd;AAEA;;;;;;AAMA,OAAM,SAAUuD,cAAcA,CAACrD,KAAsB,EAAEsD,KAAa;EAChE,MAAMC,OAAO,GAAGC,aAAa,CAACxD,KAAK,CAAC;EACpC,MAAMqB,KAAK,GAAGiC,KAAK,CAACjC,KAAK,CAACkC,OAAO,CAAC;EAClC,OAAO,CAAC,CAAClC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACoC,MAAM,GAAG,CAAC;AACzC;AAEA;;;;;;AAMA,OAAM,SAAUD,aAAaA,CAACxD,KAAsB;EAChD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3BA,KAAK,GAAG,IAAImB,MAAM,CAACnB,KAAK,CAAC;EAC7B;EACA,MAAM0D,EAAE,GAAG1D,KAAK;IAAE4B,MAAM,GAAG5B,KAAK,CAAC4B,MAAM;EACvC,IAAI+B,CAAC,GAAG,CAAC;EAET,SAASC,OAAOA,CAAA;IACZ,IAAIC,MAAM,GAAG,EAAE;MACXC,GAAG;IAEP,SAASC,SAASA,CAACC,OAAe;MAC9BH,MAAM,IAAIjC,MAAM,CAACqC,MAAM,CAACN,CAAC,EAAEK,OAAO,CAAC;MACnCL,CAAC,IAAIK,OAAO;IAChB;IAEA,SAASE,cAAcA,CAACF,OAAe;MACnCH,MAAM,IAAI,KAAK,GAAGjC,MAAM,CAACqC,MAAM,CAACN,CAAC,EAAEK,OAAO,CAAC,GAAG,KAAK;MACnDL,CAAC,IAAIK,OAAO;IAChB;IAEA,OAAOL,CAAC,GAAG/B,MAAM,CAAC6B,MAAM,EAAE;MACtB,QAAQ7B,MAAM,CAAC+B,CAAC,CAAC;QACb,KAAK,IAAI;UACL,QAAQ/B,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC;YACjB,KAAK,GAAG;cACJO,cAAc,CAAC,CAAC,CAAC;cACjB;YACJ,KAAK,GAAG;cACJA,cAAc,CAAC,CAAC,CAAC;cACjB;YACJ,KAAK,GAAG;cACJ,IAAIR,EAAE,CAACS,OAAO,EAAE;gBACZ,IAAIvC,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;kBACvBO,cAAc,CAACtC,MAAM,CAACwC,OAAO,CAAC,GAAG,EAAET,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;gBAClD,CAAC,MAAM;kBACHO,cAAc,CAAC,CAAC,CAAC;gBACrB;cACJ,CAAC,MAAM;gBACHA,cAAc,CAAC,CAAC,CAAC;cACrB;cACA;YACJ,KAAK,GAAG;YACR,KAAK,GAAG;cACJ,IAAIR,EAAE,CAACS,OAAO,EAAE;gBACZD,cAAc,CAACtC,MAAM,CAACwC,OAAO,CAAC,GAAG,EAAET,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;cAClD,CAAC,MAAM;gBACHO,cAAc,CAAC,CAAC,CAAC;cACrB;cACA;YACJ,KAAK,GAAG;cACJA,cAAc,CAACtC,MAAM,CAACwC,OAAO,CAAC,GAAG,EAAET,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;cAC9C;YACJ;cACIO,cAAc,CAAC,CAAC,CAAC;cACjB;UACR;UACA;QAEJ,KAAK,GAAG;UACJJ,GAAG,GAAG,kBAAkB;UACxBA,GAAG,CAACO,SAAS,GAAGV,CAAC;UACjBG,GAAG,GAAGA,GAAG,CAACQ,IAAI,CAAC1C,MAAM,CAAC,IAAI,EAAE;UAC5BsC,cAAc,CAACJ,GAAG,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC;UAC7B;QAEJ,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;UACJM,SAAS,CAAC,CAAC,CAAC;UACZ;QACJ,KAAK,GAAG;UACJD,GAAG,GAAG,eAAe;UACrBA,GAAG,CAACO,SAAS,GAAGV,CAAC;UACjBG,GAAG,GAAGA,GAAG,CAACQ,IAAI,CAAC1C,MAAM,CAAC;UACtB,IAAIkC,GAAG,EAAE;YACLC,SAAS,CAACD,GAAG,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC;UAC5B,CAAC,MAAM;YACHS,cAAc,CAAC,CAAC,CAAC;UACrB;UACA;QACJ,KAAK,GAAG;UACJ,IAAItC,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACvB,QAAQ/B,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC;cACjB,KAAK,GAAG;gBACJE,MAAM,IAAI,KAAK;gBACfF,CAAC,IAAI,CAAC;gBACNE,MAAM,IAAID,OAAO,EAAE,GAAG,KAAK;gBAC3B;cACJ,KAAK,GAAG;gBACJC,MAAM,IAAI,KAAK;gBACfF,CAAC,IAAI,CAAC;gBACNE,MAAM,IAAID,OAAO,EAAE,GAAG,GAAG;gBACzB;cACJ,KAAK,GAAG;gBACJE,GAAG,GAAGH,CAAC;gBACPA,CAAC,IAAI,CAAC;gBACNC,OAAO,EAAE;gBACTC,MAAM,IAAIjC,MAAM,CAACqC,MAAM,CAACH,GAAG,EAAEH,CAAC,GAAGG,GAAG,CAAC;gBACrC;cACJ,KAAK,GAAG;gBACJ,QAAQlC,MAAM,CAAC+B,CAAC,GAAG,CAAC,CAAC;kBACjB,KAAK,GAAG;kBACR,KAAK,GAAG;oBACJG,GAAG,GAAGH,CAAC;oBACPA,CAAC,IAAI,CAAC;oBACNC,OAAO,EAAE;oBACTC,MAAM,IAAIjC,MAAM,CAACqC,MAAM,CAACH,GAAG,EAAEH,CAAC,GAAGG,GAAG,CAAC;oBACrC;kBACJ;oBACIC,SAAS,CAACnC,MAAM,CAACwC,OAAO,CAAC,GAAG,EAAET,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;oBACzCE,MAAM,IAAID,OAAO,EAAE,GAAG,KAAK;oBAC3B;gBACR;gBACA;YACR;UACJ,CAAC,MAAM;YACHG,SAAS,CAAC,CAAC,CAAC;YACZF,MAAM,IAAID,OAAO,EAAE,GAAG,KAAK;UAC/B;UACA;QACJ,KAAK,GAAG;UACJ,EAAED,CAAC;UACH,OAAOE,MAAM;QACjB;UACIK,cAAc,CAAC,CAAC,CAAC;UACjB;MACR;IACJ;IAEA,OAAOL,MAAM;EACjB;EAEA,OAAO,IAAI1C,MAAM,CAACyC,OAAO,EAAE,EAAE5D,KAAK,CAACuE,KAAK,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}