{"ast": null, "code": "import { clone, drop, dropRight, first as _first, forEach, isEmpty, last } from \"lodash-es\";\nimport { first } from \"./first.js\";\nimport { RestWalker } from \"./rest.js\";\nimport { Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Rule, Terminal } from \"@chevrotain/gast\";\nexport class AbstractNextPossibleTokensWalker extends RestWalker {\n  constructor(topProd, path) {\n    super();\n    this.topProd = topProd;\n    this.path = path;\n    this.possibleTokTypes = [];\n    this.nextProductionName = \"\";\n    this.nextProductionOccurrence = 0;\n    this.found = false;\n    this.isAtEndOfPath = false;\n  }\n  startWalking() {\n    this.found = false;\n    if (this.path.ruleStack[0] !== this.topProd.name) {\n      throw Error(\"The path does not start with the walker's top Rule!\");\n    }\n    // immutable for the win\n    this.ruleStack = clone(this.path.ruleStack).reverse(); // intelij bug requires assertion\n    this.occurrenceStack = clone(this.path.occurrenceStack).reverse(); // intelij bug requires assertion\n    // already verified that the first production is valid, we now seek the 2nd production\n    this.ruleStack.pop();\n    this.occurrenceStack.pop();\n    this.updateExpectedNext();\n    this.walk(this.topProd);\n    return this.possibleTokTypes;\n  }\n  walk(prod, prevRest = []) {\n    // stop scanning once we found the path\n    if (!this.found) {\n      super.walk(prod, prevRest);\n    }\n  }\n  walkProdRef(refProd, currRest, prevRest) {\n    // found the next production, need to keep walking in it\n    if (refProd.referencedRule.name === this.nextProductionName && refProd.idx === this.nextProductionOccurrence) {\n      const fullRest = currRest.concat(prevRest);\n      this.updateExpectedNext();\n      this.walk(refProd.referencedRule, fullRest);\n    }\n  }\n  updateExpectedNext() {\n    // need to consume the Terminal\n    if (isEmpty(this.ruleStack)) {\n      // must reset nextProductionXXX to avoid walking down another Top Level production while what we are\n      // really seeking is the last Terminal...\n      this.nextProductionName = \"\";\n      this.nextProductionOccurrence = 0;\n      this.isAtEndOfPath = true;\n    } else {\n      this.nextProductionName = this.ruleStack.pop();\n      this.nextProductionOccurrence = this.occurrenceStack.pop();\n    }\n  }\n}\nexport class NextAfterTokenWalker extends AbstractNextPossibleTokensWalker {\n  constructor(topProd, path) {\n    super(topProd, path);\n    this.path = path;\n    this.nextTerminalName = \"\";\n    this.nextTerminalOccurrence = 0;\n    this.nextTerminalName = this.path.lastTok.name;\n    this.nextTerminalOccurrence = this.path.lastTokOccurrence;\n  }\n  walkTerminal(terminal, currRest, prevRest) {\n    if (this.isAtEndOfPath && terminal.terminalType.name === this.nextTerminalName && terminal.idx === this.nextTerminalOccurrence && !this.found) {\n      const fullRest = currRest.concat(prevRest);\n      const restProd = new Alternative({\n        definition: fullRest\n      });\n      this.possibleTokTypes = first(restProd);\n      this.found = true;\n    }\n  }\n}\n/**\n * This walker only \"walks\" a single \"TOP\" level in the Grammar Ast, this means\n * it never \"follows\" production refs\n */\nexport class AbstractNextTerminalAfterProductionWalker extends RestWalker {\n  constructor(topRule, occurrence) {\n    super();\n    this.topRule = topRule;\n    this.occurrence = occurrence;\n    this.result = {\n      token: undefined,\n      occurrence: undefined,\n      isEndOfRule: undefined\n    };\n  }\n  startWalking() {\n    this.walk(this.topRule);\n    return this.result;\n  }\n}\nexport class NextTerminalAfterManyWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkMany(manyProd, currRest, prevRest) {\n    if (manyProd.idx === this.occurrence) {\n      const firstAfterMany = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterMany === undefined;\n      if (firstAfterMany instanceof Terminal) {\n        this.result.token = firstAfterMany.terminalType;\n        this.result.occurrence = firstAfterMany.idx;\n      }\n    } else {\n      super.walkMany(manyProd, currRest, prevRest);\n    }\n  }\n}\nexport class NextTerminalAfterManySepWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkManySep(manySepProd, currRest, prevRest) {\n    if (manySepProd.idx === this.occurrence) {\n      const firstAfterManySep = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterManySep === undefined;\n      if (firstAfterManySep instanceof Terminal) {\n        this.result.token = firstAfterManySep.terminalType;\n        this.result.occurrence = firstAfterManySep.idx;\n      }\n    } else {\n      super.walkManySep(manySepProd, currRest, prevRest);\n    }\n  }\n}\nexport class NextTerminalAfterAtLeastOneWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkAtLeastOne(atLeastOneProd, currRest, prevRest) {\n    if (atLeastOneProd.idx === this.occurrence) {\n      const firstAfterAtLeastOne = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterAtLeastOne === undefined;\n      if (firstAfterAtLeastOne instanceof Terminal) {\n        this.result.token = firstAfterAtLeastOne.terminalType;\n        this.result.occurrence = firstAfterAtLeastOne.idx;\n      }\n    } else {\n      super.walkAtLeastOne(atLeastOneProd, currRest, prevRest);\n    }\n  }\n}\n// TODO: reduce code duplication in the AfterWalkers\nexport class NextTerminalAfterAtLeastOneSepWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkAtLeastOneSep(atleastOneSepProd, currRest, prevRest) {\n    if (atleastOneSepProd.idx === this.occurrence) {\n      const firstAfterfirstAfterAtLeastOneSep = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterfirstAfterAtLeastOneSep === undefined;\n      if (firstAfterfirstAfterAtLeastOneSep instanceof Terminal) {\n        this.result.token = firstAfterfirstAfterAtLeastOneSep.terminalType;\n        this.result.occurrence = firstAfterfirstAfterAtLeastOneSep.idx;\n      }\n    } else {\n      super.walkAtLeastOneSep(atleastOneSepProd, currRest, prevRest);\n    }\n  }\n}\nexport function possiblePathsFrom(targetDef, maxLength, currPath = []) {\n  // avoid side effects\n  currPath = clone(currPath);\n  let result = [];\n  let i = 0;\n  // TODO: avoid inner funcs\n  function remainingPathWith(nextDef) {\n    return nextDef.concat(drop(targetDef, i + 1));\n  }\n  // TODO: avoid inner funcs\n  function getAlternativesForProd(definition) {\n    const alternatives = possiblePathsFrom(remainingPathWith(definition), maxLength, currPath);\n    return result.concat(alternatives);\n  }\n  /**\n   * Mandatory productions will halt the loop as the paths computed from their recursive calls will already contain the\n   * following (rest) of the targetDef.\n   *\n   * For optional productions (Option/Repetition/...) the loop will continue to represent the paths that do not include the\n   * the optional production.\n   */\n  while (currPath.length < maxLength && i < targetDef.length) {\n    const prod = targetDef[i];\n    /* istanbul ignore else */\n    if (prod instanceof Alternative) {\n      return getAlternativesForProd(prod.definition);\n    } else if (prod instanceof NonTerminal) {\n      return getAlternativesForProd(prod.definition);\n    } else if (prod instanceof Option) {\n      result = getAlternativesForProd(prod.definition);\n    } else if (prod instanceof RepetitionMandatory) {\n      const newDef = prod.definition.concat([new Repetition({\n        definition: prod.definition\n      })]);\n      return getAlternativesForProd(newDef);\n    } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n      const newDef = [new Alternative({\n        definition: prod.definition\n      }), new Repetition({\n        definition: [new Terminal({\n          terminalType: prod.separator\n        })].concat(prod.definition)\n      })];\n      return getAlternativesForProd(newDef);\n    } else if (prod instanceof RepetitionWithSeparator) {\n      const newDef = prod.definition.concat([new Repetition({\n        definition: [new Terminal({\n          terminalType: prod.separator\n        })].concat(prod.definition)\n      })]);\n      result = getAlternativesForProd(newDef);\n    } else if (prod instanceof Repetition) {\n      const newDef = prod.definition.concat([new Repetition({\n        definition: prod.definition\n      })]);\n      result = getAlternativesForProd(newDef);\n    } else if (prod instanceof Alternation) {\n      forEach(prod.definition, currAlt => {\n        // TODO: this is a limited check for empty alternatives\n        //   It would prevent a common case of infinite loops during parser initialization.\n        //   However **in-directly** empty alternatives may still cause issues.\n        if (isEmpty(currAlt.definition) === false) {\n          result = getAlternativesForProd(currAlt.definition);\n        }\n      });\n      return result;\n    } else if (prod instanceof Terminal) {\n      currPath.push(prod.terminalType);\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n    i++;\n  }\n  result.push({\n    partialPath: currPath,\n    suffixDef: drop(targetDef, i)\n  });\n  return result;\n}\nexport function nextPossibleTokensAfter(initialDef, tokenVector, tokMatcher, maxLookAhead) {\n  const EXIT_NON_TERMINAL = \"EXIT_NONE_TERMINAL\";\n  // to avoid creating a new Array each time.\n  const EXIT_NON_TERMINAL_ARR = [EXIT_NON_TERMINAL];\n  const EXIT_ALTERNATIVE = \"EXIT_ALTERNATIVE\";\n  let foundCompletePath = false;\n  const tokenVectorLength = tokenVector.length;\n  const minimalAlternativesIndex = tokenVectorLength - maxLookAhead - 1;\n  const result = [];\n  const possiblePaths = [];\n  possiblePaths.push({\n    idx: -1,\n    def: initialDef,\n    ruleStack: [],\n    occurrenceStack: []\n  });\n  while (!isEmpty(possiblePaths)) {\n    const currPath = possiblePaths.pop();\n    // skip alternatives if no more results can be found (assuming deterministic grammar with fixed lookahead)\n    if (currPath === EXIT_ALTERNATIVE) {\n      if (foundCompletePath && last(possiblePaths).idx <= minimalAlternativesIndex) {\n        // remove irrelevant alternative\n        possiblePaths.pop();\n      }\n      continue;\n    }\n    const currDef = currPath.def;\n    const currIdx = currPath.idx;\n    const currRuleStack = currPath.ruleStack;\n    const currOccurrenceStack = currPath.occurrenceStack;\n    // For Example: an empty path could exist in a valid grammar in the case of an EMPTY_ALT\n    if (isEmpty(currDef)) {\n      continue;\n    }\n    const prod = currDef[0];\n    /* istanbul ignore else */\n    if (prod === EXIT_NON_TERMINAL) {\n      const nextPath = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: dropRight(currRuleStack),\n        occurrenceStack: dropRight(currOccurrenceStack)\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof Terminal) {\n      /* istanbul ignore else */\n      if (currIdx < tokenVectorLength - 1) {\n        const nextIdx = currIdx + 1;\n        const actualToken = tokenVector[nextIdx];\n        if (tokMatcher(actualToken, prod.terminalType)) {\n          const nextPath = {\n            idx: nextIdx,\n            def: drop(currDef),\n            ruleStack: currRuleStack,\n            occurrenceStack: currOccurrenceStack\n          };\n          possiblePaths.push(nextPath);\n        }\n        // end of the line\n      } else if (currIdx === tokenVectorLength - 1) {\n        // IGNORE ABOVE ELSE\n        result.push({\n          nextTokenType: prod.terminalType,\n          nextTokenOccurrence: prod.idx,\n          ruleStack: currRuleStack,\n          occurrenceStack: currOccurrenceStack\n        });\n        foundCompletePath = true;\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    } else if (prod instanceof NonTerminal) {\n      const newRuleStack = clone(currRuleStack);\n      newRuleStack.push(prod.nonTerminalName);\n      const newOccurrenceStack = clone(currOccurrenceStack);\n      newOccurrenceStack.push(prod.idx);\n      const nextPath = {\n        idx: currIdx,\n        def: prod.definition.concat(EXIT_NON_TERMINAL_ARR, drop(currDef)),\n        ruleStack: newRuleStack,\n        occurrenceStack: newOccurrenceStack\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof Option) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n      const nextPathWith = {\n        idx: currIdx,\n        def: prod.definition.concat(drop(currDef)),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof RepetitionMandatory) {\n      // TODO:(THE NEW operators here take a while...) (convert once?)\n      const secondIteration = new Repetition({\n        definition: prod.definition,\n        idx: prod.idx\n      });\n      const nextDef = prod.definition.concat([secondIteration], drop(currDef));\n      const nextPath = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n      // TODO:(THE NEW operators here take a while...) (convert once?)\n      const separatorGast = new Terminal({\n        terminalType: prod.separator\n      });\n      const secondIteration = new Repetition({\n        definition: [separatorGast].concat(prod.definition),\n        idx: prod.idx\n      });\n      const nextDef = prod.definition.concat([secondIteration], drop(currDef));\n      const nextPath = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof RepetitionWithSeparator) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n      const separatorGast = new Terminal({\n        terminalType: prod.separator\n      });\n      const nthRepetition = new Repetition({\n        definition: [separatorGast].concat(prod.definition),\n        idx: prod.idx\n      });\n      const nextDef = prod.definition.concat([nthRepetition], drop(currDef));\n      const nextPathWith = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof Repetition) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n      // TODO: an empty repetition will cause infinite loops here, will the parser detect this in selfAnalysis?\n      const nthRepetition = new Repetition({\n        definition: prod.definition,\n        idx: prod.idx\n      });\n      const nextDef = prod.definition.concat([nthRepetition], drop(currDef));\n      const nextPathWith = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof Alternation) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      for (let i = prod.definition.length - 1; i >= 0; i--) {\n        const currAlt = prod.definition[i];\n        const currAltPath = {\n          idx: currIdx,\n          def: currAlt.definition.concat(drop(currDef)),\n          ruleStack: currRuleStack,\n          occurrenceStack: currOccurrenceStack\n        };\n        possiblePaths.push(currAltPath);\n        possiblePaths.push(EXIT_ALTERNATIVE);\n      }\n    } else if (prod instanceof Alternative) {\n      possiblePaths.push({\n        idx: currIdx,\n        def: prod.definition.concat(drop(currDef)),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack\n      });\n    } else if (prod instanceof Rule) {\n      // last because we should only encounter at most a single one of these per invocation.\n      possiblePaths.push(expandTopLevelRule(prod, currIdx, currRuleStack, currOccurrenceStack));\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  }\n  return result;\n}\nfunction expandTopLevelRule(topRule, currIdx, currRuleStack, currOccurrenceStack) {\n  const newRuleStack = clone(currRuleStack);\n  newRuleStack.push(topRule.name);\n  const newCurrOccurrenceStack = clone(currOccurrenceStack);\n  // top rule is always assumed to have been called with occurrence index 1\n  newCurrOccurrenceStack.push(1);\n  return {\n    idx: currIdx,\n    def: topRule.definition,\n    ruleStack: newRuleStack,\n    occurrenceStack: newCurrOccurrenceStack\n  };\n}", "map": {"version": 3, "names": ["clone", "drop", "dropRight", "first", "_first", "for<PERSON>ach", "isEmpty", "last", "<PERSON><PERSON><PERSON><PERSON>", "Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Rule", "Terminal", "AbstractNextPossibleTokensWalker", "constructor", "topProd", "path", "possibleTokTypes", "nextProductionName", "nextProductionOccurrence", "found", "isAtEndOfPath", "startWalking", "ruleStack", "name", "Error", "reverse", "occurrenceStack", "pop", "updateExpectedNext", "walk", "prod", "prevRest", "walkProdRef", "refProd", "currRest", "referencedRule", "idx", "fullRest", "concat", "NextAfterToken<PERSON><PERSON>er", "nextTerminalName", "nextTerminalOccurrence", "lastTok", "lastTokOccurrence", "walkTerminal", "terminal", "terminalType", "restProd", "definition", "AbstractNextTerminalAfterProductionWalker", "topRule", "occurrence", "result", "token", "undefined", "isEndOfRule", "NextTerminalAfterManyWalker", "walkMany", "manyProd", "firstAfterMany", "NextTerminalAfterManySepWalker", "walkManySep", "manySepProd", "firstAfterManySep", "NextTerminalAfterAtLeastOneWalker", "walkAtLeastOne", "atLeastOneProd", "firstAfterAtLeastOne", "NextTerminalAfterAtLeastOneSepWalker", "walkAtLeastOneSep", "atleastOneSepProd", "firstAfterfirstAfterAtLeastOneSep", "possiblePathsFrom", "targetDef", "max<PERSON><PERSON><PERSON>", "currPath", "i", "remainingPathWith", "nextDef", "getAlternativesForProd", "alternatives", "length", "newDef", "separator", "currAlt", "push", "partialPath", "suffixDef", "nextPossibleTokensAfter", "initialDef", "tokenVector", "tokMatcher", "maxLookAhead", "EXIT_NON_TERMINAL", "EXIT_NON_TERMINAL_ARR", "EXIT_ALTERNATIVE", "foundCompletePath", "tokenVectorLength", "minimalAlternativesIndex", "possiblePaths", "def", "currDef", "currIdx", "currRuleStack", "currOccurrenceStack", "nextPath", "nextIdx", "actualToken", "nextTokenType", "nextTokenOccurrence", "newRuleStack", "nonTerminalName", "newOccurrenceStack", "nextPathWithout", "nextPathWith", "secondIteration", "separatorGast", "nthRepetition", "currAltPath", "expandTopLevelRule", "newCurrOccurrenceStack"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/interpreter.ts"], "sourcesContent": ["import {\n  clone,\n  drop,\n  dropRight,\n  first as _first,\n  forEach,\n  isEmpty,\n  last,\n} from \"lodash-es\";\nimport { first } from \"./first.js\";\nimport { RestWalker } from \"./rest.js\";\nimport { TokenMatcher } from \"../parser/parser.js\";\nimport {\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport {\n  IGrammarPath,\n  IProduction,\n  ISyntacticContentAssistPath,\n  IToken,\n  ITokenGrammarPath,\n  TokenType,\n} from \"@chevrotain/types\";\n\nexport abstract class AbstractNextPossibleTokensWalker extends RestWalker {\n  protected possibleTokTypes: TokenType[] = [];\n  protected ruleStack: string[];\n  protected occurrenceStack: number[];\n\n  protected nextProductionName = \"\";\n  protected nextProductionOccurrence = 0;\n  protected found = false;\n  protected isAtEndOfPath = false;\n\n  constructor(\n    protected topProd: Rule,\n    protected path: IGrammarPath,\n  ) {\n    super();\n  }\n\n  startWalking(): TokenType[] {\n    this.found = false;\n\n    if (this.path.ruleStack[0] !== this.topProd.name) {\n      throw Error(\"The path does not start with the walker's top Rule!\");\n    }\n\n    // immutable for the win\n    this.ruleStack = clone(this.path.ruleStack).reverse(); // intelij bug requires assertion\n    this.occurrenceStack = clone(this.path.occurrenceStack).reverse(); // intelij bug requires assertion\n\n    // already verified that the first production is valid, we now seek the 2nd production\n    this.ruleStack.pop();\n    this.occurrenceStack.pop();\n\n    this.updateExpectedNext();\n    this.walk(this.topProd);\n\n    return this.possibleTokTypes;\n  }\n\n  walk(\n    prod: { definition: IProduction[] },\n    prevRest: IProduction[] = [],\n  ): void {\n    // stop scanning once we found the path\n    if (!this.found) {\n      super.walk(prod, prevRest);\n    }\n  }\n\n  walkProdRef(\n    refProd: NonTerminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // found the next production, need to keep walking in it\n    if (\n      refProd.referencedRule.name === this.nextProductionName &&\n      refProd.idx === this.nextProductionOccurrence\n    ) {\n      const fullRest = currRest.concat(prevRest);\n      this.updateExpectedNext();\n      this.walk(refProd.referencedRule, <any>fullRest);\n    }\n  }\n\n  updateExpectedNext(): void {\n    // need to consume the Terminal\n    if (isEmpty(this.ruleStack)) {\n      // must reset nextProductionXXX to avoid walking down another Top Level production while what we are\n      // really seeking is the last Terminal...\n      this.nextProductionName = \"\";\n      this.nextProductionOccurrence = 0;\n      this.isAtEndOfPath = true;\n    } else {\n      this.nextProductionName = this.ruleStack.pop()!;\n      this.nextProductionOccurrence = this.occurrenceStack.pop()!;\n    }\n  }\n}\n\nexport class NextAfterTokenWalker extends AbstractNextPossibleTokensWalker {\n  private nextTerminalName = \"\";\n  private nextTerminalOccurrence = 0;\n\n  constructor(\n    topProd: Rule,\n    protected path: ITokenGrammarPath,\n  ) {\n    super(topProd, path);\n    this.nextTerminalName = this.path.lastTok.name;\n    this.nextTerminalOccurrence = this.path.lastTokOccurrence;\n  }\n\n  walkTerminal(\n    terminal: Terminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (\n      this.isAtEndOfPath &&\n      terminal.terminalType.name === this.nextTerminalName &&\n      terminal.idx === this.nextTerminalOccurrence &&\n      !this.found\n    ) {\n      const fullRest = currRest.concat(prevRest);\n      const restProd = new Alternative({ definition: fullRest });\n      this.possibleTokTypes = first(restProd);\n      this.found = true;\n    }\n  }\n}\n\nexport type AlternativesFirstTokens = TokenType[][];\n\nexport interface IFirstAfterRepetition {\n  token: TokenType | undefined;\n  occurrence: number | undefined;\n  isEndOfRule: boolean | undefined;\n}\n\n/**\n * This walker only \"walks\" a single \"TOP\" level in the Grammar Ast, this means\n * it never \"follows\" production refs\n */\nexport class AbstractNextTerminalAfterProductionWalker extends RestWalker {\n  protected result: IFirstAfterRepetition = {\n    token: undefined,\n    occurrence: undefined,\n    isEndOfRule: undefined,\n  };\n\n  constructor(\n    protected topRule: Rule,\n    protected occurrence: number,\n  ) {\n    super();\n  }\n\n  startWalking(): IFirstAfterRepetition {\n    this.walk(this.topRule);\n    return this.result;\n  }\n}\n\nexport class NextTerminalAfterManyWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkMany(\n    manyProd: Repetition,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (manyProd.idx === this.occurrence) {\n      const firstAfterMany = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterMany === undefined;\n      if (firstAfterMany instanceof Terminal) {\n        this.result.token = firstAfterMany.terminalType;\n        this.result.occurrence = firstAfterMany.idx;\n      }\n    } else {\n      super.walkMany(manyProd, currRest, prevRest);\n    }\n  }\n}\n\nexport class NextTerminalAfterManySepWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkManySep(\n    manySepProd: RepetitionWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (manySepProd.idx === this.occurrence) {\n      const firstAfterManySep = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterManySep === undefined;\n      if (firstAfterManySep instanceof Terminal) {\n        this.result.token = firstAfterManySep.terminalType;\n        this.result.occurrence = firstAfterManySep.idx;\n      }\n    } else {\n      super.walkManySep(manySepProd, currRest, prevRest);\n    }\n  }\n}\n\nexport class NextTerminalAfterAtLeastOneWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkAtLeastOne(\n    atLeastOneProd: RepetitionMandatory,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (atLeastOneProd.idx === this.occurrence) {\n      const firstAfterAtLeastOne = _first(currRest.concat(prevRest));\n      this.result.isEndOfRule = firstAfterAtLeastOne === undefined;\n      if (firstAfterAtLeastOne instanceof Terminal) {\n        this.result.token = firstAfterAtLeastOne.terminalType;\n        this.result.occurrence = firstAfterAtLeastOne.idx;\n      }\n    } else {\n      super.walkAtLeastOne(atLeastOneProd, currRest, prevRest);\n    }\n  }\n}\n\n// TODO: reduce code duplication in the AfterWalkers\nexport class NextTerminalAfterAtLeastOneSepWalker extends AbstractNextTerminalAfterProductionWalker {\n  walkAtLeastOneSep(\n    atleastOneSepProd: RepetitionMandatoryWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    if (atleastOneSepProd.idx === this.occurrence) {\n      const firstAfterfirstAfterAtLeastOneSep = _first(\n        currRest.concat(prevRest),\n      );\n      this.result.isEndOfRule = firstAfterfirstAfterAtLeastOneSep === undefined;\n      if (firstAfterfirstAfterAtLeastOneSep instanceof Terminal) {\n        this.result.token = firstAfterfirstAfterAtLeastOneSep.terminalType;\n        this.result.occurrence = firstAfterfirstAfterAtLeastOneSep.idx;\n      }\n    } else {\n      super.walkAtLeastOneSep(atleastOneSepProd, currRest, prevRest);\n    }\n  }\n}\n\nexport interface PartialPathAndSuffixes {\n  partialPath: TokenType[];\n  suffixDef: IProduction[];\n}\n\nexport function possiblePathsFrom(\n  targetDef: IProduction[],\n  maxLength: number,\n  currPath: TokenType[] = [],\n): PartialPathAndSuffixes[] {\n  // avoid side effects\n  currPath = clone(currPath);\n  let result: PartialPathAndSuffixes[] = [];\n  let i = 0;\n\n  // TODO: avoid inner funcs\n  function remainingPathWith(nextDef: IProduction[]) {\n    return nextDef.concat(drop(targetDef, i + 1));\n  }\n\n  // TODO: avoid inner funcs\n  function getAlternativesForProd(definition: IProduction[]) {\n    const alternatives = possiblePathsFrom(\n      remainingPathWith(definition),\n      maxLength,\n      currPath,\n    );\n    return result.concat(alternatives);\n  }\n\n  /**\n   * Mandatory productions will halt the loop as the paths computed from their recursive calls will already contain the\n   * following (rest) of the targetDef.\n   *\n   * For optional productions (Option/Repetition/...) the loop will continue to represent the paths that do not include the\n   * the optional production.\n   */\n  while (currPath.length < maxLength && i < targetDef.length) {\n    const prod = targetDef[i];\n\n    /* istanbul ignore else */\n    if (prod instanceof Alternative) {\n      return getAlternativesForProd(prod.definition);\n    } else if (prod instanceof NonTerminal) {\n      return getAlternativesForProd(prod.definition);\n    } else if (prod instanceof Option) {\n      result = getAlternativesForProd(prod.definition);\n    } else if (prod instanceof RepetitionMandatory) {\n      const newDef = prod.definition.concat([\n        new Repetition({\n          definition: prod.definition,\n        }),\n      ]);\n      return getAlternativesForProd(newDef);\n    } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n      const newDef = [\n        new Alternative({ definition: prod.definition }),\n        new Repetition({\n          definition: [new Terminal({ terminalType: prod.separator })].concat(\n            <any>prod.definition,\n          ),\n        }),\n      ];\n      return getAlternativesForProd(newDef);\n    } else if (prod instanceof RepetitionWithSeparator) {\n      const newDef = prod.definition.concat([\n        new Repetition({\n          definition: [new Terminal({ terminalType: prod.separator })].concat(\n            <any>prod.definition,\n          ),\n        }),\n      ]);\n      result = getAlternativesForProd(newDef);\n    } else if (prod instanceof Repetition) {\n      const newDef = prod.definition.concat([\n        new Repetition({\n          definition: prod.definition,\n        }),\n      ]);\n      result = getAlternativesForProd(newDef);\n    } else if (prod instanceof Alternation) {\n      forEach(prod.definition, (currAlt) => {\n        // TODO: this is a limited check for empty alternatives\n        //   It would prevent a common case of infinite loops during parser initialization.\n        //   However **in-directly** empty alternatives may still cause issues.\n        if (isEmpty(currAlt.definition) === false) {\n          result = getAlternativesForProd(currAlt.definition);\n        }\n      });\n      return result;\n    } else if (prod instanceof Terminal) {\n      currPath.push(prod.terminalType);\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n\n    i++;\n  }\n  result.push({\n    partialPath: currPath,\n    suffixDef: drop(targetDef, i),\n  });\n\n  return result;\n}\n\ninterface IPathToExamine {\n  idx: number;\n  def: IProduction[];\n  ruleStack: string[];\n  occurrenceStack: number[];\n}\n\nexport function nextPossibleTokensAfter(\n  initialDef: IProduction[],\n  tokenVector: IToken[],\n  tokMatcher: TokenMatcher,\n  maxLookAhead: number,\n): ISyntacticContentAssistPath[] {\n  const EXIT_NON_TERMINAL: any = \"EXIT_NONE_TERMINAL\";\n  // to avoid creating a new Array each time.\n  const EXIT_NON_TERMINAL_ARR = [EXIT_NON_TERMINAL];\n  const EXIT_ALTERNATIVE: any = \"EXIT_ALTERNATIVE\";\n  let foundCompletePath = false;\n\n  const tokenVectorLength = tokenVector.length;\n  const minimalAlternativesIndex = tokenVectorLength - maxLookAhead - 1;\n\n  const result: ISyntacticContentAssistPath[] = [];\n\n  const possiblePaths: IPathToExamine[] = [];\n  possiblePaths.push({\n    idx: -1,\n    def: initialDef,\n    ruleStack: [],\n    occurrenceStack: [],\n  });\n\n  while (!isEmpty(possiblePaths)) {\n    const currPath = possiblePaths.pop()!;\n\n    // skip alternatives if no more results can be found (assuming deterministic grammar with fixed lookahead)\n    if (currPath === EXIT_ALTERNATIVE) {\n      if (\n        foundCompletePath &&\n        last(possiblePaths)!.idx <= minimalAlternativesIndex\n      ) {\n        // remove irrelevant alternative\n        possiblePaths.pop();\n      }\n      continue;\n    }\n\n    const currDef = currPath.def;\n    const currIdx = currPath.idx;\n    const currRuleStack = currPath.ruleStack;\n    const currOccurrenceStack = currPath.occurrenceStack;\n\n    // For Example: an empty path could exist in a valid grammar in the case of an EMPTY_ALT\n    if (isEmpty(currDef)) {\n      continue;\n    }\n\n    const prod = currDef[0];\n    /* istanbul ignore else */\n    if (prod === EXIT_NON_TERMINAL) {\n      const nextPath = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: dropRight(currRuleStack),\n        occurrenceStack: dropRight(currOccurrenceStack),\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof Terminal) {\n      /* istanbul ignore else */\n      if (currIdx < tokenVectorLength - 1) {\n        const nextIdx = currIdx + 1;\n        const actualToken = tokenVector[nextIdx];\n        if (tokMatcher!(actualToken, prod.terminalType)) {\n          const nextPath = {\n            idx: nextIdx,\n            def: drop(currDef),\n            ruleStack: currRuleStack,\n            occurrenceStack: currOccurrenceStack,\n          };\n          possiblePaths.push(nextPath);\n        }\n        // end of the line\n      } else if (currIdx === tokenVectorLength - 1) {\n        // IGNORE ABOVE ELSE\n        result.push({\n          nextTokenType: prod.terminalType,\n          nextTokenOccurrence: prod.idx,\n          ruleStack: currRuleStack,\n          occurrenceStack: currOccurrenceStack,\n        });\n        foundCompletePath = true;\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    } else if (prod instanceof NonTerminal) {\n      const newRuleStack = clone(currRuleStack);\n      newRuleStack.push(prod.nonTerminalName);\n\n      const newOccurrenceStack = clone(currOccurrenceStack);\n      newOccurrenceStack.push(prod.idx);\n\n      const nextPath = {\n        idx: currIdx,\n        def: prod.definition.concat(EXIT_NON_TERMINAL_ARR, drop(currDef)),\n        ruleStack: newRuleStack,\n        occurrenceStack: newOccurrenceStack,\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof Option) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n\n      const nextPathWith = {\n        idx: currIdx,\n        def: prod.definition.concat(drop(currDef)),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof RepetitionMandatory) {\n      // TODO:(THE NEW operators here take a while...) (convert once?)\n      const secondIteration = new Repetition({\n        definition: prod.definition,\n        idx: prod.idx,\n      });\n      const nextDef = prod.definition.concat([secondIteration], drop(currDef));\n      const nextPath = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n      // TODO:(THE NEW operators here take a while...) (convert once?)\n      const separatorGast = new Terminal({\n        terminalType: prod.separator,\n      });\n      const secondIteration = new Repetition({\n        definition: [<any>separatorGast].concat(prod.definition),\n        idx: prod.idx,\n      });\n      const nextDef = prod.definition.concat([secondIteration], drop(currDef));\n      const nextPath = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPath);\n    } else if (prod instanceof RepetitionWithSeparator) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n\n      const separatorGast = new Terminal({\n        terminalType: prod.separator,\n      });\n      const nthRepetition = new Repetition({\n        definition: [<any>separatorGast].concat(prod.definition),\n        idx: prod.idx,\n      });\n      const nextDef = prod.definition.concat([nthRepetition], drop(currDef));\n      const nextPathWith = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof Repetition) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      const nextPathWithout = {\n        idx: currIdx,\n        def: drop(currDef),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWithout);\n      // required marker to avoid backtracking paths whose higher priority alternatives already matched\n      possiblePaths.push(EXIT_ALTERNATIVE);\n\n      // TODO: an empty repetition will cause infinite loops here, will the parser detect this in selfAnalysis?\n      const nthRepetition = new Repetition({\n        definition: prod.definition,\n        idx: prod.idx,\n      });\n      const nextDef = prod.definition.concat([nthRepetition], drop(currDef));\n      const nextPathWith = {\n        idx: currIdx,\n        def: nextDef,\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      };\n      possiblePaths.push(nextPathWith);\n    } else if (prod instanceof Alternation) {\n      // the order of alternatives is meaningful, FILO (Last path will be traversed first).\n      for (let i = prod.definition.length - 1; i >= 0; i--) {\n        const currAlt: any = prod.definition[i];\n        const currAltPath = {\n          idx: currIdx,\n          def: currAlt.definition.concat(drop(currDef)),\n          ruleStack: currRuleStack,\n          occurrenceStack: currOccurrenceStack,\n        };\n        possiblePaths.push(currAltPath);\n        possiblePaths.push(EXIT_ALTERNATIVE);\n      }\n    } else if (prod instanceof Alternative) {\n      possiblePaths.push({\n        idx: currIdx,\n        def: prod.definition.concat(drop(currDef)),\n        ruleStack: currRuleStack,\n        occurrenceStack: currOccurrenceStack,\n      });\n    } else if (prod instanceof Rule) {\n      // last because we should only encounter at most a single one of these per invocation.\n      possiblePaths.push(\n        expandTopLevelRule(prod, currIdx, currRuleStack, currOccurrenceStack),\n      );\n    } else {\n      throw Error(\"non exhaustive match\");\n    }\n  }\n  return result;\n}\n\nfunction expandTopLevelRule(\n  topRule: Rule,\n  currIdx: number,\n  currRuleStack: string[],\n  currOccurrenceStack: number[],\n): IPathToExamine {\n  const newRuleStack = clone(currRuleStack);\n  newRuleStack.push(topRule.name);\n\n  const newCurrOccurrenceStack = clone(currOccurrenceStack);\n  // top rule is always assumed to have been called with occurrence index 1\n  newCurrOccurrenceStack.push(1);\n\n  return {\n    idx: currIdx,\n    def: topRule.definition,\n    ruleStack: newRuleStack,\n    occurrenceStack: newCurrOccurrenceStack,\n  };\n}\n"], "mappings": "AAAA,SACEA,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,KAAK,IAAIC,MAAM,EACfC,OAAO,EACPC,OAAO,EACPC,IAAI,QACC,WAAW;AAClB,SAASJ,KAAK,QAAQ,YAAY;AAClC,SAASK,UAAU,QAAQ,WAAW;AAEtC,SACEC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,IAAI,EACJC,QAAQ,QACH,kBAAkB;AAUzB,OAAM,MAAgBC,gCAAiC,SAAQX,UAAU;EAUvEY,YACYC,OAAa,EACbC,IAAkB;IAE5B,KAAK,EAAE;IAHG,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAC,IAAI,GAAJA,IAAI;IAXN,KAAAC,gBAAgB,GAAgB,EAAE;IAIlC,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,wBAAwB,GAAG,CAAC;IAC5B,KAAAC,KAAK,GAAG,KAAK;IACb,KAAAC,aAAa,GAAG,KAAK;EAO/B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACF,KAAK,GAAG,KAAK;IAElB,IAAI,IAAI,CAACJ,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAACR,OAAO,CAACS,IAAI,EAAE;MAChD,MAAMC,KAAK,CAAC,qDAAqD,CAAC;;IAGpE;IACA,IAAI,CAACF,SAAS,GAAG7B,KAAK,CAAC,IAAI,CAACsB,IAAI,CAACO,SAAS,CAAC,CAACG,OAAO,EAAE,CAAC,CAAC;IACvD,IAAI,CAACC,eAAe,GAAGjC,KAAK,CAAC,IAAI,CAACsB,IAAI,CAACW,eAAe,CAAC,CAACD,OAAO,EAAE,CAAC,CAAC;IAEnE;IACA,IAAI,CAACH,SAAS,CAACK,GAAG,EAAE;IACpB,IAAI,CAACD,eAAe,CAACC,GAAG,EAAE;IAE1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACf,OAAO,CAAC;IAEvB,OAAO,IAAI,CAACE,gBAAgB;EAC9B;EAEAa,IAAIA,CACFC,IAAmC,EACnCC,QAAA,GAA0B,EAAE;IAE5B;IACA,IAAI,CAAC,IAAI,CAACZ,KAAK,EAAE;MACf,KAAK,CAACU,IAAI,CAACC,IAAI,EAAEC,QAAQ,CAAC;;EAE9B;EAEAC,WAAWA,CACTC,OAAoB,EACpBC,QAAuB,EACvBH,QAAuB;IAEvB;IACA,IACEE,OAAO,CAACE,cAAc,CAACZ,IAAI,KAAK,IAAI,CAACN,kBAAkB,IACvDgB,OAAO,CAACG,GAAG,KAAK,IAAI,CAAClB,wBAAwB,EAC7C;MACA,MAAMmB,QAAQ,GAAGH,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC;MAC1C,IAAI,CAACH,kBAAkB,EAAE;MACzB,IAAI,CAACC,IAAI,CAACI,OAAO,CAACE,cAAc,EAAOE,QAAQ,CAAC;;EAEpD;EAEAT,kBAAkBA,CAAA;IAChB;IACA,IAAI7B,OAAO,CAAC,IAAI,CAACuB,SAAS,CAAC,EAAE;MAC3B;MACA;MACA,IAAI,CAACL,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACC,wBAAwB,GAAG,CAAC;MACjC,IAAI,CAACE,aAAa,GAAG,IAAI;KAC1B,MAAM;MACL,IAAI,CAACH,kBAAkB,GAAG,IAAI,CAACK,SAAS,CAACK,GAAG,EAAG;MAC/C,IAAI,CAACT,wBAAwB,GAAG,IAAI,CAACQ,eAAe,CAACC,GAAG,EAAG;;EAE/D;;AAGF,OAAM,MAAOY,oBAAqB,SAAQ3B,gCAAgC;EAIxEC,YACEC,OAAa,EACHC,IAAuB;IAEjC,KAAK,CAACD,OAAO,EAAEC,IAAI,CAAC;IAFV,KAAAA,IAAI,GAAJA,IAAI;IALR,KAAAyB,gBAAgB,GAAG,EAAE;IACrB,KAAAC,sBAAsB,GAAG,CAAC;IAOhC,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACzB,IAAI,CAAC2B,OAAO,CAACnB,IAAI;IAC9C,IAAI,CAACkB,sBAAsB,GAAG,IAAI,CAAC1B,IAAI,CAAC4B,iBAAiB;EAC3D;EAEAC,YAAYA,CACVC,QAAkB,EAClBX,QAAuB,EACvBH,QAAuB;IAEvB,IACE,IAAI,CAACX,aAAa,IAClByB,QAAQ,CAACC,YAAY,CAACvB,IAAI,KAAK,IAAI,CAACiB,gBAAgB,IACpDK,QAAQ,CAACT,GAAG,KAAK,IAAI,CAACK,sBAAsB,IAC5C,CAAC,IAAI,CAACtB,KAAK,EACX;MACA,MAAMkB,QAAQ,GAAGH,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC;MAC1C,MAAMgB,QAAQ,GAAG,IAAI5C,WAAW,CAAC;QAAE6C,UAAU,EAAEX;MAAQ,CAAE,CAAC;MAC1D,IAAI,CAACrB,gBAAgB,GAAGpB,KAAK,CAACmD,QAAQ,CAAC;MACvC,IAAI,CAAC5B,KAAK,GAAG,IAAI;;EAErB;;AAWF;;;;AAIA,OAAM,MAAO8B,yCAA0C,SAAQhD,UAAU;EAOvEY,YACYqC,OAAa,EACbC,UAAkB;IAE5B,KAAK,EAAE;IAHG,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IARZ,KAAAC,MAAM,GAA0B;MACxCC,KAAK,EAAEC,SAAS;MAChBH,UAAU,EAAEG,SAAS;MACrBC,WAAW,EAAED;KACd;EAOD;EAEAjC,YAAYA,CAAA;IACV,IAAI,CAACQ,IAAI,CAAC,IAAI,CAACqB,OAAO,CAAC;IACvB,OAAO,IAAI,CAACE,MAAM;EACpB;;AAGF,OAAM,MAAOI,2BAA4B,SAAQP,yCAAyC;EACxFQ,QAAQA,CACNC,QAAoB,EACpBxB,QAAuB,EACvBH,QAAuB;IAEvB,IAAI2B,QAAQ,CAACtB,GAAG,KAAK,IAAI,CAACe,UAAU,EAAE;MACpC,MAAMQ,cAAc,GAAG9D,MAAM,CAACqC,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC,CAAC;MACxD,IAAI,CAACqB,MAAM,CAACG,WAAW,GAAGI,cAAc,KAAKL,SAAS;MACtD,IAAIK,cAAc,YAAYhD,QAAQ,EAAE;QACtC,IAAI,CAACyC,MAAM,CAACC,KAAK,GAAGM,cAAc,CAACb,YAAY;QAC/C,IAAI,CAACM,MAAM,CAACD,UAAU,GAAGQ,cAAc,CAACvB,GAAG;;KAE9C,MAAM;MACL,KAAK,CAACqB,QAAQ,CAACC,QAAQ,EAAExB,QAAQ,EAAEH,QAAQ,CAAC;;EAEhD;;AAGF,OAAM,MAAO6B,8BAA+B,SAAQX,yCAAyC;EAC3FY,WAAWA,CACTC,WAAoC,EACpC5B,QAAuB,EACvBH,QAAuB;IAEvB,IAAI+B,WAAW,CAAC1B,GAAG,KAAK,IAAI,CAACe,UAAU,EAAE;MACvC,MAAMY,iBAAiB,GAAGlE,MAAM,CAACqC,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC,CAAC;MAC3D,IAAI,CAACqB,MAAM,CAACG,WAAW,GAAGQ,iBAAiB,KAAKT,SAAS;MACzD,IAAIS,iBAAiB,YAAYpD,QAAQ,EAAE;QACzC,IAAI,CAACyC,MAAM,CAACC,KAAK,GAAGU,iBAAiB,CAACjB,YAAY;QAClD,IAAI,CAACM,MAAM,CAACD,UAAU,GAAGY,iBAAiB,CAAC3B,GAAG;;KAEjD,MAAM;MACL,KAAK,CAACyB,WAAW,CAACC,WAAW,EAAE5B,QAAQ,EAAEH,QAAQ,CAAC;;EAEtD;;AAGF,OAAM,MAAOiC,iCAAkC,SAAQf,yCAAyC;EAC9FgB,cAAcA,CACZC,cAAmC,EACnChC,QAAuB,EACvBH,QAAuB;IAEvB,IAAImC,cAAc,CAAC9B,GAAG,KAAK,IAAI,CAACe,UAAU,EAAE;MAC1C,MAAMgB,oBAAoB,GAAGtE,MAAM,CAACqC,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC,CAAC;MAC9D,IAAI,CAACqB,MAAM,CAACG,WAAW,GAAGY,oBAAoB,KAAKb,SAAS;MAC5D,IAAIa,oBAAoB,YAAYxD,QAAQ,EAAE;QAC5C,IAAI,CAACyC,MAAM,CAACC,KAAK,GAAGc,oBAAoB,CAACrB,YAAY;QACrD,IAAI,CAACM,MAAM,CAACD,UAAU,GAAGgB,oBAAoB,CAAC/B,GAAG;;KAEpD,MAAM;MACL,KAAK,CAAC6B,cAAc,CAACC,cAAc,EAAEhC,QAAQ,EAAEH,QAAQ,CAAC;;EAE5D;;AAGF;AACA,OAAM,MAAOqC,oCAAqC,SAAQnB,yCAAyC;EACjGoB,iBAAiBA,CACfC,iBAAmD,EACnDpC,QAAuB,EACvBH,QAAuB;IAEvB,IAAIuC,iBAAiB,CAAClC,GAAG,KAAK,IAAI,CAACe,UAAU,EAAE;MAC7C,MAAMoB,iCAAiC,GAAG1E,MAAM,CAC9CqC,QAAQ,CAACI,MAAM,CAACP,QAAQ,CAAC,CAC1B;MACD,IAAI,CAACqB,MAAM,CAACG,WAAW,GAAGgB,iCAAiC,KAAKjB,SAAS;MACzE,IAAIiB,iCAAiC,YAAY5D,QAAQ,EAAE;QACzD,IAAI,CAACyC,MAAM,CAACC,KAAK,GAAGkB,iCAAiC,CAACzB,YAAY;QAClE,IAAI,CAACM,MAAM,CAACD,UAAU,GAAGoB,iCAAiC,CAACnC,GAAG;;KAEjE,MAAM;MACL,KAAK,CAACiC,iBAAiB,CAACC,iBAAiB,EAAEpC,QAAQ,EAAEH,QAAQ,CAAC;;EAElE;;AAQF,OAAM,SAAUyC,iBAAiBA,CAC/BC,SAAwB,EACxBC,SAAiB,EACjBC,QAAA,GAAwB,EAAE;EAE1B;EACAA,QAAQ,GAAGlF,KAAK,CAACkF,QAAQ,CAAC;EAC1B,IAAIvB,MAAM,GAA6B,EAAE;EACzC,IAAIwB,CAAC,GAAG,CAAC;EAET;EACA,SAASC,iBAAiBA,CAACC,OAAsB;IAC/C,OAAOA,OAAO,CAACxC,MAAM,CAAC5C,IAAI,CAAC+E,SAAS,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C;EAEA;EACA,SAASG,sBAAsBA,CAAC/B,UAAyB;IACvD,MAAMgC,YAAY,GAAGR,iBAAiB,CACpCK,iBAAiB,CAAC7B,UAAU,CAAC,EAC7B0B,SAAS,EACTC,QAAQ,CACT;IACD,OAAOvB,MAAM,CAACd,MAAM,CAAC0C,YAAY,CAAC;EACpC;EAEA;;;;;;;EAOA,OAAOL,QAAQ,CAACM,MAAM,GAAGP,SAAS,IAAIE,CAAC,GAAGH,SAAS,CAACQ,MAAM,EAAE;IAC1D,MAAMnD,IAAI,GAAG2C,SAAS,CAACG,CAAC,CAAC;IAEzB;IACA,IAAI9C,IAAI,YAAY3B,WAAW,EAAE;MAC/B,OAAO4E,sBAAsB,CAACjD,IAAI,CAACkB,UAAU,CAAC;KAC/C,MAAM,IAAIlB,IAAI,YAAY1B,WAAW,EAAE;MACtC,OAAO2E,sBAAsB,CAACjD,IAAI,CAACkB,UAAU,CAAC;KAC/C,MAAM,IAAIlB,IAAI,YAAYzB,MAAM,EAAE;MACjC+C,MAAM,GAAG2B,sBAAsB,CAACjD,IAAI,CAACkB,UAAU,CAAC;KACjD,MAAM,IAAIlB,IAAI,YAAYvB,mBAAmB,EAAE;MAC9C,MAAM2E,MAAM,GAAGpD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CACpC,IAAIhC,UAAU,CAAC;QACb0C,UAAU,EAAElB,IAAI,CAACkB;OAClB,CAAC,CACH,CAAC;MACF,OAAO+B,sBAAsB,CAACG,MAAM,CAAC;KACtC,MAAM,IAAIpD,IAAI,YAAYtB,gCAAgC,EAAE;MAC3D,MAAM0E,MAAM,GAAG,CACb,IAAI/E,WAAW,CAAC;QAAE6C,UAAU,EAAElB,IAAI,CAACkB;MAAU,CAAE,CAAC,EAChD,IAAI1C,UAAU,CAAC;QACb0C,UAAU,EAAE,CAAC,IAAIrC,QAAQ,CAAC;UAAEmC,YAAY,EAAEhB,IAAI,CAACqD;QAAS,CAAE,CAAC,CAAC,CAAC7C,MAAM,CAC5DR,IAAI,CAACkB,UAAU;OAEvB,CAAC,CACH;MACD,OAAO+B,sBAAsB,CAACG,MAAM,CAAC;KACtC,MAAM,IAAIpD,IAAI,YAAYrB,uBAAuB,EAAE;MAClD,MAAMyE,MAAM,GAAGpD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CACpC,IAAIhC,UAAU,CAAC;QACb0C,UAAU,EAAE,CAAC,IAAIrC,QAAQ,CAAC;UAAEmC,YAAY,EAAEhB,IAAI,CAACqD;QAAS,CAAE,CAAC,CAAC,CAAC7C,MAAM,CAC5DR,IAAI,CAACkB,UAAU;OAEvB,CAAC,CACH,CAAC;MACFI,MAAM,GAAG2B,sBAAsB,CAACG,MAAM,CAAC;KACxC,MAAM,IAAIpD,IAAI,YAAYxB,UAAU,EAAE;MACrC,MAAM4E,MAAM,GAAGpD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CACpC,IAAIhC,UAAU,CAAC;QACb0C,UAAU,EAAElB,IAAI,CAACkB;OAClB,CAAC,CACH,CAAC;MACFI,MAAM,GAAG2B,sBAAsB,CAACG,MAAM,CAAC;KACxC,MAAM,IAAIpD,IAAI,YAAY5B,WAAW,EAAE;MACtCJ,OAAO,CAACgC,IAAI,CAACkB,UAAU,EAAGoC,OAAO,IAAI;QACnC;QACA;QACA;QACA,IAAIrF,OAAO,CAACqF,OAAO,CAACpC,UAAU,CAAC,KAAK,KAAK,EAAE;UACzCI,MAAM,GAAG2B,sBAAsB,CAACK,OAAO,CAACpC,UAAU,CAAC;;MAEvD,CAAC,CAAC;MACF,OAAOI,MAAM;KACd,MAAM,IAAItB,IAAI,YAAYnB,QAAQ,EAAE;MACnCgE,QAAQ,CAACU,IAAI,CAACvD,IAAI,CAACgB,YAAY,CAAC;KACjC,MAAM;MACL,MAAMtB,KAAK,CAAC,sBAAsB,CAAC;;IAGrCoD,CAAC,EAAE;;EAELxB,MAAM,CAACiC,IAAI,CAAC;IACVC,WAAW,EAAEX,QAAQ;IACrBY,SAAS,EAAE7F,IAAI,CAAC+E,SAAS,EAAEG,CAAC;GAC7B,CAAC;EAEF,OAAOxB,MAAM;AACf;AASA,OAAM,SAAUoC,uBAAuBA,CACrCC,UAAyB,EACzBC,WAAqB,EACrBC,UAAwB,EACxBC,YAAoB;EAEpB,MAAMC,iBAAiB,GAAQ,oBAAoB;EACnD;EACA,MAAMC,qBAAqB,GAAG,CAACD,iBAAiB,CAAC;EACjD,MAAME,gBAAgB,GAAQ,kBAAkB;EAChD,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,MAAMC,iBAAiB,GAAGP,WAAW,CAACT,MAAM;EAC5C,MAAMiB,wBAAwB,GAAGD,iBAAiB,GAAGL,YAAY,GAAG,CAAC;EAErE,MAAMxC,MAAM,GAAkC,EAAE;EAEhD,MAAM+C,aAAa,GAAqB,EAAE;EAC1CA,aAAa,CAACd,IAAI,CAAC;IACjBjD,GAAG,EAAE,CAAC,CAAC;IACPgE,GAAG,EAAEX,UAAU;IACfnE,SAAS,EAAE,EAAE;IACbI,eAAe,EAAE;GAClB,CAAC;EAEF,OAAO,CAAC3B,OAAO,CAACoG,aAAa,CAAC,EAAE;IAC9B,MAAMxB,QAAQ,GAAGwB,aAAa,CAACxE,GAAG,EAAG;IAErC;IACA,IAAIgD,QAAQ,KAAKoB,gBAAgB,EAAE;MACjC,IACEC,iBAAiB,IACjBhG,IAAI,CAACmG,aAAa,CAAE,CAAC/D,GAAG,IAAI8D,wBAAwB,EACpD;QACA;QACAC,aAAa,CAACxE,GAAG,EAAE;;MAErB;;IAGF,MAAM0E,OAAO,GAAG1B,QAAQ,CAACyB,GAAG;IAC5B,MAAME,OAAO,GAAG3B,QAAQ,CAACvC,GAAG;IAC5B,MAAMmE,aAAa,GAAG5B,QAAQ,CAACrD,SAAS;IACxC,MAAMkF,mBAAmB,GAAG7B,QAAQ,CAACjD,eAAe;IAEpD;IACA,IAAI3B,OAAO,CAACsG,OAAO,CAAC,EAAE;MACpB;;IAGF,MAAMvE,IAAI,GAAGuE,OAAO,CAAC,CAAC,CAAC;IACvB;IACA,IAAIvE,IAAI,KAAK+D,iBAAiB,EAAE;MAC9B,MAAMY,QAAQ,GAAG;QACfrE,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAE1G,IAAI,CAAC2G,OAAO,CAAC;QAClB/E,SAAS,EAAE3B,SAAS,CAAC4G,aAAa,CAAC;QACnC7E,eAAe,EAAE/B,SAAS,CAAC6G,mBAAmB;OAC/C;MACDL,aAAa,CAACd,IAAI,CAACoB,QAAQ,CAAC;KAC7B,MAAM,IAAI3E,IAAI,YAAYnB,QAAQ,EAAE;MACnC;MACA,IAAI2F,OAAO,GAAGL,iBAAiB,GAAG,CAAC,EAAE;QACnC,MAAMS,OAAO,GAAGJ,OAAO,GAAG,CAAC;QAC3B,MAAMK,WAAW,GAAGjB,WAAW,CAACgB,OAAO,CAAC;QACxC,IAAIf,UAAW,CAACgB,WAAW,EAAE7E,IAAI,CAACgB,YAAY,CAAC,EAAE;UAC/C,MAAM2D,QAAQ,GAAG;YACfrE,GAAG,EAAEsE,OAAO;YACZN,GAAG,EAAE1G,IAAI,CAAC2G,OAAO,CAAC;YAClB/E,SAAS,EAAEiF,aAAa;YACxB7E,eAAe,EAAE8E;WAClB;UACDL,aAAa,CAACd,IAAI,CAACoB,QAAQ,CAAC;;QAE9B;OACD,MAAM,IAAIH,OAAO,KAAKL,iBAAiB,GAAG,CAAC,EAAE;QAC5C;QACA7C,MAAM,CAACiC,IAAI,CAAC;UACVuB,aAAa,EAAE9E,IAAI,CAACgB,YAAY;UAChC+D,mBAAmB,EAAE/E,IAAI,CAACM,GAAG;UAC7Bd,SAAS,EAAEiF,aAAa;UACxB7E,eAAe,EAAE8E;SAClB,CAAC;QACFR,iBAAiB,GAAG,IAAI;OACzB,MAAM;QACL,MAAMxE,KAAK,CAAC,sBAAsB,CAAC;;KAEtC,MAAM,IAAIM,IAAI,YAAY1B,WAAW,EAAE;MACtC,MAAM0G,YAAY,GAAGrH,KAAK,CAAC8G,aAAa,CAAC;MACzCO,YAAY,CAACzB,IAAI,CAACvD,IAAI,CAACiF,eAAe,CAAC;MAEvC,MAAMC,kBAAkB,GAAGvH,KAAK,CAAC+G,mBAAmB,CAAC;MACrDQ,kBAAkB,CAAC3B,IAAI,CAACvD,IAAI,CAACM,GAAG,CAAC;MAEjC,MAAMqE,QAAQ,GAAG;QACfrE,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtE,IAAI,CAACkB,UAAU,CAACV,MAAM,CAACwD,qBAAqB,EAAEpG,IAAI,CAAC2G,OAAO,CAAC,CAAC;QACjE/E,SAAS,EAAEwF,YAAY;QACvBpF,eAAe,EAAEsF;OAClB;MACDb,aAAa,CAACd,IAAI,CAACoB,QAAQ,CAAC;KAC7B,MAAM,IAAI3E,IAAI,YAAYzB,MAAM,EAAE;MACjC;MACA,MAAM4G,eAAe,GAAG;QACtB7E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAE1G,IAAI,CAAC2G,OAAO,CAAC;QAClB/E,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC4B,eAAe,CAAC;MACnC;MACAd,aAAa,CAACd,IAAI,CAACU,gBAAgB,CAAC;MAEpC,MAAMmB,YAAY,GAAG;QACnB9E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtE,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC5C,IAAI,CAAC2G,OAAO,CAAC,CAAC;QAC1C/E,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC6B,YAAY,CAAC;KACjC,MAAM,IAAIpF,IAAI,YAAYvB,mBAAmB,EAAE;MAC9C;MACA,MAAM4G,eAAe,GAAG,IAAI7G,UAAU,CAAC;QACrC0C,UAAU,EAAElB,IAAI,CAACkB,UAAU;QAC3BZ,GAAG,EAAEN,IAAI,CAACM;OACX,CAAC;MACF,MAAM0C,OAAO,GAAGhD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CAAC6E,eAAe,CAAC,EAAEzH,IAAI,CAAC2G,OAAO,CAAC,CAAC;MACxE,MAAMI,QAAQ,GAAG;QACfrE,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtB,OAAO;QACZxD,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAACoB,QAAQ,CAAC;KAC7B,MAAM,IAAI3E,IAAI,YAAYtB,gCAAgC,EAAE;MAC3D;MACA,MAAM4G,aAAa,GAAG,IAAIzG,QAAQ,CAAC;QACjCmC,YAAY,EAAEhB,IAAI,CAACqD;OACpB,CAAC;MACF,MAAMgC,eAAe,GAAG,IAAI7G,UAAU,CAAC;QACrC0C,UAAU,EAAE,CAAMoE,aAAa,CAAC,CAAC9E,MAAM,CAACR,IAAI,CAACkB,UAAU,CAAC;QACxDZ,GAAG,EAAEN,IAAI,CAACM;OACX,CAAC;MACF,MAAM0C,OAAO,GAAGhD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CAAC6E,eAAe,CAAC,EAAEzH,IAAI,CAAC2G,OAAO,CAAC,CAAC;MACxE,MAAMI,QAAQ,GAAG;QACfrE,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtB,OAAO;QACZxD,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAACoB,QAAQ,CAAC;KAC7B,MAAM,IAAI3E,IAAI,YAAYrB,uBAAuB,EAAE;MAClD;MACA,MAAMwG,eAAe,GAAG;QACtB7E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAE1G,IAAI,CAAC2G,OAAO,CAAC;QAClB/E,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC4B,eAAe,CAAC;MACnC;MACAd,aAAa,CAACd,IAAI,CAACU,gBAAgB,CAAC;MAEpC,MAAMqB,aAAa,GAAG,IAAIzG,QAAQ,CAAC;QACjCmC,YAAY,EAAEhB,IAAI,CAACqD;OACpB,CAAC;MACF,MAAMkC,aAAa,GAAG,IAAI/G,UAAU,CAAC;QACnC0C,UAAU,EAAE,CAAMoE,aAAa,CAAC,CAAC9E,MAAM,CAACR,IAAI,CAACkB,UAAU,CAAC;QACxDZ,GAAG,EAAEN,IAAI,CAACM;OACX,CAAC;MACF,MAAM0C,OAAO,GAAGhD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CAAC+E,aAAa,CAAC,EAAE3H,IAAI,CAAC2G,OAAO,CAAC,CAAC;MACtE,MAAMa,YAAY,GAAG;QACnB9E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtB,OAAO;QACZxD,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC6B,YAAY,CAAC;KACjC,MAAM,IAAIpF,IAAI,YAAYxB,UAAU,EAAE;MACrC;MACA,MAAM2G,eAAe,GAAG;QACtB7E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAE1G,IAAI,CAAC2G,OAAO,CAAC;QAClB/E,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC4B,eAAe,CAAC;MACnC;MACAd,aAAa,CAACd,IAAI,CAACU,gBAAgB,CAAC;MAEpC;MACA,MAAMsB,aAAa,GAAG,IAAI/G,UAAU,CAAC;QACnC0C,UAAU,EAAElB,IAAI,CAACkB,UAAU;QAC3BZ,GAAG,EAAEN,IAAI,CAACM;OACX,CAAC;MACF,MAAM0C,OAAO,GAAGhD,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC,CAAC+E,aAAa,CAAC,EAAE3H,IAAI,CAAC2G,OAAO,CAAC,CAAC;MACtE,MAAMa,YAAY,GAAG;QACnB9E,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtB,OAAO;QACZxD,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB;MACDL,aAAa,CAACd,IAAI,CAAC6B,YAAY,CAAC;KACjC,MAAM,IAAIpF,IAAI,YAAY5B,WAAW,EAAE;MACtC;MACA,KAAK,IAAI0E,CAAC,GAAG9C,IAAI,CAACkB,UAAU,CAACiC,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpD,MAAMQ,OAAO,GAAQtD,IAAI,CAACkB,UAAU,CAAC4B,CAAC,CAAC;QACvC,MAAM0C,WAAW,GAAG;UAClBlF,GAAG,EAAEkE,OAAO;UACZF,GAAG,EAAEhB,OAAO,CAACpC,UAAU,CAACV,MAAM,CAAC5C,IAAI,CAAC2G,OAAO,CAAC,CAAC;UAC7C/E,SAAS,EAAEiF,aAAa;UACxB7E,eAAe,EAAE8E;SAClB;QACDL,aAAa,CAACd,IAAI,CAACiC,WAAW,CAAC;QAC/BnB,aAAa,CAACd,IAAI,CAACU,gBAAgB,CAAC;;KAEvC,MAAM,IAAIjE,IAAI,YAAY3B,WAAW,EAAE;MACtCgG,aAAa,CAACd,IAAI,CAAC;QACjBjD,GAAG,EAAEkE,OAAO;QACZF,GAAG,EAAEtE,IAAI,CAACkB,UAAU,CAACV,MAAM,CAAC5C,IAAI,CAAC2G,OAAO,CAAC,CAAC;QAC1C/E,SAAS,EAAEiF,aAAa;QACxB7E,eAAe,EAAE8E;OAClB,CAAC;KACH,MAAM,IAAI1E,IAAI,YAAYpB,IAAI,EAAE;MAC/B;MACAyF,aAAa,CAACd,IAAI,CAChBkC,kBAAkB,CAACzF,IAAI,EAAEwE,OAAO,EAAEC,aAAa,EAAEC,mBAAmB,CAAC,CACtE;KACF,MAAM;MACL,MAAMhF,KAAK,CAAC,sBAAsB,CAAC;;;EAGvC,OAAO4B,MAAM;AACf;AAEA,SAASmE,kBAAkBA,CACzBrE,OAAa,EACboD,OAAe,EACfC,aAAuB,EACvBC,mBAA6B;EAE7B,MAAMM,YAAY,GAAGrH,KAAK,CAAC8G,aAAa,CAAC;EACzCO,YAAY,CAACzB,IAAI,CAACnC,OAAO,CAAC3B,IAAI,CAAC;EAE/B,MAAMiG,sBAAsB,GAAG/H,KAAK,CAAC+G,mBAAmB,CAAC;EACzD;EACAgB,sBAAsB,CAACnC,IAAI,CAAC,CAAC,CAAC;EAE9B,OAAO;IACLjD,GAAG,EAAEkE,OAAO;IACZF,GAAG,EAAElD,OAAO,CAACF,UAAU;IACvB1B,SAAS,EAAEwF,YAAY;IACvBpF,eAAe,EAAE8F;GAClB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}