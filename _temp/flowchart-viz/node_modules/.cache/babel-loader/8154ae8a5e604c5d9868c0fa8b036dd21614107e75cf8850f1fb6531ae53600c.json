{"ast": null, "code": "import { path } from \"d3-path\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport star from \"./symbol/star.js\";\nimport square from \"./symbol/square.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport wye from \"./symbol/wye.js\";\nimport constant from \"./constant.js\";\nexport var symbols = [circle, cross, diamond, square, star, triangle, wye];\nexport default function () {\n  var type = constant(circle),\n    size = constant(64),\n    context = null;\n  function symbol() {\n    var buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n  symbol.type = function (_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n  symbol.size = function (_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n  symbol.context = function (_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n  return symbol;\n}", "map": {"version": 3, "names": ["path", "circle", "cross", "diamond", "star", "square", "triangle", "wye", "constant", "symbols", "type", "size", "context", "symbol", "buffer", "apply", "arguments", "draw", "_", "length"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-shape/src/symbol.js"], "sourcesContent": ["import {path} from \"d3-path\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport star from \"./symbol/star.js\";\nimport square from \"./symbol/square.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport wye from \"./symbol/wye.js\";\nimport constant from \"./constant.js\";\n\nexport var symbols = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\nexport default function() {\n  var type = constant(circle),\n      size = constant(64),\n      context = null;\n\n  function symbol() {\n    var buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,SAAS;AAC5B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,OAAO,IAAIC,OAAO,GAAG,CACnBR,MAAM,EACNC,KAAK,EACLC,OAAO,EACPE,MAAM,EACND,IAAI,EACJE,QAAQ,EACRC,GAAG,CACJ;AAED,eAAe,YAAW;EACxB,IAAIG,IAAI,GAAGF,QAAQ,CAACP,MAAM,CAAC;IACvBU,IAAI,GAAGH,QAAQ,CAAC,EAAE,CAAC;IACnBI,OAAO,GAAG,IAAI;EAElB,SAASC,MAAMA,CAAA,EAAG;IAChB,IAAIC,MAAM;IACV,IAAI,CAACF,OAAO,EAAEA,OAAO,GAAGE,MAAM,GAAGd,IAAI,CAAC,CAAC;IACvCU,IAAI,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACC,IAAI,CAACL,OAAO,EAAE,CAACD,IAAI,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;IACvE,IAAIF,MAAM,EAAE,OAAOF,OAAO,GAAG,IAAI,EAAEE,MAAM,GAAG,EAAE,IAAI,IAAI;EACxD;EAEAD,MAAM,CAACH,IAAI,GAAG,UAASQ,CAAC,EAAE;IACxB,OAAOF,SAAS,CAACG,MAAM,IAAIT,IAAI,GAAG,OAAOQ,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGV,QAAQ,CAACU,CAAC,CAAC,EAAEL,MAAM,IAAIH,IAAI;EAC7F,CAAC;EAEDG,MAAM,CAACF,IAAI,GAAG,UAASO,CAAC,EAAE;IACxB,OAAOF,SAAS,CAACG,MAAM,IAAIR,IAAI,GAAG,OAAOO,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGV,QAAQ,CAAC,CAACU,CAAC,CAAC,EAAEL,MAAM,IAAIF,IAAI;EAC9F,CAAC;EAEDE,MAAM,CAACD,OAAO,GAAG,UAASM,CAAC,EAAE;IAC3B,OAAOF,SAAS,CAACG,MAAM,IAAIP,OAAO,GAAGM,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,CAAC,EAAEL,MAAM,IAAID,OAAO;EAC9E,CAAC;EAED,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}