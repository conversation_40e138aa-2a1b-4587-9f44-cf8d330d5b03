{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { tarjan } from './tarjan.js';\nexport { findCycles };\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function (cmpt) {\n    return cmpt.length > 1 || cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]);\n  });\n}", "map": {"version": 3, "names": ["_", "tarjan", "findCycles", "g", "filter", "cmpt", "length", "hasEdge"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/graphlib/alg/find-cycles.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { tarjan } from './tarjan.js';\n\nexport { findCycles };\n\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function (cmpt) {\n    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));\n  });\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,MAAM,QAAQ,aAAa;AAEpC,SAASC,UAAU;AAEnB,SAASA,UAAUA,CAACC,CAAC,EAAE;EACrB,OAAOH,CAAC,CAACI,MAAM,CAACH,MAAM,CAACE,CAAC,CAAC,EAAE,UAAUE,IAAI,EAAE;IACzC,OAAOA,IAAI,CAACC,MAAM,GAAG,CAAC,IAAKD,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIH,CAAC,CAACI,OAAO,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAE;EAC9E,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}