{"ast": null, "code": "export default function (node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}", "map": {"version": 3, "names": ["node", "x0", "Math", "round", "y0", "x1", "y1"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-hierarchy/src/treemap/round.js"], "sourcesContent": ["export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n"], "mappings": "AAAA,eAAe,UAASA,IAAI,EAAE;EAC5BA,IAAI,CAACC,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACC,EAAE,CAAC;EAC7BD,IAAI,CAACI,EAAE,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,EAAE,CAAC;EAC7BJ,IAAI,CAACK,EAAE,GAAGH,IAAI,CAACC,KAAK,CAACH,IAAI,CAACK,EAAE,CAAC;EAC7BL,IAAI,CAACM,EAAE,GAAGJ,IAAI,CAACC,KAAK,CAACH,IAAI,CAACM,EAAE,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}