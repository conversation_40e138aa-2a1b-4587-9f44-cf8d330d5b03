{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { isCrossReference, isRuleCall } from '../languages/generated/ast.js';\nimport { getCrossReferenceTerminal, getRuleType } from '../utils/grammar-utils.js';\nexport class DefaultValueConverter {\n  convert(input, cstNode) {\n    let feature = cstNode.grammarSource;\n    if (isCrossReference(feature)) {\n      feature = getCrossReferenceTerminal(feature);\n    }\n    if (isRuleCall(feature)) {\n      const rule = feature.rule.ref;\n      if (!rule) {\n        throw new Error('This cst node was not parsed by a rule.');\n      }\n      return this.runConverter(rule, input, cstNode);\n    }\n    return input;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  runConverter(rule, input, cstNode) {\n    var _a;\n    switch (rule.name.toUpperCase()) {\n      case 'INT':\n        return ValueConverter.convertInt(input);\n      case 'STRING':\n        return ValueConverter.convertString(input);\n      case 'ID':\n        return ValueConverter.convertID(input);\n    }\n    switch ((_a = getRuleType(rule)) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {\n      case 'number':\n        return ValueConverter.convertNumber(input);\n      case 'boolean':\n        return ValueConverter.convertBoolean(input);\n      case 'bigint':\n        return ValueConverter.convertBigint(input);\n      case 'date':\n        return ValueConverter.convertDate(input);\n      default:\n        return input;\n    }\n  }\n}\nexport var ValueConverter;\n(function (ValueConverter) {\n  function convertString(input) {\n    let result = '';\n    for (let i = 1; i < input.length - 1; i++) {\n      const c = input.charAt(i);\n      if (c === '\\\\') {\n        const c1 = input.charAt(++i);\n        result += convertEscapeCharacter(c1);\n      } else {\n        result += c;\n      }\n    }\n    return result;\n  }\n  ValueConverter.convertString = convertString;\n  function convertEscapeCharacter(char) {\n    switch (char) {\n      case 'b':\n        return '\\b';\n      case 'f':\n        return '\\f';\n      case 'n':\n        return '\\n';\n      case 'r':\n        return '\\r';\n      case 't':\n        return '\\t';\n      case 'v':\n        return '\\v';\n      case '0':\n        return '\\0';\n      default:\n        return char;\n    }\n  }\n  function convertID(input) {\n    if (input.charAt(0) === '^') {\n      return input.substring(1);\n    } else {\n      return input;\n    }\n  }\n  ValueConverter.convertID = convertID;\n  function convertInt(input) {\n    return parseInt(input);\n  }\n  ValueConverter.convertInt = convertInt;\n  function convertBigint(input) {\n    return BigInt(input);\n  }\n  ValueConverter.convertBigint = convertBigint;\n  function convertDate(input) {\n    return new Date(input);\n  }\n  ValueConverter.convertDate = convertDate;\n  function convertNumber(input) {\n    return Number(input);\n  }\n  ValueConverter.convertNumber = convertNumber;\n  function convertBoolean(input) {\n    return input.toLowerCase() === 'true';\n  }\n  ValueConverter.convertBoolean = convertBoolean;\n})(ValueConverter || (ValueConverter = {}));", "map": {"version": 3, "names": ["isCrossReference", "isRuleCall", "getCrossReferenceTerminal", "getRuleType", "DefaultValueConverter", "convert", "input", "cstNode", "feature", "grammarSource", "rule", "ref", "Error", "runConverter", "name", "toUpperCase", "ValueConverter", "convertInt", "convertString", "convertID", "_a", "toLowerCase", "convertNumber", "convertBoolean", "convertBigint", "convertDate", "result", "i", "length", "c", "char<PERSON>t", "c1", "convertEscapeCharacter", "char", "substring", "parseInt", "BigInt", "Date", "Number"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/parser/value-converter.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { AbstractElement, AbstractRule } from '../languages/generated/ast.js';\r\nimport type { CstNode } from '../syntax-tree.js';\r\nimport { isCrossReference, isRuleCall } from '../languages/generated/ast.js';\r\nimport { getCrossReferenceTerminal, getRuleType } from '../utils/grammar-utils.js';\r\n\r\n/**\r\n * Language-specific service for converting string values from the source text format into a value to be held in the AST.\r\n */\r\nexport interface ValueConverter {\r\n    /**\r\n     * Converts a string value from the source text format into a value to be held in the AST.\r\n     */\r\n    convert(input: string, cstNode: CstNode): ValueType;\r\n}\r\n\r\nexport type ValueType = string | number | boolean | bigint | Date;\r\n\r\nexport class DefaultValueConverter implements ValueConverter {\r\n\r\n    convert(input: string, cstNode: CstNode): ValueType {\r\n        let feature: AbstractElement | undefined = cstNode.grammarSource;\r\n        if (isCrossReference(feature)) {\r\n            feature = getCrossReferenceTerminal(feature);\r\n        }\r\n        if (isRuleCall(feature)) {\r\n            const rule = feature.rule.ref;\r\n            if (!rule) {\r\n                throw new Error('This cst node was not parsed by a rule.');\r\n            }\r\n            return this.runConverter(rule, input, cstNode);\r\n        }\r\n        return input;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected runConverter(rule: AbstractRule, input: string, cstNode: CstNode): ValueType {\r\n        switch (rule.name.toUpperCase()) {\r\n            case 'INT': return ValueConverter.convertInt(input);\r\n            case 'STRING': return ValueConverter.convertString(input);\r\n            case 'ID': return ValueConverter.convertID(input);\r\n        }\r\n        switch (getRuleType(rule)?.toLowerCase()) {\r\n            case 'number': return ValueConverter.convertNumber(input);\r\n            case 'boolean': return ValueConverter.convertBoolean(input);\r\n            case 'bigint': return ValueConverter.convertBigint(input);\r\n            case 'date': return ValueConverter.convertDate(input);\r\n            default: return input;\r\n        }\r\n    }\r\n}\r\n\r\nexport namespace ValueConverter {\r\n\r\n    export function convertString(input: string): string {\r\n        let result = '';\r\n        for (let i = 1; i < input.length - 1; i++) {\r\n            const c = input.charAt(i);\r\n            if (c === '\\\\') {\r\n                const c1 = input.charAt(++i);\r\n                result += convertEscapeCharacter(c1);\r\n            } else {\r\n                result += c;\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n\r\n    function convertEscapeCharacter(char: string): string {\r\n        switch (char) {\r\n            case 'b': return '\\b';\r\n            case 'f': return '\\f';\r\n            case 'n': return '\\n';\r\n            case 'r': return '\\r';\r\n            case 't': return '\\t';\r\n            case 'v': return '\\v';\r\n            case '0': return '\\0';\r\n            default: return char;\r\n        }\r\n    }\r\n\r\n    export function convertID(input: string): string {\r\n        if (input.charAt(0) === '^') {\r\n            return input.substring(1);\r\n        } else {\r\n            return input;\r\n        }\r\n    }\r\n\r\n    export function convertInt(input: string): number {\r\n        return parseInt(input);\r\n    }\r\n\r\n    export function convertBigint(input: string): bigint {\r\n        return BigInt(input);\r\n    }\r\n\r\n    export function convertDate(input: string): Date {\r\n        return new Date(input);\r\n    }\r\n\r\n    export function convertNumber(input: string): number {\r\n        return Number(input);\r\n    }\r\n\r\n    export function convertBoolean(input: string): boolean {\r\n        return input.toLowerCase() === 'true';\r\n    }\r\n\r\n}\r\n"], "mappings": "AAAA;;;;;AAQA,SAASA,gBAAgB,EAAEC,UAAU,QAAQ,+BAA+B;AAC5E,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,2BAA2B;AAclF,OAAM,MAAOC,qBAAqB;EAE9BC,OAAOA,CAACC,KAAa,EAAEC,OAAgB;IACnC,IAAIC,OAAO,GAAgCD,OAAO,CAACE,aAAa;IAChE,IAAIT,gBAAgB,CAACQ,OAAO,CAAC,EAAE;MAC3BA,OAAO,GAAGN,yBAAyB,CAACM,OAAO,CAAC;IAChD;IACA,IAAIP,UAAU,CAACO,OAAO,CAAC,EAAE;MACrB,MAAME,IAAI,GAAGF,OAAO,CAACE,IAAI,CAACC,GAAG;MAC7B,IAAI,CAACD,IAAI,EAAE;QACP,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;MAC9D;MACA,OAAO,IAAI,CAACC,YAAY,CAACH,IAAI,EAAEJ,KAAK,EAAEC,OAAO,CAAC;IAClD;IACA,OAAOD,KAAK;EAChB;EAEA;EACUO,YAAYA,CAACH,IAAkB,EAAEJ,KAAa,EAAEC,OAAgB;;IACtE,QAAQG,IAAI,CAACI,IAAI,CAACC,WAAW,EAAE;MAC3B,KAAK,KAAK;QAAE,OAAOC,cAAc,CAACC,UAAU,CAACX,KAAK,CAAC;MACnD,KAAK,QAAQ;QAAE,OAAOU,cAAc,CAACE,aAAa,CAACZ,KAAK,CAAC;MACzD,KAAK,IAAI;QAAE,OAAOU,cAAc,CAACG,SAAS,CAACb,KAAK,CAAC;IACrD;IACA,QAAQ,CAAAc,EAAA,GAAAjB,WAAW,CAACO,IAAI,CAAC,cAAAU,EAAA,uBAAAA,EAAA,CAAEC,WAAW,EAAE;MACpC,KAAK,QAAQ;QAAE,OAAOL,cAAc,CAACM,aAAa,CAAChB,KAAK,CAAC;MACzD,KAAK,SAAS;QAAE,OAAOU,cAAc,CAACO,cAAc,CAACjB,KAAK,CAAC;MAC3D,KAAK,QAAQ;QAAE,OAAOU,cAAc,CAACQ,aAAa,CAAClB,KAAK,CAAC;MACzD,KAAK,MAAM;QAAE,OAAOU,cAAc,CAACS,WAAW,CAACnB,KAAK,CAAC;MACrD;QAAS,OAAOA,KAAK;IACzB;EACJ;;AAGJ,OAAM,IAAWU,cAAc;AAA/B,WAAiBA,cAAc;EAE3B,SAAgBE,aAAaA,CAACZ,KAAa;IACvC,IAAIoB,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;MACvC,MAAME,CAAC,GAAGvB,KAAK,CAACwB,MAAM,CAACH,CAAC,CAAC;MACzB,IAAIE,CAAC,KAAK,IAAI,EAAE;QACZ,MAAME,EAAE,GAAGzB,KAAK,CAACwB,MAAM,CAAC,EAAEH,CAAC,CAAC;QAC5BD,MAAM,IAAIM,sBAAsB,CAACD,EAAE,CAAC;MACxC,CAAC,MAAM;QACHL,MAAM,IAAIG,CAAC;MACf;IACJ;IACA,OAAOH,MAAM;EACjB;EAZgBV,cAAA,CAAAE,aAAa,GAAAA,aAY5B;EAED,SAASc,sBAAsBA,CAACC,IAAY;IACxC,QAAQA,IAAI;MACR,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB,KAAK,GAAG;QAAE,OAAO,IAAI;MACrB;QAAS,OAAOA,IAAI;IACxB;EACJ;EAEA,SAAgBd,SAASA,CAACb,KAAa;IACnC,IAAIA,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACzB,OAAOxB,KAAK,CAAC4B,SAAS,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACH,OAAO5B,KAAK;IAChB;EACJ;EANgBU,cAAA,CAAAG,SAAS,GAAAA,SAMxB;EAED,SAAgBF,UAAUA,CAACX,KAAa;IACpC,OAAO6B,QAAQ,CAAC7B,KAAK,CAAC;EAC1B;EAFgBU,cAAA,CAAAC,UAAU,GAAAA,UAEzB;EAED,SAAgBO,aAAaA,CAAClB,KAAa;IACvC,OAAO8B,MAAM,CAAC9B,KAAK,CAAC;EACxB;EAFgBU,cAAA,CAAAQ,aAAa,GAAAA,aAE5B;EAED,SAAgBC,WAAWA,CAACnB,KAAa;IACrC,OAAO,IAAI+B,IAAI,CAAC/B,KAAK,CAAC;EAC1B;EAFgBU,cAAA,CAAAS,WAAW,GAAAA,WAE1B;EAED,SAAgBH,aAAaA,CAAChB,KAAa;IACvC,OAAOgC,MAAM,CAAChC,KAAK,CAAC;EACxB;EAFgBU,cAAA,CAAAM,aAAa,GAAAA,aAE5B;EAED,SAAgBC,cAAcA,CAACjB,KAAa;IACxC,OAAOA,KAAK,CAACe,WAAW,EAAE,KAAK,MAAM;EACzC;EAFgBL,cAAA,CAAAO,cAAc,GAAAA,cAE7B;AAEL,CAAC,EAzDgBP,cAAc,KAAdA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}