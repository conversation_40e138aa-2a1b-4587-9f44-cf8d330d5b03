{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { EMPTY_STREAM, stream } from '../utils/stream.js';\n/**\n * The default scope implementation is based on a `Stream`. It has an optional _outer scope_ describing\n * the next level of elements, which are queried when a target element is not found in the stream provided\n * to this scope.\n */\nexport class StreamScope {\n  constructor(elements, outerScope, options) {\n    var _a;\n    this.elements = elements;\n    this.outerScope = outerScope;\n    this.caseInsensitive = (_a = options === null || options === void 0 ? void 0 : options.caseInsensitive) !== null && _a !== void 0 ? _a : false;\n  }\n  getAllElements() {\n    if (this.outerScope) {\n      return this.elements.concat(this.outerScope.getAllElements());\n    } else {\n      return this.elements;\n    }\n  }\n  getElement(name) {\n    const local = this.caseInsensitive ? this.elements.find(e => e.name.toLowerCase() === name.toLowerCase()) : this.elements.find(e => e.name === name);\n    if (local) {\n      return local;\n    }\n    if (this.outerScope) {\n      return this.outerScope.getElement(name);\n    }\n    return undefined;\n  }\n}\nexport class MapScope {\n  constructor(elements, outerScope, options) {\n    var _a;\n    this.elements = new Map();\n    this.caseInsensitive = (_a = options === null || options === void 0 ? void 0 : options.caseInsensitive) !== null && _a !== void 0 ? _a : false;\n    for (const element of elements) {\n      const name = this.caseInsensitive ? element.name.toLowerCase() : element.name;\n      this.elements.set(name, element);\n    }\n    this.outerScope = outerScope;\n  }\n  getElement(name) {\n    const localName = this.caseInsensitive ? name.toLowerCase() : name;\n    const local = this.elements.get(localName);\n    if (local) {\n      return local;\n    }\n    if (this.outerScope) {\n      return this.outerScope.getElement(name);\n    }\n    return undefined;\n  }\n  getAllElements() {\n    let elementStream = stream(this.elements.values());\n    if (this.outerScope) {\n      elementStream = elementStream.concat(this.outerScope.getAllElements());\n    }\n    return elementStream;\n  }\n}\nexport const EMPTY_SCOPE = {\n  getElement() {\n    return undefined;\n  },\n  getAllElements() {\n    return EMPTY_STREAM;\n  }\n};", "map": {"version": 3, "names": ["EMPTY_STREAM", "stream", "StreamScope", "constructor", "elements", "outerScope", "options", "caseInsensitive", "_a", "getAllElements", "concat", "getElement", "name", "local", "find", "e", "toLowerCase", "undefined", "MapScope", "Map", "element", "set", "localName", "get", "elementStream", "values", "EMPTY_SCOPE"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/references/scope.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { AstNodeDescription } from '../syntax-tree.js';\r\nimport type { Stream } from '../utils/stream.js';\r\nimport { EMPTY_STREAM, stream } from '../utils/stream.js';\r\n\r\n/**\r\n * A scope describes what target elements are visible from a specific cross-reference context.\r\n */\r\nexport interface Scope {\r\n\r\n    /**\r\n     * Find a target element matching the given name. If no element is found, `undefined` is returned.\r\n     * If multiple matching elements are present, the selection of the returned element should be done\r\n     * according to the semantics of your language. Usually it is the element that is most closely defined.\r\n     *\r\n     * @param name Name of the cross-reference target as it appears in the source text.\r\n     */\r\n    getElement(name: string): AstNodeDescription | undefined;\r\n\r\n    /**\r\n     * Create a stream of all elements in the scope. This is used to compute completion proposals to be\r\n     * shown in the editor.\r\n     */\r\n    getAllElements(): Stream<AstNodeDescription>;\r\n\r\n}\r\n\r\nexport interface ScopeOptions {\r\n    caseInsensitive?: boolean;\r\n}\r\n\r\n/**\r\n * The default scope implementation is based on a `Stream`. It has an optional _outer scope_ describing\r\n * the next level of elements, which are queried when a target element is not found in the stream provided\r\n * to this scope.\r\n */\r\nexport class StreamScope implements Scope {\r\n    readonly elements: Stream<AstNodeDescription>;\r\n    readonly outerScope?: Scope;\r\n    readonly caseInsensitive: boolean;\r\n\r\n    constructor(elements: Stream<AstNodeDescription>, outerScope?: Scope, options?: ScopeOptions) {\r\n        this.elements = elements;\r\n        this.outerScope = outerScope;\r\n        this.caseInsensitive = options?.caseInsensitive ?? false;\r\n    }\r\n\r\n    getAllElements(): Stream<AstNodeDescription> {\r\n        if (this.outerScope) {\r\n            return this.elements.concat(this.outerScope.getAllElements());\r\n        } else {\r\n            return this.elements;\r\n        }\r\n    }\r\n\r\n    getElement(name: string): AstNodeDescription | undefined {\r\n        const local = this.caseInsensitive\r\n            ? this.elements.find(e => e.name.toLowerCase() === name.toLowerCase())\r\n            : this.elements.find(e => e.name === name);\r\n        if (local) {\r\n            return local;\r\n        }\r\n        if (this.outerScope) {\r\n            return this.outerScope.getElement(name);\r\n        }\r\n        return undefined;\r\n    }\r\n}\r\n\r\nexport class MapScope implements Scope {\r\n    readonly elements: Map<string, AstNodeDescription>;\r\n    readonly outerScope?: Scope;\r\n    readonly caseInsensitive: boolean;\r\n\r\n    constructor(elements: Iterable<AstNodeDescription>, outerScope?: Scope, options?: ScopeOptions) {\r\n        this.elements = new Map();\r\n        this.caseInsensitive = options?.caseInsensitive ?? false;\r\n        for (const element of elements) {\r\n            const name = this.caseInsensitive\r\n                ? element.name.toLowerCase()\r\n                : element.name;\r\n            this.elements.set(name, element);\r\n        }\r\n        this.outerScope = outerScope;\r\n    }\r\n\r\n    getElement(name: string): AstNodeDescription | undefined {\r\n        const localName = this.caseInsensitive ? name.toLowerCase() : name;\r\n        const local = this.elements.get(localName);\r\n        if (local) {\r\n            return local;\r\n        }\r\n        if (this.outerScope) {\r\n            return this.outerScope.getElement(name);\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    getAllElements(): Stream<AstNodeDescription> {\r\n        let elementStream = stream(this.elements.values());\r\n        if (this.outerScope) {\r\n            elementStream = elementStream.concat(this.outerScope.getAllElements());\r\n        }\r\n        return elementStream;\r\n    }\r\n\r\n}\r\n\r\nexport const EMPTY_SCOPE: Scope = {\r\n    getElement(): undefined {\r\n        return undefined;\r\n    },\r\n    getAllElements(): Stream<AstNodeDescription> {\r\n        return EMPTY_STREAM;\r\n    }\r\n};\r\n"], "mappings": "AAAA;;;;;AAQA,SAASA,YAAY,EAAEC,MAAM,QAAQ,oBAAoB;AA4BzD;;;;;AAKA,OAAM,MAAOC,WAAW;EAKpBC,YAAYC,QAAoC,EAAEC,UAAkB,EAAEC,OAAsB;;IACxF,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,eAAe,GAAG,CAAAC,EAAA,GAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,eAAe,cAAAC,EAAA,cAAAA,EAAA,GAAI,KAAK;EAC5D;EAEAC,cAAcA,CAAA;IACV,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjB,OAAO,IAAI,CAACD,QAAQ,CAACM,MAAM,CAAC,IAAI,CAACL,UAAU,CAACI,cAAc,EAAE,CAAC;IACjE,CAAC,MAAM;MACH,OAAO,IAAI,CAACL,QAAQ;IACxB;EACJ;EAEAO,UAAUA,CAACC,IAAY;IACnB,MAAMC,KAAK,GAAG,IAAI,CAACN,eAAe,GAC5B,IAAI,CAACH,QAAQ,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,IAAI,CAACI,WAAW,EAAE,KAAKJ,IAAI,CAACI,WAAW,EAAE,CAAC,GACpE,IAAI,CAACZ,QAAQ,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAC9C,IAAIC,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB;IACA,IAAI,IAAI,CAACR,UAAU,EAAE;MACjB,OAAO,IAAI,CAACA,UAAU,CAACM,UAAU,CAACC,IAAI,CAAC;IAC3C;IACA,OAAOK,SAAS;EACpB;;AAGJ,OAAM,MAAOC,QAAQ;EAKjBf,YAAYC,QAAsC,EAAEC,UAAkB,EAAEC,OAAsB;;IAC1F,IAAI,CAACF,QAAQ,GAAG,IAAIe,GAAG,EAAE;IACzB,IAAI,CAACZ,eAAe,GAAG,CAAAC,EAAA,GAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,eAAe,cAAAC,EAAA,cAAAA,EAAA,GAAI,KAAK;IACxD,KAAK,MAAMY,OAAO,IAAIhB,QAAQ,EAAE;MAC5B,MAAMQ,IAAI,GAAG,IAAI,CAACL,eAAe,GAC3Ba,OAAO,CAACR,IAAI,CAACI,WAAW,EAAE,GAC1BI,OAAO,CAACR,IAAI;MAClB,IAAI,CAACR,QAAQ,CAACiB,GAAG,CAACT,IAAI,EAAEQ,OAAO,CAAC;IACpC;IACA,IAAI,CAACf,UAAU,GAAGA,UAAU;EAChC;EAEAM,UAAUA,CAACC,IAAY;IACnB,MAAMU,SAAS,GAAG,IAAI,CAACf,eAAe,GAAGK,IAAI,CAACI,WAAW,EAAE,GAAGJ,IAAI;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACT,QAAQ,CAACmB,GAAG,CAACD,SAAS,CAAC;IAC1C,IAAIT,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB;IACA,IAAI,IAAI,CAACR,UAAU,EAAE;MACjB,OAAO,IAAI,CAACA,UAAU,CAACM,UAAU,CAACC,IAAI,CAAC;IAC3C;IACA,OAAOK,SAAS;EACpB;EAEAR,cAAcA,CAAA;IACV,IAAIe,aAAa,GAAGvB,MAAM,CAAC,IAAI,CAACG,QAAQ,CAACqB,MAAM,EAAE,CAAC;IAClD,IAAI,IAAI,CAACpB,UAAU,EAAE;MACjBmB,aAAa,GAAGA,aAAa,CAACd,MAAM,CAAC,IAAI,CAACL,UAAU,CAACI,cAAc,EAAE,CAAC;IAC1E;IACA,OAAOe,aAAa;EACxB;;AAIJ,OAAO,MAAME,WAAW,GAAU;EAC9Bf,UAAUA,CAAA;IACN,OAAOM,SAAS;EACpB,CAAC;EACDR,cAAcA,CAAA;IACV,OAAOT,YAAY;EACvB;CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}