{"ast": null, "code": "import arrayAggregator from './_arrayAggregator.js';\nimport baseAggregator from './_baseAggregator.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a function like `_.groupBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} [initializer] The accumulator object initializer.\n * @returns {Function} Returns the new aggregator function.\n */\nfunction createAggregator(setter, initializer) {\n  return function (collection, iteratee) {\n    var func = isArray(collection) ? arrayAggregator : baseAggregator,\n      accumulator = initializer ? initializer() : {};\n    return func(collection, setter, baseIteratee(iteratee, 2), accumulator);\n  };\n}\nexport default createAggregator;", "map": {"version": 3, "names": ["arrayAggregator", "baseAggregator", "baseIteratee", "isArray", "createAggregator", "setter", "initializer", "collection", "iteratee", "func", "accumulator"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_createAggregator.js"], "sourcesContent": ["import arrayAggregator from './_arrayAggregator.js';\nimport baseAggregator from './_baseAggregator.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates a function like `_.groupBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} [initializer] The accumulator object initializer.\n * @returns {Function} Returns the new aggregator function.\n */\nfunction createAggregator(setter, initializer) {\n  return function(collection, iteratee) {\n    var func = isArray(collection) ? arrayAggregator : baseAggregator,\n        accumulator = initializer ? initializer() : {};\n\n    return func(collection, setter, baseIteratee(iteratee, 2), accumulator);\n  };\n}\n\nexport default createAggregator;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAC7C,OAAO,UAASC,UAAU,EAAEC,QAAQ,EAAE;IACpC,IAAIC,IAAI,GAAGN,OAAO,CAACI,UAAU,CAAC,GAAGP,eAAe,GAAGC,cAAc;MAC7DS,WAAW,GAAGJ,WAAW,GAAGA,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;IAElD,OAAOG,IAAI,CAACF,UAAU,EAAEF,MAAM,EAAEH,YAAY,CAACM,QAAQ,EAAE,CAAC,CAAC,EAAEE,WAAW,CAAC;EACzE,CAAC;AACH;AAEA,eAAeN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}