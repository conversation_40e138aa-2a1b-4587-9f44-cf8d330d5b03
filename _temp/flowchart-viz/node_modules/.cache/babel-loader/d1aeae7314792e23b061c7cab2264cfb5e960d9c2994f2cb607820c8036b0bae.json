{"ast": null, "code": "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\n\nexport var DocumentUri;\n(function (DocumentUri) {\n  function is(value) {\n    return typeof value === 'string';\n  }\n  DocumentUri.is = is;\n})(DocumentUri || (DocumentUri = {}));\nexport var URI;\n(function (URI) {\n  function is(value) {\n    return typeof value === 'string';\n  }\n  URI.is = is;\n})(URI || (URI = {}));\nexport var integer;\n(function (integer) {\n  integer.MIN_VALUE = -**********;\n  integer.MAX_VALUE = **********;\n  function is(value) {\n    return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;\n  }\n  integer.is = is;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n  uinteger.MIN_VALUE = 0;\n  uinteger.MAX_VALUE = **********;\n  function is(value) {\n    return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;\n  }\n  uinteger.is = is;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * {@link Position} literals.\n */\nexport var Position;\n(function (Position) {\n  /**\n   * Creates a new Position literal from the given line and character.\n   * @param line The position's line.\n   * @param character The position's character.\n   */\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return {\n      line,\n      character\n    };\n  }\n  Position.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Position} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * {@link Range} literals.\n */\nexport var Range;\n(function (Range) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return {\n        start: Position.create(one, two),\n        end: Position.create(three, four)\n      };\n    } else if (Position.is(one) && Position.is(two)) {\n      return {\n        start: one,\n        end: two\n      };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Range} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * {@link Location} literals.\n */\nexport var Location;\n(function (Location) {\n  /**\n   * Creates a Location literal.\n   * @param uri The location's uri.\n   * @param range The location's range.\n   */\n  function create(uri, range) {\n    return {\n      uri,\n      range\n    };\n  }\n  Location.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Location} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * {@link LocationLink} literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n  /**\n   * Creates a LocationLink literal.\n   * @param targetUri The definition's uri.\n   * @param targetRange The full range of the definition.\n   * @param targetSelectionRange The span of the symbol definition at the target.\n   * @param originSelectionRange The span of the symbol being defined in the originating source file.\n   */\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return {\n      targetUri,\n      targetRange,\n      targetSelectionRange,\n      originSelectionRange\n    };\n  }\n  LocationLink.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link LocationLink} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link Color} literals.\n */\nexport var Color;\n(function (Color) {\n  /**\n   * Creates a new Color literal.\n   */\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Color} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * {@link ColorInformation} literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n  /**\n   * Creates a new ColorInformation literal.\n   */\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link ColorPresentation} literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n  /**\n   * Creates a new ColorInformation literal.\n   */\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * A set of predefined range kinds.\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n  /**\n   * Folding range for a comment\n   */\n  FoldingRangeKind.Comment = 'comment';\n  /**\n   * Folding range for an import or include\n   */\n  FoldingRangeKind.Imports = 'imports';\n  /**\n   * Folding range for a region (e.g. `#region`)\n   */\n  FoldingRangeKind.Region = 'region';\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * {@link FoldingRange} literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n  /**\n   * Creates a new FoldingRange literal.\n   */\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link FoldingRange} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * {@link DiagnosticRelatedInformation} literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n  /**\n   * Creates a new DiagnosticRelatedInformation literal.\n   */\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link DiagnosticRelatedInformation} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n  /**\n   * Reports an error.\n   */\n  DiagnosticSeverity.Error = 1;\n  /**\n   * Reports a warning.\n   */\n  DiagnosticSeverity.Warning = 2;\n  /**\n   * Reports an information.\n   */\n  DiagnosticSeverity.Information = 3;\n  /**\n   * Reports a hint.\n   */\n  DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n  /**\n   * Unused or unnecessary code.\n   *\n   * Clients are allowed to render diagnostics with this tag faded out instead of having\n   * an error squiggle.\n   */\n  DiagnosticTag.Unnecessary = 1;\n  /**\n   * Deprecated or obsolete code.\n   *\n   * Clients are allowed to rendered diagnostics with this tag strike through.\n   */\n  DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * {@link Diagnostic} literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n  /**\n   * Creates a new Diagnostic literal.\n   */\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = {\n      range,\n      message\n    };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Diagnostic} interface.\n   */\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * {@link Command} literals.\n */\nexport var Command;\n(function (Command) {\n  /**\n   * Creates a new Command literal.\n   */\n  function create(title, command, ...args) {\n    let result = {\n      title,\n      command\n    };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link Command} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n  /**\n   * Creates a replace text edit.\n   * @param range The range of text to be replaced.\n   * @param newText The new text.\n   */\n  function replace(range, newText) {\n    return {\n      range,\n      newText\n    };\n  }\n  TextEdit.replace = replace;\n  /**\n   * Creates an insert text edit.\n   * @param position The position to insert the text at.\n   * @param newText The text to be inserted.\n   */\n  function insert(position, newText) {\n    return {\n      range: {\n        start: position,\n        end: position\n      },\n      newText\n    };\n  }\n  TextEdit.insert = insert;\n  /**\n   * Creates a delete text edit.\n   * @param range The range of text to be deleted.\n   */\n  function del(range) {\n    return {\n      range,\n      newText: ''\n    };\n  }\n  TextEdit.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n  function create(label, needsConfirmation, description) {\n    const result = {\n      label\n    };\n    if (needsConfirmation !== undefined) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== undefined) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) && (Is.string(candidate.description) || candidate.description === undefined);\n  }\n  ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n  /**\n   * Creates an annotated replace text edit.\n   *\n   * @param range The range of text to be replaced.\n   * @param newText The new text.\n   * @param annotation The annotation.\n   */\n  function replace(range, newText, annotation) {\n    return {\n      range,\n      newText,\n      annotationId: annotation\n    };\n  }\n  AnnotatedTextEdit.replace = replace;\n  /**\n   * Creates an annotated insert text edit.\n   *\n   * @param position The position to insert the text at.\n   * @param newText The text to be inserted.\n   * @param annotation The annotation.\n   */\n  function insert(position, newText, annotation) {\n    return {\n      range: {\n        start: position,\n        end: position\n      },\n      newText,\n      annotationId: annotation\n    };\n  }\n  AnnotatedTextEdit.insert = insert;\n  /**\n   * Creates an annotated delete text edit.\n   *\n   * @param range The range of text to be deleted.\n   * @param annotation The annotation.\n   */\n  function del(range, annotation) {\n    return {\n      range,\n      newText: '',\n      annotationId: annotation\n    };\n  }\n  AnnotatedTextEdit.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n  /**\n   * Creates a new `TextDocumentEdit`\n   */\n  function create(textDocument, edits) {\n    return {\n      textDocument,\n      edits\n    };\n  }\n  TextDocumentEdit.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: 'create',\n      uri\n    };\n    if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n      result.options = options;\n    }\n    if (annotation !== undefined) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined || (candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: 'rename',\n      oldUri,\n      newUri\n    };\n    if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n      result.options = options;\n    }\n    if (annotation !== undefined) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined || (candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: 'delete',\n      uri\n    };\n    if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n      result.options = options;\n    }\n    if (annotation !== undefined) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined || (candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== undefined || candidate.documentChanges !== undefined) && (candidate.documentChanges === undefined || candidate.documentChanges.every(change => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nclass TextEditChangeImpl {\n  constructor(edits, changeAnnotations) {\n    this.edits = edits;\n    this.changeAnnotations = changeAnnotations;\n  }\n  insert(position, newText, annotation) {\n    let edit;\n    let id;\n    if (annotation === undefined) {\n      edit = TextEdit.insert(position, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.insert(position, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.insert(position, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n  replace(range, newText, annotation) {\n    let edit;\n    let id;\n    if (annotation === undefined) {\n      edit = TextEdit.replace(range, newText);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.replace(range, newText, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.replace(range, newText, id);\n    }\n    this.edits.push(edit);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n  delete(range, annotation) {\n    let edit;\n    let id;\n    if (annotation === undefined) {\n      edit = TextEdit.del(range);\n    } else if (ChangeAnnotationIdentifier.is(annotation)) {\n      id = annotation;\n      edit = AnnotatedTextEdit.del(range, annotation);\n    } else {\n      this.assertChangeAnnotations(this.changeAnnotations);\n      id = this.changeAnnotations.manage(annotation);\n      edit = AnnotatedTextEdit.del(range, id);\n    }\n    this.edits.push(edit);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n  add(edit) {\n    this.edits.push(edit);\n  }\n  all() {\n    return this.edits;\n  }\n  clear() {\n    this.edits.splice(0, this.edits.length);\n  }\n  assertChangeAnnotations(value) {\n    if (value === undefined) {\n      throw new Error(`Text edit change is not configured to manage change annotations.`);\n    }\n  }\n}\n/**\n * A helper class\n */\nclass ChangeAnnotations {\n  constructor(annotations) {\n    this._annotations = annotations === undefined ? Object.create(null) : annotations;\n    this._counter = 0;\n    this._size = 0;\n  }\n  all() {\n    return this._annotations;\n  }\n  get size() {\n    return this._size;\n  }\n  manage(idOrAnnotation, annotation) {\n    let id;\n    if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n      id = idOrAnnotation;\n    } else {\n      id = this.nextId();\n      annotation = idOrAnnotation;\n    }\n    if (this._annotations[id] !== undefined) {\n      throw new Error(`Id ${id} is already in use.`);\n    }\n    if (annotation === undefined) {\n      throw new Error(`No annotation provided for id ${id}`);\n    }\n    this._annotations[id] = annotation;\n    this._size++;\n    return id;\n  }\n  nextId() {\n    this._counter++;\n    return this._counter.toString();\n  }\n}\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nexport class WorkspaceChange {\n  constructor(workspaceEdit) {\n    this._textEditChanges = Object.create(null);\n    if (workspaceEdit !== undefined) {\n      this._workspaceEdit = workspaceEdit;\n      if (workspaceEdit.documentChanges) {\n        this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n        workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        workspaceEdit.documentChanges.forEach(change => {\n          if (TextDocumentEdit.is(change)) {\n            const textEditChange = new TextEditChangeImpl(change.edits, this._changeAnnotations);\n            this._textEditChanges[change.textDocument.uri] = textEditChange;\n          }\n        });\n      } else if (workspaceEdit.changes) {\n        Object.keys(workspaceEdit.changes).forEach(key => {\n          const textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n          this._textEditChanges[key] = textEditChange;\n        });\n      }\n    } else {\n      this._workspaceEdit = {};\n    }\n  }\n  /**\n   * Returns the underlying {@link WorkspaceEdit} literal\n   * use to be returned from a workspace edit operation like rename.\n   */\n  get edit() {\n    this.initDocumentChanges();\n    if (this._changeAnnotations !== undefined) {\n      if (this._changeAnnotations.size === 0) {\n        this._workspaceEdit.changeAnnotations = undefined;\n      } else {\n        this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n      }\n    }\n    return this._workspaceEdit;\n  }\n  getTextEditChange(key) {\n    if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n      this.initDocumentChanges();\n      if (this._workspaceEdit.documentChanges === undefined) {\n        throw new Error('Workspace edit is not configured for document changes.');\n      }\n      const textDocument = {\n        uri: key.uri,\n        version: key.version\n      };\n      let result = this._textEditChanges[textDocument.uri];\n      if (!result) {\n        const edits = [];\n        const textDocumentEdit = {\n          textDocument,\n          edits\n        };\n        this._workspaceEdit.documentChanges.push(textDocumentEdit);\n        result = new TextEditChangeImpl(edits, this._changeAnnotations);\n        this._textEditChanges[textDocument.uri] = result;\n      }\n      return result;\n    } else {\n      this.initChanges();\n      if (this._workspaceEdit.changes === undefined) {\n        throw new Error('Workspace edit is not configured for normal text edit changes.');\n      }\n      let result = this._textEditChanges[key];\n      if (!result) {\n        let edits = [];\n        this._workspaceEdit.changes[key] = edits;\n        result = new TextEditChangeImpl(edits);\n        this._textEditChanges[key] = result;\n      }\n      return result;\n    }\n  }\n  initDocumentChanges() {\n    if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n      this._changeAnnotations = new ChangeAnnotations();\n      this._workspaceEdit.documentChanges = [];\n      this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n    }\n  }\n  initChanges() {\n    if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n      this._workspaceEdit.changes = Object.create(null);\n    }\n  }\n  createFile(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === undefined) {\n      throw new Error('Workspace edit is not configured for document changes.');\n    }\n    let annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    let operation;\n    let id;\n    if (annotation === undefined) {\n      operation = CreateFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = CreateFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n  renameFile(oldUri, newUri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === undefined) {\n      throw new Error('Workspace edit is not configured for document changes.');\n    }\n    let annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    let operation;\n    let id;\n    if (annotation === undefined) {\n      operation = RenameFile.create(oldUri, newUri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = RenameFile.create(oldUri, newUri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n  deleteFile(uri, optionsOrAnnotation, options) {\n    this.initDocumentChanges();\n    if (this._workspaceEdit.documentChanges === undefined) {\n      throw new Error('Workspace edit is not configured for document changes.');\n    }\n    let annotation;\n    if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n      annotation = optionsOrAnnotation;\n    } else {\n      options = optionsOrAnnotation;\n    }\n    let operation;\n    let id;\n    if (annotation === undefined) {\n      operation = DeleteFile.create(uri, options);\n    } else {\n      id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n      operation = DeleteFile.create(uri, options, id);\n    }\n    this._workspaceEdit.documentChanges.push(operation);\n    if (id !== undefined) {\n      return id;\n    }\n  }\n}\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * {@link TextDocumentIdentifier} literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n  /**\n   * Creates a new TextDocumentIdentifier literal.\n   * @param uri The document's uri.\n   */\n  function create(uri) {\n    return {\n      uri\n    };\n  }\n  TextDocumentIdentifier.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link TextDocumentIdentifier} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link VersionedTextDocumentIdentifier} literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n  /**\n   * Creates a new VersionedTextDocumentIdentifier literal.\n   * @param uri The document's uri.\n   * @param version The document's version.\n   */\n  function create(uri, version) {\n    return {\n      uri,\n      version\n    };\n  }\n  VersionedTextDocumentIdentifier.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link VersionedTextDocumentIdentifier} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link OptionalVersionedTextDocumentIdentifier} literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n  /**\n   * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n   * @param uri The document's uri.\n   * @param version The document's version.\n   */\n  function create(uri, version) {\n    return {\n      uri,\n      version\n    };\n  }\n  OptionalVersionedTextDocumentIdentifier.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link OptionalVersionedTextDocumentIdentifier} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * {@link TextDocumentItem} literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n  /**\n   * Creates a new TextDocumentItem literal.\n   * @param uri The document's uri.\n   * @param languageId The document's language identifier.\n   * @param version The document's version number.\n   * @param text The document's text.\n   */\n  function create(uri, languageId, version, text) {\n    return {\n      uri,\n      languageId,\n      version,\n      text\n    };\n  }\n  TextDocumentItem.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link TextDocumentItem} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n  /**\n   * Plain text is supported as a content format\n   */\n  MarkupKind.PlainText = 'plaintext';\n  /**\n   * Markdown is supported as a content format\n   */\n  MarkupKind.Markdown = 'markdown';\n  /**\n   * Checks whether the given value is a value of the {@link MarkupKind} type.\n   */\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n  }\n  MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n  /**\n   * Checks whether the given value conforms to the {@link MarkupContent} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n  CompletionItemKind.Text = 1;\n  CompletionItemKind.Method = 2;\n  CompletionItemKind.Function = 3;\n  CompletionItemKind.Constructor = 4;\n  CompletionItemKind.Field = 5;\n  CompletionItemKind.Variable = 6;\n  CompletionItemKind.Class = 7;\n  CompletionItemKind.Interface = 8;\n  CompletionItemKind.Module = 9;\n  CompletionItemKind.Property = 10;\n  CompletionItemKind.Unit = 11;\n  CompletionItemKind.Value = 12;\n  CompletionItemKind.Enum = 13;\n  CompletionItemKind.Keyword = 14;\n  CompletionItemKind.Snippet = 15;\n  CompletionItemKind.Color = 16;\n  CompletionItemKind.File = 17;\n  CompletionItemKind.Reference = 18;\n  CompletionItemKind.Folder = 19;\n  CompletionItemKind.EnumMember = 20;\n  CompletionItemKind.Constant = 21;\n  CompletionItemKind.Struct = 22;\n  CompletionItemKind.Event = 23;\n  CompletionItemKind.Operator = 24;\n  CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n  /**\n   * The primary text to be inserted is treated as a plain string.\n   */\n  InsertTextFormat.PlainText = 1;\n  /**\n   * The primary text to be inserted is treated as a snippet.\n   *\n   * A snippet can define tab stops and placeholders with `$1`, `$2`\n   * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n   * the end of the snippet. Placeholders with equal identifiers are linked,\n   * that is typing in one will update others too.\n   *\n   * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n   */\n  InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n  /**\n   * Render a completion as obsolete, usually using a strike-out.\n   */\n  CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n  /**\n   * Creates a new insert / replace edit\n   */\n  function create(newText, insert, replace) {\n    return {\n      newText,\n      insert,\n      replace\n    };\n  }\n  InsertReplaceEdit.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link InsertReplaceEdit} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n  /**\n   * The insertion or replace strings is taken as it is. If the\n   * value is multi line the lines below the cursor will be\n   * inserted using the indentation defined in the string value.\n   * The client will not apply any kind of adjustments to the\n   * string.\n   */\n  InsertTextMode.asIs = 1;\n  /**\n   * The editor adjusts leading whitespace of new lines so that\n   * they match the indentation up to the cursor of the line for\n   * which the item is accepted.\n   *\n   * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n   * multi line completion item is indented using 2 tabs and all\n   * following lines inserted will be indented using 2 tabs as well.\n   */\n  InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nexport var CompletionItemLabelDetails;\n(function (CompletionItemLabelDetails) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) && (Is.string(candidate.description) || candidate.description === undefined);\n  }\n  CompletionItemLabelDetails.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n  /**\n   * Create a completion item and seed it with a label.\n   * @param label The completion item's label\n   */\n  function create(label) {\n    return {\n      label\n    };\n  }\n  CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n  /**\n   * Creates a new completion list.\n   *\n   * @param items The completion items.\n   * @param isIncomplete The list is not complete.\n   */\n  function create(items, isIncomplete) {\n    return {\n      items: items ? items : [],\n      isIncomplete: !!isIncomplete\n    };\n  }\n  CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n  /**\n   * Creates a marked string from plain text.\n   *\n   * @param plainText The plain text.\n   */\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n  }\n  MarkedString.fromPlainText = fromPlainText;\n  /**\n   * Checks whether the given value conforms to the {@link MarkedString} type.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n  /**\n   * Checks whether the given value conforms to the {@link Hover} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n  }\n  Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * {@link ParameterInformation} literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n  /**\n   * Creates a new parameter information literal.\n   *\n   * @param label A label string.\n   * @param documentation A doc string.\n   */\n  function create(label, documentation) {\n    return documentation ? {\n      label,\n      documentation\n    } : {\n      label\n    };\n  }\n  ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * {@link SignatureInformation} literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n  function create(label, documentation, ...parameters) {\n    let result = {\n      label\n    };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n  /**\n   * A textual occurrence.\n   */\n  DocumentHighlightKind.Text = 1;\n  /**\n   * Read-access of a symbol, like reading a variable.\n   */\n  DocumentHighlightKind.Read = 2;\n  /**\n   * Write-access of a symbol, like writing to a variable.\n   */\n  DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * {@link DocumentHighlight} literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n  /**\n   * Create a DocumentHighlight object.\n   * @param range The range the highlight applies to.\n   * @param kind The highlight kind\n   */\n  function create(range, kind) {\n    let result = {\n      range\n    };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n  SymbolKind.File = 1;\n  SymbolKind.Module = 2;\n  SymbolKind.Namespace = 3;\n  SymbolKind.Package = 4;\n  SymbolKind.Class = 5;\n  SymbolKind.Method = 6;\n  SymbolKind.Property = 7;\n  SymbolKind.Field = 8;\n  SymbolKind.Constructor = 9;\n  SymbolKind.Enum = 10;\n  SymbolKind.Interface = 11;\n  SymbolKind.Function = 12;\n  SymbolKind.Variable = 13;\n  SymbolKind.Constant = 14;\n  SymbolKind.String = 15;\n  SymbolKind.Number = 16;\n  SymbolKind.Boolean = 17;\n  SymbolKind.Array = 18;\n  SymbolKind.Object = 19;\n  SymbolKind.Key = 20;\n  SymbolKind.Null = 21;\n  SymbolKind.EnumMember = 22;\n  SymbolKind.Struct = 23;\n  SymbolKind.Event = 24;\n  SymbolKind.Operator = 25;\n  SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n *\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n  /**\n   * Render a symbol as obsolete, usually using a strike-out.\n   */\n  SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n  /**\n   * Creates a new symbol information literal.\n   *\n   * @param name The name of the symbol.\n   * @param kind The kind of the symbol.\n   * @param range The range of the location of the symbol.\n   * @param uri The resource of the location of symbol.\n   * @param containerName The name of the symbol containing the symbol.\n   */\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: {\n        uri,\n        range\n      }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var WorkspaceSymbol;\n(function (WorkspaceSymbol) {\n  /**\n   * Create a new workspace symbol.\n   *\n   * @param name The name of the symbol.\n   * @param kind The kind of the symbol.\n   * @param uri The resource of the location of the symbol.\n   * @param range An options range of the location.\n   * @returns A WorkspaceSymbol.\n   */\n  function create(name, kind, uri, range) {\n    return range !== undefined ? {\n      name,\n      kind,\n      location: {\n        uri,\n        range\n      }\n    } : {\n      name,\n      kind,\n      location: {\n        uri\n      }\n    };\n  }\n  WorkspaceSymbol.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n  /**\n   * Creates a new symbol information literal.\n   *\n   * @param name The name of the symbol.\n   * @param detail The detail of the symbol.\n   * @param kind The kind of the symbol.\n   * @param range The range of the symbol.\n   * @param selectionRange The selectionRange of the symbol.\n   * @param children Children of the symbol.\n   */\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== undefined) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link DocumentSymbol} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === undefined || Is.string(candidate.detail)) && (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) && (candidate.children === undefined || Array.isArray(candidate.children)) && (candidate.tags === undefined || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n  /**\n   * Empty kind.\n   */\n  CodeActionKind.Empty = '';\n  /**\n   * Base kind for quickfix actions: 'quickfix'\n   */\n  CodeActionKind.QuickFix = 'quickfix';\n  /**\n   * Base kind for refactoring actions: 'refactor'\n   */\n  CodeActionKind.Refactor = 'refactor';\n  /**\n   * Base kind for refactoring extraction actions: 'refactor.extract'\n   *\n   * Example extract actions:\n   *\n   * - Extract method\n   * - Extract function\n   * - Extract variable\n   * - Extract interface from class\n   * - ...\n   */\n  CodeActionKind.RefactorExtract = 'refactor.extract';\n  /**\n   * Base kind for refactoring inline actions: 'refactor.inline'\n   *\n   * Example inline actions:\n   *\n   * - Inline function\n   * - Inline variable\n   * - Inline constant\n   * - ...\n   */\n  CodeActionKind.RefactorInline = 'refactor.inline';\n  /**\n   * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n   *\n   * Example rewrite actions:\n   *\n   * - Convert JavaScript function to class\n   * - Add or remove parameter\n   * - Encapsulate field\n   * - Make method static\n   * - Move method to base class\n   * - ...\n   */\n  CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n  /**\n   * Base kind for source actions: `source`\n   *\n   * Source code actions apply to the entire file.\n   */\n  CodeActionKind.Source = 'source';\n  /**\n   * Base kind for an organize imports source action: `source.organizeImports`\n   */\n  CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n  /**\n   * Base kind for auto-fix source actions: `source.fixAll`.\n   *\n   * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n   * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n   *\n   * @since 3.15.0\n   */\n  CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The reason why code actions were requested.\n *\n * @since 3.17.0\n */\nexport var CodeActionTriggerKind;\n(function (CodeActionTriggerKind) {\n  /**\n   * Code actions were explicitly requested by the user or by an extension.\n   */\n  CodeActionTriggerKind.Invoked = 1;\n  /**\n   * Code actions were requested automatically.\n   *\n   * This typically happens when current selection in a file changes, but can\n   * also be triggered when file content changes.\n   */\n  CodeActionTriggerKind.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * {@link CodeActionContext} literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n  /**\n   * Creates a new CodeActionContext literal.\n   */\n  function create(diagnostics, only, triggerKind) {\n    let result = {\n      diagnostics\n    };\n    if (only !== undefined && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== undefined && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link CodeActionContext} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = {\n      title\n    };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === 'string') {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== undefined) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === undefined || Is.string(candidate.kind)) && (candidate.edit !== undefined || candidate.command !== undefined) && (candidate.command === undefined || Command.is(candidate.command)) && (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) && (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * {@link CodeLens} literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n  /**\n   * Creates a new CodeLens literal.\n   */\n  function create(range, data) {\n    let result = {\n      range\n    };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link CodeLens} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * {@link FormattingOptions} literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n  /**\n   * Creates a new FormattingOptions literal.\n   */\n  function create(tabSize, insertSpaces) {\n    return {\n      tabSize,\n      insertSpaces\n    };\n  }\n  FormattingOptions.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link FormattingOptions} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * {@link DocumentLink} literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n  /**\n   * Creates a new DocumentLink literal.\n   */\n  function create(range, target, data) {\n    return {\n      range,\n      target,\n      data\n    };\n  }\n  DocumentLink.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link DocumentLink} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n  /**\n   * Creates a new SelectionRange\n   * @param range the range.\n   * @param parent an optional parent.\n   */\n  function create(range, parent) {\n    return {\n      range,\n      parent\n    };\n  }\n  SelectionRange.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n  }\n  SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\n/**\n * A set of predefined token types. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenTypes;\n(function (SemanticTokenTypes) {\n  SemanticTokenTypes[\"namespace\"] = \"namespace\";\n  /**\n   * Represents a generic type. Acts as a fallback for types which can't be mapped to\n   * a specific type like class or enum.\n   */\n  SemanticTokenTypes[\"type\"] = \"type\";\n  SemanticTokenTypes[\"class\"] = \"class\";\n  SemanticTokenTypes[\"enum\"] = \"enum\";\n  SemanticTokenTypes[\"interface\"] = \"interface\";\n  SemanticTokenTypes[\"struct\"] = \"struct\";\n  SemanticTokenTypes[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes[\"variable\"] = \"variable\";\n  SemanticTokenTypes[\"property\"] = \"property\";\n  SemanticTokenTypes[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes[\"event\"] = \"event\";\n  SemanticTokenTypes[\"function\"] = \"function\";\n  SemanticTokenTypes[\"method\"] = \"method\";\n  SemanticTokenTypes[\"macro\"] = \"macro\";\n  SemanticTokenTypes[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes[\"comment\"] = \"comment\";\n  SemanticTokenTypes[\"string\"] = \"string\";\n  SemanticTokenTypes[\"number\"] = \"number\";\n  SemanticTokenTypes[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes[\"operator\"] = \"operator\";\n  /**\n   * @since 3.17.0\n   */\n  SemanticTokenTypes[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\n/**\n * A set of predefined token modifiers. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenModifiers;\n(function (SemanticTokenModifiers) {\n  SemanticTokenModifiers[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers[\"definition\"] = \"definition\";\n  SemanticTokenModifiers[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers[\"static\"] = \"static\";\n  SemanticTokenModifiers[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers[\"async\"] = \"async\";\n  SemanticTokenModifiers[\"modification\"] = \"modification\";\n  SemanticTokenModifiers[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\n/**\n * @since 3.16.0\n */\nexport var SemanticTokens;\n(function (SemanticTokens) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');\n  }\n  SemanticTokens.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\n/**\n * The InlineValueText namespace provides functions to deal with InlineValueTexts.\n *\n * @since 3.17.0\n */\nexport var InlineValueText;\n(function (InlineValueText) {\n  /**\n   * Creates a new InlineValueText literal.\n   */\n  function create(range, text) {\n    return {\n      range,\n      text\n    };\n  }\n  InlineValueText.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText.is = is;\n})(InlineValueText || (InlineValueText = {}));\n/**\n * The InlineValueVariableLookup namespace provides functions to deal with InlineValueVariableLookups.\n *\n * @since 3.17.0\n */\nexport var InlineValueVariableLookup;\n(function (InlineValueVariableLookup) {\n  /**\n   * Creates a new InlineValueText literal.\n   */\n  function create(range, variableName, caseSensitiveLookup) {\n    return {\n      range,\n      variableName,\n      caseSensitiveLookup\n    };\n  }\n  InlineValueVariableLookup.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === undefined);\n  }\n  InlineValueVariableLookup.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\n/**\n * The InlineValueEvaluatableExpression namespace provides functions to deal with InlineValueEvaluatableExpression.\n *\n * @since 3.17.0\n */\nexport var InlineValueEvaluatableExpression;\n(function (InlineValueEvaluatableExpression) {\n  /**\n   * Creates a new InlineValueEvaluatableExpression literal.\n   */\n  function create(range, expression) {\n    return {\n      range,\n      expression\n    };\n  }\n  InlineValueEvaluatableExpression.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== undefined && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === undefined);\n  }\n  InlineValueEvaluatableExpression.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\n/**\n * The InlineValueContext namespace provides helper functions to work with\n * {@link InlineValueContext} literals.\n *\n * @since 3.17.0\n */\nexport var InlineValueContext;\n(function (InlineValueContext) {\n  /**\n   * Creates a new InlineValueContext literal.\n   */\n  function create(frameId, stoppedLocation) {\n    return {\n      frameId,\n      stoppedLocation\n    };\n  }\n  InlineValueContext.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link InlineValueContext} interface.\n   */\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\n/**\n * Inlay hint kinds.\n *\n * @since 3.17.0\n */\nexport var InlayHintKind;\n(function (InlayHintKind) {\n  /**\n   * An inlay hint that for a type annotation.\n   */\n  InlayHintKind.Type = 1;\n  /**\n   * An inlay hint that is for a parameter.\n   */\n  InlayHintKind.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nexport var InlayHintLabelPart;\n(function (InlayHintLabelPart) {\n  function create(value) {\n    return {\n      value\n    };\n  }\n  InlayHintLabelPart.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === undefined || Location.is(candidate.location)) && (candidate.command === undefined || Command.is(candidate.command));\n  }\n  InlayHintLabelPart.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nexport var InlayHint;\n(function (InlayHint) {\n  function create(position, label, kind) {\n    const result = {\n      position,\n      label\n    };\n    if (kind !== undefined) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === undefined || InlayHintKind.is(candidate.kind)) && candidate.textEdits === undefined || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint.is = is;\n})(InlayHint || (InlayHint = {}));\nexport var StringValue;\n(function (StringValue) {\n  function createSnippet(value) {\n    return {\n      kind: 'snippet',\n      value\n    };\n  }\n  StringValue.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nexport var InlineCompletionItem;\n(function (InlineCompletionItem) {\n  function create(insertText, filterText, range, command) {\n    return {\n      insertText,\n      filterText,\n      range,\n      command\n    };\n  }\n  InlineCompletionItem.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nexport var InlineCompletionList;\n(function (InlineCompletionList) {\n  function create(items) {\n    return {\n      items\n    };\n  }\n  InlineCompletionList.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\n/**\n * Describes how an {@link InlineCompletionItemProvider inline completion provider} was triggered.\n *\n * @since 3.18.0\n * @proposed\n */\nexport var InlineCompletionTriggerKind;\n(function (InlineCompletionTriggerKind) {\n  /**\n   * Completion was triggered explicitly by a user gesture.\n   */\n  InlineCompletionTriggerKind.Invoked = 0;\n  /**\n   * Completion was triggered automatically while editing.\n   */\n  InlineCompletionTriggerKind.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nexport var SelectedCompletionInfo;\n(function (SelectedCompletionInfo) {\n  function create(range, text) {\n    return {\n      range,\n      text\n    };\n  }\n  SelectedCompletionInfo.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nexport var InlineCompletionContext;\n(function (InlineCompletionContext) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return {\n      triggerKind,\n      selectedCompletionInfo\n    };\n  }\n  InlineCompletionContext.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nexport var WorkspaceFolder;\n(function (WorkspaceFolder) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nexport const EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n  /**\n   * Creates a new ITextDocument literal from the given uri and content.\n   * @param uri The document's uri.\n   * @param languageId The document's language Id.\n   * @param version The document's version.\n   * @param content The document's content.\n   */\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument.create = create;\n  /**\n   * Checks whether the given literal conforms to the {@link ITextDocument} interface.\n   */\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error('Overlapping edit');\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      // sorted\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        // smaller_equal -> take left to preserve order\n        data[i++] = left[leftIdx++];\n      } else {\n        // greater -> take right\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nclass FullTextDocument {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = undefined;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = undefined;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === undefined) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === '\\r' || ch === '\\n';\n        if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0,\n      high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    // low is the least x for which the line offset is larger than the current offset\n    // or array.length if no line offset is larger than the current offset\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n}\nvar Is;\n(function (Is) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== 'undefined';\n  }\n  Is.defined = defined;\n  function undefined(value) {\n    return typeof value === 'undefined';\n  }\n  Is.undefined = undefined;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === '[object String]';\n  }\n  Is.string = string;\n  function number(value) {\n    return toString.call(value) === '[object Number]';\n  }\n  Is.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === '[object Number]' && min <= value && value <= max;\n  }\n  Is.numberRange = numberRange;\n  function integer(value) {\n    return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n  }\n  Is.integer = integer;\n  function uinteger(value) {\n    return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n  }\n  Is.uinteger = uinteger;\n  function func(value) {\n    return toString.call(value) === '[object Function]';\n  }\n  Is.func = func;\n  function objectLiteral(value) {\n    // Strictly speaking class instances pass this check as well. Since the LSP\n    // doesn't use classes we ignore this for now. If we do we need to add something\n    // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n    return value !== null && typeof value === 'object';\n  }\n  Is.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is.typedArray = typedArray;\n})(Is || (Is = {}));", "map": {"version": 3, "names": ["DocumentUri", "is", "value", "URI", "integer", "MIN_VALUE", "MAX_VALUE", "<PERSON><PERSON><PERSON><PERSON>", "Position", "create", "line", "character", "Number", "candidate", "Is", "objectLiteral", "Range", "one", "two", "three", "four", "start", "end", "Error", "Location", "uri", "range", "string", "undefined", "LocationLink", "targetUri", "targetRange", "targetSelectionRange", "originSelectionRange", "Color", "red", "green", "blue", "alpha", "numberRange", "ColorInformation", "color", "ColorPresentation", "label", "textEdit", "additionalTextEdits", "TextEdit", "typedArray", "FoldingRangeKind", "Comment", "Imports", "Region", "FoldingRange", "startLine", "endLine", "startCharacter", "endCharacter", "kind", "collapsedText", "result", "defined", "DiagnosticRelatedInformation", "location", "message", "DiagnosticSeverity", "Warning", "Information", "Hint", "DiagnosticTag", "Unnecessary", "Deprecated", "CodeDescription", "href", "Diagnostic", "severity", "code", "source", "relatedInformation", "_a", "number", "codeDescription", "Command", "title", "command", "args", "length", "arguments", "replace", "newText", "insert", "position", "del", "ChangeAnnotation", "needsConfirmation", "description", "boolean", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "annotation", "annotationId", "TextDocumentEdit", "textDocument", "edits", "OptionalVersionedTextDocumentIdentifier", "Array", "isArray", "CreateFile", "options", "overwrite", "ignoreIfExists", "RenameFile", "old<PERSON><PERSON>", "newUri", "DeleteFile", "recursive", "ignoreIfNotExists", "WorkspaceEdit", "changes", "documentChanges", "every", "change", "TextEditChangeImpl", "constructor", "changeAnnotations", "edit", "id", "assertChangeAnnotations", "manage", "push", "delete", "add", "all", "clear", "splice", "ChangeAnnotations", "annotations", "_annotations", "Object", "_counter", "_size", "size", "idOrAnnotation", "nextId", "toString", "WorkspaceChange", "workspaceEdit", "_textEditChanges", "_workspaceEdit", "_changeAnnotations", "for<PERSON>ach", "textEditChange", "keys", "key", "initDocumentChanges", "getTextEditChange", "version", "textDocumentEdit", "initChanges", "createFile", "optionsOrAnnotation", "operation", "renameFile", "deleteFile", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "TextDocumentItem", "languageId", "text", "<PERSON><PERSON><PERSON><PERSON>", "PlainText", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "Text", "Method", "Function", "<PERSON><PERSON><PERSON><PERSON>", "Field", "Variable", "Class", "Interface", "<PERSON><PERSON><PERSON>", "Property", "Unit", "Value", "Enum", "Keyword", "Snippet", "File", "Reference", "Folder", "EnumMember", "Constant", "Struct", "Event", "Operator", "TypeParameter", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "InsertTextMode", "asIs", "adjustIndentation", "CompletionItemLabelDetails", "detail", "CompletionItem", "CompletionList", "items", "isIncomplete", "MarkedString", "fromPlainText", "plainText", "language", "Hover", "contents", "ParameterInformation", "documentation", "SignatureInformation", "parameters", "DocumentHighlightKind", "Read", "Write", "DocumentHighlight", "SymbolKind", "Namespace", "Package", "String", "Boolean", "Key", "<PERSON><PERSON>", "SymbolTag", "SymbolInformation", "name", "containerName", "WorkspaceSymbol", "DocumentSymbol", "<PERSON><PERSON><PERSON><PERSON>", "children", "deprecated", "tags", "CodeActionKind", "Empty", "QuickFix", "Refa<PERSON>", "RefactorExtract", "RefactorInline", "RefactorRewrite", "Source", "SourceOrganizeImports", "SourceFixAll", "CodeActionTriggerKind", "Invoked", "Automatic", "CodeActionContext", "diagnostics", "only", "trigger<PERSON>ind", "CodeAction", "kindOrCommandOrEdit", "checkKind", "isPreferred", "CodeLens", "data", "FormattingOptions", "tabSize", "insertSpaces", "DocumentLink", "target", "SelectionRange", "parent", "SemanticTokenTypes", "SemanticTokenModifiers", "SemanticTokens", "resultId", "InlineValueText", "InlineValueVariableLookup", "variableName", "caseSensitiveLookup", "InlineValueEvaluatableExpression", "expression", "InlineValueContext", "frameId", "stoppedLocation", "InlayHintKind", "Type", "Parameter", "InlayHintLabelPart", "tooltip", "InlayHint", "textEdits", "paddingLeft", "paddingRight", "StringValue", "createSnippet", "InlineCompletionItem", "insertText", "filterText", "InlineCompletionList", "InlineCompletionTriggerKind", "SelectedCompletionInfo", "InlineCompletionContext", "selectedCompletionInfo", "WorkspaceFolder", "EOL", "TextDocument", "content", "FullTextDocument", "lineCount", "func", "getText", "positionAt", "offsetAt", "applyEdits", "document", "sortedEdits", "mergeSort", "a", "b", "diff", "lastModifiedOffset", "i", "e", "startOffset", "endOffset", "substring", "compare", "p", "left", "slice", "right", "leftIdx", "rightIdx", "ret", "_uri", "_languageId", "_version", "_content", "_lineOffsets", "update", "event", "getLineOffsets", "lineOffsets", "isLineStart", "ch", "char<PERSON>t", "offset", "Math", "max", "min", "low", "high", "mid", "floor", "lineOffset", "nextLineOffset", "prototype", "call", "check"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-languageserver-types/lib/esm/main.js"], "sourcesContent": ["/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var DocumentUri;\n(function (DocumentUri) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    DocumentUri.is = is;\n})(DocumentUri || (DocumentUri = {}));\nexport var URI;\n(function (URI) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    URI.is = is;\n})(URI || (URI = {}));\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;\n    }\n    integer.is = is;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;\n    }\n    uinteger.is = is;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * {@link Position} literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line, character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Position} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * {@link Range} literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Range} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * {@link Location} literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri, range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Location} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * {@link LocationLink} literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link LocationLink} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && Range.is(candidate.targetSelectionRange)\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link Color} literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red,\n            green,\n            blue,\n            alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Color} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * {@link ColorInformation} literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range,\n            color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link ColorPresentation} literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label,\n            textEdit,\n            additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * A set of predefined range kinds.\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind.Comment = 'comment';\n    /**\n     * Folding range for an import or include\n     */\n    FoldingRangeKind.Imports = 'imports';\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind.Region = 'region';\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * {@link FoldingRange} literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n        const result = {\n            startLine,\n            endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        if (Is.defined(collapsedText)) {\n            result.collapsedText = collapsedText;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link FoldingRange} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * {@link DiagnosticRelatedInformation} literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location,\n            message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DiagnosticRelatedInformation} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * {@link Diagnostic} literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        let result = { range, message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Diagnostic} interface.\n     */\n    function is(value) {\n        var _a;\n        let candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * {@link Command} literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command, ...args) {\n        let result = { title, command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Command} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range, newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates an insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        const result = { label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        const candidate = value;\n        return Is.string(candidate);\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range, newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        const candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument, edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        let result = {\n            kind: 'create',\n            uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        let result = {\n            kind: 'rename',\n            oldUri,\n            newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        let result = {\n            kind: 'delete',\n            uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        let candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every((change) => {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nclass TextEditChangeImpl {\n    constructor(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    insert(position, newText, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    replace(range, newText, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    delete(range, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    add(edit) {\n        this.edits.push(edit);\n    }\n    all() {\n        return this.edits;\n    }\n    clear() {\n        this.edits.splice(0, this.edits.length);\n    }\n    assertChangeAnnotations(value) {\n        if (value === undefined) {\n            throw new Error(`Text edit change is not configured to manage change annotations.`);\n        }\n    }\n}\n/**\n * A helper class\n */\nclass ChangeAnnotations {\n    constructor(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    all() {\n        return this._annotations;\n    }\n    get size() {\n        return this._size;\n    }\n    manage(idOrAnnotation, annotation) {\n        let id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(`Id ${id} is already in use.`);\n        }\n        if (annotation === undefined) {\n            throw new Error(`No annotation provided for id ${id}`);\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    }\n    nextId() {\n        this._counter++;\n        return this._counter.toString();\n    }\n}\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nexport class WorkspaceChange {\n    constructor(workspaceEdit) {\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach((change) => {\n                    if (TextDocumentEdit.is(change)) {\n                        const textEditChange = new TextEditChangeImpl(change.edits, this._changeAnnotations);\n                        this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach((key) => {\n                    const textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    /**\n     * Returns the underlying {@link WorkspaceEdit} literal\n     * use to be returned from a workspace edit operation like rename.\n     */\n    get edit() {\n        this.initDocumentChanges();\n        if (this._changeAnnotations !== undefined) {\n            if (this._changeAnnotations.size === 0) {\n                this._workspaceEdit.changeAnnotations = undefined;\n            }\n            else {\n                this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n            }\n        }\n        return this._workspaceEdit;\n    }\n    getTextEditChange(key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            const textDocument = { uri: key.uri, version: key.version };\n            let result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                const edits = [];\n                const textDocumentEdit = {\n                    textDocument,\n                    edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            let result = this._textEditChanges[key];\n            if (!result) {\n                let edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    }\n    initDocumentChanges() {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    }\n    initChanges() {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    }\n    createFile(uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    renameFile(oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    deleteFile(uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n}\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * {@link TextDocumentIdentifier} literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link TextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link VersionedTextDocumentIdentifier} literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri, version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link VersionedTextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link OptionalVersionedTextDocumentIdentifier} literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri, version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link OptionalVersionedTextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * {@link TextDocumentItem} literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri, languageId, version, text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link TextDocumentItem} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n    /**\n     * Checks whether the given value is a value of the {@link MarkupKind} type.\n     */\n    function is(value) {\n        const candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the {@link MarkupContent} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText, insert, replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link InsertReplaceEdit} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nexport var CompletionItemLabelDetails;\n(function (CompletionItemLabelDetails) {\n    function is(value) {\n        const candidate = value;\n        return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    CompletionItemLabelDetails.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the {@link MarkedString} type.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the {@link Hover} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * {@link ParameterInformation} literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label, documentation } : { label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * {@link SignatureInformation} literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation, ...parameters) {\n        let result = { label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * {@link DocumentHighlight} literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     * @param kind The highlight kind\n     */\n    function create(range, kind) {\n        let result = { range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n *\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        let result = {\n            name,\n            kind,\n            location: { uri, range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var WorkspaceSymbol;\n(function (WorkspaceSymbol) {\n    /**\n     * Create a new workspace symbol.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param uri The resource of the location of the symbol.\n     * @param range An options range of the location.\n     * @returns A WorkspaceSymbol.\n     */\n    function create(name, kind, uri, range) {\n        return range !== undefined\n            ? { name, kind, location: { uri, range } }\n            : { name, kind, location: { uri } };\n    }\n    WorkspaceSymbol.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        let result = {\n            name,\n            detail,\n            kind,\n            range,\n            selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DocumentSymbol} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The reason why code actions were requested.\n *\n * @since 3.17.0\n */\nexport var CodeActionTriggerKind;\n(function (CodeActionTriggerKind) {\n    /**\n     * Code actions were explicitly requested by the user or by an extension.\n     */\n    CodeActionTriggerKind.Invoked = 1;\n    /**\n     * Code actions were requested automatically.\n     *\n     * This typically happens when current selection in a file changes, but can\n     * also be triggered when file content changes.\n     */\n    CodeActionTriggerKind.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * {@link CodeActionContext} literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only, triggerKind) {\n        let result = { diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        if (triggerKind !== undefined && triggerKind !== null) {\n            result.triggerKind = triggerKind;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link CodeActionContext} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is)\n            && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string))\n            && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        let result = { title };\n        let checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * {@link CodeLens} literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        let result = { range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link CodeLens} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * {@link FormattingOptions} literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize, insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link FormattingOptions} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * {@link DocumentLink} literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range, target, data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DocumentLink} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range, parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\n/**\n * A set of predefined token types. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenTypes;\n(function (SemanticTokenTypes) {\n    SemanticTokenTypes[\"namespace\"] = \"namespace\";\n    /**\n     * Represents a generic type. Acts as a fallback for types which can't be mapped to\n     * a specific type like class or enum.\n     */\n    SemanticTokenTypes[\"type\"] = \"type\";\n    SemanticTokenTypes[\"class\"] = \"class\";\n    SemanticTokenTypes[\"enum\"] = \"enum\";\n    SemanticTokenTypes[\"interface\"] = \"interface\";\n    SemanticTokenTypes[\"struct\"] = \"struct\";\n    SemanticTokenTypes[\"typeParameter\"] = \"typeParameter\";\n    SemanticTokenTypes[\"parameter\"] = \"parameter\";\n    SemanticTokenTypes[\"variable\"] = \"variable\";\n    SemanticTokenTypes[\"property\"] = \"property\";\n    SemanticTokenTypes[\"enumMember\"] = \"enumMember\";\n    SemanticTokenTypes[\"event\"] = \"event\";\n    SemanticTokenTypes[\"function\"] = \"function\";\n    SemanticTokenTypes[\"method\"] = \"method\";\n    SemanticTokenTypes[\"macro\"] = \"macro\";\n    SemanticTokenTypes[\"keyword\"] = \"keyword\";\n    SemanticTokenTypes[\"modifier\"] = \"modifier\";\n    SemanticTokenTypes[\"comment\"] = \"comment\";\n    SemanticTokenTypes[\"string\"] = \"string\";\n    SemanticTokenTypes[\"number\"] = \"number\";\n    SemanticTokenTypes[\"regexp\"] = \"regexp\";\n    SemanticTokenTypes[\"operator\"] = \"operator\";\n    /**\n     * @since 3.17.0\n     */\n    SemanticTokenTypes[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\n/**\n * A set of predefined token modifiers. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenModifiers;\n(function (SemanticTokenModifiers) {\n    SemanticTokenModifiers[\"declaration\"] = \"declaration\";\n    SemanticTokenModifiers[\"definition\"] = \"definition\";\n    SemanticTokenModifiers[\"readonly\"] = \"readonly\";\n    SemanticTokenModifiers[\"static\"] = \"static\";\n    SemanticTokenModifiers[\"deprecated\"] = \"deprecated\";\n    SemanticTokenModifiers[\"abstract\"] = \"abstract\";\n    SemanticTokenModifiers[\"async\"] = \"async\";\n    SemanticTokenModifiers[\"modification\"] = \"modification\";\n    SemanticTokenModifiers[\"documentation\"] = \"documentation\";\n    SemanticTokenModifiers[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\n/**\n * @since 3.16.0\n */\nexport var SemanticTokens;\n(function (SemanticTokens) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') &&\n            Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');\n    }\n    SemanticTokens.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\n/**\n * The InlineValueText namespace provides functions to deal with InlineValueTexts.\n *\n * @since 3.17.0\n */\nexport var InlineValueText;\n(function (InlineValueText) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, text) {\n        return { range, text };\n    }\n    InlineValueText.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n    }\n    InlineValueText.is = is;\n})(InlineValueText || (InlineValueText = {}));\n/**\n * The InlineValueVariableLookup namespace provides functions to deal with InlineValueVariableLookups.\n *\n * @since 3.17.0\n */\nexport var InlineValueVariableLookup;\n(function (InlineValueVariableLookup) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, variableName, caseSensitiveLookup) {\n        return { range, variableName, caseSensitiveLookup };\n    }\n    InlineValueVariableLookup.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup)\n            && (Is.string(candidate.variableName) || candidate.variableName === undefined);\n    }\n    InlineValueVariableLookup.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\n/**\n * The InlineValueEvaluatableExpression namespace provides functions to deal with InlineValueEvaluatableExpression.\n *\n * @since 3.17.0\n */\nexport var InlineValueEvaluatableExpression;\n(function (InlineValueEvaluatableExpression) {\n    /**\n     * Creates a new InlineValueEvaluatableExpression literal.\n     */\n    function create(range, expression) {\n        return { range, expression };\n    }\n    InlineValueEvaluatableExpression.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range)\n            && (Is.string(candidate.expression) || candidate.expression === undefined);\n    }\n    InlineValueEvaluatableExpression.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\n/**\n * The InlineValueContext namespace provides helper functions to work with\n * {@link InlineValueContext} literals.\n *\n * @since 3.17.0\n */\nexport var InlineValueContext;\n(function (InlineValueContext) {\n    /**\n     * Creates a new InlineValueContext literal.\n     */\n    function create(frameId, stoppedLocation) {\n        return { frameId, stoppedLocation };\n    }\n    InlineValueContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link InlineValueContext} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.defined(candidate) && Range.is(value.stoppedLocation);\n    }\n    InlineValueContext.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\n/**\n * Inlay hint kinds.\n *\n * @since 3.17.0\n */\nexport var InlayHintKind;\n(function (InlayHintKind) {\n    /**\n     * An inlay hint that for a type annotation.\n     */\n    InlayHintKind.Type = 1;\n    /**\n     * An inlay hint that is for a parameter.\n     */\n    InlayHintKind.Parameter = 2;\n    function is(value) {\n        return value === 1 || value === 2;\n    }\n    InlayHintKind.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nexport var InlayHintLabelPart;\n(function (InlayHintLabelPart) {\n    function create(value) {\n        return { value };\n    }\n    InlayHintLabelPart.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.location === undefined || Location.is(candidate.location))\n            && (candidate.command === undefined || Command.is(candidate.command));\n    }\n    InlayHintLabelPart.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nexport var InlayHint;\n(function (InlayHint) {\n    function create(position, label, kind) {\n        const result = { position, label };\n        if (kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    InlayHint.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.position)\n            && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is))\n            && (candidate.kind === undefined || InlayHintKind.is(candidate.kind))\n            && (candidate.textEdits === undefined) || Is.typedArray(candidate.textEdits, TextEdit.is)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft))\n            && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));\n    }\n    InlayHint.is = is;\n})(InlayHint || (InlayHint = {}));\nexport var StringValue;\n(function (StringValue) {\n    function createSnippet(value) {\n        return { kind: 'snippet', value };\n    }\n    StringValue.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nexport var InlineCompletionItem;\n(function (InlineCompletionItem) {\n    function create(insertText, filterText, range, command) {\n        return { insertText, filterText, range, command };\n    }\n    InlineCompletionItem.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nexport var InlineCompletionList;\n(function (InlineCompletionList) {\n    function create(items) {\n        return { items };\n    }\n    InlineCompletionList.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\n/**\n * Describes how an {@link InlineCompletionItemProvider inline completion provider} was triggered.\n *\n * @since 3.18.0\n * @proposed\n */\nexport var InlineCompletionTriggerKind;\n(function (InlineCompletionTriggerKind) {\n    /**\n     * Completion was triggered explicitly by a user gesture.\n     */\n    InlineCompletionTriggerKind.Invoked = 0;\n    /**\n     * Completion was triggered automatically while editing.\n     */\n    InlineCompletionTriggerKind.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nexport var SelectedCompletionInfo;\n(function (SelectedCompletionInfo) {\n    function create(range, text) {\n        return { range, text };\n    }\n    SelectedCompletionInfo.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nexport var InlineCompletionContext;\n(function (InlineCompletionContext) {\n    function create(triggerKind, selectedCompletionInfo) {\n        return { triggerKind, selectedCompletionInfo };\n    }\n    InlineCompletionContext.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nexport var WorkspaceFolder;\n(function (WorkspaceFolder) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n    }\n    WorkspaceFolder.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nexport const EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId The document's language Id.\n     * @param version The document's version.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ITextDocument} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        let text = document.getText();\n        let sortedEdits = mergeSort(edits, (a, b) => {\n            let diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        let lastModifiedOffset = text.length;\n        for (let i = sortedEdits.length - 1; i >= 0; i--) {\n            let e = sortedEdits[i];\n            let startOffset = document.offsetAt(e.range.start);\n            let endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        const p = (data.length / 2) | 0;\n        const left = data.slice(0, p);\n        const right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        let leftIdx = 0;\n        let rightIdx = 0;\n        let i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            let ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nclass FullTextDocument {\n    constructor(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    get uri() {\n        return this._uri;\n    }\n    get languageId() {\n        return this._languageId;\n    }\n    get version() {\n        return this._version;\n    }\n    getText(range) {\n        if (range) {\n            let start = this.offsetAt(range.start);\n            let end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    }\n    update(event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    }\n    getLineOffsets() {\n        if (this._lineOffsets === undefined) {\n            let lineOffsets = [];\n            let text = this._content;\n            let isLineStart = true;\n            for (let i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                let ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    }\n    positionAt(offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        let lineOffsets = this.getLineOffsets();\n        let low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            let mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        let line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    }\n    offsetAt(position) {\n        let lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        let lineOffset = lineOffsets[position.line];\n        let nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    }\n    get lineCount() {\n        return this.getLineOffsets().length;\n    }\n}\nvar Is;\n(function (Is) {\n    const toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,YAAY;;AACZ,OAAO,IAAIA,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpB,SAASC,EAAEA,CAACC,KAAK,EAAE;IACf,OAAO,OAAOA,KAAK,KAAK,QAAQ;EACpC;EACAF,WAAW,CAACC,EAAE,GAAGA,EAAE;AACvB,CAAC,EAAED,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,OAAO,IAAIG,GAAG;AACd,CAAC,UAAUA,GAAG,EAAE;EACZ,SAASF,EAAEA,CAACC,KAAK,EAAE;IACf,OAAO,OAAOA,KAAK,KAAK,QAAQ;EACpC;EACAC,GAAG,CAACF,EAAE,GAAGA,EAAE;AACf,CAAC,EAAEE,GAAG,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,OAAO,IAAIC,OAAO;AAClB,CAAC,UAAUA,OAAO,EAAE;EAChBA,OAAO,CAACC,SAAS,GAAG,CAAC,UAAU;EAC/BD,OAAO,CAACE,SAAS,GAAG,UAAU;EAC9B,SAASL,EAAEA,CAACC,KAAK,EAAE;IACf,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIE,OAAO,CAACC,SAAS,IAAIH,KAAK,IAAIA,KAAK,IAAIE,OAAO,CAACE,SAAS;EAChG;EACAF,OAAO,CAACH,EAAE,GAAGA,EAAE;AACnB,CAAC,EAAEG,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7B,OAAO,IAAIG,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACF,SAAS,GAAG,CAAC;EACtBE,QAAQ,CAACD,SAAS,GAAG,UAAU;EAC/B,SAASL,EAAEA,CAACC,KAAK,EAAE;IACf,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIK,QAAQ,CAACF,SAAS,IAAIH,KAAK,IAAIA,KAAK,IAAIK,QAAQ,CAACD,SAAS;EAClG;EACAC,QAAQ,CAACN,EAAE,GAAGA,EAAE;AACpB,CAAC,EAAEM,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,OAAO,IAAIC,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjB;AACJ;AACA;AACA;AACA;EACI,SAASC,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC7B,IAAID,IAAI,KAAKE,MAAM,CAACN,SAAS,EAAE;MAC3BI,IAAI,GAAGH,QAAQ,CAACD,SAAS;IAC7B;IACA,IAAIK,SAAS,KAAKC,MAAM,CAACN,SAAS,EAAE;MAChCK,SAAS,GAAGJ,QAAQ,CAACD,SAAS;IAClC;IACA,OAAO;MAAEI,IAAI;MAAEC;IAAU,CAAC;EAC9B;EACAH,QAAQ,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACP,QAAQ,CAACM,SAAS,CAACH,IAAI,CAAC,IAAII,EAAE,CAACP,QAAQ,CAACM,SAAS,CAACF,SAAS,CAAC;EACzG;EACAH,QAAQ,CAACP,EAAE,GAAGA,EAAE;AACpB,CAAC,EAAEO,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,OAAO,IAAIQ,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACd,SAASP,MAAMA,CAACQ,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;IACnC,IAAIN,EAAE,CAACP,QAAQ,CAACU,GAAG,CAAC,IAAIH,EAAE,CAACP,QAAQ,CAACW,GAAG,CAAC,IAAIJ,EAAE,CAACP,QAAQ,CAACY,KAAK,CAAC,IAAIL,EAAE,CAACP,QAAQ,CAACa,IAAI,CAAC,EAAE;MACjF,OAAO;QAAEC,KAAK,EAAEb,QAAQ,CAACC,MAAM,CAACQ,GAAG,EAAEC,GAAG,CAAC;QAAEI,GAAG,EAAEd,QAAQ,CAACC,MAAM,CAACU,KAAK,EAAEC,IAAI;MAAE,CAAC;IAClF,CAAC,MACI,IAAIZ,QAAQ,CAACP,EAAE,CAACgB,GAAG,CAAC,IAAIT,QAAQ,CAACP,EAAE,CAACiB,GAAG,CAAC,EAAE;MAC3C,OAAO;QAAEG,KAAK,EAAEJ,GAAG;QAAEK,GAAG,EAAEJ;MAAI,CAAC;IACnC,CAAC,MACI;MACD,MAAM,IAAIK,KAAK,CAAC,8CAA8CN,GAAG,KAAKC,GAAG,KAAKC,KAAK,KAAKC,IAAI,GAAG,CAAC;IACpG;EACJ;EACAJ,KAAK,CAACP,MAAM,GAAGA,MAAM;EACrB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIL,QAAQ,CAACP,EAAE,CAACY,SAAS,CAACQ,KAAK,CAAC,IAAIb,QAAQ,CAACP,EAAE,CAACY,SAAS,CAACS,GAAG,CAAC;EACpG;EACAN,KAAK,CAACf,EAAE,GAAGA,EAAE;AACjB,CAAC,EAAEe,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,OAAO,IAAIQ,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjB;AACJ;AACA;AACA;AACA;EACI,SAASf,MAAMA,CAACgB,GAAG,EAAEC,KAAK,EAAE;IACxB,OAAO;MAAED,GAAG;MAAEC;IAAM,CAAC;EACzB;EACAF,QAAQ,CAACf,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,KAAKZ,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,IAAIX,EAAE,CAACc,SAAS,CAACf,SAAS,CAACY,GAAG,CAAC,CAAC;EAChI;EACAD,QAAQ,CAACvB,EAAE,GAAGA,EAAE;AACpB,CAAC,EAAEuB,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,OAAO,IAAIK,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASpB,MAAMA,CAACqB,SAAS,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE;IAChF,OAAO;MAAEH,SAAS;MAAEC,WAAW;MAAEC,oBAAoB;MAAEC;IAAqB,CAAC;EACjF;EACAJ,YAAY,CAACpB,MAAM,GAAGA,MAAM;EAC5B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACkB,WAAW,CAAC,IAAIjB,EAAE,CAACa,MAAM,CAACd,SAAS,CAACiB,SAAS,CAAC,IAChGd,KAAK,CAACf,EAAE,CAACY,SAAS,CAACmB,oBAAoB,CAAC,KACvChB,KAAK,CAACf,EAAE,CAACY,SAAS,CAACoB,oBAAoB,CAAC,IAAInB,EAAE,CAACc,SAAS,CAACf,SAAS,CAACoB,oBAAoB,CAAC,CAAC;EACrG;EACAJ,YAAY,CAAC5B,EAAE,GAAGA,EAAE;AACxB,CAAC,EAAE4B,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,OAAO,IAAIK,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACd;AACJ;AACA;EACI,SAASzB,MAAMA,CAAC0B,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACrC,OAAO;MACHH,GAAG;MACHC,KAAK;MACLC,IAAI;MACJC;IACJ,CAAC;EACL;EACAJ,KAAK,CAACzB,MAAM,GAAGA,MAAM;EACrB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACyB,WAAW,CAAC1B,SAAS,CAACsB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAClErB,EAAE,CAACyB,WAAW,CAAC1B,SAAS,CAACuB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,IACrCtB,EAAE,CAACyB,WAAW,CAAC1B,SAAS,CAACwB,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IACpCvB,EAAE,CAACyB,WAAW,CAAC1B,SAAS,CAACyB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;EAChD;EACAJ,KAAK,CAACjC,EAAE,GAAGA,EAAE;AACjB,CAAC,EAAEiC,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,OAAO,IAAIM,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;EACI,SAAS/B,MAAMA,CAACiB,KAAK,EAAEe,KAAK,EAAE;IAC1B,OAAO;MACHf,KAAK;MACLe;IACJ,CAAC;EACL;EACAD,gBAAgB,CAAC/B,MAAM,GAAGA,MAAM;EAChC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,IAAIQ,KAAK,CAACjC,EAAE,CAACY,SAAS,CAAC4B,KAAK,CAAC;EAChG;EACAD,gBAAgB,CAACvC,EAAE,GAAGA,EAAE;AAC5B,CAAC,EAAEuC,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA,OAAO,IAAIE,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;EACI,SAASjC,MAAMA,CAACkC,KAAK,EAAEC,QAAQ,EAAEC,mBAAmB,EAAE;IAClD,OAAO;MACHF,KAAK;MACLC,QAAQ;MACRC;IACJ,CAAC;EACL;EACAH,iBAAiB,CAACjC,MAAM,GAAGA,MAAM;EACjC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC8B,KAAK,CAAC,KACxD7B,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC+B,QAAQ,CAAC,IAAIE,QAAQ,CAAC7C,EAAE,CAACY,SAAS,CAAC,CAAC,KAC3DC,EAAE,CAACc,SAAS,CAACf,SAAS,CAACgC,mBAAmB,CAAC,IAAI/B,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACgC,mBAAmB,EAAEC,QAAQ,CAAC7C,EAAE,CAAC,CAAC;EACrH;EACAyC,iBAAiB,CAACzC,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAEyC,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA,OAAO,IAAIM,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;EACIA,gBAAgB,CAACC,OAAO,GAAG,SAAS;EACpC;AACJ;AACA;EACID,gBAAgB,CAACE,OAAO,GAAG,SAAS;EACpC;AACJ;AACA;EACIF,gBAAgB,CAACG,MAAM,GAAG,QAAQ;AACtC,CAAC,EAAEH,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA,OAAO,IAAII,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;EACI,SAAS3C,MAAMA,CAAC4C,SAAS,EAAEC,OAAO,EAAEC,cAAc,EAAEC,YAAY,EAAEC,IAAI,EAAEC,aAAa,EAAE;IACnF,MAAMC,MAAM,GAAG;MACXN,SAAS;MACTC;IACJ,CAAC;IACD,IAAIxC,EAAE,CAAC8C,OAAO,CAACL,cAAc,CAAC,EAAE;MAC5BI,MAAM,CAACJ,cAAc,GAAGA,cAAc;IAC1C;IACA,IAAIzC,EAAE,CAAC8C,OAAO,CAACJ,YAAY,CAAC,EAAE;MAC1BG,MAAM,CAACH,YAAY,GAAGA,YAAY;IACtC;IACA,IAAI1C,EAAE,CAAC8C,OAAO,CAACH,IAAI,CAAC,EAAE;MAClBE,MAAM,CAACF,IAAI,GAAGA,IAAI;IACtB;IACA,IAAI3C,EAAE,CAAC8C,OAAO,CAACF,aAAa,CAAC,EAAE;MAC3BC,MAAM,CAACD,aAAa,GAAGA,aAAa;IACxC;IACA,OAAOC,MAAM;EACjB;EACAP,YAAY,CAAC3C,MAAM,GAAGA,MAAM;EAC5B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACP,QAAQ,CAACM,SAAS,CAACwC,SAAS,CAAC,IAAIvC,EAAE,CAACP,QAAQ,CAACM,SAAS,CAACwC,SAAS,CAAC,KAClGvC,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC0C,cAAc,CAAC,IAAIzC,EAAE,CAACP,QAAQ,CAACM,SAAS,CAAC0C,cAAc,CAAC,CAAC,KAChFzC,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC2C,YAAY,CAAC,IAAI1C,EAAE,CAACP,QAAQ,CAACM,SAAS,CAAC2C,YAAY,CAAC,CAAC,KAC5E1C,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC4C,IAAI,CAAC,IAAI3C,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC4C,IAAI,CAAC,CAAC;EACtE;EACAL,YAAY,CAACnD,EAAE,GAAGA,EAAE;AACxB,CAAC,EAAEmD,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,OAAO,IAAIS,4BAA4B;AACvC,CAAC,UAAUA,4BAA4B,EAAE;EACrC;AACJ;AACA;EACI,SAASpD,MAAMA,CAACqD,QAAQ,EAAEC,OAAO,EAAE;IAC/B,OAAO;MACHD,QAAQ;MACRC;IACJ,CAAC;EACL;EACAF,4BAA4B,CAACpD,MAAM,GAAGA,MAAM;EAC5C;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIW,QAAQ,CAACvB,EAAE,CAACY,SAAS,CAACiD,QAAQ,CAAC,IAAIhD,EAAE,CAACa,MAAM,CAACd,SAAS,CAACkD,OAAO,CAAC;EACnG;EACAF,4BAA4B,CAAC5D,EAAE,GAAGA,EAAE;AACxC,CAAC,EAAE4D,4BAA4B,KAAKA,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE;AACA;AACA;AACA,OAAO,IAAIG,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;AACJ;AACA;EACIA,kBAAkB,CAACzC,KAAK,GAAG,CAAC;EAC5B;AACJ;AACA;EACIyC,kBAAkB,CAACC,OAAO,GAAG,CAAC;EAC9B;AACJ;AACA;EACID,kBAAkB,CAACE,WAAW,GAAG,CAAC;EAClC;AACJ;AACA;EACIF,kBAAkB,CAACG,IAAI,GAAG,CAAC;AAC/B,CAAC,EAAEH,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIA,aAAa,CAACC,WAAW,GAAG,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACID,aAAa,CAACE,UAAU,GAAG,CAAC;AAChC,CAAC,EAAEF,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxB,SAAStE,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC2D,IAAI,CAAC;EACnE;EACAD,eAAe,CAACtE,EAAE,GAAGA,EAAE;AAC3B,CAAC,EAAEsE,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA,OAAO,IAAIE,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB;AACJ;AACA;EACI,SAAShE,MAAMA,CAACiB,KAAK,EAAEqC,OAAO,EAAEW,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;IACxE,IAAIlB,MAAM,GAAG;MAAEjC,KAAK;MAAEqC;IAAQ,CAAC;IAC/B,IAAIjD,EAAE,CAAC8C,OAAO,CAACc,QAAQ,CAAC,EAAE;MACtBf,MAAM,CAACe,QAAQ,GAAGA,QAAQ;IAC9B;IACA,IAAI5D,EAAE,CAAC8C,OAAO,CAACe,IAAI,CAAC,EAAE;MAClBhB,MAAM,CAACgB,IAAI,GAAGA,IAAI;IACtB;IACA,IAAI7D,EAAE,CAAC8C,OAAO,CAACgB,MAAM,CAAC,EAAE;MACpBjB,MAAM,CAACiB,MAAM,GAAGA,MAAM;IAC1B;IACA,IAAI9D,EAAE,CAAC8C,OAAO,CAACiB,kBAAkB,CAAC,EAAE;MAChClB,MAAM,CAACkB,kBAAkB,GAAGA,kBAAkB;IAClD;IACA,OAAOlB,MAAM;EACjB;EACAc,UAAU,CAAChE,MAAM,GAAGA,MAAM;EAC1B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAI4E,EAAE;IACN,IAAIjE,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IACrBG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,IACzBZ,EAAE,CAACa,MAAM,CAACd,SAAS,CAACkD,OAAO,CAAC,KAC3BjD,EAAE,CAACiE,MAAM,CAAClE,SAAS,CAAC6D,QAAQ,CAAC,IAAI5D,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC6D,QAAQ,CAAC,CAAC,KAClE5D,EAAE,CAACV,OAAO,CAACS,SAAS,CAAC8D,IAAI,CAAC,IAAI7D,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC8D,IAAI,CAAC,IAAI7D,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC8D,IAAI,CAAC,CAAC,KACxF7D,EAAE,CAACc,SAAS,CAACf,SAAS,CAACmE,eAAe,CAAC,IAAKlE,EAAE,CAACa,MAAM,CAAC,CAACmD,EAAE,GAAGjE,SAAS,CAACmE,eAAe,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACN,IAAI,CAAE,CAAC,KACtI1D,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC+D,MAAM,CAAC,IAAI9D,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC+D,MAAM,CAAC,CAAC,KAC9D9D,EAAE,CAACc,SAAS,CAACf,SAAS,CAACgE,kBAAkB,CAAC,IAAI/D,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACgE,kBAAkB,EAAEhB,4BAA4B,CAAC5D,EAAE,CAAC,CAAC;EACvI;EACAwE,UAAU,CAACxE,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAEwE,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA,OAAO,IAAIQ,OAAO;AAClB,CAAC,UAAUA,OAAO,EAAE;EAChB;AACJ;AACA;EACI,SAASxE,MAAMA,CAACyE,KAAK,EAAEC,OAAO,EAAE,GAAGC,IAAI,EAAE;IACrC,IAAIzB,MAAM,GAAG;MAAEuB,KAAK;MAAEC;IAAQ,CAAC;IAC/B,IAAIrE,EAAE,CAAC8C,OAAO,CAACwB,IAAI,CAAC,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MACrC1B,MAAM,CAAC2B,SAAS,GAAGF,IAAI;IAC3B;IACA,OAAOzB,MAAM;EACjB;EACAsB,OAAO,CAACxE,MAAM,GAAGA,MAAM;EACvB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACqE,KAAK,CAAC,IAAIpE,EAAE,CAACa,MAAM,CAACd,SAAS,CAACsE,OAAO,CAAC;EAC9F;EACAF,OAAO,CAAChF,EAAE,GAAGA,EAAE;AACnB,CAAC,EAAEgF,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA,OAAO,IAAInC,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjB;AACJ;AACA;AACA;AACA;EACI,SAASyC,OAAOA,CAAC7D,KAAK,EAAE8D,OAAO,EAAE;IAC7B,OAAO;MAAE9D,KAAK;MAAE8D;IAAQ,CAAC;EAC7B;EACA1C,QAAQ,CAACyC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;EACI,SAASE,MAAMA,CAACC,QAAQ,EAAEF,OAAO,EAAE;IAC/B,OAAO;MAAE9D,KAAK,EAAE;QAAEL,KAAK,EAAEqE,QAAQ;QAAEpE,GAAG,EAAEoE;MAAS,CAAC;MAAEF;IAAQ,CAAC;EACjE;EACA1C,QAAQ,CAAC2C,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA;EACI,SAASE,GAAGA,CAACjE,KAAK,EAAE;IAChB,OAAO;MAAEA,KAAK;MAAE8D,OAAO,EAAE;IAAG,CAAC;EACjC;EACA1C,QAAQ,CAAC6C,GAAG,GAAGA,GAAG;EAClB,SAAS1F,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAC3BC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC2E,OAAO,CAAC,IAC5BxE,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC;EACpC;EACAoB,QAAQ,CAAC7C,EAAE,GAAGA,EAAE;AACpB,CAAC,EAAE6C,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,OAAO,IAAI8C,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB,SAASnF,MAAMA,CAACkC,KAAK,EAAEkD,iBAAiB,EAAEC,WAAW,EAAE;IACnD,MAAMnC,MAAM,GAAG;MAAEhB;IAAM,CAAC;IACxB,IAAIkD,iBAAiB,KAAKjE,SAAS,EAAE;MACjC+B,MAAM,CAACkC,iBAAiB,GAAGA,iBAAiB;IAChD;IACA,IAAIC,WAAW,KAAKlE,SAAS,EAAE;MAC3B+B,MAAM,CAACmC,WAAW,GAAGA,WAAW;IACpC;IACA,OAAOnC,MAAM;EACjB;EACAiC,gBAAgB,CAACnF,MAAM,GAAGA,MAAM;EAChC,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC8B,KAAK,CAAC,KAC3D7B,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACgF,iBAAiB,CAAC,IAAIhF,SAAS,CAACgF,iBAAiB,KAAKjE,SAAS,CAAC,KACrFd,EAAE,CAACa,MAAM,CAACd,SAAS,CAACiF,WAAW,CAAC,IAAIjF,SAAS,CAACiF,WAAW,KAAKlE,SAAS,CAAC;EACjF;EACAgE,gBAAgB,CAAC3F,EAAE,GAAGA,EAAE;AAC5B,CAAC,EAAE2F,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO,IAAII,0BAA0B;AACrC,CAAC,UAAUA,0BAA0B,EAAE;EACnC,SAAS/F,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC;EAC/B;EACAmF,0BAA0B,CAAC/F,EAAE,GAAGA,EAAE;AACtC,CAAC,EAAE+F,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE,OAAO,IAAIC,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASV,OAAOA,CAAC7D,KAAK,EAAE8D,OAAO,EAAEU,UAAU,EAAE;IACzC,OAAO;MAAExE,KAAK;MAAE8D,OAAO;MAAEW,YAAY,EAAED;IAAW,CAAC;EACvD;EACAD,iBAAiB,CAACV,OAAO,GAAGA,OAAO;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASE,MAAMA,CAACC,QAAQ,EAAEF,OAAO,EAAEU,UAAU,EAAE;IAC3C,OAAO;MAAExE,KAAK,EAAE;QAAEL,KAAK,EAAEqE,QAAQ;QAAEpE,GAAG,EAAEoE;MAAS,CAAC;MAAEF,OAAO;MAAEW,YAAY,EAAED;IAAW,CAAC;EAC3F;EACAD,iBAAiB,CAACR,MAAM,GAAGA,MAAM;EACjC;AACJ;AACA;AACA;AACA;AACA;EACI,SAASE,GAAGA,CAACjE,KAAK,EAAEwE,UAAU,EAAE;IAC5B,OAAO;MAAExE,KAAK;MAAE8D,OAAO,EAAE,EAAE;MAAEW,YAAY,EAAED;IAAW,CAAC;EAC3D;EACAD,iBAAiB,CAACN,GAAG,GAAGA,GAAG;EAC3B,SAAS1F,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAO4C,QAAQ,CAAC7C,EAAE,CAACY,SAAS,CAAC,KAAK+E,gBAAgB,CAAC3F,EAAE,CAACY,SAAS,CAACsF,YAAY,CAAC,IAAIH,0BAA0B,CAAC/F,EAAE,CAACY,SAAS,CAACsF,YAAY,CAAC,CAAC;EAC3I;EACAF,iBAAiB,CAAChG,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAEgG,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA,OAAO,IAAIG,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;EACI,SAAS3F,MAAMA,CAAC4F,YAAY,EAAEC,KAAK,EAAE;IACjC,OAAO;MAAED,YAAY;MAAEC;IAAM,CAAC;EAClC;EACAF,gBAAgB,CAAC3F,MAAM,GAAGA,MAAM;EAChC,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IACrB0F,uCAAuC,CAACtG,EAAE,CAACY,SAAS,CAACwF,YAAY,CAAC,IAClEG,KAAK,CAACC,OAAO,CAAC5F,SAAS,CAACyF,KAAK,CAAC;EACzC;EACAF,gBAAgB,CAACnG,EAAE,GAAGA,EAAE;AAC5B,CAAC,EAAEmG,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO,IAAIM,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB,SAASjG,MAAMA,CAACgB,GAAG,EAAEkF,OAAO,EAAET,UAAU,EAAE;IACtC,IAAIvC,MAAM,GAAG;MACTF,IAAI,EAAE,QAAQ;MACdhC;IACJ,CAAC;IACD,IAAIkF,OAAO,KAAK/E,SAAS,KAAK+E,OAAO,CAACC,SAAS,KAAKhF,SAAS,IAAI+E,OAAO,CAACE,cAAc,KAAKjF,SAAS,CAAC,EAAE;MACpG+B,MAAM,CAACgD,OAAO,GAAGA,OAAO;IAC5B;IACA,IAAIT,UAAU,KAAKtE,SAAS,EAAE;MAC1B+B,MAAM,CAACwC,YAAY,GAAGD,UAAU;IACpC;IACA,OAAOvC,MAAM;EACjB;EACA+C,UAAU,CAACjG,MAAM,GAAGA,MAAM;EAC1B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,IAAIA,SAAS,CAAC4C,IAAI,KAAK,QAAQ,IAAI3C,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,KAAKZ,SAAS,CAAC8F,OAAO,KAAK/E,SAAS,IAC1G,CAACf,SAAS,CAAC8F,OAAO,CAACC,SAAS,KAAKhF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACC,SAAS,CAAC,MAAM/F,SAAS,CAAC8F,OAAO,CAACE,cAAc,KAAKjF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACE,cAAc,CAAC,CAAE,CAAC,KAAKhG,SAAS,CAACsF,YAAY,KAAKvE,SAAS,IAAIoE,0BAA0B,CAAC/F,EAAE,CAACY,SAAS,CAACsF,YAAY,CAAC,CAAC;EACxS;EACAO,UAAU,CAACzG,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAEyG,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAII,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB,SAASrG,MAAMA,CAACsG,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAET,UAAU,EAAE;IACjD,IAAIvC,MAAM,GAAG;MACTF,IAAI,EAAE,QAAQ;MACdsD,MAAM;MACNC;IACJ,CAAC;IACD,IAAIL,OAAO,KAAK/E,SAAS,KAAK+E,OAAO,CAACC,SAAS,KAAKhF,SAAS,IAAI+E,OAAO,CAACE,cAAc,KAAKjF,SAAS,CAAC,EAAE;MACpG+B,MAAM,CAACgD,OAAO,GAAGA,OAAO;IAC5B;IACA,IAAIT,UAAU,KAAKtE,SAAS,EAAE;MAC1B+B,MAAM,CAACwC,YAAY,GAAGD,UAAU;IACpC;IACA,OAAOvC,MAAM;EACjB;EACAmD,UAAU,CAACrG,MAAM,GAAGA,MAAM;EAC1B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,IAAIA,SAAS,CAAC4C,IAAI,KAAK,QAAQ,IAAI3C,EAAE,CAACa,MAAM,CAACd,SAAS,CAACkG,MAAM,CAAC,IAAIjG,EAAE,CAACa,MAAM,CAACd,SAAS,CAACmG,MAAM,CAAC,KAAKnG,SAAS,CAAC8F,OAAO,KAAK/E,SAAS,IAC5I,CAACf,SAAS,CAAC8F,OAAO,CAACC,SAAS,KAAKhF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACC,SAAS,CAAC,MAAM/F,SAAS,CAAC8F,OAAO,CAACE,cAAc,KAAKjF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACE,cAAc,CAAC,CAAE,CAAC,KAAKhG,SAAS,CAACsF,YAAY,KAAKvE,SAAS,IAAIoE,0BAA0B,CAAC/F,EAAE,CAACY,SAAS,CAACsF,YAAY,CAAC,CAAC;EACxS;EACAW,UAAU,CAAC7G,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAE6G,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAIG,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB,SAASxG,MAAMA,CAACgB,GAAG,EAAEkF,OAAO,EAAET,UAAU,EAAE;IACtC,IAAIvC,MAAM,GAAG;MACTF,IAAI,EAAE,QAAQ;MACdhC;IACJ,CAAC;IACD,IAAIkF,OAAO,KAAK/E,SAAS,KAAK+E,OAAO,CAACO,SAAS,KAAKtF,SAAS,IAAI+E,OAAO,CAACQ,iBAAiB,KAAKvF,SAAS,CAAC,EAAE;MACvG+B,MAAM,CAACgD,OAAO,GAAGA,OAAO;IAC5B;IACA,IAAIT,UAAU,KAAKtE,SAAS,EAAE;MAC1B+B,MAAM,CAACwC,YAAY,GAAGD,UAAU;IACpC;IACA,OAAOvC,MAAM;EACjB;EACAsD,UAAU,CAACxG,MAAM,GAAGA,MAAM;EAC1B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,IAAIA,SAAS,CAAC4C,IAAI,KAAK,QAAQ,IAAI3C,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,KAAKZ,SAAS,CAAC8F,OAAO,KAAK/E,SAAS,IAC1G,CAACf,SAAS,CAAC8F,OAAO,CAACO,SAAS,KAAKtF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACO,SAAS,CAAC,MAAMrG,SAAS,CAAC8F,OAAO,CAACQ,iBAAiB,KAAKvF,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC8F,OAAO,CAACQ,iBAAiB,CAAC,CAAE,CAAC,KAAKtG,SAAS,CAACsF,YAAY,KAAKvE,SAAS,IAAIoE,0BAA0B,CAAC/F,EAAE,CAACY,SAAS,CAACsF,YAAY,CAAC,CAAC;EAC9S;EACAc,UAAU,CAAChH,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAEgH,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAIG,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtB,SAASnH,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,KACXA,SAAS,CAACwG,OAAO,KAAKzF,SAAS,IAAIf,SAAS,CAACyG,eAAe,KAAK1F,SAAS,CAAC,KAC3Ef,SAAS,CAACyG,eAAe,KAAK1F,SAAS,IAAIf,SAAS,CAACyG,eAAe,CAACC,KAAK,CAAEC,MAAM,IAAK;MACpF,IAAI1G,EAAE,CAACa,MAAM,CAAC6F,MAAM,CAAC/D,IAAI,CAAC,EAAE;QACxB,OAAOiD,UAAU,CAACzG,EAAE,CAACuH,MAAM,CAAC,IAAIV,UAAU,CAAC7G,EAAE,CAACuH,MAAM,CAAC,IAAIP,UAAU,CAAChH,EAAE,CAACuH,MAAM,CAAC;MAClF,CAAC,MACI;QACD,OAAOpB,gBAAgB,CAACnG,EAAE,CAACuH,MAAM,CAAC;MACtC;IACJ,CAAC,CAAC,CAAC;EACX;EACAJ,aAAa,CAACnH,EAAE,GAAGA,EAAE;AACzB,CAAC,EAAEmH,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,MAAMK,kBAAkB,CAAC;EACrBC,WAAWA,CAACpB,KAAK,EAAEqB,iBAAiB,EAAE;IAClC,IAAI,CAACrB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqB,iBAAiB,GAAGA,iBAAiB;EAC9C;EACAlC,MAAMA,CAACC,QAAQ,EAAEF,OAAO,EAAEU,UAAU,EAAE;IAClC,IAAI0B,IAAI;IACR,IAAIC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BgG,IAAI,GAAG9E,QAAQ,CAAC2C,MAAM,CAACC,QAAQ,EAAEF,OAAO,CAAC;IAC7C,CAAC,MACI,IAAIQ,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,EAAE;MAChD2B,EAAE,GAAG3B,UAAU;MACf0B,IAAI,GAAG3B,iBAAiB,CAACR,MAAM,CAACC,QAAQ,EAAEF,OAAO,EAAEU,UAAU,CAAC;IAClE,CAAC,MACI;MACD,IAAI,CAAC4B,uBAAuB,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACpDE,EAAE,GAAG,IAAI,CAACF,iBAAiB,CAACI,MAAM,CAAC7B,UAAU,CAAC;MAC9C0B,IAAI,GAAG3B,iBAAiB,CAACR,MAAM,CAACC,QAAQ,EAAEF,OAAO,EAAEqC,EAAE,CAAC;IAC1D;IACA,IAAI,CAACvB,KAAK,CAAC0B,IAAI,CAACJ,IAAI,CAAC;IACrB,IAAIC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;EACAtC,OAAOA,CAAC7D,KAAK,EAAE8D,OAAO,EAAEU,UAAU,EAAE;IAChC,IAAI0B,IAAI;IACR,IAAIC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BgG,IAAI,GAAG9E,QAAQ,CAACyC,OAAO,CAAC7D,KAAK,EAAE8D,OAAO,CAAC;IAC3C,CAAC,MACI,IAAIQ,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,EAAE;MAChD2B,EAAE,GAAG3B,UAAU;MACf0B,IAAI,GAAG3B,iBAAiB,CAACV,OAAO,CAAC7D,KAAK,EAAE8D,OAAO,EAAEU,UAAU,CAAC;IAChE,CAAC,MACI;MACD,IAAI,CAAC4B,uBAAuB,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACpDE,EAAE,GAAG,IAAI,CAACF,iBAAiB,CAACI,MAAM,CAAC7B,UAAU,CAAC;MAC9C0B,IAAI,GAAG3B,iBAAiB,CAACV,OAAO,CAAC7D,KAAK,EAAE8D,OAAO,EAAEqC,EAAE,CAAC;IACxD;IACA,IAAI,CAACvB,KAAK,CAAC0B,IAAI,CAACJ,IAAI,CAAC;IACrB,IAAIC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;EACAI,MAAMA,CAACvG,KAAK,EAAEwE,UAAU,EAAE;IACtB,IAAI0B,IAAI;IACR,IAAIC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BgG,IAAI,GAAG9E,QAAQ,CAAC6C,GAAG,CAACjE,KAAK,CAAC;IAC9B,CAAC,MACI,IAAIsE,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,EAAE;MAChD2B,EAAE,GAAG3B,UAAU;MACf0B,IAAI,GAAG3B,iBAAiB,CAACN,GAAG,CAACjE,KAAK,EAAEwE,UAAU,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC4B,uBAAuB,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACpDE,EAAE,GAAG,IAAI,CAACF,iBAAiB,CAACI,MAAM,CAAC7B,UAAU,CAAC;MAC9C0B,IAAI,GAAG3B,iBAAiB,CAACN,GAAG,CAACjE,KAAK,EAAEmG,EAAE,CAAC;IAC3C;IACA,IAAI,CAACvB,KAAK,CAAC0B,IAAI,CAACJ,IAAI,CAAC;IACrB,IAAIC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;EACAK,GAAGA,CAACN,IAAI,EAAE;IACN,IAAI,CAACtB,KAAK,CAAC0B,IAAI,CAACJ,IAAI,CAAC;EACzB;EACAO,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAAC7B,KAAK;EACrB;EACA8B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC9B,KAAK,CAAC+B,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC/B,KAAK,CAACjB,MAAM,CAAC;EAC3C;EACAyC,uBAAuBA,CAAC5H,KAAK,EAAE;IAC3B,IAAIA,KAAK,KAAK0B,SAAS,EAAE;MACrB,MAAM,IAAIL,KAAK,CAAC,kEAAkE,CAAC;IACvF;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAM+G,iBAAiB,CAAC;EACpBZ,WAAWA,CAACa,WAAW,EAAE;IACrB,IAAI,CAACC,YAAY,GAAGD,WAAW,KAAK3G,SAAS,GAAG6G,MAAM,CAAChI,MAAM,CAAC,IAAI,CAAC,GAAG8H,WAAW;IACjF,IAAI,CAACG,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAR,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACK,YAAY;EAC5B;EACA,IAAII,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,KAAK;EACrB;EACAZ,MAAMA,CAACc,cAAc,EAAE3C,UAAU,EAAE;IAC/B,IAAI2B,EAAE;IACN,IAAI7B,0BAA0B,CAAC/F,EAAE,CAAC4I,cAAc,CAAC,EAAE;MAC/ChB,EAAE,GAAGgB,cAAc;IACvB,CAAC,MACI;MACDhB,EAAE,GAAG,IAAI,CAACiB,MAAM,CAAC,CAAC;MAClB5C,UAAU,GAAG2C,cAAc;IAC/B;IACA,IAAI,IAAI,CAACL,YAAY,CAACX,EAAE,CAAC,KAAKjG,SAAS,EAAE;MACrC,MAAM,IAAIL,KAAK,CAAC,MAAMsG,EAAE,qBAAqB,CAAC;IAClD;IACA,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1B,MAAM,IAAIL,KAAK,CAAC,iCAAiCsG,EAAE,EAAE,CAAC;IAC1D;IACA,IAAI,CAACW,YAAY,CAACX,EAAE,CAAC,GAAG3B,UAAU;IAClC,IAAI,CAACyC,KAAK,EAAE;IACZ,OAAOd,EAAE;EACb;EACAiB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACJ,QAAQ,EAAE;IACf,OAAO,IAAI,CAACA,QAAQ,CAACK,QAAQ,CAAC,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBtB,WAAWA,CAACuB,aAAa,EAAE;IACvB,IAAI,CAACC,gBAAgB,GAAGT,MAAM,CAAChI,MAAM,CAAC,IAAI,CAAC;IAC3C,IAAIwI,aAAa,KAAKrH,SAAS,EAAE;MAC7B,IAAI,CAACuH,cAAc,GAAGF,aAAa;MACnC,IAAIA,aAAa,CAAC3B,eAAe,EAAE;QAC/B,IAAI,CAAC8B,kBAAkB,GAAG,IAAId,iBAAiB,CAACW,aAAa,CAACtB,iBAAiB,CAAC;QAChFsB,aAAa,CAACtB,iBAAiB,GAAG,IAAI,CAACyB,kBAAkB,CAACjB,GAAG,CAAC,CAAC;QAC/Dc,aAAa,CAAC3B,eAAe,CAAC+B,OAAO,CAAE7B,MAAM,IAAK;UAC9C,IAAIpB,gBAAgB,CAACnG,EAAE,CAACuH,MAAM,CAAC,EAAE;YAC7B,MAAM8B,cAAc,GAAG,IAAI7B,kBAAkB,CAACD,MAAM,CAAClB,KAAK,EAAE,IAAI,CAAC8C,kBAAkB,CAAC;YACpF,IAAI,CAACF,gBAAgB,CAAC1B,MAAM,CAACnB,YAAY,CAAC5E,GAAG,CAAC,GAAG6H,cAAc;UACnE;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAIL,aAAa,CAAC5B,OAAO,EAAE;QAC5BoB,MAAM,CAACc,IAAI,CAACN,aAAa,CAAC5B,OAAO,CAAC,CAACgC,OAAO,CAAEG,GAAG,IAAK;UAChD,MAAMF,cAAc,GAAG,IAAI7B,kBAAkB,CAACwB,aAAa,CAAC5B,OAAO,CAACmC,GAAG,CAAC,CAAC;UACzE,IAAI,CAACN,gBAAgB,CAACM,GAAG,CAAC,GAAGF,cAAc;QAC/C,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACD,IAAI,CAACH,cAAc,GAAG,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIvB,IAAIA,CAAA,EAAG;IACP,IAAI,CAAC6B,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACL,kBAAkB,KAAKxH,SAAS,EAAE;MACvC,IAAI,IAAI,CAACwH,kBAAkB,CAACR,IAAI,KAAK,CAAC,EAAE;QACpC,IAAI,CAACO,cAAc,CAACxB,iBAAiB,GAAG/F,SAAS;MACrD,CAAC,MACI;QACD,IAAI,CAACuH,cAAc,CAACxB,iBAAiB,GAAG,IAAI,CAACyB,kBAAkB,CAACjB,GAAG,CAAC,CAAC;MACzE;IACJ;IACA,OAAO,IAAI,CAACgB,cAAc;EAC9B;EACAO,iBAAiBA,CAACF,GAAG,EAAE;IACnB,IAAIjD,uCAAuC,CAACtG,EAAE,CAACuJ,GAAG,CAAC,EAAE;MACjD,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,IAAI,CAACN,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,EAAE;QACnD,MAAM,IAAIL,KAAK,CAAC,wDAAwD,CAAC;MAC7E;MACA,MAAM8E,YAAY,GAAG;QAAE5E,GAAG,EAAE+H,GAAG,CAAC/H,GAAG;QAAEkI,OAAO,EAAEH,GAAG,CAACG;MAAQ,CAAC;MAC3D,IAAIhG,MAAM,GAAG,IAAI,CAACuF,gBAAgB,CAAC7C,YAAY,CAAC5E,GAAG,CAAC;MACpD,IAAI,CAACkC,MAAM,EAAE;QACT,MAAM2C,KAAK,GAAG,EAAE;QAChB,MAAMsD,gBAAgB,GAAG;UACrBvD,YAAY;UACZC;QACJ,CAAC;QACD,IAAI,CAAC6C,cAAc,CAAC7B,eAAe,CAACU,IAAI,CAAC4B,gBAAgB,CAAC;QAC1DjG,MAAM,GAAG,IAAI8D,kBAAkB,CAACnB,KAAK,EAAE,IAAI,CAAC8C,kBAAkB,CAAC;QAC/D,IAAI,CAACF,gBAAgB,CAAC7C,YAAY,CAAC5E,GAAG,CAAC,GAAGkC,MAAM;MACpD;MACA,OAAOA,MAAM;IACjB,CAAC,MACI;MACD,IAAI,CAACkG,WAAW,CAAC,CAAC;MAClB,IAAI,IAAI,CAACV,cAAc,CAAC9B,OAAO,KAAKzF,SAAS,EAAE;QAC3C,MAAM,IAAIL,KAAK,CAAC,gEAAgE,CAAC;MACrF;MACA,IAAIoC,MAAM,GAAG,IAAI,CAACuF,gBAAgB,CAACM,GAAG,CAAC;MACvC,IAAI,CAAC7F,MAAM,EAAE;QACT,IAAI2C,KAAK,GAAG,EAAE;QACd,IAAI,CAAC6C,cAAc,CAAC9B,OAAO,CAACmC,GAAG,CAAC,GAAGlD,KAAK;QACxC3C,MAAM,GAAG,IAAI8D,kBAAkB,CAACnB,KAAK,CAAC;QACtC,IAAI,CAAC4C,gBAAgB,CAACM,GAAG,CAAC,GAAG7F,MAAM;MACvC;MACA,OAAOA,MAAM;IACjB;EACJ;EACA8F,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACN,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,IAAI,IAAI,CAACuH,cAAc,CAAC9B,OAAO,KAAKzF,SAAS,EAAE;MAChG,IAAI,CAACwH,kBAAkB,GAAG,IAAId,iBAAiB,CAAC,CAAC;MACjD,IAAI,CAACa,cAAc,CAAC7B,eAAe,GAAG,EAAE;MACxC,IAAI,CAAC6B,cAAc,CAACxB,iBAAiB,GAAG,IAAI,CAACyB,kBAAkB,CAACjB,GAAG,CAAC,CAAC;IACzE;EACJ;EACA0B,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACV,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,IAAI,IAAI,CAACuH,cAAc,CAAC9B,OAAO,KAAKzF,SAAS,EAAE;MAChG,IAAI,CAACuH,cAAc,CAAC9B,OAAO,GAAGoB,MAAM,CAAChI,MAAM,CAAC,IAAI,CAAC;IACrD;EACJ;EACAqJ,UAAUA,CAACrI,GAAG,EAAEsI,mBAAmB,EAAEpD,OAAO,EAAE;IAC1C,IAAI,CAAC8C,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACN,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,EAAE;MACnD,MAAM,IAAIL,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACA,IAAI2E,UAAU;IACd,IAAIN,gBAAgB,CAAC3F,EAAE,CAAC8J,mBAAmB,CAAC,IAAI/D,0BAA0B,CAAC/F,EAAE,CAAC8J,mBAAmB,CAAC,EAAE;MAChG7D,UAAU,GAAG6D,mBAAmB;IACpC,CAAC,MACI;MACDpD,OAAO,GAAGoD,mBAAmB;IACjC;IACA,IAAIC,SAAS;IACb,IAAInC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BoI,SAAS,GAAGtD,UAAU,CAACjG,MAAM,CAACgB,GAAG,EAAEkF,OAAO,CAAC;IAC/C,CAAC,MACI;MACDkB,EAAE,GAAG7B,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,GAAGA,UAAU,GAAG,IAAI,CAACkD,kBAAkB,CAACrB,MAAM,CAAC7B,UAAU,CAAC;MACxG8D,SAAS,GAAGtD,UAAU,CAACjG,MAAM,CAACgB,GAAG,EAAEkF,OAAO,EAAEkB,EAAE,CAAC;IACnD;IACA,IAAI,CAACsB,cAAc,CAAC7B,eAAe,CAACU,IAAI,CAACgC,SAAS,CAAC;IACnD,IAAInC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;EACAoC,UAAUA,CAAClD,MAAM,EAAEC,MAAM,EAAE+C,mBAAmB,EAAEpD,OAAO,EAAE;IACrD,IAAI,CAAC8C,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACN,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,EAAE;MACnD,MAAM,IAAIL,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACA,IAAI2E,UAAU;IACd,IAAIN,gBAAgB,CAAC3F,EAAE,CAAC8J,mBAAmB,CAAC,IAAI/D,0BAA0B,CAAC/F,EAAE,CAAC8J,mBAAmB,CAAC,EAAE;MAChG7D,UAAU,GAAG6D,mBAAmB;IACpC,CAAC,MACI;MACDpD,OAAO,GAAGoD,mBAAmB;IACjC;IACA,IAAIC,SAAS;IACb,IAAInC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BoI,SAAS,GAAGlD,UAAU,CAACrG,MAAM,CAACsG,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;IAC1D,CAAC,MACI;MACDkB,EAAE,GAAG7B,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,GAAGA,UAAU,GAAG,IAAI,CAACkD,kBAAkB,CAACrB,MAAM,CAAC7B,UAAU,CAAC;MACxG8D,SAAS,GAAGlD,UAAU,CAACrG,MAAM,CAACsG,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAEkB,EAAE,CAAC;IAC9D;IACA,IAAI,CAACsB,cAAc,CAAC7B,eAAe,CAACU,IAAI,CAACgC,SAAS,CAAC;IACnD,IAAInC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;EACAqC,UAAUA,CAACzI,GAAG,EAAEsI,mBAAmB,EAAEpD,OAAO,EAAE;IAC1C,IAAI,CAAC8C,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACN,cAAc,CAAC7B,eAAe,KAAK1F,SAAS,EAAE;MACnD,MAAM,IAAIL,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACA,IAAI2E,UAAU;IACd,IAAIN,gBAAgB,CAAC3F,EAAE,CAAC8J,mBAAmB,CAAC,IAAI/D,0BAA0B,CAAC/F,EAAE,CAAC8J,mBAAmB,CAAC,EAAE;MAChG7D,UAAU,GAAG6D,mBAAmB;IACpC,CAAC,MACI;MACDpD,OAAO,GAAGoD,mBAAmB;IACjC;IACA,IAAIC,SAAS;IACb,IAAInC,EAAE;IACN,IAAI3B,UAAU,KAAKtE,SAAS,EAAE;MAC1BoI,SAAS,GAAG/C,UAAU,CAACxG,MAAM,CAACgB,GAAG,EAAEkF,OAAO,CAAC;IAC/C,CAAC,MACI;MACDkB,EAAE,GAAG7B,0BAA0B,CAAC/F,EAAE,CAACiG,UAAU,CAAC,GAAGA,UAAU,GAAG,IAAI,CAACkD,kBAAkB,CAACrB,MAAM,CAAC7B,UAAU,CAAC;MACxG8D,SAAS,GAAG/C,UAAU,CAACxG,MAAM,CAACgB,GAAG,EAAEkF,OAAO,EAAEkB,EAAE,CAAC;IACnD;IACA,IAAI,CAACsB,cAAc,CAAC7B,eAAe,CAACU,IAAI,CAACgC,SAAS,CAAC;IACnD,IAAInC,EAAE,KAAKjG,SAAS,EAAE;MAClB,OAAOiG,EAAE;IACb;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIsC,sBAAsB;AACjC,CAAC,UAAUA,sBAAsB,EAAE;EAC/B;AACJ;AACA;AACA;EACI,SAAS1J,MAAMA,CAACgB,GAAG,EAAE;IACjB,OAAO;MAAEA;IAAI,CAAC;EAClB;EACA0I,sBAAsB,CAAC1J,MAAM,GAAGA,MAAM;EACtC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC;EAC5D;EACA0I,sBAAsB,CAAClK,EAAE,GAAGA,EAAE;AAClC,CAAC,EAAEkK,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,OAAO,IAAIC,+BAA+B;AAC1C,CAAC,UAAUA,+BAA+B,EAAE;EACxC;AACJ;AACA;AACA;AACA;EACI,SAAS3J,MAAMA,CAACgB,GAAG,EAAEkI,OAAO,EAAE;IAC1B,OAAO;MAAElI,GAAG;MAAEkI;IAAQ,CAAC;EAC3B;EACAS,+BAA+B,CAAC3J,MAAM,GAAGA,MAAM;EAC/C;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,IAAIX,EAAE,CAACV,OAAO,CAACS,SAAS,CAAC8I,OAAO,CAAC;EAC7F;EACAS,+BAA+B,CAACnK,EAAE,GAAGA,EAAE;AAC3C,CAAC,EAAEmK,+BAA+B,KAAKA,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA,OAAO,IAAI7D,uCAAuC;AAClD,CAAC,UAAUA,uCAAuC,EAAE;EAChD;AACJ;AACA;AACA;AACA;EACI,SAAS9F,MAAMA,CAACgB,GAAG,EAAEkI,OAAO,EAAE;IAC1B,OAAO;MAAElI,GAAG;MAAEkI;IAAQ,CAAC;EAC3B;EACApD,uCAAuC,CAAC9F,MAAM,GAAGA,MAAM;EACvD;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,KAAKZ,SAAS,CAAC8I,OAAO,KAAK,IAAI,IAAI7I,EAAE,CAACV,OAAO,CAACS,SAAS,CAAC8I,OAAO,CAAC,CAAC;EAC7H;EACApD,uCAAuC,CAACtG,EAAE,GAAGA,EAAE;AACnD,CAAC,EAAEsG,uCAAuC,KAAKA,uCAAuC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F;AACA;AACA;AACA;AACA,OAAO,IAAI8D,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS5J,MAAMA,CAACgB,GAAG,EAAE6I,UAAU,EAAEX,OAAO,EAAEY,IAAI,EAAE;IAC5C,OAAO;MAAE9I,GAAG;MAAE6I,UAAU;MAAEX,OAAO;MAAEY;IAAK,CAAC;EAC7C;EACAF,gBAAgB,CAAC5J,MAAM,GAAGA,MAAM;EAChC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,IAAIX,EAAE,CAACa,MAAM,CAACd,SAAS,CAACyJ,UAAU,CAAC,IAAIxJ,EAAE,CAACV,OAAO,CAACS,SAAS,CAAC8I,OAAO,CAAC,IAAI7I,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC0J,IAAI,CAAC;EAC7J;EACAF,gBAAgB,CAACpK,EAAE,GAAGA,EAAE;AAC5B,CAAC,EAAEoK,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB;AACJ;AACA;EACIA,UAAU,CAACC,SAAS,GAAG,WAAW;EAClC;AACJ;AACA;EACID,UAAU,CAACE,QAAQ,GAAG,UAAU;EAChC;AACJ;AACA;EACI,SAASzK,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,KAAK2J,UAAU,CAACC,SAAS,IAAI5J,SAAS,KAAK2J,UAAU,CAACE,QAAQ;EAClF;EACAF,UAAU,CAACvK,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAEuK,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAIG,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACI,SAAS1K,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACb,KAAK,CAAC,IAAIsK,UAAU,CAACvK,EAAE,CAACY,SAAS,CAAC4C,IAAI,CAAC,IAAI3C,EAAE,CAACa,MAAM,CAACd,SAAS,CAACX,KAAK,CAAC;EACjG;EACAyK,aAAa,CAAC1K,EAAE,GAAGA,EAAE;AACzB,CAAC,EAAE0K,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3BA,kBAAkB,CAACC,IAAI,GAAG,CAAC;EAC3BD,kBAAkB,CAACE,MAAM,GAAG,CAAC;EAC7BF,kBAAkB,CAACG,QAAQ,GAAG,CAAC;EAC/BH,kBAAkB,CAACI,WAAW,GAAG,CAAC;EAClCJ,kBAAkB,CAACK,KAAK,GAAG,CAAC;EAC5BL,kBAAkB,CAACM,QAAQ,GAAG,CAAC;EAC/BN,kBAAkB,CAACO,KAAK,GAAG,CAAC;EAC5BP,kBAAkB,CAACQ,SAAS,GAAG,CAAC;EAChCR,kBAAkB,CAACS,MAAM,GAAG,CAAC;EAC7BT,kBAAkB,CAACU,QAAQ,GAAG,EAAE;EAChCV,kBAAkB,CAACW,IAAI,GAAG,EAAE;EAC5BX,kBAAkB,CAACY,KAAK,GAAG,EAAE;EAC7BZ,kBAAkB,CAACa,IAAI,GAAG,EAAE;EAC5Bb,kBAAkB,CAACc,OAAO,GAAG,EAAE;EAC/Bd,kBAAkB,CAACe,OAAO,GAAG,EAAE;EAC/Bf,kBAAkB,CAAC1I,KAAK,GAAG,EAAE;EAC7B0I,kBAAkB,CAACgB,IAAI,GAAG,EAAE;EAC5BhB,kBAAkB,CAACiB,SAAS,GAAG,EAAE;EACjCjB,kBAAkB,CAACkB,MAAM,GAAG,EAAE;EAC9BlB,kBAAkB,CAACmB,UAAU,GAAG,EAAE;EAClCnB,kBAAkB,CAACoB,QAAQ,GAAG,EAAE;EAChCpB,kBAAkB,CAACqB,MAAM,GAAG,EAAE;EAC9BrB,kBAAkB,CAACsB,KAAK,GAAG,EAAE;EAC7BtB,kBAAkB,CAACuB,QAAQ,GAAG,EAAE;EAChCvB,kBAAkB,CAACwB,aAAa,GAAG,EAAE;AACzC,CAAC,EAAExB,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA,OAAO,IAAIyB,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;EACIA,gBAAgB,CAAC5B,SAAS,GAAG,CAAC;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,gBAAgB,CAACV,OAAO,GAAG,CAAC;AAChC,CAAC,EAAEU,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;EACIA,iBAAiB,CAAChI,UAAU,GAAG,CAAC;AACpC,CAAC,EAAEgI,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;EACI,SAAS9L,MAAMA,CAAC+E,OAAO,EAAEC,MAAM,EAAEF,OAAO,EAAE;IACtC,OAAO;MAAEC,OAAO;MAAEC,MAAM;MAAEF;IAAQ,CAAC;EACvC;EACAgH,iBAAiB,CAAC9L,MAAM,GAAGA,MAAM;EACjC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC2E,OAAO,CAAC,IAAIxE,KAAK,CAACf,EAAE,CAACY,SAAS,CAAC4E,MAAM,CAAC,IAAIzE,KAAK,CAACf,EAAE,CAACY,SAAS,CAAC0E,OAAO,CAAC;EACjH;EACAgH,iBAAiB,CAACtM,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAEsM,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,cAAc,CAACC,IAAI,GAAG,CAAC;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,cAAc,CAACE,iBAAiB,GAAG,CAAC;AACxC,CAAC,EAAEF,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO,IAAIG,0BAA0B;AACrC,CAAC,UAAUA,0BAA0B,EAAE;EACnC,SAAS1M,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,KAAKC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC+L,MAAM,CAAC,IAAI/L,SAAS,CAAC+L,MAAM,KAAKhL,SAAS,CAAC,KAC9Ed,EAAE,CAACa,MAAM,CAACd,SAAS,CAACiF,WAAW,CAAC,IAAIjF,SAAS,CAACiF,WAAW,KAAKlE,SAAS,CAAC;EACjF;EACA+K,0BAA0B,CAAC1M,EAAE,GAAGA,EAAE;AACtC,CAAC,EAAE0M,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA,OAAO,IAAIE,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;EACI,SAASpM,MAAMA,CAACkC,KAAK,EAAE;IACnB,OAAO;MAAEA;IAAM,CAAC;EACpB;EACAkK,cAAc,CAACpM,MAAM,GAAGA,MAAM;AAClC,CAAC,EAAEoM,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;EACI,SAASrM,MAAMA,CAACsM,KAAK,EAAEC,YAAY,EAAE;IACjC,OAAO;MAAED,KAAK,EAAEA,KAAK,GAAGA,KAAK,GAAG,EAAE;MAAEC,YAAY,EAAE,CAAC,CAACA;IAAa,CAAC;EACtE;EACAF,cAAc,CAACrM,MAAM,GAAGA,MAAM;AAClC,CAAC,EAAEqM,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO,IAAIG,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;EACI,SAASC,aAAaA,CAACC,SAAS,EAAE;IAC9B,OAAOA,SAAS,CAAC5H,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC,CAAC;EAC/D;EACA0H,YAAY,CAACC,aAAa,GAAGA,aAAa;EAC1C;AACJ;AACA;EACI,SAASjN,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC,IAAKC,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACuM,QAAQ,CAAC,IAAItM,EAAE,CAACa,MAAM,CAACd,SAAS,CAACX,KAAK,CAAE;EAC/H;EACA+M,YAAY,CAAChN,EAAE,GAAGA,EAAE;AACxB,CAAC,EAAEgN,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,OAAO,IAAII,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACd;AACJ;AACA;EACI,SAASpN,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAO,CAAC,CAACW,SAAS,IAAIC,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,KAAK8J,aAAa,CAAC1K,EAAE,CAACY,SAAS,CAACyM,QAAQ,CAAC,IACtFL,YAAY,CAAChN,EAAE,CAACY,SAAS,CAACyM,QAAQ,CAAC,IACnCxM,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACyM,QAAQ,EAAEL,YAAY,CAAChN,EAAE,CAAC,CAAC,KAAKC,KAAK,CAACwB,KAAK,KAAKE,SAAS,IAAIZ,KAAK,CAACf,EAAE,CAACC,KAAK,CAACwB,KAAK,CAAC,CAAC;EACnH;EACA2L,KAAK,CAACpN,EAAE,GAAGA,EAAE;AACjB,CAAC,EAAEoN,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA,OAAO,IAAIE,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;AACJ;AACA;AACA;AACA;AACA;EACI,SAAS9M,MAAMA,CAACkC,KAAK,EAAE6K,aAAa,EAAE;IAClC,OAAOA,aAAa,GAAG;MAAE7K,KAAK;MAAE6K;IAAc,CAAC,GAAG;MAAE7K;IAAM,CAAC;EAC/D;EACA4K,oBAAoB,CAAC9M,MAAM,GAAGA,MAAM;AACxC,CAAC,EAAE8M,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,OAAO,IAAIE,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7B,SAAShN,MAAMA,CAACkC,KAAK,EAAE6K,aAAa,EAAE,GAAGE,UAAU,EAAE;IACjD,IAAI/J,MAAM,GAAG;MAAEhB;IAAM,CAAC;IACtB,IAAI7B,EAAE,CAAC8C,OAAO,CAAC4J,aAAa,CAAC,EAAE;MAC3B7J,MAAM,CAAC6J,aAAa,GAAGA,aAAa;IACxC;IACA,IAAI1M,EAAE,CAAC8C,OAAO,CAAC8J,UAAU,CAAC,EAAE;MACxB/J,MAAM,CAAC+J,UAAU,GAAGA,UAAU;IAClC,CAAC,MACI;MACD/J,MAAM,CAAC+J,UAAU,GAAG,EAAE;IAC1B;IACA,OAAO/J,MAAM;EACjB;EACA8J,oBAAoB,CAAChN,MAAM,GAAGA,MAAM;AACxC,CAAC,EAAEgN,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,OAAO,IAAIE,qBAAqB;AAChC,CAAC,UAAUA,qBAAqB,EAAE;EAC9B;AACJ;AACA;EACIA,qBAAqB,CAAC9C,IAAI,GAAG,CAAC;EAC9B;AACJ;AACA;EACI8C,qBAAqB,CAACC,IAAI,GAAG,CAAC;EAC9B;AACJ;AACA;EACID,qBAAqB,CAACE,KAAK,GAAG,CAAC;AACnC,CAAC,EAAEF,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA,OAAO,IAAIG,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;AACA;AACA;EACI,SAASrN,MAAMA,CAACiB,KAAK,EAAE+B,IAAI,EAAE;IACzB,IAAIE,MAAM,GAAG;MAAEjC;IAAM,CAAC;IACtB,IAAIZ,EAAE,CAACiE,MAAM,CAACtB,IAAI,CAAC,EAAE;MACjBE,MAAM,CAACF,IAAI,GAAGA,IAAI;IACtB;IACA,OAAOE,MAAM;EACjB;EACAmK,iBAAiB,CAACrN,MAAM,GAAGA,MAAM;AACrC,CAAC,EAAEqN,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACnC,IAAI,GAAG,CAAC;EACnBmC,UAAU,CAAC1C,MAAM,GAAG,CAAC;EACrB0C,UAAU,CAACC,SAAS,GAAG,CAAC;EACxBD,UAAU,CAACE,OAAO,GAAG,CAAC;EACtBF,UAAU,CAAC5C,KAAK,GAAG,CAAC;EACpB4C,UAAU,CAACjD,MAAM,GAAG,CAAC;EACrBiD,UAAU,CAACzC,QAAQ,GAAG,CAAC;EACvByC,UAAU,CAAC9C,KAAK,GAAG,CAAC;EACpB8C,UAAU,CAAC/C,WAAW,GAAG,CAAC;EAC1B+C,UAAU,CAACtC,IAAI,GAAG,EAAE;EACpBsC,UAAU,CAAC3C,SAAS,GAAG,EAAE;EACzB2C,UAAU,CAAChD,QAAQ,GAAG,EAAE;EACxBgD,UAAU,CAAC7C,QAAQ,GAAG,EAAE;EACxB6C,UAAU,CAAC/B,QAAQ,GAAG,EAAE;EACxB+B,UAAU,CAACG,MAAM,GAAG,EAAE;EACtBH,UAAU,CAACnN,MAAM,GAAG,EAAE;EACtBmN,UAAU,CAACI,OAAO,GAAG,EAAE;EACvBJ,UAAU,CAACvH,KAAK,GAAG,EAAE;EACrBuH,UAAU,CAACtF,MAAM,GAAG,EAAE;EACtBsF,UAAU,CAACK,GAAG,GAAG,EAAE;EACnBL,UAAU,CAACM,IAAI,GAAG,EAAE;EACpBN,UAAU,CAAChC,UAAU,GAAG,EAAE;EAC1BgC,UAAU,CAAC9B,MAAM,GAAG,EAAE;EACtB8B,UAAU,CAAC7B,KAAK,GAAG,EAAE;EACrB6B,UAAU,CAAC5B,QAAQ,GAAG,EAAE;EACxB4B,UAAU,CAAC3B,aAAa,GAAG,EAAE;AACjC,CAAC,EAAE2B,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClB;AACJ;AACA;EACIA,SAAS,CAAChK,UAAU,GAAG,CAAC;AAC5B,CAAC,EAAEgK,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO,IAAIC,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS9N,MAAMA,CAAC+N,IAAI,EAAE/K,IAAI,EAAE/B,KAAK,EAAED,GAAG,EAAEgN,aAAa,EAAE;IACnD,IAAI9K,MAAM,GAAG;MACT6K,IAAI;MACJ/K,IAAI;MACJK,QAAQ,EAAE;QAAErC,GAAG;QAAEC;MAAM;IAC3B,CAAC;IACD,IAAI+M,aAAa,EAAE;MACf9K,MAAM,CAAC8K,aAAa,GAAGA,aAAa;IACxC;IACA,OAAO9K,MAAM;EACjB;EACA4K,iBAAiB,CAAC9N,MAAM,GAAGA,MAAM;AACrC,CAAC,EAAE8N,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,OAAO,IAAIG,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASjO,MAAMA,CAAC+N,IAAI,EAAE/K,IAAI,EAAEhC,GAAG,EAAEC,KAAK,EAAE;IACpC,OAAOA,KAAK,KAAKE,SAAS,GACpB;MAAE4M,IAAI;MAAE/K,IAAI;MAAEK,QAAQ,EAAE;QAAErC,GAAG;QAAEC;MAAM;IAAE,CAAC,GACxC;MAAE8M,IAAI;MAAE/K,IAAI;MAAEK,QAAQ,EAAE;QAAErC;MAAI;IAAE,CAAC;EAC3C;EACAiN,eAAe,CAACjO,MAAM,GAAGA,MAAM;AACnC,CAAC,EAAEiO,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASlO,MAAMA,CAAC+N,IAAI,EAAE5B,MAAM,EAAEnJ,IAAI,EAAE/B,KAAK,EAAEkN,cAAc,EAAEC,QAAQ,EAAE;IACjE,IAAIlL,MAAM,GAAG;MACT6K,IAAI;MACJ5B,MAAM;MACNnJ,IAAI;MACJ/B,KAAK;MACLkN;IACJ,CAAC;IACD,IAAIC,QAAQ,KAAKjN,SAAS,EAAE;MACxB+B,MAAM,CAACkL,QAAQ,GAAGA,QAAQ;IAC9B;IACA,OAAOlL,MAAM;EACjB;EACAgL,cAAc,CAAClO,MAAM,GAAGA,MAAM;EAC9B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,IACZC,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC2N,IAAI,CAAC,IAAI1N,EAAE,CAACiE,MAAM,CAAClE,SAAS,CAAC4C,IAAI,CAAC,IACtDzC,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,IAAIV,KAAK,CAACf,EAAE,CAACY,SAAS,CAAC+N,cAAc,CAAC,KAC9D/N,SAAS,CAAC+L,MAAM,KAAKhL,SAAS,IAAId,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC+L,MAAM,CAAC,CAAC,KAC9D/L,SAAS,CAACiO,UAAU,KAAKlN,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACiO,UAAU,CAAC,CAAC,KACvEjO,SAAS,CAACgO,QAAQ,KAAKjN,SAAS,IAAI4E,KAAK,CAACC,OAAO,CAAC5F,SAAS,CAACgO,QAAQ,CAAC,CAAC,KACtEhO,SAAS,CAACkO,IAAI,KAAKnN,SAAS,IAAI4E,KAAK,CAACC,OAAO,CAAC5F,SAAS,CAACkO,IAAI,CAAC,CAAC;EACvE;EACAJ,cAAc,CAAC1O,EAAE,GAAGA,EAAE;AAC1B,CAAC,EAAE0O,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA,OAAO,IAAIK,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;EACIA,cAAc,CAACC,KAAK,GAAG,EAAE;EACzB;AACJ;AACA;EACID,cAAc,CAACE,QAAQ,GAAG,UAAU;EACpC;AACJ;AACA;EACIF,cAAc,CAACG,QAAQ,GAAG,UAAU;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,cAAc,CAACI,eAAe,GAAG,kBAAkB;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,cAAc,CAACK,cAAc,GAAG,iBAAiB;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,cAAc,CAACM,eAAe,GAAG,kBAAkB;EACnD;AACJ;AACA;AACA;AACA;EACIN,cAAc,CAACO,MAAM,GAAG,QAAQ;EAChC;AACJ;AACA;EACIP,cAAc,CAACQ,qBAAqB,GAAG,wBAAwB;EAC/D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,cAAc,CAACS,YAAY,GAAG,eAAe;AACjD,CAAC,EAAET,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIU,qBAAqB;AAChC,CAAC,UAAUA,qBAAqB,EAAE;EAC9B;AACJ;AACA;EACIA,qBAAqB,CAACC,OAAO,GAAG,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;EACID,qBAAqB,CAACE,SAAS,GAAG,CAAC;AACvC,CAAC,EAAEF,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA,OAAO,IAAIG,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;EACI,SAASpP,MAAMA,CAACqP,WAAW,EAAEC,IAAI,EAAEC,WAAW,EAAE;IAC5C,IAAIrM,MAAM,GAAG;MAAEmM;IAAY,CAAC;IAC5B,IAAIC,IAAI,KAAKnO,SAAS,IAAImO,IAAI,KAAK,IAAI,EAAE;MACrCpM,MAAM,CAACoM,IAAI,GAAGA,IAAI;IACtB;IACA,IAAIC,WAAW,KAAKpO,SAAS,IAAIoO,WAAW,KAAK,IAAI,EAAE;MACnDrM,MAAM,CAACqM,WAAW,GAAGA,WAAW;IACpC;IACA,OAAOrM,MAAM;EACjB;EACAkM,iBAAiB,CAACpP,MAAM,GAAGA,MAAM;EACjC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACiP,WAAW,EAAErL,UAAU,CAACxE,EAAE,CAAC,KAC3EY,SAAS,CAACkP,IAAI,KAAKnO,SAAS,IAAId,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACkP,IAAI,EAAEjP,EAAE,CAACa,MAAM,CAAC,CAAC,KACzEd,SAAS,CAACmP,WAAW,KAAKpO,SAAS,IAAIf,SAAS,CAACmP,WAAW,KAAKN,qBAAqB,CAACC,OAAO,IAAI9O,SAAS,CAACmP,WAAW,KAAKN,qBAAqB,CAACE,SAAS,CAAC;EACxK;EACAC,iBAAiB,CAAC5P,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAE4P,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,OAAO,IAAII,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnB,SAASxP,MAAMA,CAACyE,KAAK,EAAEgL,mBAAmB,EAAEzM,IAAI,EAAE;IAC9C,IAAIE,MAAM,GAAG;MAAEuB;IAAM,CAAC;IACtB,IAAIiL,SAAS,GAAG,IAAI;IACpB,IAAI,OAAOD,mBAAmB,KAAK,QAAQ,EAAE;MACzCC,SAAS,GAAG,KAAK;MACjBxM,MAAM,CAACF,IAAI,GAAGyM,mBAAmB;IACrC,CAAC,MACI,IAAIjL,OAAO,CAAChF,EAAE,CAACiQ,mBAAmB,CAAC,EAAE;MACtCvM,MAAM,CAACwB,OAAO,GAAG+K,mBAAmB;IACxC,CAAC,MACI;MACDvM,MAAM,CAACiE,IAAI,GAAGsI,mBAAmB;IACrC;IACA,IAAIC,SAAS,IAAI1M,IAAI,KAAK7B,SAAS,EAAE;MACjC+B,MAAM,CAACF,IAAI,GAAGA,IAAI;IACtB;IACA,OAAOE,MAAM;EACjB;EACAsM,UAAU,CAACxP,MAAM,GAAGA,MAAM;EAC1B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOW,SAAS,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACqE,KAAK,CAAC,KACzCrE,SAAS,CAACiP,WAAW,KAAKlO,SAAS,IAAId,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACiP,WAAW,EAAErL,UAAU,CAACxE,EAAE,CAAC,CAAC,KAC3FY,SAAS,CAAC4C,IAAI,KAAK7B,SAAS,IAAId,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC4C,IAAI,CAAC,CAAC,KAC1D5C,SAAS,CAAC+G,IAAI,KAAKhG,SAAS,IAAIf,SAAS,CAACsE,OAAO,KAAKvD,SAAS,CAAC,KAChEf,SAAS,CAACsE,OAAO,KAAKvD,SAAS,IAAIqD,OAAO,CAAChF,EAAE,CAACY,SAAS,CAACsE,OAAO,CAAC,CAAC,KACjEtE,SAAS,CAACuP,WAAW,KAAKxO,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACuP,WAAW,CAAC,CAAC,KACzEvP,SAAS,CAAC+G,IAAI,KAAKhG,SAAS,IAAIwF,aAAa,CAACnH,EAAE,CAACY,SAAS,CAAC+G,IAAI,CAAC,CAAC;EAC1E;EACAqI,UAAU,CAAChQ,EAAE,GAAGA,EAAE;AACtB,CAAC,EAAEgQ,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA,OAAO,IAAII,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjB;AACJ;AACA;EACI,SAAS5P,MAAMA,CAACiB,KAAK,EAAE4O,IAAI,EAAE;IACzB,IAAI3M,MAAM,GAAG;MAAEjC;IAAM,CAAC;IACtB,IAAIZ,EAAE,CAAC8C,OAAO,CAAC0M,IAAI,CAAC,EAAE;MAClB3M,MAAM,CAAC2M,IAAI,GAAGA,IAAI;IACtB;IACA,OAAO3M,MAAM;EACjB;EACA0M,QAAQ,CAAC5P,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,KAAKZ,EAAE,CAACc,SAAS,CAACf,SAAS,CAACsE,OAAO,CAAC,IAAIF,OAAO,CAAChF,EAAE,CAACY,SAAS,CAACsE,OAAO,CAAC,CAAC;EACnI;EACAkL,QAAQ,CAACpQ,EAAE,GAAGA,EAAE;AACpB,CAAC,EAAEoQ,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA,OAAO,IAAIE,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;EACI,SAAS9P,MAAMA,CAAC+P,OAAO,EAAEC,YAAY,EAAE;IACnC,OAAO;MAAED,OAAO;MAAEC;IAAa,CAAC;EACpC;EACAF,iBAAiB,CAAC9P,MAAM,GAAGA,MAAM;EACjC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACP,QAAQ,CAACM,SAAS,CAAC2P,OAAO,CAAC,IAAI1P,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAAC4P,YAAY,CAAC;EACxG;EACAF,iBAAiB,CAACtQ,EAAE,GAAGA,EAAE;AAC7B,CAAC,EAAEsQ,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA,OAAO,IAAIG,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;EACI,SAASjQ,MAAMA,CAACiB,KAAK,EAAEiP,MAAM,EAAEL,IAAI,EAAE;IACjC,OAAO;MAAE5O,KAAK;MAAEiP,MAAM;MAAEL;IAAK,CAAC;EAClC;EACAI,YAAY,CAACjQ,MAAM,GAAGA,MAAM;EAC5B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,KAAKZ,EAAE,CAACc,SAAS,CAACf,SAAS,CAAC8P,MAAM,CAAC,IAAI7P,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC8P,MAAM,CAAC,CAAC;EAChI;EACAD,YAAY,CAACzQ,EAAE,GAAGA,EAAE;AACxB,CAAC,EAAEyQ,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,OAAO,IAAIE,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;EACI,SAASnQ,MAAMA,CAACiB,KAAK,EAAEmP,MAAM,EAAE;IAC3B,OAAO;MAAEnP,KAAK;MAAEmP;IAAO,CAAC;EAC5B;EACAD,cAAc,CAACnQ,MAAM,GAAGA,MAAM;EAC9B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,KAAKb,SAAS,CAACgQ,MAAM,KAAKjP,SAAS,IAAIgP,cAAc,CAAC3Q,EAAE,CAACY,SAAS,CAACgQ,MAAM,CAAC,CAAC;EAC9I;EACAD,cAAc,CAAC3Q,EAAE,GAAGA,EAAE;AAC1B,CAAC,EAAE2Q,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3BA,kBAAkB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC7C;AACJ;AACA;AACA;EACIA,kBAAkB,CAAC,MAAM,CAAC,GAAG,MAAM;EACnCA,kBAAkB,CAAC,OAAO,CAAC,GAAG,OAAO;EACrCA,kBAAkB,CAAC,MAAM,CAAC,GAAG,MAAM;EACnCA,kBAAkB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC7CA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACvCA,kBAAkB,CAAC,eAAe,CAAC,GAAG,eAAe;EACrDA,kBAAkB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC7CA,kBAAkB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC3CA,kBAAkB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC3CA,kBAAkB,CAAC,YAAY,CAAC,GAAG,YAAY;EAC/CA,kBAAkB,CAAC,OAAO,CAAC,GAAG,OAAO;EACrCA,kBAAkB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC3CA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACvCA,kBAAkB,CAAC,OAAO,CAAC,GAAG,OAAO;EACrCA,kBAAkB,CAAC,SAAS,CAAC,GAAG,SAAS;EACzCA,kBAAkB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC3CA,kBAAkB,CAAC,SAAS,CAAC,GAAG,SAAS;EACzCA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACvCA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACvCA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACvCA,kBAAkB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC3C;AACJ;AACA;EACIA,kBAAkB,CAAC,WAAW,CAAC,GAAG,WAAW;AACjD,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB;AACjC,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,aAAa,CAAC,GAAG,aAAa;EACrDA,sBAAsB,CAAC,YAAY,CAAC,GAAG,YAAY;EACnDA,sBAAsB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC/CA,sBAAsB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC3CA,sBAAsB,CAAC,YAAY,CAAC,GAAG,YAAY;EACnDA,sBAAsB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC/CA,sBAAsB,CAAC,OAAO,CAAC,GAAG,OAAO;EACzCA,sBAAsB,CAAC,cAAc,CAAC,GAAG,cAAc;EACvDA,sBAAsB,CAAC,eAAe,CAAC,GAAG,eAAe;EACzDA,sBAAsB,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;AAC/D,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB,SAAS/Q,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,KAAKA,SAAS,CAACoQ,QAAQ,KAAKrP,SAAS,IAAI,OAAOf,SAAS,CAACoQ,QAAQ,KAAK,QAAQ,CAAC,IAC9GzK,KAAK,CAACC,OAAO,CAAC5F,SAAS,CAACyP,IAAI,CAAC,KAAKzP,SAAS,CAACyP,IAAI,CAACjL,MAAM,KAAK,CAAC,IAAI,OAAOxE,SAAS,CAACyP,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;EAC/G;EACAU,cAAc,CAAC/Q,EAAE,GAAGA,EAAE;AAC1B,CAAC,EAAE+Q,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxB;AACJ;AACA;EACI,SAASzQ,MAAMA,CAACiB,KAAK,EAAE6I,IAAI,EAAE;IACzB,OAAO;MAAE7I,KAAK;MAAE6I;IAAK,CAAC;EAC1B;EACA2G,eAAe,CAACzQ,MAAM,GAAGA,MAAM;EAC/B,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,KAAKe,SAAS,IAAIf,SAAS,KAAK,IAAI,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,IAAIZ,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC0J,IAAI,CAAC;EAClH;EACA2G,eAAe,CAACjR,EAAE,GAAGA,EAAE;AAC3B,CAAC,EAAEiR,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,yBAAyB;AACpC,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;EACI,SAAS1Q,MAAMA,CAACiB,KAAK,EAAE0P,YAAY,EAAEC,mBAAmB,EAAE;IACtD,OAAO;MAAE3P,KAAK;MAAE0P,YAAY;MAAEC;IAAoB,CAAC;EACvD;EACAF,yBAAyB,CAAC1Q,MAAM,GAAGA,MAAM;EACzC,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,KAAKe,SAAS,IAAIf,SAAS,KAAK,IAAI,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,IAAIZ,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACwQ,mBAAmB,CAAC,KACtHvQ,EAAE,CAACa,MAAM,CAACd,SAAS,CAACuQ,YAAY,CAAC,IAAIvQ,SAAS,CAACuQ,YAAY,KAAKxP,SAAS,CAAC;EACtF;EACAuP,yBAAyB,CAAClR,EAAE,GAAGA,EAAE;AACrC,CAAC,EAAEkR,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,gCAAgC;AAC3C,CAAC,UAAUA,gCAAgC,EAAE;EACzC;AACJ;AACA;EACI,SAAS7Q,MAAMA,CAACiB,KAAK,EAAE6P,UAAU,EAAE;IAC/B,OAAO;MAAE7P,KAAK;MAAE6P;IAAW,CAAC;EAChC;EACAD,gCAAgC,CAAC7Q,MAAM,GAAGA,MAAM;EAChD,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOW,SAAS,KAAKe,SAAS,IAAIf,SAAS,KAAK,IAAI,IAAIG,KAAK,CAACf,EAAE,CAACY,SAAS,CAACa,KAAK,CAAC,KACzEZ,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC0Q,UAAU,CAAC,IAAI1Q,SAAS,CAAC0Q,UAAU,KAAK3P,SAAS,CAAC;EAClF;EACA0P,gCAAgC,CAACrR,EAAE,GAAGA,EAAE;AAC5C,CAAC,EAAEqR,gCAAgC,KAAKA,gCAAgC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;AACJ;AACA;EACI,SAAS/Q,MAAMA,CAACgR,OAAO,EAAEC,eAAe,EAAE;IACtC,OAAO;MAAED,OAAO;MAAEC;IAAgB,CAAC;EACvC;EACAF,kBAAkB,CAAC/Q,MAAM,GAAGA,MAAM;EAClC;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIG,KAAK,CAACf,EAAE,CAACC,KAAK,CAACwR,eAAe,CAAC;EACnE;EACAF,kBAAkB,CAACvR,EAAE,GAAGA,EAAE;AAC9B,CAAC,EAAEuR,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACC,IAAI,GAAG,CAAC;EACtB;AACJ;AACA;EACID,aAAa,CAACE,SAAS,GAAG,CAAC;EAC3B,SAAS5R,EAAEA,CAACC,KAAK,EAAE;IACf,OAAOA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC;EACrC;EACAyR,aAAa,CAAC1R,EAAE,GAAGA,EAAE;AACzB,CAAC,EAAE0R,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,OAAO,IAAIG,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3B,SAASrR,MAAMA,CAACP,KAAK,EAAE;IACnB,OAAO;MAAEA;IAAM,CAAC;EACpB;EACA4R,kBAAkB,CAACrR,MAAM,GAAGA,MAAM;EAClC,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,KAC1BA,SAAS,CAACkR,OAAO,KAAKnQ,SAAS,IAAId,EAAE,CAACa,MAAM,CAACd,SAAS,CAACkR,OAAO,CAAC,IAAIpH,aAAa,CAAC1K,EAAE,CAACY,SAAS,CAACkR,OAAO,CAAC,CAAC,KACvGlR,SAAS,CAACiD,QAAQ,KAAKlC,SAAS,IAAIJ,QAAQ,CAACvB,EAAE,CAACY,SAAS,CAACiD,QAAQ,CAAC,CAAC,KACpEjD,SAAS,CAACsE,OAAO,KAAKvD,SAAS,IAAIqD,OAAO,CAAChF,EAAE,CAACY,SAAS,CAACsE,OAAO,CAAC,CAAC;EAC7E;EACA2M,kBAAkB,CAAC7R,EAAE,GAAGA,EAAE;AAC9B,CAAC,EAAE6R,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,OAAO,IAAIE,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClB,SAASvR,MAAMA,CAACiF,QAAQ,EAAE/C,KAAK,EAAEc,IAAI,EAAE;IACnC,MAAME,MAAM,GAAG;MAAE+B,QAAQ;MAAE/C;IAAM,CAAC;IAClC,IAAIc,IAAI,KAAK7B,SAAS,EAAE;MACpB+B,MAAM,CAACF,IAAI,GAAGA,IAAI;IACtB;IACA,OAAOE,MAAM;EACjB;EACAqO,SAAS,CAACvR,MAAM,GAAGA,MAAM;EACzB,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIL,QAAQ,CAACP,EAAE,CAACY,SAAS,CAAC6E,QAAQ,CAAC,KAC7D5E,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC8B,KAAK,CAAC,IAAI7B,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAAC8B,KAAK,EAAEmP,kBAAkB,CAAC7R,EAAE,CAAC,CAAC,KACpFY,SAAS,CAAC4C,IAAI,KAAK7B,SAAS,IAAI+P,aAAa,CAAC1R,EAAE,CAACY,SAAS,CAAC4C,IAAI,CAAC,CAAC,IACjE5C,SAAS,CAACoR,SAAS,KAAKrQ,SAAU,IAAId,EAAE,CAACiC,UAAU,CAAClC,SAAS,CAACoR,SAAS,EAAEnP,QAAQ,CAAC7C,EAAE,CAAC,KACrFY,SAAS,CAACkR,OAAO,KAAKnQ,SAAS,IAAId,EAAE,CAACa,MAAM,CAACd,SAAS,CAACkR,OAAO,CAAC,IAAIpH,aAAa,CAAC1K,EAAE,CAACY,SAAS,CAACkR,OAAO,CAAC,CAAC,KACvGlR,SAAS,CAACqR,WAAW,KAAKtQ,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACqR,WAAW,CAAC,CAAC,KACzErR,SAAS,CAACsR,YAAY,KAAKvQ,SAAS,IAAId,EAAE,CAACiF,OAAO,CAAClF,SAAS,CAACsR,YAAY,CAAC,CAAC;EACvF;EACAH,SAAS,CAAC/R,EAAE,GAAGA,EAAE;AACrB,CAAC,EAAE+R,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO,IAAII,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpB,SAASC,aAAaA,CAACnS,KAAK,EAAE;IAC1B,OAAO;MAAEuD,IAAI,EAAE,SAAS;MAAEvD;IAAM,CAAC;EACrC;EACAkS,WAAW,CAACC,aAAa,GAAGA,aAAa;AAC7C,CAAC,EAAED,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,OAAO,IAAIE,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7B,SAAS7R,MAAMA,CAAC8R,UAAU,EAAEC,UAAU,EAAE9Q,KAAK,EAAEyD,OAAO,EAAE;IACpD,OAAO;MAAEoN,UAAU;MAAEC,UAAU;MAAE9Q,KAAK;MAAEyD;IAAQ,CAAC;EACrD;EACAmN,oBAAoB,CAAC7R,MAAM,GAAGA,MAAM;AACxC,CAAC,EAAE6R,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,OAAO,IAAIG,oBAAoB;AAC/B,CAAC,UAAUA,oBAAoB,EAAE;EAC7B,SAAShS,MAAMA,CAACsM,KAAK,EAAE;IACnB,OAAO;MAAEA;IAAM,CAAC;EACpB;EACA0F,oBAAoB,CAAChS,MAAM,GAAGA,MAAM;AACxC,CAAC,EAAEgS,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,2BAA2B;AACtC,CAAC,UAAUA,2BAA2B,EAAE;EACpC;AACJ;AACA;EACIA,2BAA2B,CAAC/C,OAAO,GAAG,CAAC;EACvC;AACJ;AACA;EACI+C,2BAA2B,CAAC9C,SAAS,GAAG,CAAC;AAC7C,CAAC,EAAE8C,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,OAAO,IAAIC,sBAAsB;AACjC,CAAC,UAAUA,sBAAsB,EAAE;EAC/B,SAASlS,MAAMA,CAACiB,KAAK,EAAE6I,IAAI,EAAE;IACzB,OAAO;MAAE7I,KAAK;MAAE6I;IAAK,CAAC;EAC1B;EACAoI,sBAAsB,CAAClS,MAAM,GAAGA,MAAM;AAC1C,CAAC,EAAEkS,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,OAAO,IAAIC,uBAAuB;AAClC,CAAC,UAAUA,uBAAuB,EAAE;EAChC,SAASnS,MAAMA,CAACuP,WAAW,EAAE6C,sBAAsB,EAAE;IACjD,OAAO;MAAE7C,WAAW;MAAE6C;IAAuB,CAAC;EAClD;EACAD,uBAAuB,CAACnS,MAAM,GAAGA,MAAM;AAC3C,CAAC,EAAEmS,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,OAAO,IAAIE,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxB,SAAS7S,EAAEA,CAACC,KAAK,EAAE;IACf,MAAMW,SAAS,GAAGX,KAAK;IACvB,OAAOY,EAAE,CAACC,aAAa,CAACF,SAAS,CAAC,IAAIV,GAAG,CAACF,EAAE,CAACY,SAAS,CAACY,GAAG,CAAC,IAAIX,EAAE,CAACa,MAAM,CAACd,SAAS,CAAC2N,IAAI,CAAC;EAC5F;EACAsE,eAAe,CAAC7S,EAAE,GAAGA,EAAE;AAC3B,CAAC,EAAE6S,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO,MAAMC,GAAG,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;AACvC;AACA;AACA;AACA,OAAO,IAAIC,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASvS,MAAMA,CAACgB,GAAG,EAAE6I,UAAU,EAAEX,OAAO,EAAEsJ,OAAO,EAAE;IAC/C,OAAO,IAAIC,gBAAgB,CAACzR,GAAG,EAAE6I,UAAU,EAAEX,OAAO,EAAEsJ,OAAO,CAAC;EAClE;EACAD,YAAY,CAACvS,MAAM,GAAGA,MAAM;EAC5B;AACJ;AACA;EACI,SAASR,EAAEA,CAACC,KAAK,EAAE;IACf,IAAIW,SAAS,GAAGX,KAAK;IACrB,OAAOY,EAAE,CAAC8C,OAAO,CAAC/C,SAAS,CAAC,IAAIC,EAAE,CAACa,MAAM,CAACd,SAAS,CAACY,GAAG,CAAC,KAAKX,EAAE,CAACc,SAAS,CAACf,SAAS,CAACyJ,UAAU,CAAC,IAAIxJ,EAAE,CAACa,MAAM,CAACd,SAAS,CAACyJ,UAAU,CAAC,CAAC,IAAIxJ,EAAE,CAACP,QAAQ,CAACM,SAAS,CAACsS,SAAS,CAAC,IAChKrS,EAAE,CAACsS,IAAI,CAACvS,SAAS,CAACwS,OAAO,CAAC,IAAIvS,EAAE,CAACsS,IAAI,CAACvS,SAAS,CAACyS,UAAU,CAAC,IAAIxS,EAAE,CAACsS,IAAI,CAACvS,SAAS,CAAC0S,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK;EACpH;EACAP,YAAY,CAAC/S,EAAE,GAAGA,EAAE;EACpB,SAASuT,UAAUA,CAACC,QAAQ,EAAEnN,KAAK,EAAE;IACjC,IAAIiE,IAAI,GAAGkJ,QAAQ,CAACJ,OAAO,CAAC,CAAC;IAC7B,IAAIK,WAAW,GAAGC,SAAS,CAACrN,KAAK,EAAE,CAACsN,CAAC,EAAEC,CAAC,KAAK;MACzC,IAAIC,IAAI,GAAGF,CAAC,CAAClS,KAAK,CAACL,KAAK,CAACX,IAAI,GAAGmT,CAAC,CAACnS,KAAK,CAACL,KAAK,CAACX,IAAI;MAClD,IAAIoT,IAAI,KAAK,CAAC,EAAE;QACZ,OAAOF,CAAC,CAAClS,KAAK,CAACL,KAAK,CAACV,SAAS,GAAGkT,CAAC,CAACnS,KAAK,CAACL,KAAK,CAACV,SAAS;MAC5D;MACA,OAAOmT,IAAI;IACf,CAAC,CAAC;IACF,IAAIC,kBAAkB,GAAGxJ,IAAI,CAAClF,MAAM;IACpC,KAAK,IAAI2O,CAAC,GAAGN,WAAW,CAACrO,MAAM,GAAG,CAAC,EAAE2O,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAIC,CAAC,GAAGP,WAAW,CAACM,CAAC,CAAC;MACtB,IAAIE,WAAW,GAAGT,QAAQ,CAACF,QAAQ,CAACU,CAAC,CAACvS,KAAK,CAACL,KAAK,CAAC;MAClD,IAAI8S,SAAS,GAAGV,QAAQ,CAACF,QAAQ,CAACU,CAAC,CAACvS,KAAK,CAACJ,GAAG,CAAC;MAC9C,IAAI6S,SAAS,IAAIJ,kBAAkB,EAAE;QACjCxJ,IAAI,GAAGA,IAAI,CAAC6J,SAAS,CAAC,CAAC,EAAEF,WAAW,CAAC,GAAGD,CAAC,CAACzO,OAAO,GAAG+E,IAAI,CAAC6J,SAAS,CAACD,SAAS,EAAE5J,IAAI,CAAClF,MAAM,CAAC;MAC9F,CAAC,MACI;QACD,MAAM,IAAI9D,KAAK,CAAC,kBAAkB,CAAC;MACvC;MACAwS,kBAAkB,GAAGG,WAAW;IACpC;IACA,OAAO3J,IAAI;EACf;EACAyI,YAAY,CAACQ,UAAU,GAAGA,UAAU;EACpC,SAASG,SAASA,CAACrD,IAAI,EAAE+D,OAAO,EAAE;IAC9B,IAAI/D,IAAI,CAACjL,MAAM,IAAI,CAAC,EAAE;MAClB;MACA,OAAOiL,IAAI;IACf;IACA,MAAMgE,CAAC,GAAIhE,IAAI,CAACjL,MAAM,GAAG,CAAC,GAAI,CAAC;IAC/B,MAAMkP,IAAI,GAAGjE,IAAI,CAACkE,KAAK,CAAC,CAAC,EAAEF,CAAC,CAAC;IAC7B,MAAMG,KAAK,GAAGnE,IAAI,CAACkE,KAAK,CAACF,CAAC,CAAC;IAC3BX,SAAS,CAACY,IAAI,EAAEF,OAAO,CAAC;IACxBV,SAAS,CAACc,KAAK,EAAEJ,OAAO,CAAC;IACzB,IAAIK,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIX,CAAC,GAAG,CAAC;IACT,OAAOU,OAAO,GAAGH,IAAI,CAAClP,MAAM,IAAIsP,QAAQ,GAAGF,KAAK,CAACpP,MAAM,EAAE;MACrD,IAAIuP,GAAG,GAAGP,OAAO,CAACE,IAAI,CAACG,OAAO,CAAC,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC;MACjD,IAAIC,GAAG,IAAI,CAAC,EAAE;QACV;QACAtE,IAAI,CAAC0D,CAAC,EAAE,CAAC,GAAGO,IAAI,CAACG,OAAO,EAAE,CAAC;MAC/B,CAAC,MACI;QACD;QACApE,IAAI,CAAC0D,CAAC,EAAE,CAAC,GAAGS,KAAK,CAACE,QAAQ,EAAE,CAAC;MACjC;IACJ;IACA,OAAOD,OAAO,GAAGH,IAAI,CAAClP,MAAM,EAAE;MAC1BiL,IAAI,CAAC0D,CAAC,EAAE,CAAC,GAAGO,IAAI,CAACG,OAAO,EAAE,CAAC;IAC/B;IACA,OAAOC,QAAQ,GAAGF,KAAK,CAACpP,MAAM,EAAE;MAC5BiL,IAAI,CAAC0D,CAAC,EAAE,CAAC,GAAGS,KAAK,CAACE,QAAQ,EAAE,CAAC;IACjC;IACA,OAAOrE,IAAI;EACf;AACJ,CAAC,EAAE0C,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EACnBxL,WAAWA,CAACjG,GAAG,EAAE6I,UAAU,EAAEX,OAAO,EAAEsJ,OAAO,EAAE;IAC3C,IAAI,CAAC4B,IAAI,GAAGpT,GAAG;IACf,IAAI,CAACqT,WAAW,GAAGxK,UAAU;IAC7B,IAAI,CAACyK,QAAQ,GAAGpL,OAAO;IACvB,IAAI,CAACqL,QAAQ,GAAG/B,OAAO;IACvB,IAAI,CAACgC,YAAY,GAAGrT,SAAS;EACjC;EACA,IAAIH,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACoT,IAAI;EACpB;EACA,IAAIvK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACwK,WAAW;EAC3B;EACA,IAAInL,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoL,QAAQ;EACxB;EACA1B,OAAOA,CAAC3R,KAAK,EAAE;IACX,IAAIA,KAAK,EAAE;MACP,IAAIL,KAAK,GAAG,IAAI,CAACkS,QAAQ,CAAC7R,KAAK,CAACL,KAAK,CAAC;MACtC,IAAIC,GAAG,GAAG,IAAI,CAACiS,QAAQ,CAAC7R,KAAK,CAACJ,GAAG,CAAC;MAClC,OAAO,IAAI,CAAC0T,QAAQ,CAACZ,SAAS,CAAC/S,KAAK,EAAEC,GAAG,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC0T,QAAQ;EACxB;EACAE,MAAMA,CAACC,KAAK,EAAExL,OAAO,EAAE;IACnB,IAAI,CAACqL,QAAQ,GAAGG,KAAK,CAAC5K,IAAI;IAC1B,IAAI,CAACwK,QAAQ,GAAGpL,OAAO;IACvB,IAAI,CAACsL,YAAY,GAAGrT,SAAS;EACjC;EACAwT,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACH,YAAY,KAAKrT,SAAS,EAAE;MACjC,IAAIyT,WAAW,GAAG,EAAE;MACpB,IAAI9K,IAAI,GAAG,IAAI,CAACyK,QAAQ;MACxB,IAAIM,WAAW,GAAG,IAAI;MACtB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzJ,IAAI,CAAClF,MAAM,EAAE2O,CAAC,EAAE,EAAE;QAClC,IAAIsB,WAAW,EAAE;UACbD,WAAW,CAACrN,IAAI,CAACgM,CAAC,CAAC;UACnBsB,WAAW,GAAG,KAAK;QACvB;QACA,IAAIC,EAAE,GAAGhL,IAAI,CAACiL,MAAM,CAACxB,CAAC,CAAC;QACvBsB,WAAW,GAAIC,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,IAAK;QAC1C,IAAIA,EAAE,KAAK,IAAI,IAAIvB,CAAC,GAAG,CAAC,GAAGzJ,IAAI,CAAClF,MAAM,IAAIkF,IAAI,CAACiL,MAAM,CAACxB,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;UACnEA,CAAC,EAAE;QACP;MACJ;MACA,IAAIsB,WAAW,IAAI/K,IAAI,CAAClF,MAAM,GAAG,CAAC,EAAE;QAChCgQ,WAAW,CAACrN,IAAI,CAACuC,IAAI,CAAClF,MAAM,CAAC;MACjC;MACA,IAAI,CAAC4P,YAAY,GAAGI,WAAW;IACnC;IACA,OAAO,IAAI,CAACJ,YAAY;EAC5B;EACA3B,UAAUA,CAACmC,MAAM,EAAE;IACfA,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,MAAM,EAAE,IAAI,CAACT,QAAQ,CAAC3P,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5D,IAAIgQ,WAAW,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACvC,IAAIS,GAAG,GAAG,CAAC;MAAEC,IAAI,GAAGT,WAAW,CAAChQ,MAAM;IACtC,IAAIyQ,IAAI,KAAK,CAAC,EAAE;MACZ,OAAOtV,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAEgV,MAAM,CAAC;IACrC;IACA,OAAOI,GAAG,GAAGC,IAAI,EAAE;MACf,IAAIC,GAAG,GAAGL,IAAI,CAACM,KAAK,CAAC,CAACH,GAAG,GAAGC,IAAI,IAAI,CAAC,CAAC;MACtC,IAAIT,WAAW,CAACU,GAAG,CAAC,GAAGN,MAAM,EAAE;QAC3BK,IAAI,GAAGC,GAAG;MACd,CAAC,MACI;QACDF,GAAG,GAAGE,GAAG,GAAG,CAAC;MACjB;IACJ;IACA;IACA;IACA,IAAIrV,IAAI,GAAGmV,GAAG,GAAG,CAAC;IAClB,OAAOrV,QAAQ,CAACC,MAAM,CAACC,IAAI,EAAE+U,MAAM,GAAGJ,WAAW,CAAC3U,IAAI,CAAC,CAAC;EAC5D;EACA6S,QAAQA,CAAC7N,QAAQ,EAAE;IACf,IAAI2P,WAAW,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACvC,IAAI1P,QAAQ,CAAChF,IAAI,IAAI2U,WAAW,CAAChQ,MAAM,EAAE;MACrC,OAAO,IAAI,CAAC2P,QAAQ,CAAC3P,MAAM;IAC/B,CAAC,MACI,IAAIK,QAAQ,CAAChF,IAAI,GAAG,CAAC,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,IAAIuV,UAAU,GAAGZ,WAAW,CAAC3P,QAAQ,CAAChF,IAAI,CAAC;IAC3C,IAAIwV,cAAc,GAAIxQ,QAAQ,CAAChF,IAAI,GAAG,CAAC,GAAG2U,WAAW,CAAChQ,MAAM,GAAIgQ,WAAW,CAAC3P,QAAQ,CAAChF,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAACsU,QAAQ,CAAC3P,MAAM;IACrH,OAAOqQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACK,UAAU,GAAGvQ,QAAQ,CAAC/E,SAAS,EAAEuV,cAAc,CAAC,EAAED,UAAU,CAAC;EAC1F;EACA,IAAI9C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiC,cAAc,CAAC,CAAC,CAAC/P,MAAM;EACvC;AACJ;AACA,IAAIvE,EAAE;AACN,CAAC,UAAUA,EAAE,EAAE;EACX,MAAMiI,QAAQ,GAAGN,MAAM,CAAC0N,SAAS,CAACpN,QAAQ;EAC1C,SAASnF,OAAOA,CAAC1D,KAAK,EAAE;IACpB,OAAO,OAAOA,KAAK,KAAK,WAAW;EACvC;EACAY,EAAE,CAAC8C,OAAO,GAAGA,OAAO;EACpB,SAAShC,SAASA,CAAC1B,KAAK,EAAE;IACtB,OAAO,OAAOA,KAAK,KAAK,WAAW;EACvC;EACAY,EAAE,CAACc,SAAS,GAAGA,SAAS;EACxB,SAASmE,OAAOA,CAAC7F,KAAK,EAAE;IACpB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK;EAC5C;EACAY,EAAE,CAACiF,OAAO,GAAGA,OAAO;EACpB,SAASpE,MAAMA,CAACzB,KAAK,EAAE;IACnB,OAAO6I,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,iBAAiB;EACrD;EACAY,EAAE,CAACa,MAAM,GAAGA,MAAM;EAClB,SAASoD,MAAMA,CAAC7E,KAAK,EAAE;IACnB,OAAO6I,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,iBAAiB;EACrD;EACAY,EAAE,CAACiE,MAAM,GAAGA,MAAM;EAClB,SAASxC,WAAWA,CAACrC,KAAK,EAAE0V,GAAG,EAAED,GAAG,EAAE;IAClC,OAAO5M,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,iBAAiB,IAAI0V,GAAG,IAAI1V,KAAK,IAAIA,KAAK,IAAIyV,GAAG;EACrF;EACA7U,EAAE,CAACyB,WAAW,GAAGA,WAAW;EAC5B,SAASnC,OAAOA,CAACF,KAAK,EAAE;IACpB,OAAO6I,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,iBAAiB,IAAI,CAAC,UAAU,IAAIA,KAAK,IAAIA,KAAK,IAAI,UAAU;EACpG;EACAY,EAAE,CAACV,OAAO,GAAGA,OAAO;EACpB,SAASG,QAAQA,CAACL,KAAK,EAAE;IACrB,OAAO6I,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,iBAAiB,IAAI,CAAC,IAAIA,KAAK,IAAIA,KAAK,IAAI,UAAU;EAC1F;EACAY,EAAE,CAACP,QAAQ,GAAGA,QAAQ;EACtB,SAAS6S,IAAIA,CAAClT,KAAK,EAAE;IACjB,OAAO6I,QAAQ,CAACqN,IAAI,CAAClW,KAAK,CAAC,KAAK,mBAAmB;EACvD;EACAY,EAAE,CAACsS,IAAI,GAAGA,IAAI;EACd,SAASrS,aAAaA,CAACb,KAAK,EAAE;IAC1B;IACA;IACA;IACA,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ;EACtD;EACAY,EAAE,CAACC,aAAa,GAAGA,aAAa;EAChC,SAASgC,UAAUA,CAAC7C,KAAK,EAAEmW,KAAK,EAAE;IAC9B,OAAO7P,KAAK,CAACC,OAAO,CAACvG,KAAK,CAAC,IAAIA,KAAK,CAACqH,KAAK,CAAC8O,KAAK,CAAC;EACrD;EACAvV,EAAE,CAACiC,UAAU,GAAGA,UAAU;AAC9B,CAAC,EAAEjC,EAAE,KAAKA,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}