{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { tokenMatcher, tokenLabel, NonTerminal, Alternation, Option, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Repetition, Terminal, LLkLookaheadStrategy, getLookaheadPaths } from \"chevrotain\";\nimport { ATN_RULE_STOP, AtomTransition, buildATNKey, createATN, EpsilonTransition, RuleTransition } from \"./atn.js\";\nimport { ATNConfigSet, DFA_ERROR, getATNConfigKey } from \"./dfa.js\";\nimport min from \"lodash-es/min.js\";\nimport flatMap from \"lodash-es/flatMap.js\";\nimport uniqBy from \"lodash-es/uniqBy.js\";\nimport map from \"lodash-es/map.js\";\nimport flatten from \"lodash-es/flatten.js\";\nimport forEach from \"lodash-es/forEach.js\";\nimport isEmpty from \"lodash-es/isEmpty.js\";\nimport reduce from \"lodash-es/reduce.js\";\nfunction createDFACache(startState, decision) {\n  const map = {};\n  return predicateSet => {\n    const key = predicateSet.toString();\n    let existing = map[key];\n    if (existing !== undefined) {\n      return existing;\n    } else {\n      existing = {\n        atnStartState: startState,\n        decision,\n        states: {}\n      };\n      map[key] = existing;\n      return existing;\n    }\n  };\n}\nclass PredicateSet {\n  constructor() {\n    this.predicates = [];\n  }\n  is(index) {\n    return index >= this.predicates.length || this.predicates[index];\n  }\n  set(index, value) {\n    this.predicates[index] = value;\n  }\n  toString() {\n    let value = \"\";\n    const size = this.predicates.length;\n    for (let i = 0; i < size; i++) {\n      value += this.predicates[i] === true ? \"1\" : \"0\";\n    }\n    return value;\n  }\n}\nconst EMPTY_PREDICATES = new PredicateSet();\nexport class LLStarLookaheadStrategy extends LLkLookaheadStrategy {\n  constructor(options) {\n    var _a;\n    super();\n    this.logging = (_a = options === null || options === void 0 ? void 0 : options.logging) !== null && _a !== void 0 ? _a : message => console.log(message);\n  }\n  initialize(options) {\n    this.atn = createATN(options.rules);\n    this.dfas = initATNSimulator(this.atn);\n  }\n  validateAmbiguousAlternationAlternatives() {\n    return [];\n  }\n  validateEmptyOrAlternatives() {\n    return [];\n  }\n  buildLookaheadForAlternation(options) {\n    const {\n      prodOccurrence,\n      rule,\n      hasPredicates,\n      dynamicTokensEnabled\n    } = options;\n    const dfas = this.dfas;\n    const logging = this.logging;\n    const key = buildATNKey(rule, 'Alternation', prodOccurrence);\n    const decisionState = this.atn.decisionMap[key];\n    const decisionIndex = decisionState.decision;\n    const partialAlts = map(getLookaheadPaths({\n      maxLookahead: 1,\n      occurrence: prodOccurrence,\n      prodType: \"Alternation\",\n      rule: rule\n    }), currAlt => map(currAlt, path => path[0]));\n    if (isLL1Sequence(partialAlts, false) && !dynamicTokensEnabled) {\n      const choiceToAlt = reduce(partialAlts, (result, currAlt, idx) => {\n        forEach(currAlt, currTokType => {\n          if (currTokType) {\n            result[currTokType.tokenTypeIdx] = idx;\n            forEach(currTokType.categoryMatches, currExtendingType => {\n              result[currExtendingType] = idx;\n            });\n          }\n        });\n        return result;\n      }, {});\n      if (hasPredicates) {\n        return function (orAlts) {\n          var _a;\n          const nextToken = this.LA(1);\n          const prediction = choiceToAlt[nextToken.tokenTypeIdx];\n          if (orAlts !== undefined && prediction !== undefined) {\n            const gate = (_a = orAlts[prediction]) === null || _a === void 0 ? void 0 : _a.GATE;\n            if (gate !== undefined && gate.call(this) === false) {\n              return undefined;\n            }\n          }\n          return prediction;\n        };\n      } else {\n        return function () {\n          const nextToken = this.LA(1);\n          return choiceToAlt[nextToken.tokenTypeIdx];\n        };\n      }\n    } else if (hasPredicates) {\n      return function (orAlts) {\n        const predicates = new PredicateSet();\n        const length = orAlts === undefined ? 0 : orAlts.length;\n        for (let i = 0; i < length; i++) {\n          const gate = orAlts === null || orAlts === void 0 ? void 0 : orAlts[i].GATE;\n          predicates.set(i, gate === undefined || gate.call(this));\n        }\n        const result = adaptivePredict.call(this, dfas, decisionIndex, predicates, logging);\n        return typeof result === 'number' ? result : undefined;\n      };\n    } else {\n      return function () {\n        const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n        return typeof result === 'number' ? result : undefined;\n      };\n    }\n  }\n  buildLookaheadForOptional(options) {\n    const {\n      prodOccurrence,\n      rule,\n      prodType,\n      dynamicTokensEnabled\n    } = options;\n    const dfas = this.dfas;\n    const logging = this.logging;\n    const key = buildATNKey(rule, prodType, prodOccurrence);\n    const decisionState = this.atn.decisionMap[key];\n    const decisionIndex = decisionState.decision;\n    const alts = map(getLookaheadPaths({\n      maxLookahead: 1,\n      occurrence: prodOccurrence,\n      prodType,\n      rule\n    }), e => {\n      return map(e, g => g[0]);\n    });\n    if (isLL1Sequence(alts) && alts[0][0] && !dynamicTokensEnabled) {\n      const alt = alts[0];\n      const singleTokensTypes = flatten(alt);\n      if (singleTokensTypes.length === 1 && isEmpty(singleTokensTypes[0].categoryMatches)) {\n        const expectedTokenType = singleTokensTypes[0];\n        const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx;\n        return function () {\n          return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;\n        };\n      } else {\n        const choiceToAlt = reduce(singleTokensTypes, (result, currTokType) => {\n          if (currTokType !== undefined) {\n            result[currTokType.tokenTypeIdx] = true;\n            forEach(currTokType.categoryMatches, currExtendingType => {\n              result[currExtendingType] = true;\n            });\n          }\n          return result;\n        }, {});\n        return function () {\n          const nextToken = this.LA(1);\n          return choiceToAlt[nextToken.tokenTypeIdx] === true;\n        };\n      }\n    }\n    return function () {\n      const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n      return typeof result === \"object\" ? false : result === 0;\n    };\n  }\n}\nfunction isLL1Sequence(sequences, allowEmpty = true) {\n  const fullSet = new Set();\n  for (const alt of sequences) {\n    const altSet = new Set();\n    for (const tokType of alt) {\n      if (tokType === undefined) {\n        if (allowEmpty) {\n          // Epsilon production encountered\n          break;\n        } else {\n          return false;\n        }\n      }\n      const indices = [tokType.tokenTypeIdx].concat(tokType.categoryMatches);\n      for (const index of indices) {\n        if (fullSet.has(index)) {\n          if (!altSet.has(index)) {\n            return false;\n          }\n        } else {\n          fullSet.add(index);\n          altSet.add(index);\n        }\n      }\n    }\n  }\n  return true;\n}\nfunction initATNSimulator(atn) {\n  const decisionLength = atn.decisionStates.length;\n  const decisionToDFA = Array(decisionLength);\n  for (let i = 0; i < decisionLength; i++) {\n    decisionToDFA[i] = createDFACache(atn.decisionStates[i], i);\n  }\n  return decisionToDFA;\n}\nfunction adaptivePredict(dfaCaches, decision, predicateSet, logging) {\n  const dfa = dfaCaches[decision](predicateSet);\n  let start = dfa.start;\n  if (start === undefined) {\n    const closure = computeStartState(dfa.atnStartState);\n    start = addDFAState(dfa, newDFAState(closure));\n    dfa.start = start;\n  }\n  const alt = performLookahead.apply(this, [dfa, start, predicateSet, logging]);\n  return alt;\n}\nfunction performLookahead(dfa, s0, predicateSet, logging) {\n  let previousD = s0;\n  let i = 1;\n  const path = [];\n  let t = this.LA(i++);\n  while (true) {\n    let d = getExistingTargetState(previousD, t);\n    if (d === undefined) {\n      d = computeLookaheadTarget.apply(this, [dfa, previousD, t, i, predicateSet, logging]);\n    }\n    if (d === DFA_ERROR) {\n      return buildAdaptivePredictError(path, previousD, t);\n    }\n    if (d.isAcceptState === true) {\n      return d.prediction;\n    }\n    previousD = d;\n    path.push(t);\n    t = this.LA(i++);\n  }\n}\nfunction computeLookaheadTarget(dfa, previousD, token, lookahead, predicateSet, logging) {\n  const reach = computeReachSet(previousD.configs, token, predicateSet);\n  if (reach.size === 0) {\n    addDFAEdge(dfa, previousD, token, DFA_ERROR);\n    return DFA_ERROR;\n  }\n  let newState = newDFAState(reach);\n  const predictedAlt = getUniqueAlt(reach, predicateSet);\n  if (predictedAlt !== undefined) {\n    newState.isAcceptState = true;\n    newState.prediction = predictedAlt;\n    newState.configs.uniqueAlt = predictedAlt;\n  } else if (hasConflictTerminatingPrediction(reach)) {\n    const prediction = min(reach.alts);\n    newState.isAcceptState = true;\n    newState.prediction = prediction;\n    newState.configs.uniqueAlt = prediction;\n    reportLookaheadAmbiguity.apply(this, [dfa, lookahead, reach.alts, logging]);\n  }\n  newState = addDFAEdge(dfa, previousD, token, newState);\n  return newState;\n}\nfunction reportLookaheadAmbiguity(dfa, lookahead, ambiguityIndices, logging) {\n  const prefixPath = [];\n  for (let i = 1; i <= lookahead; i++) {\n    prefixPath.push(this.LA(i).tokenType);\n  }\n  const atnState = dfa.atnStartState;\n  const topLevelRule = atnState.rule;\n  const production = atnState.production;\n  const message = buildAmbiguityError({\n    topLevelRule,\n    ambiguityIndices,\n    production,\n    prefixPath\n  });\n  logging(message);\n}\nfunction buildAmbiguityError(options) {\n  const pathMsg = map(options.prefixPath, currtok => tokenLabel(currtok)).join(\", \");\n  const occurrence = options.production.idx === 0 ? \"\" : options.production.idx;\n  let currMessage = `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\", \")}> in <${getProductionDslName(options.production)}${occurrence}>` + ` inside <${options.topLevelRule.name}> Rule,\\n` + `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`;\n  currMessage = currMessage + `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` + `For Further details.`;\n  return currMessage;\n}\nfunction getProductionDslName(prod) {\n  if (prod instanceof NonTerminal) {\n    return \"SUBRULE\";\n  } else if (prod instanceof Option) {\n    return \"OPTION\";\n  } else if (prod instanceof Alternation) {\n    return \"OR\";\n  } else if (prod instanceof RepetitionMandatory) {\n    return \"AT_LEAST_ONE\";\n  } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n    return \"AT_LEAST_ONE_SEP\";\n  } else if (prod instanceof RepetitionWithSeparator) {\n    return \"MANY_SEP\";\n  } else if (prod instanceof Repetition) {\n    return \"MANY\";\n  } else if (prod instanceof Terminal) {\n    return \"CONSUME\";\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\nfunction buildAdaptivePredictError(path, previous, current) {\n  const nextTransitions = flatMap(previous.configs.elements, e => e.state.transitions);\n  const nextTokenTypes = uniqBy(nextTransitions.filter(e => e instanceof AtomTransition).map(e => e.tokenType), e => e.tokenTypeIdx);\n  return {\n    actualToken: current,\n    possibleTokenTypes: nextTokenTypes,\n    tokenPath: path\n  };\n}\nfunction getExistingTargetState(state, token) {\n  return state.edges[token.tokenTypeIdx];\n}\nfunction computeReachSet(configs, token, predicateSet) {\n  const intermediate = new ATNConfigSet();\n  const skippedStopStates = [];\n  for (const c of configs.elements) {\n    if (predicateSet.is(c.alt) === false) {\n      continue;\n    }\n    if (c.state.type === ATN_RULE_STOP) {\n      skippedStopStates.push(c);\n      continue;\n    }\n    const transitionLength = c.state.transitions.length;\n    for (let i = 0; i < transitionLength; i++) {\n      const transition = c.state.transitions[i];\n      const target = getReachableTarget(transition, token);\n      if (target !== undefined) {\n        intermediate.add({\n          state: target,\n          alt: c.alt,\n          stack: c.stack\n        });\n      }\n    }\n  }\n  let reach;\n  if (skippedStopStates.length === 0 && intermediate.size === 1) {\n    reach = intermediate;\n  }\n  if (reach === undefined) {\n    reach = new ATNConfigSet();\n    for (const c of intermediate.elements) {\n      closure(c, reach);\n    }\n  }\n  if (skippedStopStates.length > 0 && !hasConfigInRuleStopState(reach)) {\n    for (const c of skippedStopStates) {\n      reach.add(c);\n    }\n  }\n  return reach;\n}\nfunction getReachableTarget(transition, token) {\n  if (transition instanceof AtomTransition && tokenMatcher(token, transition.tokenType)) {\n    return transition.target;\n  }\n  return undefined;\n}\nfunction getUniqueAlt(configs, predicateSet) {\n  let alt;\n  for (const c of configs.elements) {\n    if (predicateSet.is(c.alt) === true) {\n      if (alt === undefined) {\n        alt = c.alt;\n      } else if (alt !== c.alt) {\n        return undefined;\n      }\n    }\n  }\n  return alt;\n}\nfunction newDFAState(closure) {\n  return {\n    configs: closure,\n    edges: {},\n    isAcceptState: false,\n    prediction: -1\n  };\n}\nfunction addDFAEdge(dfa, from, token, to) {\n  to = addDFAState(dfa, to);\n  from.edges[token.tokenTypeIdx] = to;\n  return to;\n}\nfunction addDFAState(dfa, state) {\n  if (state === DFA_ERROR) {\n    return state;\n  }\n  // Repetitions have the same config set\n  // Therefore, storing the key of the config in a map allows us to create a loop in our DFA\n  const mapKey = state.configs.key;\n  const existing = dfa.states[mapKey];\n  if (existing !== undefined) {\n    return existing;\n  }\n  state.configs.finalize();\n  dfa.states[mapKey] = state;\n  return state;\n}\nfunction computeStartState(atnState) {\n  const configs = new ATNConfigSet();\n  const numberOfTransitions = atnState.transitions.length;\n  for (let i = 0; i < numberOfTransitions; i++) {\n    const target = atnState.transitions[i].target;\n    const config = {\n      state: target,\n      alt: i,\n      stack: []\n    };\n    closure(config, configs);\n  }\n  return configs;\n}\nfunction closure(config, configs) {\n  const p = config.state;\n  if (p.type === ATN_RULE_STOP) {\n    if (config.stack.length > 0) {\n      const atnStack = [...config.stack];\n      const followState = atnStack.pop();\n      const followConfig = {\n        state: followState,\n        alt: config.alt,\n        stack: atnStack\n      };\n      closure(followConfig, configs);\n    } else {\n      // Dipping into outer context, simply add the config\n      // This will stop computation once every config is at the rule stop state\n      configs.add(config);\n    }\n    return;\n  }\n  if (!p.epsilonOnlyTransitions) {\n    configs.add(config);\n  }\n  const transitionLength = p.transitions.length;\n  for (let i = 0; i < transitionLength; i++) {\n    const transition = p.transitions[i];\n    const c = getEpsilonTarget(config, transition);\n    if (c !== undefined) {\n      closure(c, configs);\n    }\n  }\n}\nfunction getEpsilonTarget(config, transition) {\n  if (transition instanceof EpsilonTransition) {\n    return {\n      state: transition.target,\n      alt: config.alt,\n      stack: config.stack\n    };\n  } else if (transition instanceof RuleTransition) {\n    const stack = [...config.stack, transition.followState];\n    return {\n      state: transition.target,\n      alt: config.alt,\n      stack\n    };\n  }\n  return undefined;\n}\nfunction hasConfigInRuleStopState(configs) {\n  for (const c of configs.elements) {\n    if (c.state.type === ATN_RULE_STOP) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction allConfigsInRuleStopStates(configs) {\n  for (const c of configs.elements) {\n    if (c.state.type !== ATN_RULE_STOP) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction hasConflictTerminatingPrediction(configs) {\n  if (allConfigsInRuleStopStates(configs)) {\n    return true;\n  }\n  const altSets = getConflictingAltSets(configs.elements);\n  const heuristic = hasConflictingAltSet(altSets) && !hasStateAssociatedWithOneAlt(altSets);\n  return heuristic;\n}\nfunction getConflictingAltSets(configs) {\n  const configToAlts = new Map();\n  for (const c of configs) {\n    const key = getATNConfigKey(c, false);\n    let alts = configToAlts.get(key);\n    if (alts === undefined) {\n      alts = {};\n      configToAlts.set(key, alts);\n    }\n    alts[c.alt] = true;\n  }\n  return configToAlts;\n}\nfunction hasConflictingAltSet(altSets) {\n  for (const value of Array.from(altSets.values())) {\n    if (Object.keys(value).length > 1) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction hasStateAssociatedWithOneAlt(altSets) {\n  for (const value of Array.from(altSets.values())) {\n    if (Object.keys(value).length === 1) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["tokenMatcher", "tokenLabel", "NonTerminal", "Alternation", "Option", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Repetition", "Terminal", "LLkLookaheadStrategy", "get<PERSON><PERSON><PERSON>eadPath<PERSON>", "ATN_RULE_STOP", "AtomTransition", "buildATNKey", "createATN", "EpsilonTransition", "RuleTransition", "ATNConfigSet", "DFA_ERROR", "getATNConfigKey", "min", "flatMap", "uniqBy", "map", "flatten", "for<PERSON>ach", "isEmpty", "reduce", "createDFACache", "startState", "decision", "predicateSet", "key", "toString", "existing", "undefined", "atnStartState", "states", "PredicateSet", "constructor", "predicates", "is", "index", "length", "set", "value", "size", "i", "EMPTY_PREDICATES", "LLStarLookaheadStrategy", "options", "logging", "_a", "message", "console", "log", "initialize", "atn", "rules", "dfas", "initATNSimulator", "validateAmbiguousAlternationAlternatives", "validateEmptyOrAlternatives", "buildLookaheadForAlternation", "prodOccurrence", "rule", "hasPredicates", "dynamicTokensEnabled", "decisionState", "decisionMap", "decisionIndex", "partialAlts", "max<PERSON><PERSON><PERSON><PERSON>", "occurrence", "prodType", "currAlt", "path", "isLL1Sequence", "choiceToAlt", "result", "idx", "currTokType", "tokenTypeIdx", "categoryMatches", "currExtendingType", "orAlts", "nextToken", "LA", "prediction", "gate", "GATE", "call", "adaptivePredict", "buildLookaheadForOptional", "alts", "e", "g", "alt", "singleTokensTypes", "expectedTokenType", "expectedTokenUniqueKey", "sequences", "allowEmpty", "fullSet", "Set", "altSet", "tokType", "indices", "concat", "has", "add", "decision<PERSON><PERSON><PERSON>", "decisionStates", "decisionToDFA", "Array", "dfaCaches", "dfa", "start", "closure", "computeStartState", "addDFAState", "newDFAState", "performLookahead", "apply", "s0", "previousD", "t", "d", "getExistingTargetState", "computeLookaheadTarget", "buildAdaptivePredictError", "isAcceptState", "push", "token", "<PERSON><PERSON><PERSON>", "reach", "computeReachSet", "configs", "addDFAEdge", "newState", "predictedAlt", "getUniqueAlt", "uniqueAlt", "hasConflictTerminatingPrediction", "reportLookaheadAmbiguity", "ambiguityIndices", "prefixPath", "tokenType", "atnState", "topLevelRule", "production", "buildAmbiguityError", "pathMsg", "currtok", "join", "currMessage", "getProductionDslName", "name", "prod", "Error", "previous", "current", "nextTransitions", "elements", "state", "transitions", "nextTokenTypes", "filter", "actualToken", "possibleTokenTypes", "tokenPath", "edges", "intermediate", "skippedStopStates", "c", "type", "<PERSON><PERSON><PERSON><PERSON>", "transition", "target", "getReachable<PERSON><PERSON><PERSON>", "stack", "hasConfigInRuleStopState", "from", "to", "mapKey", "finalize", "numberOfTransitions", "config", "p", "atnStack", "followState", "pop", "followConfig", "epsilonOnlyTransitions", "getEpsilonTarget", "allConfigsInRuleStopStates", "altSets", "getConflictingAltSets", "heuristic", "hasConflictingAltSet", "hasStateAssociatedWithOneAlt", "configToAlts", "Map", "get", "values", "Object", "keys"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain-allstar/src/all-star-lookahead.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport {\r\n    IToken,\r\n    TokenType,\r\n    tokenMatcher,\r\n    tokenLabel,\r\n    Rule,\r\n    IProductionWithOccurrence,\r\n    NonTerminal,\r\n    Alternation,\r\n    Option,\r\n    RepetitionMandatory,\r\n    RepetitionMandatoryWithSeparator,\r\n    RepetitionWithSeparator,\r\n    Repetition,\r\n    Terminal,\r\n    BaseParser,\r\n    LLkLookaheadStrategy,\r\n    ILookaheadValidationError,\r\n    IOrAlt,\r\n    getLookaheadPaths,\r\n    OptionalProductionType\r\n} from \"chevrotain\";\r\nimport {\r\n    ATN,\r\n    ATNState,\r\n    ATN_RULE_STOP,\r\n    AtomTransition,\r\n    buildATNKey,\r\n    createATN,\r\n    DecisionState,\r\n    EpsilonTransition,\r\n    RuleTransition,\r\n    Transition\r\n} from \"./atn.js\";\r\nimport {\r\n    ATNConfig,\r\n    ATNConfigSet,\r\n    DFA,\r\n    DFAState,\r\n    DFA_ERROR,\r\n    getATNConfigKey\r\n} from \"./dfa.js\";\r\nimport min from \"lodash-es/min.js\";\r\nimport flatMap from \"lodash-es/flatMap.js\";\r\nimport uniqBy from \"lodash-es/uniqBy.js\";\r\nimport map from \"lodash-es/map.js\";\r\nimport flatten from \"lodash-es/flatten.js\";\r\nimport forEach from \"lodash-es/forEach.js\";\r\nimport isEmpty from \"lodash-es/isEmpty.js\";\r\nimport reduce from \"lodash-es/reduce.js\";\r\n\r\ntype DFACache = (predicateSet: PredicateSet) => DFA\r\n\r\nexport type AmbiguityReport = (message: string) => void;\r\n\r\nfunction createDFACache(startState: DecisionState, decision: number): DFACache {\r\n    const map: Record<string, DFA | undefined> = {}\r\n    return (predicateSet) => {\r\n        const key = predicateSet.toString()\r\n        let existing = map[key]\r\n        if (existing !== undefined) {\r\n            return existing\r\n        } else {\r\n            existing = {\r\n                atnStartState: startState,\r\n                decision,\r\n                states: {}\r\n            }\r\n            map[key] = existing\r\n            return existing\r\n        }\r\n    }\r\n}\r\n\r\nclass PredicateSet {\r\n    private predicates: boolean[] = []\r\n\r\n    is(index: number): boolean {\r\n        return index >= this.predicates.length || this.predicates[index]\r\n    }\r\n\r\n    set(index: number, value: boolean) {\r\n        this.predicates[index] = value\r\n    }\r\n\r\n    toString(): string {\r\n        let value = \"\"\r\n        const size = this.predicates.length\r\n        for (let i = 0; i < size; i++) {\r\n            value += this.predicates[i] === true ? \"1\" : \"0\"\r\n        }\r\n        return value\r\n    }\r\n}\r\n\r\ninterface AdaptivePredictError {\r\n    tokenPath: IToken[]\r\n    possibleTokenTypes: TokenType[]\r\n    actualToken: IToken\r\n}\r\n\r\nconst EMPTY_PREDICATES = new PredicateSet()\r\n\r\nexport interface LLStarLookaheadOptions {\r\n    logging?: AmbiguityReport\r\n}\r\n\r\nexport class LLStarLookaheadStrategy extends LLkLookaheadStrategy {\r\n\r\n    private atn: ATN;\r\n    private dfas: DFACache[];\r\n    private logging: AmbiguityReport;\r\n\r\n    constructor(options?: LLStarLookaheadOptions) {\r\n        super();\r\n        this.logging = options?.logging ?? ((message) => console.log(message));\r\n    }\r\n\r\n    override initialize(options: { rules: Rule[] }): void {\r\n        this.atn = createATN(options.rules);\r\n        this.dfas = initATNSimulator(this.atn);\r\n    }\r\n\r\n    override validateAmbiguousAlternationAlternatives(): ILookaheadValidationError[] {\r\n        return [];\r\n    }\r\n\r\n    override validateEmptyOrAlternatives(): ILookaheadValidationError[] {\r\n        return [];\r\n    }\r\n\r\n    override buildLookaheadForAlternation(options: {\r\n        prodOccurrence: number;\r\n        rule: Rule;\r\n        maxLookahead: number;\r\n        hasPredicates: boolean;\r\n        dynamicTokensEnabled: boolean\r\n    }): (this: BaseParser, orAlts?: IOrAlt<any>[] | undefined) => number | undefined {\r\n        const { prodOccurrence, rule, hasPredicates, dynamicTokensEnabled } = options;\r\n        const dfas = this.dfas;\r\n        const logging = this.logging;\r\n        const key = buildATNKey(rule, 'Alternation', prodOccurrence);\r\n        const decisionState = this.atn.decisionMap[key];\r\n        const decisionIndex = decisionState.decision;\r\n        const partialAlts: (TokenType | undefined)[][] = map(\r\n            getLookaheadPaths({\r\n                maxLookahead: 1,\r\n                occurrence: prodOccurrence,\r\n                prodType: \"Alternation\",\r\n                rule: rule\r\n            }),\r\n            (currAlt) => map(currAlt, (path) => path[0])\r\n        )\r\n\r\n        if (isLL1Sequence(partialAlts, false) && !dynamicTokensEnabled) {\r\n            const choiceToAlt = reduce(\r\n                partialAlts,\r\n                (result, currAlt, idx) => {\r\n                    forEach(currAlt, (currTokType) => {\r\n                        if (currTokType) {\r\n                            result[currTokType.tokenTypeIdx!] = idx\r\n                            forEach(currTokType.categoryMatches!, (currExtendingType) => {\r\n                                result[currExtendingType] = idx\r\n                            })\r\n                        }\r\n                    })\r\n                    return result\r\n                },\r\n                {} as Record<number, number>\r\n            )\r\n\r\n            if (hasPredicates) {\r\n                return function (this: BaseParser, orAlts) {\r\n                    const nextToken = this.LA(1)\r\n                    const prediction: number | undefined = choiceToAlt[nextToken.tokenTypeIdx]\r\n                    if (orAlts !== undefined && prediction !== undefined) {\r\n                        const gate = orAlts[prediction]?.GATE\r\n                        if (gate !== undefined && gate.call(this) === false) {\r\n                            return undefined;\r\n                        }\r\n                    }\r\n                    return prediction\r\n                }\r\n            } else {\r\n                return function (this: BaseParser): number | undefined {\r\n                    const nextToken = this.LA(1)\r\n                    return choiceToAlt[nextToken.tokenTypeIdx];\r\n                }\r\n            }\r\n        } else if (hasPredicates) {\r\n            return function (this: BaseParser, orAlts) {\r\n                const predicates = new PredicateSet()\r\n                const length = orAlts === undefined ? 0 : orAlts.length\r\n                for (let i = 0; i < length; i++) {\r\n                    const gate = orAlts?.[i].GATE\r\n                    predicates.set(i, gate === undefined || gate.call(this))\r\n                }\r\n                const result = adaptivePredict.call(this, dfas, decisionIndex, predicates, logging);\r\n                return typeof result === 'number' ? result : undefined;\r\n            }\r\n        } else {\r\n            return function (this: BaseParser) {\r\n                const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\r\n                return typeof result === 'number' ? result : undefined;\r\n            }\r\n        }\r\n    }\r\n\r\n    override buildLookaheadForOptional(options: {\r\n        prodOccurrence: number;\r\n        prodType: OptionalProductionType;\r\n        rule: Rule;\r\n        maxLookahead: number;\r\n        dynamicTokensEnabled: boolean\r\n    }): (this: BaseParser) => boolean {\r\n        const { prodOccurrence, rule, prodType, dynamicTokensEnabled } = options;\r\n        const dfas = this.dfas;\r\n        const logging = this.logging;\r\n        const key = buildATNKey(rule, prodType, prodOccurrence);\r\n        const decisionState = this.atn.decisionMap[key];\r\n        const decisionIndex = decisionState.decision;\r\n        const alts = map(\r\n            getLookaheadPaths({\r\n                maxLookahead: 1,\r\n                occurrence: prodOccurrence,\r\n                prodType,\r\n                rule\r\n            }),\r\n            (e) => {\r\n              return map(e, (g) => g[0])\r\n            }\r\n          )\r\n        \r\n          if (isLL1Sequence(alts) && alts[0][0] && !dynamicTokensEnabled) {\r\n            const alt = alts[0]\r\n            const singleTokensTypes = flatten(alt)\r\n        \r\n            if (\r\n              singleTokensTypes.length === 1 &&\r\n              isEmpty(singleTokensTypes[0].categoryMatches)\r\n            ) {\r\n              const expectedTokenType = singleTokensTypes[0]\r\n              const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx\r\n        \r\n              return function (this: BaseParser): boolean {\r\n                return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey\r\n              }\r\n            } else {\r\n              const choiceToAlt = reduce(\r\n                singleTokensTypes,\r\n                (result, currTokType) => {\r\n                  if (currTokType !== undefined) {\r\n                    result[currTokType.tokenTypeIdx!] = true\r\n                    forEach(currTokType.categoryMatches, (currExtendingType) => {\r\n                      result[currExtendingType] = true\r\n                    })\r\n                  }\r\n                  return result\r\n                },\r\n                {} as Record<number, boolean>\r\n              )\r\n        \r\n              return function (this: BaseParser): boolean {\r\n                const nextToken = this.LA(1)\r\n                return choiceToAlt[nextToken.tokenTypeIdx] === true\r\n              }\r\n            }\r\n          }\r\n          return function (this: BaseParser) {\r\n            const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging)\r\n              return typeof result === \"object\" ? false : result === 0;\r\n          }\r\n    }\r\n\r\n}\r\n\r\nfunction isLL1Sequence(sequences: (TokenType | undefined)[][], allowEmpty = true): boolean {\r\n    const fullSet = new Set<number>()\r\n\r\n    for (const alt of sequences) {\r\n        const altSet = new Set<number>()\r\n        for (const tokType of alt) {\r\n            if (tokType === undefined) {\r\n                if (allowEmpty) {\r\n                    // Epsilon production encountered\r\n                    break\r\n                } else {\r\n                    return false;\r\n                }\r\n            }\r\n            const indices = [tokType.tokenTypeIdx!].concat(tokType.categoryMatches!)\r\n            for (const index of indices) {\r\n                if (fullSet.has(index)) {\r\n                    if (!altSet.has(index)) {\r\n                        return false\r\n                    }\r\n                } else {\r\n                    fullSet.add(index)\r\n                    altSet.add(index)\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return true\r\n}\r\n\r\nfunction initATNSimulator(atn: ATN): DFACache[] {\r\n    const decisionLength = atn.decisionStates.length\r\n    const decisionToDFA: DFACache[] = Array(decisionLength)\r\n    for (let i = 0; i < decisionLength; i++) {\r\n        decisionToDFA[i] = createDFACache(atn.decisionStates[i], i)\r\n    }\r\n    return decisionToDFA;\r\n}\r\n\r\nfunction adaptivePredict(\r\n    this: BaseParser,\r\n    dfaCaches: DFACache[],\r\n    decision: number,\r\n    predicateSet: PredicateSet,\r\n    logging: AmbiguityReport\r\n): number | AdaptivePredictError {\r\n    const dfa = dfaCaches[decision](predicateSet)\r\n    let start = dfa.start\r\n    if (start === undefined) {\r\n        const closure = computeStartState(dfa.atnStartState as ATNState)\r\n        start = addDFAState(dfa, newDFAState(closure))\r\n        dfa.start = start\r\n    }\r\n\r\n    const alt = performLookahead.apply(this, [dfa, start, predicateSet, logging])\r\n    return alt\r\n}\r\n\r\nfunction performLookahead(\r\n    this: BaseParser,\r\n    dfa: DFA,\r\n    s0: DFAState,\r\n    predicateSet: PredicateSet,\r\n    logging: AmbiguityReport\r\n): number | AdaptivePredictError {\r\n    let previousD = s0\r\n\r\n    let i = 1\r\n    const path: IToken[] = []\r\n    let t = this.LA(i++)\r\n\r\n    while (true) {\r\n        let d = getExistingTargetState(previousD, t)\r\n        if (d === undefined) {\r\n            d = computeLookaheadTarget.apply(this, [dfa, previousD, t, i, predicateSet, logging])\r\n        }\r\n\r\n        if (d === DFA_ERROR) {\r\n            return buildAdaptivePredictError(path, previousD, t)\r\n        }\r\n\r\n        if (d.isAcceptState === true) {\r\n            return d.prediction\r\n        }\r\n\r\n        previousD = d\r\n        path.push(t)\r\n        t = this.LA(i++)\r\n    }\r\n}\r\n\r\nfunction computeLookaheadTarget(\r\n    this: BaseParser,\r\n    dfa: DFA,\r\n    previousD: DFAState,\r\n    token: IToken,\r\n    lookahead: number,\r\n    predicateSet: PredicateSet,\r\n    logging: AmbiguityReport\r\n): DFAState {\r\n    const reach = computeReachSet(previousD.configs, token, predicateSet)\r\n    if (reach.size === 0) {\r\n        addDFAEdge(dfa, previousD, token, DFA_ERROR)\r\n        return DFA_ERROR\r\n    }\r\n\r\n    let newState = newDFAState(reach)\r\n    const predictedAlt = getUniqueAlt(reach, predicateSet)\r\n\r\n    if (predictedAlt !== undefined) {\r\n        newState.isAcceptState = true\r\n        newState.prediction = predictedAlt\r\n        newState.configs.uniqueAlt = predictedAlt\r\n    } else if (hasConflictTerminatingPrediction(reach)) {\r\n        const prediction = min(reach.alts)!\r\n        newState.isAcceptState = true\r\n        newState.prediction = prediction\r\n        newState.configs.uniqueAlt = prediction\r\n        reportLookaheadAmbiguity.apply(this, [dfa, lookahead, reach.alts, logging])\r\n    }\r\n\r\n    newState = addDFAEdge(dfa, previousD, token, newState)\r\n    return newState\r\n}\r\n\r\nfunction reportLookaheadAmbiguity(\r\n    this: BaseParser,\r\n    dfa: DFA,\r\n    lookahead: number,\r\n    ambiguityIndices: number[],\r\n    logging: AmbiguityReport\r\n) {\r\n    const prefixPath: TokenType[] = []\r\n    for (let i = 1; i <= lookahead; i++) {\r\n        prefixPath.push(this.LA(i).tokenType)\r\n    }\r\n    const atnState = dfa.atnStartState\r\n    const topLevelRule = atnState.rule\r\n    const production = atnState.production\r\n    const message = buildAmbiguityError({\r\n        topLevelRule,\r\n        ambiguityIndices,\r\n        production,\r\n        prefixPath\r\n    })\r\n    logging(message)\r\n}\r\n\r\nfunction buildAmbiguityError(options: {\r\n    topLevelRule: Rule\r\n    prefixPath: TokenType[]\r\n    ambiguityIndices: number[]\r\n    production: IProductionWithOccurrence\r\n}): string {\r\n    const pathMsg = map(options.prefixPath, (currtok) =>\r\n        tokenLabel(currtok)\r\n    ).join(\", \")\r\n    const occurrence =\r\n        options.production.idx === 0 ? \"\" : options.production.idx\r\n    let currMessage =\r\n        `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\r\n            \", \"\r\n        )}> in <${getProductionDslName(options.production)}${occurrence}>` +\r\n        ` inside <${options.topLevelRule.name}> Rule,\\n` +\r\n        `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`\r\n\r\n    currMessage =\r\n        currMessage +\r\n        `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` +\r\n        `For Further details.`\r\n    return currMessage\r\n}\r\n\r\nfunction getProductionDslName(prod: IProductionWithOccurrence): string {\r\n    if (prod instanceof NonTerminal) {\r\n        return \"SUBRULE\"\r\n    } else if (prod instanceof Option) {\r\n        return \"OPTION\"\r\n    } else if (prod instanceof Alternation) {\r\n        return \"OR\"\r\n    } else if (prod instanceof RepetitionMandatory) {\r\n        return \"AT_LEAST_ONE\"\r\n    } else if (prod instanceof RepetitionMandatoryWithSeparator) {\r\n        return \"AT_LEAST_ONE_SEP\"\r\n    } else if (prod instanceof RepetitionWithSeparator) {\r\n        return \"MANY_SEP\"\r\n    } else if (prod instanceof Repetition) {\r\n        return \"MANY\"\r\n    } else if (prod instanceof Terminal) {\r\n        return \"CONSUME\"\r\n    } else {\r\n        throw Error(\"non exhaustive match\")\r\n    }\r\n}\r\n\r\nfunction buildAdaptivePredictError(\r\n    path: IToken[],\r\n    previous: DFAState,\r\n    current: IToken\r\n): AdaptivePredictError {\r\n    const nextTransitions = flatMap(\r\n        previous.configs.elements,\r\n        (e) => e.state.transitions\r\n    )\r\n    const nextTokenTypes = uniqBy(\r\n        nextTransitions\r\n            .filter((e): e is AtomTransition => e instanceof AtomTransition)\r\n            .map((e) => e.tokenType),\r\n        (e) => e.tokenTypeIdx\r\n    )\r\n    return {\r\n        actualToken: current,\r\n        possibleTokenTypes: nextTokenTypes,\r\n        tokenPath: path\r\n    }\r\n}\r\n\r\nfunction getExistingTargetState(\r\n    state: DFAState,\r\n    token: IToken\r\n): DFAState | undefined {\r\n    return state.edges[token.tokenTypeIdx]\r\n}\r\n\r\nfunction computeReachSet(\r\n    configs: ATNConfigSet,\r\n    token: IToken,\r\n    predicateSet: PredicateSet\r\n): ATNConfigSet {\r\n    const intermediate = new ATNConfigSet()\r\n    const skippedStopStates: ATNConfig[] = []\r\n\r\n    for (const c of configs.elements) {\r\n        if (predicateSet.is(c.alt) === false) {\r\n            continue\r\n        }\r\n        if (c.state.type === ATN_RULE_STOP) {\r\n            skippedStopStates.push(c)\r\n            continue\r\n        }\r\n        const transitionLength = c.state.transitions.length\r\n        for (let i = 0; i < transitionLength; i++) {\r\n            const transition = c.state.transitions[i]\r\n            const target = getReachableTarget(transition, token)\r\n            if (target !== undefined) {\r\n                intermediate.add({\r\n                    state: target,\r\n                    alt: c.alt,\r\n                    stack: c.stack\r\n                })\r\n            }\r\n        }\r\n    }\r\n\r\n    let reach: ATNConfigSet | undefined\r\n\r\n    if (skippedStopStates.length === 0 && intermediate.size === 1) {\r\n        reach = intermediate\r\n    }\r\n\r\n    if (reach === undefined) {\r\n        reach = new ATNConfigSet()\r\n        for (const c of intermediate.elements) {\r\n            closure(c, reach)\r\n        }\r\n    }\r\n\r\n    if (skippedStopStates.length > 0 && !hasConfigInRuleStopState(reach)) {\r\n        for (const c of skippedStopStates) {\r\n            reach.add(c)\r\n        }\r\n    }\r\n\r\n    return reach\r\n}\r\n\r\nfunction getReachableTarget(\r\n    transition: Transition,\r\n    token: IToken\r\n): ATNState | undefined {\r\n    if (\r\n        transition instanceof AtomTransition &&\r\n        tokenMatcher(token, transition.tokenType)\r\n    ) {\r\n        return transition.target\r\n    }\r\n    return undefined\r\n}\r\n\r\nfunction getUniqueAlt(\r\n    configs: ATNConfigSet,\r\n    predicateSet: PredicateSet\r\n): number | undefined {\r\n    let alt: number | undefined\r\n    for (const c of configs.elements) {\r\n        if (predicateSet.is(c.alt) === true) {\r\n            if (alt === undefined) {\r\n                alt = c.alt\r\n            } else if (alt !== c.alt) {\r\n                return undefined\r\n            }\r\n        }\r\n    }\r\n    return alt\r\n}\r\n\r\nfunction newDFAState(closure: ATNConfigSet): DFAState {\r\n    return {\r\n        configs: closure,\r\n        edges: {},\r\n        isAcceptState: false,\r\n        prediction: -1\r\n    }\r\n}\r\n\r\nfunction addDFAEdge(\r\n    dfa: DFA,\r\n    from: DFAState,\r\n    token: IToken,\r\n    to: DFAState\r\n): DFAState {\r\n    to = addDFAState(dfa, to)\r\n    from.edges[token.tokenTypeIdx] = to\r\n    return to\r\n}\r\n\r\nfunction addDFAState(dfa: DFA, state: DFAState): DFAState {\r\n    if (state === DFA_ERROR) {\r\n        return state\r\n    }\r\n    // Repetitions have the same config set\r\n    // Therefore, storing the key of the config in a map allows us to create a loop in our DFA\r\n    const mapKey = state.configs.key\r\n    const existing = dfa.states[mapKey]\r\n    if (existing !== undefined) {\r\n        return existing\r\n    }\r\n    state.configs.finalize()\r\n    dfa.states[mapKey] = state\r\n    return state\r\n}\r\n\r\nfunction computeStartState(atnState: ATNState): ATNConfigSet {\r\n    const configs = new ATNConfigSet()\r\n\r\n    const numberOfTransitions = atnState.transitions.length\r\n    for (let i = 0; i < numberOfTransitions; i++) {\r\n        const target = atnState.transitions[i].target\r\n        const config: ATNConfig = {\r\n            state: target,\r\n            alt: i,\r\n            stack: []\r\n        }\r\n        closure(config, configs)\r\n    }\r\n\r\n    return configs\r\n}\r\n\r\nfunction closure(config: ATNConfig, configs: ATNConfigSet): void {\r\n    const p = config.state\r\n\r\n    if (p.type === ATN_RULE_STOP) {\r\n        if (config.stack.length > 0) {\r\n            const atnStack = [...config.stack]\r\n            const followState = atnStack.pop()!\r\n            const followConfig: ATNConfig = {\r\n                state: followState,\r\n                alt: config.alt,\r\n                stack: atnStack\r\n            }\r\n            closure(followConfig, configs)\r\n        } else {\r\n            // Dipping into outer context, simply add the config\r\n            // This will stop computation once every config is at the rule stop state\r\n            configs.add(config)\r\n        }\r\n        return\r\n    }\r\n\r\n    if (!p.epsilonOnlyTransitions) {\r\n        configs.add(config)\r\n    }\r\n\r\n    const transitionLength = p.transitions.length\r\n    for (let i = 0; i < transitionLength; i++) {\r\n        const transition = p.transitions[i]\r\n        const c = getEpsilonTarget(config, transition)\r\n\r\n        if (c !== undefined) {\r\n            closure(c, configs)\r\n        }\r\n    }\r\n}\r\n\r\nfunction getEpsilonTarget(\r\n    config: ATNConfig,\r\n    transition: Transition\r\n): ATNConfig | undefined {\r\n    if (transition instanceof EpsilonTransition) {\r\n        return {\r\n            state: transition.target,\r\n            alt: config.alt,\r\n            stack: config.stack\r\n        }\r\n    } else if (transition instanceof RuleTransition) {\r\n        const stack = [...config.stack, transition.followState]\r\n        return {\r\n            state: transition.target,\r\n            alt: config.alt,\r\n            stack\r\n        }\r\n    }\r\n    return undefined\r\n}\r\n\r\nfunction hasConfigInRuleStopState(configs: ATNConfigSet): boolean {\r\n    for (const c of configs.elements) {\r\n        if (c.state.type === ATN_RULE_STOP) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\nfunction allConfigsInRuleStopStates(configs: ATNConfigSet): boolean {\r\n    for (const c of configs.elements) {\r\n        if (c.state.type !== ATN_RULE_STOP) {\r\n            return false\r\n        }\r\n    }\r\n    return true\r\n}\r\n\r\nfunction hasConflictTerminatingPrediction(configs: ATNConfigSet): boolean {\r\n    if (allConfigsInRuleStopStates(configs)) {\r\n        return true\r\n    }\r\n    const altSets = getConflictingAltSets(configs.elements)\r\n    const heuristic =\r\n        hasConflictingAltSet(altSets) && !hasStateAssociatedWithOneAlt(altSets)\r\n    return heuristic\r\n}\r\n\r\nfunction getConflictingAltSets(\r\n    configs: readonly ATNConfig[]\r\n): Map<string, Record<number, boolean>> {\r\n    const configToAlts = new Map<string, Record<number, boolean>>()\r\n    for (const c of configs) {\r\n        const key = getATNConfigKey(c, false)\r\n        let alts = configToAlts.get(key)\r\n        if (alts === undefined) {\r\n            alts = {}\r\n            configToAlts.set(key, alts)\r\n        }\r\n        alts[c.alt] = true\r\n    }\r\n    return configToAlts\r\n}\r\n\r\nfunction hasConflictingAltSet(\r\n    altSets: Map<string, Record<number, boolean>>\r\n): boolean {\r\n    for (const value of Array.from(altSets.values())) {\r\n        if (Object.keys(value).length > 1) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\nfunction hasStateAssociatedWithOneAlt(\r\n    altSets: Map<string, Record<number, boolean>>\r\n): boolean {\r\n    for (const value of Array.from(altSets.values())) {\r\n        if (Object.keys(value).length === 1) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAGIA,YAAY,EACZC,UAAU,EAGVC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,UAAU,EACVC,QAAQ,EAERC,oBAAoB,EAGpBC,iBAAiB,QAEd,YAAY;AACnB,SAGIC,aAAa,EACbC,cAAc,EACdC,WAAW,EACXC,SAAS,EAETC,iBAAiB,EACjBC,cAAc,QAEX,UAAU;AACjB,SAEIC,YAAY,EAGZC,SAAS,EACTC,eAAe,QACZ,UAAU;AACjB,OAAOC,GAAG,MAAM,kBAAkB;AAClC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,kBAAkB;AAClC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AAMxC,SAASC,cAAcA,CAACC,UAAyB,EAAEC,QAAgB;EAC/D,MAAMP,GAAG,GAAoC,EAAE;EAC/C,OAAQQ,YAAY,IAAI;IACpB,MAAMC,GAAG,GAAGD,YAAY,CAACE,QAAQ,EAAE;IACnC,IAAIC,QAAQ,GAAGX,GAAG,CAACS,GAAG,CAAC;IACvB,IAAIE,QAAQ,KAAKC,SAAS,EAAE;MACxB,OAAOD,QAAQ;KAClB,MAAM;MACHA,QAAQ,GAAG;QACPE,aAAa,EAAEP,UAAU;QACzBC,QAAQ;QACRO,MAAM,EAAE;OACX;MACDd,GAAG,CAACS,GAAG,CAAC,GAAGE,QAAQ;MACnB,OAAOA,QAAQ;;EAEvB,CAAC;AACL;AAEA,MAAMI,YAAY;EAAlBC,YAAA;IACY,KAAAC,UAAU,GAAc,EAAE;EAkBtC;EAhBIC,EAAEA,CAACC,KAAa;IACZ,OAAOA,KAAK,IAAI,IAAI,CAACF,UAAU,CAACG,MAAM,IAAI,IAAI,CAACH,UAAU,CAACE,KAAK,CAAC;EACpE;EAEAE,GAAGA,CAACF,KAAa,EAAEG,KAAc;IAC7B,IAAI,CAACL,UAAU,CAACE,KAAK,CAAC,GAAGG,KAAK;EAClC;EAEAZ,QAAQA,CAAA;IACJ,IAAIY,KAAK,GAAG,EAAE;IACd,MAAMC,IAAI,GAAG,IAAI,CAACN,UAAU,CAACG,MAAM;IACnC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,EAAE,EAAE;MAC3BF,KAAK,IAAI,IAAI,CAACL,UAAU,CAACO,CAAC,CAAC,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;;IAEpD,OAAOF,KAAK;EAChB;;AASJ,MAAMG,gBAAgB,GAAG,IAAIV,YAAY,EAAE;AAM3C,OAAM,MAAOW,uBAAwB,SAAQxC,oBAAoB;EAM7D8B,YAAYW,OAAgC;;IACxC,KAAK,EAAE;IACP,IAAI,CAACC,OAAO,GAAG,CAAAC,EAAA,GAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,OAAO,cAAAC,EAAA,cAAAA,EAAA,GAAMC,OAAO,IAAKC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAE;EAC1E;EAESG,UAAUA,CAACN,OAA0B;IAC1C,IAAI,CAACO,GAAG,GAAG3C,SAAS,CAACoC,OAAO,CAACQ,KAAK,CAAC;IACnC,IAAI,CAACC,IAAI,GAAGC,gBAAgB,CAAC,IAAI,CAACH,GAAG,CAAC;EAC1C;EAESI,wCAAwCA,CAAA;IAC7C,OAAO,EAAE;EACb;EAESC,2BAA2BA,CAAA;IAChC,OAAO,EAAE;EACb;EAESC,4BAA4BA,CAACb,OAMrC;IACG,MAAM;MAAEc,cAAc;MAAEC,IAAI;MAAEC,aAAa;MAAEC;IAAoB,CAAE,GAAGjB,OAAO;IAC7E,MAAMS,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMR,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMnB,GAAG,GAAGnB,WAAW,CAACoD,IAAI,EAAE,aAAa,EAAED,cAAc,CAAC;IAC5D,MAAMI,aAAa,GAAG,IAAI,CAACX,GAAG,CAACY,WAAW,CAACrC,GAAG,CAAC;IAC/C,MAAMsC,aAAa,GAAGF,aAAa,CAACtC,QAAQ;IAC5C,MAAMyC,WAAW,GAAgChD,GAAG,CAChDb,iBAAiB,CAAC;MACd8D,YAAY,EAAE,CAAC;MACfC,UAAU,EAAET,cAAc;MAC1BU,QAAQ,EAAE,aAAa;MACvBT,IAAI,EAAEA;KACT,CAAC,EACDU,OAAO,IAAKpD,GAAG,CAACoD,OAAO,EAAGC,IAAI,IAAKA,IAAI,CAAC,CAAC,CAAC,CAAC,CAC/C;IAED,IAAIC,aAAa,CAACN,WAAW,EAAE,KAAK,CAAC,IAAI,CAACJ,oBAAoB,EAAE;MAC5D,MAAMW,WAAW,GAAGnD,MAAM,CACtB4C,WAAW,EACX,CAACQ,MAAM,EAAEJ,OAAO,EAAEK,GAAG,KAAI;QACrBvD,OAAO,CAACkD,OAAO,EAAGM,WAAW,IAAI;UAC7B,IAAIA,WAAW,EAAE;YACbF,MAAM,CAACE,WAAW,CAACC,YAAa,CAAC,GAAGF,GAAG;YACvCvD,OAAO,CAACwD,WAAW,CAACE,eAAgB,EAAGC,iBAAiB,IAAI;cACxDL,MAAM,CAACK,iBAAiB,CAAC,GAAGJ,GAAG;YACnC,CAAC,CAAC;;QAEV,CAAC,CAAC;QACF,OAAOD,MAAM;MACjB,CAAC,EACD,EAA4B,CAC/B;MAED,IAAIb,aAAa,EAAE;QACf,OAAO,UAA4BmB,MAAM;;UACrC,MAAMC,SAAS,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;UAC5B,MAAMC,UAAU,GAAuBV,WAAW,CAACQ,SAAS,CAACJ,YAAY,CAAC;UAC1E,IAAIG,MAAM,KAAKlD,SAAS,IAAIqD,UAAU,KAAKrD,SAAS,EAAE;YAClD,MAAMsD,IAAI,GAAG,CAAArC,EAAA,GAAAiC,MAAM,CAACG,UAAU,CAAC,cAAApC,EAAA,uBAAAA,EAAA,CAAEsC,IAAI;YACrC,IAAID,IAAI,KAAKtD,SAAS,IAAIsD,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;cACjD,OAAOxD,SAAS;;;UAGxB,OAAOqD,UAAU;QACrB,CAAC;OACJ,MAAM;QACH,OAAO;UACH,MAAMF,SAAS,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;UAC5B,OAAOT,WAAW,CAACQ,SAAS,CAACJ,YAAY,CAAC;QAC9C,CAAC;;KAER,MAAM,IAAIhB,aAAa,EAAE;MACtB,OAAO,UAA4BmB,MAAM;QACrC,MAAM7C,UAAU,GAAG,IAAIF,YAAY,EAAE;QACrC,MAAMK,MAAM,GAAG0C,MAAM,KAAKlD,SAAS,GAAG,CAAC,GAAGkD,MAAM,CAAC1C,MAAM;QACvD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;UAC7B,MAAM0C,IAAI,GAAGJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGtC,CAAC,EAAE2C,IAAI;UAC7BlD,UAAU,CAACI,GAAG,CAACG,CAAC,EAAE0C,IAAI,KAAKtD,SAAS,IAAIsD,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;;QAE5D,MAAMZ,MAAM,GAAGa,eAAe,CAACD,IAAI,CAAC,IAAI,EAAEhC,IAAI,EAAEW,aAAa,EAAE9B,UAAU,EAAEW,OAAO,CAAC;QACnF,OAAO,OAAO4B,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG5C,SAAS;MAC1D,CAAC;KACJ,MAAM;MACH,OAAO;QACH,MAAM4C,MAAM,GAAGa,eAAe,CAACD,IAAI,CAAC,IAAI,EAAEhC,IAAI,EAAEW,aAAa,EAAEtB,gBAAgB,EAAEG,OAAO,CAAC;QACzF,OAAO,OAAO4B,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG5C,SAAS;MAC1D,CAAC;;EAET;EAES0D,yBAAyBA,CAAC3C,OAMlC;IACG,MAAM;MAAEc,cAAc;MAAEC,IAAI;MAAES,QAAQ;MAAEP;IAAoB,CAAE,GAAGjB,OAAO;IACxE,MAAMS,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMR,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMnB,GAAG,GAAGnB,WAAW,CAACoD,IAAI,EAAES,QAAQ,EAAEV,cAAc,CAAC;IACvD,MAAMI,aAAa,GAAG,IAAI,CAACX,GAAG,CAACY,WAAW,CAACrC,GAAG,CAAC;IAC/C,MAAMsC,aAAa,GAAGF,aAAa,CAACtC,QAAQ;IAC5C,MAAMgE,IAAI,GAAGvE,GAAG,CACZb,iBAAiB,CAAC;MACd8D,YAAY,EAAE,CAAC;MACfC,UAAU,EAAET,cAAc;MAC1BU,QAAQ;MACRT;KACH,CAAC,EACD8B,CAAC,IAAI;MACJ,OAAOxE,GAAG,CAACwE,CAAC,EAAGC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CACF;IAED,IAAInB,aAAa,CAACiB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC3B,oBAAoB,EAAE;MAC9D,MAAM8B,GAAG,GAAGH,IAAI,CAAC,CAAC,CAAC;MACnB,MAAMI,iBAAiB,GAAG1E,OAAO,CAACyE,GAAG,CAAC;MAEtC,IACEC,iBAAiB,CAACvD,MAAM,KAAK,CAAC,IAC9BjB,OAAO,CAACwE,iBAAiB,CAAC,CAAC,CAAC,CAACf,eAAe,CAAC,EAC7C;QACA,MAAMgB,iBAAiB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;QAC9C,MAAME,sBAAsB,GAAGD,iBAAiB,CAACjB,YAAY;QAE7D,OAAO;UACL,OAAO,IAAI,CAACK,EAAE,CAAC,CAAC,CAAC,CAACL,YAAY,KAAKkB,sBAAsB;QAC3D,CAAC;OACF,MAAM;QACL,MAAMtB,WAAW,GAAGnD,MAAM,CACxBuE,iBAAiB,EACjB,CAACnB,MAAM,EAAEE,WAAW,KAAI;UACtB,IAAIA,WAAW,KAAK9C,SAAS,EAAE;YAC7B4C,MAAM,CAACE,WAAW,CAACC,YAAa,CAAC,GAAG,IAAI;YACxCzD,OAAO,CAACwD,WAAW,CAACE,eAAe,EAAGC,iBAAiB,IAAI;cACzDL,MAAM,CAACK,iBAAiB,CAAC,GAAG,IAAI;YAClC,CAAC,CAAC;;UAEJ,OAAOL,MAAM;QACf,CAAC,EACD,EAA6B,CAC9B;QAED,OAAO;UACL,MAAMO,SAAS,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;UAC5B,OAAOT,WAAW,CAACQ,SAAS,CAACJ,YAAY,CAAC,KAAK,IAAI;QACrD,CAAC;;;IAGL,OAAO;MACL,MAAMH,MAAM,GAAGa,eAAe,CAACD,IAAI,CAAC,IAAI,EAAEhC,IAAI,EAAEW,aAAa,EAAEtB,gBAAgB,EAAEG,OAAO,CAAC;MACvF,OAAO,OAAO4B,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAGA,MAAM,KAAK,CAAC;IAC5D,CAAC;EACP;;AAIJ,SAASF,aAAaA,CAACwB,SAAsC,EAAEC,UAAU,GAAG,IAAI;EAC5E,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAU;EAEjC,KAAK,MAAMP,GAAG,IAAII,SAAS,EAAE;IACzB,MAAMI,MAAM,GAAG,IAAID,GAAG,EAAU;IAChC,KAAK,MAAME,OAAO,IAAIT,GAAG,EAAE;MACvB,IAAIS,OAAO,KAAKvE,SAAS,EAAE;QACvB,IAAImE,UAAU,EAAE;UACZ;UACA;SACH,MAAM;UACH,OAAO,KAAK;;;MAGpB,MAAMK,OAAO,GAAG,CAACD,OAAO,CAACxB,YAAa,CAAC,CAAC0B,MAAM,CAACF,OAAO,CAACvB,eAAgB,CAAC;MACxE,KAAK,MAAMzC,KAAK,IAAIiE,OAAO,EAAE;QACzB,IAAIJ,OAAO,CAACM,GAAG,CAACnE,KAAK,CAAC,EAAE;UACpB,IAAI,CAAC+D,MAAM,CAACI,GAAG,CAACnE,KAAK,CAAC,EAAE;YACpB,OAAO,KAAK;;SAEnB,MAAM;UACH6D,OAAO,CAACO,GAAG,CAACpE,KAAK,CAAC;UAClB+D,MAAM,CAACK,GAAG,CAACpE,KAAK,CAAC;;;;;EAKjC,OAAO,IAAI;AACf;AAEA,SAASkB,gBAAgBA,CAACH,GAAQ;EAC9B,MAAMsD,cAAc,GAAGtD,GAAG,CAACuD,cAAc,CAACrE,MAAM;EAChD,MAAMsE,aAAa,GAAeC,KAAK,CAACH,cAAc,CAAC;EACvD,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,cAAc,EAAEhE,CAAC,EAAE,EAAE;IACrCkE,aAAa,CAAClE,CAAC,CAAC,GAAGnB,cAAc,CAAC6B,GAAG,CAACuD,cAAc,CAACjE,CAAC,CAAC,EAAEA,CAAC,CAAC;;EAE/D,OAAOkE,aAAa;AACxB;AAEA,SAASrB,eAAeA,CAEpBuB,SAAqB,EACrBrF,QAAgB,EAChBC,YAA0B,EAC1BoB,OAAwB;EAExB,MAAMiE,GAAG,GAAGD,SAAS,CAACrF,QAAQ,CAAC,CAACC,YAAY,CAAC;EAC7C,IAAIsF,KAAK,GAAGD,GAAG,CAACC,KAAK;EACrB,IAAIA,KAAK,KAAKlF,SAAS,EAAE;IACrB,MAAMmF,OAAO,GAAGC,iBAAiB,CAACH,GAAG,CAAChF,aAAyB,CAAC;IAChEiF,KAAK,GAAGG,WAAW,CAACJ,GAAG,EAAEK,WAAW,CAACH,OAAO,CAAC,CAAC;IAC9CF,GAAG,CAACC,KAAK,GAAGA,KAAK;;EAGrB,MAAMpB,GAAG,GAAGyB,gBAAgB,CAACC,KAAK,CAAC,IAAI,EAAE,CAACP,GAAG,EAAEC,KAAK,EAAEtF,YAAY,EAAEoB,OAAO,CAAC,CAAC;EAC7E,OAAO8C,GAAG;AACd;AAEA,SAASyB,gBAAgBA,CAErBN,GAAQ,EACRQ,EAAY,EACZ7F,YAA0B,EAC1BoB,OAAwB;EAExB,IAAI0E,SAAS,GAAGD,EAAE;EAElB,IAAI7E,CAAC,GAAG,CAAC;EACT,MAAM6B,IAAI,GAAa,EAAE;EACzB,IAAIkD,CAAC,GAAG,IAAI,CAACvC,EAAE,CAACxC,CAAC,EAAE,CAAC;EAEpB,OAAO,IAAI,EAAE;IACT,IAAIgF,CAAC,GAAGC,sBAAsB,CAACH,SAAS,EAAEC,CAAC,CAAC;IAC5C,IAAIC,CAAC,KAAK5F,SAAS,EAAE;MACjB4F,CAAC,GAAGE,sBAAsB,CAACN,KAAK,CAAC,IAAI,EAAE,CAACP,GAAG,EAAES,SAAS,EAAEC,CAAC,EAAE/E,CAAC,EAAEhB,YAAY,EAAEoB,OAAO,CAAC,CAAC;;IAGzF,IAAI4E,CAAC,KAAK7G,SAAS,EAAE;MACjB,OAAOgH,yBAAyB,CAACtD,IAAI,EAAEiD,SAAS,EAAEC,CAAC,CAAC;;IAGxD,IAAIC,CAAC,CAACI,aAAa,KAAK,IAAI,EAAE;MAC1B,OAAOJ,CAAC,CAACvC,UAAU;;IAGvBqC,SAAS,GAAGE,CAAC;IACbnD,IAAI,CAACwD,IAAI,CAACN,CAAC,CAAC;IACZA,CAAC,GAAG,IAAI,CAACvC,EAAE,CAACxC,CAAC,EAAE,CAAC;;AAExB;AAEA,SAASkF,sBAAsBA,CAE3Bb,GAAQ,EACRS,SAAmB,EACnBQ,KAAa,EACbC,SAAiB,EACjBvG,YAA0B,EAC1BoB,OAAwB;EAExB,MAAMoF,KAAK,GAAGC,eAAe,CAACX,SAAS,CAACY,OAAO,EAAEJ,KAAK,EAAEtG,YAAY,CAAC;EACrE,IAAIwG,KAAK,CAACzF,IAAI,KAAK,CAAC,EAAE;IAClB4F,UAAU,CAACtB,GAAG,EAAES,SAAS,EAAEQ,KAAK,EAAEnH,SAAS,CAAC;IAC5C,OAAOA,SAAS;;EAGpB,IAAIyH,QAAQ,GAAGlB,WAAW,CAACc,KAAK,CAAC;EACjC,MAAMK,YAAY,GAAGC,YAAY,CAACN,KAAK,EAAExG,YAAY,CAAC;EAEtD,IAAI6G,YAAY,KAAKzG,SAAS,EAAE;IAC5BwG,QAAQ,CAACR,aAAa,GAAG,IAAI;IAC7BQ,QAAQ,CAACnD,UAAU,GAAGoD,YAAY;IAClCD,QAAQ,CAACF,OAAO,CAACK,SAAS,GAAGF,YAAY;GAC5C,MAAM,IAAIG,gCAAgC,CAACR,KAAK,CAAC,EAAE;IAChD,MAAM/C,UAAU,GAAGpE,GAAG,CAACmH,KAAK,CAACzC,IAAI,CAAE;IACnC6C,QAAQ,CAACR,aAAa,GAAG,IAAI;IAC7BQ,QAAQ,CAACnD,UAAU,GAAGA,UAAU;IAChCmD,QAAQ,CAACF,OAAO,CAACK,SAAS,GAAGtD,UAAU;IACvCwD,wBAAwB,CAACrB,KAAK,CAAC,IAAI,EAAE,CAACP,GAAG,EAAEkB,SAAS,EAAEC,KAAK,CAACzC,IAAI,EAAE3C,OAAO,CAAC,CAAC;;EAG/EwF,QAAQ,GAAGD,UAAU,CAACtB,GAAG,EAAES,SAAS,EAAEQ,KAAK,EAAEM,QAAQ,CAAC;EACtD,OAAOA,QAAQ;AACnB;AAEA,SAASK,wBAAwBA,CAE7B5B,GAAQ,EACRkB,SAAiB,EACjBW,gBAA0B,EAC1B9F,OAAwB;EAExB,MAAM+F,UAAU,GAAgB,EAAE;EAClC,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIuF,SAAS,EAAEvF,CAAC,EAAE,EAAE;IACjCmG,UAAU,CAACd,IAAI,CAAC,IAAI,CAAC7C,EAAE,CAACxC,CAAC,CAAC,CAACoG,SAAS,CAAC;;EAEzC,MAAMC,QAAQ,GAAGhC,GAAG,CAAChF,aAAa;EAClC,MAAMiH,YAAY,GAAGD,QAAQ,CAACnF,IAAI;EAClC,MAAMqF,UAAU,GAAGF,QAAQ,CAACE,UAAU;EACtC,MAAMjG,OAAO,GAAGkG,mBAAmB,CAAC;IAChCF,YAAY;IACZJ,gBAAgB;IAChBK,UAAU;IACVJ;GACH,CAAC;EACF/F,OAAO,CAACE,OAAO,CAAC;AACpB;AAEA,SAASkG,mBAAmBA,CAACrG,OAK5B;EACG,MAAMsG,OAAO,GAAGjI,GAAG,CAAC2B,OAAO,CAACgG,UAAU,EAAGO,OAAO,IAC5CzJ,UAAU,CAACyJ,OAAO,CAAC,CACtB,CAACC,IAAI,CAAC,IAAI,CAAC;EACZ,MAAMjF,UAAU,GACZvB,OAAO,CAACoG,UAAU,CAACtE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG9B,OAAO,CAACoG,UAAU,CAACtE,GAAG;EAC9D,IAAI2E,WAAW,GACX,qCAAqCzG,OAAO,CAAC+F,gBAAgB,CAACS,IAAI,CAC9D,IAAI,CACP,SAASE,oBAAoB,CAAC1G,OAAO,CAACoG,UAAU,CAAC,GAAG7E,UAAU,GAAG,GAClE,YAAYvB,OAAO,CAACmG,YAAY,CAACQ,IAAI,WAAW,GAChD,IAAIL,OAAO,6DAA6D;EAE5EG,WAAW,GACPA,WAAW,GACX,8FAA8F,GAC9F,sBAAsB;EAC1B,OAAOA,WAAW;AACtB;AAEA,SAASC,oBAAoBA,CAACE,IAA+B;EACzD,IAAIA,IAAI,YAAY7J,WAAW,EAAE;IAC7B,OAAO,SAAS;GACnB,MAAM,IAAI6J,IAAI,YAAY3J,MAAM,EAAE;IAC/B,OAAO,QAAQ;GAClB,MAAM,IAAI2J,IAAI,YAAY5J,WAAW,EAAE;IACpC,OAAO,IAAI;GACd,MAAM,IAAI4J,IAAI,YAAY1J,mBAAmB,EAAE;IAC5C,OAAO,cAAc;GACxB,MAAM,IAAI0J,IAAI,YAAYzJ,gCAAgC,EAAE;IACzD,OAAO,kBAAkB;GAC5B,MAAM,IAAIyJ,IAAI,YAAYxJ,uBAAuB,EAAE;IAChD,OAAO,UAAU;GACpB,MAAM,IAAIwJ,IAAI,YAAYvJ,UAAU,EAAE;IACnC,OAAO,MAAM;GAChB,MAAM,IAAIuJ,IAAI,YAAYtJ,QAAQ,EAAE;IACjC,OAAO,SAAS;GACnB,MAAM;IACH,MAAMuJ,KAAK,CAAC,sBAAsB,CAAC;;AAE3C;AAEA,SAAS7B,yBAAyBA,CAC9BtD,IAAc,EACdoF,QAAkB,EAClBC,OAAe;EAEf,MAAMC,eAAe,GAAG7I,OAAO,CAC3B2I,QAAQ,CAACvB,OAAO,CAAC0B,QAAQ,EACxBpE,CAAC,IAAKA,CAAC,CAACqE,KAAK,CAACC,WAAW,CAC7B;EACD,MAAMC,cAAc,GAAGhJ,MAAM,CACzB4I,eAAe,CACVK,MAAM,CAAExE,CAAC,IAA0BA,CAAC,YAAYnF,cAAc,CAAC,CAC/DW,GAAG,CAAEwE,CAAC,IAAKA,CAAC,CAACoD,SAAS,CAAC,EAC3BpD,CAAC,IAAKA,CAAC,CAACb,YAAY,CACxB;EACD,OAAO;IACHsF,WAAW,EAAEP,OAAO;IACpBQ,kBAAkB,EAAEH,cAAc;IAClCI,SAAS,EAAE9F;GACd;AACL;AAEA,SAASoD,sBAAsBA,CAC3BoC,KAAe,EACf/B,KAAa;EAEb,OAAO+B,KAAK,CAACO,KAAK,CAACtC,KAAK,CAACnD,YAAY,CAAC;AAC1C;AAEA,SAASsD,eAAeA,CACpBC,OAAqB,EACrBJ,KAAa,EACbtG,YAA0B;EAE1B,MAAM6I,YAAY,GAAG,IAAI3J,YAAY,EAAE;EACvC,MAAM4J,iBAAiB,GAAgB,EAAE;EAEzC,KAAK,MAAMC,CAAC,IAAIrC,OAAO,CAAC0B,QAAQ,EAAE;IAC9B,IAAIpI,YAAY,CAACU,EAAE,CAACqI,CAAC,CAAC7E,GAAG,CAAC,KAAK,KAAK,EAAE;MAClC;;IAEJ,IAAI6E,CAAC,CAACV,KAAK,CAACW,IAAI,KAAKpK,aAAa,EAAE;MAChCkK,iBAAiB,CAACzC,IAAI,CAAC0C,CAAC,CAAC;MACzB;;IAEJ,MAAME,gBAAgB,GAAGF,CAAC,CAACV,KAAK,CAACC,WAAW,CAAC1H,MAAM;IACnD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiI,gBAAgB,EAAEjI,CAAC,EAAE,EAAE;MACvC,MAAMkI,UAAU,GAAGH,CAAC,CAACV,KAAK,CAACC,WAAW,CAACtH,CAAC,CAAC;MACzC,MAAMmI,MAAM,GAAGC,kBAAkB,CAACF,UAAU,EAAE5C,KAAK,CAAC;MACpD,IAAI6C,MAAM,KAAK/I,SAAS,EAAE;QACtByI,YAAY,CAAC9D,GAAG,CAAC;UACbsD,KAAK,EAAEc,MAAM;UACbjF,GAAG,EAAE6E,CAAC,CAAC7E,GAAG;UACVmF,KAAK,EAAEN,CAAC,CAACM;SACZ,CAAC;;;;EAKd,IAAI7C,KAA+B;EAEnC,IAAIsC,iBAAiB,CAAClI,MAAM,KAAK,CAAC,IAAIiI,YAAY,CAAC9H,IAAI,KAAK,CAAC,EAAE;IAC3DyF,KAAK,GAAGqC,YAAY;;EAGxB,IAAIrC,KAAK,KAAKpG,SAAS,EAAE;IACrBoG,KAAK,GAAG,IAAItH,YAAY,EAAE;IAC1B,KAAK,MAAM6J,CAAC,IAAIF,YAAY,CAACT,QAAQ,EAAE;MACnC7C,OAAO,CAACwD,CAAC,EAAEvC,KAAK,CAAC;;;EAIzB,IAAIsC,iBAAiB,CAAClI,MAAM,GAAG,CAAC,IAAI,CAAC0I,wBAAwB,CAAC9C,KAAK,CAAC,EAAE;IAClE,KAAK,MAAMuC,CAAC,IAAID,iBAAiB,EAAE;MAC/BtC,KAAK,CAACzB,GAAG,CAACgE,CAAC,CAAC;;;EAIpB,OAAOvC,KAAK;AAChB;AAEA,SAAS4C,kBAAkBA,CACvBF,UAAsB,EACtB5C,KAAa;EAEb,IACI4C,UAAU,YAAYrK,cAAc,IACpCb,YAAY,CAACsI,KAAK,EAAE4C,UAAU,CAAC9B,SAAS,CAAC,EAC3C;IACE,OAAO8B,UAAU,CAACC,MAAM;;EAE5B,OAAO/I,SAAS;AACpB;AAEA,SAAS0G,YAAYA,CACjBJ,OAAqB,EACrB1G,YAA0B;EAE1B,IAAIkE,GAAuB;EAC3B,KAAK,MAAM6E,CAAC,IAAIrC,OAAO,CAAC0B,QAAQ,EAAE;IAC9B,IAAIpI,YAAY,CAACU,EAAE,CAACqI,CAAC,CAAC7E,GAAG,CAAC,KAAK,IAAI,EAAE;MACjC,IAAIA,GAAG,KAAK9D,SAAS,EAAE;QACnB8D,GAAG,GAAG6E,CAAC,CAAC7E,GAAG;OACd,MAAM,IAAIA,GAAG,KAAK6E,CAAC,CAAC7E,GAAG,EAAE;QACtB,OAAO9D,SAAS;;;;EAI5B,OAAO8D,GAAG;AACd;AAEA,SAASwB,WAAWA,CAACH,OAAqB;EACtC,OAAO;IACHmB,OAAO,EAAEnB,OAAO;IAChBqD,KAAK,EAAE,EAAE;IACTxC,aAAa,EAAE,KAAK;IACpB3C,UAAU,EAAE,CAAC;GAChB;AACL;AAEA,SAASkD,UAAUA,CACftB,GAAQ,EACRkE,IAAc,EACdjD,KAAa,EACbkD,EAAY;EAEZA,EAAE,GAAG/D,WAAW,CAACJ,GAAG,EAAEmE,EAAE,CAAC;EACzBD,IAAI,CAACX,KAAK,CAACtC,KAAK,CAACnD,YAAY,CAAC,GAAGqG,EAAE;EACnC,OAAOA,EAAE;AACb;AAEA,SAAS/D,WAAWA,CAACJ,GAAQ,EAAEgD,KAAe;EAC1C,IAAIA,KAAK,KAAKlJ,SAAS,EAAE;IACrB,OAAOkJ,KAAK;;EAEhB;EACA;EACA,MAAMoB,MAAM,GAAGpB,KAAK,CAAC3B,OAAO,CAACzG,GAAG;EAChC,MAAME,QAAQ,GAAGkF,GAAG,CAAC/E,MAAM,CAACmJ,MAAM,CAAC;EACnC,IAAItJ,QAAQ,KAAKC,SAAS,EAAE;IACxB,OAAOD,QAAQ;;EAEnBkI,KAAK,CAAC3B,OAAO,CAACgD,QAAQ,EAAE;EACxBrE,GAAG,CAAC/E,MAAM,CAACmJ,MAAM,CAAC,GAAGpB,KAAK;EAC1B,OAAOA,KAAK;AAChB;AAEA,SAAS7C,iBAAiBA,CAAC6B,QAAkB;EACzC,MAAMX,OAAO,GAAG,IAAIxH,YAAY,EAAE;EAElC,MAAMyK,mBAAmB,GAAGtC,QAAQ,CAACiB,WAAW,CAAC1H,MAAM;EACvD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2I,mBAAmB,EAAE3I,CAAC,EAAE,EAAE;IAC1C,MAAMmI,MAAM,GAAG9B,QAAQ,CAACiB,WAAW,CAACtH,CAAC,CAAC,CAACmI,MAAM;IAC7C,MAAMS,MAAM,GAAc;MACtBvB,KAAK,EAAEc,MAAM;MACbjF,GAAG,EAAElD,CAAC;MACNqI,KAAK,EAAE;KACV;IACD9D,OAAO,CAACqE,MAAM,EAAElD,OAAO,CAAC;;EAG5B,OAAOA,OAAO;AAClB;AAEA,SAASnB,OAAOA,CAACqE,MAAiB,EAAElD,OAAqB;EACrD,MAAMmD,CAAC,GAAGD,MAAM,CAACvB,KAAK;EAEtB,IAAIwB,CAAC,CAACb,IAAI,KAAKpK,aAAa,EAAE;IAC1B,IAAIgL,MAAM,CAACP,KAAK,CAACzI,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMkJ,QAAQ,GAAG,CAAC,GAAGF,MAAM,CAACP,KAAK,CAAC;MAClC,MAAMU,WAAW,GAAGD,QAAQ,CAACE,GAAG,EAAG;MACnC,MAAMC,YAAY,GAAc;QAC5B5B,KAAK,EAAE0B,WAAW;QAClB7F,GAAG,EAAE0F,MAAM,CAAC1F,GAAG;QACfmF,KAAK,EAAES;OACV;MACDvE,OAAO,CAAC0E,YAAY,EAAEvD,OAAO,CAAC;KACjC,MAAM;MACH;MACA;MACAA,OAAO,CAAC3B,GAAG,CAAC6E,MAAM,CAAC;;IAEvB;;EAGJ,IAAI,CAACC,CAAC,CAACK,sBAAsB,EAAE;IAC3BxD,OAAO,CAAC3B,GAAG,CAAC6E,MAAM,CAAC;;EAGvB,MAAMX,gBAAgB,GAAGY,CAAC,CAACvB,WAAW,CAAC1H,MAAM;EAC7C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiI,gBAAgB,EAAEjI,CAAC,EAAE,EAAE;IACvC,MAAMkI,UAAU,GAAGW,CAAC,CAACvB,WAAW,CAACtH,CAAC,CAAC;IACnC,MAAM+H,CAAC,GAAGoB,gBAAgB,CAACP,MAAM,EAAEV,UAAU,CAAC;IAE9C,IAAIH,CAAC,KAAK3I,SAAS,EAAE;MACjBmF,OAAO,CAACwD,CAAC,EAAErC,OAAO,CAAC;;;AAG/B;AAEA,SAASyD,gBAAgBA,CACrBP,MAAiB,EACjBV,UAAsB;EAEtB,IAAIA,UAAU,YAAYlK,iBAAiB,EAAE;IACzC,OAAO;MACHqJ,KAAK,EAAEa,UAAU,CAACC,MAAM;MACxBjF,GAAG,EAAE0F,MAAM,CAAC1F,GAAG;MACfmF,KAAK,EAAEO,MAAM,CAACP;KACjB;GACJ,MAAM,IAAIH,UAAU,YAAYjK,cAAc,EAAE;IAC7C,MAAMoK,KAAK,GAAG,CAAC,GAAGO,MAAM,CAACP,KAAK,EAAEH,UAAU,CAACa,WAAW,CAAC;IACvD,OAAO;MACH1B,KAAK,EAAEa,UAAU,CAACC,MAAM;MACxBjF,GAAG,EAAE0F,MAAM,CAAC1F,GAAG;MACfmF;KACH;;EAEL,OAAOjJ,SAAS;AACpB;AAEA,SAASkJ,wBAAwBA,CAAC5C,OAAqB;EACnD,KAAK,MAAMqC,CAAC,IAAIrC,OAAO,CAAC0B,QAAQ,EAAE;IAC9B,IAAIW,CAAC,CAACV,KAAK,CAACW,IAAI,KAAKpK,aAAa,EAAE;MAChC,OAAO,IAAI;;;EAGnB,OAAO,KAAK;AAChB;AAEA,SAASwL,0BAA0BA,CAAC1D,OAAqB;EACrD,KAAK,MAAMqC,CAAC,IAAIrC,OAAO,CAAC0B,QAAQ,EAAE;IAC9B,IAAIW,CAAC,CAACV,KAAK,CAACW,IAAI,KAAKpK,aAAa,EAAE;MAChC,OAAO,KAAK;;;EAGpB,OAAO,IAAI;AACf;AAEA,SAASoI,gCAAgCA,CAACN,OAAqB;EAC3D,IAAI0D,0BAA0B,CAAC1D,OAAO,CAAC,EAAE;IACrC,OAAO,IAAI;;EAEf,MAAM2D,OAAO,GAAGC,qBAAqB,CAAC5D,OAAO,CAAC0B,QAAQ,CAAC;EACvD,MAAMmC,SAAS,GACXC,oBAAoB,CAACH,OAAO,CAAC,IAAI,CAACI,4BAA4B,CAACJ,OAAO,CAAC;EAC3E,OAAOE,SAAS;AACpB;AAEA,SAASD,qBAAqBA,CAC1B5D,OAA6B;EAE7B,MAAMgE,YAAY,GAAG,IAAIC,GAAG,EAAmC;EAC/D,KAAK,MAAM5B,CAAC,IAAIrC,OAAO,EAAE;IACrB,MAAMzG,GAAG,GAAGb,eAAe,CAAC2J,CAAC,EAAE,KAAK,CAAC;IACrC,IAAIhF,IAAI,GAAG2G,YAAY,CAACE,GAAG,CAAC3K,GAAG,CAAC;IAChC,IAAI8D,IAAI,KAAK3D,SAAS,EAAE;MACpB2D,IAAI,GAAG,EAAE;MACT2G,YAAY,CAAC7J,GAAG,CAACZ,GAAG,EAAE8D,IAAI,CAAC;;IAE/BA,IAAI,CAACgF,CAAC,CAAC7E,GAAG,CAAC,GAAG,IAAI;;EAEtB,OAAOwG,YAAY;AACvB;AAEA,SAASF,oBAAoBA,CACzBH,OAA6C;EAE7C,KAAK,MAAMvJ,KAAK,IAAIqE,KAAK,CAACoE,IAAI,CAACc,OAAO,CAACQ,MAAM,EAAE,CAAC,EAAE;IAC9C,IAAIC,MAAM,CAACC,IAAI,CAACjK,KAAK,CAAC,CAACF,MAAM,GAAG,CAAC,EAAE;MAC/B,OAAO,IAAI;;;EAGnB,OAAO,KAAK;AAChB;AAEA,SAAS6J,4BAA4BA,CACjCJ,OAA6C;EAE7C,KAAK,MAAMvJ,KAAK,IAAIqE,KAAK,CAACoE,IAAI,CAACc,OAAO,CAACQ,MAAM,EAAE,CAAC,EAAE;IAC9C,IAAIC,MAAM,CAACC,IAAI,CAACjK,KAAK,CAAC,CAACF,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI;;;EAGnB,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}