{"ast": null, "code": "export default function (d) {\n  const x = +this._x.call(null, d),\n    y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n    node = tree._root,\n    leaf = {\n      data: d\n    },\n    x0 = tree._x0,\n    y0 = tree._y0,\n    x1 = tree._x1,\n    y1 = tree._y1,\n    xm,\n    ym,\n    xp,\n    yp,\n    right,\n    bottom,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | xp >= xm));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\nexport function addAll(data) {\n  var d,\n    i,\n    n = data.length,\n    x,\n    y,\n    xz = new Array(n),\n    yz = new Array(n),\n    x0 = Infinity,\n    y0 = Infinity,\n    x1 = -Infinity,\n    y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["d", "x", "_x", "call", "y", "_y", "add", "cover", "tree", "isNaN", "parent", "node", "_root", "leaf", "data", "x0", "_x0", "y0", "_y0", "x1", "_x1", "y1", "_y1", "xm", "ym", "xp", "yp", "right", "bottom", "i", "j", "length", "next", "Array", "addAll", "n", "xz", "yz", "Infinity"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-quadtree/src/add.js"], "sourcesContent": ["export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\n\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      xm,\n      ym,\n      xp,\n      yp,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  var d, i, n = data.length,\n      x,\n      y,\n      xz = new Array(n),\n      yz = new Array(n),\n      x0 = Infinity,\n      y0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,MAAMC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IAC5BI,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;EAC9B,OAAOM,GAAG,CAAC,IAAI,CAACC,KAAK,CAACN,CAAC,EAAEG,CAAC,CAAC,EAAEH,CAAC,EAAEG,CAAC,EAAEJ,CAAC,CAAC;AACvC;AAEA,SAASM,GAAGA,CAACE,IAAI,EAAEP,CAAC,EAAEG,CAAC,EAAEJ,CAAC,EAAE;EAC1B,IAAIS,KAAK,CAACR,CAAC,CAAC,IAAIQ,KAAK,CAACL,CAAC,CAAC,EAAE,OAAOI,IAAI,CAAC,CAAC;;EAEvC,IAAIE,MAAM;IACNC,IAAI,GAAGH,IAAI,CAACI,KAAK;IACjBC,IAAI,GAAG;MAACC,IAAI,EAAEd;IAAC,CAAC;IAChBe,EAAE,GAAGP,IAAI,CAACQ,GAAG;IACbC,EAAE,GAAGT,IAAI,CAACU,GAAG;IACbC,EAAE,GAAGX,IAAI,CAACY,GAAG;IACbC,EAAE,GAAGb,IAAI,CAACc,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAACnB,IAAI,EAAE,OAAOH,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAEzC;EACA,OAAOG,IAAI,CAACoB,MAAM,EAAE;IAClB,IAAIJ,KAAK,GAAG1B,CAAC,KAAKsB,EAAE,GAAG,CAACR,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGQ,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;IAC5D,IAAIK,MAAM,GAAGxB,CAAC,KAAKoB,EAAE,GAAG,CAACP,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGO,EAAE,CAAC,KAAMH,EAAE,GAAGG,EAAE;IAC7D,IAAId,MAAM,GAAGC,IAAI,EAAE,EAAEA,IAAI,GAAGA,IAAI,CAACkB,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,CAAC,CAAC,EAAE,OAAOjB,MAAM,CAACmB,CAAC,CAAC,GAAGhB,IAAI,EAAEL,IAAI;EAC3F;;EAEA;EACAiB,EAAE,GAAG,CAACjB,IAAI,CAACN,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEQ,IAAI,CAACG,IAAI,CAAC;EACnCY,EAAE,GAAG,CAAClB,IAAI,CAACH,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEQ,IAAI,CAACG,IAAI,CAAC;EACnC,IAAIb,CAAC,KAAKwB,EAAE,IAAIrB,CAAC,KAAKsB,EAAE,EAAE,OAAOb,IAAI,CAACmB,IAAI,GAAGrB,IAAI,EAAED,MAAM,GAAGA,MAAM,CAACmB,CAAC,CAAC,GAAGhB,IAAI,GAAGL,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAEtG;EACA,GAAG;IACDE,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAACmB,CAAC,CAAC,GAAG,IAAII,KAAK,CAAC,CAAC,CAAC,GAAGzB,IAAI,CAACI,KAAK,GAAG,IAAIqB,KAAK,CAAC,CAAC,CAAC;IACtE,IAAIN,KAAK,GAAG1B,CAAC,KAAKsB,EAAE,GAAG,CAACR,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGQ,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;IAC5D,IAAIK,MAAM,GAAGxB,CAAC,KAAKoB,EAAE,GAAG,CAACP,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGO,EAAE,CAAC,KAAMH,EAAE,GAAGG,EAAE;EAC/D,CAAC,QAAQ,CAACK,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,OAAOG,CAAC,GAAG,CAACJ,EAAE,IAAIF,EAAE,KAAK,CAAC,GAAIC,EAAE,IAAIF,EAAG,CAAC;EACzE,OAAOb,MAAM,CAACoB,CAAC,CAAC,GAAGnB,IAAI,EAAED,MAAM,CAACmB,CAAC,CAAC,GAAGhB,IAAI,EAAEL,IAAI;AACjD;AAEA,OAAO,SAAS0B,MAAMA,CAACpB,IAAI,EAAE;EAC3B,IAAId,CAAC;IAAE6B,CAAC;IAAEM,CAAC,GAAGrB,IAAI,CAACiB,MAAM;IACrB9B,CAAC;IACDG,CAAC;IACDgC,EAAE,GAAG,IAAIH,KAAK,CAACE,CAAC,CAAC;IACjBE,EAAE,GAAG,IAAIJ,KAAK,CAACE,CAAC,CAAC;IACjBpB,EAAE,GAAGuB,QAAQ;IACbrB,EAAE,GAAGqB,QAAQ;IACbnB,EAAE,GAAG,CAACmB,QAAQ;IACdjB,EAAE,GAAG,CAACiB,QAAQ;;EAElB;EACA,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,CAAC,EAAE,EAAEN,CAAC,EAAE;IACtB,IAAIpB,KAAK,CAACR,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,GAAGc,IAAI,CAACe,CAAC,CAAC,CAAC,CAAC,IAAIpB,KAAK,CAACL,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC,CAAC,EAAE;IACtFoC,EAAE,CAACP,CAAC,CAAC,GAAG5B,CAAC;IACToC,EAAE,CAACR,CAAC,CAAC,GAAGzB,CAAC;IACT,IAAIH,CAAC,GAAGc,EAAE,EAAEA,EAAE,GAAGd,CAAC;IAClB,IAAIA,CAAC,GAAGkB,EAAE,EAAEA,EAAE,GAAGlB,CAAC;IAClB,IAAIG,CAAC,GAAGa,EAAE,EAAEA,EAAE,GAAGb,CAAC;IAClB,IAAIA,CAAC,GAAGiB,EAAE,EAAEA,EAAE,GAAGjB,CAAC;EACpB;;EAEA;EACA,IAAIW,EAAE,GAAGI,EAAE,IAAIF,EAAE,GAAGI,EAAE,EAAE,OAAO,IAAI;;EAEnC;EACA,IAAI,CAACd,KAAK,CAACQ,EAAE,EAAEE,EAAE,CAAC,CAACV,KAAK,CAACY,EAAE,EAAEE,EAAE,CAAC;;EAEhC;EACA,KAAKQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,CAAC,EAAE,EAAEN,CAAC,EAAE;IACtBvB,GAAG,CAAC,IAAI,EAAE8B,EAAE,CAACP,CAAC,CAAC,EAAEQ,EAAE,CAACR,CAAC,CAAC,EAAEf,IAAI,CAACe,CAAC,CAAC,CAAC;EAClC;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}