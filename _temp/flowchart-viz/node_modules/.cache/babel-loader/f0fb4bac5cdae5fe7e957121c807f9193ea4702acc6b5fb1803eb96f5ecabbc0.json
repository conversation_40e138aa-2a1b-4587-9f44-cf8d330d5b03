{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { LangiumParser } from './langium-parser.js';\nimport { createParser } from './parser-builder-base.js';\n/**\n * Create and finalize a Langium parser. The parser rules are derived from the grammar, which is\n * available at `services.Grammar`.\n */\nexport function createLangiumParser(services) {\n  const parser = prepareLangiumParser(services);\n  parser.finalize();\n  return parser;\n}\n/**\n * Create a Langium parser without finalizing it. This is used to extract more detailed error\n * information when the parser is initially validated.\n */\nexport function prepareLangiumParser(services) {\n  const grammar = services.Grammar;\n  const lexer = services.parser.Lexer;\n  const parser = new LangiumParser(services);\n  return createParser(grammar, parser, lexer.definition);\n}", "map": {"version": 3, "names": ["LangiumParser", "create<PERSON><PERSON><PERSON>", "createLangiumParser", "services", "parser", "prepareLangiumParser", "finalize", "grammar", "Grammar", "lexer", "<PERSON><PERSON>", "definition"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/parser/langium-parser-builder.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport { LangiumParser } from './langium-parser.js';\r\nimport { createParser } from './parser-builder-base.js';\r\n\r\n/**\r\n * Create and finalize a Langium parser. The parser rules are derived from the grammar, which is\r\n * available at `services.Grammar`.\r\n */\r\nexport function createLangiumParser(services: LangiumCoreServices): LangiumParser {\r\n    const parser = prepareLangiumParser(services);\r\n    parser.finalize();\r\n    return parser;\r\n}\r\n\r\n/**\r\n * Create a Langium parser without finalizing it. This is used to extract more detailed error\r\n * information when the parser is initially validated.\r\n */\r\nexport function prepareLangiumParser(services: LangiumCoreServices): LangiumParser {\r\n    const grammar = services.Grammar;\r\n    const lexer = services.parser.Lexer;\r\n    const parser = new LangiumParser(services);\r\n    return createParser(grammar, parser, lexer.definition);\r\n}\r\n"], "mappings": "AAAA;;;;;AAOA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,SAASC,YAAY,QAAQ,0BAA0B;AAEvD;;;;AAIA,OAAM,SAAUC,mBAAmBA,CAACC,QAA6B;EAC7D,MAAMC,MAAM,GAAGC,oBAAoB,CAACF,QAAQ,CAAC;EAC7CC,MAAM,CAACE,QAAQ,EAAE;EACjB,OAAOF,MAAM;AACjB;AAEA;;;;AAIA,OAAM,SAAUC,oBAAoBA,CAACF,QAA6B;EAC9D,MAAMI,OAAO,GAAGJ,QAAQ,CAACK,OAAO;EAChC,MAAMC,KAAK,GAAGN,QAAQ,CAACC,MAAM,CAACM,KAAK;EACnC,MAAMN,MAAM,GAAG,IAAIJ,aAAa,CAACG,QAAQ,CAAC;EAC1C,OAAOF,YAAY,CAACM,OAAO,EAAEH,MAAM,EAAEK,KAAK,CAACE,UAAU,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}