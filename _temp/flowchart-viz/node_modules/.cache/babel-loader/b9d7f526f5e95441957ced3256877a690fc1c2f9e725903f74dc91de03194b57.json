{"ast": null, "code": "import arrayReduce from './_arrayReduce.js';\nimport deburr from './deburr.js';\nimport words from './words.js';\n\n/** Used to compose unicode capture groups. */\nvar rsApos = \"['\\u2019]\";\n\n/** Used to match apostrophes. */\nvar reApos = RegExp(rsApos, 'g');\n\n/**\n * Creates a function like `_.camelCase`.\n *\n * @private\n * @param {Function} callback The function to combine each word.\n * @returns {Function} Returns the new compounder function.\n */\nfunction createCompounder(callback) {\n  return function (string) {\n    return arrayReduce(words(deburr(string).replace(reApos, '')), callback, '');\n  };\n}\nexport default createCompounder;", "map": {"version": 3, "names": ["arrayReduce", "deburr", "words", "rsApos", "reApos", "RegExp", "createCompounder", "callback", "string", "replace"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_createCompounder.js"], "sourcesContent": ["import arrayReduce from './_arrayReduce.js';\nimport deburr from './deburr.js';\nimport words from './words.js';\n\n/** Used to compose unicode capture groups. */\nvar rsApos = \"['\\u2019]\";\n\n/** Used to match apostrophes. */\nvar reApos = RegExp(rsApos, 'g');\n\n/**\n * Creates a function like `_.camelCase`.\n *\n * @private\n * @param {Function} callback The function to combine each word.\n * @returns {Function} Returns the new compounder function.\n */\nfunction createCompounder(callback) {\n  return function(string) {\n    return arrayReduce(words(deburr(string).replace(reApos, '')), callback, '');\n  };\n}\n\nexport default createCompounder;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,KAAK,MAAM,YAAY;;AAE9B;AACA,IAAIC,MAAM,GAAG,WAAW;;AAExB;AACA,IAAIC,MAAM,GAAGC,MAAM,CAACF,MAAM,EAAE,GAAG,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,OAAO,UAASC,MAAM,EAAE;IACtB,OAAOR,WAAW,CAACE,KAAK,CAACD,MAAM,CAACO,MAAM,CAAC,CAACC,OAAO,CAACL,MAAM,EAAE,EAAE,CAAC,CAAC,EAAEG,QAAQ,EAAE,EAAE,CAAC;EAC7E,CAAC;AACH;AAEA,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}