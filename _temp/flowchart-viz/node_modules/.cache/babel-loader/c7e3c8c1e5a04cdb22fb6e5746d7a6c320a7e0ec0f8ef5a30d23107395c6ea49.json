{"ast": null, "code": "(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define([], factory);else if (typeof exports === 'object') exports[\"layoutBase\"] = factory();else root[\"layoutBase\"] = factory();\n})(this, function () {\n  return /******/function (modules) {\n    // webpackBootstrap\n    /******/ // The module cache\n    /******/\n    var installedModules = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/\n      /******/ // Check if module is in cache\n      /******/if (installedModules[moduleId]) {\n        /******/return installedModules[moduleId].exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = installedModules[moduleId] = {\n        /******/i: moduleId,\n        /******/l: false,\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Flag the module as loaded\n      /******/\n      module.l = true;\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /******/\n    /******/ // expose the modules object (__webpack_modules__)\n    /******/\n    __webpack_require__.m = modules;\n    /******/\n    /******/ // expose the module cache\n    /******/\n    __webpack_require__.c = installedModules;\n    /******/\n    /******/ // identity function for calling harmony imports with the correct context\n    /******/\n    __webpack_require__.i = function (value) {\n      return value;\n    };\n    /******/\n    /******/ // define getter function for harmony exports\n    /******/\n    __webpack_require__.d = function (exports, name, getter) {\n      /******/if (!__webpack_require__.o(exports, name)) {\n        /******/Object.defineProperty(exports, name, {\n          /******/configurable: false,\n          /******/enumerable: true,\n          /******/get: getter\n          /******/\n        });\n        /******/\n      }\n      /******/\n    };\n    /******/\n    /******/ // getDefaultExport function for compatibility with non-harmony modules\n    /******/\n    __webpack_require__.n = function (module) {\n      /******/var getter = module && module.__esModule ? /******/function getDefault() {\n        return module['default'];\n      } : /******/function getModuleExports() {\n        return module;\n      };\n      /******/\n      __webpack_require__.d(getter, 'a', getter);\n      /******/\n      return getter;\n      /******/\n    };\n    /******/\n    /******/ // Object.prototype.hasOwnProperty.call\n    /******/\n    __webpack_require__.o = function (object, property) {\n      return Object.prototype.hasOwnProperty.call(object, property);\n    };\n    /******/\n    /******/ // __webpack_public_path__\n    /******/\n    __webpack_require__.p = \"\";\n    /******/\n    /******/ // Load entry module and return exports\n    /******/\n    return __webpack_require__(__webpack_require__.s = 26);\n    /******/\n  }\n  /************************************************************************/\n  /******/([(/* 0 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function LayoutConstants() {}\n\n    /**\r\n     * Layout Quality: 0:draft, 1:default, 2:proof\r\n     */\n    LayoutConstants.QUALITY = 1;\n\n    /**\r\n     * Default parameters\r\n     */\n    LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\n    LayoutConstants.DEFAULT_INCREMENTAL = false;\n    LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\n    LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\n    LayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\n    LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n    // -----------------------------------------------------------------------------\n    // Section: General other constants\n    // -----------------------------------------------------------------------------\n    /*\r\n     * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n     * assume margins on all four sides to be uniform.\r\n     */\n    LayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n    /*\r\n     * Whether to consider labels in node dimensions or not\r\n     */\n    LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n    /*\r\n     * Default dimension of a non-compound node.\r\n     */\n    LayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n    /*\r\n     * Default dimension of a non-compound node.\r\n     */\n    LayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n    /*\r\n     * Empty compound node size. When a compound node is empty, its both\r\n     * dimensions should be of this value.\r\n     */\n    LayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n    /*\r\n     * Minimum length that an edge should take during layout\r\n     */\n    LayoutConstants.MIN_EDGE_LENGTH = 1;\n\n    /*\r\n     * World boundaries that layout operates on\r\n     */\n    LayoutConstants.WORLD_BOUNDARY = 1000000;\n\n    /*\r\n     * World boundaries that random positioning can be performed with\r\n     */\n    LayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n    /*\r\n     * Coordinates of the world center\r\n     */\n    LayoutConstants.WORLD_CENTER_X = 1200;\n    LayoutConstants.WORLD_CENTER_Y = 900;\n    module.exports = LayoutConstants;\n\n    /***/\n  }), (/* 1 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var IGeometry = __webpack_require__(8);\n    var IMath = __webpack_require__(9);\n    function LEdge(source, target, vEdge) {\n      LGraphObject.call(this, vEdge);\n      this.isOverlapingSourceAndTarget = false;\n      this.vGraphObject = vEdge;\n      this.bendpoints = [];\n      this.source = source;\n      this.target = target;\n    }\n    LEdge.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LEdge[prop] = LGraphObject[prop];\n    }\n    LEdge.prototype.getSource = function () {\n      return this.source;\n    };\n    LEdge.prototype.getTarget = function () {\n      return this.target;\n    };\n    LEdge.prototype.isInterGraph = function () {\n      return this.isInterGraph;\n    };\n    LEdge.prototype.getLength = function () {\n      return this.length;\n    };\n    LEdge.prototype.isOverlapingSourceAndTarget = function () {\n      return this.isOverlapingSourceAndTarget;\n    };\n    LEdge.prototype.getBendpoints = function () {\n      return this.bendpoints;\n    };\n    LEdge.prototype.getLca = function () {\n      return this.lca;\n    };\n    LEdge.prototype.getSourceInLca = function () {\n      return this.sourceInLca;\n    };\n    LEdge.prototype.getTargetInLca = function () {\n      return this.targetInLca;\n    };\n    LEdge.prototype.getOtherEnd = function (node) {\n      if (this.source === node) {\n        return this.target;\n      } else if (this.target === node) {\n        return this.source;\n      } else {\n        throw \"Node is not incident with this edge\";\n      }\n    };\n    LEdge.prototype.getOtherEndInGraph = function (node, graph) {\n      var otherEnd = this.getOtherEnd(node);\n      var root = graph.getGraphManager().getRoot();\n      while (true) {\n        if (otherEnd.getOwner() == graph) {\n          return otherEnd;\n        }\n        if (otherEnd.getOwner() == root) {\n          break;\n        }\n        otherEnd = otherEnd.getOwner().getParent();\n      }\n      return null;\n    };\n    LEdge.prototype.updateLength = function () {\n      var clipPointCoordinates = new Array(4);\n      this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n      if (!this.isOverlapingSourceAndTarget) {\n        this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n        this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n        if (Math.abs(this.lengthX) < 1.0) {\n          this.lengthX = IMath.sign(this.lengthX);\n        }\n        if (Math.abs(this.lengthY) < 1.0) {\n          this.lengthY = IMath.sign(this.lengthY);\n        }\n        this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n      }\n    };\n    LEdge.prototype.updateLengthSimple = function () {\n      this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n      this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n      if (Math.abs(this.lengthX) < 1.0) {\n        this.lengthX = IMath.sign(this.lengthX);\n      }\n      if (Math.abs(this.lengthY) < 1.0) {\n        this.lengthY = IMath.sign(this.lengthY);\n      }\n      this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n    };\n    module.exports = LEdge;\n\n    /***/\n  }), (/* 2 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function LGraphObject(vGraphObject) {\n      this.vGraphObject = vGraphObject;\n    }\n    module.exports = LGraphObject;\n\n    /***/\n  }), (/* 3 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var Integer = __webpack_require__(10);\n    var RectangleD = __webpack_require__(13);\n    var LayoutConstants = __webpack_require__(0);\n    var RandomSeed = __webpack_require__(16);\n    var PointD = __webpack_require__(4);\n    function LNode(gm, loc, size, vNode) {\n      //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n      if (size == null && vNode == null) {\n        vNode = loc;\n      }\n      LGraphObject.call(this, vNode);\n\n      //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n      if (gm.graphManager != null) gm = gm.graphManager;\n      this.estimatedSize = Integer.MIN_VALUE;\n      this.inclusionTreeDepth = Integer.MAX_VALUE;\n      this.vGraphObject = vNode;\n      this.edges = [];\n      this.graphManager = gm;\n      if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n    }\n    LNode.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LNode[prop] = LGraphObject[prop];\n    }\n    LNode.prototype.getEdges = function () {\n      return this.edges;\n    };\n    LNode.prototype.getChild = function () {\n      return this.child;\n    };\n    LNode.prototype.getOwner = function () {\n      //  if (this.owner != null) {\n      //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n      //      throw \"assert failed\";\n      //    }\n      //  }\n\n      return this.owner;\n    };\n    LNode.prototype.getWidth = function () {\n      return this.rect.width;\n    };\n    LNode.prototype.setWidth = function (width) {\n      this.rect.width = width;\n    };\n    LNode.prototype.getHeight = function () {\n      return this.rect.height;\n    };\n    LNode.prototype.setHeight = function (height) {\n      this.rect.height = height;\n    };\n    LNode.prototype.getCenterX = function () {\n      return this.rect.x + this.rect.width / 2;\n    };\n    LNode.prototype.getCenterY = function () {\n      return this.rect.y + this.rect.height / 2;\n    };\n    LNode.prototype.getCenter = function () {\n      return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n    };\n    LNode.prototype.getLocation = function () {\n      return new PointD(this.rect.x, this.rect.y);\n    };\n    LNode.prototype.getRect = function () {\n      return this.rect;\n    };\n    LNode.prototype.getDiagonal = function () {\n      return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n    };\n\n    /**\n     * This method returns half the diagonal length of this node.\n     */\n    LNode.prototype.getHalfTheDiagonal = function () {\n      return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n    };\n    LNode.prototype.setRect = function (upperLeft, dimension) {\n      this.rect.x = upperLeft.x;\n      this.rect.y = upperLeft.y;\n      this.rect.width = dimension.width;\n      this.rect.height = dimension.height;\n    };\n    LNode.prototype.setCenter = function (cx, cy) {\n      this.rect.x = cx - this.rect.width / 2;\n      this.rect.y = cy - this.rect.height / 2;\n    };\n    LNode.prototype.setLocation = function (x, y) {\n      this.rect.x = x;\n      this.rect.y = y;\n    };\n    LNode.prototype.moveBy = function (dx, dy) {\n      this.rect.x += dx;\n      this.rect.y += dy;\n    };\n    LNode.prototype.getEdgeListToNode = function (to) {\n      var edgeList = [];\n      var edge;\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (edge.target == to) {\n          if (edge.source != self) throw \"Incorrect edge source!\";\n          edgeList.push(edge);\n        }\n      });\n      return edgeList;\n    };\n    LNode.prototype.getEdgesBetween = function (other) {\n      var edgeList = [];\n      var edge;\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n        if (edge.target == other || edge.source == other) {\n          edgeList.push(edge);\n        }\n      });\n      return edgeList;\n    };\n    LNode.prototype.getNeighborsList = function () {\n      var neighbors = new Set();\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (edge.source == self) {\n          neighbors.add(edge.target);\n        } else {\n          if (edge.target != self) {\n            throw \"Incorrect incidency!\";\n          }\n          neighbors.add(edge.source);\n        }\n      });\n      return neighbors;\n    };\n    LNode.prototype.withChildren = function () {\n      var withNeighborsList = new Set();\n      var childNode;\n      var children;\n      withNeighborsList.add(this);\n      if (this.child != null) {\n        var nodes = this.child.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          childNode = nodes[i];\n          children = childNode.withChildren();\n          children.forEach(function (node) {\n            withNeighborsList.add(node);\n          });\n        }\n      }\n      return withNeighborsList;\n    };\n    LNode.prototype.getNoOfChildren = function () {\n      var noOfChildren = 0;\n      var childNode;\n      if (this.child == null) {\n        noOfChildren = 1;\n      } else {\n        var nodes = this.child.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          childNode = nodes[i];\n          noOfChildren += childNode.getNoOfChildren();\n        }\n      }\n      if (noOfChildren == 0) {\n        noOfChildren = 1;\n      }\n      return noOfChildren;\n    };\n    LNode.prototype.getEstimatedSize = function () {\n      if (this.estimatedSize == Integer.MIN_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.estimatedSize;\n    };\n    LNode.prototype.calcEstimatedSize = function () {\n      if (this.child == null) {\n        return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n      } else {\n        this.estimatedSize = this.child.calcEstimatedSize();\n        this.rect.width = this.estimatedSize;\n        this.rect.height = this.estimatedSize;\n        return this.estimatedSize;\n      }\n    };\n    LNode.prototype.scatter = function () {\n      var randomCenterX;\n      var randomCenterY;\n      var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n      var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n      this.rect.x = randomCenterX;\n      this.rect.y = randomCenterY;\n    };\n    LNode.prototype.updateBounds = function () {\n      if (this.getChild() == null) {\n        throw \"assert failed\";\n      }\n      if (this.getChild().getNodes().length != 0) {\n        // wrap the children nodes by re-arranging the boundaries\n        var childGraph = this.getChild();\n        childGraph.updateBounds(true);\n        this.rect.x = childGraph.getLeft();\n        this.rect.y = childGraph.getTop();\n        this.setWidth(childGraph.getRight() - childGraph.getLeft());\n        this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n        // Update compound bounds considering its label properties    \n        if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n          var width = childGraph.getRight() - childGraph.getLeft();\n          var height = childGraph.getBottom() - childGraph.getTop();\n          if (this.labelWidth > width) {\n            this.rect.x -= (this.labelWidth - width) / 2;\n            this.setWidth(this.labelWidth);\n          }\n          if (this.labelHeight > height) {\n            if (this.labelPos == \"center\") {\n              this.rect.y -= (this.labelHeight - height) / 2;\n            } else if (this.labelPos == \"top\") {\n              this.rect.y -= this.labelHeight - height;\n            }\n            this.setHeight(this.labelHeight);\n          }\n        }\n      }\n    };\n    LNode.prototype.getInclusionTreeDepth = function () {\n      if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.inclusionTreeDepth;\n    };\n    LNode.prototype.transform = function (trans) {\n      var left = this.rect.x;\n      if (left > LayoutConstants.WORLD_BOUNDARY) {\n        left = LayoutConstants.WORLD_BOUNDARY;\n      } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n        left = -LayoutConstants.WORLD_BOUNDARY;\n      }\n      var top = this.rect.y;\n      if (top > LayoutConstants.WORLD_BOUNDARY) {\n        top = LayoutConstants.WORLD_BOUNDARY;\n      } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n        top = -LayoutConstants.WORLD_BOUNDARY;\n      }\n      var leftTop = new PointD(left, top);\n      var vLeftTop = trans.inverseTransformPoint(leftTop);\n      this.setLocation(vLeftTop.x, vLeftTop.y);\n    };\n    LNode.prototype.getLeft = function () {\n      return this.rect.x;\n    };\n    LNode.prototype.getRight = function () {\n      return this.rect.x + this.rect.width;\n    };\n    LNode.prototype.getTop = function () {\n      return this.rect.y;\n    };\n    LNode.prototype.getBottom = function () {\n      return this.rect.y + this.rect.height;\n    };\n    LNode.prototype.getParent = function () {\n      if (this.owner == null) {\n        return null;\n      }\n      return this.owner.getParent();\n    };\n    module.exports = LNode;\n\n    /***/\n  }), (/* 4 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function PointD(x, y) {\n      if (x == null && y == null) {\n        this.x = 0;\n        this.y = 0;\n      } else {\n        this.x = x;\n        this.y = y;\n      }\n    }\n    PointD.prototype.getX = function () {\n      return this.x;\n    };\n    PointD.prototype.getY = function () {\n      return this.y;\n    };\n    PointD.prototype.setX = function (x) {\n      this.x = x;\n    };\n    PointD.prototype.setY = function (y) {\n      this.y = y;\n    };\n    PointD.prototype.getDifference = function (pt) {\n      return new DimensionD(this.x - pt.x, this.y - pt.y);\n    };\n    PointD.prototype.getCopy = function () {\n      return new PointD(this.x, this.y);\n    };\n    PointD.prototype.translate = function (dim) {\n      this.x += dim.width;\n      this.y += dim.height;\n      return this;\n    };\n    module.exports = PointD;\n\n    /***/\n  }), (/* 5 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var Integer = __webpack_require__(10);\n    var LayoutConstants = __webpack_require__(0);\n    var LGraphManager = __webpack_require__(6);\n    var LNode = __webpack_require__(3);\n    var LEdge = __webpack_require__(1);\n    var RectangleD = __webpack_require__(13);\n    var Point = __webpack_require__(12);\n    var LinkedList = __webpack_require__(11);\n    function LGraph(parent, obj2, vGraph) {\n      LGraphObject.call(this, vGraph);\n      this.estimatedSize = Integer.MIN_VALUE;\n      this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n      this.edges = [];\n      this.nodes = [];\n      this.isConnected = false;\n      this.parent = parent;\n      if (obj2 != null && obj2 instanceof LGraphManager) {\n        this.graphManager = obj2;\n      } else if (obj2 != null && obj2 instanceof Layout) {\n        this.graphManager = obj2.graphManager;\n      }\n    }\n    LGraph.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LGraph[prop] = LGraphObject[prop];\n    }\n    LGraph.prototype.getNodes = function () {\n      return this.nodes;\n    };\n    LGraph.prototype.getEdges = function () {\n      return this.edges;\n    };\n    LGraph.prototype.getGraphManager = function () {\n      return this.graphManager;\n    };\n    LGraph.prototype.getParent = function () {\n      return this.parent;\n    };\n    LGraph.prototype.getLeft = function () {\n      return this.left;\n    };\n    LGraph.prototype.getRight = function () {\n      return this.right;\n    };\n    LGraph.prototype.getTop = function () {\n      return this.top;\n    };\n    LGraph.prototype.getBottom = function () {\n      return this.bottom;\n    };\n    LGraph.prototype.isConnected = function () {\n      return this.isConnected;\n    };\n    LGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n      if (sourceNode == null && targetNode == null) {\n        var newNode = obj1;\n        if (this.graphManager == null) {\n          throw \"Graph has no graph mgr!\";\n        }\n        if (this.getNodes().indexOf(newNode) > -1) {\n          throw \"Node already in graph!\";\n        }\n        newNode.owner = this;\n        this.getNodes().push(newNode);\n        return newNode;\n      } else {\n        var newEdge = obj1;\n        if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n          throw \"Source or target not in graph!\";\n        }\n        if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n          throw \"Both owners must be this graph!\";\n        }\n        if (sourceNode.owner != targetNode.owner) {\n          return null;\n        }\n\n        // set source and target\n        newEdge.source = sourceNode;\n        newEdge.target = targetNode;\n\n        // set as intra-graph edge\n        newEdge.isInterGraph = false;\n\n        // add to graph edge list\n        this.getEdges().push(newEdge);\n\n        // add to incidency lists\n        sourceNode.edges.push(newEdge);\n        if (targetNode != sourceNode) {\n          targetNode.edges.push(newEdge);\n        }\n        return newEdge;\n      }\n    };\n    LGraph.prototype.remove = function (obj) {\n      var node = obj;\n      if (obj instanceof LNode) {\n        if (node == null) {\n          throw \"Node is null!\";\n        }\n        if (!(node.owner != null && node.owner == this)) {\n          throw \"Owner graph is invalid!\";\n        }\n        if (this.graphManager == null) {\n          throw \"Owner graph manager is invalid!\";\n        }\n        // remove incident edges first (make a copy to do it safely)\n        var edgesToBeRemoved = node.edges.slice();\n        var edge;\n        var s = edgesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          edge = edgesToBeRemoved[i];\n          if (edge.isInterGraph) {\n            this.graphManager.remove(edge);\n          } else {\n            edge.source.owner.remove(edge);\n          }\n        }\n\n        // now the node itself\n        var index = this.nodes.indexOf(node);\n        if (index == -1) {\n          throw \"Node not in owner node list!\";\n        }\n        this.nodes.splice(index, 1);\n      } else if (obj instanceof LEdge) {\n        var edge = obj;\n        if (edge == null) {\n          throw \"Edge is null!\";\n        }\n        if (!(edge.source != null && edge.target != null)) {\n          throw \"Source and/or target is null!\";\n        }\n        if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n          throw \"Source and/or target owner is invalid!\";\n        }\n        var sourceIndex = edge.source.edges.indexOf(edge);\n        var targetIndex = edge.target.edges.indexOf(edge);\n        if (!(sourceIndex > -1 && targetIndex > -1)) {\n          throw \"Source and/or target doesn't know this edge!\";\n        }\n        edge.source.edges.splice(sourceIndex, 1);\n        if (edge.target != edge.source) {\n          edge.target.edges.splice(targetIndex, 1);\n        }\n        var index = edge.source.owner.getEdges().indexOf(edge);\n        if (index == -1) {\n          throw \"Not in owner's edge list!\";\n        }\n        edge.source.owner.getEdges().splice(index, 1);\n      }\n    };\n    LGraph.prototype.updateLeftTop = function () {\n      var top = Integer.MAX_VALUE;\n      var left = Integer.MAX_VALUE;\n      var nodeTop;\n      var nodeLeft;\n      var margin;\n      var nodes = this.getNodes();\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        nodeTop = lNode.getTop();\n        nodeLeft = lNode.getLeft();\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n      }\n\n      // Do we have any nodes in this graph?\n      if (top == Integer.MAX_VALUE) {\n        return null;\n      }\n      if (nodes[0].getParent().paddingLeft != undefined) {\n        margin = nodes[0].getParent().paddingLeft;\n      } else {\n        margin = this.margin;\n      }\n      this.left = left - margin;\n      this.top = top - margin;\n\n      // Apply the margins and return the result\n      return new Point(this.left, this.top);\n    };\n    LGraph.prototype.updateBounds = function (recursive) {\n      // calculate bounds\n      var left = Integer.MAX_VALUE;\n      var right = -Integer.MAX_VALUE;\n      var top = Integer.MAX_VALUE;\n      var bottom = -Integer.MAX_VALUE;\n      var nodeLeft;\n      var nodeRight;\n      var nodeTop;\n      var nodeBottom;\n      var margin;\n      var nodes = this.nodes;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        if (recursive && lNode.child != null) {\n          lNode.updateBounds();\n        }\n        nodeLeft = lNode.getLeft();\n        nodeRight = lNode.getRight();\n        nodeTop = lNode.getTop();\n        nodeBottom = lNode.getBottom();\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n        if (right < nodeRight) {\n          right = nodeRight;\n        }\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (bottom < nodeBottom) {\n          bottom = nodeBottom;\n        }\n      }\n      var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n      if (left == Integer.MAX_VALUE) {\n        this.left = this.parent.getLeft();\n        this.right = this.parent.getRight();\n        this.top = this.parent.getTop();\n        this.bottom = this.parent.getBottom();\n      }\n      if (nodes[0].getParent().paddingLeft != undefined) {\n        margin = nodes[0].getParent().paddingLeft;\n      } else {\n        margin = this.margin;\n      }\n      this.left = boundingRect.x - margin;\n      this.right = boundingRect.x + boundingRect.width + margin;\n      this.top = boundingRect.y - margin;\n      this.bottom = boundingRect.y + boundingRect.height + margin;\n    };\n    LGraph.calculateBounds = function (nodes) {\n      var left = Integer.MAX_VALUE;\n      var right = -Integer.MAX_VALUE;\n      var top = Integer.MAX_VALUE;\n      var bottom = -Integer.MAX_VALUE;\n      var nodeLeft;\n      var nodeRight;\n      var nodeTop;\n      var nodeBottom;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        nodeLeft = lNode.getLeft();\n        nodeRight = lNode.getRight();\n        nodeTop = lNode.getTop();\n        nodeBottom = lNode.getBottom();\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n        if (right < nodeRight) {\n          right = nodeRight;\n        }\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (bottom < nodeBottom) {\n          bottom = nodeBottom;\n        }\n      }\n      var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n      return boundingRect;\n    };\n    LGraph.prototype.getInclusionTreeDepth = function () {\n      if (this == this.graphManager.getRoot()) {\n        return 1;\n      } else {\n        return this.parent.getInclusionTreeDepth();\n      }\n    };\n    LGraph.prototype.getEstimatedSize = function () {\n      if (this.estimatedSize == Integer.MIN_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.estimatedSize;\n    };\n    LGraph.prototype.calcEstimatedSize = function () {\n      var size = 0;\n      var nodes = this.nodes;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        size += lNode.calcEstimatedSize();\n      }\n      if (size == 0) {\n        this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n      } else {\n        this.estimatedSize = size / Math.sqrt(this.nodes.length);\n      }\n      return this.estimatedSize;\n    };\n    LGraph.prototype.updateConnected = function () {\n      var self = this;\n      if (this.nodes.length == 0) {\n        this.isConnected = true;\n        return;\n      }\n      var queue = new LinkedList();\n      var visited = new Set();\n      var currentNode = this.nodes[0];\n      var neighborEdges;\n      var currentNeighbor;\n      var childrenOfNode = currentNode.withChildren();\n      childrenOfNode.forEach(function (node) {\n        queue.push(node);\n        visited.add(node);\n      });\n      while (queue.length !== 0) {\n        currentNode = queue.shift();\n\n        // Traverse all neighbors of this node\n        neighborEdges = currentNode.getEdges();\n        var size = neighborEdges.length;\n        for (var i = 0; i < size; i++) {\n          var neighborEdge = neighborEdges[i];\n          currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n          // Add unvisited neighbors to the list to visit\n          if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n            var childrenOfNeighbor = currentNeighbor.withChildren();\n            childrenOfNeighbor.forEach(function (node) {\n              queue.push(node);\n              visited.add(node);\n            });\n          }\n        }\n      }\n      this.isConnected = false;\n      if (visited.size >= this.nodes.length) {\n        var noOfVisitedInThisGraph = 0;\n        visited.forEach(function (visitedNode) {\n          if (visitedNode.owner == self) {\n            noOfVisitedInThisGraph++;\n          }\n        });\n        if (noOfVisitedInThisGraph == this.nodes.length) {\n          this.isConnected = true;\n        }\n      }\n    };\n    module.exports = LGraph;\n\n    /***/\n  }), (/* 6 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraph;\n    var LEdge = __webpack_require__(1);\n    function LGraphManager(layout) {\n      LGraph = __webpack_require__(5); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n      this.layout = layout;\n      this.graphs = [];\n      this.edges = [];\n    }\n    LGraphManager.prototype.addRoot = function () {\n      var ngraph = this.layout.newGraph();\n      var nnode = this.layout.newNode(null);\n      var root = this.add(ngraph, nnode);\n      this.setRootGraph(root);\n      return this.rootGraph;\n    };\n    LGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n      //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n      if (newEdge == null && sourceNode == null && targetNode == null) {\n        if (newGraph == null) {\n          throw \"Graph is null!\";\n        }\n        if (parentNode == null) {\n          throw \"Parent node is null!\";\n        }\n        if (this.graphs.indexOf(newGraph) > -1) {\n          throw \"Graph already in this graph mgr!\";\n        }\n        this.graphs.push(newGraph);\n        if (newGraph.parent != null) {\n          throw \"Already has a parent!\";\n        }\n        if (parentNode.child != null) {\n          throw \"Already has a child!\";\n        }\n        newGraph.parent = parentNode;\n        parentNode.child = newGraph;\n        return newGraph;\n      } else {\n        //change the order of the parameters\n        targetNode = newEdge;\n        sourceNode = parentNode;\n        newEdge = newGraph;\n        var sourceGraph = sourceNode.getOwner();\n        var targetGraph = targetNode.getOwner();\n        if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n          throw \"Source not in this graph mgr!\";\n        }\n        if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n          throw \"Target not in this graph mgr!\";\n        }\n        if (sourceGraph == targetGraph) {\n          newEdge.isInterGraph = false;\n          return sourceGraph.add(newEdge, sourceNode, targetNode);\n        } else {\n          newEdge.isInterGraph = true;\n\n          // set source and target\n          newEdge.source = sourceNode;\n          newEdge.target = targetNode;\n\n          // add edge to inter-graph edge list\n          if (this.edges.indexOf(newEdge) > -1) {\n            throw \"Edge already in inter-graph edge list!\";\n          }\n          this.edges.push(newEdge);\n\n          // add edge to source and target incidency lists\n          if (!(newEdge.source != null && newEdge.target != null)) {\n            throw \"Edge source and/or target is null!\";\n          }\n          if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n            throw \"Edge already in source and/or target incidency list!\";\n          }\n          newEdge.source.edges.push(newEdge);\n          newEdge.target.edges.push(newEdge);\n          return newEdge;\n        }\n      }\n    };\n    LGraphManager.prototype.remove = function (lObj) {\n      if (lObj instanceof LGraph) {\n        var graph = lObj;\n        if (graph.getGraphManager() != this) {\n          throw \"Graph not in this graph mgr\";\n        }\n        if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n          throw \"Invalid parent node!\";\n        }\n\n        // first the edges (make a copy to do it safely)\n        var edgesToBeRemoved = [];\n        edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n        var edge;\n        var s = edgesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          edge = edgesToBeRemoved[i];\n          graph.remove(edge);\n        }\n\n        // then the nodes (make a copy to do it safely)\n        var nodesToBeRemoved = [];\n        nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n        var node;\n        s = nodesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          node = nodesToBeRemoved[i];\n          graph.remove(node);\n        }\n\n        // check if graph is the root\n        if (graph == this.rootGraph) {\n          this.setRootGraph(null);\n        }\n\n        // now remove the graph itself\n        var index = this.graphs.indexOf(graph);\n        this.graphs.splice(index, 1);\n\n        // also reset the parent of the graph\n        graph.parent = null;\n      } else if (lObj instanceof LEdge) {\n        edge = lObj;\n        if (edge == null) {\n          throw \"Edge is null!\";\n        }\n        if (!edge.isInterGraph) {\n          throw \"Not an inter-graph edge!\";\n        }\n        if (!(edge.source != null && edge.target != null)) {\n          throw \"Source and/or target is null!\";\n        }\n\n        // remove edge from source and target nodes' incidency lists\n\n        if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n          throw \"Source and/or target doesn't know this edge!\";\n        }\n        var index = edge.source.edges.indexOf(edge);\n        edge.source.edges.splice(index, 1);\n        index = edge.target.edges.indexOf(edge);\n        edge.target.edges.splice(index, 1);\n\n        // remove edge from owner graph manager's inter-graph edge list\n\n        if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n          throw \"Edge owner graph or owner graph manager is null!\";\n        }\n        if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n          throw \"Not in owner graph manager's edge list!\";\n        }\n        var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n        edge.source.owner.getGraphManager().edges.splice(index, 1);\n      }\n    };\n    LGraphManager.prototype.updateBounds = function () {\n      this.rootGraph.updateBounds(true);\n    };\n    LGraphManager.prototype.getGraphs = function () {\n      return this.graphs;\n    };\n    LGraphManager.prototype.getAllNodes = function () {\n      if (this.allNodes == null) {\n        var nodeList = [];\n        var graphs = this.getGraphs();\n        var s = graphs.length;\n        for (var i = 0; i < s; i++) {\n          nodeList = nodeList.concat(graphs[i].getNodes());\n        }\n        this.allNodes = nodeList;\n      }\n      return this.allNodes;\n    };\n    LGraphManager.prototype.resetAllNodes = function () {\n      this.allNodes = null;\n    };\n    LGraphManager.prototype.resetAllEdges = function () {\n      this.allEdges = null;\n    };\n    LGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n      this.allNodesToApplyGravitation = null;\n    };\n    LGraphManager.prototype.getAllEdges = function () {\n      if (this.allEdges == null) {\n        var edgeList = [];\n        var graphs = this.getGraphs();\n        var s = graphs.length;\n        for (var i = 0; i < graphs.length; i++) {\n          edgeList = edgeList.concat(graphs[i].getEdges());\n        }\n        edgeList = edgeList.concat(this.edges);\n        this.allEdges = edgeList;\n      }\n      return this.allEdges;\n    };\n    LGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n      return this.allNodesToApplyGravitation;\n    };\n    LGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n      if (this.allNodesToApplyGravitation != null) {\n        throw \"assert failed\";\n      }\n      this.allNodesToApplyGravitation = nodeList;\n    };\n    LGraphManager.prototype.getRoot = function () {\n      return this.rootGraph;\n    };\n    LGraphManager.prototype.setRootGraph = function (graph) {\n      if (graph.getGraphManager() != this) {\n        throw \"Root not in this graph mgr!\";\n      }\n      this.rootGraph = graph;\n      // root graph must have a root node associated with it for convenience\n      if (graph.parent == null) {\n        graph.parent = this.layout.newNode(\"Root node\");\n      }\n    };\n    LGraphManager.prototype.getLayout = function () {\n      return this.layout;\n    };\n    LGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n      if (!(firstNode != null && secondNode != null)) {\n        throw \"assert failed\";\n      }\n      if (firstNode == secondNode) {\n        return true;\n      }\n      // Is second node an ancestor of the first one?\n      var ownerGraph = firstNode.getOwner();\n      var parentNode;\n      do {\n        parentNode = ownerGraph.getParent();\n        if (parentNode == null) {\n          break;\n        }\n        if (parentNode == secondNode) {\n          return true;\n        }\n        ownerGraph = parentNode.getOwner();\n        if (ownerGraph == null) {\n          break;\n        }\n      } while (true);\n      // Is first node an ancestor of the second one?\n      ownerGraph = secondNode.getOwner();\n      do {\n        parentNode = ownerGraph.getParent();\n        if (parentNode == null) {\n          break;\n        }\n        if (parentNode == firstNode) {\n          return true;\n        }\n        ownerGraph = parentNode.getOwner();\n        if (ownerGraph == null) {\n          break;\n        }\n      } while (true);\n      return false;\n    };\n    LGraphManager.prototype.calcLowestCommonAncestors = function () {\n      var edge;\n      var sourceNode;\n      var targetNode;\n      var sourceAncestorGraph;\n      var targetAncestorGraph;\n      var edges = this.getAllEdges();\n      var s = edges.length;\n      for (var i = 0; i < s; i++) {\n        edge = edges[i];\n        sourceNode = edge.source;\n        targetNode = edge.target;\n        edge.lca = null;\n        edge.sourceInLca = sourceNode;\n        edge.targetInLca = targetNode;\n        if (sourceNode == targetNode) {\n          edge.lca = sourceNode.getOwner();\n          continue;\n        }\n        sourceAncestorGraph = sourceNode.getOwner();\n        while (edge.lca == null) {\n          edge.targetInLca = targetNode;\n          targetAncestorGraph = targetNode.getOwner();\n          while (edge.lca == null) {\n            if (targetAncestorGraph == sourceAncestorGraph) {\n              edge.lca = targetAncestorGraph;\n              break;\n            }\n            if (targetAncestorGraph == this.rootGraph) {\n              break;\n            }\n            if (edge.lca != null) {\n              throw \"assert failed\";\n            }\n            edge.targetInLca = targetAncestorGraph.getParent();\n            targetAncestorGraph = edge.targetInLca.getOwner();\n          }\n          if (sourceAncestorGraph == this.rootGraph) {\n            break;\n          }\n          if (edge.lca == null) {\n            edge.sourceInLca = sourceAncestorGraph.getParent();\n            sourceAncestorGraph = edge.sourceInLca.getOwner();\n          }\n        }\n        if (edge.lca == null) {\n          throw \"assert failed\";\n        }\n      }\n    };\n    LGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n      if (firstNode == secondNode) {\n        return firstNode.getOwner();\n      }\n      var firstOwnerGraph = firstNode.getOwner();\n      do {\n        if (firstOwnerGraph == null) {\n          break;\n        }\n        var secondOwnerGraph = secondNode.getOwner();\n        do {\n          if (secondOwnerGraph == null) {\n            break;\n          }\n          if (secondOwnerGraph == firstOwnerGraph) {\n            return secondOwnerGraph;\n          }\n          secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n        } while (true);\n        firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n      } while (true);\n      return firstOwnerGraph;\n    };\n    LGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n      if (graph == null && depth == null) {\n        graph = this.rootGraph;\n        depth = 1;\n      }\n      var node;\n      var nodes = graph.getNodes();\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        node = nodes[i];\n        node.inclusionTreeDepth = depth;\n        if (node.child != null) {\n          this.calcInclusionTreeDepths(node.child, depth + 1);\n        }\n      }\n    };\n    LGraphManager.prototype.includesInvalidEdge = function () {\n      var edge;\n      var s = this.edges.length;\n      for (var i = 0; i < s; i++) {\n        edge = this.edges[i];\n        if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    module.exports = LGraphManager;\n\n    /***/\n  }), (/* 7 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LayoutConstants = __webpack_require__(0);\n    function FDLayoutConstants() {}\n\n    //FDLayoutConstants inherits static props in LayoutConstants\n    for (var prop in LayoutConstants) {\n      FDLayoutConstants[prop] = LayoutConstants[prop];\n    }\n    FDLayoutConstants.MAX_ITERATIONS = 2500;\n    FDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\n    FDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\n    FDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\n    FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\n    FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\n    FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\n    FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\n    FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\n    FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\n    FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\n    FDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\n    FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\n    FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\n    FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\n    FDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\n    FDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\n    FDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\n    FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\n    FDLayoutConstants.MIN_EDGE_LENGTH = 1;\n    FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n    module.exports = FDLayoutConstants;\n\n    /***/\n  }), (/* 8 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    /**\n     * This class maintains a list of static geometry related utility methods.\n     *\n     *\n     * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n     */\n    var Point = __webpack_require__(12);\n    function IGeometry() {}\n\n    /**\n     * This method calculates *half* the amount in x and y directions of the two\n     * input rectangles needed to separate them keeping their respective\n     * positioning, and returns the result in the input array. An input\n     * separation buffer added to the amount in both directions. We assume that\n     * the two rectangles do intersect.\n     */\n    IGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n      if (!rectA.intersects(rectB)) {\n        throw \"assert failed\";\n      }\n      var directions = new Array(2);\n      this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n      overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n      overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n      // update the overlapping amounts for the following cases:\n      if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n        /* Case x.1:\n        *\n        * rectA\n        * \t|                       |\n        * \t|        _________      |\n        * \t|        |       |      |\n        * \t|________|_______|______|\n        * \t\t\t |       |\n        *           |       |\n        *        rectB\n        */\n        overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n      } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n        /* Case x.2:\n        *\n        * rectB\n        * \t|                       |\n        * \t|        _________      |\n        * \t|        |       |      |\n        * \t|________|_______|______|\n        * \t\t\t |       |\n        *           |       |\n        *        rectA\n        */\n        overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n      }\n      if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n        /* Case y.1:\n         *          ________ rectA\n         *         |\n         *         |\n         *   ______|____  rectB\n         *         |    |\n         *         |    |\n         *   ______|____|\n         *         |\n         *         |\n         *         |________\n         *\n         */\n        overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n      } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n        /* Case y.2:\n        *          ________ rectB\n        *         |\n        *         |\n        *   ______|____  rectA\n        *         |    |\n        *         |    |\n        *   ______|____|\n        *         |\n        *         |\n        *         |________\n        *\n        */\n        overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n      }\n\n      // find slope of the line passes two centers\n      var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n      // if centers are overlapped\n      if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n        // assume the slope is 1 (45 degree)\n        slope = 1.0;\n      }\n      var moveByY = slope * overlapAmount[0];\n      var moveByX = overlapAmount[1] / slope;\n      if (overlapAmount[0] < moveByX) {\n        moveByX = overlapAmount[0];\n      } else {\n        moveByY = overlapAmount[1];\n      }\n      // return half the amount so that if each rectangle is moved by these\n      // amounts in opposite directions, overlap will be resolved\n      overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n      overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n    };\n\n    /**\n     * This method decides the separation direction of overlapping nodes\n     *\n     * if directions[0] = -1, then rectA goes left\n     * if directions[0] = 1,  then rectA goes right\n     * if directions[1] = -1, then rectA goes up\n     * if directions[1] = 1,  then rectA goes down\n     */\n    IGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n      if (rectA.getCenterX() < rectB.getCenterX()) {\n        directions[0] = -1;\n      } else {\n        directions[0] = 1;\n      }\n      if (rectA.getCenterY() < rectB.getCenterY()) {\n        directions[1] = -1;\n      } else {\n        directions[1] = 1;\n      }\n    };\n\n    /**\n     * This method calculates the intersection (clipping) points of the two\n     * input rectangles with line segment defined by the centers of these two\n     * rectangles. The clipping points are saved in the input double array and\n     * whether or not the two rectangles overlap is returned.\n     */\n    IGeometry.getIntersection2 = function (rectA, rectB, result) {\n      //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n      var p1x = rectA.getCenterX();\n      var p1y = rectA.getCenterY();\n      var p2x = rectB.getCenterX();\n      var p2y = rectB.getCenterY();\n\n      //if two rectangles intersect, then clipping points are centers\n      if (rectA.intersects(rectB)) {\n        result[0] = p1x;\n        result[1] = p1y;\n        result[2] = p2x;\n        result[3] = p2y;\n        return true;\n      }\n      //variables for rectA\n      var topLeftAx = rectA.getX();\n      var topLeftAy = rectA.getY();\n      var topRightAx = rectA.getRight();\n      var bottomLeftAx = rectA.getX();\n      var bottomLeftAy = rectA.getBottom();\n      var bottomRightAx = rectA.getRight();\n      var halfWidthA = rectA.getWidthHalf();\n      var halfHeightA = rectA.getHeightHalf();\n      //variables for rectB\n      var topLeftBx = rectB.getX();\n      var topLeftBy = rectB.getY();\n      var topRightBx = rectB.getRight();\n      var bottomLeftBx = rectB.getX();\n      var bottomLeftBy = rectB.getBottom();\n      var bottomRightBx = rectB.getRight();\n      var halfWidthB = rectB.getWidthHalf();\n      var halfHeightB = rectB.getHeightHalf();\n\n      //flag whether clipping points are found\n      var clipPointAFound = false;\n      var clipPointBFound = false;\n\n      // line is vertical\n      if (p1x === p2x) {\n        if (p1y > p2y) {\n          result[0] = p1x;\n          result[1] = topLeftAy;\n          result[2] = p2x;\n          result[3] = bottomLeftBy;\n          return false;\n        } else if (p1y < p2y) {\n          result[0] = p1x;\n          result[1] = bottomLeftAy;\n          result[2] = p2x;\n          result[3] = topLeftBy;\n          return false;\n        } else {\n          //not line, return null;\n        }\n      }\n      // line is horizontal\n      else if (p1y === p2y) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = p1y;\n          result[2] = topRightBx;\n          result[3] = p2y;\n          return false;\n        } else if (p1x < p2x) {\n          result[0] = topRightAx;\n          result[1] = p1y;\n          result[2] = topLeftBx;\n          result[3] = p2y;\n          return false;\n        } else {\n          //not valid line, return null;\n        }\n      } else {\n        //slopes of rectA's and rectB's diagonals\n        var slopeA = rectA.height / rectA.width;\n        var slopeB = rectB.height / rectB.width;\n\n        //slope of line between center of rectA and center of rectB\n        var slopePrime = (p2y - p1y) / (p2x - p1x);\n        var cardinalDirectionA = void 0;\n        var cardinalDirectionB = void 0;\n        var tempPointAx = void 0;\n        var tempPointAy = void 0;\n        var tempPointBx = void 0;\n        var tempPointBy = void 0;\n\n        //determine whether clipping point is the corner of nodeA\n        if (-slopeA === slopePrime) {\n          if (p1x > p2x) {\n            result[0] = bottomLeftAx;\n            result[1] = bottomLeftAy;\n            clipPointAFound = true;\n          } else {\n            result[0] = topRightAx;\n            result[1] = topLeftAy;\n            clipPointAFound = true;\n          }\n        } else if (slopeA === slopePrime) {\n          if (p1x > p2x) {\n            result[0] = topLeftAx;\n            result[1] = topLeftAy;\n            clipPointAFound = true;\n          } else {\n            result[0] = bottomRightAx;\n            result[1] = bottomLeftAy;\n            clipPointAFound = true;\n          }\n        }\n\n        //determine whether clipping point is the corner of nodeB\n        if (-slopeB === slopePrime) {\n          if (p2x > p1x) {\n            result[2] = bottomLeftBx;\n            result[3] = bottomLeftBy;\n            clipPointBFound = true;\n          } else {\n            result[2] = topRightBx;\n            result[3] = topLeftBy;\n            clipPointBFound = true;\n          }\n        } else if (slopeB === slopePrime) {\n          if (p2x > p1x) {\n            result[2] = topLeftBx;\n            result[3] = topLeftBy;\n            clipPointBFound = true;\n          } else {\n            result[2] = bottomRightBx;\n            result[3] = bottomLeftBy;\n            clipPointBFound = true;\n          }\n        }\n\n        //if both clipping points are corners\n        if (clipPointAFound && clipPointBFound) {\n          return false;\n        }\n\n        //determine Cardinal Direction of rectangles\n        if (p1x > p2x) {\n          if (p1y > p2y) {\n            cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n            cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n          } else {\n            cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n            cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n          }\n        } else {\n          if (p1y > p2y) {\n            cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n            cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n          } else {\n            cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n            cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n          }\n        }\n        //calculate clipping Point if it is not found before\n        if (!clipPointAFound) {\n          switch (cardinalDirectionA) {\n            case 1:\n              tempPointAy = topLeftAy;\n              tempPointAx = p1x + -halfHeightA / slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 2:\n              tempPointAx = bottomRightAx;\n              tempPointAy = p1y + halfWidthA * slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 3:\n              tempPointAy = bottomLeftAy;\n              tempPointAx = p1x + halfHeightA / slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 4:\n              tempPointAx = bottomLeftAx;\n              tempPointAy = p1y + -halfWidthA * slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n          }\n        }\n        if (!clipPointBFound) {\n          switch (cardinalDirectionB) {\n            case 1:\n              tempPointBy = topLeftBy;\n              tempPointBx = p2x + -halfHeightB / slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 2:\n              tempPointBx = bottomRightBx;\n              tempPointBy = p2y + halfWidthB * slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 3:\n              tempPointBy = bottomLeftBy;\n              tempPointBx = p2x + halfHeightB / slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 4:\n              tempPointBx = bottomLeftBx;\n              tempPointBy = p2y + -halfWidthB * slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n          }\n        }\n      }\n      return false;\n    };\n\n    /**\n     * This method returns in which cardinal direction does input point stays\n     * 1: North\n     * 2: East\n     * 3: South\n     * 4: West\n     */\n    IGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n      if (slope > slopePrime) {\n        return line;\n      } else {\n        return 1 + line % 4;\n      }\n    };\n\n    /**\n     * This method calculates the intersection of the two lines defined by\n     * point pairs (s1,s2) and (f1,f2).\n     */\n    IGeometry.getIntersection = function (s1, s2, f1, f2) {\n      if (f2 == null) {\n        return this.getIntersection2(s1, s2, f1);\n      }\n      var x1 = s1.x;\n      var y1 = s1.y;\n      var x2 = s2.x;\n      var y2 = s2.y;\n      var x3 = f1.x;\n      var y3 = f1.y;\n      var x4 = f2.x;\n      var y4 = f2.y;\n      var x = void 0,\n        y = void 0; // intersection point\n      var a1 = void 0,\n        a2 = void 0,\n        b1 = void 0,\n        b2 = void 0,\n        c1 = void 0,\n        c2 = void 0; // coefficients of line eqns.\n      var denom = void 0;\n      a1 = y2 - y1;\n      b1 = x1 - x2;\n      c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n      a2 = y4 - y3;\n      b2 = x3 - x4;\n      c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n      denom = a1 * b2 - a2 * b1;\n      if (denom === 0) {\n        return null;\n      }\n      x = (b1 * c2 - b2 * c1) / denom;\n      y = (a2 * c1 - a1 * c2) / denom;\n      return new Point(x, y);\n    };\n\n    /**\n     * This method finds and returns the angle of the vector from the + x-axis\n     * in clockwise direction (compatible w/ Java coordinate system!).\n     */\n    IGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n      var C_angle = void 0;\n      if (Cx !== Nx) {\n        C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n        if (Nx < Cx) {\n          C_angle += Math.PI;\n        } else if (Ny < Cy) {\n          C_angle += this.TWO_PI;\n        }\n      } else if (Ny < Cy) {\n        C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n      } else {\n        C_angle = this.HALF_PI; // 90 degrees\n      }\n      return C_angle;\n    };\n\n    /**\n     * This method checks whether the given two line segments (one with point\n     * p1 and p2, the other with point p3 and p4) intersect at a point other\n     * than these points.\n     */\n    IGeometry.doIntersect = function (p1, p2, p3, p4) {\n      var a = p1.x;\n      var b = p1.y;\n      var c = p2.x;\n      var d = p2.y;\n      var p = p3.x;\n      var q = p3.y;\n      var r = p4.x;\n      var s = p4.y;\n      var det = (c - a) * (s - q) - (r - p) * (d - b);\n      if (det === 0) {\n        return false;\n      } else {\n        var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n        var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n        return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n      }\n    };\n\n    // -----------------------------------------------------------------------------\n    // Section: Class Constants\n    // -----------------------------------------------------------------------------\n    /**\n     * Some useful pre-calculated constants\n     */\n    IGeometry.HALF_PI = 0.5 * Math.PI;\n    IGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\n    IGeometry.TWO_PI = 2.0 * Math.PI;\n    IGeometry.THREE_PI = 3.0 * Math.PI;\n    module.exports = IGeometry;\n\n    /***/\n  }), (/* 9 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function IMath() {}\n\n    /**\n     * This method returns the sign of the input value.\n     */\n    IMath.sign = function (value) {\n      if (value > 0) {\n        return 1;\n      } else if (value < 0) {\n        return -1;\n      } else {\n        return 0;\n      }\n    };\n    IMath.floor = function (value) {\n      return value < 0 ? Math.ceil(value) : Math.floor(value);\n    };\n    IMath.ceil = function (value) {\n      return value < 0 ? Math.floor(value) : Math.ceil(value);\n    };\n    module.exports = IMath;\n\n    /***/\n  }), (/* 10 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function Integer() {}\n    Integer.MAX_VALUE = 2147483647;\n    Integer.MIN_VALUE = -2147483648;\n    module.exports = Integer;\n\n    /***/\n  }), (/* 11 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var nodeFrom = function nodeFrom(value) {\n      return {\n        value: value,\n        next: null,\n        prev: null\n      };\n    };\n    var add = function add(prev, node, next, list) {\n      if (prev !== null) {\n        prev.next = node;\n      } else {\n        list.head = node;\n      }\n      if (next !== null) {\n        next.prev = node;\n      } else {\n        list.tail = node;\n      }\n      node.prev = prev;\n      node.next = next;\n      list.length++;\n      return node;\n    };\n    var _remove = function _remove(node, list) {\n      var prev = node.prev,\n        next = node.next;\n      if (prev !== null) {\n        prev.next = next;\n      } else {\n        list.head = next;\n      }\n      if (next !== null) {\n        next.prev = prev;\n      } else {\n        list.tail = prev;\n      }\n      node.prev = node.next = null;\n      list.length--;\n      return node;\n    };\n    var LinkedList = function () {\n      function LinkedList(vals) {\n        var _this = this;\n        _classCallCheck(this, LinkedList);\n        this.length = 0;\n        this.head = null;\n        this.tail = null;\n        if (vals != null) {\n          vals.forEach(function (v) {\n            return _this.push(v);\n          });\n        }\n      }\n      _createClass(LinkedList, [{\n        key: \"size\",\n        value: function size() {\n          return this.length;\n        }\n      }, {\n        key: \"insertBefore\",\n        value: function insertBefore(val, otherNode) {\n          return add(otherNode.prev, nodeFrom(val), otherNode, this);\n        }\n      }, {\n        key: \"insertAfter\",\n        value: function insertAfter(val, otherNode) {\n          return add(otherNode, nodeFrom(val), otherNode.next, this);\n        }\n      }, {\n        key: \"insertNodeBefore\",\n        value: function insertNodeBefore(newNode, otherNode) {\n          return add(otherNode.prev, newNode, otherNode, this);\n        }\n      }, {\n        key: \"insertNodeAfter\",\n        value: function insertNodeAfter(newNode, otherNode) {\n          return add(otherNode, newNode, otherNode.next, this);\n        }\n      }, {\n        key: \"push\",\n        value: function push(val) {\n          return add(this.tail, nodeFrom(val), null, this);\n        }\n      }, {\n        key: \"unshift\",\n        value: function unshift(val) {\n          return add(null, nodeFrom(val), this.head, this);\n        }\n      }, {\n        key: \"remove\",\n        value: function remove(node) {\n          return _remove(node, this);\n        }\n      }, {\n        key: \"pop\",\n        value: function pop() {\n          return _remove(this.tail, this).value;\n        }\n      }, {\n        key: \"popNode\",\n        value: function popNode() {\n          return _remove(this.tail, this);\n        }\n      }, {\n        key: \"shift\",\n        value: function shift() {\n          return _remove(this.head, this).value;\n        }\n      }, {\n        key: \"shiftNode\",\n        value: function shiftNode() {\n          return _remove(this.head, this);\n        }\n      }, {\n        key: \"get_object_at\",\n        value: function get_object_at(index) {\n          if (index <= this.length()) {\n            var i = 1;\n            var current = this.head;\n            while (i < index) {\n              current = current.next;\n              i++;\n            }\n            return current.value;\n          }\n        }\n      }, {\n        key: \"set_object_at\",\n        value: function set_object_at(index, value) {\n          if (index <= this.length()) {\n            var i = 1;\n            var current = this.head;\n            while (i < index) {\n              current = current.next;\n              i++;\n            }\n            current.value = value;\n          }\n        }\n      }]);\n      return LinkedList;\n    }();\n    module.exports = LinkedList;\n\n    /***/\n  }), (/* 12 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    /*\r\n     *This class is the javascript implementation of the Point.java class in jdk\r\n     */\n    function Point(x, y, p) {\n      this.x = null;\n      this.y = null;\n      if (x == null && y == null && p == null) {\n        this.x = 0;\n        this.y = 0;\n      } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n        this.x = x;\n        this.y = y;\n      } else if (x.constructor.name == 'Point' && y == null && p == null) {\n        p = x;\n        this.x = p.x;\n        this.y = p.y;\n      }\n    }\n    Point.prototype.getX = function () {\n      return this.x;\n    };\n    Point.prototype.getY = function () {\n      return this.y;\n    };\n    Point.prototype.getLocation = function () {\n      return new Point(this.x, this.y);\n    };\n    Point.prototype.setLocation = function (x, y, p) {\n      if (x.constructor.name == 'Point' && y == null && p == null) {\n        p = x;\n        this.setLocation(p.x, p.y);\n      } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n        //if both parameters are integer just move (x,y) location\n        if (parseInt(x) == x && parseInt(y) == y) {\n          this.move(x, y);\n        } else {\n          this.x = Math.floor(x + 0.5);\n          this.y = Math.floor(y + 0.5);\n        }\n      }\n    };\n    Point.prototype.move = function (x, y) {\n      this.x = x;\n      this.y = y;\n    };\n    Point.prototype.translate = function (dx, dy) {\n      this.x += dx;\n      this.y += dy;\n    };\n    Point.prototype.equals = function (obj) {\n      if (obj.constructor.name == \"Point\") {\n        var pt = obj;\n        return this.x == pt.x && this.y == pt.y;\n      }\n      return this == obj;\n    };\n    Point.prototype.toString = function () {\n      return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n    };\n    module.exports = Point;\n\n    /***/\n  }), (/* 13 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function RectangleD(x, y, width, height) {\n      this.x = 0;\n      this.y = 0;\n      this.width = 0;\n      this.height = 0;\n      if (x != null && y != null && width != null && height != null) {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n      }\n    }\n    RectangleD.prototype.getX = function () {\n      return this.x;\n    };\n    RectangleD.prototype.setX = function (x) {\n      this.x = x;\n    };\n    RectangleD.prototype.getY = function () {\n      return this.y;\n    };\n    RectangleD.prototype.setY = function (y) {\n      this.y = y;\n    };\n    RectangleD.prototype.getWidth = function () {\n      return this.width;\n    };\n    RectangleD.prototype.setWidth = function (width) {\n      this.width = width;\n    };\n    RectangleD.prototype.getHeight = function () {\n      return this.height;\n    };\n    RectangleD.prototype.setHeight = function (height) {\n      this.height = height;\n    };\n    RectangleD.prototype.getRight = function () {\n      return this.x + this.width;\n    };\n    RectangleD.prototype.getBottom = function () {\n      return this.y + this.height;\n    };\n    RectangleD.prototype.intersects = function (a) {\n      if (this.getRight() < a.x) {\n        return false;\n      }\n      if (this.getBottom() < a.y) {\n        return false;\n      }\n      if (a.getRight() < this.x) {\n        return false;\n      }\n      if (a.getBottom() < this.y) {\n        return false;\n      }\n      return true;\n    };\n    RectangleD.prototype.getCenterX = function () {\n      return this.x + this.width / 2;\n    };\n    RectangleD.prototype.getMinX = function () {\n      return this.getX();\n    };\n    RectangleD.prototype.getMaxX = function () {\n      return this.getX() + this.width;\n    };\n    RectangleD.prototype.getCenterY = function () {\n      return this.y + this.height / 2;\n    };\n    RectangleD.prototype.getMinY = function () {\n      return this.getY();\n    };\n    RectangleD.prototype.getMaxY = function () {\n      return this.getY() + this.height;\n    };\n    RectangleD.prototype.getWidthHalf = function () {\n      return this.width / 2;\n    };\n    RectangleD.prototype.getHeightHalf = function () {\n      return this.height / 2;\n    };\n    module.exports = RectangleD;\n\n    /***/\n  }), (/* 14 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    function UniqueIDGeneretor() {}\n    UniqueIDGeneretor.lastID = 0;\n    UniqueIDGeneretor.createID = function (obj) {\n      if (UniqueIDGeneretor.isPrimitive(obj)) {\n        return obj;\n      }\n      if (obj.uniqueID != null) {\n        return obj.uniqueID;\n      }\n      obj.uniqueID = UniqueIDGeneretor.getString();\n      UniqueIDGeneretor.lastID++;\n      return obj.uniqueID;\n    };\n    UniqueIDGeneretor.getString = function (id) {\n      if (id == null) id = UniqueIDGeneretor.lastID;\n      return \"Object#\" + id + \"\";\n    };\n    UniqueIDGeneretor.isPrimitive = function (arg) {\n      var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n      return arg == null || type != \"object\" && type != \"function\";\n    };\n    module.exports = UniqueIDGeneretor;\n\n    /***/\n  }), (/* 15 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function _toConsumableArray(arr) {\n      if (Array.isArray(arr)) {\n        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      } else {\n        return Array.from(arr);\n      }\n    }\n    var LayoutConstants = __webpack_require__(0);\n    var LGraphManager = __webpack_require__(6);\n    var LNode = __webpack_require__(3);\n    var LEdge = __webpack_require__(1);\n    var LGraph = __webpack_require__(5);\n    var PointD = __webpack_require__(4);\n    var Transform = __webpack_require__(17);\n    var Emitter = __webpack_require__(27);\n    function Layout(isRemoteUse) {\n      Emitter.call(this);\n\n      //Layout Quality: 0:draft, 1:default, 2:proof\n      this.layoutQuality = LayoutConstants.QUALITY;\n      //Whether layout should create bendpoints as needed or not\n      this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n      //Whether layout should be incremental or not\n      this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n      //Whether we animate from before to after layout node positions\n      this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n      //Whether we animate the layout process or not\n      this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n      //Number iterations that should be done between two successive animations\n      this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n      /**\r\n       * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n       * they are, both spring and repulsion forces between two leaf nodes can be\r\n       * calculated without the expensive clipping point calculations, resulting\r\n       * in major speed-up.\r\n       */\n      this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n      /**\r\n       * This is used for creation of bendpoints by using dummy nodes and edges.\r\n       * Maps an LEdge to its dummy bendpoint path.\r\n       */\n      this.edgeToDummyNodes = new Map();\n      this.graphManager = new LGraphManager(this);\n      this.isLayoutFinished = false;\n      this.isSubLayout = false;\n      this.isRemoteUse = false;\n      if (isRemoteUse != null) {\n        this.isRemoteUse = isRemoteUse;\n      }\n    }\n    Layout.RANDOM_SEED = 1;\n    Layout.prototype = Object.create(Emitter.prototype);\n    Layout.prototype.getGraphManager = function () {\n      return this.graphManager;\n    };\n    Layout.prototype.getAllNodes = function () {\n      return this.graphManager.getAllNodes();\n    };\n    Layout.prototype.getAllEdges = function () {\n      return this.graphManager.getAllEdges();\n    };\n    Layout.prototype.getAllNodesToApplyGravitation = function () {\n      return this.graphManager.getAllNodesToApplyGravitation();\n    };\n    Layout.prototype.newGraphManager = function () {\n      var gm = new LGraphManager(this);\n      this.graphManager = gm;\n      return gm;\n    };\n    Layout.prototype.newGraph = function (vGraph) {\n      return new LGraph(null, this.graphManager, vGraph);\n    };\n    Layout.prototype.newNode = function (vNode) {\n      return new LNode(this.graphManager, vNode);\n    };\n    Layout.prototype.newEdge = function (vEdge) {\n      return new LEdge(null, null, vEdge);\n    };\n    Layout.prototype.checkLayoutSuccess = function () {\n      return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n    };\n    Layout.prototype.runLayout = function () {\n      this.isLayoutFinished = false;\n      if (this.tilingPreLayout) {\n        this.tilingPreLayout();\n      }\n      this.initParameters();\n      var isLayoutSuccessfull;\n      if (this.checkLayoutSuccess()) {\n        isLayoutSuccessfull = false;\n      } else {\n        isLayoutSuccessfull = this.layout();\n      }\n      if (LayoutConstants.ANIMATE === 'during') {\n        // If this is a 'during' layout animation. Layout is not finished yet. \n        // We need to perform these in index.js when layout is really finished.\n        return false;\n      }\n      if (isLayoutSuccessfull) {\n        if (!this.isSubLayout) {\n          this.doPostLayout();\n        }\n      }\n      if (this.tilingPostLayout) {\n        this.tilingPostLayout();\n      }\n      this.isLayoutFinished = true;\n      return isLayoutSuccessfull;\n    };\n\n    /**\r\n     * This method performs the operations required after layout.\r\n     */\n    Layout.prototype.doPostLayout = function () {\n      //assert !isSubLayout : \"Should not be called on sub-layout!\";\n      // Propagate geometric changes to v-level objects\n      if (!this.incremental) {\n        this.transform();\n      }\n      this.update();\n    };\n\n    /**\r\n     * This method updates the geometry of the target graph according to\r\n     * calculated layout.\r\n     */\n    Layout.prototype.update2 = function () {\n      // update bend points\n      if (this.createBendsAsNeeded) {\n        this.createBendpointsFromDummyNodes();\n\n        // reset all edges, since the topology has changed\n        this.graphManager.resetAllEdges();\n      }\n\n      // perform edge, node and root updates if layout is not called\n      // remotely\n      if (!this.isRemoteUse) {\n        // update all edges\n        var edge;\n        var allEdges = this.graphManager.getAllEdges();\n        for (var i = 0; i < allEdges.length; i++) {\n          edge = allEdges[i];\n          //      this.update(edge);\n        }\n\n        // recursively update nodes\n        var node;\n        var nodes = this.graphManager.getRoot().getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          node = nodes[i];\n          //      this.update(node);\n        }\n\n        // update root graph\n        this.update(this.graphManager.getRoot());\n      }\n    };\n    Layout.prototype.update = function (obj) {\n      if (obj == null) {\n        this.update2();\n      } else if (obj instanceof LNode) {\n        var node = obj;\n        if (node.getChild() != null) {\n          // since node is compound, recursively update child nodes\n          var nodes = node.getChild().getNodes();\n          for (var i = 0; i < nodes.length; i++) {\n            update(nodes[i]);\n          }\n        }\n\n        // if the l-level node is associated with a v-level graph object,\n        // then it is assumed that the v-level node implements the\n        // interface Updatable.\n        if (node.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vNode = node.vGraphObject;\n\n          // call the update method of the interface\n          vNode.update(node);\n        }\n      } else if (obj instanceof LEdge) {\n        var edge = obj;\n        // if the l-level edge is associated with a v-level graph object,\n        // then it is assumed that the v-level edge implements the\n        // interface Updatable.\n\n        if (edge.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vEdge = edge.vGraphObject;\n\n          // call the update method of the interface\n          vEdge.update(edge);\n        }\n      } else if (obj instanceof LGraph) {\n        var graph = obj;\n        // if the l-level graph is associated with a v-level graph object,\n        // then it is assumed that the v-level object implements the\n        // interface Updatable.\n\n        if (graph.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vGraph = graph.vGraphObject;\n\n          // call the update method of the interface\n          vGraph.update(graph);\n        }\n      }\n    };\n\n    /**\r\n     * This method is used to set all layout parameters to default values\r\n     * determined at compile time.\r\n     */\n    Layout.prototype.initParameters = function () {\n      if (!this.isSubLayout) {\n        this.layoutQuality = LayoutConstants.QUALITY;\n        this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n        this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n        this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n        this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n        this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n        this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n      }\n      if (this.animationDuringLayout) {\n        this.animationOnLayout = false;\n      }\n    };\n    Layout.prototype.transform = function (newLeftTop) {\n      if (newLeftTop == undefined) {\n        this.transform(new PointD(0, 0));\n      } else {\n        // create a transformation object (from Eclipse to layout). When an\n        // inverse transform is applied, we get upper-left coordinate of the\n        // drawing or the root graph at given input coordinate (some margins\n        // already included in calculation of left-top).\n\n        var trans = new Transform();\n        var leftTop = this.graphManager.getRoot().updateLeftTop();\n        if (leftTop != null) {\n          trans.setWorldOrgX(newLeftTop.x);\n          trans.setWorldOrgY(newLeftTop.y);\n          trans.setDeviceOrgX(leftTop.x);\n          trans.setDeviceOrgY(leftTop.y);\n          var nodes = this.getAllNodes();\n          var node;\n          for (var i = 0; i < nodes.length; i++) {\n            node = nodes[i];\n            node.transform(trans);\n          }\n        }\n      }\n    };\n    Layout.prototype.positionNodesRandomly = function (graph) {\n      if (graph == undefined) {\n        //assert !this.incremental;\n        this.positionNodesRandomly(this.getGraphManager().getRoot());\n        this.getGraphManager().getRoot().updateBounds(true);\n      } else {\n        var lNode;\n        var childGraph;\n        var nodes = graph.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          lNode = nodes[i];\n          childGraph = lNode.getChild();\n          if (childGraph == null) {\n            lNode.scatter();\n          } else if (childGraph.getNodes().length == 0) {\n            lNode.scatter();\n          } else {\n            this.positionNodesRandomly(childGraph);\n            lNode.updateBounds();\n          }\n        }\n      }\n    };\n\n    /**\r\n     * This method returns a list of trees where each tree is represented as a\r\n     * list of l-nodes. The method returns a list of size 0 when:\r\n     * - The graph is not flat or\r\n     * - One of the component(s) of the graph is not a tree.\r\n     */\n    Layout.prototype.getFlatForest = function () {\n      var flatForest = [];\n      var isForest = true;\n\n      // Quick reference for all nodes in the graph manager associated with\n      // this layout. The list should not be changed.\n      var allNodes = this.graphManager.getRoot().getNodes();\n\n      // First be sure that the graph is flat\n      var isFlat = true;\n      for (var i = 0; i < allNodes.length; i++) {\n        if (allNodes[i].getChild() != null) {\n          isFlat = false;\n        }\n      }\n\n      // Return empty forest if the graph is not flat.\n      if (!isFlat) {\n        return flatForest;\n      }\n\n      // Run BFS for each component of the graph.\n\n      var visited = new Set();\n      var toBeVisited = [];\n      var parents = new Map();\n      var unProcessedNodes = [];\n      unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n      // Each iteration of this loop finds a component of the graph and\n      // decides whether it is a tree or not. If it is a tree, adds it to the\n      // forest and continued with the next component.\n\n      while (unProcessedNodes.length > 0 && isForest) {\n        toBeVisited.push(unProcessedNodes[0]);\n\n        // Start the BFS. Each iteration of this loop visits a node in a\n        // BFS manner.\n        while (toBeVisited.length > 0 && isForest) {\n          //pool operation\n          var currentNode = toBeVisited[0];\n          toBeVisited.splice(0, 1);\n          visited.add(currentNode);\n\n          // Traverse all neighbors of this node\n          var neighborEdges = currentNode.getEdges();\n          for (var i = 0; i < neighborEdges.length; i++) {\n            var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n            // If BFS is not growing from this neighbor.\n            if (parents.get(currentNode) != currentNeighbor) {\n              // We haven't previously visited this neighbor.\n              if (!visited.has(currentNeighbor)) {\n                toBeVisited.push(currentNeighbor);\n                parents.set(currentNeighbor, currentNode);\n              }\n              // Since we have previously visited this neighbor and\n              // this neighbor is not parent of currentNode, given\n              // graph contains a component that is not tree, hence\n              // it is not a forest.\n              else {\n                isForest = false;\n                break;\n              }\n            }\n          }\n        }\n\n        // The graph contains a component that is not a tree. Empty\n        // previously found trees. The method will end.\n        if (!isForest) {\n          flatForest = [];\n        }\n        // Save currently visited nodes as a tree in our forest. Reset\n        // visited and parents lists. Continue with the next component of\n        // the graph, if any.\n        else {\n          var temp = [].concat(_toConsumableArray(visited));\n          flatForest.push(temp);\n          //flatForest = flatForest.concat(temp);\n          //unProcessedNodes.removeAll(visited);\n          for (var i = 0; i < temp.length; i++) {\n            var value = temp[i];\n            var index = unProcessedNodes.indexOf(value);\n            if (index > -1) {\n              unProcessedNodes.splice(index, 1);\n            }\n          }\n          visited = new Set();\n          parents = new Map();\n        }\n      }\n      return flatForest;\n    };\n\n    /**\r\n     * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n     * for the given edge (one per bendpoint). The existing l-level structure\r\n     * is updated accordingly.\r\n     */\n    Layout.prototype.createDummyNodesForBendpoints = function (edge) {\n      var dummyNodes = [];\n      var prev = edge.source;\n      var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n      for (var i = 0; i < edge.bendpoints.length; i++) {\n        // create new dummy node\n        var dummyNode = this.newNode(null);\n        dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n        graph.add(dummyNode);\n\n        // create new dummy edge between prev and dummy node\n        var dummyEdge = this.newEdge(null);\n        this.graphManager.add(dummyEdge, prev, dummyNode);\n        dummyNodes.add(dummyNode);\n        prev = dummyNode;\n      }\n      var dummyEdge = this.newEdge(null);\n      this.graphManager.add(dummyEdge, prev, edge.target);\n      this.edgeToDummyNodes.set(edge, dummyNodes);\n\n      // remove real edge from graph manager if it is inter-graph\n      if (edge.isInterGraph()) {\n        this.graphManager.remove(edge);\n      }\n      // else, remove the edge from the current graph\n      else {\n        graph.remove(edge);\n      }\n      return dummyNodes;\n    };\n\n    /**\r\n     * This method creates bendpoints for edges from the dummy nodes\r\n     * at l-level.\r\n     */\n    Layout.prototype.createBendpointsFromDummyNodes = function () {\n      var edges = [];\n      edges = edges.concat(this.graphManager.getAllEdges());\n      edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n      for (var k = 0; k < edges.length; k++) {\n        var lEdge = edges[k];\n        if (lEdge.bendpoints.length > 0) {\n          var path = this.edgeToDummyNodes.get(lEdge);\n          for (var i = 0; i < path.length; i++) {\n            var dummyNode = path[i];\n            var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n            // update bendpoint's location according to dummy node\n            var ebp = lEdge.bendpoints.get(i);\n            ebp.x = p.x;\n            ebp.y = p.y;\n\n            // remove the dummy node, dummy edges incident with this\n            // dummy node is also removed (within the remove method)\n            dummyNode.getOwner().remove(dummyNode);\n          }\n\n          // add the real edge to graph\n          this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n        }\n      }\n    };\n    Layout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n      if (minDiv != undefined && maxMul != undefined) {\n        var value = defaultValue;\n        if (sliderValue <= 50) {\n          var minValue = defaultValue / minDiv;\n          value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n        } else {\n          var maxValue = defaultValue * maxMul;\n          value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n        }\n        return value;\n      } else {\n        var a, b;\n        if (sliderValue <= 50) {\n          a = 9.0 * defaultValue / 500.0;\n          b = defaultValue / 10.0;\n        } else {\n          a = 9.0 * defaultValue / 50.0;\n          b = -8 * defaultValue;\n        }\n        return a * sliderValue + b;\n      }\n    };\n\n    /**\r\n     * This method finds and returns the center of the given nodes, assuming\r\n     * that the given nodes form a tree in themselves.\r\n     */\n    Layout.findCenterOfTree = function (nodes) {\n      var list = [];\n      list = list.concat(nodes);\n      var removedNodes = [];\n      var remainingDegrees = new Map();\n      var foundCenter = false;\n      var centerNode = null;\n      if (list.length == 1 || list.length == 2) {\n        foundCenter = true;\n        centerNode = list[0];\n      }\n      for (var i = 0; i < list.length; i++) {\n        var node = list[i];\n        var degree = node.getNeighborsList().size;\n        remainingDegrees.set(node, node.getNeighborsList().size);\n        if (degree == 1) {\n          removedNodes.push(node);\n        }\n      }\n      var tempList = [];\n      tempList = tempList.concat(removedNodes);\n      while (!foundCenter) {\n        var tempList2 = [];\n        tempList2 = tempList2.concat(tempList);\n        tempList = [];\n        for (var i = 0; i < list.length; i++) {\n          var node = list[i];\n          var index = list.indexOf(node);\n          if (index >= 0) {\n            list.splice(index, 1);\n          }\n          var neighbours = node.getNeighborsList();\n          neighbours.forEach(function (neighbour) {\n            if (removedNodes.indexOf(neighbour) < 0) {\n              var otherDegree = remainingDegrees.get(neighbour);\n              var newDegree = otherDegree - 1;\n              if (newDegree == 1) {\n                tempList.push(neighbour);\n              }\n              remainingDegrees.set(neighbour, newDegree);\n            }\n          });\n        }\n        removedNodes = removedNodes.concat(tempList);\n        if (list.length == 1 || list.length == 2) {\n          foundCenter = true;\n          centerNode = list[0];\n        }\n      }\n      return centerNode;\n    };\n\n    /**\r\n     * During the coarsening process, this layout may be referenced by two graph managers\r\n     * this setter function grants access to change the currently being used graph manager\r\n     */\n    Layout.prototype.setGraphManager = function (gm) {\n      this.graphManager = gm;\n    };\n    module.exports = Layout;\n\n    /***/\n  }), (/* 16 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function RandomSeed() {}\n    // adapted from: https://stackoverflow.com/a/19303725\n    RandomSeed.seed = 1;\n    RandomSeed.x = 0;\n    RandomSeed.nextDouble = function () {\n      RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n      return RandomSeed.x - Math.floor(RandomSeed.x);\n    };\n    module.exports = RandomSeed;\n\n    /***/\n  }), (/* 17 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var PointD = __webpack_require__(4);\n    function Transform(x, y) {\n      this.lworldOrgX = 0.0;\n      this.lworldOrgY = 0.0;\n      this.ldeviceOrgX = 0.0;\n      this.ldeviceOrgY = 0.0;\n      this.lworldExtX = 1.0;\n      this.lworldExtY = 1.0;\n      this.ldeviceExtX = 1.0;\n      this.ldeviceExtY = 1.0;\n    }\n    Transform.prototype.getWorldOrgX = function () {\n      return this.lworldOrgX;\n    };\n    Transform.prototype.setWorldOrgX = function (wox) {\n      this.lworldOrgX = wox;\n    };\n    Transform.prototype.getWorldOrgY = function () {\n      return this.lworldOrgY;\n    };\n    Transform.prototype.setWorldOrgY = function (woy) {\n      this.lworldOrgY = woy;\n    };\n    Transform.prototype.getWorldExtX = function () {\n      return this.lworldExtX;\n    };\n    Transform.prototype.setWorldExtX = function (wex) {\n      this.lworldExtX = wex;\n    };\n    Transform.prototype.getWorldExtY = function () {\n      return this.lworldExtY;\n    };\n    Transform.prototype.setWorldExtY = function (wey) {\n      this.lworldExtY = wey;\n    };\n\n    /* Device related */\n\n    Transform.prototype.getDeviceOrgX = function () {\n      return this.ldeviceOrgX;\n    };\n    Transform.prototype.setDeviceOrgX = function (dox) {\n      this.ldeviceOrgX = dox;\n    };\n    Transform.prototype.getDeviceOrgY = function () {\n      return this.ldeviceOrgY;\n    };\n    Transform.prototype.setDeviceOrgY = function (doy) {\n      this.ldeviceOrgY = doy;\n    };\n    Transform.prototype.getDeviceExtX = function () {\n      return this.ldeviceExtX;\n    };\n    Transform.prototype.setDeviceExtX = function (dex) {\n      this.ldeviceExtX = dex;\n    };\n    Transform.prototype.getDeviceExtY = function () {\n      return this.ldeviceExtY;\n    };\n    Transform.prototype.setDeviceExtY = function (dey) {\n      this.ldeviceExtY = dey;\n    };\n    Transform.prototype.transformX = function (x) {\n      var xDevice = 0.0;\n      var worldExtX = this.lworldExtX;\n      if (worldExtX != 0.0) {\n        xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n      }\n      return xDevice;\n    };\n    Transform.prototype.transformY = function (y) {\n      var yDevice = 0.0;\n      var worldExtY = this.lworldExtY;\n      if (worldExtY != 0.0) {\n        yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n      }\n      return yDevice;\n    };\n    Transform.prototype.inverseTransformX = function (x) {\n      var xWorld = 0.0;\n      var deviceExtX = this.ldeviceExtX;\n      if (deviceExtX != 0.0) {\n        xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n      }\n      return xWorld;\n    };\n    Transform.prototype.inverseTransformY = function (y) {\n      var yWorld = 0.0;\n      var deviceExtY = this.ldeviceExtY;\n      if (deviceExtY != 0.0) {\n        yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n      }\n      return yWorld;\n    };\n    Transform.prototype.inverseTransformPoint = function (inPoint) {\n      var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n      return outPoint;\n    };\n    module.exports = Transform;\n\n    /***/\n  }), (/* 18 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function _toConsumableArray(arr) {\n      if (Array.isArray(arr)) {\n        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      } else {\n        return Array.from(arr);\n      }\n    }\n    var Layout = __webpack_require__(15);\n    var FDLayoutConstants = __webpack_require__(7);\n    var LayoutConstants = __webpack_require__(0);\n    var IGeometry = __webpack_require__(8);\n    var IMath = __webpack_require__(9);\n    function FDLayout() {\n      Layout.call(this);\n      this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n      this.idealEdgeLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n      this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n      this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n      this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n      this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n      this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n      this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n      this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n      this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      this.totalDisplacement = 0.0;\n      this.oldTotalDisplacement = 0.0;\n      this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n    }\n    FDLayout.prototype = Object.create(Layout.prototype);\n    for (var prop in Layout) {\n      FDLayout[prop] = Layout[prop];\n    }\n    FDLayout.prototype.initParameters = function () {\n      Layout.prototype.initParameters.call(this, arguments);\n      this.totalIterations = 0;\n      this.notAnimatedIterations = 0;\n      this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n      this.grid = [];\n    };\n    FDLayout.prototype.calcIdealEdgeLengths = function () {\n      var edge;\n      var lcaDepth;\n      var source;\n      var target;\n      var sizeOfSourceInLca;\n      var sizeOfTargetInLca;\n      var allEdges = this.getGraphManager().getAllEdges();\n      for (var i = 0; i < allEdges.length; i++) {\n        edge = allEdges[i];\n        edge.idealLength = this.idealEdgeLength;\n        if (edge.isInterGraph) {\n          source = edge.getSource();\n          target = edge.getTarget();\n          sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n          sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n          if (this.useSmartIdealEdgeLengthCalculation) {\n            edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n          }\n          lcaDepth = edge.getLca().getInclusionTreeDepth();\n          edge.idealLength += FDLayoutConstants.DEFAULT_EDGE_LENGTH * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n        }\n      }\n    };\n    FDLayout.prototype.initSpringEmbedder = function () {\n      var s = this.getAllNodes().length;\n      if (this.incremental) {\n        if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n          this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n        }\n        this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n      } else {\n        if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n          this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n        } else {\n          this.coolingFactor = 1.0;\n        }\n        this.initialCoolingFactor = this.coolingFactor;\n        this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n      }\n      this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n      this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n      this.repulsionRange = this.calcRepulsionRange();\n    };\n    FDLayout.prototype.calcSpringForces = function () {\n      var lEdges = this.getAllEdges();\n      var edge;\n      for (var i = 0; i < lEdges.length; i++) {\n        edge = lEdges[i];\n        this.calcSpringForce(edge, edge.idealLength);\n      }\n    };\n    FDLayout.prototype.calcRepulsionForces = function () {\n      var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var i, j;\n      var nodeA, nodeB;\n      var lNodes = this.getAllNodes();\n      var processedNodeSet;\n      if (this.useFRGridVariant) {\n        if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n          this.updateGrid();\n        }\n        processedNodeSet = new Set();\n\n        // calculate repulsion forces between each nodes and its surrounding\n        for (i = 0; i < lNodes.length; i++) {\n          nodeA = lNodes[i];\n          this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n          processedNodeSet.add(nodeA);\n        }\n      } else {\n        for (i = 0; i < lNodes.length; i++) {\n          nodeA = lNodes[i];\n          for (j = i + 1; j < lNodes.length; j++) {\n            nodeB = lNodes[j];\n\n            // If both nodes are not members of the same graph, skip.\n            if (nodeA.getOwner() != nodeB.getOwner()) {\n              continue;\n            }\n            this.calcRepulsionForce(nodeA, nodeB);\n          }\n        }\n      }\n    };\n    FDLayout.prototype.calcGravitationalForces = function () {\n      var node;\n      var lNodes = this.getAllNodesToApplyGravitation();\n      for (var i = 0; i < lNodes.length; i++) {\n        node = lNodes[i];\n        this.calcGravitationalForce(node);\n      }\n    };\n    FDLayout.prototype.moveNodes = function () {\n      var lNodes = this.getAllNodes();\n      var node;\n      for (var i = 0; i < lNodes.length; i++) {\n        node = lNodes[i];\n        node.move();\n      }\n    };\n    FDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n      var sourceNode = edge.getSource();\n      var targetNode = edge.getTarget();\n      var length;\n      var springForce;\n      var springForceX;\n      var springForceY;\n\n      // Update edge length\n      if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n        edge.updateLengthSimple();\n      } else {\n        edge.updateLength();\n        if (edge.isOverlapingSourceAndTarget) {\n          return;\n        }\n      }\n      length = edge.getLength();\n      if (length == 0) return;\n\n      // Calculate spring forces\n      springForce = this.springConstant * (length - idealLength);\n\n      // Project force onto x and y axes\n      springForceX = springForce * (edge.lengthX / length);\n      springForceY = springForce * (edge.lengthY / length);\n\n      // Apply forces on the end nodes\n      sourceNode.springForceX += springForceX;\n      sourceNode.springForceY += springForceY;\n      targetNode.springForceX -= springForceX;\n      targetNode.springForceY -= springForceY;\n    };\n    FDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n      var rectA = nodeA.getRect();\n      var rectB = nodeB.getRect();\n      var overlapAmount = new Array(2);\n      var clipPoints = new Array(4);\n      var distanceX;\n      var distanceY;\n      var distanceSquared;\n      var distance;\n      var repulsionForce;\n      var repulsionForceX;\n      var repulsionForceY;\n      if (rectA.intersects(rectB))\n        // two nodes overlap\n        {\n          // calculate separation amount in x and y directions\n          IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n          repulsionForceX = 2 * overlapAmount[0];\n          repulsionForceY = 2 * overlapAmount[1];\n          var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n          // Apply forces on the two nodes\n          nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n          nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n          nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n          nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n        } else\n        // no overlap\n        {\n          // calculate distance\n\n          if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null)\n            // simply base repulsion on distance of node centers\n            {\n              distanceX = rectB.getCenterX() - rectA.getCenterX();\n              distanceY = rectB.getCenterY() - rectA.getCenterY();\n            } else\n            // use clipping points\n            {\n              IGeometry.getIntersection(rectA, rectB, clipPoints);\n              distanceX = clipPoints[2] - clipPoints[0];\n              distanceY = clipPoints[3] - clipPoints[1];\n            }\n\n          // No repulsion range. FR grid variant should take care of this.\n          if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n            distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n          }\n          if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n            distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n          }\n          distanceSquared = distanceX * distanceX + distanceY * distanceY;\n          distance = Math.sqrt(distanceSquared);\n          repulsionForce = this.repulsionConstant * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n          // Project force onto x and y axes\n          repulsionForceX = repulsionForce * distanceX / distance;\n          repulsionForceY = repulsionForce * distanceY / distance;\n\n          // Apply forces on the two nodes    \n          nodeA.repulsionForceX -= repulsionForceX;\n          nodeA.repulsionForceY -= repulsionForceY;\n          nodeB.repulsionForceX += repulsionForceX;\n          nodeB.repulsionForceY += repulsionForceY;\n        }\n    };\n    FDLayout.prototype.calcGravitationalForce = function (node) {\n      var ownerGraph;\n      var ownerCenterX;\n      var ownerCenterY;\n      var distanceX;\n      var distanceY;\n      var absDistanceX;\n      var absDistanceY;\n      var estimatedSize;\n      ownerGraph = node.getOwner();\n      ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n      ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n      distanceX = node.getCenterX() - ownerCenterX;\n      distanceY = node.getCenterY() - ownerCenterY;\n      absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n      absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n      if (node.getOwner() == this.graphManager.getRoot())\n        // in the root graph\n        {\n          estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n          if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n            node.gravitationForceX = -this.gravityConstant * distanceX;\n            node.gravitationForceY = -this.gravityConstant * distanceY;\n          }\n        } else\n        // inside a compound\n        {\n          estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n          if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n            node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n            node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n          }\n        }\n    };\n    FDLayout.prototype.isConverged = function () {\n      var converged;\n      var oscilating = false;\n      if (this.totalIterations > this.maxIterations / 3) {\n        oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n      }\n      converged = this.totalDisplacement < this.totalDisplacementThreshold;\n      this.oldTotalDisplacement = this.totalDisplacement;\n      return converged || oscilating;\n    };\n    FDLayout.prototype.animate = function () {\n      if (this.animationDuringLayout && !this.isSubLayout) {\n        if (this.notAnimatedIterations == this.animationPeriod) {\n          this.update();\n          this.notAnimatedIterations = 0;\n        } else {\n          this.notAnimatedIterations++;\n        }\n      }\n    };\n\n    //This method calculates the number of children (weight) for all nodes\n    FDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n      var node;\n      var allNodes = this.graphManager.getAllNodes();\n      for (var i = 0; i < allNodes.length; i++) {\n        node = allNodes[i];\n        node.noOfChildren = node.getNoOfChildren();\n      }\n    };\n\n    // -----------------------------------------------------------------------------\n    // Section: FR-Grid Variant Repulsion Force Calculation\n    // -----------------------------------------------------------------------------\n\n    FDLayout.prototype.calcGrid = function (graph) {\n      var sizeX = 0;\n      var sizeY = 0;\n      sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n      sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n      var grid = new Array(sizeX);\n      for (var i = 0; i < sizeX; i++) {\n        grid[i] = new Array(sizeY);\n      }\n      for (var i = 0; i < sizeX; i++) {\n        for (var j = 0; j < sizeY; j++) {\n          grid[i][j] = new Array();\n        }\n      }\n      return grid;\n    };\n    FDLayout.prototype.addNodeToGrid = function (v, left, top) {\n      var startX = 0;\n      var finishX = 0;\n      var startY = 0;\n      var finishY = 0;\n      startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n      finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n      startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n      finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n      for (var i = startX; i <= finishX; i++) {\n        for (var j = startY; j <= finishY; j++) {\n          this.grid[i][j].push(v);\n          v.setGridCoordinates(startX, finishX, startY, finishY);\n        }\n      }\n    };\n    FDLayout.prototype.updateGrid = function () {\n      var i;\n      var nodeA;\n      var lNodes = this.getAllNodes();\n      this.grid = this.calcGrid(this.graphManager.getRoot());\n\n      // put all nodes to proper grid cells\n      for (i = 0; i < lNodes.length; i++) {\n        nodeA = lNodes[i];\n        this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n      }\n    };\n    FDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n      if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n        var surrounding = new Set();\n        nodeA.surrounding = new Array();\n        var nodeB;\n        var grid = this.grid;\n        for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n          for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n            if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n              for (var k = 0; k < grid[i][j].length; k++) {\n                nodeB = grid[i][j][k];\n\n                // If both nodes are not members of the same graph, \n                // or both nodes are the same, skip.\n                if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n                  continue;\n                }\n\n                // check if the repulsion force between\n                // nodeA and nodeB has already been calculated\n                if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n                  var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n                  var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n                  // if the distance between nodeA and nodeB \n                  // is less then calculation range\n                  if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                    //then add nodeB to surrounding of nodeA\n                    surrounding.add(nodeB);\n                  }\n                }\n              }\n            }\n          }\n        }\n        nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n      }\n      for (i = 0; i < nodeA.surrounding.length; i++) {\n        this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n      }\n    };\n    FDLayout.prototype.calcRepulsionRange = function () {\n      return 0.0;\n    };\n    module.exports = FDLayout;\n\n    /***/\n  }), (/* 19 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LEdge = __webpack_require__(1);\n    var FDLayoutConstants = __webpack_require__(7);\n    function FDLayoutEdge(source, target, vEdge) {\n      LEdge.call(this, source, target, vEdge);\n      this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n    }\n    FDLayoutEdge.prototype = Object.create(LEdge.prototype);\n    for (var prop in LEdge) {\n      FDLayoutEdge[prop] = LEdge[prop];\n    }\n    module.exports = FDLayoutEdge;\n\n    /***/\n  }), (/* 20 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LNode = __webpack_require__(3);\n    function FDLayoutNode(gm, loc, size, vNode) {\n      // alternative constructor is handled inside LNode\n      LNode.call(this, gm, loc, size, vNode);\n      //Spring, repulsion and gravitational forces acting on this node\n      this.springForceX = 0;\n      this.springForceY = 0;\n      this.repulsionForceX = 0;\n      this.repulsionForceY = 0;\n      this.gravitationForceX = 0;\n      this.gravitationForceY = 0;\n      //Amount by which this node is to be moved in this iteration\n      this.displacementX = 0;\n      this.displacementY = 0;\n\n      //Start and finish grid coordinates that this node is fallen into\n      this.startX = 0;\n      this.finishX = 0;\n      this.startY = 0;\n      this.finishY = 0;\n\n      //Geometric neighbors of this node\n      this.surrounding = [];\n    }\n    FDLayoutNode.prototype = Object.create(LNode.prototype);\n    for (var prop in LNode) {\n      FDLayoutNode[prop] = LNode[prop];\n    }\n    FDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n      this.startX = _startX;\n      this.finishX = _finishX;\n      this.startY = _startY;\n      this.finishY = _finishY;\n    };\n    module.exports = FDLayoutNode;\n\n    /***/\n  }), (/* 21 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function DimensionD(width, height) {\n      this.width = 0;\n      this.height = 0;\n      if (width !== null && height !== null) {\n        this.height = height;\n        this.width = width;\n      }\n    }\n    DimensionD.prototype.getWidth = function () {\n      return this.width;\n    };\n    DimensionD.prototype.setWidth = function (width) {\n      this.width = width;\n    };\n    DimensionD.prototype.getHeight = function () {\n      return this.height;\n    };\n    DimensionD.prototype.setHeight = function (height) {\n      this.height = height;\n    };\n    module.exports = DimensionD;\n\n    /***/\n  }), (/* 22 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var UniqueIDGeneretor = __webpack_require__(14);\n    function HashMap() {\n      this.map = {};\n      this.keys = [];\n    }\n    HashMap.prototype.put = function (key, value) {\n      var theId = UniqueIDGeneretor.createID(key);\n      if (!this.contains(theId)) {\n        this.map[theId] = value;\n        this.keys.push(key);\n      }\n    };\n    HashMap.prototype.contains = function (key) {\n      var theId = UniqueIDGeneretor.createID(key);\n      return this.map[key] != null;\n    };\n    HashMap.prototype.get = function (key) {\n      var theId = UniqueIDGeneretor.createID(key);\n      return this.map[theId];\n    };\n    HashMap.prototype.keySet = function () {\n      return this.keys;\n    };\n    module.exports = HashMap;\n\n    /***/\n  }), (/* 23 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var UniqueIDGeneretor = __webpack_require__(14);\n    function HashSet() {\n      this.set = {};\n    }\n    ;\n    HashSet.prototype.add = function (obj) {\n      var theId = UniqueIDGeneretor.createID(obj);\n      if (!this.contains(theId)) this.set[theId] = obj;\n    };\n    HashSet.prototype.remove = function (obj) {\n      delete this.set[UniqueIDGeneretor.createID(obj)];\n    };\n    HashSet.prototype.clear = function () {\n      this.set = {};\n    };\n    HashSet.prototype.contains = function (obj) {\n      return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n    };\n    HashSet.prototype.isEmpty = function () {\n      return this.size() === 0;\n    };\n    HashSet.prototype.size = function () {\n      return Object.keys(this.set).length;\n    };\n\n    //concats this.set to the given list\n    HashSet.prototype.addAllTo = function (list) {\n      var keys = Object.keys(this.set);\n      var length = keys.length;\n      for (var i = 0; i < length; i++) {\n        list.push(this.set[keys[i]]);\n      }\n    };\n    HashSet.prototype.size = function () {\n      return Object.keys(this.set).length;\n    };\n    HashSet.prototype.addAll = function (list) {\n      var s = list.length;\n      for (var i = 0; i < s; i++) {\n        var v = list[i];\n        this.add(v);\n      }\n    };\n    module.exports = HashSet;\n\n    /***/\n  }), (/* 24 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n\n    /**\n     * A classic Quicksort algorithm with Hoare's partition\n     * - Works also on LinkedList objects\n     *\n     * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n     */\n\n    var LinkedList = __webpack_require__(11);\n    var Quicksort = function () {\n      function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n        this._quicksort(A, 0, length - 1);\n      }\n      _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n          if (p < r) {\n            var q = this._partition(A, p, r);\n            this._quicksort(A, p, q);\n            this._quicksort(A, q + 1, r);\n          }\n        }\n      }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n          var x = this._get(A, p);\n          var i = p;\n          var j = r;\n          while (true) {\n            while (this.compareFunction(x, this._get(A, j))) {\n              j--;\n            }\n            while (this.compareFunction(this._get(A, i), x)) {\n              i++;\n            }\n            if (i < j) {\n              this._swap(A, i, j);\n              i++;\n              j--;\n            } else return j;\n          }\n        }\n      }, {\n        key: '_get',\n        value: function _get(object, index) {\n          if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n      }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n          if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n      }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n          var temp = this._get(A, i);\n          this._set(A, i, this._get(A, j));\n          this._set(A, j, temp);\n        }\n      }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n          return b > a;\n        }\n      }]);\n      return Quicksort;\n    }();\n    module.exports = Quicksort;\n\n    /***/\n  }), (/* 25 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n\n    /**\n     *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n     *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n     *\n     *   Aside from the inputs, you can assign the scores for,\n     *   - Match: The two characters at the current index are same.\n     *   - Mismatch: The two characters at the current index are different.\n     *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n     */\n\n    var NeedlemanWunsch = function () {\n      function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n        _classCallCheck(this, NeedlemanWunsch);\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n          this.grid[i] = new Array(this.jMax);\n          for (var j = 0; j < this.jMax; j++) {\n            this.grid[i][j] = 0;\n          }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n          this.tracebackGrid[_i] = new Array(this.jMax);\n          for (var _j = 0; _j < this.jMax; _j++) {\n            this.tracebackGrid[_i][_j] = [null, null, null];\n          }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n      }\n      _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n          return this.score;\n        }\n      }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n          return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n      }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n          // Fill in the first row\n          for (var j = 1; j < this.jMax; j++) {\n            this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n            this.tracebackGrid[0][j] = [false, false, true];\n          }\n\n          // Fill in the first column\n          for (var i = 1; i < this.iMax; i++) {\n            this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n            this.tracebackGrid[i][0] = [false, true, false];\n          }\n\n          // Fill the rest of the grid\n          for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n            for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n              // Find the max score(s) among [`Diag`, `Up`, `Left`]\n              var diag = void 0;\n              if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n              var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n              var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n              // If there exists multiple max values, capture them for multiple paths\n              var maxOf = [diag, up, left];\n              var indices = this.arrayAllMaxIndexes(maxOf);\n\n              // Update Grids\n              this.grid[_i2][_j2] = maxOf[indices[0]];\n              this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n            }\n          }\n\n          // Update alignment score\n          this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n      }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n          var inProcessAlignments = [];\n          inProcessAlignments.push({\n            pos: [this.sequence1.length, this.sequence2.length],\n            seq1: \"\",\n            seq2: \"\"\n          });\n          while (inProcessAlignments[0]) {\n            var current = inProcessAlignments[0];\n            var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n            if (directions[0]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0] - 1, current.pos[1] - 1],\n                seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n              });\n            }\n            if (directions[1]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0] - 1, current.pos[1]],\n                seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                seq2: '-' + current.seq2\n              });\n            }\n            if (directions[2]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0], current.pos[1] - 1],\n                seq1: '-' + current.seq1,\n                seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n              });\n            }\n            if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({\n              sequence1: current.seq1,\n              sequence2: current.seq2\n            });\n            inProcessAlignments.shift();\n          }\n          return this.alignments;\n        }\n\n        // Helper Functions\n      }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n          var indexes = [],\n            i = -1;\n          while ((i = arr.indexOf(val, i + 1)) !== -1) {\n            indexes.push(i);\n          }\n          return indexes;\n        }\n      }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n          return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n      }]);\n      return NeedlemanWunsch;\n    }();\n    module.exports = NeedlemanWunsch;\n\n    /***/\n  }), (/* 26 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var layoutBase = function layoutBase() {\n      return;\n    };\n    layoutBase.FDLayout = __webpack_require__(18);\n    layoutBase.FDLayoutConstants = __webpack_require__(7);\n    layoutBase.FDLayoutEdge = __webpack_require__(19);\n    layoutBase.FDLayoutNode = __webpack_require__(20);\n    layoutBase.DimensionD = __webpack_require__(21);\n    layoutBase.HashMap = __webpack_require__(22);\n    layoutBase.HashSet = __webpack_require__(23);\n    layoutBase.IGeometry = __webpack_require__(8);\n    layoutBase.IMath = __webpack_require__(9);\n    layoutBase.Integer = __webpack_require__(10);\n    layoutBase.Point = __webpack_require__(12);\n    layoutBase.PointD = __webpack_require__(4);\n    layoutBase.RandomSeed = __webpack_require__(16);\n    layoutBase.RectangleD = __webpack_require__(13);\n    layoutBase.Transform = __webpack_require__(17);\n    layoutBase.UniqueIDGeneretor = __webpack_require__(14);\n    layoutBase.Quicksort = __webpack_require__(24);\n    layoutBase.LinkedList = __webpack_require__(11);\n    layoutBase.LGraphObject = __webpack_require__(2);\n    layoutBase.LGraph = __webpack_require__(5);\n    layoutBase.LEdge = __webpack_require__(1);\n    layoutBase.LGraphManager = __webpack_require__(6);\n    layoutBase.LNode = __webpack_require__(3);\n    layoutBase.Layout = __webpack_require__(15);\n    layoutBase.LayoutConstants = __webpack_require__(0);\n    layoutBase.NeedlemanWunsch = __webpack_require__(25);\n    module.exports = layoutBase;\n\n    /***/\n  }), (/* 27 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function Emitter() {\n      this.listeners = [];\n    }\n    var p = Emitter.prototype;\n    p.addListener = function (event, callback) {\n      this.listeners.push({\n        event: event,\n        callback: callback\n      });\n    };\n    p.removeListener = function (event, callback) {\n      for (var i = this.listeners.length; i >= 0; i--) {\n        var l = this.listeners[i];\n        if (l.event === event && l.callback === callback) {\n          this.listeners.splice(i, 1);\n        }\n      }\n    };\n    p.emit = function (event, data) {\n      for (var i = 0; i < this.listeners.length; i++) {\n        var l = this.listeners[i];\n        if (event === l.event) {\n          l.callback(data);\n        }\n      }\n    };\n    module.exports = Emitter;\n\n    /***/\n  }\n  /******/)]);\n});", "map": {"version": 3, "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "define", "amd", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "value", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "LayoutConstants", "QUALITY", "DEFAULT_CREATE_BENDS_AS_NEEDED", "DEFAULT_INCREMENTAL", "DEFAULT_ANIMATION_ON_LAYOUT", "DEFAULT_ANIMATION_DURING_LAYOUT", "DEFAULT_ANIMATION_PERIOD", "DEFAULT_UNIFORM_LEAF_NODE_SIZES", "DEFAULT_GRAPH_MARGIN", "NODE_DIMENSIONS_INCLUDE_LABELS", "SIMPLE_NODE_SIZE", "SIMPLE_NODE_HALF_SIZE", "EMPTY_COMPOUND_NODE_SIZE", "MIN_EDGE_LENGTH", "WORLD_BOUNDARY", "INITIAL_WORLD_BOUNDARY", "WORLD_CENTER_X", "WORLD_CENTER_Y", "LGraphObject", "IGeometry", "IMath", "<PERSON><PERSON><PERSON>", "source", "target", "vEdge", "isOverlapingSourceAndTarget", "vGraphObject", "bendpoints", "create", "prop", "getSource", "get<PERSON><PERSON><PERSON>", "isInterGraph", "<PERSON><PERSON><PERSON><PERSON>", "length", "getBendpoints", "getLca", "lca", "getSourceInLca", "sourceInLca", "getTargetInLca", "targetInLca", "getOtherEnd", "node", "getOtherEndInGraph", "graph", "otherEnd", "getGraphManager", "getRoot", "get<PERSON>wner", "getParent", "updateLength", "clipPointCoordinates", "Array", "getIntersection", "getRect", "lengthX", "lengthY", "Math", "abs", "sign", "sqrt", "updateLengthSimple", "getCenterX", "getCenterY", "Integer", "RectangleD", "RandomSeed", "PointD", "LNode", "gm", "loc", "size", "vNode", "graphManager", "estimatedSize", "MIN_VALUE", "inclusion<PERSON><PERSON><PERSON><PERSON><PERSON>", "MAX_VALUE", "edges", "rect", "x", "y", "width", "height", "get<PERSON>dges", "<PERSON><PERSON><PERSON><PERSON>", "child", "owner", "getWidth", "<PERSON><PERSON><PERSON><PERSON>", "getHeight", "setHeight", "getCenter", "getLocation", "getDiagonal", "getHalfTheDiagonal", "setRect", "upperLeft", "dimension", "setCenter", "cx", "cy", "setLocation", "moveBy", "dx", "dy", "getEdgeListToNode", "to", "edgeList", "edge", "self", "for<PERSON>ach", "push", "getEdgesBetween", "other", "getNeighborsList", "neighbors", "Set", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withNeighborsList", "childNode", "children", "nodes", "getNodes", "getNoOfChildren", "noOf<PERSON><PERSON><PERSON><PERSON>", "getEstimatedSize", "calcEstimatedSize", "scatter", "randomCenterX", "randomCenterY", "minX", "maxX", "nextDouble", "minY", "maxY", "updateBounds", "childGraph", "getLeft", "getTop", "getRight", "getBottom", "labelWidth", "labelHeight", "labelPos", "getInclusionTreeDepth", "transform", "trans", "left", "top", "leftTop", "vLeftTop", "inverseTransformPoint", "getX", "getY", "setX", "setY", "getDifference", "pt", "DimensionD", "getCopy", "translate", "dim", "LGraphManager", "Point", "LinkedList", "LGraph", "parent", "obj2", "vGraph", "margin", "isConnected", "Layout", "right", "bottom", "obj1", "sourceNode", "targetNode", "newNode", "indexOf", "newEdge", "remove", "obj", "edgesToBeRemoved", "slice", "index", "splice", "sourceIndex", "targetIndex", "updateLeftTop", "nodeTop", "nodeLeft", "lNode", "paddingLeft", "undefined", "recursive", "nodeRight", "nodeBottom", "boundingRect", "calculateBounds", "updateConnected", "queue", "visited", "currentNode", "neighborEdges", "currentNeighbor", "childrenOfNode", "shift", "neighborEdge", "has", "childrenOfNeighbor", "noOfVisitedInThisGraph", "visitedNode", "layout", "graphs", "addRoot", "ngraph", "newGraph", "nnode", "setRootGraph", "rootGraph", "parentNode", "sourceGraph", "targetGraph", "lObj", "concat", "nodesToBeRemoved", "getGraphs", "getAllNodes", "allNodes", "nodeList", "resetAllNodes", "resetAll<PERSON>dges", "allEdges", "resetAllNodesToApplyGravitation", "allNodesToApplyGravitation", "getAllEdges", "getAllNodesToApplyGravitation", "setAllNodesToApplyGravitation", "getLayout", "isOneAncestorOfOther", "firstNode", "secondNode", "ownerGraph", "calcLowestCommonAncestors", "sourceAncestorGraph", "targetAncestorGraph", "calcLowestCommonAncestor", "firstOwnerGraph", "secondOwnerGraph", "calcInclusionTreeDepths", "depth", "includesInvalidEdge", "FDLayoutConstants", "MAX_ITERATIONS", "DEFAULT_EDGE_LENGTH", "DEFAULT_SPRING_STRENGTH", "DEFAULT_REPULSION_STRENGTH", "DEFAULT_GRAVITY_STRENGTH", "DEFAULT_COMPOUND_GRAVITY_STRENGTH", "DEFAULT_GRAVITY_RANGE_FACTOR", "DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR", "DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION", "DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION", "DEFAULT_COOLING_FACTOR_INCREMENTAL", "COOLING_ADAPTATION_FACTOR", "ADAPTATION_LOWER_NODE_LIMIT", "ADAPTATION_UPPER_NODE_LIMIT", "MAX_NODE_DISPLACEMENT_INCREMENTAL", "MAX_NODE_DISPLACEMENT", "MIN_REPULSION_DIST", "CONVERGENCE_CHECK_PERIOD", "PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR", "GRID_CALCULATION_CHECK_PERIOD", "calcSeparationAmount", "rectA", "rectB", "overlapAmount", "<PERSON><PERSON><PERSON><PERSON>", "intersects", "directions", "decideDirectionsForOverlappingNodes", "min", "max", "slope", "moveByY", "moveByX", "getIntersection2", "result", "p1x", "p1y", "p2x", "p2y", "topLeftAx", "topLeftAy", "topRightAx", "bottomLeftAx", "bottomLeftAy", "bottomRightAx", "halfWidthA", "getWidthHalf", "halfHeightA", "getHeightHalf", "topLeftBx", "topLeftBy", "topRightBx", "bottomLeftBx", "bottomLeftBy", "bottomRightBx", "halfWidthB", "halfHeightB", "clipPointAFound", "clipPointBFound", "slopeA", "slopeB", "slopePrime", "cardinalDirectionA", "cardinalDirectionB", "tempPointAx", "tempPointAy", "tempPointBx", "tempPointBy", "getCardinalDirection", "line", "s1", "s2", "f1", "f2", "x1", "y1", "x2", "y2", "x3", "y3", "x4", "y4", "a1", "a2", "b1", "b2", "c1", "c2", "denom", "angleOfVector", "Cx", "Cy", "Nx", "Ny", "C_angle", "atan", "PI", "TWO_PI", "ONE_AND_HALF_PI", "HALF_PI", "doIntersect", "p1", "p2", "p3", "p4", "a", "b", "q", "r", "det", "lambda", "gamma", "THREE_PI", "floor", "ceil", "_createClass", "defineProperties", "props", "descriptor", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "TypeError", "nodeFrom", "next", "prev", "list", "head", "tail", "_remove", "vals", "_this", "v", "insertBefore", "val", "otherNode", "insertAfter", "insertNodeBefore", "insertNodeAfter", "unshift", "pop", "popNode", "shiftNode", "get_object_at", "current", "set_object_at", "constructor", "parseInt", "move", "equals", "toString", "getMinX", "getMaxX", "getMinY", "getMaxY", "_typeof", "Symbol", "iterator", "UniqueIDGeneretor", "lastID", "createID", "isPrimitive", "uniqueID", "getString", "id", "arg", "type", "_toConsumableArray", "arr", "isArray", "arr2", "from", "Transform", "Emitter", "isRemoteUse", "layoutQuality", "createBendsAsNeeded", "incremental", "animationOnLayout", "animationDuringLayout", "animationPeriod", "uniformLeafNodeSizes", "edgeToDummyNodes", "Map", "isLayoutFinished", "isSubLayout", "RANDOM_SEED", "newGraphManager", "checkLayoutSuccess", "runLayout", "tilingPreLayout", "initParameters", "isLayoutSuccessfull", "ANIMATE", "doPostLayout", "tilingPostLayout", "update", "update2", "createBendpointsFromDummyNodes", "newLeftTop", "setWorldOrgX", "setWorldOrgY", "setDeviceOrgX", "setDeviceOrgY", "positionNodesRandomly", "getFlatForest", "flatForest", "isForest", "is<PERSON><PERSON>", "toBeVisited", "parents", "unProcessedNodes", "set", "temp", "createDummyNodesForBendpoints", "dummyNodes", "dummy<PERSON>ode", "Dimension", "dummy<PERSON><PERSON>", "keys", "k", "lEdge", "path", "ebp", "slider<PERSON><PERSON><PERSON>", "defaultValue", "minDiv", "maxMul", "minValue", "maxValue", "findCenterOfTree", "removedNodes", "remainingDegrees", "foundCenter", "centerNode", "degree", "tempList", "tempList2", "neighbours", "neighbour", "otherDegree", "newDegree", "setGraphManager", "seed", "sin", "lworldOrgX", "lworldOrgY", "ldeviceOrgX", "ldeviceOrgY", "lworldExtX", "lworldExtY", "ldeviceExtX", "ldeviceExtY", "getWorldOrgX", "wox", "getWorldOrgY", "woy", "getWorldExtX", "setWorldExtX", "wex", "getWorldExtY", "setWorldExtY", "wey", "getDeviceOrgX", "dox", "getDeviceOrgY", "doy", "getDeviceExtX", "setDeviceExtX", "dex", "getDeviceExtY", "setDeviceExtY", "dey", "transformX", "xDevice", "worldExtX", "transformY", "yDevice", "worldExtY", "inverseTransformX", "xWorld", "deviceExtX", "inverseTransformY", "yWorld", "deviceExtY", "inPoint", "outPoint", "FDLayout", "useSmartIdealEdgeLengthCalculation", "idealEdgeLength", "springConstant", "repulsionConstant", "gravityConstant", "compoundGravityConstant", "gravityRangeFactor", "compoundGravityRangeFactor", "displacementThresholdPerNode", "coolingFactor", "initialCoolingFactor", "totalDisplacement", "oldTotalDisplacement", "maxIterations", "arguments", "totalIterations", "notAnimatedIterations", "useFRGridVariant", "grid", "calcIdealEdgeLengths", "lcaDepth", "sizeOfSourceInLca", "sizeOfTargetInLca", "ideal<PERSON>ength", "initSpringEmbedder", "maxNodeDisplacement", "totalDisplacementThreshold", "repulsionRange", "calcRepulsionRange", "calcSpringForces", "l<PERSON><PERSON>", "calcSpringForce", "calcRepulsionForces", "gridUpdateAllowed", "forceToNodeSurroundingUpdate", "j", "nodeA", "nodeB", "lNodes", "processedNodeSet", "updateGrid", "calculateRepulsionForceOfANode", "calcRepulsionForce", "calcGravitationalForces", "calcGravitationalForce", "moveNodes", "springForce", "springForceX", "springForceY", "clipPoints", "distanceX", "distanceY", "distanceSquared", "distance", "repulsionForce", "repulsionForceX", "repulsionForceY", "childrenConstant", "ownerCenterX", "ownerCenterY", "absDistanceX", "absDistanceY", "gravitationForceX", "gravitationForceY", "isConverged", "converged", "oscilating", "animate", "calcNoOfChildrenForAllNodes", "calcGrid", "sizeX", "sizeY", "addNodeToGrid", "startX", "finishX", "startY", "finishY", "setGridCoordinates", "surrounding", "FDLayoutEdge", "FDLayoutNode", "displacementX", "displacementY", "_startX", "_finishX", "_startY", "_finishY", "HashMap", "map", "put", "theId", "contains", "keySet", "HashSet", "clear", "isEmpty", "addAllTo", "addAll", "Quicksort", "A", "compareFunction", "_defaultCompareFunction", "_quicksort", "_partition", "_get", "_swap", "_set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sequence1", "sequence2", "match_score", "mismatch_penalty", "gap_penalty", "iMax", "jMax", "tracebackGrid", "_i", "_j", "alignments", "score", "computeGrids", "getScore", "getAlignments", "_i2", "_j2", "diag", "up", "maxOf", "indices", "arrayAllMaxIndexes", "includes", "alignmentTraceback", "inProcessAlignments", "pos", "seq1", "seq2", "getAllIndexes", "indexes", "array", "apply", "layoutBase", "listeners", "addListener", "event", "callback", "removeListener", "emit", "data"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/layout-base/layout-base.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"layoutBase\"] = factory();\n\telse\n\t\troot[\"layoutBase\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 26);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar RectangleD = __webpack_require__(13);\nvar LayoutConstants = __webpack_require__(0);\nvar RandomSeed = __webpack_require__(16);\nvar PointD = __webpack_require__(4);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth > width) {\n        this.rect.x -= (this.labelWidth - width) / 2;\n        this.setWidth(this.labelWidth);\n      }\n\n      if (this.labelHeight > height) {\n        if (this.labelPos == \"center\") {\n          this.rect.y -= (this.labelHeight - height) / 2;\n        } else if (this.labelPos == \"top\") {\n          this.rect.y -= this.labelHeight - height;\n        }\n        this.setHeight(this.labelHeight);\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar RectangleD = __webpack_require__(13);\nvar Point = __webpack_require__(12);\nvar LinkedList = __webpack_require__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __webpack_require__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __webpack_require__(5); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __webpack_require__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar LGraph = __webpack_require__(5);\nvar PointD = __webpack_require__(4);\nvar Transform = __webpack_require__(17);\nvar Emitter = __webpack_require__(27);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar PointD = __webpack_require__(4);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __webpack_require__(15);\nvar FDLayoutConstants = __webpack_require__(7);\nvar LayoutConstants = __webpack_require__(0);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.idealEdgeLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n  this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    edge.idealLength = this.idealEdgeLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += FDLayoutConstants.DEFAULT_EDGE_LENGTH * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = this.springConstant * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      repulsionForce = this.repulsionConstant * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LEdge = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(7);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LNode = __webpack_require__(3);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __webpack_require__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __webpack_require__(18);\nlayoutBase.FDLayoutConstants = __webpack_require__(7);\nlayoutBase.FDLayoutEdge = __webpack_require__(19);\nlayoutBase.FDLayoutNode = __webpack_require__(20);\nlayoutBase.DimensionD = __webpack_require__(21);\nlayoutBase.HashMap = __webpack_require__(22);\nlayoutBase.HashSet = __webpack_require__(23);\nlayoutBase.IGeometry = __webpack_require__(8);\nlayoutBase.IMath = __webpack_require__(9);\nlayoutBase.Integer = __webpack_require__(10);\nlayoutBase.Point = __webpack_require__(12);\nlayoutBase.PointD = __webpack_require__(4);\nlayoutBase.RandomSeed = __webpack_require__(16);\nlayoutBase.RectangleD = __webpack_require__(13);\nlayoutBase.Transform = __webpack_require__(17);\nlayoutBase.UniqueIDGeneretor = __webpack_require__(14);\nlayoutBase.Quicksort = __webpack_require__(24);\nlayoutBase.LinkedList = __webpack_require__(11);\nlayoutBase.LGraphObject = __webpack_require__(2);\nlayoutBase.LGraph = __webpack_require__(5);\nlayoutBase.LEdge = __webpack_require__(1);\nlayoutBase.LGraphManager = __webpack_require__(6);\nlayoutBase.LNode = __webpack_require__(3);\nlayoutBase.Layout = __webpack_require__(15);\nlayoutBase.LayoutConstants = __webpack_require__(0);\nlayoutBase.NeedlemanWunsch = __webpack_require__(25);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});"], "mappings": "AAAA,CAAC,SAASA,gCAAgCA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzD,IAAG,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAC3DA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,CAAC,KACvB,IAAG,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EACjDD,MAAM,CAAC,EAAE,EAAEH,OAAO,CAAC,CAAC,KAChB,IAAG,OAAOC,OAAO,KAAK,QAAQ,EAClCA,OAAO,CAAC,YAAY,CAAC,GAAGD,OAAO,CAAC,CAAC,CAAC,KAElCD,IAAI,CAAC,YAAY,CAAC,GAAGC,OAAO,CAAC,CAAC;AAChC,CAAC,EAAE,IAAI,EAAE,YAAW;EACpB,OAAO,QAAU,UAASK,OAAO,EAAE;IAAE;IACrC,SAAU;IACV;IAAU,IAAIC,gBAAgB,GAAG,CAAC,CAAC;IACnC;IACA,SAAU;IACV;IAAU,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;MACjD;MACA,SAAW;MACX,QAAW,IAAGF,gBAAgB,CAACE,QAAQ,CAAC,EAAE;QAC1C,QAAY,OAAOF,gBAAgB,CAACE,QAAQ,CAAC,CAACP,OAAO;QACrD;MAAW;MACX,SAAW;MACX;MAAW,IAAIC,MAAM,GAAGI,gBAAgB,CAACE,QAAQ,CAAC,GAAG;QACrD,QAAYC,CAAC,EAAED,QAAQ;QACvB,QAAYE,CAAC,EAAE,KAAK;QACpB,QAAYT,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAWI,OAAO,CAACG,QAAQ,CAAC,CAACG,IAAI,CAACT,MAAM,CAACD,OAAO,EAAEC,MAAM,EAAEA,MAAM,CAACD,OAAO,EAAEM,mBAAmB,CAAC;MAC9F;MACA,SAAW;MACX;MAAWL,MAAM,CAACQ,CAAC,GAAG,IAAI;MAC1B;MACA,SAAW;MACX;MAAW,OAAOR,MAAM,CAACD,OAAO;MAChC;IAAU;IACV;IACA;IACA,SAAU;IACV;IAAUM,mBAAmB,CAACK,CAAC,GAAGP,OAAO;IACzC;IACA,SAAU;IACV;IAAUE,mBAAmB,CAACM,CAAC,GAAGP,gBAAgB;IAClD;IACA,SAAU;IACV;IAAUC,mBAAmB,CAACE,CAAC,GAAG,UAASK,KAAK,EAAE;MAAE,OAAOA,KAAK;IAAE,CAAC;IACnE;IACA,SAAU;IACV;IAAUP,mBAAmB,CAACQ,CAAC,GAAG,UAASd,OAAO,EAAEe,IAAI,EAAEC,MAAM,EAAE;MAClE,QAAW,IAAG,CAACV,mBAAmB,CAACW,CAAC,CAACjB,OAAO,EAAEe,IAAI,CAAC,EAAE;QACrD,QAAYG,MAAM,CAACC,cAAc,CAACnB,OAAO,EAAEe,IAAI,EAAE;UACjD,QAAaK,YAAY,EAAE,KAAK;UAChC,QAAaC,UAAU,EAAE,IAAI;UAC7B,QAAaC,GAAG,EAAEN;UAClB;QAAY,CAAC,CAAC;QACd;MAAW;MACX;IAAU,CAAC;IACX;IACA,SAAU;IACV;IAAUV,mBAAmB,CAACiB,CAAC,GAAG,UAAStB,MAAM,EAAE;MACnD,QAAW,IAAIe,MAAM,GAAGf,MAAM,IAAIA,MAAM,CAACuB,UAAU,GACnD,QAAY,SAASC,UAAUA,CAAA,EAAG;QAAE,OAAOxB,MAAM,CAAC,SAAS,CAAC;MAAE,CAAC,GAC/D,QAAY,SAASyB,gBAAgBA,CAAA,EAAG;QAAE,OAAOzB,MAAM;MAAE,CAAC;MAC1D;MAAWK,mBAAmB,CAACQ,CAAC,CAACE,MAAM,EAAE,GAAG,EAAEA,MAAM,CAAC;MACrD;MAAW,OAAOA,MAAM;MACxB;IAAU,CAAC;IACX;IACA,SAAU;IACV;IAAUV,mBAAmB,CAACW,CAAC,GAAG,UAASU,MAAM,EAAEC,QAAQ,EAAE;MAAE,OAAOV,MAAM,CAACW,SAAS,CAACC,cAAc,CAACpB,IAAI,CAACiB,MAAM,EAAEC,QAAQ,CAAC;IAAE,CAAC;IAC/H;IACA,SAAU;IACV;IAAUtB,mBAAmB,CAACyB,CAAC,GAAG,EAAE;IACpC;IACA,SAAU;IACV;IAAU,OAAOzB,mBAAmB,CAACA,mBAAmB,CAAC0B,CAAC,GAAG,EAAE,CAAC;IAChE;EAAS;EACT;EACA,SAAU,EACV;EACA,KAAO,UAAS/B,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS2B,eAAeA,CAAA,EAAG,CAAC;;IAE5B;AACA;AACA;IACAA,eAAe,CAACC,OAAO,GAAG,CAAC;;IAE3B;AACA;AACA;IACAD,eAAe,CAACE,8BAA8B,GAAG,KAAK;IACtDF,eAAe,CAACG,mBAAmB,GAAG,KAAK;IAC3CH,eAAe,CAACI,2BAA2B,GAAG,IAAI;IAClDJ,eAAe,CAACK,+BAA+B,GAAG,KAAK;IACvDL,eAAe,CAACM,wBAAwB,GAAG,EAAE;IAC7CN,eAAe,CAACO,+BAA+B,GAAG,KAAK;;IAEvD;IACA;IACA;IACA;AACA;AACA;AACA;IACAP,eAAe,CAACQ,oBAAoB,GAAG,EAAE;;IAEzC;AACA;AACA;IACAR,eAAe,CAACS,8BAA8B,GAAG,KAAK;;IAEtD;AACA;AACA;IACAT,eAAe,CAACU,gBAAgB,GAAG,EAAE;;IAErC;AACA;AACA;IACAV,eAAe,CAACW,qBAAqB,GAAGX,eAAe,CAACU,gBAAgB,GAAG,CAAC;;IAE5E;AACA;AACA;AACA;IACAV,eAAe,CAACY,wBAAwB,GAAG,EAAE;;IAE7C;AACA;AACA;IACAZ,eAAe,CAACa,eAAe,GAAG,CAAC;;IAEnC;AACA;AACA;IACAb,eAAe,CAACc,cAAc,GAAG,OAAO;;IAExC;AACA;AACA;IACAd,eAAe,CAACe,sBAAsB,GAAGf,eAAe,CAACc,cAAc,GAAG,IAAI;;IAE9E;AACA;AACA;IACAd,eAAe,CAACgB,cAAc,GAAG,IAAI;IACrChB,eAAe,CAACiB,cAAc,GAAG,GAAG;IAEpCjD,MAAM,CAACD,OAAO,GAAGiC,eAAe;;IAEhC;EAAM,CAAC,IACP;EACA,KAAO,UAAShC,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI8C,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IACtC,IAAI+C,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAASgD,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;MACpCN,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAE+C,KAAK,CAAC;MAE9B,IAAI,CAACC,2BAA2B,GAAG,KAAK;MACxC,IAAI,CAACC,YAAY,GAAGF,KAAK;MACzB,IAAI,CAACG,UAAU,GAAG,EAAE;MACpB,IAAI,CAACL,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACtB;IAEAF,KAAK,CAACzB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7BG,KAAK,CAACQ,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IAClC;IAEAR,KAAK,CAACzB,SAAS,CAACkC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACR,MAAM;IACpB,CAAC;IAEDD,KAAK,CAACzB,SAAS,CAACmC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACR,MAAM;IACpB,CAAC;IAEDF,KAAK,CAACzB,SAAS,CAACoC,YAAY,GAAG,YAAY;MACzC,OAAO,IAAI,CAACA,YAAY;IAC1B,CAAC;IAEDX,KAAK,CAACzB,SAAS,CAACqC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACC,MAAM;IACpB,CAAC;IAEDb,KAAK,CAACzB,SAAS,CAAC6B,2BAA2B,GAAG,YAAY;MACxD,OAAO,IAAI,CAACA,2BAA2B;IACzC,CAAC;IAEDJ,KAAK,CAACzB,SAAS,CAACuC,aAAa,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAEDN,KAAK,CAACzB,SAAS,CAACwC,MAAM,GAAG,YAAY;MACnC,OAAO,IAAI,CAACC,GAAG;IACjB,CAAC;IAEDhB,KAAK,CAACzB,SAAS,CAAC0C,cAAc,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACC,WAAW;IACzB,CAAC;IAEDlB,KAAK,CAACzB,SAAS,CAAC4C,cAAc,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACC,WAAW;IACzB,CAAC;IAEDpB,KAAK,CAACzB,SAAS,CAAC8C,WAAW,GAAG,UAAUC,IAAI,EAAE;MAC5C,IAAI,IAAI,CAACrB,MAAM,KAAKqB,IAAI,EAAE;QACxB,OAAO,IAAI,CAACpB,MAAM;MACpB,CAAC,MAAM,IAAI,IAAI,CAACA,MAAM,KAAKoB,IAAI,EAAE;QAC/B,OAAO,IAAI,CAACrB,MAAM;MACpB,CAAC,MAAM;QACL,MAAM,qCAAqC;MAC7C;IACF,CAAC;IAEDD,KAAK,CAACzB,SAAS,CAACgD,kBAAkB,GAAG,UAAUD,IAAI,EAAEE,KAAK,EAAE;MAC1D,IAAIC,QAAQ,GAAG,IAAI,CAACJ,WAAW,CAACC,IAAI,CAAC;MACrC,IAAI9E,IAAI,GAAGgF,KAAK,CAACE,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAE5C,OAAO,IAAI,EAAE;QACX,IAAIF,QAAQ,CAACG,QAAQ,CAAC,CAAC,IAAIJ,KAAK,EAAE;UAChC,OAAOC,QAAQ;QACjB;QAEA,IAAIA,QAAQ,CAACG,QAAQ,CAAC,CAAC,IAAIpF,IAAI,EAAE;UAC/B;QACF;QAEAiF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;MAC5C;MAEA,OAAO,IAAI;IACb,CAAC;IAED7B,KAAK,CAACzB,SAAS,CAACuD,YAAY,GAAG,YAAY;MACzC,IAAIC,oBAAoB,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;MAEvC,IAAI,CAAC5B,2BAA2B,GAAGN,SAAS,CAACmC,eAAe,CAAC,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,CAAC,EAAE,IAAI,CAACjC,MAAM,CAACiC,OAAO,CAAC,CAAC,EAAEH,oBAAoB,CAAC;MAEhI,IAAI,CAAC,IAAI,CAAC3B,2BAA2B,EAAE;QACrC,IAAI,CAAC+B,OAAO,GAAGJ,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;QAChE,IAAI,CAACK,OAAO,GAAGL,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;QAEhE,IAAIM,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAC,GAAG,GAAG,EAAE;UAChC,IAAI,CAACA,OAAO,GAAGpC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;QACzC;QAEA,IAAIE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,OAAO,CAAC,GAAG,GAAG,EAAE;UAChC,IAAI,CAACA,OAAO,GAAGrC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC;QACzC;QAEA,IAAI,CAACvB,MAAM,GAAGwB,IAAI,CAACG,IAAI,CAAC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC;MACpF;IACF,CAAC;IAEDpC,KAAK,CAACzB,SAAS,CAACkE,kBAAkB,GAAG,YAAY;MAC/C,IAAI,CAACN,OAAO,GAAG,IAAI,CAACjC,MAAM,CAACwC,UAAU,CAAC,CAAC,GAAG,IAAI,CAACzC,MAAM,CAACyC,UAAU,CAAC,CAAC;MAClE,IAAI,CAACN,OAAO,GAAG,IAAI,CAAClC,MAAM,CAACyC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC1C,MAAM,CAAC0C,UAAU,CAAC,CAAC;MAElE,IAAIN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAC,GAAG,GAAG,EAAE;QAChC,IAAI,CAACA,OAAO,GAAGpC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;MACzC;MAEA,IAAIE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,OAAO,CAAC,GAAG,GAAG,EAAE;QAChC,IAAI,CAACA,OAAO,GAAGrC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC;MACzC;MAEA,IAAI,CAACvB,MAAM,GAAGwB,IAAI,CAACG,IAAI,CAAC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC;IACpF,CAAC;IAEDzF,MAAM,CAACD,OAAO,GAAGsD,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASrD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS6C,YAAYA,CAACQ,YAAY,EAAE;MAClC,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC;IAEA1D,MAAM,CAACD,OAAO,GAAGmD,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAASlD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI4F,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IACrC,IAAI6F,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAI8F,UAAU,GAAG9F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASgG,KAAKA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACnC;MACA,IAAID,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;QACjCA,KAAK,GAAGF,GAAG;MACb;MAEArD,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAEgG,KAAK,CAAC;;MAE9B;MACA,IAAIH,EAAE,CAACI,YAAY,IAAI,IAAI,EAAEJ,EAAE,GAAGA,EAAE,CAACI,YAAY;MAEjD,IAAI,CAACC,aAAa,GAAGV,OAAO,CAACW,SAAS;MACtC,IAAI,CAACC,kBAAkB,GAAGZ,OAAO,CAACa,SAAS;MAC3C,IAAI,CAACpD,YAAY,GAAG+C,KAAK;MACzB,IAAI,CAACM,KAAK,GAAG,EAAE;MACf,IAAI,CAACL,YAAY,GAAGJ,EAAE;MAEtB,IAAIE,IAAI,IAAI,IAAI,IAAID,GAAG,IAAI,IAAI,EAAE,IAAI,CAACS,IAAI,GAAG,IAAId,UAAU,CAACK,GAAG,CAACU,CAAC,EAAEV,GAAG,CAACW,CAAC,EAAEV,IAAI,CAACW,KAAK,EAAEX,IAAI,CAACY,MAAM,CAAC,CAAC,KAAK,IAAI,CAACJ,IAAI,GAAG,IAAId,UAAU,CAAC,CAAC;IACtI;IAEAG,KAAK,CAACzE,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IACvD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7BmD,KAAK,CAACxC,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IAClC;IAEAwC,KAAK,CAACzE,SAAS,CAACyF,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDV,KAAK,CAACzE,SAAS,CAAC0F,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACC,KAAK;IACnB,CAAC;IAEDlB,KAAK,CAACzE,SAAS,CAACqD,QAAQ,GAAG,YAAY;MACrC;MACA;MACA;MACA;MACA;;MAEA,OAAO,IAAI,CAACuC,KAAK;IACnB,CAAC;IAEDnB,KAAK,CAACzE,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACT,IAAI,CAACG,KAAK;IACxB,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC1C,IAAI,CAACH,IAAI,CAACG,KAAK,GAAGA,KAAK;IACzB,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAAC+F,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACX,IAAI,CAACI,MAAM;IACzB,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MAC5C,IAAI,CAACJ,IAAI,CAACI,MAAM,GAAGA,MAAM;IAC3B,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACmE,UAAU,GAAG,YAAY;MACvC,OAAO,IAAI,CAACiB,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK,GAAG,CAAC;IAC1C,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACoE,UAAU,GAAG,YAAY;MACvC,OAAO,IAAI,CAACgB,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM,GAAG,CAAC;IAC3C,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACiG,SAAS,GAAG,YAAY;MACtC,OAAO,IAAIzB,MAAM,CAAC,IAAI,CAACY,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK,GAAG,CAAC,EAAE,IAAI,CAACH,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;IAC1F,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACkG,WAAW,GAAG,YAAY;MACxC,OAAO,IAAI1B,MAAM,CAAC,IAAI,CAACY,IAAI,CAACC,CAAC,EAAE,IAAI,CAACD,IAAI,CAACE,CAAC,CAAC;IAC7C,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAAC2D,OAAO,GAAG,YAAY;MACpC,OAAO,IAAI,CAACyB,IAAI;IAClB,CAAC;IAEDX,KAAK,CAACzE,SAAS,CAACmG,WAAW,GAAG,YAAY;MACxC,OAAOrC,IAAI,CAACG,IAAI,CAAC,IAAI,CAACmB,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACI,MAAM,CAAC;IAC3F,CAAC;;IAED;AACA;AACA;IACAf,KAAK,CAACzE,SAAS,CAACoG,kBAAkB,GAAG,YAAY;MAC/C,OAAOtC,IAAI,CAACG,IAAI,CAAC,IAAI,CAACmB,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC/F,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACqG,OAAO,GAAG,UAAUC,SAAS,EAAEC,SAAS,EAAE;MACxD,IAAI,CAACnB,IAAI,CAACC,CAAC,GAAGiB,SAAS,CAACjB,CAAC;MACzB,IAAI,CAACD,IAAI,CAACE,CAAC,GAAGgB,SAAS,CAAChB,CAAC;MACzB,IAAI,CAACF,IAAI,CAACG,KAAK,GAAGgB,SAAS,CAAChB,KAAK;MACjC,IAAI,CAACH,IAAI,CAACI,MAAM,GAAGe,SAAS,CAACf,MAAM;IACrC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACwG,SAAS,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;MAC5C,IAAI,CAACtB,IAAI,CAACC,CAAC,GAAGoB,EAAE,GAAG,IAAI,CAACrB,IAAI,CAACG,KAAK,GAAG,CAAC;MACtC,IAAI,CAACH,IAAI,CAACE,CAAC,GAAGoB,EAAE,GAAG,IAAI,CAACtB,IAAI,CAACI,MAAM,GAAG,CAAC;IACzC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAAC2G,WAAW,GAAG,UAAUtB,CAAC,EAAEC,CAAC,EAAE;MAC5C,IAAI,CAACF,IAAI,CAACC,CAAC,GAAGA,CAAC;MACf,IAAI,CAACD,IAAI,CAACE,CAAC,GAAGA,CAAC;IACjB,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAAC4G,MAAM,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;MACzC,IAAI,CAAC1B,IAAI,CAACC,CAAC,IAAIwB,EAAE;MACjB,IAAI,CAACzB,IAAI,CAACE,CAAC,IAAIwB,EAAE;IACnB,CAAC;IAEDrC,KAAK,CAACzE,SAAS,CAAC+G,iBAAiB,GAAG,UAAUC,EAAE,EAAE;MAChD,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAIC,IAAI;MACR,IAAIC,IAAI,GAAG,IAAI;MAEfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAIA,IAAI,CAACvF,MAAM,IAAIqF,EAAE,EAAE;UACrB,IAAIE,IAAI,CAACxF,MAAM,IAAIyF,IAAI,EAAE,MAAM,wBAAwB;UAEvDF,QAAQ,CAACI,IAAI,CAACH,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;MAEF,OAAOD,QAAQ;IACjB,CAAC;IAEDxC,KAAK,CAACzE,SAAS,CAACsH,eAAe,GAAG,UAAUC,KAAK,EAAE;MACjD,IAAIN,QAAQ,GAAG,EAAE;MACjB,IAAIC,IAAI;MAER,IAAIC,IAAI,GAAG,IAAI;MACfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAI,EAAEA,IAAI,CAACxF,MAAM,IAAIyF,IAAI,IAAID,IAAI,CAACvF,MAAM,IAAIwF,IAAI,CAAC,EAAE,MAAM,qCAAqC;QAE9F,IAAID,IAAI,CAACvF,MAAM,IAAI4F,KAAK,IAAIL,IAAI,CAACxF,MAAM,IAAI6F,KAAK,EAAE;UAChDN,QAAQ,CAACI,IAAI,CAACH,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;MAEF,OAAOD,QAAQ;IACjB,CAAC;IAEDxC,KAAK,CAACzE,SAAS,CAACwH,gBAAgB,GAAG,YAAY;MAC7C,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;MAEzB,IAAIP,IAAI,GAAG,IAAI;MACfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAIA,IAAI,CAACxF,MAAM,IAAIyF,IAAI,EAAE;UACvBM,SAAS,CAACE,GAAG,CAACT,IAAI,CAACvF,MAAM,CAAC;QAC5B,CAAC,MAAM;UACL,IAAIuF,IAAI,CAACvF,MAAM,IAAIwF,IAAI,EAAE;YACvB,MAAM,sBAAsB;UAC9B;UAEAM,SAAS,CAACE,GAAG,CAACT,IAAI,CAACxF,MAAM,CAAC;QAC5B;MACF,CAAC,CAAC;MAEF,OAAO+F,SAAS;IAClB,CAAC;IAEDhD,KAAK,CAACzE,SAAS,CAAC4H,YAAY,GAAG,YAAY;MACzC,IAAIC,iBAAiB,GAAG,IAAIH,GAAG,CAAC,CAAC;MACjC,IAAII,SAAS;MACb,IAAIC,QAAQ;MAEZF,iBAAiB,CAACF,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI,IAAI,CAAChC,KAAK,IAAI,IAAI,EAAE;QACtB,IAAIqC,KAAK,GAAG,IAAI,CAACrC,KAAK,CAACsC,QAAQ,CAAC,CAAC;QACjC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCmJ,SAAS,GAAGE,KAAK,CAACrJ,CAAC,CAAC;UACpBoJ,QAAQ,GAAGD,SAAS,CAACF,YAAY,CAAC,CAAC;UACnCG,QAAQ,CAACX,OAAO,CAAC,UAAUrE,IAAI,EAAE;YAC/B8E,iBAAiB,CAACF,GAAG,CAAC5E,IAAI,CAAC;UAC7B,CAAC,CAAC;QACJ;MACF;MAEA,OAAO8E,iBAAiB;IAC1B,CAAC;IAEDpD,KAAK,CAACzE,SAAS,CAACkI,eAAe,GAAG,YAAY;MAC5C,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIL,SAAS;MAEb,IAAI,IAAI,CAACnC,KAAK,IAAI,IAAI,EAAE;QACtBwC,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACL,IAAIH,KAAK,GAAG,IAAI,CAACrC,KAAK,CAACsC,QAAQ,CAAC,CAAC;QACjC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCmJ,SAAS,GAAGE,KAAK,CAACrJ,CAAC,CAAC;UAEpBwJ,YAAY,IAAIL,SAAS,CAACI,eAAe,CAAC,CAAC;QAC7C;MACF;MAEA,IAAIC,YAAY,IAAI,CAAC,EAAE;QACrBA,YAAY,GAAG,CAAC;MAClB;MACA,OAAOA,YAAY;IACrB,CAAC;IAED1D,KAAK,CAACzE,SAAS,CAACoI,gBAAgB,GAAG,YAAY;MAC7C,IAAI,IAAI,CAACrD,aAAa,IAAIV,OAAO,CAACW,SAAS,EAAE;QAC3C,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,aAAa;IAC3B,CAAC;IAEDN,KAAK,CAACzE,SAAS,CAACqI,iBAAiB,GAAG,YAAY;MAC9C,IAAI,IAAI,CAAC1C,KAAK,IAAI,IAAI,EAAE;QACtB,OAAO,IAAI,CAACZ,aAAa,GAAG,CAAC,IAAI,CAACK,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,IAAI,CAAC;MACtE,CAAC,MAAM;QACL,IAAI,CAACT,aAAa,GAAG,IAAI,CAACY,KAAK,CAAC0C,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAACjD,IAAI,CAACG,KAAK,GAAG,IAAI,CAACR,aAAa;QACpC,IAAI,CAACK,IAAI,CAACI,MAAM,GAAG,IAAI,CAACT,aAAa;QAErC,OAAO,IAAI,CAACA,aAAa;MAC3B;IACF,CAAC;IAEDN,KAAK,CAACzE,SAAS,CAACsI,OAAO,GAAG,YAAY;MACpC,IAAIC,aAAa;MACjB,IAAIC,aAAa;MAEjB,IAAIC,IAAI,GAAG,CAACrI,eAAe,CAACe,sBAAsB;MAClD,IAAIuH,IAAI,GAAGtI,eAAe,CAACe,sBAAsB;MACjDoH,aAAa,GAAGnI,eAAe,CAACgB,cAAc,GAAGmD,UAAU,CAACoE,UAAU,CAAC,CAAC,IAAID,IAAI,GAAGD,IAAI,CAAC,GAAGA,IAAI;MAE/F,IAAIG,IAAI,GAAG,CAACxI,eAAe,CAACe,sBAAsB;MAClD,IAAI0H,IAAI,GAAGzI,eAAe,CAACe,sBAAsB;MACjDqH,aAAa,GAAGpI,eAAe,CAACiB,cAAc,GAAGkD,UAAU,CAACoE,UAAU,CAAC,CAAC,IAAIE,IAAI,GAAGD,IAAI,CAAC,GAAGA,IAAI;MAE/F,IAAI,CAACxD,IAAI,CAACC,CAAC,GAAGkD,aAAa;MAC3B,IAAI,CAACnD,IAAI,CAACE,CAAC,GAAGkD,aAAa;IAC7B,CAAC;IAED/D,KAAK,CAACzE,SAAS,CAAC8I,YAAY,GAAG,YAAY;MACzC,IAAI,IAAI,CAACpD,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;QAC3B,MAAM,eAAe;MACvB;MACA,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,EAAE;QAC1C;QACA,IAAIyG,UAAU,GAAG,IAAI,CAACrD,QAAQ,CAAC,CAAC;QAChCqD,UAAU,CAACD,YAAY,CAAC,IAAI,CAAC;QAE7B,IAAI,CAAC1D,IAAI,CAACC,CAAC,GAAG0D,UAAU,CAACC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC5D,IAAI,CAACE,CAAC,GAAGyD,UAAU,CAACE,MAAM,CAAC,CAAC;QAEjC,IAAI,CAACnD,QAAQ,CAACiD,UAAU,CAACG,QAAQ,CAAC,CAAC,GAAGH,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAChD,SAAS,CAAC+C,UAAU,CAACI,SAAS,CAAC,CAAC,GAAGJ,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAI7I,eAAe,CAACS,8BAA8B,EAAE;UAElD,IAAI0E,KAAK,GAAGwD,UAAU,CAACG,QAAQ,CAAC,CAAC,GAAGH,UAAU,CAACC,OAAO,CAAC,CAAC;UACxD,IAAIxD,MAAM,GAAGuD,UAAU,CAACI,SAAS,CAAC,CAAC,GAAGJ,UAAU,CAACE,MAAM,CAAC,CAAC;UAEzD,IAAI,IAAI,CAACG,UAAU,GAAG7D,KAAK,EAAE;YAC3B,IAAI,CAACH,IAAI,CAACC,CAAC,IAAI,CAAC,IAAI,CAAC+D,UAAU,GAAG7D,KAAK,IAAI,CAAC;YAC5C,IAAI,CAACO,QAAQ,CAAC,IAAI,CAACsD,UAAU,CAAC;UAChC;UAEA,IAAI,IAAI,CAACC,WAAW,GAAG7D,MAAM,EAAE;YAC7B,IAAI,IAAI,CAAC8D,QAAQ,IAAI,QAAQ,EAAE;cAC7B,IAAI,CAAClE,IAAI,CAACE,CAAC,IAAI,CAAC,IAAI,CAAC+D,WAAW,GAAG7D,MAAM,IAAI,CAAC;YAChD,CAAC,MAAM,IAAI,IAAI,CAAC8D,QAAQ,IAAI,KAAK,EAAE;cACjC,IAAI,CAAClE,IAAI,CAACE,CAAC,IAAI,IAAI,CAAC+D,WAAW,GAAG7D,MAAM;YAC1C;YACA,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACqD,WAAW,CAAC;UAClC;QACF;MACF;IACF,CAAC;IAED5E,KAAK,CAACzE,SAAS,CAACuJ,qBAAqB,GAAG,YAAY;MAClD,IAAI,IAAI,CAACtE,kBAAkB,IAAIZ,OAAO,CAACa,SAAS,EAAE;QAChD,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,kBAAkB;IAChC,CAAC;IAEDR,KAAK,CAACzE,SAAS,CAACwJ,SAAS,GAAG,UAAUC,KAAK,EAAE;MAC3C,IAAIC,IAAI,GAAG,IAAI,CAACtE,IAAI,CAACC,CAAC;MAEtB,IAAIqE,IAAI,GAAGtJ,eAAe,CAACc,cAAc,EAAE;QACzCwI,IAAI,GAAGtJ,eAAe,CAACc,cAAc;MACvC,CAAC,MAAM,IAAIwI,IAAI,GAAG,CAACtJ,eAAe,CAACc,cAAc,EAAE;QACjDwI,IAAI,GAAG,CAACtJ,eAAe,CAACc,cAAc;MACxC;MAEA,IAAIyI,GAAG,GAAG,IAAI,CAACvE,IAAI,CAACE,CAAC;MAErB,IAAIqE,GAAG,GAAGvJ,eAAe,CAACc,cAAc,EAAE;QACxCyI,GAAG,GAAGvJ,eAAe,CAACc,cAAc;MACtC,CAAC,MAAM,IAAIyI,GAAG,GAAG,CAACvJ,eAAe,CAACc,cAAc,EAAE;QAChDyI,GAAG,GAAG,CAACvJ,eAAe,CAACc,cAAc;MACvC;MAEA,IAAI0I,OAAO,GAAG,IAAIpF,MAAM,CAACkF,IAAI,EAAEC,GAAG,CAAC;MACnC,IAAIE,QAAQ,GAAGJ,KAAK,CAACK,qBAAqB,CAACF,OAAO,CAAC;MAEnD,IAAI,CAACjD,WAAW,CAACkD,QAAQ,CAACxE,CAAC,EAAEwE,QAAQ,CAACvE,CAAC,CAAC;IAC1C,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAACgJ,OAAO,GAAG,YAAY;MACpC,OAAO,IAAI,CAAC5D,IAAI,CAACC,CAAC;IACpB,CAAC;IAEDZ,KAAK,CAACzE,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAAC9D,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK;IACtC,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACiJ,MAAM,GAAG,YAAY;MACnC,OAAO,IAAI,CAAC7D,IAAI,CAACE,CAAC;IACpB,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAACmJ,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAAC/D,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM;IACvC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACsD,SAAS,GAAG,YAAY;MACtC,IAAI,IAAI,CAACsC,KAAK,IAAI,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,OAAO,IAAI,CAACA,KAAK,CAACtC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEDlF,MAAM,CAACD,OAAO,GAAGsG,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASrG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS+F,MAAMA,CAACa,CAAC,EAAEC,CAAC,EAAE;MACpB,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACD,CAAC,GAAG,CAAC;QACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACZ,CAAC,MAAM;QACL,IAAI,CAACD,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;MACZ;IACF;IAEAd,MAAM,CAACxE,SAAS,CAAC+J,IAAI,GAAG,YAAY;MAClC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDb,MAAM,CAACxE,SAAS,CAACgK,IAAI,GAAG,YAAY;MAClC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACiK,IAAI,GAAG,UAAU5E,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDb,MAAM,CAACxE,SAAS,CAACkK,IAAI,GAAG,UAAU5E,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACmK,aAAa,GAAG,UAAUC,EAAE,EAAE;MAC7C,OAAO,IAAIC,UAAU,CAAC,IAAI,CAAChF,CAAC,GAAG+E,EAAE,CAAC/E,CAAC,EAAE,IAAI,CAACC,CAAC,GAAG8E,EAAE,CAAC9E,CAAC,CAAC;IACrD,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACsK,OAAO,GAAG,YAAY;MACrC,OAAO,IAAI9F,MAAM,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IACnC,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACuK,SAAS,GAAG,UAAUC,GAAG,EAAE;MAC1C,IAAI,CAACnF,CAAC,IAAImF,GAAG,CAACjF,KAAK;MACnB,IAAI,CAACD,CAAC,IAAIkF,GAAG,CAAChF,MAAM;MACpB,OAAO,IAAI;IACb,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGqG,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAASpG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI4F,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IACrC,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAIgM,aAAa,GAAGhM,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAI6F,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAIiM,KAAK,GAAGjM,mBAAmB,CAAC,EAAE,CAAC;IACnC,IAAIkM,UAAU,GAAGlM,mBAAmB,CAAC,EAAE,CAAC;IAExC,SAASmM,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;MACpCzJ,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAEkM,MAAM,CAAC;MAC/B,IAAI,CAAChG,aAAa,GAAGV,OAAO,CAACW,SAAS;MACtC,IAAI,CAACgG,MAAM,GAAG5K,eAAe,CAACQ,oBAAoB;MAClD,IAAI,CAACuE,KAAK,GAAG,EAAE;MACf,IAAI,CAAC6C,KAAK,GAAG,EAAE;MACf,IAAI,CAACiD,WAAW,GAAG,KAAK;MACxB,IAAI,CAACJ,MAAM,GAAGA,MAAM;MAEpB,IAAIC,IAAI,IAAI,IAAI,IAAIA,IAAI,YAAYL,aAAa,EAAE;QACjD,IAAI,CAAC3F,YAAY,GAAGgG,IAAI;MAC1B,CAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,YAAYI,MAAM,EAAE;QACjD,IAAI,CAACpG,YAAY,GAAGgG,IAAI,CAAChG,YAAY;MACvC;IACF;IAEA8F,MAAM,CAAC5K,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IACxD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7BsJ,MAAM,CAAC3I,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IACnC;IAEA2I,MAAM,CAAC5K,SAAS,CAACiI,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACD,KAAK;IACnB,CAAC;IAED4C,MAAM,CAAC5K,SAAS,CAACyF,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDyF,MAAM,CAAC5K,SAAS,CAACmD,eAAe,GAAG,YAAY;MAC7C,OAAO,IAAI,CAAC2B,YAAY;IAC1B,CAAC;IAED8F,MAAM,CAAC5K,SAAS,CAACsD,SAAS,GAAG,YAAY;MACvC,OAAO,IAAI,CAACuH,MAAM;IACpB,CAAC;IAEDD,MAAM,CAAC5K,SAAS,CAACgJ,OAAO,GAAG,YAAY;MACrC,OAAO,IAAI,CAACU,IAAI;IAClB,CAAC;IAEDkB,MAAM,CAAC5K,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACiC,KAAK;IACnB,CAAC;IAEDP,MAAM,CAAC5K,SAAS,CAACiJ,MAAM,GAAG,YAAY;MACpC,OAAO,IAAI,CAACU,GAAG;IACjB,CAAC;IAEDiB,MAAM,CAAC5K,SAAS,CAACmJ,SAAS,GAAG,YAAY;MACvC,OAAO,IAAI,CAACiC,MAAM;IACpB,CAAC;IAEDR,MAAM,CAAC5K,SAAS,CAACiL,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAACA,WAAW;IACzB,CAAC;IAEDL,MAAM,CAAC5K,SAAS,CAAC2H,GAAG,GAAG,UAAU0D,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC7D,IAAID,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;QAC5C,IAAIC,OAAO,GAAGH,IAAI;QAClB,IAAI,IAAI,CAACvG,YAAY,IAAI,IAAI,EAAE;UAC7B,MAAM,yBAAyB;QACjC;QACA,IAAI,IAAI,CAACmD,QAAQ,CAAC,CAAC,CAACwD,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;UACzC,MAAM,wBAAwB;QAChC;QACAA,OAAO,CAAC5F,KAAK,GAAG,IAAI;QACpB,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACZ,IAAI,CAACmE,OAAO,CAAC;QAE7B,OAAOA,OAAO;MAChB,CAAC,MAAM;QACL,IAAIE,OAAO,GAAGL,IAAI;QAClB,IAAI,EAAE,IAAI,CAACpD,QAAQ,CAAC,CAAC,CAACwD,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACrD,QAAQ,CAAC,CAAC,CAACwD,OAAO,CAACF,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAC3F,MAAM,gCAAgC;QACxC;QAEA,IAAI,EAAED,UAAU,CAAC1F,KAAK,IAAI2F,UAAU,CAAC3F,KAAK,IAAI0F,UAAU,CAAC1F,KAAK,IAAI,IAAI,CAAC,EAAE;UACvE,MAAM,iCAAiC;QACzC;QAEA,IAAI0F,UAAU,CAAC1F,KAAK,IAAI2F,UAAU,CAAC3F,KAAK,EAAE;UACxC,OAAO,IAAI;QACb;;QAEA;QACA8F,OAAO,CAAChK,MAAM,GAAG4J,UAAU;QAC3BI,OAAO,CAAC/J,MAAM,GAAG4J,UAAU;;QAE3B;QACAG,OAAO,CAACtJ,YAAY,GAAG,KAAK;;QAE5B;QACA,IAAI,CAACqD,QAAQ,CAAC,CAAC,CAAC4B,IAAI,CAACqE,OAAO,CAAC;;QAE7B;QACAJ,UAAU,CAACnG,KAAK,CAACkC,IAAI,CAACqE,OAAO,CAAC;QAE9B,IAAIH,UAAU,IAAID,UAAU,EAAE;UAC5BC,UAAU,CAACpG,KAAK,CAACkC,IAAI,CAACqE,OAAO,CAAC;QAChC;QAEA,OAAOA,OAAO;MAChB;IACF,CAAC;IAEDd,MAAM,CAAC5K,SAAS,CAAC2L,MAAM,GAAG,UAAUC,GAAG,EAAE;MACvC,IAAI7I,IAAI,GAAG6I,GAAG;MACd,IAAIA,GAAG,YAAYnH,KAAK,EAAE;QACxB,IAAI1B,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,EAAEA,IAAI,CAAC6C,KAAK,IAAI,IAAI,IAAI7C,IAAI,CAAC6C,KAAK,IAAI,IAAI,CAAC,EAAE;UAC/C,MAAM,yBAAyB;QACjC;QACA,IAAI,IAAI,CAACd,YAAY,IAAI,IAAI,EAAE;UAC7B,MAAM,iCAAiC;QACzC;QACA;QACA,IAAI+G,gBAAgB,GAAG9I,IAAI,CAACoC,KAAK,CAAC2G,KAAK,CAAC,CAAC;QACzC,IAAI5E,IAAI;QACR,IAAI/G,CAAC,GAAG0L,gBAAgB,CAACvJ,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BuI,IAAI,GAAG2E,gBAAgB,CAAClN,CAAC,CAAC;UAE1B,IAAIuI,IAAI,CAAC9E,YAAY,EAAE;YACrB,IAAI,CAAC0C,YAAY,CAAC6G,MAAM,CAACzE,IAAI,CAAC;UAChC,CAAC,MAAM;YACLA,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAAC+F,MAAM,CAACzE,IAAI,CAAC;UAChC;QACF;;QAEA;QACA,IAAI6E,KAAK,GAAG,IAAI,CAAC/D,KAAK,CAACyD,OAAO,CAAC1I,IAAI,CAAC;QACpC,IAAIgJ,KAAK,IAAI,CAAC,CAAC,EAAE;UACf,MAAM,8BAA8B;QACtC;QAEA,IAAI,CAAC/D,KAAK,CAACgE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIH,GAAG,YAAYnK,KAAK,EAAE;QAC/B,IAAIyF,IAAI,GAAG0E,GAAG;QACd,IAAI1E,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,EAAEA,IAAI,CAACxF,MAAM,IAAI,IAAI,IAAIwF,IAAI,CAACvF,MAAM,IAAI,IAAI,CAAC,EAAE;UACjD,MAAM,+BAA+B;QACvC;QACA,IAAI,EAAEuF,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACvF,MAAM,CAACiE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACvF,MAAM,CAACiE,KAAK,IAAI,IAAI,CAAC,EAAE;UACvH,MAAM,wCAAwC;QAChD;QAEA,IAAIqG,WAAW,GAAG/E,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC;QACjD,IAAIgF,WAAW,GAAGhF,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC;QACjD,IAAI,EAAE+E,WAAW,GAAG,CAAC,CAAC,IAAIC,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE;UAC3C,MAAM,8CAA8C;QACtD;QAEAhF,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAAC6G,MAAM,CAACC,WAAW,EAAE,CAAC,CAAC;QAExC,IAAI/E,IAAI,CAACvF,MAAM,IAAIuF,IAAI,CAACxF,MAAM,EAAE;UAC9BwF,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAAC6G,MAAM,CAACE,WAAW,EAAE,CAAC,CAAC;QAC1C;QAEA,IAAIH,KAAK,GAAG7E,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACH,QAAQ,CAAC,CAAC,CAACgG,OAAO,CAACvE,IAAI,CAAC;QACtD,IAAI6E,KAAK,IAAI,CAAC,CAAC,EAAE;UACf,MAAM,2BAA2B;QACnC;QAEA7E,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACH,QAAQ,CAAC,CAAC,CAACuG,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC;IAEDnB,MAAM,CAAC5K,SAAS,CAACmM,aAAa,GAAG,YAAY;MAC3C,IAAIxC,GAAG,GAAGtF,OAAO,CAACa,SAAS;MAC3B,IAAIwE,IAAI,GAAGrF,OAAO,CAACa,SAAS;MAC5B,IAAIkH,OAAO;MACX,IAAIC,QAAQ;MACZ,IAAIrB,MAAM;MAEV,IAAIhD,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC3B,IAAI9H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAI2N,KAAK,GAAGtE,KAAK,CAACrJ,CAAC,CAAC;QACpByN,OAAO,GAAGE,KAAK,CAACrD,MAAM,CAAC,CAAC;QACxBoD,QAAQ,GAAGC,KAAK,CAACtD,OAAO,CAAC,CAAC;QAE1B,IAAIW,GAAG,GAAGyC,OAAO,EAAE;UACjBzC,GAAG,GAAGyC,OAAO;QACf;QAEA,IAAI1C,IAAI,GAAG2C,QAAQ,EAAE;UACnB3C,IAAI,GAAG2C,QAAQ;QACjB;MACF;;MAEA;MACA,IAAI1C,GAAG,IAAItF,OAAO,CAACa,SAAS,EAAE;QAC5B,OAAO,IAAI;MACb;MAEA,IAAI8C,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACiJ,WAAW,IAAIC,SAAS,EAAE;QACjDxB,MAAM,GAAGhD,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACiJ,WAAW;MAC3C,CAAC,MAAM;QACLvB,MAAM,GAAG,IAAI,CAACA,MAAM;MACtB;MAEA,IAAI,CAACtB,IAAI,GAAGA,IAAI,GAAGsB,MAAM;MACzB,IAAI,CAACrB,GAAG,GAAGA,GAAG,GAAGqB,MAAM;;MAEvB;MACA,OAAO,IAAIN,KAAK,CAAC,IAAI,CAAChB,IAAI,EAAE,IAAI,CAACC,GAAG,CAAC;IACvC,CAAC;IAEDiB,MAAM,CAAC5K,SAAS,CAAC8I,YAAY,GAAG,UAAU2D,SAAS,EAAE;MACnD;MACA,IAAI/C,IAAI,GAAGrF,OAAO,CAACa,SAAS;MAC5B,IAAIiG,KAAK,GAAG,CAAC9G,OAAO,CAACa,SAAS;MAC9B,IAAIyE,GAAG,GAAGtF,OAAO,CAACa,SAAS;MAC3B,IAAIkG,MAAM,GAAG,CAAC/G,OAAO,CAACa,SAAS;MAC/B,IAAImH,QAAQ;MACZ,IAAIK,SAAS;MACb,IAAIN,OAAO;MACX,IAAIO,UAAU;MACd,IAAI3B,MAAM;MAEV,IAAIhD,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI7H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAI2N,KAAK,GAAGtE,KAAK,CAACrJ,CAAC,CAAC;QAEpB,IAAI8N,SAAS,IAAIH,KAAK,CAAC3G,KAAK,IAAI,IAAI,EAAE;UACpC2G,KAAK,CAACxD,YAAY,CAAC,CAAC;QACtB;QACAuD,QAAQ,GAAGC,KAAK,CAACtD,OAAO,CAAC,CAAC;QAC1B0D,SAAS,GAAGJ,KAAK,CAACpD,QAAQ,CAAC,CAAC;QAC5BkD,OAAO,GAAGE,KAAK,CAACrD,MAAM,CAAC,CAAC;QACxB0D,UAAU,GAAGL,KAAK,CAACnD,SAAS,CAAC,CAAC;QAE9B,IAAIO,IAAI,GAAG2C,QAAQ,EAAE;UACnB3C,IAAI,GAAG2C,QAAQ;QACjB;QAEA,IAAIlB,KAAK,GAAGuB,SAAS,EAAE;UACrBvB,KAAK,GAAGuB,SAAS;QACnB;QAEA,IAAI/C,GAAG,GAAGyC,OAAO,EAAE;UACjBzC,GAAG,GAAGyC,OAAO;QACf;QAEA,IAAIhB,MAAM,GAAGuB,UAAU,EAAE;UACvBvB,MAAM,GAAGuB,UAAU;QACrB;MACF;MAEA,IAAIC,YAAY,GAAG,IAAItI,UAAU,CAACoF,IAAI,EAAEC,GAAG,EAAEwB,KAAK,GAAGzB,IAAI,EAAE0B,MAAM,GAAGzB,GAAG,CAAC;MACxE,IAAID,IAAI,IAAIrF,OAAO,CAACa,SAAS,EAAE;QAC7B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACmB,MAAM,CAAC7B,OAAO,CAAC,CAAC;QACjC,IAAI,CAACmC,KAAK,GAAG,IAAI,CAACN,MAAM,CAAC3B,QAAQ,CAAC,CAAC;QACnC,IAAI,CAACS,GAAG,GAAG,IAAI,CAACkB,MAAM,CAAC5B,MAAM,CAAC,CAAC;QAC/B,IAAI,CAACmC,MAAM,GAAG,IAAI,CAACP,MAAM,CAAC1B,SAAS,CAAC,CAAC;MACvC;MAEA,IAAInB,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACiJ,WAAW,IAAIC,SAAS,EAAE;QACjDxB,MAAM,GAAGhD,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACiJ,WAAW;MAC3C,CAAC,MAAM;QACLvB,MAAM,GAAG,IAAI,CAACA,MAAM;MACtB;MAEA,IAAI,CAACtB,IAAI,GAAGkD,YAAY,CAACvH,CAAC,GAAG2F,MAAM;MACnC,IAAI,CAACG,KAAK,GAAGyB,YAAY,CAACvH,CAAC,GAAGuH,YAAY,CAACrH,KAAK,GAAGyF,MAAM;MACzD,IAAI,CAACrB,GAAG,GAAGiD,YAAY,CAACtH,CAAC,GAAG0F,MAAM;MAClC,IAAI,CAACI,MAAM,GAAGwB,YAAY,CAACtH,CAAC,GAAGsH,YAAY,CAACpH,MAAM,GAAGwF,MAAM;IAC7D,CAAC;IAEDJ,MAAM,CAACiC,eAAe,GAAG,UAAU7E,KAAK,EAAE;MACxC,IAAI0B,IAAI,GAAGrF,OAAO,CAACa,SAAS;MAC5B,IAAIiG,KAAK,GAAG,CAAC9G,OAAO,CAACa,SAAS;MAC9B,IAAIyE,GAAG,GAAGtF,OAAO,CAACa,SAAS;MAC3B,IAAIkG,MAAM,GAAG,CAAC/G,OAAO,CAACa,SAAS;MAC/B,IAAImH,QAAQ;MACZ,IAAIK,SAAS;MACb,IAAIN,OAAO;MACX,IAAIO,UAAU;MAEd,IAAIxM,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAI2N,KAAK,GAAGtE,KAAK,CAACrJ,CAAC,CAAC;QACpB0N,QAAQ,GAAGC,KAAK,CAACtD,OAAO,CAAC,CAAC;QAC1B0D,SAAS,GAAGJ,KAAK,CAACpD,QAAQ,CAAC,CAAC;QAC5BkD,OAAO,GAAGE,KAAK,CAACrD,MAAM,CAAC,CAAC;QACxB0D,UAAU,GAAGL,KAAK,CAACnD,SAAS,CAAC,CAAC;QAE9B,IAAIO,IAAI,GAAG2C,QAAQ,EAAE;UACnB3C,IAAI,GAAG2C,QAAQ;QACjB;QAEA,IAAIlB,KAAK,GAAGuB,SAAS,EAAE;UACrBvB,KAAK,GAAGuB,SAAS;QACnB;QAEA,IAAI/C,GAAG,GAAGyC,OAAO,EAAE;UACjBzC,GAAG,GAAGyC,OAAO;QACf;QAEA,IAAIhB,MAAM,GAAGuB,UAAU,EAAE;UACvBvB,MAAM,GAAGuB,UAAU;QACrB;MACF;MAEA,IAAIC,YAAY,GAAG,IAAItI,UAAU,CAACoF,IAAI,EAAEC,GAAG,EAAEwB,KAAK,GAAGzB,IAAI,EAAE0B,MAAM,GAAGzB,GAAG,CAAC;MAExE,OAAOiD,YAAY;IACrB,CAAC;IAEDhC,MAAM,CAAC5K,SAAS,CAACuJ,qBAAqB,GAAG,YAAY;MACnD,IAAI,IAAI,IAAI,IAAI,CAACzE,YAAY,CAAC1B,OAAO,CAAC,CAAC,EAAE;QACvC,OAAO,CAAC;MACV,CAAC,MAAM;QACL,OAAO,IAAI,CAACyH,MAAM,CAACtB,qBAAqB,CAAC,CAAC;MAC5C;IACF,CAAC;IAEDqB,MAAM,CAAC5K,SAAS,CAACoI,gBAAgB,GAAG,YAAY;MAC9C,IAAI,IAAI,CAACrD,aAAa,IAAIV,OAAO,CAACW,SAAS,EAAE;QAC3C,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,aAAa;IAC3B,CAAC;IAED6F,MAAM,CAAC5K,SAAS,CAACqI,iBAAiB,GAAG,YAAY;MAC/C,IAAIzD,IAAI,GAAG,CAAC;MACZ,IAAIoD,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI7H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAI2N,KAAK,GAAGtE,KAAK,CAACrJ,CAAC,CAAC;QACpBiG,IAAI,IAAI0H,KAAK,CAACjE,iBAAiB,CAAC,CAAC;MACnC;MAEA,IAAIzD,IAAI,IAAI,CAAC,EAAE;QACb,IAAI,CAACG,aAAa,GAAG3E,eAAe,CAACY,wBAAwB;MAC/D,CAAC,MAAM;QACL,IAAI,CAAC+D,aAAa,GAAGH,IAAI,GAAGd,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC+D,KAAK,CAAC1F,MAAM,CAAC;MAC1D;MAEA,OAAO,IAAI,CAACyC,aAAa;IAC3B,CAAC;IAED6F,MAAM,CAAC5K,SAAS,CAAC8M,eAAe,GAAG,YAAY;MAC7C,IAAI3F,IAAI,GAAG,IAAI;MACf,IAAI,IAAI,CAACa,KAAK,CAAC1F,MAAM,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC2I,WAAW,GAAG,IAAI;QACvB;MACF;MAEA,IAAI8B,KAAK,GAAG,IAAIpC,UAAU,CAAC,CAAC;MAC5B,IAAIqC,OAAO,GAAG,IAAItF,GAAG,CAAC,CAAC;MACvB,IAAIuF,WAAW,GAAG,IAAI,CAACjF,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIkF,aAAa;MACjB,IAAIC,eAAe;MACnB,IAAIC,cAAc,GAAGH,WAAW,CAACrF,YAAY,CAAC,CAAC;MAC/CwF,cAAc,CAAChG,OAAO,CAAC,UAAUrE,IAAI,EAAE;QACrCgK,KAAK,CAAC1F,IAAI,CAACtE,IAAI,CAAC;QAChBiK,OAAO,CAACrF,GAAG,CAAC5E,IAAI,CAAC;MACnB,CAAC,CAAC;MAEF,OAAOgK,KAAK,CAACzK,MAAM,KAAK,CAAC,EAAE;QACzB2K,WAAW,GAAGF,KAAK,CAACM,KAAK,CAAC,CAAC;;QAE3B;QACAH,aAAa,GAAGD,WAAW,CAACxH,QAAQ,CAAC,CAAC;QACtC,IAAIb,IAAI,GAAGsI,aAAa,CAAC5K,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,IAAI,EAAEjG,CAAC,EAAE,EAAE;UAC7B,IAAI2O,YAAY,GAAGJ,aAAa,CAACvO,CAAC,CAAC;UACnCwO,eAAe,GAAGG,YAAY,CAACtK,kBAAkB,CAACiK,WAAW,EAAE,IAAI,CAAC;;UAEpE;UACA,IAAIE,eAAe,IAAI,IAAI,IAAI,CAACH,OAAO,CAACO,GAAG,CAACJ,eAAe,CAAC,EAAE;YAC5D,IAAIK,kBAAkB,GAAGL,eAAe,CAACvF,YAAY,CAAC,CAAC;YAEvD4F,kBAAkB,CAACpG,OAAO,CAAC,UAAUrE,IAAI,EAAE;cACzCgK,KAAK,CAAC1F,IAAI,CAACtE,IAAI,CAAC;cAChBiK,OAAO,CAACrF,GAAG,CAAC5E,IAAI,CAAC;YACnB,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAI,CAACkI,WAAW,GAAG,KAAK;MAExB,IAAI+B,OAAO,CAACpI,IAAI,IAAI,IAAI,CAACoD,KAAK,CAAC1F,MAAM,EAAE;QACrC,IAAImL,sBAAsB,GAAG,CAAC;QAE9BT,OAAO,CAAC5F,OAAO,CAAC,UAAUsG,WAAW,EAAE;UACrC,IAAIA,WAAW,CAAC9H,KAAK,IAAIuB,IAAI,EAAE;YAC7BsG,sBAAsB,EAAE;UAC1B;QACF,CAAC,CAAC;QAEF,IAAIA,sBAAsB,IAAI,IAAI,CAACzF,KAAK,CAAC1F,MAAM,EAAE;UAC/C,IAAI,CAAC2I,WAAW,GAAG,IAAI;QACzB;MACF;IACF,CAAC;IAED7M,MAAM,CAACD,OAAO,GAAGyM,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAASxM,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAImM,MAAM;IACV,IAAInJ,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAASgM,aAAaA,CAACkD,MAAM,EAAE;MAC7B/C,MAAM,GAAGnM,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,IAAI,CAACkP,MAAM,GAAGA,MAAM;MAEpB,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACzI,KAAK,GAAG,EAAE;IACjB;IAEAsF,aAAa,CAACzK,SAAS,CAAC6N,OAAO,GAAG,YAAY;MAC5C,IAAIC,MAAM,GAAG,IAAI,CAACH,MAAM,CAACI,QAAQ,CAAC,CAAC;MACnC,IAAIC,KAAK,GAAG,IAAI,CAACL,MAAM,CAACnC,OAAO,CAAC,IAAI,CAAC;MACrC,IAAIvN,IAAI,GAAG,IAAI,CAAC0J,GAAG,CAACmG,MAAM,EAAEE,KAAK,CAAC;MAClC,IAAI,CAACC,YAAY,CAAChQ,IAAI,CAAC;MACvB,OAAO,IAAI,CAACiQ,SAAS;IACvB,CAAC;IAEDzD,aAAa,CAACzK,SAAS,CAAC2H,GAAG,GAAG,UAAUoG,QAAQ,EAAEI,UAAU,EAAEzC,OAAO,EAAEJ,UAAU,EAAEC,UAAU,EAAE;MAC7F;MACA,IAAIG,OAAO,IAAI,IAAI,IAAIJ,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;QAC/D,IAAIwC,QAAQ,IAAI,IAAI,EAAE;UACpB,MAAM,gBAAgB;QACxB;QACA,IAAII,UAAU,IAAI,IAAI,EAAE;UACtB,MAAM,sBAAsB;QAC9B;QACA,IAAI,IAAI,CAACP,MAAM,CAACnC,OAAO,CAACsC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;UACtC,MAAM,kCAAkC;QAC1C;QAEA,IAAI,CAACH,MAAM,CAACvG,IAAI,CAAC0G,QAAQ,CAAC;QAE1B,IAAIA,QAAQ,CAAClD,MAAM,IAAI,IAAI,EAAE;UAC3B,MAAM,uBAAuB;QAC/B;QACA,IAAIsD,UAAU,CAACxI,KAAK,IAAI,IAAI,EAAE;UAC5B,MAAM,sBAAsB;QAC9B;QAEAoI,QAAQ,CAAClD,MAAM,GAAGsD,UAAU;QAC5BA,UAAU,CAACxI,KAAK,GAAGoI,QAAQ;QAE3B,OAAOA,QAAQ;MACjB,CAAC,MAAM;QACL;QACAxC,UAAU,GAAGG,OAAO;QACpBJ,UAAU,GAAG6C,UAAU;QACvBzC,OAAO,GAAGqC,QAAQ;QAClB,IAAIK,WAAW,GAAG9C,UAAU,CAACjI,QAAQ,CAAC,CAAC;QACvC,IAAIgL,WAAW,GAAG9C,UAAU,CAAClI,QAAQ,CAAC,CAAC;QAEvC,IAAI,EAAE+K,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACjL,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UACnE,MAAM,+BAA+B;QACvC;QACA,IAAI,EAAEkL,WAAW,IAAI,IAAI,IAAIA,WAAW,CAAClL,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UACnE,MAAM,+BAA+B;QACvC;QAEA,IAAIiL,WAAW,IAAIC,WAAW,EAAE;UAC9B3C,OAAO,CAACtJ,YAAY,GAAG,KAAK;UAC5B,OAAOgM,WAAW,CAACzG,GAAG,CAAC+D,OAAO,EAAEJ,UAAU,EAAEC,UAAU,CAAC;QACzD,CAAC,MAAM;UACLG,OAAO,CAACtJ,YAAY,GAAG,IAAI;;UAE3B;UACAsJ,OAAO,CAAChK,MAAM,GAAG4J,UAAU;UAC3BI,OAAO,CAAC/J,MAAM,GAAG4J,UAAU;;UAE3B;UACA,IAAI,IAAI,CAACpG,KAAK,CAACsG,OAAO,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;YACpC,MAAM,wCAAwC;UAChD;UAEA,IAAI,CAACvG,KAAK,CAACkC,IAAI,CAACqE,OAAO,CAAC;;UAExB;UACA,IAAI,EAAEA,OAAO,CAAChK,MAAM,IAAI,IAAI,IAAIgK,OAAO,CAAC/J,MAAM,IAAI,IAAI,CAAC,EAAE;YACvD,MAAM,oCAAoC;UAC5C;UAEA,IAAI,EAAE+J,OAAO,CAAChK,MAAM,CAACyD,KAAK,CAACsG,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAIA,OAAO,CAAC/J,MAAM,CAACwD,KAAK,CAACsG,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACjG,MAAM,sDAAsD;UAC9D;UAEAA,OAAO,CAAChK,MAAM,CAACyD,KAAK,CAACkC,IAAI,CAACqE,OAAO,CAAC;UAClCA,OAAO,CAAC/J,MAAM,CAACwD,KAAK,CAACkC,IAAI,CAACqE,OAAO,CAAC;UAElC,OAAOA,OAAO;QAChB;MACF;IACF,CAAC;IAEDjB,aAAa,CAACzK,SAAS,CAAC2L,MAAM,GAAG,UAAU2C,IAAI,EAAE;MAC/C,IAAIA,IAAI,YAAY1D,MAAM,EAAE;QAC1B,IAAI3H,KAAK,GAAGqL,IAAI;QAChB,IAAIrL,KAAK,CAACE,eAAe,CAAC,CAAC,IAAI,IAAI,EAAE;UACnC,MAAM,6BAA6B;QACrC;QACA,IAAI,EAAEF,KAAK,IAAI,IAAI,CAACiL,SAAS,IAAIjL,KAAK,CAAC4H,MAAM,IAAI,IAAI,IAAI5H,KAAK,CAAC4H,MAAM,CAAC/F,YAAY,IAAI,IAAI,CAAC,EAAE;UAC3F,MAAM,sBAAsB;QAC9B;;QAEA;QACA,IAAI+G,gBAAgB,GAAG,EAAE;QAEzBA,gBAAgB,GAAGA,gBAAgB,CAAC0C,MAAM,CAACtL,KAAK,CAACwC,QAAQ,CAAC,CAAC,CAAC;QAE5D,IAAIyB,IAAI;QACR,IAAI/G,CAAC,GAAG0L,gBAAgB,CAACvJ,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BuI,IAAI,GAAG2E,gBAAgB,CAAClN,CAAC,CAAC;UAC1BsE,KAAK,CAAC0I,MAAM,CAACzE,IAAI,CAAC;QACpB;;QAEA;QACA,IAAIsH,gBAAgB,GAAG,EAAE;QAEzBA,gBAAgB,GAAGA,gBAAgB,CAACD,MAAM,CAACtL,KAAK,CAACgF,QAAQ,CAAC,CAAC,CAAC;QAE5D,IAAIlF,IAAI;QACR5C,CAAC,GAAGqO,gBAAgB,CAAClM,MAAM;QAC3B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BoE,IAAI,GAAGyL,gBAAgB,CAAC7P,CAAC,CAAC;UAC1BsE,KAAK,CAAC0I,MAAM,CAAC5I,IAAI,CAAC;QACpB;;QAEA;QACA,IAAIE,KAAK,IAAI,IAAI,CAACiL,SAAS,EAAE;UAC3B,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC;QACzB;;QAEA;QACA,IAAIlC,KAAK,GAAG,IAAI,CAAC6B,MAAM,CAACnC,OAAO,CAACxI,KAAK,CAAC;QACtC,IAAI,CAAC2K,MAAM,CAAC5B,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;QAE5B;QACA9I,KAAK,CAAC4H,MAAM,GAAG,IAAI;MACrB,CAAC,MAAM,IAAIyD,IAAI,YAAY7M,KAAK,EAAE;QAChCyF,IAAI,GAAGoH,IAAI;QACX,IAAIpH,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,CAACA,IAAI,CAAC9E,YAAY,EAAE;UACtB,MAAM,0BAA0B;QAClC;QACA,IAAI,EAAE8E,IAAI,CAACxF,MAAM,IAAI,IAAI,IAAIwF,IAAI,CAACvF,MAAM,IAAI,IAAI,CAAC,EAAE;UACjD,MAAM,+BAA+B;QACvC;;QAEA;;QAEA,IAAI,EAAEuF,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIA,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;UACrF,MAAM,8CAA8C;QACtD;QAEA,IAAI6E,KAAK,GAAG7E,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC;QAC3CA,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAAC6G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAClCA,KAAK,GAAG7E,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC;QACvCA,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAAC6G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;QAElC;;QAEA,IAAI,EAAE7E,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UAC/E,MAAM,kDAAkD;QAC1D;QACA,IAAI+D,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;UACjE,MAAM,yCAAyC;QACjD;QAEA,IAAI6E,KAAK,GAAG7E,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAACsG,OAAO,CAACvE,IAAI,CAAC;QACnEA,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAAC6G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC5D;IACF,CAAC;IAEDtB,aAAa,CAACzK,SAAS,CAAC8I,YAAY,GAAG,YAAY;MACjD,IAAI,CAACoF,SAAS,CAACpF,YAAY,CAAC,IAAI,CAAC;IACnC,CAAC;IAED2B,aAAa,CAACzK,SAAS,CAACyO,SAAS,GAAG,YAAY;MAC9C,OAAO,IAAI,CAACb,MAAM;IACpB,CAAC;IAEDnD,aAAa,CAACzK,SAAS,CAAC0O,WAAW,GAAG,YAAY;MAChD,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,EAAE;QACzB,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIhB,MAAM,GAAG,IAAI,CAACa,SAAS,CAAC,CAAC;QAC7B,IAAItO,CAAC,GAAGyN,MAAM,CAACtL,MAAM;QACrB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BiQ,QAAQ,GAAGA,QAAQ,CAACL,MAAM,CAACX,MAAM,CAACjP,CAAC,CAAC,CAACsJ,QAAQ,CAAC,CAAC,CAAC;QAClD;QACA,IAAI,CAAC0G,QAAQ,GAAGC,QAAQ;MAC1B;MACA,OAAO,IAAI,CAACD,QAAQ;IACtB,CAAC;IAEDlE,aAAa,CAACzK,SAAS,CAAC6O,aAAa,GAAG,YAAY;MAClD,IAAI,CAACF,QAAQ,GAAG,IAAI;IACtB,CAAC;IAEDlE,aAAa,CAACzK,SAAS,CAAC8O,aAAa,GAAG,YAAY;MAClD,IAAI,CAACC,QAAQ,GAAG,IAAI;IACtB,CAAC;IAEDtE,aAAa,CAACzK,SAAS,CAACgP,+BAA+B,GAAG,YAAY;MACpE,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACxC,CAAC;IAEDxE,aAAa,CAACzK,SAAS,CAACkP,WAAW,GAAG,YAAY;MAChD,IAAI,IAAI,CAACH,QAAQ,IAAI,IAAI,EAAE;QACzB,IAAI9H,QAAQ,GAAG,EAAE;QACjB,IAAI2G,MAAM,GAAG,IAAI,CAACa,SAAS,CAAC,CAAC;QAC7B,IAAItO,CAAC,GAAGyN,MAAM,CAACtL,MAAM;QACrB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiP,MAAM,CAACtL,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACtCsI,QAAQ,GAAGA,QAAQ,CAACsH,MAAM,CAACX,MAAM,CAACjP,CAAC,CAAC,CAAC8G,QAAQ,CAAC,CAAC,CAAC;QAClD;QAEAwB,QAAQ,GAAGA,QAAQ,CAACsH,MAAM,CAAC,IAAI,CAACpJ,KAAK,CAAC;QAEtC,IAAI,CAAC4J,QAAQ,GAAG9H,QAAQ;MAC1B;MACA,OAAO,IAAI,CAAC8H,QAAQ;IACtB,CAAC;IAEDtE,aAAa,CAACzK,SAAS,CAACmP,6BAA6B,GAAG,YAAY;MAClE,OAAO,IAAI,CAACF,0BAA0B;IACxC,CAAC;IAEDxE,aAAa,CAACzK,SAAS,CAACoP,6BAA6B,GAAG,UAAUR,QAAQ,EAAE;MAC1E,IAAI,IAAI,CAACK,0BAA0B,IAAI,IAAI,EAAE;QAC3C,MAAM,eAAe;MACvB;MAEA,IAAI,CAACA,0BAA0B,GAAGL,QAAQ;IAC5C,CAAC;IAEDnE,aAAa,CAACzK,SAAS,CAACoD,OAAO,GAAG,YAAY;MAC5C,OAAO,IAAI,CAAC8K,SAAS;IACvB,CAAC;IAEDzD,aAAa,CAACzK,SAAS,CAACiO,YAAY,GAAG,UAAUhL,KAAK,EAAE;MACtD,IAAIA,KAAK,CAACE,eAAe,CAAC,CAAC,IAAI,IAAI,EAAE;QACnC,MAAM,6BAA6B;MACrC;MAEA,IAAI,CAAC+K,SAAS,GAAGjL,KAAK;MACtB;MACA,IAAIA,KAAK,CAAC4H,MAAM,IAAI,IAAI,EAAE;QACxB5H,KAAK,CAAC4H,MAAM,GAAG,IAAI,CAAC8C,MAAM,CAACnC,OAAO,CAAC,WAAW,CAAC;MACjD;IACF,CAAC;IAEDf,aAAa,CAACzK,SAAS,CAACqP,SAAS,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAC1B,MAAM;IACpB,CAAC;IAEDlD,aAAa,CAACzK,SAAS,CAACsP,oBAAoB,GAAG,UAAUC,SAAS,EAAEC,UAAU,EAAE;MAC9E,IAAI,EAAED,SAAS,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,CAAC,EAAE;QAC9C,MAAM,eAAe;MACvB;MAEA,IAAID,SAAS,IAAIC,UAAU,EAAE;QAC3B,OAAO,IAAI;MACb;MACA;MACA,IAAIC,UAAU,GAAGF,SAAS,CAAClM,QAAQ,CAAC,CAAC;MACrC,IAAI8K,UAAU;MAEd,GAAG;QACDA,UAAU,GAAGsB,UAAU,CAACnM,SAAS,CAAC,CAAC;QAEnC,IAAI6K,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;QAEA,IAAIA,UAAU,IAAIqB,UAAU,EAAE;UAC5B,OAAO,IAAI;QACb;QAEAC,UAAU,GAAGtB,UAAU,CAAC9K,QAAQ,CAAC,CAAC;QAClC,IAAIoM,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;MACF,CAAC,QAAQ,IAAI;MACb;MACAA,UAAU,GAAGD,UAAU,CAACnM,QAAQ,CAAC,CAAC;MAElC,GAAG;QACD8K,UAAU,GAAGsB,UAAU,CAACnM,SAAS,CAAC,CAAC;QAEnC,IAAI6K,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;QAEA,IAAIA,UAAU,IAAIoB,SAAS,EAAE;UAC3B,OAAO,IAAI;QACb;QAEAE,UAAU,GAAGtB,UAAU,CAAC9K,QAAQ,CAAC,CAAC;QAClC,IAAIoM,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;MACF,CAAC,QAAQ,IAAI;MAEb,OAAO,KAAK;IACd,CAAC;IAEDhF,aAAa,CAACzK,SAAS,CAAC0P,yBAAyB,GAAG,YAAY;MAC9D,IAAIxI,IAAI;MACR,IAAIoE,UAAU;MACd,IAAIC,UAAU;MACd,IAAIoE,mBAAmB;MACvB,IAAIC,mBAAmB;MAEvB,IAAIzK,KAAK,GAAG,IAAI,CAAC+J,WAAW,CAAC,CAAC;MAC9B,IAAI/O,CAAC,GAAGgF,KAAK,CAAC7C,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BuI,IAAI,GAAG/B,KAAK,CAACxG,CAAC,CAAC;QAEf2M,UAAU,GAAGpE,IAAI,CAACxF,MAAM;QACxB6J,UAAU,GAAGrE,IAAI,CAACvF,MAAM;QACxBuF,IAAI,CAACzE,GAAG,GAAG,IAAI;QACfyE,IAAI,CAACvE,WAAW,GAAG2I,UAAU;QAC7BpE,IAAI,CAACrE,WAAW,GAAG0I,UAAU;QAE7B,IAAID,UAAU,IAAIC,UAAU,EAAE;UAC5BrE,IAAI,CAACzE,GAAG,GAAG6I,UAAU,CAACjI,QAAQ,CAAC,CAAC;UAChC;QACF;QAEAsM,mBAAmB,GAAGrE,UAAU,CAACjI,QAAQ,CAAC,CAAC;QAE3C,OAAO6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;UACvByE,IAAI,CAACrE,WAAW,GAAG0I,UAAU;UAC7BqE,mBAAmB,GAAGrE,UAAU,CAAClI,QAAQ,CAAC,CAAC;UAE3C,OAAO6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;YACvB,IAAImN,mBAAmB,IAAID,mBAAmB,EAAE;cAC9CzI,IAAI,CAACzE,GAAG,GAAGmN,mBAAmB;cAC9B;YACF;YAEA,IAAIA,mBAAmB,IAAI,IAAI,CAAC1B,SAAS,EAAE;cACzC;YACF;YAEA,IAAIhH,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;cACpB,MAAM,eAAe;YACvB;YACAyE,IAAI,CAACrE,WAAW,GAAG+M,mBAAmB,CAACtM,SAAS,CAAC,CAAC;YAClDsM,mBAAmB,GAAG1I,IAAI,CAACrE,WAAW,CAACQ,QAAQ,CAAC,CAAC;UACnD;UAEA,IAAIsM,mBAAmB,IAAI,IAAI,CAACzB,SAAS,EAAE;YACzC;UACF;UAEA,IAAIhH,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;YACpByE,IAAI,CAACvE,WAAW,GAAGgN,mBAAmB,CAACrM,SAAS,CAAC,CAAC;YAClDqM,mBAAmB,GAAGzI,IAAI,CAACvE,WAAW,CAACU,QAAQ,CAAC,CAAC;UACnD;QACF;QAEA,IAAI6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;UACpB,MAAM,eAAe;QACvB;MACF;IACF,CAAC;IAEDgI,aAAa,CAACzK,SAAS,CAAC6P,wBAAwB,GAAG,UAAUN,SAAS,EAAEC,UAAU,EAAE;MAClF,IAAID,SAAS,IAAIC,UAAU,EAAE;QAC3B,OAAOD,SAAS,CAAClM,QAAQ,CAAC,CAAC;MAC7B;MACA,IAAIyM,eAAe,GAAGP,SAAS,CAAClM,QAAQ,CAAC,CAAC;MAE1C,GAAG;QACD,IAAIyM,eAAe,IAAI,IAAI,EAAE;UAC3B;QACF;QACA,IAAIC,gBAAgB,GAAGP,UAAU,CAACnM,QAAQ,CAAC,CAAC;QAE5C,GAAG;UACD,IAAI0M,gBAAgB,IAAI,IAAI,EAAE;YAC5B;UACF;UAEA,IAAIA,gBAAgB,IAAID,eAAe,EAAE;YACvC,OAAOC,gBAAgB;UACzB;UACAA,gBAAgB,GAAGA,gBAAgB,CAACzM,SAAS,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC;QAC5D,CAAC,QAAQ,IAAI;QAEbyM,eAAe,GAAGA,eAAe,CAACxM,SAAS,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC;MAC1D,CAAC,QAAQ,IAAI;MAEb,OAAOyM,eAAe;IACxB,CAAC;IAEDrF,aAAa,CAACzK,SAAS,CAACgQ,uBAAuB,GAAG,UAAU/M,KAAK,EAAEgN,KAAK,EAAE;MACxE,IAAIhN,KAAK,IAAI,IAAI,IAAIgN,KAAK,IAAI,IAAI,EAAE;QAClChN,KAAK,GAAG,IAAI,CAACiL,SAAS;QACtB+B,KAAK,GAAG,CAAC;MACX;MACA,IAAIlN,IAAI;MAER,IAAIiF,KAAK,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC;MAC5B,IAAI9H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;QACfoE,IAAI,CAACkC,kBAAkB,GAAGgL,KAAK;QAE/B,IAAIlN,IAAI,CAAC4C,KAAK,IAAI,IAAI,EAAE;UACtB,IAAI,CAACqK,uBAAuB,CAACjN,IAAI,CAAC4C,KAAK,EAAEsK,KAAK,GAAG,CAAC,CAAC;QACrD;MACF;IACF,CAAC;IAEDxF,aAAa,CAACzK,SAAS,CAACkQ,mBAAmB,GAAG,YAAY;MACxD,IAAIhJ,IAAI;MAER,IAAI/G,CAAC,GAAG,IAAI,CAACgF,KAAK,CAAC7C,MAAM;MACzB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BuI,IAAI,GAAG,IAAI,CAAC/B,KAAK,CAACxG,CAAC,CAAC;QAEpB,IAAI,IAAI,CAAC2Q,oBAAoB,CAACpI,IAAI,CAACxF,MAAM,EAAEwF,IAAI,CAACvF,MAAM,CAAC,EAAE;UACvD,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd,CAAC;IAEDvD,MAAM,CAACD,OAAO,GAAGsM,aAAa;;IAE9B;EAAM,CAAC,IACP;EACA,KAAO,UAASrM,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAE5C,SAAS0R,iBAAiBA,CAAA,EAAG,CAAC;;IAE9B;IACA,KAAK,IAAIlO,IAAI,IAAI7B,eAAe,EAAE;MAChC+P,iBAAiB,CAAClO,IAAI,CAAC,GAAG7B,eAAe,CAAC6B,IAAI,CAAC;IACjD;IAEAkO,iBAAiB,CAACC,cAAc,GAAG,IAAI;IAEvCD,iBAAiB,CAACE,mBAAmB,GAAG,EAAE;IAC1CF,iBAAiB,CAACG,uBAAuB,GAAG,IAAI;IAChDH,iBAAiB,CAACI,0BAA0B,GAAG,MAAM;IACrDJ,iBAAiB,CAACK,wBAAwB,GAAG,GAAG;IAChDL,iBAAiB,CAACM,iCAAiC,GAAG,GAAG;IACzDN,iBAAiB,CAACO,4BAA4B,GAAG,GAAG;IACpDP,iBAAiB,CAACQ,qCAAqC,GAAG,GAAG;IAC7DR,iBAAiB,CAACS,+CAA+C,GAAG,IAAI;IACxET,iBAAiB,CAACU,6CAA6C,GAAG,IAAI;IACtEV,iBAAiB,CAACW,kCAAkC,GAAG,GAAG;IAC1DX,iBAAiB,CAACY,yBAAyB,GAAG,IAAI;IAClDZ,iBAAiB,CAACa,2BAA2B,GAAG,IAAI;IACpDb,iBAAiB,CAACc,2BAA2B,GAAG,IAAI;IACpDd,iBAAiB,CAACe,iCAAiC,GAAG,KAAK;IAC3Df,iBAAiB,CAACgB,qBAAqB,GAAGhB,iBAAiB,CAACe,iCAAiC,GAAG,CAAC;IACjGf,iBAAiB,CAACiB,kBAAkB,GAAGjB,iBAAiB,CAACE,mBAAmB,GAAG,IAAI;IACnFF,iBAAiB,CAACkB,wBAAwB,GAAG,GAAG;IAChDlB,iBAAiB,CAACmB,kCAAkC,GAAG,GAAG;IAC1DnB,iBAAiB,CAAClP,eAAe,GAAG,CAAC;IACrCkP,iBAAiB,CAACoB,6BAA6B,GAAG,EAAE;IAEpDnT,MAAM,CAACD,OAAO,GAAGgS,iBAAiB;;IAElC;EAAM,CAAC,IACP;EACA,KAAO,UAAS/R,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;AACA;AACA;AACA;AACA;AACA;IAEA,IAAIiM,KAAK,GAAGjM,mBAAmB,CAAC,EAAE,CAAC;IAEnC,SAAS8C,SAASA,CAAA,EAAG,CAAC;;IAEtB;AACA;AACA;AACA;AACA;AACA;AACA;IACAA,SAAS,CAACiQ,oBAAoB,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,gBAAgB,EAAE;MACxF,IAAI,CAACH,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC,EAAE;QAC5B,MAAM,eAAe;MACvB;MAEA,IAAII,UAAU,GAAG,IAAIrO,KAAK,CAAC,CAAC,CAAC;MAE7B,IAAI,CAACsO,mCAAmC,CAACN,KAAK,EAAEC,KAAK,EAAEI,UAAU,CAAC;MAElEH,aAAa,CAAC,CAAC,CAAC,GAAG7N,IAAI,CAACkO,GAAG,CAACP,KAAK,CAACvI,QAAQ,CAAC,CAAC,EAAEwI,KAAK,CAACxI,QAAQ,CAAC,CAAC,CAAC,GAAGpF,IAAI,CAACmO,GAAG,CAACR,KAAK,CAACpM,CAAC,EAAEqM,KAAK,CAACrM,CAAC,CAAC;MAC5FsM,aAAa,CAAC,CAAC,CAAC,GAAG7N,IAAI,CAACkO,GAAG,CAACP,KAAK,CAACtI,SAAS,CAAC,CAAC,EAAEuI,KAAK,CAACvI,SAAS,CAAC,CAAC,CAAC,GAAGrF,IAAI,CAACmO,GAAG,CAACR,KAAK,CAACnM,CAAC,EAAEoM,KAAK,CAACpM,CAAC,CAAC;;MAE9F;MACA,IAAImM,KAAK,CAAC1H,IAAI,CAAC,CAAC,IAAI2H,KAAK,CAAC3H,IAAI,CAAC,CAAC,IAAI0H,KAAK,CAACvI,QAAQ,CAAC,CAAC,IAAIwI,KAAK,CAACxI,QAAQ,CAAC,CAAC,EAAE;QACxE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACIyI,aAAa,CAAC,CAAC,CAAC,IAAI7N,IAAI,CAACkO,GAAG,CAACN,KAAK,CAAC3H,IAAI,CAAC,CAAC,GAAG0H,KAAK,CAAC1H,IAAI,CAAC,CAAC,EAAE0H,KAAK,CAACvI,QAAQ,CAAC,CAAC,GAAGwI,KAAK,CAACxI,QAAQ,CAAC,CAAC,CAAC;MAChG,CAAC,MAAM,IAAIwI,KAAK,CAAC3H,IAAI,CAAC,CAAC,IAAI0H,KAAK,CAAC1H,IAAI,CAAC,CAAC,IAAI2H,KAAK,CAACxI,QAAQ,CAAC,CAAC,IAAIuI,KAAK,CAACvI,QAAQ,CAAC,CAAC,EAAE;QAC/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACIyI,aAAa,CAAC,CAAC,CAAC,IAAI7N,IAAI,CAACkO,GAAG,CAACP,KAAK,CAAC1H,IAAI,CAAC,CAAC,GAAG2H,KAAK,CAAC3H,IAAI,CAAC,CAAC,EAAE2H,KAAK,CAACxI,QAAQ,CAAC,CAAC,GAAGuI,KAAK,CAACvI,QAAQ,CAAC,CAAC,CAAC;MAChG;MACA,IAAIuI,KAAK,CAACzH,IAAI,CAAC,CAAC,IAAI0H,KAAK,CAAC1H,IAAI,CAAC,CAAC,IAAIyH,KAAK,CAACtI,SAAS,CAAC,CAAC,IAAIuI,KAAK,CAACvI,SAAS,CAAC,CAAC,EAAE;QAC1E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACIwI,aAAa,CAAC,CAAC,CAAC,IAAI7N,IAAI,CAACkO,GAAG,CAACN,KAAK,CAAC1H,IAAI,CAAC,CAAC,GAAGyH,KAAK,CAACzH,IAAI,CAAC,CAAC,EAAEyH,KAAK,CAACtI,SAAS,CAAC,CAAC,GAAGuI,KAAK,CAACvI,SAAS,CAAC,CAAC,CAAC;MAClG,CAAC,MAAM,IAAIuI,KAAK,CAAC1H,IAAI,CAAC,CAAC,IAAIyH,KAAK,CAACzH,IAAI,CAAC,CAAC,IAAI0H,KAAK,CAACvI,SAAS,CAAC,CAAC,IAAIsI,KAAK,CAACtI,SAAS,CAAC,CAAC,EAAE;QACjF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACIwI,aAAa,CAAC,CAAC,CAAC,IAAI7N,IAAI,CAACkO,GAAG,CAACP,KAAK,CAACzH,IAAI,CAAC,CAAC,GAAG0H,KAAK,CAAC1H,IAAI,CAAC,CAAC,EAAE0H,KAAK,CAACvI,SAAS,CAAC,CAAC,GAAGsI,KAAK,CAACtI,SAAS,CAAC,CAAC,CAAC;MAClG;;MAEA;MACA,IAAI+I,KAAK,GAAGpO,IAAI,CAACC,GAAG,CAAC,CAAC2N,KAAK,CAACtN,UAAU,CAAC,CAAC,GAAGqN,KAAK,CAACrN,UAAU,CAAC,CAAC,KAAKsN,KAAK,CAACvN,UAAU,CAAC,CAAC,GAAGsN,KAAK,CAACtN,UAAU,CAAC,CAAC,CAAC,CAAC;MAC3G;MACA,IAAIuN,KAAK,CAACtN,UAAU,CAAC,CAAC,KAAKqN,KAAK,CAACrN,UAAU,CAAC,CAAC,IAAIsN,KAAK,CAACvN,UAAU,CAAC,CAAC,KAAKsN,KAAK,CAACtN,UAAU,CAAC,CAAC,EAAE;QAC1F;QACA+N,KAAK,GAAG,GAAG;MACb;MAEA,IAAIC,OAAO,GAAGD,KAAK,GAAGP,aAAa,CAAC,CAAC,CAAC;MACtC,IAAIS,OAAO,GAAGT,aAAa,CAAC,CAAC,CAAC,GAAGO,KAAK;MACtC,IAAIP,aAAa,CAAC,CAAC,CAAC,GAAGS,OAAO,EAAE;QAC9BA,OAAO,GAAGT,aAAa,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLQ,OAAO,GAAGR,aAAa,CAAC,CAAC,CAAC;MAC5B;MACA;MACA;MACAA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC,IAAIM,OAAO,GAAG,CAAC,GAAGR,gBAAgB,CAAC;MACxED,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC,IAAIK,OAAO,GAAG,CAAC,GAAGP,gBAAgB,CAAC;IAC1E,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACArQ,SAAS,CAACwQ,mCAAmC,GAAG,UAAUN,KAAK,EAAEC,KAAK,EAAEI,UAAU,EAAE;MAClF,IAAIL,KAAK,CAACtN,UAAU,CAAC,CAAC,GAAGuN,KAAK,CAACvN,UAAU,CAAC,CAAC,EAAE;QAC3C2N,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpB,CAAC,MAAM;QACLA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACnB;MAEA,IAAIL,KAAK,CAACrN,UAAU,CAAC,CAAC,GAAGsN,KAAK,CAACtN,UAAU,CAAC,CAAC,EAAE;QAC3C0N,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpB,CAAC,MAAM;QACLA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACAvQ,SAAS,CAAC8Q,gBAAgB,GAAG,UAAUZ,KAAK,EAAEC,KAAK,EAAEY,MAAM,EAAE;MAC3D;MACA,IAAIC,GAAG,GAAGd,KAAK,CAACtN,UAAU,CAAC,CAAC;MAC5B,IAAIqO,GAAG,GAAGf,KAAK,CAACrN,UAAU,CAAC,CAAC;MAC5B,IAAIqO,GAAG,GAAGf,KAAK,CAACvN,UAAU,CAAC,CAAC;MAC5B,IAAIuO,GAAG,GAAGhB,KAAK,CAACtN,UAAU,CAAC,CAAC;;MAE5B;MACA,IAAIqN,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC,EAAE;QAC3BY,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;QACfD,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;QACfF,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;QACfH,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;QACf,OAAO,IAAI;MACb;MACA;MACA,IAAIC,SAAS,GAAGlB,KAAK,CAAC1H,IAAI,CAAC,CAAC;MAC5B,IAAI6I,SAAS,GAAGnB,KAAK,CAACzH,IAAI,CAAC,CAAC;MAC5B,IAAI6I,UAAU,GAAGpB,KAAK,CAACvI,QAAQ,CAAC,CAAC;MACjC,IAAI4J,YAAY,GAAGrB,KAAK,CAAC1H,IAAI,CAAC,CAAC;MAC/B,IAAIgJ,YAAY,GAAGtB,KAAK,CAACtI,SAAS,CAAC,CAAC;MACpC,IAAI6J,aAAa,GAAGvB,KAAK,CAACvI,QAAQ,CAAC,CAAC;MACpC,IAAI+J,UAAU,GAAGxB,KAAK,CAACyB,YAAY,CAAC,CAAC;MACrC,IAAIC,WAAW,GAAG1B,KAAK,CAAC2B,aAAa,CAAC,CAAC;MACvC;MACA,IAAIC,SAAS,GAAG3B,KAAK,CAAC3H,IAAI,CAAC,CAAC;MAC5B,IAAIuJ,SAAS,GAAG5B,KAAK,CAAC1H,IAAI,CAAC,CAAC;MAC5B,IAAIuJ,UAAU,GAAG7B,KAAK,CAACxI,QAAQ,CAAC,CAAC;MACjC,IAAIsK,YAAY,GAAG9B,KAAK,CAAC3H,IAAI,CAAC,CAAC;MAC/B,IAAI0J,YAAY,GAAG/B,KAAK,CAACvI,SAAS,CAAC,CAAC;MACpC,IAAIuK,aAAa,GAAGhC,KAAK,CAACxI,QAAQ,CAAC,CAAC;MACpC,IAAIyK,UAAU,GAAGjC,KAAK,CAACwB,YAAY,CAAC,CAAC;MACrC,IAAIU,WAAW,GAAGlC,KAAK,CAAC0B,aAAa,CAAC,CAAC;;MAEvC;MACA,IAAIS,eAAe,GAAG,KAAK;MAC3B,IAAIC,eAAe,GAAG,KAAK;;MAE3B;MACA,IAAIvB,GAAG,KAAKE,GAAG,EAAE;QACf,IAAID,GAAG,GAAGE,GAAG,EAAE;UACbJ,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;UACfD,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;UACrBN,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;UACfH,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;UACxB,OAAO,KAAK;QACd,CAAC,MAAM,IAAIjB,GAAG,GAAGE,GAAG,EAAE;UACpBJ,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;UACfD,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;UACxBT,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;UACfH,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;UACrB,OAAO,KAAK;QACd,CAAC,MAAM;UACL;QAAA;MAEJ;MACA;MAAA,KACK,IAAId,GAAG,KAAKE,GAAG,EAAE;QAClB,IAAIH,GAAG,GAAGE,GAAG,EAAE;UACbH,MAAM,CAAC,CAAC,CAAC,GAAGK,SAAS;UACrBL,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;UACfF,MAAM,CAAC,CAAC,CAAC,GAAGiB,UAAU;UACtBjB,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;UACf,OAAO,KAAK;QACd,CAAC,MAAM,IAAIH,GAAG,GAAGE,GAAG,EAAE;UACpBH,MAAM,CAAC,CAAC,CAAC,GAAGO,UAAU;UACtBP,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;UACfF,MAAM,CAAC,CAAC,CAAC,GAAGe,SAAS;UACrBf,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;UACf,OAAO,KAAK;QACd,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC,MAAM;QACL;QACA,IAAIqB,MAAM,GAAGtC,KAAK,CAACjM,MAAM,GAAGiM,KAAK,CAAClM,KAAK;QACvC,IAAIyO,MAAM,GAAGtC,KAAK,CAAClM,MAAM,GAAGkM,KAAK,CAACnM,KAAK;;QAEvC;QACA,IAAI0O,UAAU,GAAG,CAACvB,GAAG,GAAGF,GAAG,KAAKC,GAAG,GAAGF,GAAG,CAAC;QAC1C,IAAI2B,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAIC,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;;QAExB;QACA,IAAI,CAACR,MAAM,KAAKE,UAAU,EAAE;UAC1B,IAAI1B,GAAG,GAAGE,GAAG,EAAE;YACbH,MAAM,CAAC,CAAC,CAAC,GAAGQ,YAAY;YACxBR,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;YACxBc,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLvB,MAAM,CAAC,CAAC,CAAC,GAAGO,UAAU;YACtBP,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;YACrBiB,eAAe,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIE,MAAM,KAAKE,UAAU,EAAE;UAChC,IAAI1B,GAAG,GAAGE,GAAG,EAAE;YACbH,MAAM,CAAC,CAAC,CAAC,GAAGK,SAAS;YACrBL,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;YACrBiB,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLvB,MAAM,CAAC,CAAC,CAAC,GAAGU,aAAa;YACzBV,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;YACxBc,eAAe,GAAG,IAAI;UACxB;QACF;;QAEA;QACA,IAAI,CAACG,MAAM,KAAKC,UAAU,EAAE;UAC1B,IAAIxB,GAAG,GAAGF,GAAG,EAAE;YACbD,MAAM,CAAC,CAAC,CAAC,GAAGkB,YAAY;YACxBlB,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;YACxBK,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLxB,MAAM,CAAC,CAAC,CAAC,GAAGiB,UAAU;YACtBjB,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;YACrBQ,eAAe,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIE,MAAM,KAAKC,UAAU,EAAE;UAChC,IAAIxB,GAAG,GAAGF,GAAG,EAAE;YACbD,MAAM,CAAC,CAAC,CAAC,GAAGe,SAAS;YACrBf,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;YACrBQ,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLxB,MAAM,CAAC,CAAC,CAAC,GAAGoB,aAAa;YACzBpB,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;YACxBK,eAAe,GAAG,IAAI;UACxB;QACF;;QAEA;QACA,IAAID,eAAe,IAAIC,eAAe,EAAE;UACtC,OAAO,KAAK;QACd;;QAEA;QACA,IAAIvB,GAAG,GAAGE,GAAG,EAAE;UACb,IAAID,GAAG,GAAGE,GAAG,EAAE;YACbwB,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACrEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACvE,CAAC,MAAM;YACLC,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAAC,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACtEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAAC,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACxE;QACF,CAAC,MAAM;UACL,IAAIzB,GAAG,GAAGE,GAAG,EAAE;YACbwB,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAAC,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACtEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAAC,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACxE,CAAC,MAAM;YACLC,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACrEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACvE;QACF;QACA;QACA,IAAI,CAACJ,eAAe,EAAE;UACpB,QAAQK,kBAAkB;YACxB,KAAK,CAAC;cACJG,WAAW,GAAGzB,SAAS;cACvBwB,WAAW,GAAG7B,GAAG,GAAG,CAACY,WAAW,GAAGc,UAAU;cAC7C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGpB,aAAa;cAC3BqB,WAAW,GAAG7B,GAAG,GAAGS,UAAU,GAAGgB,UAAU;cAC3C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJA,WAAW,GAAGtB,YAAY;cAC1BqB,WAAW,GAAG7B,GAAG,GAAGY,WAAW,GAAGc,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGtB,YAAY;cAC1BuB,WAAW,GAAG7B,GAAG,GAAG,CAACS,UAAU,GAAGgB,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;UACJ;QACF;QACA,IAAI,CAACP,eAAe,EAAE;UACpB,QAAQK,kBAAkB;YACxB,KAAK,CAAC;cACJI,WAAW,GAAGjB,SAAS;cACvBgB,WAAW,GAAG7B,GAAG,GAAG,CAACmB,WAAW,GAAGK,UAAU;cAC7C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGZ,aAAa;cAC3Ba,WAAW,GAAG7B,GAAG,GAAGiB,UAAU,GAAGM,UAAU;cAC3C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJA,WAAW,GAAGd,YAAY;cAC1Ba,WAAW,GAAG7B,GAAG,GAAGmB,WAAW,GAAGK,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGd,YAAY;cAC1Be,WAAW,GAAG7B,GAAG,GAAG,CAACiB,UAAU,GAAGM,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;UACJ;QACF;MACF;MACF,OAAO,KAAK;IACd,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;IACAhT,SAAS,CAACiT,oBAAoB,GAAG,UAAUtC,KAAK,EAAE+B,UAAU,EAAEQ,IAAI,EAAE;MAClE,IAAIvC,KAAK,GAAG+B,UAAU,EAAE;QACtB,OAAOQ,IAAI;MACb,CAAC,MAAM;QACL,OAAO,CAAC,GAAGA,IAAI,GAAG,CAAC;MACrB;IACF,CAAC;;IAED;AACA;AACA;AACA;IACAlT,SAAS,CAACmC,eAAe,GAAG,UAAUgR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACpD,IAAIA,EAAE,IAAI,IAAI,EAAE;QACd,OAAO,IAAI,CAACxC,gBAAgB,CAACqC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC1C;MAEA,IAAIE,EAAE,GAAGJ,EAAE,CAACrP,CAAC;MACb,IAAI0P,EAAE,GAAGL,EAAE,CAACpP,CAAC;MACb,IAAI0P,EAAE,GAAGL,EAAE,CAACtP,CAAC;MACb,IAAI4P,EAAE,GAAGN,EAAE,CAACrP,CAAC;MACb,IAAI4P,EAAE,GAAGN,EAAE,CAACvP,CAAC;MACb,IAAI8P,EAAE,GAAGP,EAAE,CAACtP,CAAC;MACb,IAAI8P,EAAE,GAAGP,EAAE,CAACxP,CAAC;MACb,IAAIgQ,EAAE,GAAGR,EAAE,CAACvP,CAAC;MACb,IAAID,CAAC,GAAG,KAAK,CAAC;QACVC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MAChB,IAAIgQ,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;MACjB,IAAIC,KAAK,GAAG,KAAK,CAAC;MAElBN,EAAE,GAAGL,EAAE,GAAGF,EAAE;MACZS,EAAE,GAAGV,EAAE,GAAGE,EAAE;MACZU,EAAE,GAAGV,EAAE,GAAGD,EAAE,GAAGD,EAAE,GAAGG,EAAE,CAAC,CAAC;;MAExBM,EAAE,GAAGF,EAAE,GAAGF,EAAE;MACZM,EAAE,GAAGP,EAAE,GAAGE,EAAE;MACZO,EAAE,GAAGP,EAAE,GAAGD,EAAE,GAAGD,EAAE,GAAGG,EAAE,CAAC,CAAC;;MAExBO,KAAK,GAAGN,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE;MAEzB,IAAII,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,IAAI;MACb;MAEAvQ,CAAC,GAAG,CAACmQ,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE,IAAIE,KAAK;MAC/BtQ,CAAC,GAAG,CAACiQ,EAAE,GAAGG,EAAE,GAAGJ,EAAE,GAAGK,EAAE,IAAIC,KAAK;MAE/B,OAAO,IAAIlL,KAAK,CAACrF,CAAC,EAAEC,CAAC,CAAC;IACxB,CAAC;;IAED;AACA;AACA;AACA;IACA/D,SAAS,CAACsU,aAAa,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAClD,IAAIC,OAAO,GAAG,KAAK,CAAC;MAEpB,IAAIJ,EAAE,KAAKE,EAAE,EAAE;QACbE,OAAO,GAAGpS,IAAI,CAACqS,IAAI,CAAC,CAACF,EAAE,GAAGF,EAAE,KAAKC,EAAE,GAAGF,EAAE,CAAC,CAAC;QAE1C,IAAIE,EAAE,GAAGF,EAAE,EAAE;UACXI,OAAO,IAAIpS,IAAI,CAACsS,EAAE;QACpB,CAAC,MAAM,IAAIH,EAAE,GAAGF,EAAE,EAAE;UAClBG,OAAO,IAAI,IAAI,CAACG,MAAM;QACxB;MACF,CAAC,MAAM,IAAIJ,EAAE,GAAGF,EAAE,EAAE;QAClBG,OAAO,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC;MAClC,CAAC,MAAM;QACLJ,OAAO,GAAG,IAAI,CAACK,OAAO,CAAC,CAAC;MAC1B;MAEA,OAAOL,OAAO;IAChB,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA3U,SAAS,CAACiV,WAAW,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAChD,IAAIC,CAAC,GAAGJ,EAAE,CAACpR,CAAC;MACZ,IAAIyR,CAAC,GAAGL,EAAE,CAACnR,CAAC;MACZ,IAAIvG,CAAC,GAAG2X,EAAE,CAACrR,CAAC;MACZ,IAAIpG,CAAC,GAAGyX,EAAE,CAACpR,CAAC;MACZ,IAAIpF,CAAC,GAAGyW,EAAE,CAACtR,CAAC;MACZ,IAAI0R,CAAC,GAAGJ,EAAE,CAACrR,CAAC;MACZ,IAAI0R,CAAC,GAAGJ,EAAE,CAACvR,CAAC;MACZ,IAAIlF,CAAC,GAAGyW,EAAE,CAACtR,CAAC;MACZ,IAAI2R,GAAG,GAAG,CAAClY,CAAC,GAAG8X,CAAC,KAAK1W,CAAC,GAAG4W,CAAC,CAAC,GAAG,CAACC,CAAC,GAAG9W,CAAC,KAAKjB,CAAC,GAAG6X,CAAC,CAAC;MAE/C,IAAIG,GAAG,KAAK,CAAC,EAAE;QACb,OAAO,KAAK;MACd,CAAC,MAAM;QACL,IAAIC,MAAM,GAAG,CAAC,CAAC/W,CAAC,GAAG4W,CAAC,KAAKC,CAAC,GAAGH,CAAC,CAAC,GAAG,CAAC3W,CAAC,GAAG8W,CAAC,KAAK7W,CAAC,GAAG2W,CAAC,CAAC,IAAIG,GAAG;QAC1D,IAAIE,KAAK,GAAG,CAAC,CAACL,CAAC,GAAG7X,CAAC,KAAK+X,CAAC,GAAGH,CAAC,CAAC,GAAG,CAAC9X,CAAC,GAAG8X,CAAC,KAAK1W,CAAC,GAAG2W,CAAC,CAAC,IAAIG,GAAG;QACzD,OAAO,CAAC,GAAGC,MAAM,IAAIA,MAAM,GAAG,CAAC,IAAI,CAAC,GAAGC,KAAK,IAAIA,KAAK,GAAG,CAAC;MAC3D;IACF,CAAC;;IAED;IACA;IACA;IACA;AACA;AACA;IACA5V,SAAS,CAACgV,OAAO,GAAG,GAAG,GAAGzS,IAAI,CAACsS,EAAE;IACjC7U,SAAS,CAAC+U,eAAe,GAAG,GAAG,GAAGxS,IAAI,CAACsS,EAAE;IACzC7U,SAAS,CAAC8U,MAAM,GAAG,GAAG,GAAGvS,IAAI,CAACsS,EAAE;IAChC7U,SAAS,CAAC6V,QAAQ,GAAG,GAAG,GAAGtT,IAAI,CAACsS,EAAE;IAElChY,MAAM,CAACD,OAAO,GAAGoD,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAASnD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS+C,KAAKA,CAAA,EAAG,CAAC;;IAElB;AACA;AACA;IACAA,KAAK,CAACwC,IAAI,GAAG,UAAUhF,KAAK,EAAE;MAC5B,IAAIA,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,CAAC;MACV,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC;IAEDwC,KAAK,CAAC6V,KAAK,GAAG,UAAUrY,KAAK,EAAE;MAC7B,OAAOA,KAAK,GAAG,CAAC,GAAG8E,IAAI,CAACwT,IAAI,CAACtY,KAAK,CAAC,GAAG8E,IAAI,CAACuT,KAAK,CAACrY,KAAK,CAAC;IACzD,CAAC;IAEDwC,KAAK,CAAC8V,IAAI,GAAG,UAAUtY,KAAK,EAAE;MAC5B,OAAOA,KAAK,GAAG,CAAC,GAAG8E,IAAI,CAACuT,KAAK,CAACrY,KAAK,CAAC,GAAG8E,IAAI,CAACwT,IAAI,CAACtY,KAAK,CAAC;IACzD,CAAC;IAEDZ,MAAM,CAACD,OAAO,GAAGqD,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASpD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS4F,OAAOA,CAAA,EAAG,CAAC;IAEpBA,OAAO,CAACa,SAAS,GAAG,UAAU;IAC9Bb,OAAO,CAACW,SAAS,GAAG,CAAC,UAAU;IAE/B5G,MAAM,CAACD,OAAO,GAAGkG,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAASjG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI8Y,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAAC7V,MAAM,EAAE8V,KAAK,EAAE;QAAE,KAAK,IAAI9Y,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Y,KAAK,CAACnV,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI+Y,UAAU,GAAGD,KAAK,CAAC9Y,CAAC,CAAC;UAAE+Y,UAAU,CAAClY,UAAU,GAAGkY,UAAU,CAAClY,UAAU,IAAI,KAAK;UAAEkY,UAAU,CAACnY,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAImY,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEtY,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE+V,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAAC7X,SAAS,EAAE8X,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;IAExJ,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACnZ,KAAK,EAAE;MACtC,OAAO;QAAEA,KAAK,EAAEA,KAAK;QAAEoZ,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAK,CAAC;IACjD,CAAC;IAED,IAAI1Q,GAAG,GAAG,SAASA,GAAGA,CAAC0Q,IAAI,EAAEtV,IAAI,EAAEqV,IAAI,EAAEE,IAAI,EAAE;MAC7C,IAAID,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACD,IAAI,GAAGrV,IAAI;MAClB,CAAC,MAAM;QACLuV,IAAI,CAACC,IAAI,GAAGxV,IAAI;MAClB;MAEA,IAAIqV,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACC,IAAI,GAAGtV,IAAI;MAClB,CAAC,MAAM;QACLuV,IAAI,CAACE,IAAI,GAAGzV,IAAI;MAClB;MAEAA,IAAI,CAACsV,IAAI,GAAGA,IAAI;MAChBtV,IAAI,CAACqV,IAAI,GAAGA,IAAI;MAEhBE,IAAI,CAAChW,MAAM,EAAE;MAEb,OAAOS,IAAI;IACb,CAAC;IAED,IAAI0V,OAAO,GAAG,SAASA,OAAOA,CAAC1V,IAAI,EAAEuV,IAAI,EAAE;MACzC,IAAID,IAAI,GAAGtV,IAAI,CAACsV,IAAI;QAChBD,IAAI,GAAGrV,IAAI,CAACqV,IAAI;MAGpB,IAAIC,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAClB,CAAC,MAAM;QACLE,IAAI,CAACC,IAAI,GAAGH,IAAI;MAClB;MAEA,IAAIA,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACC,IAAI,GAAGA,IAAI;MAClB,CAAC,MAAM;QACLC,IAAI,CAACE,IAAI,GAAGH,IAAI;MAClB;MAEAtV,IAAI,CAACsV,IAAI,GAAGtV,IAAI,CAACqV,IAAI,GAAG,IAAI;MAE5BE,IAAI,CAAChW,MAAM,EAAE;MAEb,OAAOS,IAAI;IACb,CAAC;IAED,IAAI4H,UAAU,GAAG,YAAY;MAC3B,SAASA,UAAUA,CAAC+N,IAAI,EAAE;QACxB,IAAIC,KAAK,GAAG,IAAI;QAEhBX,eAAe,CAAC,IAAI,EAAErN,UAAU,CAAC;QAEjC,IAAI,CAACrI,MAAM,GAAG,CAAC;QACf,IAAI,CAACiW,IAAI,GAAG,IAAI;QAChB,IAAI,CAACC,IAAI,GAAG,IAAI;QAEhB,IAAIE,IAAI,IAAI,IAAI,EAAE;UAChBA,IAAI,CAACtR,OAAO,CAAC,UAAUwR,CAAC,EAAE;YACxB,OAAOD,KAAK,CAACtR,IAAI,CAACuR,CAAC,CAAC;UACtB,CAAC,CAAC;QACJ;MACF;MAEArB,YAAY,CAAC5M,UAAU,EAAE,CAAC;QACxBiN,GAAG,EAAE,MAAM;QACX5Y,KAAK,EAAE,SAAS4F,IAAIA,CAAA,EAAG;UACrB,OAAO,IAAI,CAACtC,MAAM;QACpB;MACF,CAAC,EAAE;QACDsV,GAAG,EAAE,cAAc;QACnB5Y,KAAK,EAAE,SAAS6Z,YAAYA,CAACC,GAAG,EAAEC,SAAS,EAAE;UAC3C,OAAOpR,GAAG,CAACoR,SAAS,CAACV,IAAI,EAAEF,QAAQ,CAACW,GAAG,CAAC,EAAEC,SAAS,EAAE,IAAI,CAAC;QAC5D;MACF,CAAC,EAAE;QACDnB,GAAG,EAAE,aAAa;QAClB5Y,KAAK,EAAE,SAASga,WAAWA,CAACF,GAAG,EAAEC,SAAS,EAAE;UAC1C,OAAOpR,GAAG,CAACoR,SAAS,EAAEZ,QAAQ,CAACW,GAAG,CAAC,EAAEC,SAAS,CAACX,IAAI,EAAE,IAAI,CAAC;QAC5D;MACF,CAAC,EAAE;QACDR,GAAG,EAAE,kBAAkB;QACvB5Y,KAAK,EAAE,SAASia,gBAAgBA,CAACzN,OAAO,EAAEuN,SAAS,EAAE;UACnD,OAAOpR,GAAG,CAACoR,SAAS,CAACV,IAAI,EAAE7M,OAAO,EAAEuN,SAAS,EAAE,IAAI,CAAC;QACtD;MACF,CAAC,EAAE;QACDnB,GAAG,EAAE,iBAAiB;QACtB5Y,KAAK,EAAE,SAASka,eAAeA,CAAC1N,OAAO,EAAEuN,SAAS,EAAE;UAClD,OAAOpR,GAAG,CAACoR,SAAS,EAAEvN,OAAO,EAAEuN,SAAS,CAACX,IAAI,EAAE,IAAI,CAAC;QACtD;MACF,CAAC,EAAE;QACDR,GAAG,EAAE,MAAM;QACX5Y,KAAK,EAAE,SAASqI,IAAIA,CAACyR,GAAG,EAAE;UACxB,OAAOnR,GAAG,CAAC,IAAI,CAAC6Q,IAAI,EAAEL,QAAQ,CAACW,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,EAAE;QACDlB,GAAG,EAAE,SAAS;QACd5Y,KAAK,EAAE,SAASma,OAAOA,CAACL,GAAG,EAAE;UAC3B,OAAOnR,GAAG,CAAC,IAAI,EAAEwQ,QAAQ,CAACW,GAAG,CAAC,EAAE,IAAI,CAACP,IAAI,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,EAAE;QACDX,GAAG,EAAE,QAAQ;QACb5Y,KAAK,EAAE,SAAS2M,MAAMA,CAAC5I,IAAI,EAAE;UAC3B,OAAO0V,OAAO,CAAC1V,IAAI,EAAE,IAAI,CAAC;QAC5B;MACF,CAAC,EAAE;QACD6U,GAAG,EAAE,KAAK;QACV5Y,KAAK,EAAE,SAASoa,GAAGA,CAAA,EAAG;UACpB,OAAOX,OAAO,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAAC,CAACxZ,KAAK;QACvC;MACF,CAAC,EAAE;QACD4Y,GAAG,EAAE,SAAS;QACd5Y,KAAK,EAAE,SAASqa,OAAOA,CAAA,EAAG;UACxB,OAAOZ,OAAO,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAAC;QACjC;MACF,CAAC,EAAE;QACDZ,GAAG,EAAE,OAAO;QACZ5Y,KAAK,EAAE,SAASqO,KAAKA,CAAA,EAAG;UACtB,OAAOoL,OAAO,CAAC,IAAI,CAACF,IAAI,EAAE,IAAI,CAAC,CAACvZ,KAAK;QACvC;MACF,CAAC,EAAE;QACD4Y,GAAG,EAAE,WAAW;QAChB5Y,KAAK,EAAE,SAASsa,SAASA,CAAA,EAAG;UAC1B,OAAOb,OAAO,CAAC,IAAI,CAACF,IAAI,EAAE,IAAI,CAAC;QACjC;MACF,CAAC,EAAE;QACDX,GAAG,EAAE,eAAe;QACpB5Y,KAAK,EAAE,SAASua,aAAaA,CAACxN,KAAK,EAAE;UACnC,IAAIA,KAAK,IAAI,IAAI,CAACzJ,MAAM,CAAC,CAAC,EAAE;YAC1B,IAAI3D,CAAC,GAAG,CAAC;YACT,IAAI6a,OAAO,GAAG,IAAI,CAACjB,IAAI;YACvB,OAAO5Z,CAAC,GAAGoN,KAAK,EAAE;cAChByN,OAAO,GAAGA,OAAO,CAACpB,IAAI;cACtBzZ,CAAC,EAAE;YACL;YACA,OAAO6a,OAAO,CAACxa,KAAK;UACtB;QACF;MACF,CAAC,EAAE;QACD4Y,GAAG,EAAE,eAAe;QACpB5Y,KAAK,EAAE,SAASya,aAAaA,CAAC1N,KAAK,EAAE/M,KAAK,EAAE;UAC1C,IAAI+M,KAAK,IAAI,IAAI,CAACzJ,MAAM,CAAC,CAAC,EAAE;YAC1B,IAAI3D,CAAC,GAAG,CAAC;YACT,IAAI6a,OAAO,GAAG,IAAI,CAACjB,IAAI;YACvB,OAAO5Z,CAAC,GAAGoN,KAAK,EAAE;cAChByN,OAAO,GAAGA,OAAO,CAACpB,IAAI;cACtBzZ,CAAC,EAAE;YACL;YACA6a,OAAO,CAACxa,KAAK,GAAGA,KAAK;UACvB;QACF;MACF,CAAC,CAAC,CAAC;MAEH,OAAO2L,UAAU;IACnB,CAAC,CAAC,CAAC;IAEHvM,MAAM,CAACD,OAAO,GAAGwM,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASvM,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;AACA;AACA;IACA,SAASiM,KAAKA,CAACrF,CAAC,EAAEC,CAAC,EAAEpF,CAAC,EAAE;MACtB,IAAI,CAACmF,CAAC,GAAG,IAAI;MACb,IAAI,CAACC,CAAC,GAAG,IAAI;MACb,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACvC,IAAI,CAACmF,CAAC,GAAG,CAAC;QACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACZ,CAAC,MAAM,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACpE,IAAI,CAACmF,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;MACZ,CAAC,MAAM,IAAID,CAAC,CAACqU,WAAW,CAACxa,IAAI,IAAI,OAAO,IAAIoG,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QAClEA,CAAC,GAAGmF,CAAC;QACL,IAAI,CAACA,CAAC,GAAGnF,CAAC,CAACmF,CAAC;QACZ,IAAI,CAACC,CAAC,GAAGpF,CAAC,CAACoF,CAAC;MACd;IACF;IAEAoF,KAAK,CAAC1K,SAAS,CAAC+J,IAAI,GAAG,YAAY;MACjC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDqF,KAAK,CAAC1K,SAAS,CAACgK,IAAI,GAAG,YAAY;MACjC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDoF,KAAK,CAAC1K,SAAS,CAACkG,WAAW,GAAG,YAAY;MACxC,OAAO,IAAIwE,KAAK,CAAC,IAAI,CAACrF,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAClC,CAAC;IAEDoF,KAAK,CAAC1K,SAAS,CAAC2G,WAAW,GAAG,UAAUtB,CAAC,EAAEC,CAAC,EAAEpF,CAAC,EAAE;MAC/C,IAAImF,CAAC,CAACqU,WAAW,CAACxa,IAAI,IAAI,OAAO,IAAIoG,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QAC3DA,CAAC,GAAGmF,CAAC;QACL,IAAI,CAACsB,WAAW,CAACzG,CAAC,CAACmF,CAAC,EAAEnF,CAAC,CAACoF,CAAC,CAAC;MAC5B,CAAC,MAAM,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACpE;QACA,IAAIyZ,QAAQ,CAACtU,CAAC,CAAC,IAAIA,CAAC,IAAIsU,QAAQ,CAACrU,CAAC,CAAC,IAAIA,CAAC,EAAE;UACxC,IAAI,CAACsU,IAAI,CAACvU,CAAC,EAAEC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL,IAAI,CAACD,CAAC,GAAGvB,IAAI,CAACuT,KAAK,CAAChS,CAAC,GAAG,GAAG,CAAC;UAC5B,IAAI,CAACC,CAAC,GAAGxB,IAAI,CAACuT,KAAK,CAAC/R,CAAC,GAAG,GAAG,CAAC;QAC9B;MACF;IACF,CAAC;IAEDoF,KAAK,CAAC1K,SAAS,CAAC4Z,IAAI,GAAG,UAAUvU,CAAC,EAAEC,CAAC,EAAE;MACrC,IAAI,CAACD,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDoF,KAAK,CAAC1K,SAAS,CAACuK,SAAS,GAAG,UAAU1D,EAAE,EAAEC,EAAE,EAAE;MAC5C,IAAI,CAACzB,CAAC,IAAIwB,EAAE;MACZ,IAAI,CAACvB,CAAC,IAAIwB,EAAE;IACd,CAAC;IAED4D,KAAK,CAAC1K,SAAS,CAAC6Z,MAAM,GAAG,UAAUjO,GAAG,EAAE;MACtC,IAAIA,GAAG,CAAC8N,WAAW,CAACxa,IAAI,IAAI,OAAO,EAAE;QACnC,IAAIkL,EAAE,GAAGwB,GAAG;QACZ,OAAO,IAAI,CAACvG,CAAC,IAAI+E,EAAE,CAAC/E,CAAC,IAAI,IAAI,CAACC,CAAC,IAAI8E,EAAE,CAAC9E,CAAC;MACzC;MACA,OAAO,IAAI,IAAIsG,GAAG;IACpB,CAAC;IAEDlB,KAAK,CAAC1K,SAAS,CAAC8Z,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAIpP,KAAK,CAAC,CAAC,CAACgP,WAAW,CAACxa,IAAI,GAAG,KAAK,GAAG,IAAI,CAACmG,CAAC,GAAG,KAAK,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG;IAC7E,CAAC;IAEDlH,MAAM,CAACD,OAAO,GAAGuM,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAAStM,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS6F,UAAUA,CAACe,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;MACvC,IAAI,CAACH,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,MAAM,GAAG,CAAC;MAEf,IAAIH,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;QAC7D,IAAI,CAACH,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;MACtB;IACF;IAEAlB,UAAU,CAACtE,SAAS,CAAC+J,IAAI,GAAG,YAAY;MACtC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDf,UAAU,CAACtE,SAAS,CAACiK,IAAI,GAAG,UAAU5E,CAAC,EAAE;MACvC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDf,UAAU,CAACtE,SAAS,CAACgK,IAAI,GAAG,YAAY;MACtC,OAAO,IAAI,CAAC1E,CAAC;IACf,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAACkK,IAAI,GAAG,UAAU5E,CAAC,EAAE;MACvC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC/C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC+F,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACP,MAAM;IACpB,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MACjD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAAC7D,CAAC,GAAG,IAAI,CAACE,KAAK;IAC5B,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACmJ,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAAC7D,CAAC,GAAG,IAAI,CAACE,MAAM;IAC7B,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAAC6R,UAAU,GAAG,UAAUgF,CAAC,EAAE;MAC7C,IAAI,IAAI,CAAC3N,QAAQ,CAAC,CAAC,GAAG2N,CAAC,CAACxR,CAAC,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI,IAAI,CAAC8D,SAAS,CAAC,CAAC,GAAG0N,CAAC,CAACvR,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MAEA,IAAIuR,CAAC,CAAC3N,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC7D,CAAC,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAIwR,CAAC,CAAC1N,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC7D,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAACmE,UAAU,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACkB,CAAC,GAAG,IAAI,CAACE,KAAK,GAAG,CAAC;IAChC,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC+Z,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAAChQ,IAAI,CAAC,CAAC;IACpB,CAAC;IAEDzF,UAAU,CAACtE,SAAS,CAACga,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACjQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAACxE,KAAK;IACjC,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACoE,UAAU,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACkB,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC;IACjC,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACia,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACjQ,IAAI,CAAC,CAAC;IACpB,CAAC;IAED1F,UAAU,CAACtE,SAAS,CAACka,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAAClQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAACxE,MAAM;IAClC,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACkT,YAAY,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAC3N,KAAK,GAAG,CAAC;IACvB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACoT,aAAa,GAAG,YAAY;MAC/C,OAAO,IAAI,CAAC5N,MAAM,GAAG,CAAC;IACxB,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGmG,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASlG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI0b,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUzO,GAAG,EAAE;MAAE,OAAO,OAAOA,GAAG;IAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAI,OAAOwO,MAAM,KAAK,UAAU,IAAIxO,GAAG,CAAC8N,WAAW,KAAKU,MAAM,IAAIxO,GAAG,KAAKwO,MAAM,CAACpa,SAAS,GAAG,QAAQ,GAAG,OAAO4L,GAAG;IAAE,CAAC;IAE5Q,SAAS0O,iBAAiBA,CAAA,EAAG,CAAC;IAE9BA,iBAAiB,CAACC,MAAM,GAAG,CAAC;IAE5BD,iBAAiB,CAACE,QAAQ,GAAG,UAAU5O,GAAG,EAAE;MAC1C,IAAI0O,iBAAiB,CAACG,WAAW,CAAC7O,GAAG,CAAC,EAAE;QACtC,OAAOA,GAAG;MACZ;MACA,IAAIA,GAAG,CAAC8O,QAAQ,IAAI,IAAI,EAAE;QACxB,OAAO9O,GAAG,CAAC8O,QAAQ;MACrB;MACA9O,GAAG,CAAC8O,QAAQ,GAAGJ,iBAAiB,CAACK,SAAS,CAAC,CAAC;MAC5CL,iBAAiB,CAACC,MAAM,EAAE;MAC1B,OAAO3O,GAAG,CAAC8O,QAAQ;IACrB,CAAC;IAEDJ,iBAAiB,CAACK,SAAS,GAAG,UAAUC,EAAE,EAAE;MAC1C,IAAIA,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGN,iBAAiB,CAACC,MAAM;MAC7C,OAAO,SAAS,GAAGK,EAAE,GAAG,EAAE;IAC5B,CAAC;IAEDN,iBAAiB,CAACG,WAAW,GAAG,UAAUI,GAAG,EAAE;MAC7C,IAAIC,IAAI,GAAG,OAAOD,GAAG,KAAK,WAAW,GAAG,WAAW,GAAGV,OAAO,CAACU,GAAG,CAAC;MAClE,OAAOA,GAAG,IAAI,IAAI,IAAIC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,UAAU;IAC9D,CAAC;IAED1c,MAAM,CAACD,OAAO,GAAGmc,iBAAiB;;IAElC;EAAM,CAAC,IACP;EACA,KAAO,UAASlc,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASsc,kBAAkBA,CAACC,GAAG,EAAE;MAAE,IAAIvX,KAAK,CAACwX,OAAO,CAACD,GAAG,CAAC,EAAE;QAAE,KAAK,IAAIrc,CAAC,GAAG,CAAC,EAAEuc,IAAI,GAAGzX,KAAK,CAACuX,GAAG,CAAC1Y,MAAM,CAAC,EAAE3D,CAAC,GAAGqc,GAAG,CAAC1Y,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAEuc,IAAI,CAACvc,CAAC,CAAC,GAAGqc,GAAG,CAACrc,CAAC,CAAC;QAAE;QAAE,OAAOuc,IAAI;MAAE,CAAC,MAAM;QAAE,OAAOzX,KAAK,CAAC0X,IAAI,CAACH,GAAG,CAAC;MAAE;IAAE;IAElM,IAAI5a,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAIgM,aAAa,GAAGhM,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAImM,MAAM,GAAGnM,mBAAmB,CAAC,CAAC,CAAC;IACnC,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IACnC,IAAI2c,SAAS,GAAG3c,mBAAmB,CAAC,EAAE,CAAC;IACvC,IAAI4c,OAAO,GAAG5c,mBAAmB,CAAC,EAAE,CAAC;IAErC,SAASyM,MAAMA,CAACoQ,WAAW,EAAE;MAC3BD,OAAO,CAACxc,IAAI,CAAC,IAAI,CAAC;;MAElB;MACA,IAAI,CAAC0c,aAAa,GAAGnb,eAAe,CAACC,OAAO;MAC5C;MACA,IAAI,CAACmb,mBAAmB,GAAGpb,eAAe,CAACE,8BAA8B;MACzE;MACA,IAAI,CAACmb,WAAW,GAAGrb,eAAe,CAACG,mBAAmB;MACtD;MACA,IAAI,CAACmb,iBAAiB,GAAGtb,eAAe,CAACI,2BAA2B;MACpE;MACA,IAAI,CAACmb,qBAAqB,GAAGvb,eAAe,CAACK,+BAA+B;MAC5E;MACA,IAAI,CAACmb,eAAe,GAAGxb,eAAe,CAACM,wBAAwB;MAC/D;AACF;AACA;AACA;AACA;AACA;MACE,IAAI,CAACmb,oBAAoB,GAAGzb,eAAe,CAACO,+BAA+B;MAC3E;AACF;AACA;AACA;MACE,IAAI,CAACmb,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjC,IAAI,CAACjX,YAAY,GAAG,IAAI2F,aAAa,CAAC,IAAI,CAAC;MAC3C,IAAI,CAACuR,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACX,WAAW,GAAG,KAAK;MAExB,IAAIA,WAAW,IAAI,IAAI,EAAE;QACvB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAChC;IACF;IAEApQ,MAAM,CAACgR,WAAW,GAAG,CAAC;IAEtBhR,MAAM,CAAClL,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACqZ,OAAO,CAACrb,SAAS,CAAC;IAEnDkL,MAAM,CAAClL,SAAS,CAACmD,eAAe,GAAG,YAAY;MAC7C,OAAO,IAAI,CAAC2B,YAAY;IAC1B,CAAC;IAEDoG,MAAM,CAAClL,SAAS,CAAC0O,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAAC5J,YAAY,CAAC4J,WAAW,CAAC,CAAC;IACxC,CAAC;IAEDxD,MAAM,CAAClL,SAAS,CAACkP,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAACpK,YAAY,CAACoK,WAAW,CAAC,CAAC;IACxC,CAAC;IAEDhE,MAAM,CAAClL,SAAS,CAACmP,6BAA6B,GAAG,YAAY;MAC3D,OAAO,IAAI,CAACrK,YAAY,CAACqK,6BAA6B,CAAC,CAAC;IAC1D,CAAC;IAEDjE,MAAM,CAAClL,SAAS,CAACmc,eAAe,GAAG,YAAY;MAC7C,IAAIzX,EAAE,GAAG,IAAI+F,aAAa,CAAC,IAAI,CAAC;MAChC,IAAI,CAAC3F,YAAY,GAAGJ,EAAE;MACtB,OAAOA,EAAE;IACX,CAAC;IAEDwG,MAAM,CAAClL,SAAS,CAAC+N,QAAQ,GAAG,UAAUhD,MAAM,EAAE;MAC5C,OAAO,IAAIH,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC9F,YAAY,EAAEiG,MAAM,CAAC;IACpD,CAAC;IAEDG,MAAM,CAAClL,SAAS,CAACwL,OAAO,GAAG,UAAU3G,KAAK,EAAE;MAC1C,OAAO,IAAIJ,KAAK,CAAC,IAAI,CAACK,YAAY,EAAED,KAAK,CAAC;IAC5C,CAAC;IAEDqG,MAAM,CAAClL,SAAS,CAAC0L,OAAO,GAAG,UAAU9J,KAAK,EAAE;MAC1C,OAAO,IAAIH,KAAK,CAAC,IAAI,EAAE,IAAI,EAAEG,KAAK,CAAC;IACrC,CAAC;IAEDsJ,MAAM,CAAClL,SAAS,CAACoc,kBAAkB,GAAG,YAAY;MAChD,OAAO,IAAI,CAACtX,YAAY,CAAC1B,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC0B,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,IAAI,IAAI,CAACwC,YAAY,CAACoL,mBAAmB,CAAC,CAAC;IAC7I,CAAC;IAEDhF,MAAM,CAAClL,SAAS,CAACqc,SAAS,GAAG,YAAY;MACvC,IAAI,CAACL,gBAAgB,GAAG,KAAK;MAE7B,IAAI,IAAI,CAACM,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC,CAAC;MACxB;MAEA,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAIC,mBAAmB;MAEvB,IAAI,IAAI,CAACJ,kBAAkB,CAAC,CAAC,EAAE;QAC7BI,mBAAmB,GAAG,KAAK;MAC7B,CAAC,MAAM;QACLA,mBAAmB,GAAG,IAAI,CAAC7O,MAAM,CAAC,CAAC;MACrC;MAEA,IAAIvN,eAAe,CAACqc,OAAO,KAAK,QAAQ,EAAE;QACxC;QACA;QACA,OAAO,KAAK;MACd;MAEA,IAAID,mBAAmB,EAAE;QACvB,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;UACrB,IAAI,CAACS,YAAY,CAAC,CAAC;QACrB;MACF;MAEA,IAAI,IAAI,CAACC,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC,CAAC;MACzB;MAEA,IAAI,CAACX,gBAAgB,GAAG,IAAI;MAE5B,OAAOQ,mBAAmB;IAC5B,CAAC;;IAED;AACA;AACA;IACAtR,MAAM,CAAClL,SAAS,CAAC0c,YAAY,GAAG,YAAY;MAC1C;MACA;MACA,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;QACrB,IAAI,CAACjS,SAAS,CAAC,CAAC;MAClB;MACA,IAAI,CAACoT,MAAM,CAAC,CAAC;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA1R,MAAM,CAAClL,SAAS,CAAC6c,OAAO,GAAG,YAAY;MACrC;MACA,IAAI,IAAI,CAACrB,mBAAmB,EAAE;QAC5B,IAAI,CAACsB,8BAA8B,CAAC,CAAC;;QAErC;QACA,IAAI,CAAChY,YAAY,CAACgK,aAAa,CAAC,CAAC;MACnC;;MAEA;MACA;MACA,IAAI,CAAC,IAAI,CAACwM,WAAW,EAAE;QACrB;QACA,IAAIpU,IAAI;QACR,IAAI6H,QAAQ,GAAG,IAAI,CAACjK,YAAY,CAACoK,WAAW,CAAC,CAAC;QAC9C,KAAK,IAAIvQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoQ,QAAQ,CAACzM,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACxCuI,IAAI,GAAG6H,QAAQ,CAACpQ,CAAC,CAAC;UAClB;QACF;;QAEA;QACA,IAAIoE,IAAI;QACR,IAAIiF,KAAK,GAAG,IAAI,CAAClD,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC;QAClD,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;UACf;QACF;;QAEA;QACA,IAAI,CAACie,MAAM,CAAC,IAAI,CAAC9X,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC;MAC1C;IACF,CAAC;IAED8H,MAAM,CAAClL,SAAS,CAAC4c,MAAM,GAAG,UAAUhR,GAAG,EAAE;MACvC,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,IAAI,CAACiR,OAAO,CAAC,CAAC;MAChB,CAAC,MAAM,IAAIjR,GAAG,YAAYnH,KAAK,EAAE;QAC/B,IAAI1B,IAAI,GAAG6I,GAAG;QACd,IAAI7I,IAAI,CAAC2C,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;UAC3B;UACA,IAAIsC,KAAK,GAAGjF,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC;UACtC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACrCie,MAAM,CAAC5U,KAAK,CAACrJ,CAAC,CAAC,CAAC;UAClB;QACF;;QAEA;QACA;QACA;QACA,IAAIoE,IAAI,CAACjB,YAAY,IAAI,IAAI,EAAE;UAC7B;UACA,IAAI+C,KAAK,GAAG9B,IAAI,CAACjB,YAAY;;UAE7B;UACA+C,KAAK,CAAC+X,MAAM,CAAC7Z,IAAI,CAAC;QACpB;MACF,CAAC,MAAM,IAAI6I,GAAG,YAAYnK,KAAK,EAAE;QAC/B,IAAIyF,IAAI,GAAG0E,GAAG;QACd;QACA;QACA;;QAEA,IAAI1E,IAAI,CAACpF,YAAY,IAAI,IAAI,EAAE;UAC7B;UACA,IAAIF,KAAK,GAAGsF,IAAI,CAACpF,YAAY;;UAE7B;UACAF,KAAK,CAACgb,MAAM,CAAC1V,IAAI,CAAC;QACpB;MACF,CAAC,MAAM,IAAI0E,GAAG,YAAYhB,MAAM,EAAE;QAChC,IAAI3H,KAAK,GAAG2I,GAAG;QACf;QACA;QACA;;QAEA,IAAI3I,KAAK,CAACnB,YAAY,IAAI,IAAI,EAAE;UAC9B;UACA,IAAIiJ,MAAM,GAAG9H,KAAK,CAACnB,YAAY;;UAE/B;UACAiJ,MAAM,CAAC6R,MAAM,CAAC3Z,KAAK,CAAC;QACtB;MACF;IACF,CAAC;;IAED;AACA;AACA;AACA;IACAiI,MAAM,CAAClL,SAAS,CAACuc,cAAc,GAAG,YAAY;MAC5C,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE;QACrB,IAAI,CAACV,aAAa,GAAGnb,eAAe,CAACC,OAAO;QAC5C,IAAI,CAACsb,qBAAqB,GAAGvb,eAAe,CAACK,+BAA+B;QAC5E,IAAI,CAACmb,eAAe,GAAGxb,eAAe,CAACM,wBAAwB;QAC/D,IAAI,CAACgb,iBAAiB,GAAGtb,eAAe,CAACI,2BAA2B;QACpE,IAAI,CAACib,WAAW,GAAGrb,eAAe,CAACG,mBAAmB;QACtD,IAAI,CAACib,mBAAmB,GAAGpb,eAAe,CAACE,8BAA8B;QACzE,IAAI,CAACub,oBAAoB,GAAGzb,eAAe,CAACO,+BAA+B;MAC7E;MAEA,IAAI,IAAI,CAACgb,qBAAqB,EAAE;QAC9B,IAAI,CAACD,iBAAiB,GAAG,KAAK;MAChC;IACF,CAAC;IAEDxQ,MAAM,CAAClL,SAAS,CAACwJ,SAAS,GAAG,UAAUuT,UAAU,EAAE;MACjD,IAAIA,UAAU,IAAIvQ,SAAS,EAAE;QAC3B,IAAI,CAAChD,SAAS,CAAC,IAAIhF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM;QACL;QACA;QACA;QACA;;QAEA,IAAIiF,KAAK,GAAG,IAAI2R,SAAS,CAAC,CAAC;QAC3B,IAAIxR,OAAO,GAAG,IAAI,CAAC9E,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC+I,aAAa,CAAC,CAAC;QAEzD,IAAIvC,OAAO,IAAI,IAAI,EAAE;UACnBH,KAAK,CAACuT,YAAY,CAACD,UAAU,CAAC1X,CAAC,CAAC;UAChCoE,KAAK,CAACwT,YAAY,CAACF,UAAU,CAACzX,CAAC,CAAC;UAEhCmE,KAAK,CAACyT,aAAa,CAACtT,OAAO,CAACvE,CAAC,CAAC;UAC9BoE,KAAK,CAAC0T,aAAa,CAACvT,OAAO,CAACtE,CAAC,CAAC;UAE9B,IAAI0C,KAAK,GAAG,IAAI,CAAC0G,WAAW,CAAC,CAAC;UAC9B,IAAI3L,IAAI;UAER,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACrCoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;YACfoE,IAAI,CAACyG,SAAS,CAACC,KAAK,CAAC;UACvB;QACF;MACF;IACF,CAAC;IAEDyB,MAAM,CAAClL,SAAS,CAACod,qBAAqB,GAAG,UAAUna,KAAK,EAAE;MAExD,IAAIA,KAAK,IAAIuJ,SAAS,EAAE;QACtB;QACA,IAAI,CAAC4Q,qBAAqB,CAAC,IAAI,CAACja,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC0F,YAAY,CAAC,IAAI,CAAC;MACrD,CAAC,MAAM;QACL,IAAIwD,KAAK;QACT,IAAIvD,UAAU;QAEd,IAAIf,KAAK,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC;QAC5B,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrC2N,KAAK,GAAGtE,KAAK,CAACrJ,CAAC,CAAC;UAChBoK,UAAU,GAAGuD,KAAK,CAAC5G,QAAQ,CAAC,CAAC;UAE7B,IAAIqD,UAAU,IAAI,IAAI,EAAE;YACtBuD,KAAK,CAAChE,OAAO,CAAC,CAAC;UACjB,CAAC,MAAM,IAAIS,UAAU,CAACd,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,EAAE;YAC5CgK,KAAK,CAAChE,OAAO,CAAC,CAAC;UACjB,CAAC,MAAM;YACL,IAAI,CAAC8U,qBAAqB,CAACrU,UAAU,CAAC;YACtCuD,KAAK,CAACxD,YAAY,CAAC,CAAC;UACtB;QACF;MACF;IACF,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACAoC,MAAM,CAAClL,SAAS,CAACqd,aAAa,GAAG,YAAY;MAC3C,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,QAAQ,GAAG,IAAI;;MAEnB;MACA;MACA,IAAI5O,QAAQ,GAAG,IAAI,CAAC7J,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC;;MAErD;MACA,IAAIuV,MAAM,GAAG,IAAI;MAEjB,KAAK,IAAI7e,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgQ,QAAQ,CAACrM,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxC,IAAIgQ,QAAQ,CAAChQ,CAAC,CAAC,CAAC+G,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;UAClC8X,MAAM,GAAG,KAAK;QAChB;MACF;;MAEA;MACA,IAAI,CAACA,MAAM,EAAE;QACX,OAAOF,UAAU;MACnB;;MAEA;;MAEA,IAAItQ,OAAO,GAAG,IAAItF,GAAG,CAAC,CAAC;MACvB,IAAI+V,WAAW,GAAG,EAAE;MACpB,IAAIC,OAAO,GAAG,IAAI3B,GAAG,CAAC,CAAC;MACvB,IAAI4B,gBAAgB,GAAG,EAAE;MAEzBA,gBAAgB,GAAGA,gBAAgB,CAACpP,MAAM,CAACI,QAAQ,CAAC;;MAEpD;MACA;MACA;;MAEA,OAAOgP,gBAAgB,CAACrb,MAAM,GAAG,CAAC,IAAIib,QAAQ,EAAE;QAC9CE,WAAW,CAACpW,IAAI,CAACsW,gBAAgB,CAAC,CAAC,CAAC,CAAC;;QAErC;QACA;QACA,OAAOF,WAAW,CAACnb,MAAM,GAAG,CAAC,IAAIib,QAAQ,EAAE;UACzC;UACA,IAAItQ,WAAW,GAAGwQ,WAAW,CAAC,CAAC,CAAC;UAChCA,WAAW,CAACzR,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UACxBgB,OAAO,CAACrF,GAAG,CAACsF,WAAW,CAAC;;UAExB;UACA,IAAIC,aAAa,GAAGD,WAAW,CAACxH,QAAQ,CAAC,CAAC;UAE1C,KAAK,IAAI9G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuO,aAAa,CAAC5K,MAAM,EAAE3D,CAAC,EAAE,EAAE;YAC7C,IAAIwO,eAAe,GAAGD,aAAa,CAACvO,CAAC,CAAC,CAACmE,WAAW,CAACmK,WAAW,CAAC;;YAE/D;YACA,IAAIyQ,OAAO,CAACje,GAAG,CAACwN,WAAW,CAAC,IAAIE,eAAe,EAAE;cAC/C;cACA,IAAI,CAACH,OAAO,CAACO,GAAG,CAACJ,eAAe,CAAC,EAAE;gBACjCsQ,WAAW,CAACpW,IAAI,CAAC8F,eAAe,CAAC;gBACjCuQ,OAAO,CAACE,GAAG,CAACzQ,eAAe,EAAEF,WAAW,CAAC;cAC3C;cACA;cACA;cACA;cACA;cAAA,KACK;gBACDsQ,QAAQ,GAAG,KAAK;gBAChB;cACF;YACJ;UACF;QACF;;QAEA;QACA;QACA,IAAI,CAACA,QAAQ,EAAE;UACbD,UAAU,GAAG,EAAE;QACjB;QACA;QACA;QACA;QAAA,KACK;UACD,IAAIO,IAAI,GAAG,EAAE,CAACtP,MAAM,CAACwM,kBAAkB,CAAC/N,OAAO,CAAC,CAAC;UACjDsQ,UAAU,CAACjW,IAAI,CAACwW,IAAI,CAAC;UACrB;UACA;UACA,KAAK,IAAIlf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkf,IAAI,CAACvb,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACpC,IAAIK,KAAK,GAAG6e,IAAI,CAAClf,CAAC,CAAC;YACnB,IAAIoN,KAAK,GAAG4R,gBAAgB,CAAClS,OAAO,CAACzM,KAAK,CAAC;YAC3C,IAAI+M,KAAK,GAAG,CAAC,CAAC,EAAE;cACd4R,gBAAgB,CAAC3R,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;YACnC;UACF;UACAiB,OAAO,GAAG,IAAItF,GAAG,CAAC,CAAC;UACnBgW,OAAO,GAAG,IAAI3B,GAAG,CAAC,CAAC;QACrB;MACJ;MAEA,OAAOuB,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;AACA;IACApS,MAAM,CAAClL,SAAS,CAAC8d,6BAA6B,GAAG,UAAU5W,IAAI,EAAE;MAC/D,IAAI6W,UAAU,GAAG,EAAE;MACnB,IAAI1F,IAAI,GAAGnR,IAAI,CAACxF,MAAM;MAEtB,IAAIuB,KAAK,GAAG,IAAI,CAAC6B,YAAY,CAAC+K,wBAAwB,CAAC3I,IAAI,CAACxF,MAAM,EAAEwF,IAAI,CAACvF,MAAM,CAAC;MAEhF,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,IAAI,CAACnF,UAAU,CAACO,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC/C;QACA,IAAIqf,SAAS,GAAG,IAAI,CAACxS,OAAO,CAAC,IAAI,CAAC;QAClCwS,SAAS,CAAC3X,OAAO,CAAC,IAAIqE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIuT,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvDhb,KAAK,CAAC0E,GAAG,CAACqW,SAAS,CAAC;;QAEpB;QACA,IAAIE,SAAS,GAAG,IAAI,CAACxS,OAAO,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC5G,YAAY,CAAC6C,GAAG,CAACuW,SAAS,EAAE7F,IAAI,EAAE2F,SAAS,CAAC;QAEjDD,UAAU,CAACpW,GAAG,CAACqW,SAAS,CAAC;QACzB3F,IAAI,GAAG2F,SAAS;MAClB;MAEA,IAAIE,SAAS,GAAG,IAAI,CAACxS,OAAO,CAAC,IAAI,CAAC;MAClC,IAAI,CAAC5G,YAAY,CAAC6C,GAAG,CAACuW,SAAS,EAAE7F,IAAI,EAAEnR,IAAI,CAACvF,MAAM,CAAC;MAEnD,IAAI,CAACma,gBAAgB,CAAC8B,GAAG,CAAC1W,IAAI,EAAE6W,UAAU,CAAC;;MAE3C;MACA,IAAI7W,IAAI,CAAC9E,YAAY,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC0C,YAAY,CAAC6G,MAAM,CAACzE,IAAI,CAAC;MAChC;MACA;MAAA,KACK;QACDjE,KAAK,CAAC0I,MAAM,CAACzE,IAAI,CAAC;MACpB;MAEF,OAAO6W,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;IACA7S,MAAM,CAAClL,SAAS,CAAC8c,8BAA8B,GAAG,YAAY;MAC5D,IAAI3X,KAAK,GAAG,EAAE;MACdA,KAAK,GAAGA,KAAK,CAACoJ,MAAM,CAAC,IAAI,CAACzJ,YAAY,CAACoK,WAAW,CAAC,CAAC,CAAC;MACrD/J,KAAK,GAAG,EAAE,CAACoJ,MAAM,CAACwM,kBAAkB,CAAC,IAAI,CAACe,gBAAgB,CAACqC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC5P,MAAM,CAACpJ,KAAK,CAAC;MAEjF,KAAK,IAAIiZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjZ,KAAK,CAAC7C,MAAM,EAAE8b,CAAC,EAAE,EAAE;QACrC,IAAIC,KAAK,GAAGlZ,KAAK,CAACiZ,CAAC,CAAC;QAEpB,IAAIC,KAAK,CAACtc,UAAU,CAACO,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAIgc,IAAI,GAAG,IAAI,CAACxC,gBAAgB,CAACrc,GAAG,CAAC4e,KAAK,CAAC;UAE3C,KAAK,IAAI1f,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2f,IAAI,CAAChc,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACpC,IAAIqf,SAAS,GAAGM,IAAI,CAAC3f,CAAC,CAAC;YACvB,IAAIuB,CAAC,GAAG,IAAIsE,MAAM,CAACwZ,SAAS,CAAC7Z,UAAU,CAAC,CAAC,EAAE6Z,SAAS,CAAC5Z,UAAU,CAAC,CAAC,CAAC;;YAElE;YACA,IAAIma,GAAG,GAAGF,KAAK,CAACtc,UAAU,CAACtC,GAAG,CAACd,CAAC,CAAC;YACjC4f,GAAG,CAAClZ,CAAC,GAAGnF,CAAC,CAACmF,CAAC;YACXkZ,GAAG,CAACjZ,CAAC,GAAGpF,CAAC,CAACoF,CAAC;;YAEX;YACA;YACA0Y,SAAS,CAAC3a,QAAQ,CAAC,CAAC,CAACsI,MAAM,CAACqS,SAAS,CAAC;UACxC;;UAEA;UACA,IAAI,CAAClZ,YAAY,CAAC6C,GAAG,CAAC0W,KAAK,EAAEA,KAAK,CAAC3c,MAAM,EAAE2c,KAAK,CAAC1c,MAAM,CAAC;QAC1D;MACF;IACF,CAAC;IAEDuJ,MAAM,CAAC1B,SAAS,GAAG,UAAUgV,WAAW,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAE;MACtE,IAAID,MAAM,IAAIlS,SAAS,IAAImS,MAAM,IAAInS,SAAS,EAAE;QAC9C,IAAIxN,KAAK,GAAGyf,YAAY;QAExB,IAAID,WAAW,IAAI,EAAE,EAAE;UACrB,IAAII,QAAQ,GAAGH,YAAY,GAAGC,MAAM;UACpC1f,KAAK,IAAI,CAACyf,YAAY,GAAGG,QAAQ,IAAI,EAAE,IAAI,EAAE,GAAGJ,WAAW,CAAC;QAC9D,CAAC,MAAM;UACL,IAAIK,QAAQ,GAAGJ,YAAY,GAAGE,MAAM;UACpC3f,KAAK,IAAI,CAAC6f,QAAQ,GAAGJ,YAAY,IAAI,EAAE,IAAID,WAAW,GAAG,EAAE,CAAC;QAC9D;QAEA,OAAOxf,KAAK;MACd,CAAC,MAAM;QACL,IAAI6X,CAAC,EAAEC,CAAC;QAER,IAAI0H,WAAW,IAAI,EAAE,EAAE;UACrB3H,CAAC,GAAG,GAAG,GAAG4H,YAAY,GAAG,KAAK;UAC9B3H,CAAC,GAAG2H,YAAY,GAAG,IAAI;QACzB,CAAC,MAAM;UACL5H,CAAC,GAAG,GAAG,GAAG4H,YAAY,GAAG,IAAI;UAC7B3H,CAAC,GAAG,CAAC,CAAC,GAAG2H,YAAY;QACvB;QAEA,OAAO5H,CAAC,GAAG2H,WAAW,GAAG1H,CAAC;MAC5B;IACF,CAAC;;IAED;AACA;AACA;AACA;IACA5L,MAAM,CAAC4T,gBAAgB,GAAG,UAAU9W,KAAK,EAAE;MACzC,IAAIsQ,IAAI,GAAG,EAAE;MACbA,IAAI,GAAGA,IAAI,CAAC/J,MAAM,CAACvG,KAAK,CAAC;MAEzB,IAAI+W,YAAY,GAAG,EAAE;MACrB,IAAIC,gBAAgB,GAAG,IAAIjD,GAAG,CAAC,CAAC;MAChC,IAAIkD,WAAW,GAAG,KAAK;MACvB,IAAIC,UAAU,GAAG,IAAI;MAErB,IAAI5G,IAAI,CAAChW,MAAM,IAAI,CAAC,IAAIgW,IAAI,CAAChW,MAAM,IAAI,CAAC,EAAE;QACxC2c,WAAW,GAAG,IAAI;QAClBC,UAAU,GAAG5G,IAAI,CAAC,CAAC,CAAC;MACtB;MAEA,KAAK,IAAI3Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2Z,IAAI,CAAChW,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACpC,IAAIoE,IAAI,GAAGuV,IAAI,CAAC3Z,CAAC,CAAC;QAClB,IAAIwgB,MAAM,GAAGpc,IAAI,CAACyE,gBAAgB,CAAC,CAAC,CAAC5C,IAAI;QACzCoa,gBAAgB,CAACpB,GAAG,CAAC7a,IAAI,EAAEA,IAAI,CAACyE,gBAAgB,CAAC,CAAC,CAAC5C,IAAI,CAAC;QAExD,IAAIua,MAAM,IAAI,CAAC,EAAE;UACfJ,YAAY,CAAC1X,IAAI,CAACtE,IAAI,CAAC;QACzB;MACF;MAEA,IAAIqc,QAAQ,GAAG,EAAE;MACjBA,QAAQ,GAAGA,QAAQ,CAAC7Q,MAAM,CAACwQ,YAAY,CAAC;MAExC,OAAO,CAACE,WAAW,EAAE;QACnB,IAAII,SAAS,GAAG,EAAE;QAClBA,SAAS,GAAGA,SAAS,CAAC9Q,MAAM,CAAC6Q,QAAQ,CAAC;QACtCA,QAAQ,GAAG,EAAE;QAEb,KAAK,IAAIzgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2Z,IAAI,CAAChW,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACpC,IAAIoE,IAAI,GAAGuV,IAAI,CAAC3Z,CAAC,CAAC;UAElB,IAAIoN,KAAK,GAAGuM,IAAI,CAAC7M,OAAO,CAAC1I,IAAI,CAAC;UAC9B,IAAIgJ,KAAK,IAAI,CAAC,EAAE;YACduM,IAAI,CAACtM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;UACvB;UAEA,IAAIuT,UAAU,GAAGvc,IAAI,CAACyE,gBAAgB,CAAC,CAAC;UAExC8X,UAAU,CAAClY,OAAO,CAAC,UAAUmY,SAAS,EAAE;YACtC,IAAIR,YAAY,CAACtT,OAAO,CAAC8T,SAAS,CAAC,GAAG,CAAC,EAAE;cACvC,IAAIC,WAAW,GAAGR,gBAAgB,CAACvf,GAAG,CAAC8f,SAAS,CAAC;cACjD,IAAIE,SAAS,GAAGD,WAAW,GAAG,CAAC;cAE/B,IAAIC,SAAS,IAAI,CAAC,EAAE;gBAClBL,QAAQ,CAAC/X,IAAI,CAACkY,SAAS,CAAC;cAC1B;cAEAP,gBAAgB,CAACpB,GAAG,CAAC2B,SAAS,EAAEE,SAAS,CAAC;YAC5C;UACF,CAAC,CAAC;QACJ;QAEAV,YAAY,GAAGA,YAAY,CAACxQ,MAAM,CAAC6Q,QAAQ,CAAC;QAE5C,IAAI9G,IAAI,CAAChW,MAAM,IAAI,CAAC,IAAIgW,IAAI,CAAChW,MAAM,IAAI,CAAC,EAAE;UACxC2c,WAAW,GAAG,IAAI;UAClBC,UAAU,GAAG5G,IAAI,CAAC,CAAC,CAAC;QACtB;MACF;MAEA,OAAO4G,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;IACAhU,MAAM,CAAClL,SAAS,CAAC0f,eAAe,GAAG,UAAUhb,EAAE,EAAE;MAC/C,IAAI,CAACI,YAAY,GAAGJ,EAAE;IACxB,CAAC;IAEDtG,MAAM,CAACD,OAAO,GAAG+M,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAAS9M,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS8F,UAAUA,CAAA,EAAG,CAAC;IACvB;IACAA,UAAU,CAACob,IAAI,GAAG,CAAC;IACnBpb,UAAU,CAACc,CAAC,GAAG,CAAC;IAEhBd,UAAU,CAACoE,UAAU,GAAG,YAAY;MAClCpE,UAAU,CAACc,CAAC,GAAGvB,IAAI,CAAC8b,GAAG,CAACrb,UAAU,CAACob,IAAI,EAAE,CAAC,GAAG,KAAK;MAClD,OAAOpb,UAAU,CAACc,CAAC,GAAGvB,IAAI,CAACuT,KAAK,CAAC9S,UAAU,CAACc,CAAC,CAAC;IAChD,CAAC;IAEDjH,MAAM,CAACD,OAAO,GAAGoG,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASnG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAAS2c,SAASA,CAAC/V,CAAC,EAAEC,CAAC,EAAE;MACvB,IAAI,CAACua,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,WAAW,GAAG,GAAG;IACxB;IAEAhF,SAAS,CAACpb,SAAS,CAACqgB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAEDzE,SAAS,CAACpb,SAAS,CAACgd,YAAY,GAAG,UAAUsD,GAAG,EAAE;MAChD,IAAI,CAACT,UAAU,GAAGS,GAAG;IACvB,CAAC;IAEDlF,SAAS,CAACpb,SAAS,CAACugB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACT,UAAU;IACxB,CAAC;IAED1E,SAAS,CAACpb,SAAS,CAACid,YAAY,GAAG,UAAUuD,GAAG,EAAE;MAChD,IAAI,CAACV,UAAU,GAAGU,GAAG;IACvB,CAAC;IAEDpF,SAAS,CAACpb,SAAS,CAACygB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAED7E,SAAS,CAACpb,SAAS,CAAC0gB,YAAY,GAAG,UAAUC,GAAG,EAAE;MAChD,IAAI,CAACV,UAAU,GAAGU,GAAG;IACvB,CAAC;IAEDvF,SAAS,CAACpb,SAAS,CAAC4gB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACV,UAAU;IACxB,CAAC;IAED9E,SAAS,CAACpb,SAAS,CAAC6gB,YAAY,GAAG,UAAUC,GAAG,EAAE;MAChD,IAAI,CAACZ,UAAU,GAAGY,GAAG;IACvB,CAAC;;IAED;;IAEA1F,SAAS,CAACpb,SAAS,CAAC+gB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAChB,WAAW;IACzB,CAAC;IAED3E,SAAS,CAACpb,SAAS,CAACkd,aAAa,GAAG,UAAU8D,GAAG,EAAE;MACjD,IAAI,CAACjB,WAAW,GAAGiB,GAAG;IACxB,CAAC;IAED5F,SAAS,CAACpb,SAAS,CAACihB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAACjB,WAAW;IACzB,CAAC;IAED5E,SAAS,CAACpb,SAAS,CAACmd,aAAa,GAAG,UAAU+D,GAAG,EAAE;MACjD,IAAI,CAAClB,WAAW,GAAGkB,GAAG;IACxB,CAAC;IAED9F,SAAS,CAACpb,SAAS,CAACmhB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAChB,WAAW;IACzB,CAAC;IAED/E,SAAS,CAACpb,SAAS,CAACohB,aAAa,GAAG,UAAUC,GAAG,EAAE;MACjD,IAAI,CAAClB,WAAW,GAAGkB,GAAG;IACxB,CAAC;IAEDjG,SAAS,CAACpb,SAAS,CAACshB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAClB,WAAW;IACzB,CAAC;IAEDhF,SAAS,CAACpb,SAAS,CAACuhB,aAAa,GAAG,UAAUC,GAAG,EAAE;MACjD,IAAI,CAACpB,WAAW,GAAGoB,GAAG;IACxB,CAAC;IAEDpG,SAAS,CAACpb,SAAS,CAACyhB,UAAU,GAAG,UAAUpc,CAAC,EAAE;MAC5C,IAAIqc,OAAO,GAAG,GAAG;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAC1B,UAAU;MAC/B,IAAI0B,SAAS,IAAI,GAAG,EAAE;QACpBD,OAAO,GAAG,IAAI,CAAC3B,WAAW,GAAG,CAAC1a,CAAC,GAAG,IAAI,CAACwa,UAAU,IAAI,IAAI,CAACM,WAAW,GAAGwB,SAAS;MACnF;MAEA,OAAOD,OAAO;IAChB,CAAC;IAEDtG,SAAS,CAACpb,SAAS,CAAC4hB,UAAU,GAAG,UAAUtc,CAAC,EAAE;MAC5C,IAAIuc,OAAO,GAAG,GAAG;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAC5B,UAAU;MAC/B,IAAI4B,SAAS,IAAI,GAAG,EAAE;QACpBD,OAAO,GAAG,IAAI,CAAC7B,WAAW,GAAG,CAAC1a,CAAC,GAAG,IAAI,CAACwa,UAAU,IAAI,IAAI,CAACM,WAAW,GAAG0B,SAAS;MACnF;MAEA,OAAOD,OAAO;IAChB,CAAC;IAEDzG,SAAS,CAACpb,SAAS,CAAC+hB,iBAAiB,GAAG,UAAU1c,CAAC,EAAE;MACnD,IAAI2c,MAAM,GAAG,GAAG;MAChB,IAAIC,UAAU,GAAG,IAAI,CAAC9B,WAAW;MACjC,IAAI8B,UAAU,IAAI,GAAG,EAAE;QACrBD,MAAM,GAAG,IAAI,CAACnC,UAAU,GAAG,CAACxa,CAAC,GAAG,IAAI,CAAC0a,WAAW,IAAI,IAAI,CAACE,UAAU,GAAGgC,UAAU;MAClF;MAEA,OAAOD,MAAM;IACf,CAAC;IAED5G,SAAS,CAACpb,SAAS,CAACkiB,iBAAiB,GAAG,UAAU5c,CAAC,EAAE;MACnD,IAAI6c,MAAM,GAAG,GAAG;MAChB,IAAIC,UAAU,GAAG,IAAI,CAAChC,WAAW;MACjC,IAAIgC,UAAU,IAAI,GAAG,EAAE;QACrBD,MAAM,GAAG,IAAI,CAACrC,UAAU,GAAG,CAACxa,CAAC,GAAG,IAAI,CAAC0a,WAAW,IAAI,IAAI,CAACE,UAAU,GAAGkC,UAAU;MAClF;MACA,OAAOD,MAAM;IACf,CAAC;IAED/G,SAAS,CAACpb,SAAS,CAAC8J,qBAAqB,GAAG,UAAUuY,OAAO,EAAE;MAC7D,IAAIC,QAAQ,GAAG,IAAI9d,MAAM,CAAC,IAAI,CAACud,iBAAiB,CAACM,OAAO,CAAChd,CAAC,CAAC,EAAE,IAAI,CAAC6c,iBAAiB,CAACG,OAAO,CAAC/c,CAAC,CAAC,CAAC;MAC/F,OAAOgd,QAAQ;IACjB,CAAC;IAEDlkB,MAAM,CAACD,OAAO,GAAGid,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAAShd,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASsc,kBAAkBA,CAACC,GAAG,EAAE;MAAE,IAAIvX,KAAK,CAACwX,OAAO,CAACD,GAAG,CAAC,EAAE;QAAE,KAAK,IAAIrc,CAAC,GAAG,CAAC,EAAEuc,IAAI,GAAGzX,KAAK,CAACuX,GAAG,CAAC1Y,MAAM,CAAC,EAAE3D,CAAC,GAAGqc,GAAG,CAAC1Y,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAEuc,IAAI,CAACvc,CAAC,CAAC,GAAGqc,GAAG,CAACrc,CAAC,CAAC;QAAE;QAAE,OAAOuc,IAAI;MAAE,CAAC,MAAM;QAAE,OAAOzX,KAAK,CAAC0X,IAAI,CAACH,GAAG,CAAC;MAAE;IAAE;IAElM,IAAI9P,MAAM,GAAGzM,mBAAmB,CAAC,EAAE,CAAC;IACpC,IAAI0R,iBAAiB,GAAG1R,mBAAmB,CAAC,CAAC,CAAC;IAC9C,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAI8C,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IACtC,IAAI+C,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAAS8jB,QAAQA,CAAA,EAAG;MAClBrX,MAAM,CAACrM,IAAI,CAAC,IAAI,CAAC;MAEjB,IAAI,CAAC2jB,kCAAkC,GAAGrS,iBAAiB,CAACS,+CAA+C;MAC3G,IAAI,CAAC6R,eAAe,GAAGtS,iBAAiB,CAACE,mBAAmB;MAC5D,IAAI,CAACqS,cAAc,GAAGvS,iBAAiB,CAACG,uBAAuB;MAC/D,IAAI,CAACqS,iBAAiB,GAAGxS,iBAAiB,CAACI,0BAA0B;MACrE,IAAI,CAACqS,eAAe,GAAGzS,iBAAiB,CAACK,wBAAwB;MACjE,IAAI,CAACqS,uBAAuB,GAAG1S,iBAAiB,CAACM,iCAAiC;MAClF,IAAI,CAACqS,kBAAkB,GAAG3S,iBAAiB,CAACO,4BAA4B;MACxE,IAAI,CAACqS,0BAA0B,GAAG5S,iBAAiB,CAACQ,qCAAqC;MACzF,IAAI,CAACqS,4BAA4B,GAAG,GAAG,GAAG7S,iBAAiB,CAACE,mBAAmB,GAAG,GAAG;MACrF,IAAI,CAAC4S,aAAa,GAAG9S,iBAAiB,CAACW,kCAAkC;MACzE,IAAI,CAACoS,oBAAoB,GAAG/S,iBAAiB,CAACW,kCAAkC;MAChF,IAAI,CAACqS,iBAAiB,GAAG,GAAG;MAC5B,IAAI,CAACC,oBAAoB,GAAG,GAAG;MAC/B,IAAI,CAACC,aAAa,GAAGlT,iBAAiB,CAACC,cAAc;IACvD;IAEAmS,QAAQ,CAACviB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACkJ,MAAM,CAAClL,SAAS,CAAC;IAEpD,KAAK,IAAIiC,IAAI,IAAIiJ,MAAM,EAAE;MACvBqX,QAAQ,CAACtgB,IAAI,CAAC,GAAGiJ,MAAM,CAACjJ,IAAI,CAAC;IAC/B;IAEAsgB,QAAQ,CAACviB,SAAS,CAACuc,cAAc,GAAG,YAAY;MAC9CrR,MAAM,CAAClL,SAAS,CAACuc,cAAc,CAAC1d,IAAI,CAAC,IAAI,EAAEykB,SAAS,CAAC;MAErD,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,qBAAqB,GAAG,CAAC;MAE9B,IAAI,CAACC,gBAAgB,GAAGtT,iBAAiB,CAACU,6CAA6C;MAEvF,IAAI,CAAC6S,IAAI,GAAG,EAAE;IAChB,CAAC;IAEDnB,QAAQ,CAACviB,SAAS,CAAC2jB,oBAAoB,GAAG,YAAY;MACpD,IAAIzc,IAAI;MACR,IAAI0c,QAAQ;MACZ,IAAIliB,MAAM;MACV,IAAIC,MAAM;MACV,IAAIkiB,iBAAiB;MACrB,IAAIC,iBAAiB;MAErB,IAAI/U,QAAQ,GAAG,IAAI,CAAC5L,eAAe,CAAC,CAAC,CAAC+L,WAAW,CAAC,CAAC;MACnD,KAAK,IAAIvQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoQ,QAAQ,CAACzM,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxCuI,IAAI,GAAG6H,QAAQ,CAACpQ,CAAC,CAAC;QAElBuI,IAAI,CAAC6c,WAAW,GAAG,IAAI,CAACtB,eAAe;QAEvC,IAAIvb,IAAI,CAAC9E,YAAY,EAAE;UACrBV,MAAM,GAAGwF,IAAI,CAAChF,SAAS,CAAC,CAAC;UACzBP,MAAM,GAAGuF,IAAI,CAAC/E,SAAS,CAAC,CAAC;UAEzB0hB,iBAAiB,GAAG3c,IAAI,CAACxE,cAAc,CAAC,CAAC,CAAC0F,gBAAgB,CAAC,CAAC;UAC5D0b,iBAAiB,GAAG5c,IAAI,CAACtE,cAAc,CAAC,CAAC,CAACwF,gBAAgB,CAAC,CAAC;UAE5D,IAAI,IAAI,CAACoa,kCAAkC,EAAE;YAC3Ctb,IAAI,CAAC6c,WAAW,IAAIF,iBAAiB,GAAGC,iBAAiB,GAAG,CAAC,GAAG1jB,eAAe,CAACU,gBAAgB;UAClG;UAEA8iB,QAAQ,GAAG1c,IAAI,CAAC1E,MAAM,CAAC,CAAC,CAAC+G,qBAAqB,CAAC,CAAC;UAEhDrC,IAAI,CAAC6c,WAAW,IAAI5T,iBAAiB,CAACE,mBAAmB,GAAGF,iBAAiB,CAACmB,kCAAkC,IAAI5P,MAAM,CAAC6H,qBAAqB,CAAC,CAAC,GAAG5H,MAAM,CAAC4H,qBAAqB,CAAC,CAAC,GAAG,CAAC,GAAGqa,QAAQ,CAAC;QACrM;MACF;IACF,CAAC;IAEDrB,QAAQ,CAACviB,SAAS,CAACgkB,kBAAkB,GAAG,YAAY;MAElD,IAAI7jB,CAAC,GAAG,IAAI,CAACuO,WAAW,CAAC,CAAC,CAACpM,MAAM;MACjC,IAAI,IAAI,CAACmZ,WAAW,EAAE;QACpB,IAAItb,CAAC,GAAGgQ,iBAAiB,CAACa,2BAA2B,EAAE;UACrD,IAAI,CAACiS,aAAa,GAAGnf,IAAI,CAACmO,GAAG,CAAC,IAAI,CAACgR,aAAa,GAAG9S,iBAAiB,CAACY,yBAAyB,EAAE,IAAI,CAACkS,aAAa,GAAG,CAAC9iB,CAAC,GAAGgQ,iBAAiB,CAACa,2BAA2B,KAAKb,iBAAiB,CAACc,2BAA2B,GAAGd,iBAAiB,CAACa,2BAA2B,CAAC,GAAG,IAAI,CAACiS,aAAa,IAAI,CAAC,GAAG9S,iBAAiB,CAACY,yBAAyB,CAAC,CAAC;QACtV;QACA,IAAI,CAACkT,mBAAmB,GAAG9T,iBAAiB,CAACe,iCAAiC;MAChF,CAAC,MAAM;QACL,IAAI/Q,CAAC,GAAGgQ,iBAAiB,CAACa,2BAA2B,EAAE;UACrD,IAAI,CAACiS,aAAa,GAAGnf,IAAI,CAACmO,GAAG,CAAC9B,iBAAiB,CAACY,yBAAyB,EAAE,GAAG,GAAG,CAAC5Q,CAAC,GAAGgQ,iBAAiB,CAACa,2BAA2B,KAAKb,iBAAiB,CAACc,2BAA2B,GAAGd,iBAAiB,CAACa,2BAA2B,CAAC,IAAI,CAAC,GAAGb,iBAAiB,CAACY,yBAAyB,CAAC,CAAC;QAC7R,CAAC,MAAM;UACL,IAAI,CAACkS,aAAa,GAAG,GAAG;QAC1B;QACA,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACD,aAAa;QAC9C,IAAI,CAACgB,mBAAmB,GAAG9T,iBAAiB,CAACgB,qBAAqB;MACpE;MAEA,IAAI,CAACkS,aAAa,GAAGvf,IAAI,CAACmO,GAAG,CAAC,IAAI,CAACvD,WAAW,CAAC,CAAC,CAACpM,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC+gB,aAAa,CAAC;MAEhF,IAAI,CAACa,0BAA0B,GAAG,IAAI,CAAClB,4BAA4B,GAAG,IAAI,CAACtU,WAAW,CAAC,CAAC,CAACpM,MAAM;MAE/F,IAAI,CAAC6hB,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,CAAC;IAED7B,QAAQ,CAACviB,SAAS,CAACqkB,gBAAgB,GAAG,YAAY;MAChD,IAAIC,MAAM,GAAG,IAAI,CAACpV,WAAW,CAAC,CAAC;MAC/B,IAAIhI,IAAI;MAER,KAAK,IAAIvI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2lB,MAAM,CAAChiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCuI,IAAI,GAAGod,MAAM,CAAC3lB,CAAC,CAAC;QAEhB,IAAI,CAAC4lB,eAAe,CAACrd,IAAI,EAAEA,IAAI,CAAC6c,WAAW,CAAC;MAC9C;IACF,CAAC;IAEDxB,QAAQ,CAACviB,SAAS,CAACwkB,mBAAmB,GAAG,YAAY;MACnD,IAAIC,iBAAiB,GAAGnB,SAAS,CAAChhB,MAAM,GAAG,CAAC,IAAIghB,SAAS,CAAC,CAAC,CAAC,KAAK9W,SAAS,GAAG8W,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAChG,IAAIoB,4BAA4B,GAAGpB,SAAS,CAAChhB,MAAM,GAAG,CAAC,IAAIghB,SAAS,CAAC,CAAC,CAAC,KAAK9W,SAAS,GAAG8W,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAE5G,IAAI3kB,CAAC,EAAEgmB,CAAC;MACR,IAAIC,KAAK,EAAEC,KAAK;MAChB,IAAIC,MAAM,GAAG,IAAI,CAACpW,WAAW,CAAC,CAAC;MAC/B,IAAIqW,gBAAgB;MAEpB,IAAI,IAAI,CAACtB,gBAAgB,EAAE;QACzB,IAAI,IAAI,CAACF,eAAe,GAAGpT,iBAAiB,CAACoB,6BAA6B,IAAI,CAAC,IAAIkT,iBAAiB,EAAE;UACpG,IAAI,CAACO,UAAU,CAAC,CAAC;QACnB;QAEAD,gBAAgB,GAAG,IAAIrd,GAAG,CAAC,CAAC;;QAE5B;QACA,KAAK/I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmmB,MAAM,CAACxiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAClCimB,KAAK,GAAGE,MAAM,CAACnmB,CAAC,CAAC;UACjB,IAAI,CAACsmB,8BAA8B,CAACL,KAAK,EAAEG,gBAAgB,EAAEN,iBAAiB,EAAEC,4BAA4B,CAAC;UAC7GK,gBAAgB,CAACpd,GAAG,CAACid,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QACL,KAAKjmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmmB,MAAM,CAACxiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAClCimB,KAAK,GAAGE,MAAM,CAACnmB,CAAC,CAAC;UAEjB,KAAKgmB,CAAC,GAAGhmB,CAAC,GAAG,CAAC,EAAEgmB,CAAC,GAAGG,MAAM,CAACxiB,MAAM,EAAEqiB,CAAC,EAAE,EAAE;YACtCE,KAAK,GAAGC,MAAM,CAACH,CAAC,CAAC;;YAEjB;YACA,IAAIC,KAAK,CAACvhB,QAAQ,CAAC,CAAC,IAAIwhB,KAAK,CAACxhB,QAAQ,CAAC,CAAC,EAAE;cACxC;YACF;YAEA,IAAI,CAAC6hB,kBAAkB,CAACN,KAAK,EAAEC,KAAK,CAAC;UACvC;QACF;MACF;IACF,CAAC;IAEDtC,QAAQ,CAACviB,SAAS,CAACmlB,uBAAuB,GAAG,YAAY;MACvD,IAAIpiB,IAAI;MACR,IAAI+hB,MAAM,GAAG,IAAI,CAAC3V,6BAA6B,CAAC,CAAC;MAEjD,KAAK,IAAIxQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmmB,MAAM,CAACxiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCoE,IAAI,GAAG+hB,MAAM,CAACnmB,CAAC,CAAC;QAChB,IAAI,CAACymB,sBAAsB,CAACriB,IAAI,CAAC;MACnC;IACF,CAAC;IAEDwf,QAAQ,CAACviB,SAAS,CAACqlB,SAAS,GAAG,YAAY;MACzC,IAAIP,MAAM,GAAG,IAAI,CAACpW,WAAW,CAAC,CAAC;MAC/B,IAAI3L,IAAI;MAER,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmmB,MAAM,CAACxiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCoE,IAAI,GAAG+hB,MAAM,CAACnmB,CAAC,CAAC;QAChBoE,IAAI,CAAC6W,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IAED2I,QAAQ,CAACviB,SAAS,CAACukB,eAAe,GAAG,UAAUrd,IAAI,EAAE6c,WAAW,EAAE;MAChE,IAAIzY,UAAU,GAAGpE,IAAI,CAAChF,SAAS,CAAC,CAAC;MACjC,IAAIqJ,UAAU,GAAGrE,IAAI,CAAC/E,SAAS,CAAC,CAAC;MAEjC,IAAIG,MAAM;MACV,IAAIgjB,WAAW;MACf,IAAIC,YAAY;MAChB,IAAIC,YAAY;;MAEhB;MACA,IAAI,IAAI,CAAC3J,oBAAoB,IAAIvQ,UAAU,CAAC5F,QAAQ,CAAC,CAAC,IAAI,IAAI,IAAI6F,UAAU,CAAC7F,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;QAC/FwB,IAAI,CAAChD,kBAAkB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLgD,IAAI,CAAC3D,YAAY,CAAC,CAAC;QAEnB,IAAI2D,IAAI,CAACrF,2BAA2B,EAAE;UACpC;QACF;MACF;MAEAS,MAAM,GAAG4E,IAAI,CAAC7E,SAAS,CAAC,CAAC;MAEzB,IAAIC,MAAM,IAAI,CAAC,EAAE;;MAEjB;MACAgjB,WAAW,GAAG,IAAI,CAAC5C,cAAc,IAAIpgB,MAAM,GAAGyhB,WAAW,CAAC;;MAE1D;MACAwB,YAAY,GAAGD,WAAW,IAAIpe,IAAI,CAACtD,OAAO,GAAGtB,MAAM,CAAC;MACpDkjB,YAAY,GAAGF,WAAW,IAAIpe,IAAI,CAACrD,OAAO,GAAGvB,MAAM,CAAC;;MAEpD;MACAgJ,UAAU,CAACia,YAAY,IAAIA,YAAY;MACvCja,UAAU,CAACka,YAAY,IAAIA,YAAY;MACvCja,UAAU,CAACga,YAAY,IAAIA,YAAY;MACvCha,UAAU,CAACia,YAAY,IAAIA,YAAY;IACzC,CAAC;IAEDjD,QAAQ,CAACviB,SAAS,CAACklB,kBAAkB,GAAG,UAAUN,KAAK,EAAEC,KAAK,EAAE;MAC9D,IAAIpT,KAAK,GAAGmT,KAAK,CAACjhB,OAAO,CAAC,CAAC;MAC3B,IAAI+N,KAAK,GAAGmT,KAAK,CAAClhB,OAAO,CAAC,CAAC;MAC3B,IAAIgO,aAAa,GAAG,IAAIlO,KAAK,CAAC,CAAC,CAAC;MAChC,IAAIgiB,UAAU,GAAG,IAAIhiB,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIiiB,SAAS;MACb,IAAIC,SAAS;MACb,IAAIC,eAAe;MACnB,IAAIC,QAAQ;MACZ,IAAIC,cAAc;MAClB,IAAIC,eAAe;MACnB,IAAIC,eAAe;MAEnB,IAAIvU,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC;QAAE;QAC3B;UACE;UACAnQ,SAAS,CAACiQ,oBAAoB,CAACC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAExB,iBAAiB,CAACE,mBAAmB,GAAG,GAAG,CAAC;UAExG0V,eAAe,GAAG,CAAC,GAAGpU,aAAa,CAAC,CAAC,CAAC;UACtCqU,eAAe,GAAG,CAAC,GAAGrU,aAAa,CAAC,CAAC,CAAC;UAEtC,IAAIsU,gBAAgB,GAAGrB,KAAK,CAACzc,YAAY,GAAG0c,KAAK,CAAC1c,YAAY,IAAIyc,KAAK,CAACzc,YAAY,GAAG0c,KAAK,CAAC1c,YAAY,CAAC;;UAE1G;UACAyc,KAAK,CAACmB,eAAe,IAAIE,gBAAgB,GAAGF,eAAe;UAC3DnB,KAAK,CAACoB,eAAe,IAAIC,gBAAgB,GAAGD,eAAe;UAC3DnB,KAAK,CAACkB,eAAe,IAAIE,gBAAgB,GAAGF,eAAe;UAC3DlB,KAAK,CAACmB,eAAe,IAAIC,gBAAgB,GAAGD,eAAe;QAC7D,CAAC;QAAM;QACP;UACE;;UAEA,IAAI,IAAI,CAACnK,oBAAoB,IAAI+I,KAAK,CAAClf,QAAQ,CAAC,CAAC,IAAI,IAAI,IAAImf,KAAK,CAACnf,QAAQ,CAAC,CAAC,IAAI,IAAI;YAAE;YACrF;cACEggB,SAAS,GAAGhU,KAAK,CAACvN,UAAU,CAAC,CAAC,GAAGsN,KAAK,CAACtN,UAAU,CAAC,CAAC;cACnDwhB,SAAS,GAAGjU,KAAK,CAACtN,UAAU,CAAC,CAAC,GAAGqN,KAAK,CAACrN,UAAU,CAAC,CAAC;YACrD,CAAC;YAAM;YACP;cACE7C,SAAS,CAACmC,eAAe,CAAC+N,KAAK,EAAEC,KAAK,EAAE+T,UAAU,CAAC;cAEnDC,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;cACzCE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;YAC3C;;UAEF;UACA,IAAI3hB,IAAI,CAACC,GAAG,CAAC2hB,SAAS,CAAC,GAAGvV,iBAAiB,CAACiB,kBAAkB,EAAE;YAC9DsU,SAAS,GAAGlkB,KAAK,CAACwC,IAAI,CAAC0hB,SAAS,CAAC,GAAGvV,iBAAiB,CAACiB,kBAAkB;UAC1E;UAEA,IAAItN,IAAI,CAACC,GAAG,CAAC4hB,SAAS,CAAC,GAAGxV,iBAAiB,CAACiB,kBAAkB,EAAE;YAC9DuU,SAAS,GAAGnkB,KAAK,CAACwC,IAAI,CAAC2hB,SAAS,CAAC,GAAGxV,iBAAiB,CAACiB,kBAAkB;UAC1E;UAEAwU,eAAe,GAAGF,SAAS,GAAGA,SAAS,GAAGC,SAAS,GAAGA,SAAS;UAC/DE,QAAQ,GAAG/hB,IAAI,CAACG,IAAI,CAAC2hB,eAAe,CAAC;UAErCE,cAAc,GAAG,IAAI,CAACnD,iBAAiB,GAAGiC,KAAK,CAACzc,YAAY,GAAG0c,KAAK,CAAC1c,YAAY,GAAGyd,eAAe;;UAEnG;UACAG,eAAe,GAAGD,cAAc,GAAGJ,SAAS,GAAGG,QAAQ;UACvDG,eAAe,GAAGF,cAAc,GAAGH,SAAS,GAAGE,QAAQ;;UAEvD;UACAjB,KAAK,CAACmB,eAAe,IAAIA,eAAe;UACxCnB,KAAK,CAACoB,eAAe,IAAIA,eAAe;UACxCnB,KAAK,CAACkB,eAAe,IAAIA,eAAe;UACxClB,KAAK,CAACmB,eAAe,IAAIA,eAAe;QAC1C;IACJ,CAAC;IAEDzD,QAAQ,CAACviB,SAAS,CAAColB,sBAAsB,GAAG,UAAUriB,IAAI,EAAE;MAC1D,IAAI0M,UAAU;MACd,IAAIyW,YAAY;MAChB,IAAIC,YAAY;MAChB,IAAIT,SAAS;MACb,IAAIC,SAAS;MACb,IAAIS,YAAY;MAChB,IAAIC,YAAY;MAChB,IAAIthB,aAAa;MACjB0K,UAAU,GAAG1M,IAAI,CAACM,QAAQ,CAAC,CAAC;MAE5B6iB,YAAY,GAAG,CAACzW,UAAU,CAACvG,QAAQ,CAAC,CAAC,GAAGuG,UAAU,CAACzG,OAAO,CAAC,CAAC,IAAI,CAAC;MACjEmd,YAAY,GAAG,CAAC1W,UAAU,CAACxG,MAAM,CAAC,CAAC,GAAGwG,UAAU,CAACtG,SAAS,CAAC,CAAC,IAAI,CAAC;MACjEuc,SAAS,GAAG3iB,IAAI,CAACoB,UAAU,CAAC,CAAC,GAAG+hB,YAAY;MAC5CP,SAAS,GAAG5iB,IAAI,CAACqB,UAAU,CAAC,CAAC,GAAG+hB,YAAY;MAC5CC,YAAY,GAAGtiB,IAAI,CAACC,GAAG,CAAC2hB,SAAS,CAAC,GAAG3iB,IAAI,CAAC8C,QAAQ,CAAC,CAAC,GAAG,CAAC;MACxDwgB,YAAY,GAAGviB,IAAI,CAACC,GAAG,CAAC4hB,SAAS,CAAC,GAAG5iB,IAAI,CAACgD,SAAS,CAAC,CAAC,GAAG,CAAC;MAEzD,IAAIhD,IAAI,CAACM,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACyB,YAAY,CAAC1B,OAAO,CAAC,CAAC;QAAE;QAClD;UACE2B,aAAa,GAAG0K,UAAU,CAACrH,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC0a,kBAAkB;UAEvE,IAAIsD,YAAY,GAAGrhB,aAAa,IAAIshB,YAAY,GAAGthB,aAAa,EAAE;YAChEhC,IAAI,CAACujB,iBAAiB,GAAG,CAAC,IAAI,CAAC1D,eAAe,GAAG8C,SAAS;YAC1D3iB,IAAI,CAACwjB,iBAAiB,GAAG,CAAC,IAAI,CAAC3D,eAAe,GAAG+C,SAAS;UAC5D;QACF,CAAC;QAAM;QACP;UACE5gB,aAAa,GAAG0K,UAAU,CAACrH,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC2a,0BAA0B;UAE/E,IAAIqD,YAAY,GAAGrhB,aAAa,IAAIshB,YAAY,GAAGthB,aAAa,EAAE;YAChEhC,IAAI,CAACujB,iBAAiB,GAAG,CAAC,IAAI,CAAC1D,eAAe,GAAG8C,SAAS,GAAG,IAAI,CAAC7C,uBAAuB;YACzF9f,IAAI,CAACwjB,iBAAiB,GAAG,CAAC,IAAI,CAAC3D,eAAe,GAAG+C,SAAS,GAAG,IAAI,CAAC9C,uBAAuB;UAC3F;QACF;IACJ,CAAC;IAEDN,QAAQ,CAACviB,SAAS,CAACwmB,WAAW,GAAG,YAAY;MAC3C,IAAIC,SAAS;MACb,IAAIC,UAAU,GAAG,KAAK;MAEtB,IAAI,IAAI,CAACnD,eAAe,GAAG,IAAI,CAACF,aAAa,GAAG,CAAC,EAAE;QACjDqD,UAAU,GAAG5iB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACof,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,GAAG,CAAC;MAC/E;MAEAqD,SAAS,GAAG,IAAI,CAACtD,iBAAiB,GAAG,IAAI,CAACe,0BAA0B;MAEpE,IAAI,CAACd,oBAAoB,GAAG,IAAI,CAACD,iBAAiB;MAElD,OAAOsD,SAAS,IAAIC,UAAU;IAChC,CAAC;IAEDnE,QAAQ,CAACviB,SAAS,CAAC2mB,OAAO,GAAG,YAAY;MACvC,IAAI,IAAI,CAAChL,qBAAqB,IAAI,CAAC,IAAI,CAACM,WAAW,EAAE;QACnD,IAAI,IAAI,CAACuH,qBAAqB,IAAI,IAAI,CAAC5H,eAAe,EAAE;UACtD,IAAI,CAACgB,MAAM,CAAC,CAAC;UACb,IAAI,CAAC4G,qBAAqB,GAAG,CAAC;QAChC,CAAC,MAAM;UACL,IAAI,CAACA,qBAAqB,EAAE;QAC9B;MACF;IACF,CAAC;;IAED;IACAjB,QAAQ,CAACviB,SAAS,CAAC4mB,2BAA2B,GAAG,YAAY;MAC3D,IAAI7jB,IAAI;MACR,IAAI4L,QAAQ,GAAG,IAAI,CAAC7J,YAAY,CAAC4J,WAAW,CAAC,CAAC;MAE9C,KAAK,IAAI/P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgQ,QAAQ,CAACrM,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxCoE,IAAI,GAAG4L,QAAQ,CAAChQ,CAAC,CAAC;QAClBoE,IAAI,CAACoF,YAAY,GAAGpF,IAAI,CAACmF,eAAe,CAAC,CAAC;MAC5C;IACF,CAAC;;IAED;IACA;IACA;;IAEAqa,QAAQ,CAACviB,SAAS,CAAC6mB,QAAQ,GAAG,UAAU5jB,KAAK,EAAE;MAE7C,IAAI6jB,KAAK,GAAG,CAAC;MACb,IAAIC,KAAK,GAAG,CAAC;MAEbD,KAAK,GAAGnN,QAAQ,CAAC7V,IAAI,CAACwT,IAAI,CAAC,CAACrU,KAAK,CAACiG,QAAQ,CAAC,CAAC,GAAGjG,KAAK,CAAC+F,OAAO,CAAC,CAAC,IAAI,IAAI,CAACmb,cAAc,CAAC,CAAC;MACvF4C,KAAK,GAAGpN,QAAQ,CAAC7V,IAAI,CAACwT,IAAI,CAAC,CAACrU,KAAK,CAACkG,SAAS,CAAC,CAAC,GAAGlG,KAAK,CAACgG,MAAM,CAAC,CAAC,IAAI,IAAI,CAACkb,cAAc,CAAC,CAAC;MAEvF,IAAIT,IAAI,GAAG,IAAIjgB,KAAK,CAACqjB,KAAK,CAAC;MAE3B,KAAK,IAAInoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmoB,KAAK,EAAEnoB,CAAC,EAAE,EAAE;QAC9B+kB,IAAI,CAAC/kB,CAAC,CAAC,GAAG,IAAI8E,KAAK,CAACsjB,KAAK,CAAC;MAC5B;MAEA,KAAK,IAAIpoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmoB,KAAK,EAAEnoB,CAAC,EAAE,EAAE;QAC9B,KAAK,IAAIgmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,EAAEpC,CAAC,EAAE,EAAE;UAC9BjB,IAAI,CAAC/kB,CAAC,CAAC,CAACgmB,CAAC,CAAC,GAAG,IAAIlhB,KAAK,CAAC,CAAC;QAC1B;MACF;MAEA,OAAOigB,IAAI;IACb,CAAC;IAEDnB,QAAQ,CAACviB,SAAS,CAACgnB,aAAa,GAAG,UAAUpO,CAAC,EAAElP,IAAI,EAAEC,GAAG,EAAE;MAEzD,IAAIsd,MAAM,GAAG,CAAC;MACd,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,OAAO,GAAG,CAAC;MAEfH,MAAM,GAAGtN,QAAQ,CAAC7V,IAAI,CAACuT,KAAK,CAAC,CAACuB,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC0B,CAAC,GAAGqE,IAAI,IAAI,IAAI,CAACya,cAAc,CAAC,CAAC;MAC3E+C,OAAO,GAAGvN,QAAQ,CAAC7V,IAAI,CAACuT,KAAK,CAAC,CAACuB,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC4B,KAAK,GAAGqT,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC0B,CAAC,GAAGqE,IAAI,IAAI,IAAI,CAACya,cAAc,CAAC,CAAC;MAChGgD,MAAM,GAAGxN,QAAQ,CAAC7V,IAAI,CAACuT,KAAK,CAAC,CAACuB,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC2B,CAAC,GAAGqE,GAAG,IAAI,IAAI,CAACwa,cAAc,CAAC,CAAC;MAC1EiD,OAAO,GAAGzN,QAAQ,CAAC7V,IAAI,CAACuT,KAAK,CAAC,CAACuB,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC6B,MAAM,GAAGoT,CAAC,CAACjV,OAAO,CAAC,CAAC,CAAC2B,CAAC,GAAGqE,GAAG,IAAI,IAAI,CAACwa,cAAc,CAAC,CAAC;MAEhG,KAAK,IAAIxlB,CAAC,GAAGsoB,MAAM,EAAEtoB,CAAC,IAAIuoB,OAAO,EAAEvoB,CAAC,EAAE,EAAE;QACtC,KAAK,IAAIgmB,CAAC,GAAGwC,MAAM,EAAExC,CAAC,IAAIyC,OAAO,EAAEzC,CAAC,EAAE,EAAE;UACtC,IAAI,CAACjB,IAAI,CAAC/kB,CAAC,CAAC,CAACgmB,CAAC,CAAC,CAACtd,IAAI,CAACuR,CAAC,CAAC;UACvBA,CAAC,CAACyO,kBAAkB,CAACJ,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,CAAC;QACxD;MACF;IACF,CAAC;IAED7E,QAAQ,CAACviB,SAAS,CAACglB,UAAU,GAAG,YAAY;MAC1C,IAAIrmB,CAAC;MACL,IAAIimB,KAAK;MACT,IAAIE,MAAM,GAAG,IAAI,CAACpW,WAAW,CAAC,CAAC;MAE/B,IAAI,CAACgV,IAAI,GAAG,IAAI,CAACmD,QAAQ,CAAC,IAAI,CAAC/hB,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC;;MAEtD;MACA,KAAKzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmmB,MAAM,CAACxiB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAClCimB,KAAK,GAAGE,MAAM,CAACnmB,CAAC,CAAC;QACjB,IAAI,CAACqoB,aAAa,CAACpC,KAAK,EAAE,IAAI,CAAC9f,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC4F,OAAO,CAAC,CAAC,EAAE,IAAI,CAAClE,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6F,MAAM,CAAC,CAAC,CAAC;MACxG;IACF,CAAC;IAEDsZ,QAAQ,CAACviB,SAAS,CAACilB,8BAA8B,GAAG,UAAUL,KAAK,EAAEG,gBAAgB,EAAEN,iBAAiB,EAAEC,4BAA4B,EAAE;MAEtI,IAAI,IAAI,CAACnB,eAAe,GAAGpT,iBAAiB,CAACoB,6BAA6B,IAAI,CAAC,IAAIkT,iBAAiB,IAAIC,4BAA4B,EAAE;QACpI,IAAI4C,WAAW,GAAG,IAAI5f,GAAG,CAAC,CAAC;QAC3Bkd,KAAK,CAAC0C,WAAW,GAAG,IAAI7jB,KAAK,CAAC,CAAC;QAC/B,IAAIohB,KAAK;QACT,IAAInB,IAAI,GAAG,IAAI,CAACA,IAAI;QAEpB,KAAK,IAAI/kB,CAAC,GAAGimB,KAAK,CAACqC,MAAM,GAAG,CAAC,EAAEtoB,CAAC,GAAGimB,KAAK,CAACsC,OAAO,GAAG,CAAC,EAAEvoB,CAAC,EAAE,EAAE;UACzD,KAAK,IAAIgmB,CAAC,GAAGC,KAAK,CAACuC,MAAM,GAAG,CAAC,EAAExC,CAAC,GAAGC,KAAK,CAACwC,OAAO,GAAG,CAAC,EAAEzC,CAAC,EAAE,EAAE;YACzD,IAAI,EAAEhmB,CAAC,GAAG,CAAC,IAAIgmB,CAAC,GAAG,CAAC,IAAIhmB,CAAC,IAAI+kB,IAAI,CAACphB,MAAM,IAAIqiB,CAAC,IAAIjB,IAAI,CAAC,CAAC,CAAC,CAACphB,MAAM,CAAC,EAAE;cAChE,KAAK,IAAI8b,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsF,IAAI,CAAC/kB,CAAC,CAAC,CAACgmB,CAAC,CAAC,CAACriB,MAAM,EAAE8b,CAAC,EAAE,EAAE;gBAC1CyG,KAAK,GAAGnB,IAAI,CAAC/kB,CAAC,CAAC,CAACgmB,CAAC,CAAC,CAACvG,CAAC,CAAC;;gBAErB;gBACA;gBACA,IAAIwG,KAAK,CAACvhB,QAAQ,CAAC,CAAC,IAAIwhB,KAAK,CAACxhB,QAAQ,CAAC,CAAC,IAAIuhB,KAAK,IAAIC,KAAK,EAAE;kBAC1D;gBACF;;gBAEA;gBACA;gBACA,IAAI,CAACE,gBAAgB,CAACxX,GAAG,CAACsX,KAAK,CAAC,IAAI,CAACyC,WAAW,CAAC/Z,GAAG,CAACsX,KAAK,CAAC,EAAE;kBAC3D,IAAIa,SAAS,GAAG5hB,IAAI,CAACC,GAAG,CAAC6gB,KAAK,CAACzgB,UAAU,CAAC,CAAC,GAAG0gB,KAAK,CAAC1gB,UAAU,CAAC,CAAC,CAAC,IAAIygB,KAAK,CAAC/e,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGgf,KAAK,CAAChf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;kBACjH,IAAI8f,SAAS,GAAG7hB,IAAI,CAACC,GAAG,CAAC6gB,KAAK,CAACxgB,UAAU,CAAC,CAAC,GAAGygB,KAAK,CAACzgB,UAAU,CAAC,CAAC,CAAC,IAAIwgB,KAAK,CAAC7e,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG8e,KAAK,CAAC9e,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;;kBAEnH;kBACA;kBACA,IAAI2f,SAAS,IAAI,IAAI,CAACvB,cAAc,IAAIwB,SAAS,IAAI,IAAI,CAACxB,cAAc,EAAE;oBACxE;oBACAmD,WAAW,CAAC3f,GAAG,CAACkd,KAAK,CAAC;kBACxB;gBACF;cACF;YACF;UACF;QACF;QAEAD,KAAK,CAAC0C,WAAW,GAAG,EAAE,CAAC/Y,MAAM,CAACwM,kBAAkB,CAACuM,WAAW,CAAC,CAAC;MAChE;MACA,KAAK3oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGimB,KAAK,CAAC0C,WAAW,CAAChlB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC7C,IAAI,CAACumB,kBAAkB,CAACN,KAAK,EAAEA,KAAK,CAAC0C,WAAW,CAAC3oB,CAAC,CAAC,CAAC;MACtD;IACF,CAAC;IAED4jB,QAAQ,CAACviB,SAAS,CAACokB,kBAAkB,GAAG,YAAY;MAClD,OAAO,GAAG;IACZ,CAAC;IAEDhmB,MAAM,CAACD,OAAO,GAAGokB,QAAQ;;IAEzB;EAAM,CAAC,IACP;EACA,KAAO,UAASnkB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAI0R,iBAAiB,GAAG1R,mBAAmB,CAAC,CAAC,CAAC;IAE9C,SAAS8oB,YAAYA,CAAC7lB,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;MAC3CH,KAAK,CAAC5C,IAAI,CAAC,IAAI,EAAE6C,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;MACvC,IAAI,CAACmiB,WAAW,GAAG5T,iBAAiB,CAACE,mBAAmB;IAC1D;IAEAkX,YAAY,CAACvnB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACP,KAAK,CAACzB,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIR,KAAK,EAAE;MACtB8lB,YAAY,CAACtlB,IAAI,CAAC,GAAGR,KAAK,CAACQ,IAAI,CAAC;IAClC;IAEA7D,MAAM,CAACD,OAAO,GAAGopB,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAASnpB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAAS+oB,YAAYA,CAAC9iB,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;MAC1C;MACAJ,KAAK,CAAC5F,IAAI,CAAC,IAAI,EAAE6F,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,CAAC;MACtC;MACA,IAAI,CAAC0gB,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACO,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACM,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;MACA,IAAI,CAACkB,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,aAAa,GAAG,CAAC;;MAEtB;MACA,IAAI,CAACT,MAAM,GAAG,CAAC;MACf,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,MAAM,GAAG,CAAC;MACf,IAAI,CAACC,OAAO,GAAG,CAAC;;MAEhB;MACA,IAAI,CAACE,WAAW,GAAG,EAAE;IACvB;IAEAE,YAAY,CAACxnB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACyC,KAAK,CAACzE,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIwC,KAAK,EAAE;MACtB+iB,YAAY,CAACvlB,IAAI,CAAC,GAAGwC,KAAK,CAACxC,IAAI,CAAC;IAClC;IAEAulB,YAAY,CAACxnB,SAAS,CAACqnB,kBAAkB,GAAG,UAAUM,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAE;MAC1F,IAAI,CAACb,MAAM,GAAGU,OAAO;MACrB,IAAI,CAACT,OAAO,GAAGU,QAAQ;MACvB,IAAI,CAACT,MAAM,GAAGU,OAAO;MACrB,IAAI,CAACT,OAAO,GAAGU,QAAQ;IACzB,CAAC;IAED1pB,MAAM,CAACD,OAAO,GAAGqpB,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAASppB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS4L,UAAUA,CAAC9E,KAAK,EAAEC,MAAM,EAAE;MACjC,IAAI,CAACD,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,MAAM,GAAG,CAAC;MACf,IAAID,KAAK,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;QACrC,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;MACpB;IACF;IAEA8E,UAAU,CAACrK,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAED8E,UAAU,CAACrK,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC/C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC;IAED8E,UAAU,CAACrK,SAAS,CAAC+F,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACP,MAAM;IACpB,CAAC;IAED6E,UAAU,CAACrK,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MACjD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGkM,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASjM,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6b,iBAAiB,GAAG7b,mBAAmB,CAAC,EAAE,CAAC;IAE/C,SAASspB,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;MACb,IAAI,CAAC7J,IAAI,GAAG,EAAE;IAChB;IAEA4J,OAAO,CAAC/nB,SAAS,CAACioB,GAAG,GAAG,UAAUrQ,GAAG,EAAE5Y,KAAK,EAAE;MAC5C,IAAIkpB,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAI,CAACuQ,QAAQ,CAACD,KAAK,CAAC,EAAE;QACzB,IAAI,CAACF,GAAG,CAACE,KAAK,CAAC,GAAGlpB,KAAK;QACvB,IAAI,CAACmf,IAAI,CAAC9W,IAAI,CAACuQ,GAAG,CAAC;MACrB;IACF,CAAC;IAEDmQ,OAAO,CAAC/nB,SAAS,CAACmoB,QAAQ,GAAG,UAAUvQ,GAAG,EAAE;MAC1C,IAAIsQ,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,OAAO,IAAI,CAACoQ,GAAG,CAACpQ,GAAG,CAAC,IAAI,IAAI;IAC9B,CAAC;IAEDmQ,OAAO,CAAC/nB,SAAS,CAACP,GAAG,GAAG,UAAUmY,GAAG,EAAE;MACrC,IAAIsQ,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,OAAO,IAAI,CAACoQ,GAAG,CAACE,KAAK,CAAC;IACxB,CAAC;IAEDH,OAAO,CAAC/nB,SAAS,CAACooB,MAAM,GAAG,YAAY;MACrC,OAAO,IAAI,CAACjK,IAAI;IAClB,CAAC;IAED/f,MAAM,CAACD,OAAO,GAAG4pB,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAAS3pB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6b,iBAAiB,GAAG7b,mBAAmB,CAAC,EAAE,CAAC;IAE/C,SAAS4pB,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACzK,GAAG,GAAG,CAAC,CAAC;IACf;IACA;IAEAyK,OAAO,CAACroB,SAAS,CAAC2H,GAAG,GAAG,UAAUiE,GAAG,EAAE;MACrC,IAAIsc,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5O,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAI,CAACuc,QAAQ,CAACD,KAAK,CAAC,EAAE,IAAI,CAACtK,GAAG,CAACsK,KAAK,CAAC,GAAGtc,GAAG;IAClD,CAAC;IAEDyc,OAAO,CAACroB,SAAS,CAAC2L,MAAM,GAAG,UAAUC,GAAG,EAAE;MACxC,OAAO,IAAI,CAACgS,GAAG,CAACtD,iBAAiB,CAACE,QAAQ,CAAC5O,GAAG,CAAC,CAAC;IAClD,CAAC;IAEDyc,OAAO,CAACroB,SAAS,CAACsoB,KAAK,GAAG,YAAY;MACpC,IAAI,CAAC1K,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAEDyK,OAAO,CAACroB,SAAS,CAACmoB,QAAQ,GAAG,UAAUvc,GAAG,EAAE;MAC1C,OAAO,IAAI,CAACgS,GAAG,CAACtD,iBAAiB,CAACE,QAAQ,CAAC5O,GAAG,CAAC,CAAC,IAAIA,GAAG;IACzD,CAAC;IAEDyc,OAAO,CAACroB,SAAS,CAACuoB,OAAO,GAAG,YAAY;MACtC,OAAO,IAAI,CAAC3jB,IAAI,CAAC,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEDyjB,OAAO,CAACroB,SAAS,CAAC4E,IAAI,GAAG,YAAY;MACnC,OAAOvF,MAAM,CAAC8e,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC,CAACtb,MAAM;IACrC,CAAC;;IAED;IACA+lB,OAAO,CAACroB,SAAS,CAACwoB,QAAQ,GAAG,UAAUlQ,IAAI,EAAE;MAC3C,IAAI6F,IAAI,GAAG9e,MAAM,CAAC8e,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC;MAChC,IAAItb,MAAM,GAAG6b,IAAI,CAAC7b,MAAM;MACxB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC/B2Z,IAAI,CAACjR,IAAI,CAAC,IAAI,CAACuW,GAAG,CAACO,IAAI,CAACxf,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC;IAED0pB,OAAO,CAACroB,SAAS,CAAC4E,IAAI,GAAG,YAAY;MACnC,OAAOvF,MAAM,CAAC8e,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC,CAACtb,MAAM;IACrC,CAAC;IAED+lB,OAAO,CAACroB,SAAS,CAACyoB,MAAM,GAAG,UAAUnQ,IAAI,EAAE;MACzC,IAAInY,CAAC,GAAGmY,IAAI,CAAChW,MAAM;MACnB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAIia,CAAC,GAAGN,IAAI,CAAC3Z,CAAC,CAAC;QACf,IAAI,CAACgJ,GAAG,CAACiR,CAAC,CAAC;MACb;IACF,CAAC;IAEDxa,MAAM,CAACD,OAAO,GAAGkqB,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAASjqB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI8Y,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAAC7V,MAAM,EAAE8V,KAAK,EAAE;QAAE,KAAK,IAAI9Y,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Y,KAAK,CAACnV,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI+Y,UAAU,GAAGD,KAAK,CAAC9Y,CAAC,CAAC;UAAE+Y,UAAU,CAAClY,UAAU,GAAGkY,UAAU,CAAClY,UAAU,IAAI,KAAK;UAAEkY,UAAU,CAACnY,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAImY,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEtY,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE+V,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAAC7X,SAAS,EAAE8X,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;;IAExJ;AACA;AACA;AACA;AACA;AACA;;IAEA,IAAIvN,UAAU,GAAGlM,mBAAmB,CAAC,EAAE,CAAC;IAExC,IAAIiqB,SAAS,GAAG,YAAY;MACxB,SAASA,SAASA,CAACC,CAAC,EAAEC,eAAe,EAAE;QACnC5Q,eAAe,CAAC,IAAI,EAAE0Q,SAAS,CAAC;QAEhC,IAAIE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKpc,SAAS,EAAE,IAAI,CAACoc,eAAe,GAAG,IAAI,CAACC,uBAAuB;QAElH,IAAIvmB,MAAM,GAAG,KAAK,CAAC;QACnB,IAAIqmB,CAAC,YAAYhe,UAAU,EAAErI,MAAM,GAAGqmB,CAAC,CAAC/jB,IAAI,CAAC,CAAC,CAAC,KAAKtC,MAAM,GAAGqmB,CAAC,CAACrmB,MAAM;QAErE,IAAI,CAACwmB,UAAU,CAACH,CAAC,EAAE,CAAC,EAAErmB,MAAM,GAAG,CAAC,CAAC;MACrC;MAEAiV,YAAY,CAACmR,SAAS,EAAE,CAAC;QACrB9Q,GAAG,EAAE,YAAY;QACjB5Y,KAAK,EAAE,SAAS8pB,UAAUA,CAACH,CAAC,EAAEzoB,CAAC,EAAE8W,CAAC,EAAE;UAChC,IAAI9W,CAAC,GAAG8W,CAAC,EAAE;YACP,IAAID,CAAC,GAAG,IAAI,CAACgS,UAAU,CAACJ,CAAC,EAAEzoB,CAAC,EAAE8W,CAAC,CAAC;YAChC,IAAI,CAAC8R,UAAU,CAACH,CAAC,EAAEzoB,CAAC,EAAE6W,CAAC,CAAC;YACxB,IAAI,CAAC+R,UAAU,CAACH,CAAC,EAAE5R,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;UAChC;QACJ;MACJ,CAAC,EAAE;QACCY,GAAG,EAAE,YAAY;QACjB5Y,KAAK,EAAE,SAAS+pB,UAAUA,CAACJ,CAAC,EAAEzoB,CAAC,EAAE8W,CAAC,EAAE;UAChC,IAAI3R,CAAC,GAAG,IAAI,CAAC2jB,IAAI,CAACL,CAAC,EAAEzoB,CAAC,CAAC;UACvB,IAAIvB,CAAC,GAAGuB,CAAC;UACT,IAAIykB,CAAC,GAAG3N,CAAC;UACT,OAAO,IAAI,EAAE;YACT,OAAO,IAAI,CAAC4R,eAAe,CAACvjB,CAAC,EAAE,IAAI,CAAC2jB,IAAI,CAACL,CAAC,EAAEhE,CAAC,CAAC,CAAC,EAAE;cAC7CA,CAAC,EAAE;YACP;YAAC,OAAO,IAAI,CAACiE,eAAe,CAAC,IAAI,CAACI,IAAI,CAACL,CAAC,EAAEhqB,CAAC,CAAC,EAAE0G,CAAC,CAAC,EAAE;cAC9C1G,CAAC,EAAE;YACP;YAAC,IAAIA,CAAC,GAAGgmB,CAAC,EAAE;cACR,IAAI,CAACsE,KAAK,CAACN,CAAC,EAAEhqB,CAAC,EAAEgmB,CAAC,CAAC;cACnBhmB,CAAC,EAAE;cACHgmB,CAAC,EAAE;YACP,CAAC,MAAM,OAAOA,CAAC;UACnB;QACJ;MACJ,CAAC,EAAE;QACC/M,GAAG,EAAE,MAAM;QACX5Y,KAAK,EAAE,SAASgqB,IAAIA,CAAClpB,MAAM,EAAEiM,KAAK,EAAE;UAChC,IAAIjM,MAAM,YAAY6K,UAAU,EAAE,OAAO7K,MAAM,CAACyZ,aAAa,CAACxN,KAAK,CAAC,CAAC,KAAK,OAAOjM,MAAM,CAACiM,KAAK,CAAC;QAClG;MACJ,CAAC,EAAE;QACC6L,GAAG,EAAE,MAAM;QACX5Y,KAAK,EAAE,SAASkqB,IAAIA,CAACppB,MAAM,EAAEiM,KAAK,EAAE/M,KAAK,EAAE;UACvC,IAAIc,MAAM,YAAY6K,UAAU,EAAE7K,MAAM,CAAC2Z,aAAa,CAAC1N,KAAK,EAAE/M,KAAK,CAAC,CAAC,KAAKc,MAAM,CAACiM,KAAK,CAAC,GAAG/M,KAAK;QACnG;MACJ,CAAC,EAAE;QACC4Y,GAAG,EAAE,OAAO;QACZ5Y,KAAK,EAAE,SAASiqB,KAAKA,CAACN,CAAC,EAAEhqB,CAAC,EAAEgmB,CAAC,EAAE;UAC3B,IAAI9G,IAAI,GAAG,IAAI,CAACmL,IAAI,CAACL,CAAC,EAAEhqB,CAAC,CAAC;UAC1B,IAAI,CAACuqB,IAAI,CAACP,CAAC,EAAEhqB,CAAC,EAAE,IAAI,CAACqqB,IAAI,CAACL,CAAC,EAAEhE,CAAC,CAAC,CAAC;UAChC,IAAI,CAACuE,IAAI,CAACP,CAAC,EAAEhE,CAAC,EAAE9G,IAAI,CAAC;QACzB;MACJ,CAAC,EAAE;QACCjG,GAAG,EAAE,yBAAyB;QAC9B5Y,KAAK,EAAE,SAAS6pB,uBAAuBA,CAAChS,CAAC,EAAEC,CAAC,EAAE;UAC1C,OAAOA,CAAC,GAAGD,CAAC;QAChB;MACJ,CAAC,CAAC,CAAC;MAEH,OAAO6R,SAAS;IACpB,CAAC,CAAC,CAAC;IAEHtqB,MAAM,CAACD,OAAO,GAAGuqB,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAAStqB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI8Y,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAAC7V,MAAM,EAAE8V,KAAK,EAAE;QAAE,KAAK,IAAI9Y,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Y,KAAK,CAACnV,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI+Y,UAAU,GAAGD,KAAK,CAAC9Y,CAAC,CAAC;UAAE+Y,UAAU,CAAClY,UAAU,GAAGkY,UAAU,CAAClY,UAAU,IAAI,KAAK;UAAEkY,UAAU,CAACnY,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAImY,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEtY,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE+V,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAAC7X,SAAS,EAAE8X,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;;IAExJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,IAAIiR,eAAe,GAAG,YAAY;MAC9B,SAASA,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAE;QAC3C,IAAIC,WAAW,GAAGhG,SAAS,CAAChhB,MAAM,GAAG,CAAC,IAAIghB,SAAS,CAAC,CAAC,CAAC,KAAK9W,SAAS,GAAG8W,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QACvF,IAAIiG,gBAAgB,GAAGjG,SAAS,CAAChhB,MAAM,GAAG,CAAC,IAAIghB,SAAS,CAAC,CAAC,CAAC,KAAK9W,SAAS,GAAG8W,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7F,IAAIkG,WAAW,GAAGlG,SAAS,CAAChhB,MAAM,GAAG,CAAC,IAAIghB,SAAS,CAAC,CAAC,CAAC,KAAK9W,SAAS,GAAG8W,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAExFtL,eAAe,CAAC,IAAI,EAAEmR,eAAe,CAAC;QAEtC,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;;QAE9B;QACA,IAAI,CAACC,IAAI,GAAGL,SAAS,CAAC9mB,MAAM,GAAG,CAAC;QAChC,IAAI,CAAConB,IAAI,GAAGL,SAAS,CAAC/mB,MAAM,GAAG,CAAC;;QAEhC;QACA,IAAI,CAACohB,IAAI,GAAG,IAAIjgB,KAAK,CAAC,IAAI,CAACgmB,IAAI,CAAC;QAChC,KAAK,IAAI9qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8qB,IAAI,EAAE9qB,CAAC,EAAE,EAAE;UAChC,IAAI,CAAC+kB,IAAI,CAAC/kB,CAAC,CAAC,GAAG,IAAI8E,KAAK,CAAC,IAAI,CAACimB,IAAI,CAAC;UAEnC,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+E,IAAI,EAAE/E,CAAC,EAAE,EAAE;YAChC,IAAI,CAACjB,IAAI,CAAC/kB,CAAC,CAAC,CAACgmB,CAAC,CAAC,GAAG,CAAC;UACvB;QACJ;;QAEA;QACA,IAAI,CAACgF,aAAa,GAAG,IAAIlmB,KAAK,CAAC,IAAI,CAACgmB,IAAI,CAAC;QACzC,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACH,IAAI,EAAEG,EAAE,EAAE,EAAE;UACnC,IAAI,CAACD,aAAa,CAACC,EAAE,CAAC,GAAG,IAAInmB,KAAK,CAAC,IAAI,CAACimB,IAAI,CAAC;UAE7C,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACH,IAAI,EAAEG,EAAE,EAAE,EAAE;YACnC,IAAI,CAACF,aAAa,CAACC,EAAE,CAAC,CAACC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACJ;;QAEA;QACA,IAAI,CAACC,UAAU,GAAG,EAAE;;QAEpB;QACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;QAEf;QACA,IAAI,CAACC,YAAY,CAAC,CAAC;MACvB;MAEAzS,YAAY,CAAC4R,eAAe,EAAE,CAAC;QAC3BvR,GAAG,EAAE,UAAU;QACf5Y,KAAK,EAAE,SAASirB,QAAQA,CAAA,EAAG;UACvB,OAAO,IAAI,CAACF,KAAK;QACrB;MACJ,CAAC,EAAE;QACCnS,GAAG,EAAE,eAAe;QACpB5Y,KAAK,EAAE,SAASkrB,aAAaA,CAAA,EAAG;UAC5B,OAAO,IAAI,CAACJ,UAAU;QAC1B;;QAEA;MAEJ,CAAC,EAAE;QACClS,GAAG,EAAE,cAAc;QACnB5Y,KAAK,EAAE,SAASgrB,YAAYA,CAAA,EAAG;UAC3B;UACA,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+E,IAAI,EAAE/E,CAAC,EAAE,EAAE;YAChC,IAAI,CAACjB,IAAI,CAAC,CAAC,CAAC,CAACiB,CAAC,CAAC,GAAG,IAAI,CAACjB,IAAI,CAAC,CAAC,CAAC,CAACiB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC6E,WAAW;YACxD,IAAI,CAACG,aAAa,CAAC,CAAC,CAAC,CAAChF,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;UACnD;;UAEA;UACA,KAAK,IAAIhmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8qB,IAAI,EAAE9qB,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC+kB,IAAI,CAAC/kB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC+kB,IAAI,CAAC/kB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC6qB,WAAW;YACxD,IAAI,CAACG,aAAa,CAAChrB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;UACnD;;UAEA;UACA,KAAK,IAAIwrB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACV,IAAI,EAAEU,GAAG,EAAE,EAAE;YACtC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACV,IAAI,EAAEU,GAAG,EAAE,EAAE;cACtC;cACA,IAAIC,IAAI,GAAG,KAAK,CAAC;cACjB,IAAI,IAAI,CAACjB,SAAS,CAACe,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAACd,SAAS,CAACe,GAAG,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,IAAI,CAAC3G,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,WAAW,CAAC,KAAKe,IAAI,GAAG,IAAI,CAAC3G,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACb,gBAAgB;cAE9K,IAAIe,EAAE,GAAG,IAAI,CAAC5G,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,IAAI,CAACZ,WAAW;cACnD,IAAI9f,IAAI,GAAG,IAAI,CAACga,IAAI,CAACyG,GAAG,CAAC,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACZ,WAAW;;cAErD;cACA,IAAIe,KAAK,GAAG,CAACF,IAAI,EAAEC,EAAE,EAAE5gB,IAAI,CAAC;cAC5B,IAAI8gB,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAAC;;cAE5C;cACA,IAAI,CAAC7G,IAAI,CAACyG,GAAG,CAAC,CAACC,GAAG,CAAC,GAAGG,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;cACvC,IAAI,CAACb,aAAa,CAACQ,GAAG,CAAC,CAACC,GAAG,CAAC,GAAG,CAACI,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClG;UACJ;;UAEA;UACA,IAAI,CAACX,KAAK,GAAG,IAAI,CAACrG,IAAI,CAAC,IAAI,CAAC+F,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;QACxD;;QAEA;MAEJ,CAAC,EAAE;QACC9R,GAAG,EAAE,oBAAoB;QACzB5Y,KAAK,EAAE,SAAS2rB,kBAAkBA,CAAA,EAAG;UACjC,IAAIC,mBAAmB,GAAG,EAAE;UAE5BA,mBAAmB,CAACvjB,IAAI,CAAC;YAAEwjB,GAAG,EAAE,CAAC,IAAI,CAACzB,SAAS,CAAC9mB,MAAM,EAAE,IAAI,CAAC+mB,SAAS,CAAC/mB,MAAM,CAAC;YAC1EwoB,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE;UACV,CAAC,CAAC;UAEF,OAAOH,mBAAmB,CAAC,CAAC,CAAC,EAAE;YAC3B,IAAIpR,OAAO,GAAGoR,mBAAmB,CAAC,CAAC,CAAC;YACpC,IAAI9Y,UAAU,GAAG,IAAI,CAAC6X,aAAa,CAACnQ,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,CAAC,CAACrR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,CAAC;YAEnE,IAAI/Y,UAAU,CAAC,CAAC,CAAC,EAAE;cACf8Y,mBAAmB,CAACvjB,IAAI,CAAC;gBAAEwjB,GAAG,EAAE,CAACrR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAErR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACpEC,IAAI,EAAE,IAAI,CAAC1B,SAAS,CAAC5P,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGrR,OAAO,CAACsR,IAAI;gBACvDC,IAAI,EAAE,IAAI,CAAC1B,SAAS,CAAC7P,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGrR,OAAO,CAACuR;cACvD,CAAC,CAAC;YACN;YACA,IAAIjZ,UAAU,CAAC,CAAC,CAAC,EAAE;cACf8Y,mBAAmB,CAACvjB,IAAI,CAAC;gBAAEwjB,GAAG,EAAE,CAACrR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAErR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChEC,IAAI,EAAE,IAAI,CAAC1B,SAAS,CAAC5P,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGrR,OAAO,CAACsR,IAAI;gBACvDC,IAAI,EAAE,GAAG,GAAGvR,OAAO,CAACuR;cACxB,CAAC,CAAC;YACN;YACA,IAAIjZ,UAAU,CAAC,CAAC,CAAC,EAAE;cACf8Y,mBAAmB,CAACvjB,IAAI,CAAC;gBAAEwjB,GAAG,EAAE,CAACrR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,EAAErR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAChEC,IAAI,EAAE,GAAG,GAAGtR,OAAO,CAACsR,IAAI;gBACxBC,IAAI,EAAE,IAAI,CAAC1B,SAAS,CAAC7P,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGrR,OAAO,CAACuR;cACvD,CAAC,CAAC;YACN;YAEA,IAAIvR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIrR,OAAO,CAACqR,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,CAACf,UAAU,CAACziB,IAAI,CAAC;cAAE+hB,SAAS,EAAE5P,OAAO,CAACsR,IAAI;cAC5FzB,SAAS,EAAE7P,OAAO,CAACuR;YACvB,CAAC,CAAC;YAEFH,mBAAmB,CAACvd,KAAK,CAAC,CAAC;UAC/B;UAEA,OAAO,IAAI,CAACyc,UAAU;QAC1B;;QAEA;MAEJ,CAAC,EAAE;QACClS,GAAG,EAAE,eAAe;QACpB5Y,KAAK,EAAE,SAASgsB,aAAaA,CAAChQ,GAAG,EAAElC,GAAG,EAAE;UACpC,IAAImS,OAAO,GAAG,EAAE;YACZtsB,CAAC,GAAG,CAAC,CAAC;UACV,OAAO,CAACA,CAAC,GAAGqc,GAAG,CAACvP,OAAO,CAACqN,GAAG,EAAEna,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;YACzCssB,OAAO,CAAC5jB,IAAI,CAAC1I,CAAC,CAAC;UACnB;UACA,OAAOssB,OAAO;QAClB;MACJ,CAAC,EAAE;QACCrT,GAAG,EAAE,oBAAoB;QACzB5Y,KAAK,EAAE,SAASyrB,kBAAkBA,CAACS,KAAK,EAAE;UACtC,OAAO,IAAI,CAACF,aAAa,CAACE,KAAK,EAAEpnB,IAAI,CAACmO,GAAG,CAACkZ,KAAK,CAAC,IAAI,EAAED,KAAK,CAAC,CAAC;QACjE;MACJ,CAAC,CAAC,CAAC;MAEH,OAAO/B,eAAe;IAC1B,CAAC,CAAC,CAAC;IAEH/qB,MAAM,CAACD,OAAO,GAAGgrB,eAAe;;IAEhC;EAAM,CAAC,IACP;EACA,KAAO,UAAS/qB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI2sB,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;MACrC;IACF,CAAC;IAEDA,UAAU,CAAC7I,QAAQ,GAAG9jB,mBAAmB,CAAC,EAAE,CAAC;IAC7C2sB,UAAU,CAACjb,iBAAiB,GAAG1R,mBAAmB,CAAC,CAAC,CAAC;IACrD2sB,UAAU,CAAC7D,YAAY,GAAG9oB,mBAAmB,CAAC,EAAE,CAAC;IACjD2sB,UAAU,CAAC5D,YAAY,GAAG/oB,mBAAmB,CAAC,EAAE,CAAC;IACjD2sB,UAAU,CAAC/gB,UAAU,GAAG5L,mBAAmB,CAAC,EAAE,CAAC;IAC/C2sB,UAAU,CAACrD,OAAO,GAAGtpB,mBAAmB,CAAC,EAAE,CAAC;IAC5C2sB,UAAU,CAAC/C,OAAO,GAAG5pB,mBAAmB,CAAC,EAAE,CAAC;IAC5C2sB,UAAU,CAAC7pB,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IAC7C2sB,UAAU,CAAC5pB,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IACzC2sB,UAAU,CAAC/mB,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IAC5C2sB,UAAU,CAAC1gB,KAAK,GAAGjM,mBAAmB,CAAC,EAAE,CAAC;IAC1C2sB,UAAU,CAAC5mB,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAC1C2sB,UAAU,CAAC7mB,UAAU,GAAG9F,mBAAmB,CAAC,EAAE,CAAC;IAC/C2sB,UAAU,CAAC9mB,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IAC/C2sB,UAAU,CAAChQ,SAAS,GAAG3c,mBAAmB,CAAC,EAAE,CAAC;IAC9C2sB,UAAU,CAAC9Q,iBAAiB,GAAG7b,mBAAmB,CAAC,EAAE,CAAC;IACtD2sB,UAAU,CAAC1C,SAAS,GAAGjqB,mBAAmB,CAAC,EAAE,CAAC;IAC9C2sB,UAAU,CAACzgB,UAAU,GAAGlM,mBAAmB,CAAC,EAAE,CAAC;IAC/C2sB,UAAU,CAAC9pB,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IAChD2sB,UAAU,CAACxgB,MAAM,GAAGnM,mBAAmB,CAAC,CAAC,CAAC;IAC1C2sB,UAAU,CAAC3pB,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IACzC2sB,UAAU,CAAC3gB,aAAa,GAAGhM,mBAAmB,CAAC,CAAC,CAAC;IACjD2sB,UAAU,CAAC3mB,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IACzC2sB,UAAU,CAAClgB,MAAM,GAAGzM,mBAAmB,CAAC,EAAE,CAAC;IAC3C2sB,UAAU,CAAChrB,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IACnD2sB,UAAU,CAACjC,eAAe,GAAG1qB,mBAAmB,CAAC,EAAE,CAAC;IAEpDL,MAAM,CAACD,OAAO,GAAGitB,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAAShtB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS4c,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACgQ,SAAS,GAAG,EAAE;IACrB;IAEA,IAAInrB,CAAC,GAAGmb,OAAO,CAACrb,SAAS;IAEzBE,CAAC,CAACorB,WAAW,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;MACzC,IAAI,CAACH,SAAS,CAAChkB,IAAI,CAAC;QAClBkkB,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC;IAEDtrB,CAAC,CAACurB,cAAc,GAAG,UAAUF,KAAK,EAAEC,QAAQ,EAAE;MAC5C,KAAK,IAAI7sB,CAAC,GAAG,IAAI,CAAC0sB,SAAS,CAAC/oB,MAAM,EAAE3D,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/C,IAAIC,CAAC,GAAG,IAAI,CAACysB,SAAS,CAAC1sB,CAAC,CAAC;QAEzB,IAAIC,CAAC,CAAC2sB,KAAK,KAAKA,KAAK,IAAI3sB,CAAC,CAAC4sB,QAAQ,KAAKA,QAAQ,EAAE;UAChD,IAAI,CAACH,SAAS,CAACrf,MAAM,CAACrN,CAAC,EAAE,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;IAEDuB,CAAC,CAACwrB,IAAI,GAAG,UAAUH,KAAK,EAAEI,IAAI,EAAE;MAC9B,KAAK,IAAIhtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC0sB,SAAS,CAAC/oB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC9C,IAAIC,CAAC,GAAG,IAAI,CAACysB,SAAS,CAAC1sB,CAAC,CAAC;QAEzB,IAAI4sB,KAAK,KAAK3sB,CAAC,CAAC2sB,KAAK,EAAE;UACrB3sB,CAAC,CAAC4sB,QAAQ,CAACG,IAAI,CAAC;QAClB;MACF;IACF,CAAC;IAEDvtB,MAAM,CAACD,OAAO,GAAGkd,OAAO;;IAExB;EAAM;EACN,UAAU,CAAC;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}