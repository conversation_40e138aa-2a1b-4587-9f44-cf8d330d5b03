{"ast": null, "code": "/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { Lexer as ChevrotainLexer, defaultLexerErrorProvider } from 'chevrotain';\nexport class DefaultLexerErrorMessageProvider {\n  buildUnexpectedCharactersMessage(fullText, startOffset, length, line, column) {\n    return defaultLexerErrorProvider.buildUnexpectedCharactersMessage(fullText, startOffset, length, line, column);\n  }\n  buildUnableToPopLexerModeMessage(token) {\n    return defaultLexerErrorProvider.buildUnableToPopLexerModeMessage(token);\n  }\n}\nexport const DEFAULT_TOKENIZE_OPTIONS = {\n  mode: 'full'\n};\nexport class DefaultLexer {\n  constructor(services) {\n    this.errorMessageProvider = services.parser.LexerErrorMessageProvider;\n    this.tokenBuilder = services.parser.TokenBuilder;\n    const tokens = this.tokenBuilder.buildTokens(services.Grammar, {\n      caseInsensitive: services.LanguageMetaData.caseInsensitive\n    });\n    this.tokenTypes = this.toTokenTypeDictionary(tokens);\n    const lexerTokens = isTokenTypeDictionary(tokens) ? Object.values(tokens) : tokens;\n    const production = services.LanguageMetaData.mode === 'production';\n    this.chevrotainLexer = new ChevrotainLexer(lexerTokens, {\n      positionTracking: 'full',\n      skipValidations: production,\n      errorMessageProvider: this.errorMessageProvider\n    });\n  }\n  get definition() {\n    return this.tokenTypes;\n  }\n  tokenize(text, _options = DEFAULT_TOKENIZE_OPTIONS) {\n    var _a, _b, _c;\n    const chevrotainResult = this.chevrotainLexer.tokenize(text);\n    return {\n      tokens: chevrotainResult.tokens,\n      errors: chevrotainResult.errors,\n      hidden: (_a = chevrotainResult.groups.hidden) !== null && _a !== void 0 ? _a : [],\n      report: (_c = (_b = this.tokenBuilder).flushLexingReport) === null || _c === void 0 ? void 0 : _c.call(_b, text)\n    };\n  }\n  toTokenTypeDictionary(buildTokens) {\n    if (isTokenTypeDictionary(buildTokens)) return buildTokens;\n    const tokens = isIMultiModeLexerDefinition(buildTokens) ? Object.values(buildTokens.modes).flat() : buildTokens;\n    const res = {};\n    tokens.forEach(token => res[token.name] = token);\n    return res;\n  }\n}\n/**\n * Returns a check whether the given TokenVocabulary is TokenType array\n */\nexport function isTokenTypeArray(tokenVocabulary) {\n  return Array.isArray(tokenVocabulary) && (tokenVocabulary.length === 0 || 'name' in tokenVocabulary[0]);\n}\n/**\n * Returns a check whether the given TokenVocabulary is IMultiModeLexerDefinition\n */\nexport function isIMultiModeLexerDefinition(tokenVocabulary) {\n  return tokenVocabulary && 'modes' in tokenVocabulary && 'defaultMode' in tokenVocabulary;\n}\n/**\n * Returns a check whether the given TokenVocabulary is TokenTypeDictionary\n */\nexport function isTokenTypeDictionary(tokenVocabulary) {\n  return !isTokenTypeArray(tokenVocabulary) && !isIMultiModeLexerDefinition(tokenVocabulary);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "ChevrotainLexer", "defaultLexerErrorProvider", "DefaultLexerErrorMessageProvider", "buildUnexpectedCharactersMessage", "fullText", "startOffset", "length", "line", "column", "buildUnableToPopLexerModeMessage", "token", "DEFAULT_TOKENIZE_OPTIONS", "mode", "DefaultLexer", "constructor", "services", "errorMessageProvider", "parser", "LexerErrorMessageProvider", "tokenBuilder", "TokenBuilder", "tokens", "buildTokens", "Grammar", "caseInsensitive", "LanguageMetaData", "tokenTypes", "toTokenTypeDictionary", "lexerTokens", "isTokenTypeDictionary", "Object", "values", "production", "chevrot<PERSON><PERSON><PERSON><PERSON>", "positionTracking", "skipValidations", "definition", "tokenize", "text", "_options", "chevrotainResult", "errors", "hidden", "_a", "groups", "report", "_c", "_b", "flushLexingReport", "call", "isIMultiModeLexerDefinition", "modes", "flat", "res", "for<PERSON>ach", "name", "isTokenTypeArray", "tokenVocabulary", "Array", "isArray"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/parser/lexer.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2022 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { ILexerErrorMessageProvider, ILexingError, IMultiModeLexerDefinition, IToken, TokenType, TokenTypeDictionary, TokenVocabulary } from 'chevrotain';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport { Lexer as ChevrotainLexer, defaultLexerErrorProvider } from 'chevrotain';\r\nimport type { LexingReport, TokenBuilder } from './token-builder.js';\r\n\r\nexport class DefaultLexerErrorMessageProvider implements ILexerErrorMessageProvider {\r\n\r\n    buildUnexpectedCharactersMessage(fullText: string, startOffset: number, length: number, line?: number, column?: number): string {\r\n        return defaultLexerErrorProvider.buildUnexpectedCharactersMessage(fullText, startOffset, length, line, column);\r\n    }\r\n\r\n    buildUnableToPopLexerModeMessage(token: IToken): string {\r\n        return defaultLexerErrorProvider.buildUnableToPopLexerModeMessage(token);\r\n    }\r\n}\r\n\r\nexport interface LexerResult {\r\n    /**\r\n     * A list of all tokens that were lexed from the input.\r\n     *\r\n     * Note that Langium requires the optional properties\r\n     * `startLine`, `startColumn`, `endOffset`, `endLine` and `endColumn` to be set on each token.\r\n     */\r\n    tokens: IToken[];\r\n    /**\r\n     * Contains hidden tokens, usually comments.\r\n     */\r\n    hidden: IToken[];\r\n    errors: ILexingError[];\r\n    report?: LexingReport;\r\n}\r\n\r\nexport type TokenizeMode = 'full' | 'partial';\r\n\r\nexport interface TokenizeOptions {\r\n    mode?: TokenizeMode;\r\n}\r\n\r\nexport const DEFAULT_TOKENIZE_OPTIONS: TokenizeOptions = { mode: 'full' };\r\n\r\nexport interface Lexer {\r\n    readonly definition: TokenTypeDictionary;\r\n    tokenize(text: string, options?: TokenizeOptions): LexerResult;\r\n}\r\n\r\nexport class DefaultLexer implements Lexer {\r\n\r\n    protected readonly tokenBuilder: TokenBuilder;\r\n    protected readonly errorMessageProvider: ILexerErrorMessageProvider;\r\n    protected tokenTypes: TokenTypeDictionary;\r\n    protected chevrotainLexer: ChevrotainLexer;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.errorMessageProvider = services.parser.LexerErrorMessageProvider;\r\n        this.tokenBuilder = services.parser.TokenBuilder;\r\n        const tokens = this.tokenBuilder.buildTokens(services.Grammar, {\r\n            caseInsensitive: services.LanguageMetaData.caseInsensitive\r\n        });\r\n        this.tokenTypes = this.toTokenTypeDictionary(tokens);\r\n        const lexerTokens = isTokenTypeDictionary(tokens) ? Object.values(tokens) : tokens;\r\n        const production = services.LanguageMetaData.mode === 'production';\r\n        this.chevrotainLexer = new ChevrotainLexer(lexerTokens, {\r\n            positionTracking: 'full',\r\n            skipValidations: production,\r\n            errorMessageProvider: this.errorMessageProvider\r\n        });\r\n    }\r\n\r\n    get definition(): TokenTypeDictionary {\r\n        return this.tokenTypes;\r\n    }\r\n\r\n    tokenize(text: string, _options: TokenizeOptions = DEFAULT_TOKENIZE_OPTIONS): LexerResult {\r\n        const chevrotainResult = this.chevrotainLexer.tokenize(text);\r\n        return {\r\n            tokens: chevrotainResult.tokens,\r\n            errors: chevrotainResult.errors,\r\n            hidden: chevrotainResult.groups.hidden ?? [],\r\n            report: this.tokenBuilder.flushLexingReport?.(text)\r\n        };\r\n    }\r\n\r\n    protected toTokenTypeDictionary(buildTokens: TokenVocabulary): TokenTypeDictionary {\r\n        if (isTokenTypeDictionary(buildTokens)) return buildTokens;\r\n        const tokens = isIMultiModeLexerDefinition(buildTokens) ? Object.values(buildTokens.modes).flat() : buildTokens;\r\n        const res: TokenTypeDictionary = {};\r\n        tokens.forEach(token => res[token.name] = token);\r\n        return res;\r\n    }\r\n}\r\n\r\n/**\r\n * Returns a check whether the given TokenVocabulary is TokenType array\r\n */\r\nexport function isTokenTypeArray(tokenVocabulary: TokenVocabulary): tokenVocabulary is TokenType[] {\r\n    return Array.isArray(tokenVocabulary) && (tokenVocabulary.length === 0 || 'name' in tokenVocabulary[0]);\r\n}\r\n\r\n/**\r\n * Returns a check whether the given TokenVocabulary is IMultiModeLexerDefinition\r\n */\r\nexport function isIMultiModeLexerDefinition(tokenVocabulary: TokenVocabulary): tokenVocabulary is IMultiModeLexerDefinition {\r\n    return tokenVocabulary && 'modes' in tokenVocabulary && 'defaultMode' in tokenVocabulary;\r\n}\r\n\r\n/**\r\n * Returns a check whether the given TokenVocabulary is TokenTypeDictionary\r\n */\r\nexport function isTokenTypeDictionary(tokenVocabulary: TokenVocabulary): tokenVocabulary is TokenTypeDictionary {\r\n    return !isTokenTypeArray(tokenVocabulary) && !isIMultiModeLexerDefinition(tokenVocabulary);\r\n}\r\n"], "mappings": "AAAA;;;;;AAQA,SAASA,KAAK,IAAIC,eAAe,EAAEC,yBAAyB,QAAQ,YAAY;AAGhF,OAAM,MAAOC,gCAAgC;EAEzCC,gCAAgCA,CAACC,QAAgB,EAAEC,WAAmB,EAAEC,MAAc,EAAEC,IAAa,EAAEC,MAAe;IAClH,OAAOP,yBAAyB,CAACE,gCAAgC,CAACC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,CAAC;EAClH;EAEAC,gCAAgCA,CAACC,KAAa;IAC1C,OAAOT,yBAAyB,CAACQ,gCAAgC,CAACC,KAAK,CAAC;EAC5E;;AAyBJ,OAAO,MAAMC,wBAAwB,GAAoB;EAAEC,IAAI,EAAE;AAAM,CAAE;AAOzE,OAAM,MAAOC,YAAY;EAOrBC,YAAYC,QAA6B;IACrC,IAAI,CAACC,oBAAoB,GAAGD,QAAQ,CAACE,MAAM,CAACC,yBAAyB;IACrE,IAAI,CAACC,YAAY,GAAGJ,QAAQ,CAACE,MAAM,CAACG,YAAY;IAChD,MAAMC,MAAM,GAAG,IAAI,CAACF,YAAY,CAACG,WAAW,CAACP,QAAQ,CAACQ,OAAO,EAAE;MAC3DC,eAAe,EAAET,QAAQ,CAACU,gBAAgB,CAACD;KAC9C,CAAC;IACF,IAAI,CAACE,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACN,MAAM,CAAC;IACpD,MAAMO,WAAW,GAAGC,qBAAqB,CAACR,MAAM,CAAC,GAAGS,MAAM,CAACC,MAAM,CAACV,MAAM,CAAC,GAAGA,MAAM;IAClF,MAAMW,UAAU,GAAGjB,QAAQ,CAACU,gBAAgB,CAACb,IAAI,KAAK,YAAY;IAClE,IAAI,CAACqB,eAAe,GAAG,IAAIjC,eAAe,CAAC4B,WAAW,EAAE;MACpDM,gBAAgB,EAAE,MAAM;MACxBC,eAAe,EAAEH,UAAU;MAC3BhB,oBAAoB,EAAE,IAAI,CAACA;KAC9B,CAAC;EACN;EAEA,IAAIoB,UAAUA,CAAA;IACV,OAAO,IAAI,CAACV,UAAU;EAC1B;EAEAW,QAAQA,CAACC,IAAY,EAAEC,QAAA,GAA4B5B,wBAAwB;;IACvE,MAAM6B,gBAAgB,GAAG,IAAI,CAACP,eAAe,CAACI,QAAQ,CAACC,IAAI,CAAC;IAC5D,OAAO;MACHjB,MAAM,EAAEmB,gBAAgB,CAACnB,MAAM;MAC/BoB,MAAM,EAAED,gBAAgB,CAACC,MAAM;MAC/BC,MAAM,EAAE,CAAAC,EAAA,GAAAH,gBAAgB,CAACI,MAAM,CAACF,MAAM,cAAAC,EAAA,cAAAA,EAAA,GAAI,EAAE;MAC5CE,MAAM,EAAE,CAAAC,EAAA,IAAAC,EAAA,OAAI,CAAC5B,YAAY,EAAC6B,iBAAiB,cAAAF,EAAA,uBAAAA,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAGT,IAAI;KACrD;EACL;EAEUX,qBAAqBA,CAACL,WAA4B;IACxD,IAAIO,qBAAqB,CAACP,WAAW,CAAC,EAAE,OAAOA,WAAW;IAC1D,MAAMD,MAAM,GAAG6B,2BAA2B,CAAC5B,WAAW,CAAC,GAAGQ,MAAM,CAACC,MAAM,CAACT,WAAW,CAAC6B,KAAK,CAAC,CAACC,IAAI,EAAE,GAAG9B,WAAW;IAC/G,MAAM+B,GAAG,GAAwB,EAAE;IACnChC,MAAM,CAACiC,OAAO,CAAC5C,KAAK,IAAI2C,GAAG,CAAC3C,KAAK,CAAC6C,IAAI,CAAC,GAAG7C,KAAK,CAAC;IAChD,OAAO2C,GAAG;EACd;;AAGJ;;;AAGA,OAAM,SAAUG,gBAAgBA,CAACC,eAAgC;EAC7D,OAAOC,KAAK,CAACC,OAAO,CAACF,eAAe,CAAC,KAAKA,eAAe,CAACnD,MAAM,KAAK,CAAC,IAAI,MAAM,IAAImD,eAAe,CAAC,CAAC,CAAC,CAAC;AAC3G;AAEA;;;AAGA,OAAM,SAAUP,2BAA2BA,CAACO,eAAgC;EACxE,OAAOA,eAAe,IAAI,OAAO,IAAIA,eAAe,IAAI,aAAa,IAAIA,eAAe;AAC5F;AAEA;;;AAGA,OAAM,SAAU5B,qBAAqBA,CAAC4B,eAAgC;EAClE,OAAO,CAACD,gBAAgB,CAACC,eAAe,CAAC,IAAI,CAACP,2BAA2B,CAACO,eAAe,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}