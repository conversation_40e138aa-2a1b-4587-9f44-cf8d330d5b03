{"ast": null, "code": "import baseInverter from './_baseInverter.js';\n\n/**\n * Creates a function like `_.invertBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} toIteratee The function to resolve iteratees.\n * @returns {Function} Returns the new inverter function.\n */\nfunction createInverter(setter, toIteratee) {\n  return function (object, iteratee) {\n    return baseInverter(object, setter, toIteratee(iteratee), {});\n  };\n}\nexport default createInverter;", "map": {"version": 3, "names": ["baseInverter", "createInverter", "setter", "toIteratee", "object", "iteratee"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_createInverter.js"], "sourcesContent": ["import baseInverter from './_baseInverter.js';\n\n/**\n * Creates a function like `_.invertBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} toIteratee The function to resolve iteratees.\n * @returns {Function} Returns the new inverter function.\n */\nfunction createInverter(setter, toIteratee) {\n  return function(object, iteratee) {\n    return baseInverter(object, setter, toIteratee(iteratee), {});\n  };\n}\n\nexport default createInverter;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC1C,OAAO,UAASC,MAAM,EAAEC,QAAQ,EAAE;IAChC,OAAOL,YAAY,CAACI,MAAM,EAAEF,MAAM,EAAEC,UAAU,CAACE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/D,CAAC;AACH;AAEA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}