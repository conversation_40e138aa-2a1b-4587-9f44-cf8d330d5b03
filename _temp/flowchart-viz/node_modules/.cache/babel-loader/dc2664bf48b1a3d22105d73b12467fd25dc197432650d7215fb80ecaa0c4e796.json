{"ast": null, "code": "/* IMPORT */\nimport _ from '../utils/index.js';\nimport ChannelsReusable from '../channels/reusable.js';\n/* MAIN */\nconst HSL = {\n  /* VARIABLES */\n  re: /^hsla?\\(\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?(?:deg|grad|rad|turn)?)\\s*?(?:,|\\s)\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?%)\\s*?(?:,|\\s)\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?%)(?:\\s*?(?:,|\\/)\\s*?\\+?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?(%)?))?\\s*?\\)$/i,\n  hueRe: /^(.+?)(deg|grad|rad|turn)$/i,\n  /* HELPERS */\n  _hue2deg: hue => {\n    const match = hue.match(HSL.hueRe);\n    if (match) {\n      const [, number, unit] = match;\n      switch (unit) {\n        case 'grad':\n          return _.channel.clamp.h(parseFloat(number) * .9);\n        case 'rad':\n          return _.channel.clamp.h(parseFloat(number) * 180 / Math.PI);\n        case 'turn':\n          return _.channel.clamp.h(parseFloat(number) * 360);\n      }\n    }\n    return _.channel.clamp.h(parseFloat(hue));\n  },\n  /* API */\n  parse: color => {\n    const charCode = color.charCodeAt(0);\n    if (charCode !== 104 && charCode !== 72) return; // 'h'/'H'\n    const match = color.match(HSL.re);\n    if (!match) return;\n    const [, h, s, l, a, isAlphaPercentage] = match;\n    return ChannelsReusable.set({\n      h: HSL._hue2deg(h),\n      s: _.channel.clamp.s(parseFloat(s)),\n      l: _.channel.clamp.l(parseFloat(l)),\n      a: a ? _.channel.clamp.a(isAlphaPercentage ? parseFloat(a) / 100 : parseFloat(a)) : 1\n    }, color);\n  },\n  stringify: channels => {\n    const {\n      h,\n      s,\n      l,\n      a\n    } = channels;\n    if (a < 1) {\n      // HSLA\n      return `hsla(${_.lang.round(h)}, ${_.lang.round(s)}%, ${_.lang.round(l)}%, ${a})`;\n    } else {\n      // HSL\n      return `hsl(${_.lang.round(h)}, ${_.lang.round(s)}%, ${_.lang.round(l)}%)`;\n    }\n  }\n};\n/* EXPORT */\nexport default HSL;", "map": {"version": 3, "names": ["_", "ChannelsReusable", "HSL", "re", "hueRe", "_hue2deg", "hue", "match", "number", "unit", "channel", "clamp", "h", "parseFloat", "Math", "PI", "parse", "color", "charCode", "charCodeAt", "s", "l", "a", "isAlphaPercentage", "set", "stringify", "channels", "lang", "round"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/color/hsl.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport ChannelsReusable from '../channels/reusable.js';\n/* MAIN */\nconst HSL = {\n    /* VARIABLES */\n    re: /^hsla?\\(\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?(?:deg|grad|rad|turn)?)\\s*?(?:,|\\s)\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?%)\\s*?(?:,|\\s)\\s*?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?%)(?:\\s*?(?:,|\\/)\\s*?\\+?(-?(?:\\d+(?:\\.\\d+)?|(?:\\.\\d+))(?:e-?\\d+)?(%)?))?\\s*?\\)$/i,\n    hueRe: /^(.+?)(deg|grad|rad|turn)$/i,\n    /* HELPERS */\n    _hue2deg: (hue) => {\n        const match = hue.match(HSL.hueRe);\n        if (match) {\n            const [, number, unit] = match;\n            switch (unit) {\n                case 'grad': return _.channel.clamp.h(parseFloat(number) * .9);\n                case 'rad': return _.channel.clamp.h(parseFloat(number) * 180 / Math.PI);\n                case 'turn': return _.channel.clamp.h(parseFloat(number) * 360);\n            }\n        }\n        return _.channel.clamp.h(parseFloat(hue));\n    },\n    /* API */\n    parse: (color) => {\n        const charCode = color.charCodeAt(0);\n        if (charCode !== 104 && charCode !== 72)\n            return; // 'h'/'H'\n        const match = color.match(HSL.re);\n        if (!match)\n            return;\n        const [, h, s, l, a, isAlphaPercentage] = match;\n        return ChannelsReusable.set({\n            h: HSL._hue2deg(h),\n            s: _.channel.clamp.s(parseFloat(s)),\n            l: _.channel.clamp.l(parseFloat(l)),\n            a: a ? _.channel.clamp.a(isAlphaPercentage ? parseFloat(a) / 100 : parseFloat(a)) : 1\n        }, color);\n    },\n    stringify: (channels) => {\n        const { h, s, l, a } = channels;\n        if (a < 1) { // HSLA\n            return `hsla(${_.lang.round(h)}, ${_.lang.round(s)}%, ${_.lang.round(l)}%, ${a})`;\n        }\n        else { // HSL\n            return `hsl(${_.lang.round(h)}, ${_.lang.round(s)}%, ${_.lang.round(l)}%)`;\n        }\n    }\n};\n/* EXPORT */\nexport default HSL;\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,mBAAmB;AACjC,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD;AACA,MAAMC,GAAG,GAAG;EACR;EACAC,EAAE,EAAE,kRAAkR;EACtRC,KAAK,EAAE,6BAA6B;EACpC;EACAC,QAAQ,EAAGC,GAAG,IAAK;IACf,MAAMC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAACL,GAAG,CAACE,KAAK,CAAC;IAClC,IAAIG,KAAK,EAAE;MACP,MAAM,GAAGC,MAAM,EAAEC,IAAI,CAAC,GAAGF,KAAK;MAC9B,QAAQE,IAAI;QACR,KAAK,MAAM;UAAE,OAAOT,CAAC,CAACU,OAAO,CAACC,KAAK,CAACC,CAAC,CAACC,UAAU,CAACL,MAAM,CAAC,GAAG,EAAE,CAAC;QAC9D,KAAK,KAAK;UAAE,OAAOR,CAAC,CAACU,OAAO,CAACC,KAAK,CAACC,CAAC,CAACC,UAAU,CAACL,MAAM,CAAC,GAAG,GAAG,GAAGM,IAAI,CAACC,EAAE,CAAC;QACxE,KAAK,MAAM;UAAE,OAAOf,CAAC,CAACU,OAAO,CAACC,KAAK,CAACC,CAAC,CAACC,UAAU,CAACL,MAAM,CAAC,GAAG,GAAG,CAAC;MACnE;IACJ;IACA,OAAOR,CAAC,CAACU,OAAO,CAACC,KAAK,CAACC,CAAC,CAACC,UAAU,CAACP,GAAG,CAAC,CAAC;EAC7C,CAAC;EACD;EACAU,KAAK,EAAGC,KAAK,IAAK;IACd,MAAMC,QAAQ,GAAGD,KAAK,CAACE,UAAU,CAAC,CAAC,CAAC;IACpC,IAAID,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,EAAE,EACnC,OAAO,CAAC;IACZ,MAAMX,KAAK,GAAGU,KAAK,CAACV,KAAK,CAACL,GAAG,CAACC,EAAE,CAAC;IACjC,IAAI,CAACI,KAAK,EACN;IACJ,MAAM,GAAGK,CAAC,EAAEQ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,iBAAiB,CAAC,GAAGhB,KAAK;IAC/C,OAAON,gBAAgB,CAACuB,GAAG,CAAC;MACxBZ,CAAC,EAAEV,GAAG,CAACG,QAAQ,CAACO,CAAC,CAAC;MAClBQ,CAAC,EAAEpB,CAAC,CAACU,OAAO,CAACC,KAAK,CAACS,CAAC,CAACP,UAAU,CAACO,CAAC,CAAC,CAAC;MACnCC,CAAC,EAAErB,CAAC,CAACU,OAAO,CAACC,KAAK,CAACU,CAAC,CAACR,UAAU,CAACQ,CAAC,CAAC,CAAC;MACnCC,CAAC,EAAEA,CAAC,GAAGtB,CAAC,CAACU,OAAO,CAACC,KAAK,CAACW,CAAC,CAACC,iBAAiB,GAAGV,UAAU,CAACS,CAAC,CAAC,GAAG,GAAG,GAAGT,UAAU,CAACS,CAAC,CAAC,CAAC,GAAG;IACxF,CAAC,EAAEL,KAAK,CAAC;EACb,CAAC;EACDQ,SAAS,EAAGC,QAAQ,IAAK;IACrB,MAAM;MAAEd,CAAC;MAAEQ,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGI,QAAQ;IAC/B,IAAIJ,CAAC,GAAG,CAAC,EAAE;MAAE;MACT,OAAO,QAAQtB,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAAChB,CAAC,CAAC,KAAKZ,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAACR,CAAC,CAAC,MAAMpB,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,MAAMC,CAAC,GAAG;IACrF,CAAC,MACI;MAAE;MACH,OAAO,OAAOtB,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAAChB,CAAC,CAAC,KAAKZ,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAACR,CAAC,CAAC,MAAMpB,CAAC,CAAC2B,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,IAAI;IAC9E;EACJ;AACJ,CAAC;AACD;AACA,eAAenB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}