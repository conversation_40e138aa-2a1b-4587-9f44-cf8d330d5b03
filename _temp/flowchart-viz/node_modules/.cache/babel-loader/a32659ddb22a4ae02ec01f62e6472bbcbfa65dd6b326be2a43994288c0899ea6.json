{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken, CancellationTokenSource } from '../utils/cancellation.js';\nimport { Deferred, isOperationCancelled, startCancelableOperation } from '../utils/promise-utils.js';\nexport class DefaultWorkspaceLock {\n  constructor() {\n    this.previousTokenSource = new CancellationTokenSource();\n    this.writeQueue = [];\n    this.readQueue = [];\n    this.done = true;\n  }\n  write(action) {\n    this.cancelWrite();\n    const tokenSource = startCancelableOperation();\n    this.previousTokenSource = tokenSource;\n    return this.enqueue(this.writeQueue, action, tokenSource.token);\n  }\n  read(action) {\n    return this.enqueue(this.readQueue, action);\n  }\n  enqueue(queue, action, cancellationToken = CancellationToken.None) {\n    const deferred = new Deferred();\n    const entry = {\n      action,\n      deferred,\n      cancellationToken\n    };\n    queue.push(entry);\n    this.performNextOperation();\n    return deferred.promise;\n  }\n  async performNextOperation() {\n    if (!this.done) {\n      return;\n    }\n    const entries = [];\n    if (this.writeQueue.length > 0) {\n      // Just perform the next write action\n      entries.push(this.writeQueue.shift());\n    } else if (this.readQueue.length > 0) {\n      // Empty the read queue and perform all actions in parallel\n      entries.push(...this.readQueue.splice(0, this.readQueue.length));\n    } else {\n      return;\n    }\n    this.done = false;\n    await Promise.all(entries.map(async ({\n      action,\n      deferred,\n      cancellationToken\n    }) => {\n      try {\n        // Move the execution of the action to the next event loop tick via `Promise.resolve()`\n        const result = await Promise.resolve().then(() => action(cancellationToken));\n        deferred.resolve(result);\n      } catch (err) {\n        if (isOperationCancelled(err)) {\n          // If the operation was cancelled, we don't want to reject the promise\n          deferred.resolve(undefined);\n        } else {\n          deferred.reject(err);\n        }\n      }\n    }));\n    this.done = true;\n    this.performNextOperation();\n  }\n  cancelWrite() {\n    this.previousTokenSource.cancel();\n  }\n}", "map": {"version": 3, "names": ["CancellationToken", "CancellationTokenSource", "Deferred", "isOperationCancelled", "startCancelableOperation", "DefaultWorkspaceLock", "constructor", "previousTokenSource", "writeQueue", "readQueue", "done", "write", "action", "cancelWrite", "tokenSource", "enqueue", "token", "read", "queue", "cancellationToken", "None", "deferred", "entry", "push", "performNextOperation", "promise", "entries", "length", "shift", "splice", "Promise", "all", "map", "result", "resolve", "then", "err", "undefined", "reject", "cancel"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/workspace-lock.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport { type AbstractCancellationTokenSource, CancellationToken, CancellationTokenSource } from '../utils/cancellation.js';\r\nimport { Deferred, isOperationCancelled, startCancelableOperation, type MaybePromise } from '../utils/promise-utils.js';\r\n\r\n/**\r\n * Utility service to execute mutually exclusive actions.\r\n */\r\nexport interface WorkspaceLock {\r\n    /**\r\n     * Performs a single async action, like initializing the workspace or processing document changes.\r\n     * Only one action will be executed at a time.\r\n     *\r\n     * When another action is queued up, the token provided for the action will be cancelled.\r\n     * Assuming the action makes use of this token, the next action only has to wait for the current action to finish cancellation.\r\n     */\r\n    write(action: (token: CancellationToken) => MaybePromise<void>): Promise<void>;\r\n\r\n    /**\r\n     * Performs a single action, like computing completion results or providing workspace symbols.\r\n     * Read actions will only be executed after all write actions have finished. They will be executed in parallel if possible.\r\n     *\r\n     * If a write action is currently running, the read action will be queued up and executed afterwards.\r\n     * If a new write action is queued up while a read action is waiting, the write action will receive priority and will be handled before the read action.\r\n     *\r\n     * Note that read actions are not allowed to modify anything in the workspace. Please use {@link write} instead.\r\n     */\r\n    read<T>(action: () => MaybePromise<T>): Promise<T>;\r\n\r\n    /**\r\n     * Cancels the last queued write action. All previous write actions already have been cancelled.\r\n     */\r\n    cancelWrite(): void;\r\n}\r\n\r\ntype LockAction<T = void> = (token: CancellationToken) => MaybePromise<T>;\r\n\r\ninterface LockEntry {\r\n    action: LockAction<unknown>;\r\n    deferred: Deferred<unknown>;\r\n    cancellationToken: CancellationToken;\r\n}\r\n\r\nexport class DefaultWorkspaceLock implements WorkspaceLock {\r\n\r\n    private previousTokenSource: AbstractCancellationTokenSource = new CancellationTokenSource();\r\n    private writeQueue: LockEntry[] = [];\r\n    private readQueue: LockEntry[] = [];\r\n    private done = true;\r\n\r\n    write(action: (token: CancellationToken) => MaybePromise<void>): Promise<void> {\r\n        this.cancelWrite();\r\n        const tokenSource = startCancelableOperation();\r\n        this.previousTokenSource = tokenSource;\r\n        return this.enqueue(this.writeQueue, action, tokenSource.token);\r\n    }\r\n\r\n    read<T>(action: () => MaybePromise<T>): Promise<T> {\r\n        return this.enqueue(this.readQueue, action);\r\n    }\r\n\r\n    private enqueue<T = void>(queue: LockEntry[], action: LockAction<T>, cancellationToken = CancellationToken.None): Promise<T> {\r\n        const deferred = new Deferred<unknown>();\r\n        const entry: LockEntry = {\r\n            action,\r\n            deferred,\r\n            cancellationToken\r\n        };\r\n        queue.push(entry);\r\n        this.performNextOperation();\r\n        return deferred.promise as Promise<T>;\r\n    }\r\n\r\n    private async performNextOperation(): Promise<void> {\r\n        if (!this.done) {\r\n            return;\r\n        }\r\n        const entries: LockEntry[] = [];\r\n        if (this.writeQueue.length > 0) {\r\n            // Just perform the next write action\r\n            entries.push(this.writeQueue.shift()!);\r\n        } else if (this.readQueue.length > 0) {\r\n            // Empty the read queue and perform all actions in parallel\r\n            entries.push(...this.readQueue.splice(0, this.readQueue.length));\r\n        } else {\r\n            return;\r\n        }\r\n        this.done = false;\r\n        await Promise.all(entries.map(async ({ action, deferred, cancellationToken }) => {\r\n            try {\r\n                // Move the execution of the action to the next event loop tick via `Promise.resolve()`\r\n                const result = await Promise.resolve().then(() => action(cancellationToken));\r\n                deferred.resolve(result);\r\n            } catch (err) {\r\n                if (isOperationCancelled(err)) {\r\n                    // If the operation was cancelled, we don't want to reject the promise\r\n                    deferred.resolve(undefined);\r\n                } else {\r\n                    deferred.reject(err);\r\n                }\r\n            }\r\n        }));\r\n        this.done = true;\r\n        this.performNextOperation();\r\n    }\r\n\r\n    cancelWrite(): void {\r\n        this.previousTokenSource.cancel();\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAMA,SAA+CA,iBAAiB,EAAEC,uBAAuB,QAAQ,0BAA0B;AAC3H,SAASC,QAAQ,EAAEC,oBAAoB,EAAEC,wBAAwB,QAA2B,2BAA2B;AAwCvH,OAAM,MAAOC,oBAAoB;EAAjCC,YAAA;IAEY,KAAAC,mBAAmB,GAAoC,IAAIN,uBAAuB,EAAE;IACpF,KAAAO,UAAU,GAAgB,EAAE;IAC5B,KAAAC,SAAS,GAAgB,EAAE;IAC3B,KAAAC,IAAI,GAAG,IAAI;EA6DvB;EA3DIC,KAAKA,CAACC,MAAwD;IAC1D,IAAI,CAACC,WAAW,EAAE;IAClB,MAAMC,WAAW,GAAGV,wBAAwB,EAAE;IAC9C,IAAI,CAACG,mBAAmB,GAAGO,WAAW;IACtC,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAACP,UAAU,EAAEI,MAAM,EAAEE,WAAW,CAACE,KAAK,CAAC;EACnE;EAEAC,IAAIA,CAAIL,MAA6B;IACjC,OAAO,IAAI,CAACG,OAAO,CAAC,IAAI,CAACN,SAAS,EAAEG,MAAM,CAAC;EAC/C;EAEQG,OAAOA,CAAWG,KAAkB,EAAEN,MAAqB,EAAEO,iBAAiB,GAAGnB,iBAAiB,CAACoB,IAAI;IAC3G,MAAMC,QAAQ,GAAG,IAAInB,QAAQ,EAAW;IACxC,MAAMoB,KAAK,GAAc;MACrBV,MAAM;MACNS,QAAQ;MACRF;KACH;IACDD,KAAK,CAACK,IAAI,CAACD,KAAK,CAAC;IACjB,IAAI,CAACE,oBAAoB,EAAE;IAC3B,OAAOH,QAAQ,CAACI,OAAqB;EACzC;EAEQ,MAAMD,oBAAoBA,CAAA;IAC9B,IAAI,CAAC,IAAI,CAACd,IAAI,EAAE;MACZ;IACJ;IACA,MAAMgB,OAAO,GAAgB,EAAE;IAC/B,IAAI,IAAI,CAAClB,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAE;MAC5B;MACAD,OAAO,CAACH,IAAI,CAAC,IAAI,CAACf,UAAU,CAACoB,KAAK,EAAG,CAAC;IAC1C,CAAC,MAAM,IAAI,IAAI,CAACnB,SAAS,CAACkB,MAAM,GAAG,CAAC,EAAE;MAClC;MACAD,OAAO,CAACH,IAAI,CAAC,GAAG,IAAI,CAACd,SAAS,CAACoB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpB,SAAS,CAACkB,MAAM,CAAC,CAAC;IACpE,CAAC,MAAM;MACH;IACJ;IACA,IAAI,CAACjB,IAAI,GAAG,KAAK;IACjB,MAAMoB,OAAO,CAACC,GAAG,CAACL,OAAO,CAACM,GAAG,CAAC,OAAO;MAAEpB,MAAM;MAAES,QAAQ;MAAEF;IAAiB,CAAE,KAAI;MAC5E,IAAI;QACA;QACA,MAAMc,MAAM,GAAG,MAAMH,OAAO,CAACI,OAAO,EAAE,CAACC,IAAI,CAAC,MAAMvB,MAAM,CAACO,iBAAiB,CAAC,CAAC;QAC5EE,QAAQ,CAACa,OAAO,CAACD,MAAM,CAAC;MAC5B,CAAC,CAAC,OAAOG,GAAG,EAAE;QACV,IAAIjC,oBAAoB,CAACiC,GAAG,CAAC,EAAE;UAC3B;UACAf,QAAQ,CAACa,OAAO,CAACG,SAAS,CAAC;QAC/B,CAAC,MAAM;UACHhB,QAAQ,CAACiB,MAAM,CAACF,GAAG,CAAC;QACxB;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC1B,IAAI,GAAG,IAAI;IAChB,IAAI,CAACc,oBAAoB,EAAE;EAC/B;EAEAX,WAAWA,CAAA;IACP,IAAI,CAACN,mBAAmB,CAACgC,MAAM,EAAE;EACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}