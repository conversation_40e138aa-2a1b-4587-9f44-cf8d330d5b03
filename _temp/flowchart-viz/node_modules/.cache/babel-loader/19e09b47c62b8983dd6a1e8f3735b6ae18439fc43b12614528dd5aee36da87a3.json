{"ast": null, "code": "import { every, includes, some } from \"lodash-es\";\nimport { AbstractProduction, Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Rule, Terminal } from \"./model.js\";\nexport function isSequenceProd(prod) {\n  return prod instanceof Alternative || prod instanceof Option || prod instanceof Repetition || prod instanceof RepetitionMandatory || prod instanceof RepetitionMandatoryWithSeparator || prod instanceof RepetitionWithSeparator || prod instanceof Terminal || prod instanceof Rule;\n}\nexport function isOptionalProd(prod, alreadyVisited = []) {\n  const isDirectlyOptional = prod instanceof Option || prod instanceof Repetition || prod instanceof RepetitionWithSeparator;\n  if (isDirectlyOptional) {\n    return true;\n  }\n  // note that this can cause infinite loop if one optional empty TOP production has a cyclic dependency with another\n  // empty optional top rule\n  // may be indirectly optional ((A?B?C?) | (D?E?F?))\n  if (prod instanceof Alternation) {\n    // for OR its enough for just one of the alternatives to be optional\n    return some(prod.definition, subProd => {\n      return isOptionalProd(subProd, alreadyVisited);\n    });\n  } else if (prod instanceof NonTerminal && includes(alreadyVisited, prod)) {\n    // avoiding stack overflow due to infinite recursion\n    return false;\n  } else if (prod instanceof AbstractProduction) {\n    if (prod instanceof NonTerminal) {\n      alreadyVisited.push(prod);\n    }\n    return every(prod.definition, subProd => {\n      return isOptionalProd(subProd, alreadyVisited);\n    });\n  } else {\n    return false;\n  }\n}\nexport function isBranchingProd(prod) {\n  return prod instanceof Alternation;\n}\nexport function getProductionDslName(prod) {\n  /* istanbul ignore else */\n  if (prod instanceof NonTerminal) {\n    return \"SUBRULE\";\n  } else if (prod instanceof Option) {\n    return \"OPTION\";\n  } else if (prod instanceof Alternation) {\n    return \"OR\";\n  } else if (prod instanceof RepetitionMandatory) {\n    return \"AT_LEAST_ONE\";\n  } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n    return \"AT_LEAST_ONE_SEP\";\n  } else if (prod instanceof RepetitionWithSeparator) {\n    return \"MANY_SEP\";\n  } else if (prod instanceof Repetition) {\n    return \"MANY\";\n  } else if (prod instanceof Terminal) {\n    return \"CONSUME\";\n    /* c8 ignore next 3 */\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}", "map": {"version": 3, "names": ["every", "includes", "some", "AbstractProduction", "Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Rule", "Terminal", "isSequenceProd", "prod", "isOptionalProd", "alreadyVisited", "isDirectlyOptional", "definition", "subProd", "push", "isBranchingProd", "getProductionDslName", "Error"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/gast/src/helpers.ts"], "sourcesContent": ["import { every, includes, some } from \"lodash-es\";\nimport {\n  AbstractProduction,\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n} from \"./model.js\";\nimport type { IProduction, IProductionWithOccurrence } from \"@chevrotain/types\";\n\nexport function isSequenceProd(\n  prod: IProduction,\n): prod is { definition: IProduction[] } & IProduction {\n  return (\n    prod instanceof Alternative ||\n    prod instanceof Option ||\n    prod instanceof Repetition ||\n    prod instanceof RepetitionMandatory ||\n    prod instanceof RepetitionMandatoryWithSeparator ||\n    prod instanceof RepetitionWithSeparator ||\n    prod instanceof Terminal ||\n    prod instanceof Rule\n  );\n}\n\nexport function isOptionalProd(\n  prod: IProduction,\n  alreadyVisited: NonTerminal[] = [],\n): boolean {\n  const isDirectlyOptional =\n    prod instanceof Option ||\n    prod instanceof Repetition ||\n    prod instanceof RepetitionWithSeparator;\n  if (isDirectlyOptional) {\n    return true;\n  }\n\n  // note that this can cause infinite loop if one optional empty TOP production has a cyclic dependency with another\n  // empty optional top rule\n  // may be indirectly optional ((A?B?C?) | (D?E?F?))\n  if (prod instanceof Alternation) {\n    // for OR its enough for just one of the alternatives to be optional\n    return some((<Alternation>prod).definition, (subProd: IProduction) => {\n      return isOptionalProd(subProd, alreadyVisited);\n    });\n  } else if (prod instanceof NonTerminal && includes(alreadyVisited, prod)) {\n    // avoiding stack overflow due to infinite recursion\n    return false;\n  } else if (prod instanceof AbstractProduction) {\n    if (prod instanceof NonTerminal) {\n      alreadyVisited.push(prod);\n    }\n    return every(\n      (<AbstractProduction>prod).definition,\n      (subProd: IProduction) => {\n        return isOptionalProd(subProd, alreadyVisited);\n      },\n    );\n  } else {\n    return false;\n  }\n}\n\nexport function isBranchingProd(\n  prod: IProduction,\n): prod is { definition: IProduction[] } & IProduction {\n  return prod instanceof Alternation;\n}\n\nexport function getProductionDslName(prod: IProductionWithOccurrence): string {\n  /* istanbul ignore else */\n  if (prod instanceof NonTerminal) {\n    return \"SUBRULE\";\n  } else if (prod instanceof Option) {\n    return \"OPTION\";\n  } else if (prod instanceof Alternation) {\n    return \"OR\";\n  } else if (prod instanceof RepetitionMandatory) {\n    return \"AT_LEAST_ONE\";\n  } else if (prod instanceof RepetitionMandatoryWithSeparator) {\n    return \"AT_LEAST_ONE_SEP\";\n  } else if (prod instanceof RepetitionWithSeparator) {\n    return \"MANY_SEP\";\n  } else if (prod instanceof Repetition) {\n    return \"MANY\";\n  } else if (prod instanceof Terminal) {\n    return \"CONSUME\";\n    /* c8 ignore next 3 */\n  } else {\n    throw Error(\"non exhaustive match\");\n  }\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,WAAW;AACjD,SACEC,kBAAkB,EAClBC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,IAAI,EACJC,QAAQ,QACH,YAAY;AAGnB,OAAM,SAAUC,cAAcA,CAC5BC,IAAiB;EAEjB,OACEA,IAAI,YAAYV,WAAW,IAC3BU,IAAI,YAAYR,MAAM,IACtBQ,IAAI,YAAYP,UAAU,IAC1BO,IAAI,YAAYN,mBAAmB,IACnCM,IAAI,YAAYL,gCAAgC,IAChDK,IAAI,YAAYJ,uBAAuB,IACvCI,IAAI,YAAYF,QAAQ,IACxBE,IAAI,YAAYH,IAAI;AAExB;AAEA,OAAM,SAAUI,cAAcA,CAC5BD,IAAiB,EACjBE,cAAA,GAAgC,EAAE;EAElC,MAAMC,kBAAkB,GACtBH,IAAI,YAAYR,MAAM,IACtBQ,IAAI,YAAYP,UAAU,IAC1BO,IAAI,YAAYJ,uBAAuB;EACzC,IAAIO,kBAAkB,EAAE;IACtB,OAAO,IAAI;;EAGb;EACA;EACA;EACA,IAAIH,IAAI,YAAYX,WAAW,EAAE;IAC/B;IACA,OAAOF,IAAI,CAAea,IAAK,CAACI,UAAU,EAAGC,OAAoB,IAAI;MACnE,OAAOJ,cAAc,CAACI,OAAO,EAAEH,cAAc,CAAC;IAChD,CAAC,CAAC;GACH,MAAM,IAAIF,IAAI,YAAYT,WAAW,IAAIL,QAAQ,CAACgB,cAAc,EAAEF,IAAI,CAAC,EAAE;IACxE;IACA,OAAO,KAAK;GACb,MAAM,IAAIA,IAAI,YAAYZ,kBAAkB,EAAE;IAC7C,IAAIY,IAAI,YAAYT,WAAW,EAAE;MAC/BW,cAAc,CAACI,IAAI,CAACN,IAAI,CAAC;;IAE3B,OAAOf,KAAK,CACWe,IAAK,CAACI,UAAU,EACpCC,OAAoB,IAAI;MACvB,OAAOJ,cAAc,CAACI,OAAO,EAAEH,cAAc,CAAC;IAChD,CAAC,CACF;GACF,MAAM;IACL,OAAO,KAAK;;AAEhB;AAEA,OAAM,SAAUK,eAAeA,CAC7BP,IAAiB;EAEjB,OAAOA,IAAI,YAAYX,WAAW;AACpC;AAEA,OAAM,SAAUmB,oBAAoBA,CAACR,IAA+B;EAClE;EACA,IAAIA,IAAI,YAAYT,WAAW,EAAE;IAC/B,OAAO,SAAS;GACjB,MAAM,IAAIS,IAAI,YAAYR,MAAM,EAAE;IACjC,OAAO,QAAQ;GAChB,MAAM,IAAIQ,IAAI,YAAYX,WAAW,EAAE;IACtC,OAAO,IAAI;GACZ,MAAM,IAAIW,IAAI,YAAYN,mBAAmB,EAAE;IAC9C,OAAO,cAAc;GACtB,MAAM,IAAIM,IAAI,YAAYL,gCAAgC,EAAE;IAC3D,OAAO,kBAAkB;GAC1B,MAAM,IAAIK,IAAI,YAAYJ,uBAAuB,EAAE;IAClD,OAAO,UAAU;GAClB,MAAM,IAAII,IAAI,YAAYP,UAAU,EAAE;IACrC,OAAO,MAAM;GACd,MAAM,IAAIO,IAAI,YAAYF,QAAQ,EAAE;IACnC,OAAO,SAAS;IAChB;GACD,MAAM;IACL,MAAMW,KAAK,CAAC,sBAAsB,CAAC;;AAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}