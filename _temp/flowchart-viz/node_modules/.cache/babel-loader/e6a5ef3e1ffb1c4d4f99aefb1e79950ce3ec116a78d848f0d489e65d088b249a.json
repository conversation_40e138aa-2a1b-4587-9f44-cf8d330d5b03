{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\nexport { sortSubgraph };\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n  var result = sort(entries, biasRight);\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n  return result;\n}\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(entry.vs.map(function (v) {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    }));\n  });\n}\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter = (target.barycenter * target.weight + other.barycenter * other.weight) / (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}", "map": {"version": 3, "names": ["_", "barycenter", "resolveConflicts", "sort", "sortSubgraph", "g", "v", "cg", "biasRight", "movable", "children", "node", "bl", "borderLeft", "undefined", "br", "borderRight", "subgraphs", "filter", "w", "barycenters", "for<PERSON>ach", "entry", "length", "subgraphResult", "Object", "prototype", "hasOwnProperty", "call", "mergeBarycenters", "entries", "expandSubgraphs", "result", "vs", "flatten", "predecessors", "blPred", "br<PERSON><PERSON>", "weight", "order", "map", "target", "other", "isUndefined"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,IAAI,QAAQ,WAAW;AAEhC,SAASC,YAAY;AAErB,SAASA,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,SAAS,EAAE;EACzC,IAAIC,OAAO,GAAGJ,CAAC,CAACK,QAAQ,CAACJ,CAAC,CAAC;EAC3B,IAAIK,IAAI,GAAGN,CAAC,CAACM,IAAI,CAACL,CAAC,CAAC;EACpB,IAAIM,EAAE,GAAGD,IAAI,GAAGA,IAAI,CAACE,UAAU,GAAGC,SAAS;EAC3C,IAAIC,EAAE,GAAGJ,IAAI,GAAGA,IAAI,CAACK,WAAW,GAAGF,SAAS;EAC5C,IAAIG,SAAS,GAAG,CAAC,CAAC;EAElB,IAAIL,EAAE,EAAE;IACNH,OAAO,GAAGT,CAAC,CAACkB,MAAM,CAACT,OAAO,EAAE,UAAUU,CAAC,EAAE;MACvC,OAAOA,CAAC,KAAKP,EAAE,IAAIO,CAAC,KAAKJ,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEA,IAAIK,WAAW,GAAGnB,UAAU,CAACI,CAAC,EAAEI,OAAO,CAAC;EACxCT,CAAC,CAACqB,OAAO,CAACD,WAAW,EAAE,UAAUE,KAAK,EAAE;IACtC,IAAIjB,CAAC,CAACK,QAAQ,CAACY,KAAK,CAAChB,CAAC,CAAC,CAACiB,MAAM,EAAE;MAC9B,IAAIC,cAAc,GAAGpB,YAAY,CAACC,CAAC,EAAEiB,KAAK,CAAChB,CAAC,EAAEC,EAAE,EAAEC,SAAS,CAAC;MAC5DS,SAAS,CAACK,KAAK,CAAChB,CAAC,CAAC,GAAGkB,cAAc;MACnC,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,cAAc,EAAE,YAAY,CAAC,EAAE;QACtEK,gBAAgB,CAACP,KAAK,EAAEE,cAAc,CAAC;MACzC;IACF;EACF,CAAC,CAAC;EAEF,IAAIM,OAAO,GAAG5B,gBAAgB,CAACkB,WAAW,EAAEb,EAAE,CAAC;EAC/CwB,eAAe,CAACD,OAAO,EAAEb,SAAS,CAAC;EAEnC,IAAIe,MAAM,GAAG7B,IAAI,CAAC2B,OAAO,EAAEtB,SAAS,CAAC;EAErC,IAAII,EAAE,EAAE;IACNoB,MAAM,CAACC,EAAE,GAAGjC,CAAC,CAACkC,OAAO,CAAC,CAACtB,EAAE,EAAEoB,MAAM,CAACC,EAAE,EAAElB,EAAE,CAAC,CAAC;IAC1C,IAAIV,CAAC,CAAC8B,YAAY,CAACvB,EAAE,CAAC,CAACW,MAAM,EAAE;MAC7B,IAAIa,MAAM,GAAG/B,CAAC,CAACM,IAAI,CAACN,CAAC,CAAC8B,YAAY,CAACvB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxCyB,MAAM,GAAGhC,CAAC,CAACM,IAAI,CAACN,CAAC,CAAC8B,YAAY,CAACpB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAACU,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACI,MAAM,EAAE,YAAY,CAAC,EAAE;QAC/DA,MAAM,CAAC/B,UAAU,GAAG,CAAC;QACrB+B,MAAM,CAACM,MAAM,GAAG,CAAC;MACnB;MACAN,MAAM,CAAC/B,UAAU,GACf,CAAC+B,MAAM,CAAC/B,UAAU,GAAG+B,MAAM,CAACM,MAAM,GAAGF,MAAM,CAACG,KAAK,GAAGF,MAAM,CAACE,KAAK,KAAKP,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;MACzFN,MAAM,CAACM,MAAM,IAAI,CAAC;IACpB;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,OAAO,EAAEb,SAAS,EAAE;EAC3CjB,CAAC,CAACqB,OAAO,CAACS,OAAO,EAAE,UAAUR,KAAK,EAAE;IAClCA,KAAK,CAACW,EAAE,GAAGjC,CAAC,CAACkC,OAAO,CAClBZ,KAAK,CAACW,EAAE,CAACO,GAAG,CAAC,UAAUlC,CAAC,EAAE;MACxB,IAAIW,SAAS,CAACX,CAAC,CAAC,EAAE;QAChB,OAAOW,SAAS,CAACX,CAAC,CAAC,CAAC2B,EAAE;MACxB;MACA,OAAO3B,CAAC;IACV,CAAC,CACH,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASuB,gBAAgBA,CAACY,MAAM,EAAEC,KAAK,EAAE;EACvC,IAAI,CAAC1C,CAAC,CAAC2C,WAAW,CAACF,MAAM,CAACxC,UAAU,CAAC,EAAE;IACrCwC,MAAM,CAACxC,UAAU,GACf,CAACwC,MAAM,CAACxC,UAAU,GAAGwC,MAAM,CAACH,MAAM,GAAGI,KAAK,CAACzC,UAAU,GAAGyC,KAAK,CAACJ,MAAM,KACnEG,MAAM,CAACH,MAAM,GAAGI,KAAK,CAACJ,MAAM,CAAC;IAChCG,MAAM,CAACH,MAAM,IAAII,KAAK,CAACJ,MAAM;EAC/B,CAAC,MAAM;IACLG,MAAM,CAACxC,UAAU,GAAGyC,KAAK,CAACzC,UAAU;IACpCwC,MAAM,CAACH,MAAM,GAAGI,KAAK,CAACJ,MAAM;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}