{"ast": null, "code": "/******************************************************************************\n * Copyright 2024 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n// eslint-disable-next-line no-restricted-imports\nexport * from 'vscode-jsonrpc/lib/common/cancellation.js';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/cancellation.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2024 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n// eslint-disable-next-line no-restricted-imports\r\nexport * from 'vscode-jsonrpc/lib/common/cancellation.js';\r\n"], "mappings": "AAAA;;;;;AAMA;AACA,cAAc,2CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}