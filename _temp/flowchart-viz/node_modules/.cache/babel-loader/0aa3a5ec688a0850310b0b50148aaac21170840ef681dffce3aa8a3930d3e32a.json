{"ast": null, "code": "import { drop, forEach } from \"lodash-es\";\nimport { Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Terminal } from \"@chevrotain/gast\";\n/**\n *  A Grammar Walker that computes the \"remaining\" grammar \"after\" a productions in the grammar.\n */\nexport class RestWalker {\n  walk(prod, prevRest = []) {\n    forEach(prod.definition, (subProd, index) => {\n      const currRest = drop(prod.definition, index + 1);\n      /* istanbul ignore else */\n      if (subProd instanceof NonTerminal) {\n        this.walkProdRef(subProd, currRest, prevRest);\n      } else if (subProd instanceof Terminal) {\n        this.walkTerminal(subProd, currRest, prevRest);\n      } else if (subProd instanceof Alternative) {\n        this.walkFlat(subProd, currRest, prevRest);\n      } else if (subProd instanceof Option) {\n        this.walkOption(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionMandatory) {\n        this.walkAtLeastOne(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionMandatoryWithSeparator) {\n        this.walkAtLeastOneSep(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionWithSeparator) {\n        this.walkManySep(subProd, currRest, prevRest);\n      } else if (subProd instanceof Repetition) {\n        this.walkMany(subProd, currRest, prevRest);\n      } else if (subProd instanceof Alternation) {\n        this.walkOr(subProd, currRest, prevRest);\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    });\n  }\n  walkTerminal(terminal, currRest, prevRest) {}\n  walkProdRef(refProd, currRest, prevRest) {}\n  walkFlat(flatProd, currRest, prevRest) {\n    // ABCDEF => after the D the rest is EF\n    const fullOrRest = currRest.concat(prevRest);\n    this.walk(flatProd, fullOrRest);\n  }\n  walkOption(optionProd, currRest, prevRest) {\n    // ABC(DE)?F => after the (DE)? the rest is F\n    const fullOrRest = currRest.concat(prevRest);\n    this.walk(optionProd, fullOrRest);\n  }\n  walkAtLeastOne(atLeastOneProd, currRest, prevRest) {\n    // ABC(DE)+F => after the (DE)+ the rest is (DE)?F\n    const fullAtLeastOneRest = [new Option({\n      definition: atLeastOneProd.definition\n    })].concat(currRest, prevRest);\n    this.walk(atLeastOneProd, fullAtLeastOneRest);\n  }\n  walkAtLeastOneSep(atLeastOneSepProd, currRest, prevRest) {\n    // ABC DE(,DE)* F => after the (,DE)+ the rest is (,DE)?F\n    const fullAtLeastOneSepRest = restForRepetitionWithSeparator(atLeastOneSepProd, currRest, prevRest);\n    this.walk(atLeastOneSepProd, fullAtLeastOneSepRest);\n  }\n  walkMany(manyProd, currRest, prevRest) {\n    // ABC(DE)*F => after the (DE)* the rest is (DE)?F\n    const fullManyRest = [new Option({\n      definition: manyProd.definition\n    })].concat(currRest, prevRest);\n    this.walk(manyProd, fullManyRest);\n  }\n  walkManySep(manySepProd, currRest, prevRest) {\n    // ABC (DE(,DE)*)? F => after the (,DE)* the rest is (,DE)?F\n    const fullManySepRest = restForRepetitionWithSeparator(manySepProd, currRest, prevRest);\n    this.walk(manySepProd, fullManySepRest);\n  }\n  walkOr(orProd, currRest, prevRest) {\n    // ABC(D|E|F)G => when finding the (D|E|F) the rest is G\n    const fullOrRest = currRest.concat(prevRest);\n    // walk all different alternatives\n    forEach(orProd.definition, alt => {\n      // wrapping each alternative in a single definition wrapper\n      // to avoid errors in computing the rest of that alternative in the invocation to computeInProdFollows\n      // (otherwise for OR([alt1,alt2]) alt2 will be considered in 'rest' of alt1\n      const prodWrapper = new Alternative({\n        definition: [alt]\n      });\n      this.walk(prodWrapper, fullOrRest);\n    });\n  }\n}\nfunction restForRepetitionWithSeparator(repSepProd, currRest, prevRest) {\n  const repSepRest = [new Option({\n    definition: [new Terminal({\n      terminalType: repSepProd.separator\n    })].concat(repSepProd.definition)\n  })];\n  const fullRepSepRest = repSepRest.concat(currRest, prevRest);\n  return fullRepSepRest;\n}", "map": {"version": 3, "names": ["drop", "for<PERSON>ach", "Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Terminal", "<PERSON><PERSON><PERSON><PERSON>", "walk", "prod", "prevRest", "definition", "subProd", "index", "currRest", "walkProdRef", "walkTerminal", "walk<PERSON><PERSON>", "walkOption", "walkAtLeastOne", "walkAtLeastOneSep", "walkManySep", "walkMany", "walkOr", "Error", "terminal", "refProd", "flatProd", "fullOrRest", "concat", "optionProd", "atLeastOneProd", "fullAtLeastOneRest", "atLeastOneSepProd", "fullAtLeastOneSepRest", "restForRepetitionWithSeparator", "manyProd", "fullManyRest", "manySepProd", "fullManySepRest", "<PERSON><PERSON><PERSON>", "alt", "prodWrapper", "repSepProd", "repSepRest", "terminalType", "separator", "fullRepSepRest"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/grammar/rest.ts"], "sourcesContent": ["import { drop, forEach } from \"lodash-es\";\nimport {\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Terminal,\n} from \"@chevrotain/gast\";\nimport { IProduction } from \"@chevrotain/types\";\n\n/**\n *  A Grammar Walker that computes the \"remaining\" grammar \"after\" a productions in the grammar.\n */\nexport abstract class RestWalker {\n  walk(prod: { definition: IProduction[] }, prevRest: any[] = []): void {\n    forEach(prod.definition, (subProd: IProduction, index) => {\n      const currRest = drop(prod.definition, index + 1);\n      /* istanbul ignore else */\n      if (subProd instanceof NonTerminal) {\n        this.walkProdRef(subProd, currRest, prevRest);\n      } else if (subProd instanceof Terminal) {\n        this.walkTerminal(subProd, currRest, prevRest);\n      } else if (subProd instanceof Alternative) {\n        this.walkFlat(subProd, currRest, prevRest);\n      } else if (subProd instanceof Option) {\n        this.walkOption(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionMandatory) {\n        this.walkAtLeastOne(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionMandatoryWithSeparator) {\n        this.walkAtLeastOneSep(subProd, currRest, prevRest);\n      } else if (subProd instanceof RepetitionWithSeparator) {\n        this.walkManySep(subProd, currRest, prevRest);\n      } else if (subProd instanceof Repetition) {\n        this.walkMany(subProd, currRest, prevRest);\n      } else if (subProd instanceof Alternation) {\n        this.walkOr(subProd, currRest, prevRest);\n      } else {\n        throw Error(\"non exhaustive match\");\n      }\n    });\n  }\n\n  walkTerminal(\n    terminal: Terminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {}\n\n  walkProdRef(\n    refProd: NonTerminal,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {}\n\n  walkFlat(\n    flatProd: Alternative,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABCDEF => after the D the rest is EF\n    const fullOrRest = currRest.concat(prevRest);\n    this.walk(flatProd, <any>fullOrRest);\n  }\n\n  walkOption(\n    optionProd: Option,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC(DE)?F => after the (DE)? the rest is F\n    const fullOrRest = currRest.concat(prevRest);\n    this.walk(optionProd, <any>fullOrRest);\n  }\n\n  walkAtLeastOne(\n    atLeastOneProd: RepetitionMandatory,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC(DE)+F => after the (DE)+ the rest is (DE)?F\n    const fullAtLeastOneRest: IProduction[] = [\n      new Option({ definition: atLeastOneProd.definition }),\n    ].concat(<any>currRest, <any>prevRest);\n    this.walk(atLeastOneProd, fullAtLeastOneRest);\n  }\n\n  walkAtLeastOneSep(\n    atLeastOneSepProd: RepetitionMandatoryWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC DE(,DE)* F => after the (,DE)+ the rest is (,DE)?F\n    const fullAtLeastOneSepRest = restForRepetitionWithSeparator(\n      atLeastOneSepProd,\n      currRest,\n      prevRest,\n    );\n    this.walk(atLeastOneSepProd, fullAtLeastOneSepRest);\n  }\n\n  walkMany(\n    manyProd: Repetition,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC(DE)*F => after the (DE)* the rest is (DE)?F\n    const fullManyRest: IProduction[] = [\n      new Option({ definition: manyProd.definition }),\n    ].concat(<any>currRest, <any>prevRest);\n    this.walk(manyProd, fullManyRest);\n  }\n\n  walkManySep(\n    manySepProd: RepetitionWithSeparator,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC (DE(,DE)*)? F => after the (,DE)* the rest is (,DE)?F\n    const fullManySepRest = restForRepetitionWithSeparator(\n      manySepProd,\n      currRest,\n      prevRest,\n    );\n    this.walk(manySepProd, fullManySepRest);\n  }\n\n  walkOr(\n    orProd: Alternation,\n    currRest: IProduction[],\n    prevRest: IProduction[],\n  ): void {\n    // ABC(D|E|F)G => when finding the (D|E|F) the rest is G\n    const fullOrRest = currRest.concat(prevRest);\n    // walk all different alternatives\n    forEach(orProd.definition, (alt) => {\n      // wrapping each alternative in a single definition wrapper\n      // to avoid errors in computing the rest of that alternative in the invocation to computeInProdFollows\n      // (otherwise for OR([alt1,alt2]) alt2 will be considered in 'rest' of alt1\n      const prodWrapper = new Alternative({ definition: [alt] });\n      this.walk(prodWrapper, <any>fullOrRest);\n    });\n  }\n}\n\nfunction restForRepetitionWithSeparator(\n  repSepProd: RepetitionWithSeparator,\n  currRest: IProduction[],\n  prevRest: IProduction[],\n) {\n  const repSepRest = [\n    new Option({\n      definition: [\n        new Terminal({ terminalType: repSepProd.separator }) as IProduction,\n      ].concat(repSepProd.definition),\n    }) as IProduction,\n  ];\n  const fullRepSepRest: IProduction[] = repSepRest.concat(currRest, prevRest);\n  return fullRepSepRest;\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,WAAW;AACzC,SACEC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,QAAQ,QACH,kBAAkB;AAGzB;;;AAGA,OAAM,MAAgBC,UAAU;EAC9BC,IAAIA,CAACC,IAAmC,EAAEC,QAAA,GAAkB,EAAE;IAC5Db,OAAO,CAACY,IAAI,CAACE,UAAU,EAAE,CAACC,OAAoB,EAAEC,KAAK,KAAI;MACvD,MAAMC,QAAQ,GAAGlB,IAAI,CAACa,IAAI,CAACE,UAAU,EAAEE,KAAK,GAAG,CAAC,CAAC;MACjD;MACA,IAAID,OAAO,YAAYZ,WAAW,EAAE;QAClC,IAAI,CAACe,WAAW,CAACH,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC9C,MAAM,IAAIE,OAAO,YAAYN,QAAQ,EAAE;QACtC,IAAI,CAACU,YAAY,CAACJ,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC/C,MAAM,IAAIE,OAAO,YAAYb,WAAW,EAAE;QACzC,IAAI,CAACkB,QAAQ,CAACL,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC3C,MAAM,IAAIE,OAAO,YAAYX,MAAM,EAAE;QACpC,IAAI,CAACiB,UAAU,CAACN,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC7C,MAAM,IAAIE,OAAO,YAAYT,mBAAmB,EAAE;QACjD,IAAI,CAACgB,cAAc,CAACP,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OACjD,MAAM,IAAIE,OAAO,YAAYR,gCAAgC,EAAE;QAC9D,IAAI,CAACgB,iBAAiB,CAACR,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OACpD,MAAM,IAAIE,OAAO,YAAYP,uBAAuB,EAAE;QACrD,IAAI,CAACgB,WAAW,CAACT,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC9C,MAAM,IAAIE,OAAO,YAAYV,UAAU,EAAE;QACxC,IAAI,CAACoB,QAAQ,CAACV,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OAC3C,MAAM,IAAIE,OAAO,YAAYd,WAAW,EAAE;QACzC,IAAI,CAACyB,MAAM,CAACX,OAAO,EAAEE,QAAQ,EAAEJ,QAAQ,CAAC;OACzC,MAAM;QACL,MAAMc,KAAK,CAAC,sBAAsB,CAAC;;IAEvC,CAAC,CAAC;EACJ;EAEAR,YAAYA,CACVS,QAAkB,EAClBX,QAAuB,EACvBJ,QAAuB,GAChB;EAETK,WAAWA,CACTW,OAAoB,EACpBZ,QAAuB,EACvBJ,QAAuB,GAChB;EAETO,QAAQA,CACNU,QAAqB,EACrBb,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAMkB,UAAU,GAAGd,QAAQ,CAACe,MAAM,CAACnB,QAAQ,CAAC;IAC5C,IAAI,CAACF,IAAI,CAACmB,QAAQ,EAAOC,UAAU,CAAC;EACtC;EAEAV,UAAUA,CACRY,UAAkB,EAClBhB,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAMkB,UAAU,GAAGd,QAAQ,CAACe,MAAM,CAACnB,QAAQ,CAAC;IAC5C,IAAI,CAACF,IAAI,CAACsB,UAAU,EAAOF,UAAU,CAAC;EACxC;EAEAT,cAAcA,CACZY,cAAmC,EACnCjB,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAMsB,kBAAkB,GAAkB,CACxC,IAAI/B,MAAM,CAAC;MAAEU,UAAU,EAAEoB,cAAc,CAACpB;IAAU,CAAE,CAAC,CACtD,CAACkB,MAAM,CAAMf,QAAQ,EAAOJ,QAAQ,CAAC;IACtC,IAAI,CAACF,IAAI,CAACuB,cAAc,EAAEC,kBAAkB,CAAC;EAC/C;EAEAZ,iBAAiBA,CACfa,iBAAmD,EACnDnB,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAMwB,qBAAqB,GAAGC,8BAA8B,CAC1DF,iBAAiB,EACjBnB,QAAQ,EACRJ,QAAQ,CACT;IACD,IAAI,CAACF,IAAI,CAACyB,iBAAiB,EAAEC,qBAAqB,CAAC;EACrD;EAEAZ,QAAQA,CACNc,QAAoB,EACpBtB,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAM2B,YAAY,GAAkB,CAClC,IAAIpC,MAAM,CAAC;MAAEU,UAAU,EAAEyB,QAAQ,CAACzB;IAAU,CAAE,CAAC,CAChD,CAACkB,MAAM,CAAMf,QAAQ,EAAOJ,QAAQ,CAAC;IACtC,IAAI,CAACF,IAAI,CAAC4B,QAAQ,EAAEC,YAAY,CAAC;EACnC;EAEAhB,WAAWA,CACTiB,WAAoC,EACpCxB,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAM6B,eAAe,GAAGJ,8BAA8B,CACpDG,WAAW,EACXxB,QAAQ,EACRJ,QAAQ,CACT;IACD,IAAI,CAACF,IAAI,CAAC8B,WAAW,EAAEC,eAAe,CAAC;EACzC;EAEAhB,MAAMA,CACJiB,MAAmB,EACnB1B,QAAuB,EACvBJ,QAAuB;IAEvB;IACA,MAAMkB,UAAU,GAAGd,QAAQ,CAACe,MAAM,CAACnB,QAAQ,CAAC;IAC5C;IACAb,OAAO,CAAC2C,MAAM,CAAC7B,UAAU,EAAG8B,GAAG,IAAI;MACjC;MACA;MACA;MACA,MAAMC,WAAW,GAAG,IAAI3C,WAAW,CAAC;QAAEY,UAAU,EAAE,CAAC8B,GAAG;MAAC,CAAE,CAAC;MAC1D,IAAI,CAACjC,IAAI,CAACkC,WAAW,EAAOd,UAAU,CAAC;IACzC,CAAC,CAAC;EACJ;;AAGF,SAASO,8BAA8BA,CACrCQ,UAAmC,EACnC7B,QAAuB,EACvBJ,QAAuB;EAEvB,MAAMkC,UAAU,GAAG,CACjB,IAAI3C,MAAM,CAAC;IACTU,UAAU,EAAE,CACV,IAAIL,QAAQ,CAAC;MAAEuC,YAAY,EAAEF,UAAU,CAACG;IAAS,CAAE,CAAgB,CACpE,CAACjB,MAAM,CAACc,UAAU,CAAChC,UAAU;GAC/B,CAAgB,CAClB;EACD,MAAMoC,cAAc,GAAkBH,UAAU,CAACf,MAAM,CAACf,QAAQ,EAAEJ,QAAQ,CAAC;EAC3E,OAAOqC,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}