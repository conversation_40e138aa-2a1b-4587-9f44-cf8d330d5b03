{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport * from './ast-descriptions.js';\nexport * from './ast-node-locator.js';\nexport * from './configuration.js';\nexport * from './document-builder.js';\nexport * from './documents.js';\nexport * from './file-system-provider.js';\nexport * from './index-manager.js';\nexport * from './workspace-lock.js';\nexport * from './workspace-manager.js';", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/workspace/index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport * from './ast-descriptions.js';\r\nexport * from './ast-node-locator.js';\r\nexport * from './configuration.js';\r\nexport * from './document-builder.js';\r\nexport * from './documents.js';\r\nexport * from './file-system-provider.js';\r\nexport * from './index-manager.js';\r\nexport * from './workspace-lock.js';\r\nexport * from './workspace-manager.js';\r\n"], "mappings": "AAAA;;;;;AAMA,cAAc,uBAAuB;AACrC,cAAc,uBAAuB;AACrC,cAAc,oBAAoB;AAClC,cAAc,uBAAuB;AACrC,cAAc,gBAAgB;AAC9B,cAAc,2BAA2B;AACzC,cAAc,oBAAoB;AAClC,cAAc,qBAAqB;AACnC,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}