{"ast": null, "code": "import { flatten, isArray, map, reduce, uniq, upperFirst } from \"lodash-es\";\nexport function genDts(model, options) {\n  let contentParts = [];\n  contentParts = contentParts.concat(`import type { CstNode, ICstVisitor, IToken } from \"chevrotain\";`);\n  contentParts = contentParts.concat(flatten(map(model, node => genCstNodeTypes(node))));\n  if (options.includeVisitorInterface) {\n    contentParts = contentParts.concat(genVisitor(options.visitorInterfaceName, model));\n  }\n  return contentParts.join(\"\\n\\n\") + \"\\n\";\n}\nfunction genCstNodeTypes(node) {\n  const nodeCstInterface = genNodeInterface(node);\n  const nodeChildrenInterface = genNodeChildrenType(node);\n  return [nodeCstInterface, nodeChildrenInterface];\n}\nfunction genNodeInterface(node) {\n  const nodeInterfaceName = getNodeInterfaceName(node.name);\n  const childrenTypeName = getNodeChildrenTypeName(node.name);\n  return `export interface ${nodeInterfaceName} extends CstNode {\n  name: \"${node.name}\";\n  children: ${childrenTypeName};\n}`;\n}\nfunction genNodeChildrenType(node) {\n  const typeName = getNodeChildrenTypeName(node.name);\n  return `export type ${typeName} = {\n  ${map(node.properties, property => genChildProperty(property)).join(\"\\n  \")}\n};`;\n}\nfunction genChildProperty(prop) {\n  const typeName = buildTypeString(prop.type);\n  return `${prop.name}${prop.optional ? \"?\" : \"\"}: ${typeName}[];`;\n}\nfunction genVisitor(name, nodes) {\n  return `export interface ${name}<IN, OUT> extends ICstVisitor<IN, OUT> {\n  ${map(nodes, node => genVisitorFunction(node)).join(\"\\n  \")}\n}`;\n}\nfunction genVisitorFunction(node) {\n  const childrenTypeName = getNodeChildrenTypeName(node.name);\n  return `${node.name}(children: ${childrenTypeName}, param?: IN): OUT;`;\n}\nfunction buildTypeString(type) {\n  if (isArray(type)) {\n    const typeNames = uniq(map(type, t => getTypeString(t)));\n    const typeString = reduce(typeNames, (sum, t) => sum + \" | \" + t);\n    return \"(\" + typeString + \")\";\n  } else {\n    return getTypeString(type);\n  }\n}\nfunction getTypeString(type) {\n  if (type.kind === \"token\") {\n    return \"IToken\";\n  }\n  return getNodeInterfaceName(type.name);\n}\nfunction getNodeInterfaceName(ruleName) {\n  return upperFirst(ruleName) + \"CstNode\";\n}\nfunction getNodeChildrenTypeName(ruleName) {\n  return upperFirst(ruleName) + \"CstChildren\";\n}", "map": {"version": 3, "names": ["flatten", "isArray", "map", "reduce", "uniq", "upperFirst", "genDts", "model", "options", "contentParts", "concat", "node", "genCstNodeTypes", "includeVisitorInterface", "genVisitor", "visitorInterfaceName", "join", "nodeCstInterface", "genNodeInterface", "nodeChildrenInterface", "genNodeChildrenType", "nodeInterfaceName", "getNodeInterfaceName", "name", "childrenTypeName", "getNodeChildrenTypeName", "typeName", "properties", "property", "gen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prop", "buildTypeString", "type", "optional", "nodes", "genVisitorFunction", "typeNames", "t", "getTypeString", "typeString", "sum", "kind", "ruleName"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/cst-dts-gen/src/generate.ts"], "sourcesContent": ["import { flatten, isArray, map, reduce, uniq, upperFirst } from \"lodash-es\";\nimport { GenerateDtsOptions } from \"@chevrotain/types\";\nimport {\n  CstNodeTypeDefinition,\n  PropertyArrayType,\n  PropertyTypeDefinition,\n  RuleArrayType,\n  TokenArrayType,\n} from \"./model.js\";\n\nexport function genDts(\n  model: CstNodeTypeDefinition[],\n  options: Required<GenerateDtsOptions>,\n): string {\n  let contentParts: string[] = [];\n\n  contentParts = contentParts.concat(\n    `import type { CstNode, ICstVisitor, IToken } from \"chevrotain\";`,\n  );\n\n  contentParts = contentParts.concat(\n    flatten(map(model, (node) => genCstNodeTypes(node))),\n  );\n\n  if (options.includeVisitorInterface) {\n    contentParts = contentParts.concat(\n      genVisitor(options.visitorInterfaceName, model),\n    );\n  }\n\n  return contentParts.join(\"\\n\\n\") + \"\\n\";\n}\n\nfunction genCstNodeTypes(node: CstNodeTypeDefinition) {\n  const nodeCstInterface = genNodeInterface(node);\n  const nodeChildrenInterface = genNodeChildrenType(node);\n\n  return [nodeCstInterface, nodeChildrenInterface];\n}\n\nfunction genNodeInterface(node: CstNodeTypeDefinition) {\n  const nodeInterfaceName = getNodeInterfaceName(node.name);\n  const childrenTypeName = getNodeChildrenTypeName(node.name);\n\n  return `export interface ${nodeInterfaceName} extends CstNode {\n  name: \"${node.name}\";\n  children: ${childrenTypeName};\n}`;\n}\n\nfunction genNodeChildrenType(node: CstNodeTypeDefinition) {\n  const typeName = getNodeChildrenTypeName(node.name);\n\n  return `export type ${typeName} = {\n  ${map(node.properties, (property) => genChildProperty(property)).join(\"\\n  \")}\n};`;\n}\n\nfunction genChildProperty(prop: PropertyTypeDefinition) {\n  const typeName = buildTypeString(prop.type);\n  return `${prop.name}${prop.optional ? \"?\" : \"\"}: ${typeName}[];`;\n}\n\nfunction genVisitor(name: string, nodes: CstNodeTypeDefinition[]) {\n  return `export interface ${name}<IN, OUT> extends ICstVisitor<IN, OUT> {\n  ${map(nodes, (node) => genVisitorFunction(node)).join(\"\\n  \")}\n}`;\n}\n\nfunction genVisitorFunction(node: CstNodeTypeDefinition) {\n  const childrenTypeName = getNodeChildrenTypeName(node.name);\n  return `${node.name}(children: ${childrenTypeName}, param?: IN): OUT;`;\n}\n\nfunction buildTypeString(type: PropertyArrayType) {\n  if (isArray(type)) {\n    const typeNames = uniq(map(type, (t) => getTypeString(t)));\n    const typeString = reduce(typeNames, (sum, t) => sum + \" | \" + t);\n    return \"(\" + typeString + \")\";\n  } else {\n    return getTypeString(type);\n  }\n}\n\nfunction getTypeString(type: TokenArrayType | RuleArrayType) {\n  if (type.kind === \"token\") {\n    return \"IToken\";\n  }\n  return getNodeInterfaceName(type.name);\n}\n\nfunction getNodeInterfaceName(ruleName: string) {\n  return upperFirst(ruleName) + \"CstNode\";\n}\n\nfunction getNodeChildrenTypeName(ruleName: string) {\n  return upperFirst(ruleName) + \"CstChildren\";\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,QAAQ,WAAW;AAU3E,OAAM,SAAUC,MAAMA,CACpBC,KAA8B,EAC9BC,OAAqC;EAErC,IAAIC,YAAY,GAAa,EAAE;EAE/BA,YAAY,GAAGA,YAAY,CAACC,MAAM,CAChC,iEAAiE,CAClE;EAEDD,YAAY,GAAGA,YAAY,CAACC,MAAM,CAChCV,OAAO,CAACE,GAAG,CAACK,KAAK,EAAGI,IAAI,IAAKC,eAAe,CAACD,IAAI,CAAC,CAAC,CAAC,CACrD;EAED,IAAIH,OAAO,CAACK,uBAAuB,EAAE;IACnCJ,YAAY,GAAGA,YAAY,CAACC,MAAM,CAChCI,UAAU,CAACN,OAAO,CAACO,oBAAoB,EAAER,KAAK,CAAC,CAChD;;EAGH,OAAOE,YAAY,CAACO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;AACzC;AAEA,SAASJ,eAAeA,CAACD,IAA2B;EAClD,MAAMM,gBAAgB,GAAGC,gBAAgB,CAACP,IAAI,CAAC;EAC/C,MAAMQ,qBAAqB,GAAGC,mBAAmB,CAACT,IAAI,CAAC;EAEvD,OAAO,CAACM,gBAAgB,EAAEE,qBAAqB,CAAC;AAClD;AAEA,SAASD,gBAAgBA,CAACP,IAA2B;EACnD,MAAMU,iBAAiB,GAAGC,oBAAoB,CAACX,IAAI,CAACY,IAAI,CAAC;EACzD,MAAMC,gBAAgB,GAAGC,uBAAuB,CAACd,IAAI,CAACY,IAAI,CAAC;EAE3D,OAAO,oBAAoBF,iBAAiB;WACnCV,IAAI,CAACY,IAAI;cACNC,gBAAgB;EAC5B;AACF;AAEA,SAASJ,mBAAmBA,CAACT,IAA2B;EACtD,MAAMe,QAAQ,GAAGD,uBAAuB,CAACd,IAAI,CAACY,IAAI,CAAC;EAEnD,OAAO,eAAeG,QAAQ;IAC5BxB,GAAG,CAACS,IAAI,CAACgB,UAAU,EAAGC,QAAQ,IAAKC,gBAAgB,CAACD,QAAQ,CAAC,CAAC,CAACZ,IAAI,CAAC,MAAM,CAAC;GAC5E;AACH;AAEA,SAASa,gBAAgBA,CAACC,IAA4B;EACpD,MAAMJ,QAAQ,GAAGK,eAAe,CAACD,IAAI,CAACE,IAAI,CAAC;EAC3C,OAAO,GAAGF,IAAI,CAACP,IAAI,GAAGO,IAAI,CAACG,QAAQ,GAAG,GAAG,GAAG,EAAE,KAAKP,QAAQ,KAAK;AAClE;AAEA,SAASZ,UAAUA,CAACS,IAAY,EAAEW,KAA8B;EAC9D,OAAO,oBAAoBX,IAAI;IAC7BrB,GAAG,CAACgC,KAAK,EAAGvB,IAAI,IAAKwB,kBAAkB,CAACxB,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC,MAAM,CAAC;EAC7D;AACF;AAEA,SAASmB,kBAAkBA,CAACxB,IAA2B;EACrD,MAAMa,gBAAgB,GAAGC,uBAAuB,CAACd,IAAI,CAACY,IAAI,CAAC;EAC3D,OAAO,GAAGZ,IAAI,CAACY,IAAI,cAAcC,gBAAgB,qBAAqB;AACxE;AAEA,SAASO,eAAeA,CAACC,IAAuB;EAC9C,IAAI/B,OAAO,CAAC+B,IAAI,CAAC,EAAE;IACjB,MAAMI,SAAS,GAAGhC,IAAI,CAACF,GAAG,CAAC8B,IAAI,EAAGK,CAAC,IAAKC,aAAa,CAACD,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAME,UAAU,GAAGpC,MAAM,CAACiC,SAAS,EAAE,CAACI,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAG,KAAK,GAAGH,CAAC,CAAC;IACjE,OAAO,GAAG,GAAGE,UAAU,GAAG,GAAG;GAC9B,MAAM;IACL,OAAOD,aAAa,CAACN,IAAI,CAAC;;AAE9B;AAEA,SAASM,aAAaA,CAACN,IAAoC;EACzD,IAAIA,IAAI,CAACS,IAAI,KAAK,OAAO,EAAE;IACzB,OAAO,QAAQ;;EAEjB,OAAOnB,oBAAoB,CAACU,IAAI,CAACT,IAAI,CAAC;AACxC;AAEA,SAASD,oBAAoBA,CAACoB,QAAgB;EAC5C,OAAOrC,UAAU,CAACqC,QAAQ,CAAC,GAAG,SAAS;AACzC;AAEA,SAASjB,uBAAuBA,CAACiB,QAAgB;EAC/C,OAAOrC,UAAU,CAACqC,QAAQ,CAAC,GAAG,aAAa;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}