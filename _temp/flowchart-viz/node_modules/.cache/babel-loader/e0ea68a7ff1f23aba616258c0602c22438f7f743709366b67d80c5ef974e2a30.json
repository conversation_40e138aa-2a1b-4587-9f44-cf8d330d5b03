{"ast": null, "code": "/* IMPORT */\nimport adjustChannel from './adjust_channel.js';\n/* MAIN */\nconst desaturate = (color, amount) => {\n  return adjustChannel(color, 's', -amount);\n};\n/* EXPORT */\nexport default desaturate;", "map": {"version": 3, "names": ["adjustChannel", "desaturate", "color", "amount"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/khroma/dist/methods/desaturate.js"], "sourcesContent": ["/* IMPORT */\nimport adjustChannel from './adjust_channel.js';\n/* MAIN */\nconst desaturate = (color, amount) => {\n    return adjustChannel(color, 's', -amount);\n};\n/* EXPORT */\nexport default desaturate;\n"], "mappings": "AAAA;AACA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C;AACA,MAAMC,UAAU,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAClC,OAAOH,aAAa,CAACE,KAAK,EAAE,GAAG,EAAE,CAACC,MAAM,CAAC;AAC7C,CAAC;AACD;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}