{"ast": null, "code": "import { Alternation, Alternative, NonTerminal, Option, Repetition, RepetitionMandatory, RepetitionMandatoryWithSeparator, RepetitionWithSeparator, Rule, Terminal } from \"./model.js\";\nexport class GAstVisitor {\n  visit(node) {\n    const nodeAny = node;\n    switch (nodeAny.constructor) {\n      case NonTerminal:\n        return this.visitNonTerminal(nodeAny);\n      case Alternative:\n        return this.visitAlternative(nodeAny);\n      case Option:\n        return this.visitOption(nodeAny);\n      case RepetitionMandatory:\n        return this.visitRepetitionMandatory(nodeAny);\n      case RepetitionMandatoryWithSeparator:\n        return this.visitRepetitionMandatoryWithSeparator(nodeAny);\n      case RepetitionWithSeparator:\n        return this.visitRepetitionWithSeparator(nodeAny);\n      case Repetition:\n        return this.visitRepetition(nodeAny);\n      case Alternation:\n        return this.visitAlternation(nodeAny);\n      case Terminal:\n        return this.visitTerminal(nodeAny);\n      case Rule:\n        return this.visitRule(nodeAny);\n      /* c8 ignore next 2 */\n      default:\n        throw Error(\"non exhaustive match\");\n    }\n  }\n  /* c8 ignore next */\n  visitNonTerminal(node) {}\n  /* c8 ignore next */\n  visitAlternative(node) {}\n  /* c8 ignore next */\n  visitOption(node) {}\n  /* c8 ignore next */\n  visitRepetition(node) {}\n  /* c8 ignore next */\n  visitRepetitionMandatory(node) {}\n  /* c8 ignore next 3 */\n  visitRepetitionMandatoryWithSeparator(node) {}\n  /* c8 ignore next */\n  visitRepetitionWithSeparator(node) {}\n  /* c8 ignore next */\n  visitAlternation(node) {}\n  /* c8 ignore next */\n  visitTerminal(node) {}\n  /* c8 ignore next */\n  visitRule(node) {}\n}", "map": {"version": 3, "names": ["Alternation", "Alternative", "NonTerminal", "Option", "Repetition", "RepetitionMandatory", "RepetitionMandatoryWithSeparator", "RepetitionWithSeparator", "Rule", "Terminal", "GAstVisitor", "visit", "node", "nodeAny", "constructor", "visitNonTerminal", "visitAlternative", "visitOption", "visitRepetitionMandatory", "visitRepetitionMandatoryWithSeparator", "visitRepetitionWithSeparator", "visitRepetition", "visitAlternation", "visitTerminal", "visitRule", "Error"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/gast/src/visitor.ts"], "sourcesContent": ["import {\n  Alternation,\n  Alternative,\n  NonTerminal,\n  Option,\n  Repetition,\n  RepetitionMandatory,\n  RepetitionMandatoryWithSeparator,\n  RepetitionWithSeparator,\n  Rule,\n  Terminal,\n} from \"./model.js\";\nimport type { IProduction } from \"@chevrotain/types\";\n\nexport abstract class GAstVisitor {\n  public visit(node: IProduction): any {\n    const nodeAny: any = node;\n    switch (nodeAny.constructor) {\n      case NonTerminal:\n        return this.visitNonTerminal(nodeAny);\n      case Alternative:\n        return this.visitAlternative(nodeAny);\n      case Option:\n        return this.visitOption(nodeAny);\n      case RepetitionMandatory:\n        return this.visitRepetitionMandatory(nodeAny);\n      case RepetitionMandatoryWithSeparator:\n        return this.visitRepetitionMandatoryWithSeparator(nodeAny);\n      case RepetitionWithSeparator:\n        return this.visitRepetitionWithSeparator(nodeAny);\n      case Repetition:\n        return this.visitRepetition(nodeAny);\n      case Alternation:\n        return this.visitAlternation(nodeAny);\n      case Terminal:\n        return this.visitTerminal(nodeAny);\n      case Rule:\n        return this.visitRule(nodeAny);\n      /* c8 ignore next 2 */\n      default:\n        throw Error(\"non exhaustive match\");\n    }\n  }\n\n  /* c8 ignore next */\n  public visitNonTerminal(node: NonTerminal): any {}\n\n  /* c8 ignore next */\n  public visitAlternative(node: Alternative): any {}\n\n  /* c8 ignore next */\n  public visitOption(node: Option): any {}\n\n  /* c8 ignore next */\n  public visitRepetition(node: Repetition): any {}\n\n  /* c8 ignore next */\n  public visitRepetitionMandatory(node: RepetitionMandatory): any {}\n\n  /* c8 ignore next 3 */\n  public visitRepetitionMandatoryWithSeparator(\n    node: RepetitionMandatoryWithSeparator,\n  ): any {}\n\n  /* c8 ignore next */\n  public visitRepetitionWithSeparator(node: RepetitionWithSeparator): any {}\n\n  /* c8 ignore next */\n  public visitAlternation(node: Alternation): any {}\n\n  /* c8 ignore next */\n  public visitTerminal(node: Terminal): any {}\n\n  /* c8 ignore next */\n  public visitRule(node: Rule): any {}\n}\n"], "mappings": "AAAA,SACEA,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,mBAAmB,EACnBC,gCAAgC,EAChCC,uBAAuB,EACvBC,IAAI,EACJC,QAAQ,QACH,YAAY;AAGnB,OAAM,MAAgBC,WAAW;EACxBC,KAAKA,CAACC,IAAiB;IAC5B,MAAMC,OAAO,GAAQD,IAAI;IACzB,QAAQC,OAAO,CAACC,WAAW;MACzB,KAAKZ,WAAW;QACd,OAAO,IAAI,CAACa,gBAAgB,CAACF,OAAO,CAAC;MACvC,KAAKZ,WAAW;QACd,OAAO,IAAI,CAACe,gBAAgB,CAACH,OAAO,CAAC;MACvC,KAAKV,MAAM;QACT,OAAO,IAAI,CAACc,WAAW,CAACJ,OAAO,CAAC;MAClC,KAAKR,mBAAmB;QACtB,OAAO,IAAI,CAACa,wBAAwB,CAACL,OAAO,CAAC;MAC/C,KAAKP,gCAAgC;QACnC,OAAO,IAAI,CAACa,qCAAqC,CAACN,OAAO,CAAC;MAC5D,KAAKN,uBAAuB;QAC1B,OAAO,IAAI,CAACa,4BAA4B,CAACP,OAAO,CAAC;MACnD,KAAKT,UAAU;QACb,OAAO,IAAI,CAACiB,eAAe,CAACR,OAAO,CAAC;MACtC,KAAKb,WAAW;QACd,OAAO,IAAI,CAACsB,gBAAgB,CAACT,OAAO,CAAC;MACvC,KAAKJ,QAAQ;QACX,OAAO,IAAI,CAACc,aAAa,CAACV,OAAO,CAAC;MACpC,KAAKL,IAAI;QACP,OAAO,IAAI,CAACgB,SAAS,CAACX,OAAO,CAAC;MAChC;MACA;QACE,MAAMY,KAAK,CAAC,sBAAsB,CAAC;;EAEzC;EAEA;EACOV,gBAAgBA,CAACH,IAAiB,GAAQ;EAEjD;EACOI,gBAAgBA,CAACJ,IAAiB,GAAQ;EAEjD;EACOK,WAAWA,CAACL,IAAY,GAAQ;EAEvC;EACOS,eAAeA,CAACT,IAAgB,GAAQ;EAE/C;EACOM,wBAAwBA,CAACN,IAAyB,GAAQ;EAEjE;EACOO,qCAAqCA,CAC1CP,IAAsC,GAChC;EAER;EACOQ,4BAA4BA,CAACR,IAA6B,GAAQ;EAEzE;EACOU,gBAAgBA,CAACV,IAAiB,GAAQ;EAEjD;EACOW,aAAaA,CAACX,IAAc,GAAQ;EAE3C;EACOY,SAASA,CAACZ,IAAU,GAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}