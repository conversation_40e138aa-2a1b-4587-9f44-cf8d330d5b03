{"ast": null, "code": "import baseIsEqualDeep from './_baseIsEqualDeep.js';\nimport isObjectLike from './isObjectLike.js';\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\nexport default baseIsEqual;", "map": {"version": 3, "names": ["baseIsEqualDeep", "isObjectLike", "baseIsEqual", "value", "other", "bitmask", "customizer", "stack"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/lodash-es/_baseIsEqual.js"], "sourcesContent": ["import baseIsEqualDeep from './_baseIsEqualDeep.js';\nimport isObjectLike from './isObjectLike.js';\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nexport default baseIsEqual;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC7D,IAAIJ,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAID,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAK,CAACH,YAAY,CAACE,KAAK,CAAC,IAAI,CAACF,YAAY,CAACG,KAAK,CAAE,EAAE;IACpF,OAAOD,KAAK,KAAKA,KAAK,IAAIC,KAAK,KAAKA,KAAK;EAC3C;EACA,OAAOJ,eAAe,CAACG,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEJ,WAAW,EAAEK,KAAK,CAAC;AAC/E;AAEA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}