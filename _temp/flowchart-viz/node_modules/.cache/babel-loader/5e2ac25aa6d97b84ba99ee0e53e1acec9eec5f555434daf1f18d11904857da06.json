{"ast": null, "code": "// based on: https://github.com/petkaantonov/bluebird/blob/b97c0d2d487e8c5076e8bd897e0dcd4622d31846/src/util.js#L201-L216\nexport function toFastProperties(toBecomeFast) {\n  function FakeConstructor() {}\n  // If our object is used as a constructor, it would receive\n  FakeConstructor.prototype = toBecomeFast;\n  const fakeInstance = new FakeConstructor();\n  function fakeAccess() {\n    return typeof fakeInstance.bar;\n  }\n  // help V8 understand this is a \"real\" prototype by actually using\n  // the fake instance.\n  fakeAccess();\n  fakeAccess();\n  // Always true condition to suppress the Firefox warning of unreachable\n  // code after a return statement.\n  if (1) return toBecomeFast;\n  // Eval prevents optimization of this method (even though this is dead code)\n  // - https://esbuild.github.io/content-types/#direct-eval\n  /* istanbul ignore next */\n  // tslint:disable-next-line\n  (0, eval)(toBecomeFast);\n}", "map": {"version": 3, "names": ["toFastProperties", "toBecomeFast", "FakeConstructor", "prototype", "fakeInstance", "fakeAccess", "bar", "eval"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/utils/src/to-fast-properties.ts"], "sourcesContent": ["// based on: https://github.com/petkaantonov/bluebird/blob/b97c0d2d487e8c5076e8bd897e0dcd4622d31846/src/util.js#L201-L216\nexport function toFastProperties(toBecomeFast: any) {\n  function FakeConstructor() {}\n\n  // If our object is used as a constructor, it would receive\n  FakeConstructor.prototype = toBecomeFast;\n  const fakeInstance = new (FakeConstructor as any)();\n\n  function fakeAccess() {\n    return typeof fakeInstance.bar;\n  }\n\n  // help V8 understand this is a \"real\" prototype by actually using\n  // the fake instance.\n  fakeAccess();\n  fakeAccess();\n\n  // Always true condition to suppress the Firefox warning of unreachable\n  // code after a return statement.\n  if (1) return toBecomeFast;\n\n  // Eval prevents optimization of this method (even though this is dead code)\n  // - https://esbuild.github.io/content-types/#direct-eval\n  /* istanbul ignore next */\n  // tslint:disable-next-line\n  (0, eval)(toBecomeFast);\n}\n"], "mappings": "AAAA;AACA,OAAM,SAAUA,gBAAgBA,CAACC,YAAiB;EAChD,SAASC,eAAeA,CAAA,GAAI;EAE5B;EACAA,eAAe,CAACC,SAAS,GAAGF,YAAY;EACxC,MAAMG,YAAY,GAAG,IAAKF,eAAuB,EAAE;EAEnD,SAASG,UAAUA,CAAA;IACjB,OAAO,OAAOD,YAAY,CAACE,GAAG;EAChC;EAEA;EACA;EACAD,UAAU,EAAE;EACZA,UAAU,EAAE;EAEZ;EACA;EACA,IAAI,CAAC,EAAE,OAAOJ,YAAY;EAE1B;EACA;EACA;EACA;EACA,CAAC,CAAC,EAAEM,IAAI,EAAEN,YAAY,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}