{"ast": null, "code": "(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define([], factory);else if (typeof exports === 'object') exports[\"layoutBase\"] = factory();else root[\"layoutBase\"] = factory();\n})(this, function () {\n  return /******/function (modules) {\n    // webpackBootstrap\n    /******/ // The module cache\n    /******/\n    var installedModules = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/\n      /******/ // Check if module is in cache\n      /******/if (installedModules[moduleId]) {\n        /******/return installedModules[moduleId].exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = installedModules[moduleId] = {\n        /******/i: moduleId,\n        /******/l: false,\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Flag the module as loaded\n      /******/\n      module.l = true;\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /******/\n    /******/ // expose the modules object (__webpack_modules__)\n    /******/\n    __webpack_require__.m = modules;\n    /******/\n    /******/ // expose the module cache\n    /******/\n    __webpack_require__.c = installedModules;\n    /******/\n    /******/ // identity function for calling harmony imports with the correct context\n    /******/\n    __webpack_require__.i = function (value) {\n      return value;\n    };\n    /******/\n    /******/ // define getter function for harmony exports\n    /******/\n    __webpack_require__.d = function (exports, name, getter) {\n      /******/if (!__webpack_require__.o(exports, name)) {\n        /******/Object.defineProperty(exports, name, {\n          /******/configurable: false,\n          /******/enumerable: true,\n          /******/get: getter\n          /******/\n        });\n        /******/\n      }\n      /******/\n    };\n    /******/\n    /******/ // getDefaultExport function for compatibility with non-harmony modules\n    /******/\n    __webpack_require__.n = function (module) {\n      /******/var getter = module && module.__esModule ? /******/function getDefault() {\n        return module['default'];\n      } : /******/function getModuleExports() {\n        return module;\n      };\n      /******/\n      __webpack_require__.d(getter, 'a', getter);\n      /******/\n      return getter;\n      /******/\n    };\n    /******/\n    /******/ // Object.prototype.hasOwnProperty.call\n    /******/\n    __webpack_require__.o = function (object, property) {\n      return Object.prototype.hasOwnProperty.call(object, property);\n    };\n    /******/\n    /******/ // __webpack_public_path__\n    /******/\n    __webpack_require__.p = \"\";\n    /******/\n    /******/ // Load entry module and return exports\n    /******/\n    return __webpack_require__(__webpack_require__.s = 28);\n    /******/\n  }\n  /************************************************************************/\n  /******/([(/* 0 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function LayoutConstants() {}\n\n    /**\r\n     * Layout Quality: 0:draft, 1:default, 2:proof\r\n     */\n    LayoutConstants.QUALITY = 1;\n\n    /**\r\n     * Default parameters\r\n     */\n    LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\n    LayoutConstants.DEFAULT_INCREMENTAL = false;\n    LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\n    LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\n    LayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\n    LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n    // -----------------------------------------------------------------------------\n    // Section: General other constants\n    // -----------------------------------------------------------------------------\n    /*\r\n     * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n     * assume margins on all four sides to be uniform.\r\n     */\n    LayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n    /*\r\n     * Whether to consider labels in node dimensions or not\r\n     */\n    LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n    /*\r\n     * Default dimension of a non-compound node.\r\n     */\n    LayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n    /*\r\n     * Default dimension of a non-compound node.\r\n     */\n    LayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n    /*\r\n     * Empty compound node size. When a compound node is empty, its both\r\n     * dimensions should be of this value.\r\n     */\n    LayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n    /*\r\n     * Minimum length that an edge should take during layout\r\n     */\n    LayoutConstants.MIN_EDGE_LENGTH = 1;\n\n    /*\r\n     * World boundaries that layout operates on\r\n     */\n    LayoutConstants.WORLD_BOUNDARY = 1000000;\n\n    /*\r\n     * World boundaries that random positioning can be performed with\r\n     */\n    LayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n    /*\r\n     * Coordinates of the world center\r\n     */\n    LayoutConstants.WORLD_CENTER_X = 1200;\n    LayoutConstants.WORLD_CENTER_Y = 900;\n    module.exports = LayoutConstants;\n\n    /***/\n  }), (/* 1 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var IGeometry = __webpack_require__(8);\n    var IMath = __webpack_require__(9);\n    function LEdge(source, target, vEdge) {\n      LGraphObject.call(this, vEdge);\n      this.isOverlapingSourceAndTarget = false;\n      this.vGraphObject = vEdge;\n      this.bendpoints = [];\n      this.source = source;\n      this.target = target;\n    }\n    LEdge.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LEdge[prop] = LGraphObject[prop];\n    }\n    LEdge.prototype.getSource = function () {\n      return this.source;\n    };\n    LEdge.prototype.getTarget = function () {\n      return this.target;\n    };\n    LEdge.prototype.isInterGraph = function () {\n      return this.isInterGraph;\n    };\n    LEdge.prototype.getLength = function () {\n      return this.length;\n    };\n    LEdge.prototype.isOverlapingSourceAndTarget = function () {\n      return this.isOverlapingSourceAndTarget;\n    };\n    LEdge.prototype.getBendpoints = function () {\n      return this.bendpoints;\n    };\n    LEdge.prototype.getLca = function () {\n      return this.lca;\n    };\n    LEdge.prototype.getSourceInLca = function () {\n      return this.sourceInLca;\n    };\n    LEdge.prototype.getTargetInLca = function () {\n      return this.targetInLca;\n    };\n    LEdge.prototype.getOtherEnd = function (node) {\n      if (this.source === node) {\n        return this.target;\n      } else if (this.target === node) {\n        return this.source;\n      } else {\n        throw \"Node is not incident with this edge\";\n      }\n    };\n    LEdge.prototype.getOtherEndInGraph = function (node, graph) {\n      var otherEnd = this.getOtherEnd(node);\n      var root = graph.getGraphManager().getRoot();\n      while (true) {\n        if (otherEnd.getOwner() == graph) {\n          return otherEnd;\n        }\n        if (otherEnd.getOwner() == root) {\n          break;\n        }\n        otherEnd = otherEnd.getOwner().getParent();\n      }\n      return null;\n    };\n    LEdge.prototype.updateLength = function () {\n      var clipPointCoordinates = new Array(4);\n      this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n      if (!this.isOverlapingSourceAndTarget) {\n        this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n        this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n        if (Math.abs(this.lengthX) < 1.0) {\n          this.lengthX = IMath.sign(this.lengthX);\n        }\n        if (Math.abs(this.lengthY) < 1.0) {\n          this.lengthY = IMath.sign(this.lengthY);\n        }\n        this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n      }\n    };\n    LEdge.prototype.updateLengthSimple = function () {\n      this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n      this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n      if (Math.abs(this.lengthX) < 1.0) {\n        this.lengthX = IMath.sign(this.lengthX);\n      }\n      if (Math.abs(this.lengthY) < 1.0) {\n        this.lengthY = IMath.sign(this.lengthY);\n      }\n      this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n    };\n    module.exports = LEdge;\n\n    /***/\n  }), (/* 2 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function LGraphObject(vGraphObject) {\n      this.vGraphObject = vGraphObject;\n    }\n    module.exports = LGraphObject;\n\n    /***/\n  }), (/* 3 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var Integer = __webpack_require__(10);\n    var RectangleD = __webpack_require__(13);\n    var LayoutConstants = __webpack_require__(0);\n    var RandomSeed = __webpack_require__(16);\n    var PointD = __webpack_require__(5);\n    function LNode(gm, loc, size, vNode) {\n      //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n      if (size == null && vNode == null) {\n        vNode = loc;\n      }\n      LGraphObject.call(this, vNode);\n\n      //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n      if (gm.graphManager != null) gm = gm.graphManager;\n      this.estimatedSize = Integer.MIN_VALUE;\n      this.inclusionTreeDepth = Integer.MAX_VALUE;\n      this.vGraphObject = vNode;\n      this.edges = [];\n      this.graphManager = gm;\n      if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n    }\n    LNode.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LNode[prop] = LGraphObject[prop];\n    }\n    LNode.prototype.getEdges = function () {\n      return this.edges;\n    };\n    LNode.prototype.getChild = function () {\n      return this.child;\n    };\n    LNode.prototype.getOwner = function () {\n      //  if (this.owner != null) {\n      //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n      //      throw \"assert failed\";\n      //    }\n      //  }\n\n      return this.owner;\n    };\n    LNode.prototype.getWidth = function () {\n      return this.rect.width;\n    };\n    LNode.prototype.setWidth = function (width) {\n      this.rect.width = width;\n    };\n    LNode.prototype.getHeight = function () {\n      return this.rect.height;\n    };\n    LNode.prototype.setHeight = function (height) {\n      this.rect.height = height;\n    };\n    LNode.prototype.getCenterX = function () {\n      return this.rect.x + this.rect.width / 2;\n    };\n    LNode.prototype.getCenterY = function () {\n      return this.rect.y + this.rect.height / 2;\n    };\n    LNode.prototype.getCenter = function () {\n      return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n    };\n    LNode.prototype.getLocation = function () {\n      return new PointD(this.rect.x, this.rect.y);\n    };\n    LNode.prototype.getRect = function () {\n      return this.rect;\n    };\n    LNode.prototype.getDiagonal = function () {\n      return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n    };\n\n    /**\n     * This method returns half the diagonal length of this node.\n     */\n    LNode.prototype.getHalfTheDiagonal = function () {\n      return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n    };\n    LNode.prototype.setRect = function (upperLeft, dimension) {\n      this.rect.x = upperLeft.x;\n      this.rect.y = upperLeft.y;\n      this.rect.width = dimension.width;\n      this.rect.height = dimension.height;\n    };\n    LNode.prototype.setCenter = function (cx, cy) {\n      this.rect.x = cx - this.rect.width / 2;\n      this.rect.y = cy - this.rect.height / 2;\n    };\n    LNode.prototype.setLocation = function (x, y) {\n      this.rect.x = x;\n      this.rect.y = y;\n    };\n    LNode.prototype.moveBy = function (dx, dy) {\n      this.rect.x += dx;\n      this.rect.y += dy;\n    };\n    LNode.prototype.getEdgeListToNode = function (to) {\n      var edgeList = [];\n      var edge;\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (edge.target == to) {\n          if (edge.source != self) throw \"Incorrect edge source!\";\n          edgeList.push(edge);\n        }\n      });\n      return edgeList;\n    };\n    LNode.prototype.getEdgesBetween = function (other) {\n      var edgeList = [];\n      var edge;\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n        if (edge.target == other || edge.source == other) {\n          edgeList.push(edge);\n        }\n      });\n      return edgeList;\n    };\n    LNode.prototype.getNeighborsList = function () {\n      var neighbors = new Set();\n      var self = this;\n      self.edges.forEach(function (edge) {\n        if (edge.source == self) {\n          neighbors.add(edge.target);\n        } else {\n          if (edge.target != self) {\n            throw \"Incorrect incidency!\";\n          }\n          neighbors.add(edge.source);\n        }\n      });\n      return neighbors;\n    };\n    LNode.prototype.withChildren = function () {\n      var withNeighborsList = new Set();\n      var childNode;\n      var children;\n      withNeighborsList.add(this);\n      if (this.child != null) {\n        var nodes = this.child.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          childNode = nodes[i];\n          children = childNode.withChildren();\n          children.forEach(function (node) {\n            withNeighborsList.add(node);\n          });\n        }\n      }\n      return withNeighborsList;\n    };\n    LNode.prototype.getNoOfChildren = function () {\n      var noOfChildren = 0;\n      var childNode;\n      if (this.child == null) {\n        noOfChildren = 1;\n      } else {\n        var nodes = this.child.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          childNode = nodes[i];\n          noOfChildren += childNode.getNoOfChildren();\n        }\n      }\n      if (noOfChildren == 0) {\n        noOfChildren = 1;\n      }\n      return noOfChildren;\n    };\n    LNode.prototype.getEstimatedSize = function () {\n      if (this.estimatedSize == Integer.MIN_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.estimatedSize;\n    };\n    LNode.prototype.calcEstimatedSize = function () {\n      if (this.child == null) {\n        return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n      } else {\n        this.estimatedSize = this.child.calcEstimatedSize();\n        this.rect.width = this.estimatedSize;\n        this.rect.height = this.estimatedSize;\n        return this.estimatedSize;\n      }\n    };\n    LNode.prototype.scatter = function () {\n      var randomCenterX;\n      var randomCenterY;\n      var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n      var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n      randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n      this.rect.x = randomCenterX;\n      this.rect.y = randomCenterY;\n    };\n    LNode.prototype.updateBounds = function () {\n      if (this.getChild() == null) {\n        throw \"assert failed\";\n      }\n      if (this.getChild().getNodes().length != 0) {\n        // wrap the children nodes by re-arranging the boundaries\n        var childGraph = this.getChild();\n        childGraph.updateBounds(true);\n        this.rect.x = childGraph.getLeft();\n        this.rect.y = childGraph.getTop();\n        this.setWidth(childGraph.getRight() - childGraph.getLeft());\n        this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n        // Update compound bounds considering its label properties    \n        if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n          var width = childGraph.getRight() - childGraph.getLeft();\n          var height = childGraph.getBottom() - childGraph.getTop();\n          if (this.labelWidth) {\n            if (this.labelPosHorizontal == \"left\") {\n              this.rect.x -= this.labelWidth;\n              this.setWidth(width + this.labelWidth);\n            } else if (this.labelPosHorizontal == \"center\" && this.labelWidth > width) {\n              this.rect.x -= (this.labelWidth - width) / 2;\n              this.setWidth(this.labelWidth);\n            } else if (this.labelPosHorizontal == \"right\") {\n              this.setWidth(width + this.labelWidth);\n            }\n          }\n          if (this.labelHeight) {\n            if (this.labelPosVertical == \"top\") {\n              this.rect.y -= this.labelHeight;\n              this.setHeight(height + this.labelHeight);\n            } else if (this.labelPosVertical == \"center\" && this.labelHeight > height) {\n              this.rect.y -= (this.labelHeight - height) / 2;\n              this.setHeight(this.labelHeight);\n            } else if (this.labelPosVertical == \"bottom\") {\n              this.setHeight(height + this.labelHeight);\n            }\n          }\n        }\n      }\n    };\n    LNode.prototype.getInclusionTreeDepth = function () {\n      if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.inclusionTreeDepth;\n    };\n    LNode.prototype.transform = function (trans) {\n      var left = this.rect.x;\n      if (left > LayoutConstants.WORLD_BOUNDARY) {\n        left = LayoutConstants.WORLD_BOUNDARY;\n      } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n        left = -LayoutConstants.WORLD_BOUNDARY;\n      }\n      var top = this.rect.y;\n      if (top > LayoutConstants.WORLD_BOUNDARY) {\n        top = LayoutConstants.WORLD_BOUNDARY;\n      } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n        top = -LayoutConstants.WORLD_BOUNDARY;\n      }\n      var leftTop = new PointD(left, top);\n      var vLeftTop = trans.inverseTransformPoint(leftTop);\n      this.setLocation(vLeftTop.x, vLeftTop.y);\n    };\n    LNode.prototype.getLeft = function () {\n      return this.rect.x;\n    };\n    LNode.prototype.getRight = function () {\n      return this.rect.x + this.rect.width;\n    };\n    LNode.prototype.getTop = function () {\n      return this.rect.y;\n    };\n    LNode.prototype.getBottom = function () {\n      return this.rect.y + this.rect.height;\n    };\n    LNode.prototype.getParent = function () {\n      if (this.owner == null) {\n        return null;\n      }\n      return this.owner.getParent();\n    };\n    module.exports = LNode;\n\n    /***/\n  }), (/* 4 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LayoutConstants = __webpack_require__(0);\n    function FDLayoutConstants() {}\n\n    //FDLayoutConstants inherits static props in LayoutConstants\n    for (var prop in LayoutConstants) {\n      FDLayoutConstants[prop] = LayoutConstants[prop];\n    }\n    FDLayoutConstants.MAX_ITERATIONS = 2500;\n    FDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\n    FDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\n    FDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\n    FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\n    FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\n    FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\n    FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\n    FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\n    FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\n    FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\n    FDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\n    FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\n    FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\n    FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\n    FDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\n    FDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\n    FDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\n    FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\n    FDLayoutConstants.MIN_EDGE_LENGTH = 1;\n    FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n    module.exports = FDLayoutConstants;\n\n    /***/\n  }), (/* 5 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function PointD(x, y) {\n      if (x == null && y == null) {\n        this.x = 0;\n        this.y = 0;\n      } else {\n        this.x = x;\n        this.y = y;\n      }\n    }\n    PointD.prototype.getX = function () {\n      return this.x;\n    };\n    PointD.prototype.getY = function () {\n      return this.y;\n    };\n    PointD.prototype.setX = function (x) {\n      this.x = x;\n    };\n    PointD.prototype.setY = function (y) {\n      this.y = y;\n    };\n    PointD.prototype.getDifference = function (pt) {\n      return new DimensionD(this.x - pt.x, this.y - pt.y);\n    };\n    PointD.prototype.getCopy = function () {\n      return new PointD(this.x, this.y);\n    };\n    PointD.prototype.translate = function (dim) {\n      this.x += dim.width;\n      this.y += dim.height;\n      return this;\n    };\n    module.exports = PointD;\n\n    /***/\n  }), (/* 6 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraphObject = __webpack_require__(2);\n    var Integer = __webpack_require__(10);\n    var LayoutConstants = __webpack_require__(0);\n    var LGraphManager = __webpack_require__(7);\n    var LNode = __webpack_require__(3);\n    var LEdge = __webpack_require__(1);\n    var RectangleD = __webpack_require__(13);\n    var Point = __webpack_require__(12);\n    var LinkedList = __webpack_require__(11);\n    function LGraph(parent, obj2, vGraph) {\n      LGraphObject.call(this, vGraph);\n      this.estimatedSize = Integer.MIN_VALUE;\n      this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n      this.edges = [];\n      this.nodes = [];\n      this.isConnected = false;\n      this.parent = parent;\n      if (obj2 != null && obj2 instanceof LGraphManager) {\n        this.graphManager = obj2;\n      } else if (obj2 != null && obj2 instanceof Layout) {\n        this.graphManager = obj2.graphManager;\n      }\n    }\n    LGraph.prototype = Object.create(LGraphObject.prototype);\n    for (var prop in LGraphObject) {\n      LGraph[prop] = LGraphObject[prop];\n    }\n    LGraph.prototype.getNodes = function () {\n      return this.nodes;\n    };\n    LGraph.prototype.getEdges = function () {\n      return this.edges;\n    };\n    LGraph.prototype.getGraphManager = function () {\n      return this.graphManager;\n    };\n    LGraph.prototype.getParent = function () {\n      return this.parent;\n    };\n    LGraph.prototype.getLeft = function () {\n      return this.left;\n    };\n    LGraph.prototype.getRight = function () {\n      return this.right;\n    };\n    LGraph.prototype.getTop = function () {\n      return this.top;\n    };\n    LGraph.prototype.getBottom = function () {\n      return this.bottom;\n    };\n    LGraph.prototype.isConnected = function () {\n      return this.isConnected;\n    };\n    LGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n      if (sourceNode == null && targetNode == null) {\n        var newNode = obj1;\n        if (this.graphManager == null) {\n          throw \"Graph has no graph mgr!\";\n        }\n        if (this.getNodes().indexOf(newNode) > -1) {\n          throw \"Node already in graph!\";\n        }\n        newNode.owner = this;\n        this.getNodes().push(newNode);\n        return newNode;\n      } else {\n        var newEdge = obj1;\n        if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n          throw \"Source or target not in graph!\";\n        }\n        if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n          throw \"Both owners must be this graph!\";\n        }\n        if (sourceNode.owner != targetNode.owner) {\n          return null;\n        }\n\n        // set source and target\n        newEdge.source = sourceNode;\n        newEdge.target = targetNode;\n\n        // set as intra-graph edge\n        newEdge.isInterGraph = false;\n\n        // add to graph edge list\n        this.getEdges().push(newEdge);\n\n        // add to incidency lists\n        sourceNode.edges.push(newEdge);\n        if (targetNode != sourceNode) {\n          targetNode.edges.push(newEdge);\n        }\n        return newEdge;\n      }\n    };\n    LGraph.prototype.remove = function (obj) {\n      var node = obj;\n      if (obj instanceof LNode) {\n        if (node == null) {\n          throw \"Node is null!\";\n        }\n        if (!(node.owner != null && node.owner == this)) {\n          throw \"Owner graph is invalid!\";\n        }\n        if (this.graphManager == null) {\n          throw \"Owner graph manager is invalid!\";\n        }\n        // remove incident edges first (make a copy to do it safely)\n        var edgesToBeRemoved = node.edges.slice();\n        var edge;\n        var s = edgesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          edge = edgesToBeRemoved[i];\n          if (edge.isInterGraph) {\n            this.graphManager.remove(edge);\n          } else {\n            edge.source.owner.remove(edge);\n          }\n        }\n\n        // now the node itself\n        var index = this.nodes.indexOf(node);\n        if (index == -1) {\n          throw \"Node not in owner node list!\";\n        }\n        this.nodes.splice(index, 1);\n      } else if (obj instanceof LEdge) {\n        var edge = obj;\n        if (edge == null) {\n          throw \"Edge is null!\";\n        }\n        if (!(edge.source != null && edge.target != null)) {\n          throw \"Source and/or target is null!\";\n        }\n        if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n          throw \"Source and/or target owner is invalid!\";\n        }\n        var sourceIndex = edge.source.edges.indexOf(edge);\n        var targetIndex = edge.target.edges.indexOf(edge);\n        if (!(sourceIndex > -1 && targetIndex > -1)) {\n          throw \"Source and/or target doesn't know this edge!\";\n        }\n        edge.source.edges.splice(sourceIndex, 1);\n        if (edge.target != edge.source) {\n          edge.target.edges.splice(targetIndex, 1);\n        }\n        var index = edge.source.owner.getEdges().indexOf(edge);\n        if (index == -1) {\n          throw \"Not in owner's edge list!\";\n        }\n        edge.source.owner.getEdges().splice(index, 1);\n      }\n    };\n    LGraph.prototype.updateLeftTop = function () {\n      var top = Integer.MAX_VALUE;\n      var left = Integer.MAX_VALUE;\n      var nodeTop;\n      var nodeLeft;\n      var margin;\n      var nodes = this.getNodes();\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        nodeTop = lNode.getTop();\n        nodeLeft = lNode.getLeft();\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n      }\n\n      // Do we have any nodes in this graph?\n      if (top == Integer.MAX_VALUE) {\n        return null;\n      }\n      if (nodes[0].getParent().paddingLeft != undefined) {\n        margin = nodes[0].getParent().paddingLeft;\n      } else {\n        margin = this.margin;\n      }\n      this.left = left - margin;\n      this.top = top - margin;\n\n      // Apply the margins and return the result\n      return new Point(this.left, this.top);\n    };\n    LGraph.prototype.updateBounds = function (recursive) {\n      // calculate bounds\n      var left = Integer.MAX_VALUE;\n      var right = -Integer.MAX_VALUE;\n      var top = Integer.MAX_VALUE;\n      var bottom = -Integer.MAX_VALUE;\n      var nodeLeft;\n      var nodeRight;\n      var nodeTop;\n      var nodeBottom;\n      var margin;\n      var nodes = this.nodes;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        if (recursive && lNode.child != null) {\n          lNode.updateBounds();\n        }\n        nodeLeft = lNode.getLeft();\n        nodeRight = lNode.getRight();\n        nodeTop = lNode.getTop();\n        nodeBottom = lNode.getBottom();\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n        if (right < nodeRight) {\n          right = nodeRight;\n        }\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (bottom < nodeBottom) {\n          bottom = nodeBottom;\n        }\n      }\n      var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n      if (left == Integer.MAX_VALUE) {\n        this.left = this.parent.getLeft();\n        this.right = this.parent.getRight();\n        this.top = this.parent.getTop();\n        this.bottom = this.parent.getBottom();\n      }\n      if (nodes[0].getParent().paddingLeft != undefined) {\n        margin = nodes[0].getParent().paddingLeft;\n      } else {\n        margin = this.margin;\n      }\n      this.left = boundingRect.x - margin;\n      this.right = boundingRect.x + boundingRect.width + margin;\n      this.top = boundingRect.y - margin;\n      this.bottom = boundingRect.y + boundingRect.height + margin;\n    };\n    LGraph.calculateBounds = function (nodes) {\n      var left = Integer.MAX_VALUE;\n      var right = -Integer.MAX_VALUE;\n      var top = Integer.MAX_VALUE;\n      var bottom = -Integer.MAX_VALUE;\n      var nodeLeft;\n      var nodeRight;\n      var nodeTop;\n      var nodeBottom;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        nodeLeft = lNode.getLeft();\n        nodeRight = lNode.getRight();\n        nodeTop = lNode.getTop();\n        nodeBottom = lNode.getBottom();\n        if (left > nodeLeft) {\n          left = nodeLeft;\n        }\n        if (right < nodeRight) {\n          right = nodeRight;\n        }\n        if (top > nodeTop) {\n          top = nodeTop;\n        }\n        if (bottom < nodeBottom) {\n          bottom = nodeBottom;\n        }\n      }\n      var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n      return boundingRect;\n    };\n    LGraph.prototype.getInclusionTreeDepth = function () {\n      if (this == this.graphManager.getRoot()) {\n        return 1;\n      } else {\n        return this.parent.getInclusionTreeDepth();\n      }\n    };\n    LGraph.prototype.getEstimatedSize = function () {\n      if (this.estimatedSize == Integer.MIN_VALUE) {\n        throw \"assert failed\";\n      }\n      return this.estimatedSize;\n    };\n    LGraph.prototype.calcEstimatedSize = function () {\n      var size = 0;\n      var nodes = this.nodes;\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        var lNode = nodes[i];\n        size += lNode.calcEstimatedSize();\n      }\n      if (size == 0) {\n        this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n      } else {\n        this.estimatedSize = size / Math.sqrt(this.nodes.length);\n      }\n      return this.estimatedSize;\n    };\n    LGraph.prototype.updateConnected = function () {\n      var self = this;\n      if (this.nodes.length == 0) {\n        this.isConnected = true;\n        return;\n      }\n      var queue = new LinkedList();\n      var visited = new Set();\n      var currentNode = this.nodes[0];\n      var neighborEdges;\n      var currentNeighbor;\n      var childrenOfNode = currentNode.withChildren();\n      childrenOfNode.forEach(function (node) {\n        queue.push(node);\n        visited.add(node);\n      });\n      while (queue.length !== 0) {\n        currentNode = queue.shift();\n\n        // Traverse all neighbors of this node\n        neighborEdges = currentNode.getEdges();\n        var size = neighborEdges.length;\n        for (var i = 0; i < size; i++) {\n          var neighborEdge = neighborEdges[i];\n          currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n          // Add unvisited neighbors to the list to visit\n          if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n            var childrenOfNeighbor = currentNeighbor.withChildren();\n            childrenOfNeighbor.forEach(function (node) {\n              queue.push(node);\n              visited.add(node);\n            });\n          }\n        }\n      }\n      this.isConnected = false;\n      if (visited.size >= this.nodes.length) {\n        var noOfVisitedInThisGraph = 0;\n        visited.forEach(function (visitedNode) {\n          if (visitedNode.owner == self) {\n            noOfVisitedInThisGraph++;\n          }\n        });\n        if (noOfVisitedInThisGraph == this.nodes.length) {\n          this.isConnected = true;\n        }\n      }\n    };\n    module.exports = LGraph;\n\n    /***/\n  }), (/* 7 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LGraph;\n    var LEdge = __webpack_require__(1);\n    function LGraphManager(layout) {\n      LGraph = __webpack_require__(6); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n      this.layout = layout;\n      this.graphs = [];\n      this.edges = [];\n    }\n    LGraphManager.prototype.addRoot = function () {\n      var ngraph = this.layout.newGraph();\n      var nnode = this.layout.newNode(null);\n      var root = this.add(ngraph, nnode);\n      this.setRootGraph(root);\n      return this.rootGraph;\n    };\n    LGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n      //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n      if (newEdge == null && sourceNode == null && targetNode == null) {\n        if (newGraph == null) {\n          throw \"Graph is null!\";\n        }\n        if (parentNode == null) {\n          throw \"Parent node is null!\";\n        }\n        if (this.graphs.indexOf(newGraph) > -1) {\n          throw \"Graph already in this graph mgr!\";\n        }\n        this.graphs.push(newGraph);\n        if (newGraph.parent != null) {\n          throw \"Already has a parent!\";\n        }\n        if (parentNode.child != null) {\n          throw \"Already has a child!\";\n        }\n        newGraph.parent = parentNode;\n        parentNode.child = newGraph;\n        return newGraph;\n      } else {\n        //change the order of the parameters\n        targetNode = newEdge;\n        sourceNode = parentNode;\n        newEdge = newGraph;\n        var sourceGraph = sourceNode.getOwner();\n        var targetGraph = targetNode.getOwner();\n        if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n          throw \"Source not in this graph mgr!\";\n        }\n        if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n          throw \"Target not in this graph mgr!\";\n        }\n        if (sourceGraph == targetGraph) {\n          newEdge.isInterGraph = false;\n          return sourceGraph.add(newEdge, sourceNode, targetNode);\n        } else {\n          newEdge.isInterGraph = true;\n\n          // set source and target\n          newEdge.source = sourceNode;\n          newEdge.target = targetNode;\n\n          // add edge to inter-graph edge list\n          if (this.edges.indexOf(newEdge) > -1) {\n            throw \"Edge already in inter-graph edge list!\";\n          }\n          this.edges.push(newEdge);\n\n          // add edge to source and target incidency lists\n          if (!(newEdge.source != null && newEdge.target != null)) {\n            throw \"Edge source and/or target is null!\";\n          }\n          if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n            throw \"Edge already in source and/or target incidency list!\";\n          }\n          newEdge.source.edges.push(newEdge);\n          newEdge.target.edges.push(newEdge);\n          return newEdge;\n        }\n      }\n    };\n    LGraphManager.prototype.remove = function (lObj) {\n      if (lObj instanceof LGraph) {\n        var graph = lObj;\n        if (graph.getGraphManager() != this) {\n          throw \"Graph not in this graph mgr\";\n        }\n        if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n          throw \"Invalid parent node!\";\n        }\n\n        // first the edges (make a copy to do it safely)\n        var edgesToBeRemoved = [];\n        edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n        var edge;\n        var s = edgesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          edge = edgesToBeRemoved[i];\n          graph.remove(edge);\n        }\n\n        // then the nodes (make a copy to do it safely)\n        var nodesToBeRemoved = [];\n        nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n        var node;\n        s = nodesToBeRemoved.length;\n        for (var i = 0; i < s; i++) {\n          node = nodesToBeRemoved[i];\n          graph.remove(node);\n        }\n\n        // check if graph is the root\n        if (graph == this.rootGraph) {\n          this.setRootGraph(null);\n        }\n\n        // now remove the graph itself\n        var index = this.graphs.indexOf(graph);\n        this.graphs.splice(index, 1);\n\n        // also reset the parent of the graph\n        graph.parent = null;\n      } else if (lObj instanceof LEdge) {\n        edge = lObj;\n        if (edge == null) {\n          throw \"Edge is null!\";\n        }\n        if (!edge.isInterGraph) {\n          throw \"Not an inter-graph edge!\";\n        }\n        if (!(edge.source != null && edge.target != null)) {\n          throw \"Source and/or target is null!\";\n        }\n\n        // remove edge from source and target nodes' incidency lists\n\n        if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n          throw \"Source and/or target doesn't know this edge!\";\n        }\n        var index = edge.source.edges.indexOf(edge);\n        edge.source.edges.splice(index, 1);\n        index = edge.target.edges.indexOf(edge);\n        edge.target.edges.splice(index, 1);\n\n        // remove edge from owner graph manager's inter-graph edge list\n\n        if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n          throw \"Edge owner graph or owner graph manager is null!\";\n        }\n        if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n          throw \"Not in owner graph manager's edge list!\";\n        }\n        var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n        edge.source.owner.getGraphManager().edges.splice(index, 1);\n      }\n    };\n    LGraphManager.prototype.updateBounds = function () {\n      this.rootGraph.updateBounds(true);\n    };\n    LGraphManager.prototype.getGraphs = function () {\n      return this.graphs;\n    };\n    LGraphManager.prototype.getAllNodes = function () {\n      if (this.allNodes == null) {\n        var nodeList = [];\n        var graphs = this.getGraphs();\n        var s = graphs.length;\n        for (var i = 0; i < s; i++) {\n          nodeList = nodeList.concat(graphs[i].getNodes());\n        }\n        this.allNodes = nodeList;\n      }\n      return this.allNodes;\n    };\n    LGraphManager.prototype.resetAllNodes = function () {\n      this.allNodes = null;\n    };\n    LGraphManager.prototype.resetAllEdges = function () {\n      this.allEdges = null;\n    };\n    LGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n      this.allNodesToApplyGravitation = null;\n    };\n    LGraphManager.prototype.getAllEdges = function () {\n      if (this.allEdges == null) {\n        var edgeList = [];\n        var graphs = this.getGraphs();\n        var s = graphs.length;\n        for (var i = 0; i < graphs.length; i++) {\n          edgeList = edgeList.concat(graphs[i].getEdges());\n        }\n        edgeList = edgeList.concat(this.edges);\n        this.allEdges = edgeList;\n      }\n      return this.allEdges;\n    };\n    LGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n      return this.allNodesToApplyGravitation;\n    };\n    LGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n      if (this.allNodesToApplyGravitation != null) {\n        throw \"assert failed\";\n      }\n      this.allNodesToApplyGravitation = nodeList;\n    };\n    LGraphManager.prototype.getRoot = function () {\n      return this.rootGraph;\n    };\n    LGraphManager.prototype.setRootGraph = function (graph) {\n      if (graph.getGraphManager() != this) {\n        throw \"Root not in this graph mgr!\";\n      }\n      this.rootGraph = graph;\n      // root graph must have a root node associated with it for convenience\n      if (graph.parent == null) {\n        graph.parent = this.layout.newNode(\"Root node\");\n      }\n    };\n    LGraphManager.prototype.getLayout = function () {\n      return this.layout;\n    };\n    LGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n      if (!(firstNode != null && secondNode != null)) {\n        throw \"assert failed\";\n      }\n      if (firstNode == secondNode) {\n        return true;\n      }\n      // Is second node an ancestor of the first one?\n      var ownerGraph = firstNode.getOwner();\n      var parentNode;\n      do {\n        parentNode = ownerGraph.getParent();\n        if (parentNode == null) {\n          break;\n        }\n        if (parentNode == secondNode) {\n          return true;\n        }\n        ownerGraph = parentNode.getOwner();\n        if (ownerGraph == null) {\n          break;\n        }\n      } while (true);\n      // Is first node an ancestor of the second one?\n      ownerGraph = secondNode.getOwner();\n      do {\n        parentNode = ownerGraph.getParent();\n        if (parentNode == null) {\n          break;\n        }\n        if (parentNode == firstNode) {\n          return true;\n        }\n        ownerGraph = parentNode.getOwner();\n        if (ownerGraph == null) {\n          break;\n        }\n      } while (true);\n      return false;\n    };\n    LGraphManager.prototype.calcLowestCommonAncestors = function () {\n      var edge;\n      var sourceNode;\n      var targetNode;\n      var sourceAncestorGraph;\n      var targetAncestorGraph;\n      var edges = this.getAllEdges();\n      var s = edges.length;\n      for (var i = 0; i < s; i++) {\n        edge = edges[i];\n        sourceNode = edge.source;\n        targetNode = edge.target;\n        edge.lca = null;\n        edge.sourceInLca = sourceNode;\n        edge.targetInLca = targetNode;\n        if (sourceNode == targetNode) {\n          edge.lca = sourceNode.getOwner();\n          continue;\n        }\n        sourceAncestorGraph = sourceNode.getOwner();\n        while (edge.lca == null) {\n          edge.targetInLca = targetNode;\n          targetAncestorGraph = targetNode.getOwner();\n          while (edge.lca == null) {\n            if (targetAncestorGraph == sourceAncestorGraph) {\n              edge.lca = targetAncestorGraph;\n              break;\n            }\n            if (targetAncestorGraph == this.rootGraph) {\n              break;\n            }\n            if (edge.lca != null) {\n              throw \"assert failed\";\n            }\n            edge.targetInLca = targetAncestorGraph.getParent();\n            targetAncestorGraph = edge.targetInLca.getOwner();\n          }\n          if (sourceAncestorGraph == this.rootGraph) {\n            break;\n          }\n          if (edge.lca == null) {\n            edge.sourceInLca = sourceAncestorGraph.getParent();\n            sourceAncestorGraph = edge.sourceInLca.getOwner();\n          }\n        }\n        if (edge.lca == null) {\n          throw \"assert failed\";\n        }\n      }\n    };\n    LGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n      if (firstNode == secondNode) {\n        return firstNode.getOwner();\n      }\n      var firstOwnerGraph = firstNode.getOwner();\n      do {\n        if (firstOwnerGraph == null) {\n          break;\n        }\n        var secondOwnerGraph = secondNode.getOwner();\n        do {\n          if (secondOwnerGraph == null) {\n            break;\n          }\n          if (secondOwnerGraph == firstOwnerGraph) {\n            return secondOwnerGraph;\n          }\n          secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n        } while (true);\n        firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n      } while (true);\n      return firstOwnerGraph;\n    };\n    LGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n      if (graph == null && depth == null) {\n        graph = this.rootGraph;\n        depth = 1;\n      }\n      var node;\n      var nodes = graph.getNodes();\n      var s = nodes.length;\n      for (var i = 0; i < s; i++) {\n        node = nodes[i];\n        node.inclusionTreeDepth = depth;\n        if (node.child != null) {\n          this.calcInclusionTreeDepths(node.child, depth + 1);\n        }\n      }\n    };\n    LGraphManager.prototype.includesInvalidEdge = function () {\n      var edge;\n      var edgesToRemove = [];\n      var s = this.edges.length;\n      for (var i = 0; i < s; i++) {\n        edge = this.edges[i];\n        if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n          edgesToRemove.push(edge);\n        }\n      }\n\n      // Remove invalid edges from graph manager\n      for (var i = 0; i < edgesToRemove.length; i++) {\n        this.remove(edgesToRemove[i]);\n      }\n\n      // Invalid edges are cleared, so return false\n      return false;\n    };\n    module.exports = LGraphManager;\n\n    /***/\n  }), (/* 8 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    /**\n     * This class maintains a list of static geometry related utility methods.\n     *\n     *\n     * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n     */\n    var Point = __webpack_require__(12);\n    function IGeometry() {}\n\n    /**\n     * This method calculates *half* the amount in x and y directions of the two\n     * input rectangles needed to separate them keeping their respective\n     * positioning, and returns the result in the input array. An input\n     * separation buffer added to the amount in both directions. We assume that\n     * the two rectangles do intersect.\n     */\n    IGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n      if (!rectA.intersects(rectB)) {\n        throw \"assert failed\";\n      }\n      var directions = new Array(2);\n      this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n      overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n      overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n      // update the overlapping amounts for the following cases:\n      if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n        /* Case x.1:\n        *\n        * rectA\n        * \t|                       |\n        * \t|        _________      |\n        * \t|        |       |      |\n        * \t|________|_______|______|\n        * \t\t\t |       |\n        *           |       |\n        *        rectB\n        */\n        overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n      } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n        /* Case x.2:\n        *\n        * rectB\n        * \t|                       |\n        * \t|        _________      |\n        * \t|        |       |      |\n        * \t|________|_______|______|\n        * \t\t\t |       |\n        *           |       |\n        *        rectA\n        */\n        overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n      }\n      if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n        /* Case y.1:\n         *          ________ rectA\n         *         |\n         *         |\n         *   ______|____  rectB\n         *         |    |\n         *         |    |\n         *   ______|____|\n         *         |\n         *         |\n         *         |________\n         *\n         */\n        overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n      } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n        /* Case y.2:\n        *          ________ rectB\n        *         |\n        *         |\n        *   ______|____  rectA\n        *         |    |\n        *         |    |\n        *   ______|____|\n        *         |\n        *         |\n        *         |________\n        *\n        */\n        overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n      }\n\n      // find slope of the line passes two centers\n      var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n      // if centers are overlapped\n      if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n        // assume the slope is 1 (45 degree)\n        slope = 1.0;\n      }\n      var moveByY = slope * overlapAmount[0];\n      var moveByX = overlapAmount[1] / slope;\n      if (overlapAmount[0] < moveByX) {\n        moveByX = overlapAmount[0];\n      } else {\n        moveByY = overlapAmount[1];\n      }\n      // return half the amount so that if each rectangle is moved by these\n      // amounts in opposite directions, overlap will be resolved\n      overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n      overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n    };\n\n    /**\n     * This method decides the separation direction of overlapping nodes\n     *\n     * if directions[0] = -1, then rectA goes left\n     * if directions[0] = 1,  then rectA goes right\n     * if directions[1] = -1, then rectA goes up\n     * if directions[1] = 1,  then rectA goes down\n     */\n    IGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n      if (rectA.getCenterX() < rectB.getCenterX()) {\n        directions[0] = -1;\n      } else {\n        directions[0] = 1;\n      }\n      if (rectA.getCenterY() < rectB.getCenterY()) {\n        directions[1] = -1;\n      } else {\n        directions[1] = 1;\n      }\n    };\n\n    /**\n     * This method calculates the intersection (clipping) points of the two\n     * input rectangles with line segment defined by the centers of these two\n     * rectangles. The clipping points are saved in the input double array and\n     * whether or not the two rectangles overlap is returned.\n     */\n    IGeometry.getIntersection2 = function (rectA, rectB, result) {\n      //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n      var p1x = rectA.getCenterX();\n      var p1y = rectA.getCenterY();\n      var p2x = rectB.getCenterX();\n      var p2y = rectB.getCenterY();\n\n      //if two rectangles intersect, then clipping points are centers\n      if (rectA.intersects(rectB)) {\n        result[0] = p1x;\n        result[1] = p1y;\n        result[2] = p2x;\n        result[3] = p2y;\n        return true;\n      }\n      //variables for rectA\n      var topLeftAx = rectA.getX();\n      var topLeftAy = rectA.getY();\n      var topRightAx = rectA.getRight();\n      var bottomLeftAx = rectA.getX();\n      var bottomLeftAy = rectA.getBottom();\n      var bottomRightAx = rectA.getRight();\n      var halfWidthA = rectA.getWidthHalf();\n      var halfHeightA = rectA.getHeightHalf();\n      //variables for rectB\n      var topLeftBx = rectB.getX();\n      var topLeftBy = rectB.getY();\n      var topRightBx = rectB.getRight();\n      var bottomLeftBx = rectB.getX();\n      var bottomLeftBy = rectB.getBottom();\n      var bottomRightBx = rectB.getRight();\n      var halfWidthB = rectB.getWidthHalf();\n      var halfHeightB = rectB.getHeightHalf();\n\n      //flag whether clipping points are found\n      var clipPointAFound = false;\n      var clipPointBFound = false;\n\n      // line is vertical\n      if (p1x === p2x) {\n        if (p1y > p2y) {\n          result[0] = p1x;\n          result[1] = topLeftAy;\n          result[2] = p2x;\n          result[3] = bottomLeftBy;\n          return false;\n        } else if (p1y < p2y) {\n          result[0] = p1x;\n          result[1] = bottomLeftAy;\n          result[2] = p2x;\n          result[3] = topLeftBy;\n          return false;\n        } else {\n          //not line, return null;\n        }\n      }\n      // line is horizontal\n      else if (p1y === p2y) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = p1y;\n          result[2] = topRightBx;\n          result[3] = p2y;\n          return false;\n        } else if (p1x < p2x) {\n          result[0] = topRightAx;\n          result[1] = p1y;\n          result[2] = topLeftBx;\n          result[3] = p2y;\n          return false;\n        } else {\n          //not valid line, return null;\n        }\n      } else {\n        //slopes of rectA's and rectB's diagonals\n        var slopeA = rectA.height / rectA.width;\n        var slopeB = rectB.height / rectB.width;\n\n        //slope of line between center of rectA and center of rectB\n        var slopePrime = (p2y - p1y) / (p2x - p1x);\n        var cardinalDirectionA = void 0;\n        var cardinalDirectionB = void 0;\n        var tempPointAx = void 0;\n        var tempPointAy = void 0;\n        var tempPointBx = void 0;\n        var tempPointBy = void 0;\n\n        //determine whether clipping point is the corner of nodeA\n        if (-slopeA === slopePrime) {\n          if (p1x > p2x) {\n            result[0] = bottomLeftAx;\n            result[1] = bottomLeftAy;\n            clipPointAFound = true;\n          } else {\n            result[0] = topRightAx;\n            result[1] = topLeftAy;\n            clipPointAFound = true;\n          }\n        } else if (slopeA === slopePrime) {\n          if (p1x > p2x) {\n            result[0] = topLeftAx;\n            result[1] = topLeftAy;\n            clipPointAFound = true;\n          } else {\n            result[0] = bottomRightAx;\n            result[1] = bottomLeftAy;\n            clipPointAFound = true;\n          }\n        }\n\n        //determine whether clipping point is the corner of nodeB\n        if (-slopeB === slopePrime) {\n          if (p2x > p1x) {\n            result[2] = bottomLeftBx;\n            result[3] = bottomLeftBy;\n            clipPointBFound = true;\n          } else {\n            result[2] = topRightBx;\n            result[3] = topLeftBy;\n            clipPointBFound = true;\n          }\n        } else if (slopeB === slopePrime) {\n          if (p2x > p1x) {\n            result[2] = topLeftBx;\n            result[3] = topLeftBy;\n            clipPointBFound = true;\n          } else {\n            result[2] = bottomRightBx;\n            result[3] = bottomLeftBy;\n            clipPointBFound = true;\n          }\n        }\n\n        //if both clipping points are corners\n        if (clipPointAFound && clipPointBFound) {\n          return false;\n        }\n\n        //determine Cardinal Direction of rectangles\n        if (p1x > p2x) {\n          if (p1y > p2y) {\n            cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n            cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n          } else {\n            cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n            cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n          }\n        } else {\n          if (p1y > p2y) {\n            cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n            cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n          } else {\n            cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n            cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n          }\n        }\n        //calculate clipping Point if it is not found before\n        if (!clipPointAFound) {\n          switch (cardinalDirectionA) {\n            case 1:\n              tempPointAy = topLeftAy;\n              tempPointAx = p1x + -halfHeightA / slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 2:\n              tempPointAx = bottomRightAx;\n              tempPointAy = p1y + halfWidthA * slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 3:\n              tempPointAy = bottomLeftAy;\n              tempPointAx = p1x + halfHeightA / slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n            case 4:\n              tempPointAx = bottomLeftAx;\n              tempPointAy = p1y + -halfWidthA * slopePrime;\n              result[0] = tempPointAx;\n              result[1] = tempPointAy;\n              break;\n          }\n        }\n        if (!clipPointBFound) {\n          switch (cardinalDirectionB) {\n            case 1:\n              tempPointBy = topLeftBy;\n              tempPointBx = p2x + -halfHeightB / slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 2:\n              tempPointBx = bottomRightBx;\n              tempPointBy = p2y + halfWidthB * slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 3:\n              tempPointBy = bottomLeftBy;\n              tempPointBx = p2x + halfHeightB / slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n            case 4:\n              tempPointBx = bottomLeftBx;\n              tempPointBy = p2y + -halfWidthB * slopePrime;\n              result[2] = tempPointBx;\n              result[3] = tempPointBy;\n              break;\n          }\n        }\n      }\n      return false;\n    };\n\n    /**\n     * This method returns in which cardinal direction does input point stays\n     * 1: North\n     * 2: East\n     * 3: South\n     * 4: West\n     */\n    IGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n      if (slope > slopePrime) {\n        return line;\n      } else {\n        return 1 + line % 4;\n      }\n    };\n\n    /**\n     * This method calculates the intersection of the two lines defined by\n     * point pairs (s1,s2) and (f1,f2).\n     */\n    IGeometry.getIntersection = function (s1, s2, f1, f2) {\n      if (f2 == null) {\n        return this.getIntersection2(s1, s2, f1);\n      }\n      var x1 = s1.x;\n      var y1 = s1.y;\n      var x2 = s2.x;\n      var y2 = s2.y;\n      var x3 = f1.x;\n      var y3 = f1.y;\n      var x4 = f2.x;\n      var y4 = f2.y;\n      var x = void 0,\n        y = void 0; // intersection point\n      var a1 = void 0,\n        a2 = void 0,\n        b1 = void 0,\n        b2 = void 0,\n        c1 = void 0,\n        c2 = void 0; // coefficients of line eqns.\n      var denom = void 0;\n      a1 = y2 - y1;\n      b1 = x1 - x2;\n      c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n      a2 = y4 - y3;\n      b2 = x3 - x4;\n      c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n      denom = a1 * b2 - a2 * b1;\n      if (denom === 0) {\n        return null;\n      }\n      x = (b1 * c2 - b2 * c1) / denom;\n      y = (a2 * c1 - a1 * c2) / denom;\n      return new Point(x, y);\n    };\n\n    /**\n     * This method finds and returns the angle of the vector from the + x-axis\n     * in clockwise direction (compatible w/ Java coordinate system!).\n     */\n    IGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n      var C_angle = void 0;\n      if (Cx !== Nx) {\n        C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n        if (Nx < Cx) {\n          C_angle += Math.PI;\n        } else if (Ny < Cy) {\n          C_angle += this.TWO_PI;\n        }\n      } else if (Ny < Cy) {\n        C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n      } else {\n        C_angle = this.HALF_PI; // 90 degrees\n      }\n      return C_angle;\n    };\n\n    /**\n     * This method checks whether the given two line segments (one with point\n     * p1 and p2, the other with point p3 and p4) intersect at a point other\n     * than these points.\n     */\n    IGeometry.doIntersect = function (p1, p2, p3, p4) {\n      var a = p1.x;\n      var b = p1.y;\n      var c = p2.x;\n      var d = p2.y;\n      var p = p3.x;\n      var q = p3.y;\n      var r = p4.x;\n      var s = p4.y;\n      var det = (c - a) * (s - q) - (r - p) * (d - b);\n      if (det === 0) {\n        return false;\n      } else {\n        var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n        var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n        return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n      }\n    };\n\n    /**\n     * This method checks and calculates the intersection of \n     * a line segment and a circle.\n     */\n    IGeometry.findCircleLineIntersections = function (Ex, Ey, Lx, Ly, Cx, Cy, r) {\n      // E is the starting point of the ray,\n      // L is the end point of the ray,\n      // C is the center of sphere you're testing against\n      // r is the radius of that sphere\n\n      // Compute:\n      // d = L - E ( Direction vector of ray, from start to end )\n      // f = E - C ( Vector from center sphere to ray start )\n\n      // Then the intersection is found by..\n      // P = E + t * d\n      // This is a parametric equation:\n      // Px = Ex + tdx\n      // Py = Ey + tdy\n\n      // get a, b, c values\n      var a = (Lx - Ex) * (Lx - Ex) + (Ly - Ey) * (Ly - Ey);\n      var b = 2 * ((Ex - Cx) * (Lx - Ex) + (Ey - Cy) * (Ly - Ey));\n      var c = (Ex - Cx) * (Ex - Cx) + (Ey - Cy) * (Ey - Cy) - r * r;\n\n      // get discriminant\n      var disc = b * b - 4 * a * c;\n      if (disc >= 0) {\n        // insert into quadratic formula\n        var t1 = (-b + Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n        var t2 = (-b - Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n        var intersections = null;\n        if (t1 >= 0 && t1 <= 1) {\n          // t1 is the intersection, and it's closer than t2\n          // (since t1 uses -b - discriminant)\n          // Impale, Poke\n          return [t1];\n        }\n\n        // here t1 didn't intersect so we are either started\n        // inside the sphere or completely past it\n        if (t2 >= 0 && t2 <= 1) {\n          // ExitWound\n          return [t2];\n        }\n        return intersections;\n      } else return null;\n    };\n\n    // -----------------------------------------------------------------------------\n    // Section: Class Constants\n    // -----------------------------------------------------------------------------\n    /**\n     * Some useful pre-calculated constants\n     */\n    IGeometry.HALF_PI = 0.5 * Math.PI;\n    IGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\n    IGeometry.TWO_PI = 2.0 * Math.PI;\n    IGeometry.THREE_PI = 3.0 * Math.PI;\n    module.exports = IGeometry;\n\n    /***/\n  }), (/* 9 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function IMath() {}\n\n    /**\n     * This method returns the sign of the input value.\n     */\n    IMath.sign = function (value) {\n      if (value > 0) {\n        return 1;\n      } else if (value < 0) {\n        return -1;\n      } else {\n        return 0;\n      }\n    };\n    IMath.floor = function (value) {\n      return value < 0 ? Math.ceil(value) : Math.floor(value);\n    };\n    IMath.ceil = function (value) {\n      return value < 0 ? Math.floor(value) : Math.ceil(value);\n    };\n    module.exports = IMath;\n\n    /***/\n  }), (/* 10 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function Integer() {}\n    Integer.MAX_VALUE = 2147483647;\n    Integer.MIN_VALUE = -2147483648;\n    module.exports = Integer;\n\n    /***/\n  }), (/* 11 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n    var nodeFrom = function nodeFrom(value) {\n      return {\n        value: value,\n        next: null,\n        prev: null\n      };\n    };\n    var add = function add(prev, node, next, list) {\n      if (prev !== null) {\n        prev.next = node;\n      } else {\n        list.head = node;\n      }\n      if (next !== null) {\n        next.prev = node;\n      } else {\n        list.tail = node;\n      }\n      node.prev = prev;\n      node.next = next;\n      list.length++;\n      return node;\n    };\n    var _remove = function _remove(node, list) {\n      var prev = node.prev,\n        next = node.next;\n      if (prev !== null) {\n        prev.next = next;\n      } else {\n        list.head = next;\n      }\n      if (next !== null) {\n        next.prev = prev;\n      } else {\n        list.tail = prev;\n      }\n      node.prev = node.next = null;\n      list.length--;\n      return node;\n    };\n    var LinkedList = function () {\n      function LinkedList(vals) {\n        var _this = this;\n        _classCallCheck(this, LinkedList);\n        this.length = 0;\n        this.head = null;\n        this.tail = null;\n        if (vals != null) {\n          vals.forEach(function (v) {\n            return _this.push(v);\n          });\n        }\n      }\n      _createClass(LinkedList, [{\n        key: \"size\",\n        value: function size() {\n          return this.length;\n        }\n      }, {\n        key: \"insertBefore\",\n        value: function insertBefore(val, otherNode) {\n          return add(otherNode.prev, nodeFrom(val), otherNode, this);\n        }\n      }, {\n        key: \"insertAfter\",\n        value: function insertAfter(val, otherNode) {\n          return add(otherNode, nodeFrom(val), otherNode.next, this);\n        }\n      }, {\n        key: \"insertNodeBefore\",\n        value: function insertNodeBefore(newNode, otherNode) {\n          return add(otherNode.prev, newNode, otherNode, this);\n        }\n      }, {\n        key: \"insertNodeAfter\",\n        value: function insertNodeAfter(newNode, otherNode) {\n          return add(otherNode, newNode, otherNode.next, this);\n        }\n      }, {\n        key: \"push\",\n        value: function push(val) {\n          return add(this.tail, nodeFrom(val), null, this);\n        }\n      }, {\n        key: \"unshift\",\n        value: function unshift(val) {\n          return add(null, nodeFrom(val), this.head, this);\n        }\n      }, {\n        key: \"remove\",\n        value: function remove(node) {\n          return _remove(node, this);\n        }\n      }, {\n        key: \"pop\",\n        value: function pop() {\n          return _remove(this.tail, this).value;\n        }\n      }, {\n        key: \"popNode\",\n        value: function popNode() {\n          return _remove(this.tail, this);\n        }\n      }, {\n        key: \"shift\",\n        value: function shift() {\n          return _remove(this.head, this).value;\n        }\n      }, {\n        key: \"shiftNode\",\n        value: function shiftNode() {\n          return _remove(this.head, this);\n        }\n      }, {\n        key: \"get_object_at\",\n        value: function get_object_at(index) {\n          if (index <= this.length()) {\n            var i = 1;\n            var current = this.head;\n            while (i < index) {\n              current = current.next;\n              i++;\n            }\n            return current.value;\n          }\n        }\n      }, {\n        key: \"set_object_at\",\n        value: function set_object_at(index, value) {\n          if (index <= this.length()) {\n            var i = 1;\n            var current = this.head;\n            while (i < index) {\n              current = current.next;\n              i++;\n            }\n            current.value = value;\n          }\n        }\n      }]);\n      return LinkedList;\n    }();\n    module.exports = LinkedList;\n\n    /***/\n  }), (/* 12 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    /*\r\n     *This class is the javascript implementation of the Point.java class in jdk\r\n     */\n    function Point(x, y, p) {\n      this.x = null;\n      this.y = null;\n      if (x == null && y == null && p == null) {\n        this.x = 0;\n        this.y = 0;\n      } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n        this.x = x;\n        this.y = y;\n      } else if (x.constructor.name == 'Point' && y == null && p == null) {\n        p = x;\n        this.x = p.x;\n        this.y = p.y;\n      }\n    }\n    Point.prototype.getX = function () {\n      return this.x;\n    };\n    Point.prototype.getY = function () {\n      return this.y;\n    };\n    Point.prototype.getLocation = function () {\n      return new Point(this.x, this.y);\n    };\n    Point.prototype.setLocation = function (x, y, p) {\n      if (x.constructor.name == 'Point' && y == null && p == null) {\n        p = x;\n        this.setLocation(p.x, p.y);\n      } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n        //if both parameters are integer just move (x,y) location\n        if (parseInt(x) == x && parseInt(y) == y) {\n          this.move(x, y);\n        } else {\n          this.x = Math.floor(x + 0.5);\n          this.y = Math.floor(y + 0.5);\n        }\n      }\n    };\n    Point.prototype.move = function (x, y) {\n      this.x = x;\n      this.y = y;\n    };\n    Point.prototype.translate = function (dx, dy) {\n      this.x += dx;\n      this.y += dy;\n    };\n    Point.prototype.equals = function (obj) {\n      if (obj.constructor.name == \"Point\") {\n        var pt = obj;\n        return this.x == pt.x && this.y == pt.y;\n      }\n      return this == obj;\n    };\n    Point.prototype.toString = function () {\n      return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n    };\n    module.exports = Point;\n\n    /***/\n  }), (/* 13 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function RectangleD(x, y, width, height) {\n      this.x = 0;\n      this.y = 0;\n      this.width = 0;\n      this.height = 0;\n      if (x != null && y != null && width != null && height != null) {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n      }\n    }\n    RectangleD.prototype.getX = function () {\n      return this.x;\n    };\n    RectangleD.prototype.setX = function (x) {\n      this.x = x;\n    };\n    RectangleD.prototype.getY = function () {\n      return this.y;\n    };\n    RectangleD.prototype.setY = function (y) {\n      this.y = y;\n    };\n    RectangleD.prototype.getWidth = function () {\n      return this.width;\n    };\n    RectangleD.prototype.setWidth = function (width) {\n      this.width = width;\n    };\n    RectangleD.prototype.getHeight = function () {\n      return this.height;\n    };\n    RectangleD.prototype.setHeight = function (height) {\n      this.height = height;\n    };\n    RectangleD.prototype.getRight = function () {\n      return this.x + this.width;\n    };\n    RectangleD.prototype.getBottom = function () {\n      return this.y + this.height;\n    };\n    RectangleD.prototype.intersects = function (a) {\n      if (this.getRight() < a.x) {\n        return false;\n      }\n      if (this.getBottom() < a.y) {\n        return false;\n      }\n      if (a.getRight() < this.x) {\n        return false;\n      }\n      if (a.getBottom() < this.y) {\n        return false;\n      }\n      return true;\n    };\n    RectangleD.prototype.getCenterX = function () {\n      return this.x + this.width / 2;\n    };\n    RectangleD.prototype.getMinX = function () {\n      return this.getX();\n    };\n    RectangleD.prototype.getMaxX = function () {\n      return this.getX() + this.width;\n    };\n    RectangleD.prototype.getCenterY = function () {\n      return this.y + this.height / 2;\n    };\n    RectangleD.prototype.getMinY = function () {\n      return this.getY();\n    };\n    RectangleD.prototype.getMaxY = function () {\n      return this.getY() + this.height;\n    };\n    RectangleD.prototype.getWidthHalf = function () {\n      return this.width / 2;\n    };\n    RectangleD.prototype.getHeightHalf = function () {\n      return this.height / 2;\n    };\n    module.exports = RectangleD;\n\n    /***/\n  }), (/* 14 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n    function UniqueIDGeneretor() {}\n    UniqueIDGeneretor.lastID = 0;\n    UniqueIDGeneretor.createID = function (obj) {\n      if (UniqueIDGeneretor.isPrimitive(obj)) {\n        return obj;\n      }\n      if (obj.uniqueID != null) {\n        return obj.uniqueID;\n      }\n      obj.uniqueID = UniqueIDGeneretor.getString();\n      UniqueIDGeneretor.lastID++;\n      return obj.uniqueID;\n    };\n    UniqueIDGeneretor.getString = function (id) {\n      if (id == null) id = UniqueIDGeneretor.lastID;\n      return \"Object#\" + id + \"\";\n    };\n    UniqueIDGeneretor.isPrimitive = function (arg) {\n      var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n      return arg == null || type != \"object\" && type != \"function\";\n    };\n    module.exports = UniqueIDGeneretor;\n\n    /***/\n  }), (/* 15 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function _toConsumableArray(arr) {\n      if (Array.isArray(arr)) {\n        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      } else {\n        return Array.from(arr);\n      }\n    }\n    var LayoutConstants = __webpack_require__(0);\n    var LGraphManager = __webpack_require__(7);\n    var LNode = __webpack_require__(3);\n    var LEdge = __webpack_require__(1);\n    var LGraph = __webpack_require__(6);\n    var PointD = __webpack_require__(5);\n    var Transform = __webpack_require__(17);\n    var Emitter = __webpack_require__(29);\n    function Layout(isRemoteUse) {\n      Emitter.call(this);\n\n      //Layout Quality: 0:draft, 1:default, 2:proof\n      this.layoutQuality = LayoutConstants.QUALITY;\n      //Whether layout should create bendpoints as needed or not\n      this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n      //Whether layout should be incremental or not\n      this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n      //Whether we animate from before to after layout node positions\n      this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n      //Whether we animate the layout process or not\n      this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n      //Number iterations that should be done between two successive animations\n      this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n      /**\r\n       * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n       * they are, both spring and repulsion forces between two leaf nodes can be\r\n       * calculated without the expensive clipping point calculations, resulting\r\n       * in major speed-up.\r\n       */\n      this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n      /**\r\n       * This is used for creation of bendpoints by using dummy nodes and edges.\r\n       * Maps an LEdge to its dummy bendpoint path.\r\n       */\n      this.edgeToDummyNodes = new Map();\n      this.graphManager = new LGraphManager(this);\n      this.isLayoutFinished = false;\n      this.isSubLayout = false;\n      this.isRemoteUse = false;\n      if (isRemoteUse != null) {\n        this.isRemoteUse = isRemoteUse;\n      }\n    }\n    Layout.RANDOM_SEED = 1;\n    Layout.prototype = Object.create(Emitter.prototype);\n    Layout.prototype.getGraphManager = function () {\n      return this.graphManager;\n    };\n    Layout.prototype.getAllNodes = function () {\n      return this.graphManager.getAllNodes();\n    };\n    Layout.prototype.getAllEdges = function () {\n      return this.graphManager.getAllEdges();\n    };\n    Layout.prototype.getAllNodesToApplyGravitation = function () {\n      return this.graphManager.getAllNodesToApplyGravitation();\n    };\n    Layout.prototype.newGraphManager = function () {\n      var gm = new LGraphManager(this);\n      this.graphManager = gm;\n      return gm;\n    };\n    Layout.prototype.newGraph = function (vGraph) {\n      return new LGraph(null, this.graphManager, vGraph);\n    };\n    Layout.prototype.newNode = function (vNode) {\n      return new LNode(this.graphManager, vNode);\n    };\n    Layout.prototype.newEdge = function (vEdge) {\n      return new LEdge(null, null, vEdge);\n    };\n    Layout.prototype.checkLayoutSuccess = function () {\n      return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n    };\n    Layout.prototype.runLayout = function () {\n      this.isLayoutFinished = false;\n      if (this.tilingPreLayout) {\n        this.tilingPreLayout();\n      }\n      this.initParameters();\n      var isLayoutSuccessfull;\n      if (this.checkLayoutSuccess()) {\n        isLayoutSuccessfull = false;\n      } else {\n        isLayoutSuccessfull = this.layout();\n      }\n      if (LayoutConstants.ANIMATE === 'during') {\n        // If this is a 'during' layout animation. Layout is not finished yet. \n        // We need to perform these in index.js when layout is really finished.\n        return false;\n      }\n      if (isLayoutSuccessfull) {\n        if (!this.isSubLayout) {\n          this.doPostLayout();\n        }\n      }\n      if (this.tilingPostLayout) {\n        this.tilingPostLayout();\n      }\n      this.isLayoutFinished = true;\n      return isLayoutSuccessfull;\n    };\n\n    /**\r\n     * This method performs the operations required after layout.\r\n     */\n    Layout.prototype.doPostLayout = function () {\n      //assert !isSubLayout : \"Should not be called on sub-layout!\";\n      // Propagate geometric changes to v-level objects\n      if (!this.incremental) {\n        this.transform();\n      }\n      this.update();\n    };\n\n    /**\r\n     * This method updates the geometry of the target graph according to\r\n     * calculated layout.\r\n     */\n    Layout.prototype.update2 = function () {\n      // update bend points\n      if (this.createBendsAsNeeded) {\n        this.createBendpointsFromDummyNodes();\n\n        // reset all edges, since the topology has changed\n        this.graphManager.resetAllEdges();\n      }\n\n      // perform edge, node and root updates if layout is not called\n      // remotely\n      if (!this.isRemoteUse) {\n        // update all edges\n        var edge;\n        var allEdges = this.graphManager.getAllEdges();\n        for (var i = 0; i < allEdges.length; i++) {\n          edge = allEdges[i];\n          //      this.update(edge);\n        }\n\n        // recursively update nodes\n        var node;\n        var nodes = this.graphManager.getRoot().getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          node = nodes[i];\n          //      this.update(node);\n        }\n\n        // update root graph\n        this.update(this.graphManager.getRoot());\n      }\n    };\n    Layout.prototype.update = function (obj) {\n      if (obj == null) {\n        this.update2();\n      } else if (obj instanceof LNode) {\n        var node = obj;\n        if (node.getChild() != null) {\n          // since node is compound, recursively update child nodes\n          var nodes = node.getChild().getNodes();\n          for (var i = 0; i < nodes.length; i++) {\n            update(nodes[i]);\n          }\n        }\n\n        // if the l-level node is associated with a v-level graph object,\n        // then it is assumed that the v-level node implements the\n        // interface Updatable.\n        if (node.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vNode = node.vGraphObject;\n\n          // call the update method of the interface\n          vNode.update(node);\n        }\n      } else if (obj instanceof LEdge) {\n        var edge = obj;\n        // if the l-level edge is associated with a v-level graph object,\n        // then it is assumed that the v-level edge implements the\n        // interface Updatable.\n\n        if (edge.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vEdge = edge.vGraphObject;\n\n          // call the update method of the interface\n          vEdge.update(edge);\n        }\n      } else if (obj instanceof LGraph) {\n        var graph = obj;\n        // if the l-level graph is associated with a v-level graph object,\n        // then it is assumed that the v-level object implements the\n        // interface Updatable.\n\n        if (graph.vGraphObject != null) {\n          // cast to Updatable without any type check\n          var vGraph = graph.vGraphObject;\n\n          // call the update method of the interface\n          vGraph.update(graph);\n        }\n      }\n    };\n\n    /**\r\n     * This method is used to set all layout parameters to default values\r\n     * determined at compile time.\r\n     */\n    Layout.prototype.initParameters = function () {\n      if (!this.isSubLayout) {\n        this.layoutQuality = LayoutConstants.QUALITY;\n        this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n        this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n        this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n        this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n        this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n        this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n      }\n      if (this.animationDuringLayout) {\n        this.animationOnLayout = false;\n      }\n    };\n    Layout.prototype.transform = function (newLeftTop) {\n      if (newLeftTop == undefined) {\n        this.transform(new PointD(0, 0));\n      } else {\n        // create a transformation object (from Eclipse to layout). When an\n        // inverse transform is applied, we get upper-left coordinate of the\n        // drawing or the root graph at given input coordinate (some margins\n        // already included in calculation of left-top).\n\n        var trans = new Transform();\n        var leftTop = this.graphManager.getRoot().updateLeftTop();\n        if (leftTop != null) {\n          trans.setWorldOrgX(newLeftTop.x);\n          trans.setWorldOrgY(newLeftTop.y);\n          trans.setDeviceOrgX(leftTop.x);\n          trans.setDeviceOrgY(leftTop.y);\n          var nodes = this.getAllNodes();\n          var node;\n          for (var i = 0; i < nodes.length; i++) {\n            node = nodes[i];\n            node.transform(trans);\n          }\n        }\n      }\n    };\n    Layout.prototype.positionNodesRandomly = function (graph) {\n      if (graph == undefined) {\n        //assert !this.incremental;\n        this.positionNodesRandomly(this.getGraphManager().getRoot());\n        this.getGraphManager().getRoot().updateBounds(true);\n      } else {\n        var lNode;\n        var childGraph;\n        var nodes = graph.getNodes();\n        for (var i = 0; i < nodes.length; i++) {\n          lNode = nodes[i];\n          childGraph = lNode.getChild();\n          if (childGraph == null) {\n            lNode.scatter();\n          } else if (childGraph.getNodes().length == 0) {\n            lNode.scatter();\n          } else {\n            this.positionNodesRandomly(childGraph);\n            lNode.updateBounds();\n          }\n        }\n      }\n    };\n\n    /**\r\n     * This method returns a list of trees where each tree is represented as a\r\n     * list of l-nodes. The method returns a list of size 0 when:\r\n     * - The graph is not flat or\r\n     * - One of the component(s) of the graph is not a tree.\r\n     */\n    Layout.prototype.getFlatForest = function () {\n      var flatForest = [];\n      var isForest = true;\n\n      // Quick reference for all nodes in the graph manager associated with\n      // this layout. The list should not be changed.\n      var allNodes = this.graphManager.getRoot().getNodes();\n\n      // First be sure that the graph is flat\n      var isFlat = true;\n      for (var i = 0; i < allNodes.length; i++) {\n        if (allNodes[i].getChild() != null) {\n          isFlat = false;\n        }\n      }\n\n      // Return empty forest if the graph is not flat.\n      if (!isFlat) {\n        return flatForest;\n      }\n\n      // Run BFS for each component of the graph.\n\n      var visited = new Set();\n      var toBeVisited = [];\n      var parents = new Map();\n      var unProcessedNodes = [];\n      unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n      // Each iteration of this loop finds a component of the graph and\n      // decides whether it is a tree or not. If it is a tree, adds it to the\n      // forest and continued with the next component.\n\n      while (unProcessedNodes.length > 0 && isForest) {\n        toBeVisited.push(unProcessedNodes[0]);\n\n        // Start the BFS. Each iteration of this loop visits a node in a\n        // BFS manner.\n        while (toBeVisited.length > 0 && isForest) {\n          //pool operation\n          var currentNode = toBeVisited[0];\n          toBeVisited.splice(0, 1);\n          visited.add(currentNode);\n\n          // Traverse all neighbors of this node\n          var neighborEdges = currentNode.getEdges();\n          for (var i = 0; i < neighborEdges.length; i++) {\n            var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n            // If BFS is not growing from this neighbor.\n            if (parents.get(currentNode) != currentNeighbor) {\n              // We haven't previously visited this neighbor.\n              if (!visited.has(currentNeighbor)) {\n                toBeVisited.push(currentNeighbor);\n                parents.set(currentNeighbor, currentNode);\n              }\n              // Since we have previously visited this neighbor and\n              // this neighbor is not parent of currentNode, given\n              // graph contains a component that is not tree, hence\n              // it is not a forest.\n              else {\n                isForest = false;\n                break;\n              }\n            }\n          }\n        }\n\n        // The graph contains a component that is not a tree. Empty\n        // previously found trees. The method will end.\n        if (!isForest) {\n          flatForest = [];\n        }\n        // Save currently visited nodes as a tree in our forest. Reset\n        // visited and parents lists. Continue with the next component of\n        // the graph, if any.\n        else {\n          var temp = [].concat(_toConsumableArray(visited));\n          flatForest.push(temp);\n          //flatForest = flatForest.concat(temp);\n          //unProcessedNodes.removeAll(visited);\n          for (var i = 0; i < temp.length; i++) {\n            var value = temp[i];\n            var index = unProcessedNodes.indexOf(value);\n            if (index > -1) {\n              unProcessedNodes.splice(index, 1);\n            }\n          }\n          visited = new Set();\n          parents = new Map();\n        }\n      }\n      return flatForest;\n    };\n\n    /**\r\n     * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n     * for the given edge (one per bendpoint). The existing l-level structure\r\n     * is updated accordingly.\r\n     */\n    Layout.prototype.createDummyNodesForBendpoints = function (edge) {\n      var dummyNodes = [];\n      var prev = edge.source;\n      var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n      for (var i = 0; i < edge.bendpoints.length; i++) {\n        // create new dummy node\n        var dummyNode = this.newNode(null);\n        dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n        graph.add(dummyNode);\n\n        // create new dummy edge between prev and dummy node\n        var dummyEdge = this.newEdge(null);\n        this.graphManager.add(dummyEdge, prev, dummyNode);\n        dummyNodes.add(dummyNode);\n        prev = dummyNode;\n      }\n      var dummyEdge = this.newEdge(null);\n      this.graphManager.add(dummyEdge, prev, edge.target);\n      this.edgeToDummyNodes.set(edge, dummyNodes);\n\n      // remove real edge from graph manager if it is inter-graph\n      if (edge.isInterGraph()) {\n        this.graphManager.remove(edge);\n      }\n      // else, remove the edge from the current graph\n      else {\n        graph.remove(edge);\n      }\n      return dummyNodes;\n    };\n\n    /**\r\n     * This method creates bendpoints for edges from the dummy nodes\r\n     * at l-level.\r\n     */\n    Layout.prototype.createBendpointsFromDummyNodes = function () {\n      var edges = [];\n      edges = edges.concat(this.graphManager.getAllEdges());\n      edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n      for (var k = 0; k < edges.length; k++) {\n        var lEdge = edges[k];\n        if (lEdge.bendpoints.length > 0) {\n          var path = this.edgeToDummyNodes.get(lEdge);\n          for (var i = 0; i < path.length; i++) {\n            var dummyNode = path[i];\n            var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n            // update bendpoint's location according to dummy node\n            var ebp = lEdge.bendpoints.get(i);\n            ebp.x = p.x;\n            ebp.y = p.y;\n\n            // remove the dummy node, dummy edges incident with this\n            // dummy node is also removed (within the remove method)\n            dummyNode.getOwner().remove(dummyNode);\n          }\n\n          // add the real edge to graph\n          this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n        }\n      }\n    };\n    Layout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n      if (minDiv != undefined && maxMul != undefined) {\n        var value = defaultValue;\n        if (sliderValue <= 50) {\n          var minValue = defaultValue / minDiv;\n          value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n        } else {\n          var maxValue = defaultValue * maxMul;\n          value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n        }\n        return value;\n      } else {\n        var a, b;\n        if (sliderValue <= 50) {\n          a = 9.0 * defaultValue / 500.0;\n          b = defaultValue / 10.0;\n        } else {\n          a = 9.0 * defaultValue / 50.0;\n          b = -8 * defaultValue;\n        }\n        return a * sliderValue + b;\n      }\n    };\n\n    /**\r\n     * This method finds and returns the center of the given nodes, assuming\r\n     * that the given nodes form a tree in themselves.\r\n     */\n    Layout.findCenterOfTree = function (nodes) {\n      var list = [];\n      list = list.concat(nodes);\n      var removedNodes = [];\n      var remainingDegrees = new Map();\n      var foundCenter = false;\n      var centerNode = null;\n      if (list.length == 1 || list.length == 2) {\n        foundCenter = true;\n        centerNode = list[0];\n      }\n      for (var i = 0; i < list.length; i++) {\n        var node = list[i];\n        var degree = node.getNeighborsList().size;\n        remainingDegrees.set(node, node.getNeighborsList().size);\n        if (degree == 1) {\n          removedNodes.push(node);\n        }\n      }\n      var tempList = [];\n      tempList = tempList.concat(removedNodes);\n      while (!foundCenter) {\n        var tempList2 = [];\n        tempList2 = tempList2.concat(tempList);\n        tempList = [];\n        for (var i = 0; i < list.length; i++) {\n          var node = list[i];\n          var index = list.indexOf(node);\n          if (index >= 0) {\n            list.splice(index, 1);\n          }\n          var neighbours = node.getNeighborsList();\n          neighbours.forEach(function (neighbour) {\n            if (removedNodes.indexOf(neighbour) < 0) {\n              var otherDegree = remainingDegrees.get(neighbour);\n              var newDegree = otherDegree - 1;\n              if (newDegree == 1) {\n                tempList.push(neighbour);\n              }\n              remainingDegrees.set(neighbour, newDegree);\n            }\n          });\n        }\n        removedNodes = removedNodes.concat(tempList);\n        if (list.length == 1 || list.length == 2) {\n          foundCenter = true;\n          centerNode = list[0];\n        }\n      }\n      return centerNode;\n    };\n\n    /**\r\n     * During the coarsening process, this layout may be referenced by two graph managers\r\n     * this setter function grants access to change the currently being used graph manager\r\n     */\n    Layout.prototype.setGraphManager = function (gm) {\n      this.graphManager = gm;\n    };\n    module.exports = Layout;\n\n    /***/\n  }), (/* 16 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function RandomSeed() {}\n    // adapted from: https://stackoverflow.com/a/19303725\n    RandomSeed.seed = 1;\n    RandomSeed.x = 0;\n    RandomSeed.nextDouble = function () {\n      RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n      return RandomSeed.x - Math.floor(RandomSeed.x);\n    };\n    module.exports = RandomSeed;\n\n    /***/\n  }), (/* 17 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var PointD = __webpack_require__(5);\n    function Transform(x, y) {\n      this.lworldOrgX = 0.0;\n      this.lworldOrgY = 0.0;\n      this.ldeviceOrgX = 0.0;\n      this.ldeviceOrgY = 0.0;\n      this.lworldExtX = 1.0;\n      this.lworldExtY = 1.0;\n      this.ldeviceExtX = 1.0;\n      this.ldeviceExtY = 1.0;\n    }\n    Transform.prototype.getWorldOrgX = function () {\n      return this.lworldOrgX;\n    };\n    Transform.prototype.setWorldOrgX = function (wox) {\n      this.lworldOrgX = wox;\n    };\n    Transform.prototype.getWorldOrgY = function () {\n      return this.lworldOrgY;\n    };\n    Transform.prototype.setWorldOrgY = function (woy) {\n      this.lworldOrgY = woy;\n    };\n    Transform.prototype.getWorldExtX = function () {\n      return this.lworldExtX;\n    };\n    Transform.prototype.setWorldExtX = function (wex) {\n      this.lworldExtX = wex;\n    };\n    Transform.prototype.getWorldExtY = function () {\n      return this.lworldExtY;\n    };\n    Transform.prototype.setWorldExtY = function (wey) {\n      this.lworldExtY = wey;\n    };\n\n    /* Device related */\n\n    Transform.prototype.getDeviceOrgX = function () {\n      return this.ldeviceOrgX;\n    };\n    Transform.prototype.setDeviceOrgX = function (dox) {\n      this.ldeviceOrgX = dox;\n    };\n    Transform.prototype.getDeviceOrgY = function () {\n      return this.ldeviceOrgY;\n    };\n    Transform.prototype.setDeviceOrgY = function (doy) {\n      this.ldeviceOrgY = doy;\n    };\n    Transform.prototype.getDeviceExtX = function () {\n      return this.ldeviceExtX;\n    };\n    Transform.prototype.setDeviceExtX = function (dex) {\n      this.ldeviceExtX = dex;\n    };\n    Transform.prototype.getDeviceExtY = function () {\n      return this.ldeviceExtY;\n    };\n    Transform.prototype.setDeviceExtY = function (dey) {\n      this.ldeviceExtY = dey;\n    };\n    Transform.prototype.transformX = function (x) {\n      var xDevice = 0.0;\n      var worldExtX = this.lworldExtX;\n      if (worldExtX != 0.0) {\n        xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n      }\n      return xDevice;\n    };\n    Transform.prototype.transformY = function (y) {\n      var yDevice = 0.0;\n      var worldExtY = this.lworldExtY;\n      if (worldExtY != 0.0) {\n        yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n      }\n      return yDevice;\n    };\n    Transform.prototype.inverseTransformX = function (x) {\n      var xWorld = 0.0;\n      var deviceExtX = this.ldeviceExtX;\n      if (deviceExtX != 0.0) {\n        xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n      }\n      return xWorld;\n    };\n    Transform.prototype.inverseTransformY = function (y) {\n      var yWorld = 0.0;\n      var deviceExtY = this.ldeviceExtY;\n      if (deviceExtY != 0.0) {\n        yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n      }\n      return yWorld;\n    };\n    Transform.prototype.inverseTransformPoint = function (inPoint) {\n      var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n      return outPoint;\n    };\n    module.exports = Transform;\n\n    /***/\n  }), (/* 18 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function _toConsumableArray(arr) {\n      if (Array.isArray(arr)) {\n        for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {\n          arr2[i] = arr[i];\n        }\n        return arr2;\n      } else {\n        return Array.from(arr);\n      }\n    }\n    var Layout = __webpack_require__(15);\n    var FDLayoutConstants = __webpack_require__(4);\n    var LayoutConstants = __webpack_require__(0);\n    var IGeometry = __webpack_require__(8);\n    var IMath = __webpack_require__(9);\n    function FDLayout() {\n      Layout.call(this);\n      this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n      this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n      this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n      this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n      this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n      this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n      this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      this.totalDisplacement = 0.0;\n      this.oldTotalDisplacement = 0.0;\n      this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n    }\n    FDLayout.prototype = Object.create(Layout.prototype);\n    for (var prop in Layout) {\n      FDLayout[prop] = Layout[prop];\n    }\n    FDLayout.prototype.initParameters = function () {\n      Layout.prototype.initParameters.call(this, arguments);\n      this.totalIterations = 0;\n      this.notAnimatedIterations = 0;\n      this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n      this.grid = [];\n    };\n    FDLayout.prototype.calcIdealEdgeLengths = function () {\n      var edge;\n      var originalIdealLength;\n      var lcaDepth;\n      var source;\n      var target;\n      var sizeOfSourceInLca;\n      var sizeOfTargetInLca;\n      var allEdges = this.getGraphManager().getAllEdges();\n      for (var i = 0; i < allEdges.length; i++) {\n        edge = allEdges[i];\n        originalIdealLength = edge.idealLength;\n        if (edge.isInterGraph) {\n          source = edge.getSource();\n          target = edge.getTarget();\n          sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n          sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n          if (this.useSmartIdealEdgeLengthCalculation) {\n            edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n          }\n          lcaDepth = edge.getLca().getInclusionTreeDepth();\n          edge.idealLength += originalIdealLength * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n        }\n      }\n    };\n    FDLayout.prototype.initSpringEmbedder = function () {\n      var s = this.getAllNodes().length;\n      if (this.incremental) {\n        if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n          this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n        }\n        this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n      } else {\n        if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n          this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n        } else {\n          this.coolingFactor = 1.0;\n        }\n        this.initialCoolingFactor = this.coolingFactor;\n        this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n      }\n      this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n      // Reassign this attribute by using new constant value\n      this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n      this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n      this.repulsionRange = this.calcRepulsionRange();\n    };\n    FDLayout.prototype.calcSpringForces = function () {\n      var lEdges = this.getAllEdges();\n      var edge;\n      for (var i = 0; i < lEdges.length; i++) {\n        edge = lEdges[i];\n        this.calcSpringForce(edge, edge.idealLength);\n      }\n    };\n    FDLayout.prototype.calcRepulsionForces = function () {\n      var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var i, j;\n      var nodeA, nodeB;\n      var lNodes = this.getAllNodes();\n      var processedNodeSet;\n      if (this.useFRGridVariant) {\n        if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n          this.updateGrid();\n        }\n        processedNodeSet = new Set();\n\n        // calculate repulsion forces between each nodes and its surrounding\n        for (i = 0; i < lNodes.length; i++) {\n          nodeA = lNodes[i];\n          this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n          processedNodeSet.add(nodeA);\n        }\n      } else {\n        for (i = 0; i < lNodes.length; i++) {\n          nodeA = lNodes[i];\n          for (j = i + 1; j < lNodes.length; j++) {\n            nodeB = lNodes[j];\n\n            // If both nodes are not members of the same graph, skip.\n            if (nodeA.getOwner() != nodeB.getOwner()) {\n              continue;\n            }\n            this.calcRepulsionForce(nodeA, nodeB);\n          }\n        }\n      }\n    };\n    FDLayout.prototype.calcGravitationalForces = function () {\n      var node;\n      var lNodes = this.getAllNodesToApplyGravitation();\n      for (var i = 0; i < lNodes.length; i++) {\n        node = lNodes[i];\n        this.calcGravitationalForce(node);\n      }\n    };\n    FDLayout.prototype.moveNodes = function () {\n      var lNodes = this.getAllNodes();\n      var node;\n      for (var i = 0; i < lNodes.length; i++) {\n        node = lNodes[i];\n        node.move();\n      }\n    };\n    FDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n      var sourceNode = edge.getSource();\n      var targetNode = edge.getTarget();\n      var length;\n      var springForce;\n      var springForceX;\n      var springForceY;\n\n      // Update edge length\n      if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n        edge.updateLengthSimple();\n      } else {\n        edge.updateLength();\n        if (edge.isOverlapingSourceAndTarget) {\n          return;\n        }\n      }\n      length = edge.getLength();\n      if (length == 0) return;\n\n      // Calculate spring forces\n      springForce = edge.edgeElasticity * (length - idealLength);\n\n      // Project force onto x and y axes\n      springForceX = springForce * (edge.lengthX / length);\n      springForceY = springForce * (edge.lengthY / length);\n\n      // Apply forces on the end nodes\n      sourceNode.springForceX += springForceX;\n      sourceNode.springForceY += springForceY;\n      targetNode.springForceX -= springForceX;\n      targetNode.springForceY -= springForceY;\n    };\n    FDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n      var rectA = nodeA.getRect();\n      var rectB = nodeB.getRect();\n      var overlapAmount = new Array(2);\n      var clipPoints = new Array(4);\n      var distanceX;\n      var distanceY;\n      var distanceSquared;\n      var distance;\n      var repulsionForce;\n      var repulsionForceX;\n      var repulsionForceY;\n      if (rectA.intersects(rectB))\n        // two nodes overlap\n        {\n          // calculate separation amount in x and y directions\n          IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n          repulsionForceX = 2 * overlapAmount[0];\n          repulsionForceY = 2 * overlapAmount[1];\n          var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n          // Apply forces on the two nodes\n          nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n          nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n          nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n          nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n        } else\n        // no overlap\n        {\n          // calculate distance\n\n          if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null)\n            // simply base repulsion on distance of node centers\n            {\n              distanceX = rectB.getCenterX() - rectA.getCenterX();\n              distanceY = rectB.getCenterY() - rectA.getCenterY();\n            } else\n            // use clipping points\n            {\n              IGeometry.getIntersection(rectA, rectB, clipPoints);\n              distanceX = clipPoints[2] - clipPoints[0];\n              distanceY = clipPoints[3] - clipPoints[1];\n            }\n\n          // No repulsion range. FR grid variant should take care of this.\n          if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n            distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n          }\n          if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n            distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n          }\n          distanceSquared = distanceX * distanceX + distanceY * distanceY;\n          distance = Math.sqrt(distanceSquared);\n\n          // Here we use half of the nodes' repulsion values for backward compatibility\n          repulsionForce = (nodeA.nodeRepulsion / 2 + nodeB.nodeRepulsion / 2) * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n          // Project force onto x and y axes\n          repulsionForceX = repulsionForce * distanceX / distance;\n          repulsionForceY = repulsionForce * distanceY / distance;\n\n          // Apply forces on the two nodes    \n          nodeA.repulsionForceX -= repulsionForceX;\n          nodeA.repulsionForceY -= repulsionForceY;\n          nodeB.repulsionForceX += repulsionForceX;\n          nodeB.repulsionForceY += repulsionForceY;\n        }\n    };\n    FDLayout.prototype.calcGravitationalForce = function (node) {\n      var ownerGraph;\n      var ownerCenterX;\n      var ownerCenterY;\n      var distanceX;\n      var distanceY;\n      var absDistanceX;\n      var absDistanceY;\n      var estimatedSize;\n      ownerGraph = node.getOwner();\n      ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n      ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n      distanceX = node.getCenterX() - ownerCenterX;\n      distanceY = node.getCenterY() - ownerCenterY;\n      absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n      absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n      if (node.getOwner() == this.graphManager.getRoot())\n        // in the root graph\n        {\n          estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n          if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n            node.gravitationForceX = -this.gravityConstant * distanceX;\n            node.gravitationForceY = -this.gravityConstant * distanceY;\n          }\n        } else\n        // inside a compound\n        {\n          estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n          if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n            node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n            node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n          }\n        }\n    };\n    FDLayout.prototype.isConverged = function () {\n      var converged;\n      var oscilating = false;\n      if (this.totalIterations > this.maxIterations / 3) {\n        oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n      }\n      converged = this.totalDisplacement < this.totalDisplacementThreshold;\n      this.oldTotalDisplacement = this.totalDisplacement;\n      return converged || oscilating;\n    };\n    FDLayout.prototype.animate = function () {\n      if (this.animationDuringLayout && !this.isSubLayout) {\n        if (this.notAnimatedIterations == this.animationPeriod) {\n          this.update();\n          this.notAnimatedIterations = 0;\n        } else {\n          this.notAnimatedIterations++;\n        }\n      }\n    };\n\n    //This method calculates the number of children (weight) for all nodes\n    FDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n      var node;\n      var allNodes = this.graphManager.getAllNodes();\n      for (var i = 0; i < allNodes.length; i++) {\n        node = allNodes[i];\n        node.noOfChildren = node.getNoOfChildren();\n      }\n    };\n\n    // -----------------------------------------------------------------------------\n    // Section: FR-Grid Variant Repulsion Force Calculation\n    // -----------------------------------------------------------------------------\n\n    FDLayout.prototype.calcGrid = function (graph) {\n      var sizeX = 0;\n      var sizeY = 0;\n      sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n      sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n      var grid = new Array(sizeX);\n      for (var i = 0; i < sizeX; i++) {\n        grid[i] = new Array(sizeY);\n      }\n      for (var i = 0; i < sizeX; i++) {\n        for (var j = 0; j < sizeY; j++) {\n          grid[i][j] = new Array();\n        }\n      }\n      return grid;\n    };\n    FDLayout.prototype.addNodeToGrid = function (v, left, top) {\n      var startX = 0;\n      var finishX = 0;\n      var startY = 0;\n      var finishY = 0;\n      startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n      finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n      startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n      finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n      for (var i = startX; i <= finishX; i++) {\n        for (var j = startY; j <= finishY; j++) {\n          this.grid[i][j].push(v);\n          v.setGridCoordinates(startX, finishX, startY, finishY);\n        }\n      }\n    };\n    FDLayout.prototype.updateGrid = function () {\n      var i;\n      var nodeA;\n      var lNodes = this.getAllNodes();\n      this.grid = this.calcGrid(this.graphManager.getRoot());\n\n      // put all nodes to proper grid cells\n      for (i = 0; i < lNodes.length; i++) {\n        nodeA = lNodes[i];\n        this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n      }\n    };\n    FDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n      if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n        var surrounding = new Set();\n        nodeA.surrounding = new Array();\n        var nodeB;\n        var grid = this.grid;\n        for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n          for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n            if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n              for (var k = 0; k < grid[i][j].length; k++) {\n                nodeB = grid[i][j][k];\n\n                // If both nodes are not members of the same graph, \n                // or both nodes are the same, skip.\n                if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n                  continue;\n                }\n\n                // check if the repulsion force between\n                // nodeA and nodeB has already been calculated\n                if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n                  var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n                  var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n                  // if the distance between nodeA and nodeB \n                  // is less then calculation range\n                  if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                    //then add nodeB to surrounding of nodeA\n                    surrounding.add(nodeB);\n                  }\n                }\n              }\n            }\n          }\n        }\n        nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n      }\n      for (i = 0; i < nodeA.surrounding.length; i++) {\n        this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n      }\n    };\n    FDLayout.prototype.calcRepulsionRange = function () {\n      return 0.0;\n    };\n    module.exports = FDLayout;\n\n    /***/\n  }), (/* 19 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LEdge = __webpack_require__(1);\n    var FDLayoutConstants = __webpack_require__(4);\n    function FDLayoutEdge(source, target, vEdge) {\n      LEdge.call(this, source, target, vEdge);\n\n      // Ideal length and elasticity value for this edge\n      this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n      this.edgeElasticity = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n    }\n    FDLayoutEdge.prototype = Object.create(LEdge.prototype);\n    for (var prop in LEdge) {\n      FDLayoutEdge[prop] = LEdge[prop];\n    }\n    module.exports = FDLayoutEdge;\n\n    /***/\n  }), (/* 20 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var LNode = __webpack_require__(3);\n    var FDLayoutConstants = __webpack_require__(4);\n    function FDLayoutNode(gm, loc, size, vNode) {\n      // alternative constructor is handled inside LNode\n      LNode.call(this, gm, loc, size, vNode);\n\n      // Repulsion value of this node\n      this.nodeRepulsion = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n\n      //Spring, repulsion and gravitational forces acting on this node\n      this.springForceX = 0;\n      this.springForceY = 0;\n      this.repulsionForceX = 0;\n      this.repulsionForceY = 0;\n      this.gravitationForceX = 0;\n      this.gravitationForceY = 0;\n      //Amount by which this node is to be moved in this iteration\n      this.displacementX = 0;\n      this.displacementY = 0;\n\n      //Start and finish grid coordinates that this node is fallen into\n      this.startX = 0;\n      this.finishX = 0;\n      this.startY = 0;\n      this.finishY = 0;\n\n      //Geometric neighbors of this node\n      this.surrounding = [];\n    }\n    FDLayoutNode.prototype = Object.create(LNode.prototype);\n    for (var prop in LNode) {\n      FDLayoutNode[prop] = LNode[prop];\n    }\n    FDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n      this.startX = _startX;\n      this.finishX = _finishX;\n      this.startY = _startY;\n      this.finishY = _finishY;\n    };\n    module.exports = FDLayoutNode;\n\n    /***/\n  }), (/* 21 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function DimensionD(width, height) {\n      this.width = 0;\n      this.height = 0;\n      if (width !== null && height !== null) {\n        this.height = height;\n        this.width = width;\n      }\n    }\n    DimensionD.prototype.getWidth = function () {\n      return this.width;\n    };\n    DimensionD.prototype.setWidth = function (width) {\n      this.width = width;\n    };\n    DimensionD.prototype.getHeight = function () {\n      return this.height;\n    };\n    DimensionD.prototype.setHeight = function (height) {\n      this.height = height;\n    };\n    module.exports = DimensionD;\n\n    /***/\n  }), (/* 22 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var UniqueIDGeneretor = __webpack_require__(14);\n    function HashMap() {\n      this.map = {};\n      this.keys = [];\n    }\n    HashMap.prototype.put = function (key, value) {\n      var theId = UniqueIDGeneretor.createID(key);\n      if (!this.contains(theId)) {\n        this.map[theId] = value;\n        this.keys.push(key);\n      }\n    };\n    HashMap.prototype.contains = function (key) {\n      var theId = UniqueIDGeneretor.createID(key);\n      return this.map[key] != null;\n    };\n    HashMap.prototype.get = function (key) {\n      var theId = UniqueIDGeneretor.createID(key);\n      return this.map[theId];\n    };\n    HashMap.prototype.keySet = function () {\n      return this.keys;\n    };\n    module.exports = HashMap;\n\n    /***/\n  }), (/* 23 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var UniqueIDGeneretor = __webpack_require__(14);\n    function HashSet() {\n      this.set = {};\n    }\n    ;\n    HashSet.prototype.add = function (obj) {\n      var theId = UniqueIDGeneretor.createID(obj);\n      if (!this.contains(theId)) this.set[theId] = obj;\n    };\n    HashSet.prototype.remove = function (obj) {\n      delete this.set[UniqueIDGeneretor.createID(obj)];\n    };\n    HashSet.prototype.clear = function () {\n      this.set = {};\n    };\n    HashSet.prototype.contains = function (obj) {\n      return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n    };\n    HashSet.prototype.isEmpty = function () {\n      return this.size() === 0;\n    };\n    HashSet.prototype.size = function () {\n      return Object.keys(this.set).length;\n    };\n\n    //concats this.set to the given list\n    HashSet.prototype.addAllTo = function (list) {\n      var keys = Object.keys(this.set);\n      var length = keys.length;\n      for (var i = 0; i < length; i++) {\n        list.push(this.set[keys[i]]);\n      }\n    };\n    HashSet.prototype.size = function () {\n      return Object.keys(this.set).length;\n    };\n    HashSet.prototype.addAll = function (list) {\n      var s = list.length;\n      for (var i = 0; i < s; i++) {\n        var v = list[i];\n        this.add(v);\n      }\n    };\n    module.exports = HashSet;\n\n    /***/\n  }), (/* 24 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    // Some matrix (1d and 2d array) operations\n    function Matrix() {}\n\n    /**\n     * matrix multiplication\n     * array1, array2 and result are 2d arrays\n     */\n    Matrix.multMat = function (array1, array2) {\n      var result = [];\n      for (var i = 0; i < array1.length; i++) {\n        result[i] = [];\n        for (var j = 0; j < array2[0].length; j++) {\n          result[i][j] = 0;\n          for (var k = 0; k < array1[0].length; k++) {\n            result[i][j] += array1[i][k] * array2[k][j];\n          }\n        }\n      }\n      return result;\n    };\n\n    /**\n     * matrix transpose\n     * array and result are 2d arrays\n     */\n    Matrix.transpose = function (array) {\n      var result = [];\n      for (var i = 0; i < array[0].length; i++) {\n        result[i] = [];\n        for (var j = 0; j < array.length; j++) {\n          result[i][j] = array[j][i];\n        }\n      }\n      return result;\n    };\n\n    /**\n     * multiply array with constant\n     * array and result are 1d arrays\n     */\n    Matrix.multCons = function (array, constant) {\n      var result = [];\n      for (var i = 0; i < array.length; i++) {\n        result[i] = array[i] * constant;\n      }\n      return result;\n    };\n\n    /**\n     * substract two arrays\n     * array1, array2 and result are 1d arrays\n     */\n    Matrix.minusOp = function (array1, array2) {\n      var result = [];\n      for (var i = 0; i < array1.length; i++) {\n        result[i] = array1[i] - array2[i];\n      }\n      return result;\n    };\n\n    /**\n     * dot product of two arrays with same size\n     * array1 and array2 are 1d arrays\n     */\n    Matrix.dotProduct = function (array1, array2) {\n      var product = 0;\n      for (var i = 0; i < array1.length; i++) {\n        product += array1[i] * array2[i];\n      }\n      return product;\n    };\n\n    /**\n     * magnitude of an array\n     * array is 1d array\n     */\n    Matrix.mag = function (array) {\n      return Math.sqrt(this.dotProduct(array, array));\n    };\n\n    /**\n     * normalization of an array\n     * array and result are 1d array\n     */\n    Matrix.normalize = function (array) {\n      var result = [];\n      var magnitude = this.mag(array);\n      for (var i = 0; i < array.length; i++) {\n        result[i] = array[i] / magnitude;\n      }\n      return result;\n    };\n\n    /**\n     * multiply an array with centering matrix\n     * array and result are 1d array\n     */\n    Matrix.multGamma = function (array) {\n      var result = [];\n      var sum = 0;\n      for (var i = 0; i < array.length; i++) {\n        sum += array[i];\n      }\n      sum *= -1 / array.length;\n      for (var _i = 0; _i < array.length; _i++) {\n        result[_i] = sum + array[_i];\n      }\n      return result;\n    };\n\n    /**\n     * a special matrix multiplication\n     * result = 0.5 * C * INV * C^T * array\n     * array and result are 1d, C and INV are 2d arrays\n     */\n    Matrix.multL = function (array, C, INV) {\n      var result = [];\n      var temp1 = [];\n      var temp2 = [];\n\n      // multiply by C^T\n      for (var i = 0; i < C[0].length; i++) {\n        var sum = 0;\n        for (var j = 0; j < C.length; j++) {\n          sum += -0.5 * C[j][i] * array[j];\n        }\n        temp1[i] = sum;\n      }\n      // multiply the result by INV\n      for (var _i2 = 0; _i2 < INV.length; _i2++) {\n        var _sum = 0;\n        for (var _j = 0; _j < INV.length; _j++) {\n          _sum += INV[_i2][_j] * temp1[_j];\n        }\n        temp2[_i2] = _sum;\n      }\n      // multiply the result by C\n      for (var _i3 = 0; _i3 < C.length; _i3++) {\n        var _sum2 = 0;\n        for (var _j2 = 0; _j2 < C[0].length; _j2++) {\n          _sum2 += C[_i3][_j2] * temp2[_j2];\n        }\n        result[_i3] = _sum2;\n      }\n      return result;\n    };\n    module.exports = Matrix;\n\n    /***/\n  }), (/* 25 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n\n    /**\n     * A classic Quicksort algorithm with Hoare's partition\n     * - Works also on LinkedList objects\n     *\n     * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n     */\n\n    var LinkedList = __webpack_require__(11);\n    var Quicksort = function () {\n      function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n        this._quicksort(A, 0, length - 1);\n      }\n      _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n          if (p < r) {\n            var q = this._partition(A, p, r);\n            this._quicksort(A, p, q);\n            this._quicksort(A, q + 1, r);\n          }\n        }\n      }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n          var x = this._get(A, p);\n          var i = p;\n          var j = r;\n          while (true) {\n            while (this.compareFunction(x, this._get(A, j))) {\n              j--;\n            }\n            while (this.compareFunction(this._get(A, i), x)) {\n              i++;\n            }\n            if (i < j) {\n              this._swap(A, i, j);\n              i++;\n              j--;\n            } else return j;\n          }\n        }\n      }, {\n        key: '_get',\n        value: function _get(object, index) {\n          if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n      }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n          if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n      }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n          var temp = this._get(A, i);\n          this._set(A, i, this._get(A, j));\n          this._set(A, j, temp);\n        }\n      }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n          return b > a;\n        }\n      }]);\n      return Quicksort;\n    }();\n    module.exports = Quicksort;\n\n    /***/\n  }), (/* 26 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    // Singular Value Decomposition implementation\n    function SVD() {}\n    ;\n\n    /* Below singular value decomposition (svd) code including hypot function is adopted from https://github.com/dragonfly-ai/JamaJS\n       Some changes are applied to make the code compatible with the fcose code and to make it independent from Jama.\n       Input matrix is changed to a 2D array instead of Jama matrix. Matrix dimensions are taken according to 2D array instead of using Jama functions.\n       An object that includes singular value components is created for return. \n       The types of input parameters of the hypot function are removed. \n       let is used instead of var for the variable initialization.\n    */\n    /*\n                                   Apache License\n                               Version 2.0, January 2004\n                            http://www.apache.org/licenses/\n    \n       TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n    \n       1. Definitions.\n    \n          \"License\" shall mean the terms and conditions for use, reproduction,\n          and distribution as defined by Sections 1 through 9 of this document.\n    \n          \"Licensor\" shall mean the copyright owner or entity authorized by\n          the copyright owner that is granting the License.\n    \n          \"Legal Entity\" shall mean the union of the acting entity and all\n          other entities that control, are controlled by, or are under common\n          control with that entity. For the purposes of this definition,\n          \"control\" means (i) the power, direct or indirect, to cause the\n          direction or management of such entity, whether by contract or\n          otherwise, or (ii) ownership of fifty percent (50%) or more of the\n          outstanding shares, or (iii) beneficial ownership of such entity.\n    \n          \"You\" (or \"Your\") shall mean an individual or Legal Entity\n          exercising permissions granted by this License.\n    \n          \"Source\" form shall mean the preferred form for making modifications,\n          including but not limited to software source code, documentation\n          source, and configuration files.\n    \n          \"Object\" form shall mean any form resulting from mechanical\n          transformation or translation of a Source form, including but\n          not limited to compiled object code, generated documentation,\n          and conversions to other media types.\n    \n          \"Work\" shall mean the work of authorship, whether in Source or\n          Object form, made available under the License, as indicated by a\n          copyright notice that is included in or attached to the work\n          (an example is provided in the Appendix below).\n    \n          \"Derivative Works\" shall mean any work, whether in Source or Object\n          form, that is based on (or derived from) the Work and for which the\n          editorial revisions, annotations, elaborations, or other modifications\n          represent, as a whole, an original work of authorship. For the purposes\n          of this License, Derivative Works shall not include works that remain\n          separable from, or merely link (or bind by name) to the interfaces of,\n          the Work and Derivative Works thereof.\n    \n          \"Contribution\" shall mean any work of authorship, including\n          the original version of the Work and any modifications or additions\n          to that Work or Derivative Works thereof, that is intentionally\n          submitted to Licensor for inclusion in the Work by the copyright owner\n          or by an individual or Legal Entity authorized to submit on behalf of\n          the copyright owner. For the purposes of this definition, \"submitted\"\n          means any form of electronic, verbal, or written communication sent\n          to the Licensor or its representatives, including but not limited to\n          communication on electronic mailing lists, source code control systems,\n          and issue tracking systems that are managed by, or on behalf of, the\n          Licensor for the purpose of discussing and improving the Work, but\n          excluding communication that is conspicuously marked or otherwise\n          designated in writing by the copyright owner as \"Not a Contribution.\"\n    \n          \"Contributor\" shall mean Licensor and any individual or Legal Entity\n          on behalf of whom a Contribution has been received by Licensor and\n          subsequently incorporated within the Work.\n    \n       2. Grant of Copyright License. Subject to the terms and conditions of\n          this License, each Contributor hereby grants to You a perpetual,\n          worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n          copyright license to reproduce, prepare Derivative Works of,\n          publicly display, publicly perform, sublicense, and distribute the\n          Work and such Derivative Works in Source or Object form.\n    \n       3. Grant of Patent License. Subject to the terms and conditions of\n          this License, each Contributor hereby grants to You a perpetual,\n          worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n          (except as stated in this section) patent license to make, have made,\n          use, offer to sell, sell, import, and otherwise transfer the Work,\n          where such license applies only to those patent claims licensable\n          by such Contributor that are necessarily infringed by their\n          Contribution(s) alone or by combination of their Contribution(s)\n          with the Work to which such Contribution(s) was submitted. If You\n          institute patent litigation against any entity (including a\n          cross-claim or counterclaim in a lawsuit) alleging that the Work\n          or a Contribution incorporated within the Work constitutes direct\n          or contributory patent infringement, then any patent licenses\n          granted to You under this License for that Work shall terminate\n          as of the date such litigation is filed.\n    \n       4. Redistribution. You may reproduce and distribute copies of the\n          Work or Derivative Works thereof in any medium, with or without\n          modifications, and in Source or Object form, provided that You\n          meet the following conditions:\n    \n          (a) You must give any other recipients of the Work or\n              Derivative Works a copy of this License; and\n    \n          (b) You must cause any modified files to carry prominent notices\n              stating that You changed the files; and\n    \n          (c) You must retain, in the Source form of any Derivative Works\n              that You distribute, all copyright, patent, trademark, and\n              attribution notices from the Source form of the Work,\n              excluding those notices that do not pertain to any part of\n              the Derivative Works; and\n    \n          (d) If the Work includes a \"NOTICE\" text file as part of its\n              distribution, then any Derivative Works that You distribute must\n              include a readable copy of the attribution notices contained\n              within such NOTICE file, excluding those notices that do not\n              pertain to any part of the Derivative Works, in at least one\n              of the following places: within a NOTICE text file distributed\n              as part of the Derivative Works; within the Source form or\n              documentation, if provided along with the Derivative Works; or,\n              within a display generated by the Derivative Works, if and\n              wherever such third-party notices normally appear. The contents\n              of the NOTICE file are for informational purposes only and\n              do not modify the License. You may add Your own attribution\n              notices within Derivative Works that You distribute, alongside\n              or as an addendum to the NOTICE text from the Work, provided\n              that such additional attribution notices cannot be construed\n              as modifying the License.\n    \n          You may add Your own copyright statement to Your modifications and\n          may provide additional or different license terms and conditions\n          for use, reproduction, or distribution of Your modifications, or\n          for any such Derivative Works as a whole, provided Your use,\n          reproduction, and distribution of the Work otherwise complies with\n          the conditions stated in this License.\n    \n       5. Submission of Contributions. Unless You explicitly state otherwise,\n          any Contribution intentionally submitted for inclusion in the Work\n          by You to the Licensor shall be under the terms and conditions of\n          this License, without any additional terms or conditions.\n          Notwithstanding the above, nothing herein shall supersede or modify\n          the terms of any separate license agreement you may have executed\n          with Licensor regarding such Contributions.\n    \n       6. Trademarks. This License does not grant permission to use the trade\n          names, trademarks, service marks, or product names of the Licensor,\n          except as required for reasonable and customary use in describing the\n          origin of the Work and reproducing the content of the NOTICE file.\n    \n       7. Disclaimer of Warranty. Unless required by applicable law or\n          agreed to in writing, Licensor provides the Work (and each\n          Contributor provides its Contributions) on an \"AS IS\" BASIS,\n          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n          implied, including, without limitation, any warranties or conditions\n          of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n          PARTICULAR PURPOSE. You are solely responsible for determining the\n          appropriateness of using or redistributing the Work and assume any\n          risks associated with Your exercise of permissions under this License.\n    \n       8. Limitation of Liability. In no event and under no legal theory,\n          whether in tort (including negligence), contract, or otherwise,\n          unless required by applicable law (such as deliberate and grossly\n          negligent acts) or agreed to in writing, shall any Contributor be\n          liable to You for damages, including any direct, indirect, special,\n          incidental, or consequential damages of any character arising as a\n          result of this License or out of the use or inability to use the\n          Work (including but not limited to damages for loss of goodwill,\n          work stoppage, computer failure or malfunction, or any and all\n          other commercial damages or losses), even if such Contributor\n          has been advised of the possibility of such damages.\n    \n       9. Accepting Warranty or Additional Liability. While redistributing\n          the Work or Derivative Works thereof, You may choose to offer,\n          and charge a fee for, acceptance of support, warranty, indemnity,\n          or other liability obligations and/or rights consistent with this\n          License. However, in accepting such obligations, You may act only\n          on Your own behalf and on Your sole responsibility, not on behalf\n          of any other Contributor, and only if You agree to indemnify,\n          defend, and hold each Contributor harmless for any liability\n          incurred by, or claims asserted against, such Contributor by reason\n          of your accepting any such warranty or additional liability.\n    \n       END OF TERMS AND CONDITIONS\n    \n       APPENDIX: How to apply the Apache License to your work.\n    \n          To apply the Apache License to your work, attach the following\n          boilerplate notice, with the fields enclosed by brackets \"{}\"\n          replaced with your own identifying information. (Don't include\n          the brackets!)  The text should be enclosed in the appropriate\n          comment syntax for the file format. We also recommend that a\n          file or class name and description of purpose be included on the\n          same \"printed page\" as the copyright notice for easier\n          identification within third-party archives.\n    \n       Copyright {yyyy} {name of copyright owner}\n    \n       Licensed under the Apache License, Version 2.0 (the \"License\");\n       you may not use this file except in compliance with the License.\n       You may obtain a copy of the License at\n    \n           http://www.apache.org/licenses/LICENSE-2.0\n    \n       Unless required by applicable law or agreed to in writing, software\n       distributed under the License is distributed on an \"AS IS\" BASIS,\n       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n       See the License for the specific language governing permissions and\n       limitations under the License.\n    */\n\n    SVD.svd = function (A) {\n      this.U = null;\n      this.V = null;\n      this.s = null;\n      this.m = 0;\n      this.n = 0;\n      this.m = A.length;\n      this.n = A[0].length;\n      var nu = Math.min(this.m, this.n);\n      this.s = function (s) {\n        var a = [];\n        while (s-- > 0) {\n          a.push(0);\n        }\n        return a;\n      }(Math.min(this.m + 1, this.n));\n      this.U = function (dims) {\n        var allocate = function allocate(dims) {\n          if (dims.length == 0) {\n            return 0;\n          } else {\n            var array = [];\n            for (var i = 0; i < dims[0]; i++) {\n              array.push(allocate(dims.slice(1)));\n            }\n            return array;\n          }\n        };\n        return allocate(dims);\n      }([this.m, nu]);\n      this.V = function (dims) {\n        var allocate = function allocate(dims) {\n          if (dims.length == 0) {\n            return 0;\n          } else {\n            var array = [];\n            for (var i = 0; i < dims[0]; i++) {\n              array.push(allocate(dims.slice(1)));\n            }\n            return array;\n          }\n        };\n        return allocate(dims);\n      }([this.n, this.n]);\n      var e = function (s) {\n        var a = [];\n        while (s-- > 0) {\n          a.push(0);\n        }\n        return a;\n      }(this.n);\n      var work = function (s) {\n        var a = [];\n        while (s-- > 0) {\n          a.push(0);\n        }\n        return a;\n      }(this.m);\n      var wantu = true;\n      var wantv = true;\n      var nct = Math.min(this.m - 1, this.n);\n      var nrt = Math.max(0, Math.min(this.n - 2, this.m));\n      for (var k = 0; k < Math.max(nct, nrt); k++) {\n        if (k < nct) {\n          this.s[k] = 0;\n          for (var i = k; i < this.m; i++) {\n            this.s[k] = SVD.hypot(this.s[k], A[i][k]);\n          }\n          ;\n          if (this.s[k] !== 0.0) {\n            if (A[k][k] < 0.0) {\n              this.s[k] = -this.s[k];\n            }\n            for (var _i = k; _i < this.m; _i++) {\n              A[_i][k] /= this.s[k];\n            }\n            ;\n            A[k][k] += 1.0;\n          }\n          this.s[k] = -this.s[k];\n        }\n        for (var j = k + 1; j < this.n; j++) {\n          if (function (lhs, rhs) {\n            return lhs && rhs;\n          }(k < nct, this.s[k] !== 0.0)) {\n            var t = 0;\n            for (var _i2 = k; _i2 < this.m; _i2++) {\n              t += A[_i2][k] * A[_i2][j];\n            }\n            ;\n            t = -t / A[k][k];\n            for (var _i3 = k; _i3 < this.m; _i3++) {\n              A[_i3][j] += t * A[_i3][k];\n            }\n            ;\n          }\n          e[j] = A[k][j];\n        }\n        ;\n        if (function (lhs, rhs) {\n          return lhs && rhs;\n        }(wantu, k < nct)) {\n          for (var _i4 = k; _i4 < this.m; _i4++) {\n            this.U[_i4][k] = A[_i4][k];\n          }\n          ;\n        }\n        if (k < nrt) {\n          e[k] = 0;\n          for (var _i5 = k + 1; _i5 < this.n; _i5++) {\n            e[k] = SVD.hypot(e[k], e[_i5]);\n          }\n          ;\n          if (e[k] !== 0.0) {\n            if (e[k + 1] < 0.0) {\n              e[k] = -e[k];\n            }\n            for (var _i6 = k + 1; _i6 < this.n; _i6++) {\n              e[_i6] /= e[k];\n            }\n            ;\n            e[k + 1] += 1.0;\n          }\n          e[k] = -e[k];\n          if (function (lhs, rhs) {\n            return lhs && rhs;\n          }(k + 1 < this.m, e[k] !== 0.0)) {\n            for (var _i7 = k + 1; _i7 < this.m; _i7++) {\n              work[_i7] = 0.0;\n            }\n            ;\n            for (var _j = k + 1; _j < this.n; _j++) {\n              for (var _i8 = k + 1; _i8 < this.m; _i8++) {\n                work[_i8] += e[_j] * A[_i8][_j];\n              }\n              ;\n            }\n            ;\n            for (var _j2 = k + 1; _j2 < this.n; _j2++) {\n              var _t = -e[_j2] / e[k + 1];\n              for (var _i9 = k + 1; _i9 < this.m; _i9++) {\n                A[_i9][_j2] += _t * work[_i9];\n              }\n              ;\n            }\n            ;\n          }\n          if (wantv) {\n            for (var _i10 = k + 1; _i10 < this.n; _i10++) {\n              this.V[_i10][k] = e[_i10];\n            }\n            ;\n          }\n        }\n      }\n      ;\n      var p = Math.min(this.n, this.m + 1);\n      if (nct < this.n) {\n        this.s[nct] = A[nct][nct];\n      }\n      if (this.m < p) {\n        this.s[p - 1] = 0.0;\n      }\n      if (nrt + 1 < p) {\n        e[nrt] = A[nrt][p - 1];\n      }\n      e[p - 1] = 0.0;\n      if (wantu) {\n        for (var _j3 = nct; _j3 < nu; _j3++) {\n          for (var _i11 = 0; _i11 < this.m; _i11++) {\n            this.U[_i11][_j3] = 0.0;\n          }\n          ;\n          this.U[_j3][_j3] = 1.0;\n        }\n        ;\n        for (var _k = nct - 1; _k >= 0; _k--) {\n          if (this.s[_k] !== 0.0) {\n            for (var _j4 = _k + 1; _j4 < nu; _j4++) {\n              var _t2 = 0;\n              for (var _i12 = _k; _i12 < this.m; _i12++) {\n                _t2 += this.U[_i12][_k] * this.U[_i12][_j4];\n              }\n              ;\n              _t2 = -_t2 / this.U[_k][_k];\n              for (var _i13 = _k; _i13 < this.m; _i13++) {\n                this.U[_i13][_j4] += _t2 * this.U[_i13][_k];\n              }\n              ;\n            }\n            ;\n            for (var _i14 = _k; _i14 < this.m; _i14++) {\n              this.U[_i14][_k] = -this.U[_i14][_k];\n            }\n            ;\n            this.U[_k][_k] = 1.0 + this.U[_k][_k];\n            for (var _i15 = 0; _i15 < _k - 1; _i15++) {\n              this.U[_i15][_k] = 0.0;\n            }\n            ;\n          } else {\n            for (var _i16 = 0; _i16 < this.m; _i16++) {\n              this.U[_i16][_k] = 0.0;\n            }\n            ;\n            this.U[_k][_k] = 1.0;\n          }\n        }\n        ;\n      }\n      if (wantv) {\n        for (var _k2 = this.n - 1; _k2 >= 0; _k2--) {\n          if (function (lhs, rhs) {\n            return lhs && rhs;\n          }(_k2 < nrt, e[_k2] !== 0.0)) {\n            for (var _j5 = _k2 + 1; _j5 < nu; _j5++) {\n              var _t3 = 0;\n              for (var _i17 = _k2 + 1; _i17 < this.n; _i17++) {\n                _t3 += this.V[_i17][_k2] * this.V[_i17][_j5];\n              }\n              ;\n              _t3 = -_t3 / this.V[_k2 + 1][_k2];\n              for (var _i18 = _k2 + 1; _i18 < this.n; _i18++) {\n                this.V[_i18][_j5] += _t3 * this.V[_i18][_k2];\n              }\n              ;\n            }\n            ;\n          }\n          for (var _i19 = 0; _i19 < this.n; _i19++) {\n            this.V[_i19][_k2] = 0.0;\n          }\n          ;\n          this.V[_k2][_k2] = 1.0;\n        }\n        ;\n      }\n      var pp = p - 1;\n      var iter = 0;\n      var eps = Math.pow(2.0, -52.0);\n      var tiny = Math.pow(2.0, -966.0);\n      while (p > 0) {\n        var _k3 = void 0;\n        var kase = void 0;\n        for (_k3 = p - 2; _k3 >= -1; _k3--) {\n          if (_k3 === -1) {\n            break;\n          }\n          if (Math.abs(e[_k3]) <= tiny + eps * (Math.abs(this.s[_k3]) + Math.abs(this.s[_k3 + 1]))) {\n            e[_k3] = 0.0;\n            break;\n          }\n        }\n        ;\n        if (_k3 === p - 2) {\n          kase = 4;\n        } else {\n          var ks = void 0;\n          for (ks = p - 1; ks >= _k3; ks--) {\n            if (ks === _k3) {\n              break;\n            }\n            var _t4 = (ks !== p ? Math.abs(e[ks]) : 0.0) + (ks !== _k3 + 1 ? Math.abs(e[ks - 1]) : 0.0);\n            if (Math.abs(this.s[ks]) <= tiny + eps * _t4) {\n              this.s[ks] = 0.0;\n              break;\n            }\n          }\n          ;\n          if (ks === _k3) {\n            kase = 3;\n          } else if (ks === p - 1) {\n            kase = 1;\n          } else {\n            kase = 2;\n            _k3 = ks;\n          }\n        }\n        _k3++;\n        switch (kase) {\n          case 1:\n            {\n              var f = e[p - 2];\n              e[p - 2] = 0.0;\n              for (var _j6 = p - 2; _j6 >= _k3; _j6--) {\n                var _t5 = SVD.hypot(this.s[_j6], f);\n                var cs = this.s[_j6] / _t5;\n                var sn = f / _t5;\n                this.s[_j6] = _t5;\n                if (_j6 !== _k3) {\n                  f = -sn * e[_j6 - 1];\n                  e[_j6 - 1] = cs * e[_j6 - 1];\n                }\n                if (wantv) {\n                  for (var _i20 = 0; _i20 < this.n; _i20++) {\n                    _t5 = cs * this.V[_i20][_j6] + sn * this.V[_i20][p - 1];\n                    this.V[_i20][p - 1] = -sn * this.V[_i20][_j6] + cs * this.V[_i20][p - 1];\n                    this.V[_i20][_j6] = _t5;\n                  }\n                  ;\n                }\n              }\n              ;\n            }\n            ;\n            break;\n          case 2:\n            {\n              var _f = e[_k3 - 1];\n              e[_k3 - 1] = 0.0;\n              for (var _j7 = _k3; _j7 < p; _j7++) {\n                var _t6 = SVD.hypot(this.s[_j7], _f);\n                var _cs = this.s[_j7] / _t6;\n                var _sn = _f / _t6;\n                this.s[_j7] = _t6;\n                _f = -_sn * e[_j7];\n                e[_j7] = _cs * e[_j7];\n                if (wantu) {\n                  for (var _i21 = 0; _i21 < this.m; _i21++) {\n                    _t6 = _cs * this.U[_i21][_j7] + _sn * this.U[_i21][_k3 - 1];\n                    this.U[_i21][_k3 - 1] = -_sn * this.U[_i21][_j7] + _cs * this.U[_i21][_k3 - 1];\n                    this.U[_i21][_j7] = _t6;\n                  }\n                  ;\n                }\n              }\n              ;\n            }\n            ;\n            break;\n          case 3:\n            {\n              var scale = Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[p - 1]), Math.abs(this.s[p - 2])), Math.abs(e[p - 2])), Math.abs(this.s[_k3])), Math.abs(e[_k3]));\n              var sp = this.s[p - 1] / scale;\n              var spm1 = this.s[p - 2] / scale;\n              var epm1 = e[p - 2] / scale;\n              var sk = this.s[_k3] / scale;\n              var ek = e[_k3] / scale;\n              var b = ((spm1 + sp) * (spm1 - sp) + epm1 * epm1) / 2.0;\n              var c = sp * epm1 * (sp * epm1);\n              var shift = 0.0;\n              if (function (lhs, rhs) {\n                return lhs || rhs;\n              }(b !== 0.0, c !== 0.0)) {\n                shift = Math.sqrt(b * b + c);\n                if (b < 0.0) {\n                  shift = -shift;\n                }\n                shift = c / (b + shift);\n              }\n              var _f2 = (sk + sp) * (sk - sp) + shift;\n              var g = sk * ek;\n              for (var _j8 = _k3; _j8 < p - 1; _j8++) {\n                var _t7 = SVD.hypot(_f2, g);\n                var _cs2 = _f2 / _t7;\n                var _sn2 = g / _t7;\n                if (_j8 !== _k3) {\n                  e[_j8 - 1] = _t7;\n                }\n                _f2 = _cs2 * this.s[_j8] + _sn2 * e[_j8];\n                e[_j8] = _cs2 * e[_j8] - _sn2 * this.s[_j8];\n                g = _sn2 * this.s[_j8 + 1];\n                this.s[_j8 + 1] = _cs2 * this.s[_j8 + 1];\n                if (wantv) {\n                  for (var _i22 = 0; _i22 < this.n; _i22++) {\n                    _t7 = _cs2 * this.V[_i22][_j8] + _sn2 * this.V[_i22][_j8 + 1];\n                    this.V[_i22][_j8 + 1] = -_sn2 * this.V[_i22][_j8] + _cs2 * this.V[_i22][_j8 + 1];\n                    this.V[_i22][_j8] = _t7;\n                  }\n                  ;\n                }\n                _t7 = SVD.hypot(_f2, g);\n                _cs2 = _f2 / _t7;\n                _sn2 = g / _t7;\n                this.s[_j8] = _t7;\n                _f2 = _cs2 * e[_j8] + _sn2 * this.s[_j8 + 1];\n                this.s[_j8 + 1] = -_sn2 * e[_j8] + _cs2 * this.s[_j8 + 1];\n                g = _sn2 * e[_j8 + 1];\n                e[_j8 + 1] = _cs2 * e[_j8 + 1];\n                if (wantu && _j8 < this.m - 1) {\n                  for (var _i23 = 0; _i23 < this.m; _i23++) {\n                    _t7 = _cs2 * this.U[_i23][_j8] + _sn2 * this.U[_i23][_j8 + 1];\n                    this.U[_i23][_j8 + 1] = -_sn2 * this.U[_i23][_j8] + _cs2 * this.U[_i23][_j8 + 1];\n                    this.U[_i23][_j8] = _t7;\n                  }\n                  ;\n                }\n              }\n              ;\n              e[p - 2] = _f2;\n              iter = iter + 1;\n            }\n            ;\n            break;\n          case 4:\n            {\n              if (this.s[_k3] <= 0.0) {\n                this.s[_k3] = this.s[_k3] < 0.0 ? -this.s[_k3] : 0.0;\n                if (wantv) {\n                  for (var _i24 = 0; _i24 <= pp; _i24++) {\n                    this.V[_i24][_k3] = -this.V[_i24][_k3];\n                  }\n                  ;\n                }\n              }\n              while (_k3 < pp) {\n                if (this.s[_k3] >= this.s[_k3 + 1]) {\n                  break;\n                }\n                var _t8 = this.s[_k3];\n                this.s[_k3] = this.s[_k3 + 1];\n                this.s[_k3 + 1] = _t8;\n                if (wantv && _k3 < this.n - 1) {\n                  for (var _i25 = 0; _i25 < this.n; _i25++) {\n                    _t8 = this.V[_i25][_k3 + 1];\n                    this.V[_i25][_k3 + 1] = this.V[_i25][_k3];\n                    this.V[_i25][_k3] = _t8;\n                  }\n                  ;\n                }\n                if (wantu && _k3 < this.m - 1) {\n                  for (var _i26 = 0; _i26 < this.m; _i26++) {\n                    _t8 = this.U[_i26][_k3 + 1];\n                    this.U[_i26][_k3 + 1] = this.U[_i26][_k3];\n                    this.U[_i26][_k3] = _t8;\n                  }\n                  ;\n                }\n                _k3++;\n              }\n              ;\n              iter = 0;\n              p--;\n            }\n            ;\n            break;\n        }\n      }\n      ;\n      var result = {\n        U: this.U,\n        V: this.V,\n        S: this.s\n      };\n      return result;\n    };\n\n    // sqrt(a^2 + b^2) without under/overflow.\n    SVD.hypot = function (a, b) {\n      var r = void 0;\n      if (Math.abs(a) > Math.abs(b)) {\n        r = b / a;\n        r = Math.abs(a) * Math.sqrt(1 + r * r);\n      } else if (b != 0) {\n        r = a / b;\n        r = Math.abs(b) * Math.sqrt(1 + r * r);\n      } else {\n        r = 0.0;\n      }\n      return r;\n    };\n    module.exports = SVD;\n\n    /***/\n  }), (/* 27 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var _createClass = function () {\n      function defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n      return function (Constructor, protoProps, staticProps) {\n        if (protoProps) defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) defineProperties(Constructor, staticProps);\n        return Constructor;\n      };\n    }();\n    function _classCallCheck(instance, Constructor) {\n      if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n      }\n    }\n\n    /**\n     *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n     *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n     *\n     *   Aside from the inputs, you can assign the scores for,\n     *   - Match: The two characters at the current index are same.\n     *   - Mismatch: The two characters at the current index are different.\n     *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n     */\n\n    var NeedlemanWunsch = function () {\n      function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n        _classCallCheck(this, NeedlemanWunsch);\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n          this.grid[i] = new Array(this.jMax);\n          for (var j = 0; j < this.jMax; j++) {\n            this.grid[i][j] = 0;\n          }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n          this.tracebackGrid[_i] = new Array(this.jMax);\n          for (var _j = 0; _j < this.jMax; _j++) {\n            this.tracebackGrid[_i][_j] = [null, null, null];\n          }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n      }\n      _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n          return this.score;\n        }\n      }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n          return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n      }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n          // Fill in the first row\n          for (var j = 1; j < this.jMax; j++) {\n            this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n            this.tracebackGrid[0][j] = [false, false, true];\n          }\n\n          // Fill in the first column\n          for (var i = 1; i < this.iMax; i++) {\n            this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n            this.tracebackGrid[i][0] = [false, true, false];\n          }\n\n          // Fill the rest of the grid\n          for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n            for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n              // Find the max score(s) among [`Diag`, `Up`, `Left`]\n              var diag = void 0;\n              if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n              var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n              var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n              // If there exists multiple max values, capture them for multiple paths\n              var maxOf = [diag, up, left];\n              var indices = this.arrayAllMaxIndexes(maxOf);\n\n              // Update Grids\n              this.grid[_i2][_j2] = maxOf[indices[0]];\n              this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n            }\n          }\n\n          // Update alignment score\n          this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n      }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n          var inProcessAlignments = [];\n          inProcessAlignments.push({\n            pos: [this.sequence1.length, this.sequence2.length],\n            seq1: \"\",\n            seq2: \"\"\n          });\n          while (inProcessAlignments[0]) {\n            var current = inProcessAlignments[0];\n            var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n            if (directions[0]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0] - 1, current.pos[1] - 1],\n                seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n              });\n            }\n            if (directions[1]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0] - 1, current.pos[1]],\n                seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                seq2: '-' + current.seq2\n              });\n            }\n            if (directions[2]) {\n              inProcessAlignments.push({\n                pos: [current.pos[0], current.pos[1] - 1],\n                seq1: '-' + current.seq1,\n                seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n              });\n            }\n            if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({\n              sequence1: current.seq1,\n              sequence2: current.seq2\n            });\n            inProcessAlignments.shift();\n          }\n          return this.alignments;\n        }\n\n        // Helper Functions\n      }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n          var indexes = [],\n            i = -1;\n          while ((i = arr.indexOf(val, i + 1)) !== -1) {\n            indexes.push(i);\n          }\n          return indexes;\n        }\n      }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n          return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n      }]);\n      return NeedlemanWunsch;\n    }();\n    module.exports = NeedlemanWunsch;\n\n    /***/\n  }), (/* 28 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    var layoutBase = function layoutBase() {\n      return;\n    };\n    layoutBase.FDLayout = __webpack_require__(18);\n    layoutBase.FDLayoutConstants = __webpack_require__(4);\n    layoutBase.FDLayoutEdge = __webpack_require__(19);\n    layoutBase.FDLayoutNode = __webpack_require__(20);\n    layoutBase.DimensionD = __webpack_require__(21);\n    layoutBase.HashMap = __webpack_require__(22);\n    layoutBase.HashSet = __webpack_require__(23);\n    layoutBase.IGeometry = __webpack_require__(8);\n    layoutBase.IMath = __webpack_require__(9);\n    layoutBase.Integer = __webpack_require__(10);\n    layoutBase.Point = __webpack_require__(12);\n    layoutBase.PointD = __webpack_require__(5);\n    layoutBase.RandomSeed = __webpack_require__(16);\n    layoutBase.RectangleD = __webpack_require__(13);\n    layoutBase.Transform = __webpack_require__(17);\n    layoutBase.UniqueIDGeneretor = __webpack_require__(14);\n    layoutBase.Quicksort = __webpack_require__(25);\n    layoutBase.LinkedList = __webpack_require__(11);\n    layoutBase.LGraphObject = __webpack_require__(2);\n    layoutBase.LGraph = __webpack_require__(6);\n    layoutBase.LEdge = __webpack_require__(1);\n    layoutBase.LGraphManager = __webpack_require__(7);\n    layoutBase.LNode = __webpack_require__(3);\n    layoutBase.Layout = __webpack_require__(15);\n    layoutBase.LayoutConstants = __webpack_require__(0);\n    layoutBase.NeedlemanWunsch = __webpack_require__(27);\n    layoutBase.Matrix = __webpack_require__(24);\n    layoutBase.SVD = __webpack_require__(26);\n    module.exports = layoutBase;\n\n    /***/\n  }), (/* 29 */\n  /***/function (module, exports, __webpack_require__) {\n    \"use strict\";\n\n    function Emitter() {\n      this.listeners = [];\n    }\n    var p = Emitter.prototype;\n    p.addListener = function (event, callback) {\n      this.listeners.push({\n        event: event,\n        callback: callback\n      });\n    };\n    p.removeListener = function (event, callback) {\n      for (var i = this.listeners.length; i >= 0; i--) {\n        var l = this.listeners[i];\n        if (l.event === event && l.callback === callback) {\n          this.listeners.splice(i, 1);\n        }\n      }\n    };\n    p.emit = function (event, data) {\n      for (var i = 0; i < this.listeners.length; i++) {\n        var l = this.listeners[i];\n        if (event === l.event) {\n          l.callback(data);\n        }\n      }\n    };\n    module.exports = Emitter;\n\n    /***/\n  }\n  /******/)]);\n});", "map": {"version": 3, "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "define", "amd", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "value", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "LayoutConstants", "QUALITY", "DEFAULT_CREATE_BENDS_AS_NEEDED", "DEFAULT_INCREMENTAL", "DEFAULT_ANIMATION_ON_LAYOUT", "DEFAULT_ANIMATION_DURING_LAYOUT", "DEFAULT_ANIMATION_PERIOD", "DEFAULT_UNIFORM_LEAF_NODE_SIZES", "DEFAULT_GRAPH_MARGIN", "NODE_DIMENSIONS_INCLUDE_LABELS", "SIMPLE_NODE_SIZE", "SIMPLE_NODE_HALF_SIZE", "EMPTY_COMPOUND_NODE_SIZE", "MIN_EDGE_LENGTH", "WORLD_BOUNDARY", "INITIAL_WORLD_BOUNDARY", "WORLD_CENTER_X", "WORLD_CENTER_Y", "LGraphObject", "IGeometry", "IMath", "<PERSON><PERSON><PERSON>", "source", "target", "vEdge", "isOverlapingSourceAndTarget", "vGraphObject", "bendpoints", "create", "prop", "getSource", "get<PERSON><PERSON><PERSON>", "isInterGraph", "<PERSON><PERSON><PERSON><PERSON>", "length", "getBendpoints", "getLca", "lca", "getSourceInLca", "sourceInLca", "getTargetInLca", "targetInLca", "getOtherEnd", "node", "getOtherEndInGraph", "graph", "otherEnd", "getGraphManager", "getRoot", "get<PERSON>wner", "getParent", "updateLength", "clipPointCoordinates", "Array", "getIntersection", "getRect", "lengthX", "lengthY", "Math", "abs", "sign", "sqrt", "updateLengthSimple", "getCenterX", "getCenterY", "Integer", "RectangleD", "RandomSeed", "PointD", "LNode", "gm", "loc", "size", "vNode", "graphManager", "estimatedSize", "MIN_VALUE", "inclusion<PERSON><PERSON><PERSON><PERSON><PERSON>", "MAX_VALUE", "edges", "rect", "x", "y", "width", "height", "get<PERSON>dges", "<PERSON><PERSON><PERSON><PERSON>", "child", "owner", "getWidth", "<PERSON><PERSON><PERSON><PERSON>", "getHeight", "setHeight", "getCenter", "getLocation", "getDiagonal", "getHalfTheDiagonal", "setRect", "upperLeft", "dimension", "setCenter", "cx", "cy", "setLocation", "moveBy", "dx", "dy", "getEdgeListToNode", "to", "edgeList", "edge", "self", "for<PERSON>ach", "push", "getEdgesBetween", "other", "getNeighborsList", "neighbors", "Set", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withNeighborsList", "childNode", "children", "nodes", "getNodes", "getNoOfChildren", "noOf<PERSON><PERSON><PERSON><PERSON>", "getEstimatedSize", "calcEstimatedSize", "scatter", "randomCenterX", "randomCenterY", "minX", "maxX", "nextDouble", "minY", "maxY", "updateBounds", "childGraph", "getLeft", "getTop", "getRight", "getBottom", "labelWidth", "labelPosHorizontal", "labelHeight", "labelPosVertical", "getInclusionTreeDepth", "transform", "trans", "left", "top", "leftTop", "vLeftTop", "inverseTransformPoint", "FDLayoutConstants", "MAX_ITERATIONS", "DEFAULT_EDGE_LENGTH", "DEFAULT_SPRING_STRENGTH", "DEFAULT_REPULSION_STRENGTH", "DEFAULT_GRAVITY_STRENGTH", "DEFAULT_COMPOUND_GRAVITY_STRENGTH", "DEFAULT_GRAVITY_RANGE_FACTOR", "DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR", "DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION", "DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION", "DEFAULT_COOLING_FACTOR_INCREMENTAL", "COOLING_ADAPTATION_FACTOR", "ADAPTATION_LOWER_NODE_LIMIT", "ADAPTATION_UPPER_NODE_LIMIT", "MAX_NODE_DISPLACEMENT_INCREMENTAL", "MAX_NODE_DISPLACEMENT", "MIN_REPULSION_DIST", "CONVERGENCE_CHECK_PERIOD", "PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR", "GRID_CALCULATION_CHECK_PERIOD", "getX", "getY", "setX", "setY", "getDifference", "pt", "DimensionD", "getCopy", "translate", "dim", "LGraphManager", "Point", "LinkedList", "LGraph", "parent", "obj2", "vGraph", "margin", "isConnected", "Layout", "right", "bottom", "obj1", "sourceNode", "targetNode", "newNode", "indexOf", "newEdge", "remove", "obj", "edgesToBeRemoved", "slice", "index", "splice", "sourceIndex", "targetIndex", "updateLeftTop", "nodeTop", "nodeLeft", "lNode", "paddingLeft", "undefined", "recursive", "nodeRight", "nodeBottom", "boundingRect", "calculateBounds", "updateConnected", "queue", "visited", "currentNode", "neighborEdges", "currentNeighbor", "childrenOfNode", "shift", "neighborEdge", "has", "childrenOfNeighbor", "noOfVisitedInThisGraph", "visitedNode", "layout", "graphs", "addRoot", "ngraph", "newGraph", "nnode", "setRootGraph", "rootGraph", "parentNode", "sourceGraph", "targetGraph", "lObj", "concat", "nodesToBeRemoved", "getGraphs", "getAllNodes", "allNodes", "nodeList", "resetAllNodes", "resetAll<PERSON>dges", "allEdges", "resetAllNodesToApplyGravitation", "allNodesToApplyGravitation", "getAllEdges", "getAllNodesToApplyGravitation", "setAllNodesToApplyGravitation", "getLayout", "isOneAncestorOfOther", "firstNode", "secondNode", "ownerGraph", "calcLowestCommonAncestors", "sourceAncestorGraph", "targetAncestorGraph", "calcLowestCommonAncestor", "firstOwnerGraph", "secondOwnerGraph", "calcInclusionTreeDepths", "depth", "includesInvalidEdge", "edgesToRemove", "calcSeparationAmount", "rectA", "rectB", "overlapAmount", "<PERSON><PERSON><PERSON><PERSON>", "intersects", "directions", "decideDirectionsForOverlappingNodes", "min", "max", "slope", "moveByY", "moveByX", "getIntersection2", "result", "p1x", "p1y", "p2x", "p2y", "topLeftAx", "topLeftAy", "topRightAx", "bottomLeftAx", "bottomLeftAy", "bottomRightAx", "halfWidthA", "getWidthHalf", "halfHeightA", "getHeightHalf", "topLeftBx", "topLeftBy", "topRightBx", "bottomLeftBx", "bottomLeftBy", "bottomRightBx", "halfWidthB", "halfHeightB", "clipPointAFound", "clipPointBFound", "slopeA", "slopeB", "slopePrime", "cardinalDirectionA", "cardinalDirectionB", "tempPointAx", "tempPointAy", "tempPointBx", "tempPointBy", "getCardinalDirection", "line", "s1", "s2", "f1", "f2", "x1", "y1", "x2", "y2", "x3", "y3", "x4", "y4", "a1", "a2", "b1", "b2", "c1", "c2", "denom", "angleOfVector", "Cx", "Cy", "Nx", "Ny", "C_angle", "atan", "PI", "TWO_PI", "ONE_AND_HALF_PI", "HALF_PI", "doIntersect", "p1", "p2", "p3", "p4", "a", "b", "q", "r", "det", "lambda", "gamma", "findCircleLineIntersections", "Ex", "<PERSON><PERSON>", "Lx", "Ly", "disc", "t1", "t2", "intersections", "THREE_PI", "floor", "ceil", "_createClass", "defineProperties", "props", "descriptor", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "TypeError", "nodeFrom", "next", "prev", "list", "head", "tail", "_remove", "vals", "_this", "v", "insertBefore", "val", "otherNode", "insertAfter", "insertNodeBefore", "insertNodeAfter", "unshift", "pop", "popNode", "shiftNode", "get_object_at", "current", "set_object_at", "constructor", "parseInt", "move", "equals", "toString", "getMinX", "getMaxX", "getMinY", "getMaxY", "_typeof", "Symbol", "iterator", "UniqueIDGeneretor", "lastID", "createID", "isPrimitive", "uniqueID", "getString", "id", "arg", "type", "_toConsumableArray", "arr", "isArray", "arr2", "from", "Transform", "Emitter", "isRemoteUse", "layoutQuality", "createBendsAsNeeded", "incremental", "animationOnLayout", "animationDuringLayout", "animationPeriod", "uniformLeafNodeSizes", "edgeToDummyNodes", "Map", "isLayoutFinished", "isSubLayout", "RANDOM_SEED", "newGraphManager", "checkLayoutSuccess", "runLayout", "tilingPreLayout", "initParameters", "isLayoutSuccessfull", "ANIMATE", "doPostLayout", "tilingPostLayout", "update", "update2", "createBendpointsFromDummyNodes", "newLeftTop", "setWorldOrgX", "setWorldOrgY", "setDeviceOrgX", "setDeviceOrgY", "positionNodesRandomly", "getFlatForest", "flatForest", "isForest", "is<PERSON><PERSON>", "toBeVisited", "parents", "unProcessedNodes", "set", "temp", "createDummyNodesForBendpoints", "dummyNodes", "dummy<PERSON>ode", "Dimension", "dummy<PERSON><PERSON>", "keys", "k", "lEdge", "path", "ebp", "slider<PERSON><PERSON><PERSON>", "defaultValue", "minDiv", "maxMul", "minValue", "maxValue", "findCenterOfTree", "removedNodes", "remainingDegrees", "foundCenter", "centerNode", "degree", "tempList", "tempList2", "neighbours", "neighbour", "otherDegree", "newDegree", "setGraphManager", "seed", "sin", "lworldOrgX", "lworldOrgY", "ldeviceOrgX", "ldeviceOrgY", "lworldExtX", "lworldExtY", "ldeviceExtX", "ldeviceExtY", "getWorldOrgX", "wox", "getWorldOrgY", "woy", "getWorldExtX", "setWorldExtX", "wex", "getWorldExtY", "setWorldExtY", "wey", "getDeviceOrgX", "dox", "getDeviceOrgY", "doy", "getDeviceExtX", "setDeviceExtX", "dex", "getDeviceExtY", "setDeviceExtY", "dey", "transformX", "xDevice", "worldExtX", "transformY", "yDevice", "worldExtY", "inverseTransformX", "xWorld", "deviceExtX", "inverseTransformY", "yWorld", "deviceExtY", "inPoint", "outPoint", "FDLayout", "useSmartIdealEdgeLengthCalculation", "gravityConstant", "compoundGravityConstant", "gravityRangeFactor", "compoundGravityRangeFactor", "displacementThresholdPerNode", "coolingFactor", "initialCoolingFactor", "totalDisplacement", "oldTotalDisplacement", "maxIterations", "arguments", "totalIterations", "notAnimatedIterations", "useFRGridVariant", "grid", "calcIdealEdgeLengths", "originalIdealLength", "lcaDepth", "sizeOfSourceInLca", "sizeOfTargetInLca", "ideal<PERSON>ength", "initSpringEmbedder", "maxNodeDisplacement", "totalDisplacementThreshold", "repulsionRange", "calcRepulsionRange", "calcSpringForces", "l<PERSON><PERSON>", "calcSpringForce", "calcRepulsionForces", "gridUpdateAllowed", "forceToNodeSurroundingUpdate", "j", "nodeA", "nodeB", "lNodes", "processedNodeSet", "updateGrid", "calculateRepulsionForceOfANode", "calcRepulsionForce", "calcGravitationalForces", "calcGravitationalForce", "moveNodes", "springForce", "springForceX", "springForceY", "edgeElasticity", "clipPoints", "distanceX", "distanceY", "distanceSquared", "distance", "repulsionForce", "repulsionForceX", "repulsionForceY", "childrenConstant", "nodeRepulsion", "ownerCenterX", "ownerCenterY", "absDistanceX", "absDistanceY", "gravitationForceX", "gravitationForceY", "isConverged", "converged", "oscilating", "animate", "calcNoOfChildrenForAllNodes", "calcGrid", "sizeX", "sizeY", "addNodeToGrid", "startX", "finishX", "startY", "finishY", "setGridCoordinates", "surrounding", "FDLayoutEdge", "FDLayoutNode", "displacementX", "displacementY", "_startX", "_finishX", "_startY", "_finishY", "HashMap", "map", "put", "theId", "contains", "keySet", "HashSet", "clear", "isEmpty", "addAllTo", "addAll", "Matrix", "multMat", "array1", "array2", "transpose", "array", "multCons", "constant", "minusOp", "dotProduct", "product", "mag", "normalize", "magnitude", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sum", "_i", "multL", "C", "INV", "temp1", "temp2", "_i2", "_sum", "_j", "_i3", "_sum2", "_j2", "Quicksort", "A", "compareFunction", "_defaultCompareFunction", "_quicksort", "_partition", "_get", "_swap", "_set", "SVD", "svd", "U", "V", "nu", "dims", "allocate", "e", "work", "wantu", "wantv", "nct", "nrt", "hypot", "lhs", "rhs", "t", "_i4", "_i5", "_i6", "_i7", "_i8", "_t", "_i9", "_i10", "_j3", "_i11", "_k", "_j4", "_t2", "_i12", "_i13", "_i14", "_i15", "_i16", "_k2", "_j5", "_t3", "_i17", "_i18", "_i19", "pp", "iter", "eps", "pow", "tiny", "_k3", "kase", "ks", "_t4", "f", "_j6", "_t5", "cs", "sn", "_i20", "_f", "_j7", "_t6", "_cs", "_sn", "_i21", "scale", "sp", "spm1", "epm1", "sk", "ek", "_f2", "g", "_j8", "_t7", "_cs2", "_sn2", "_i22", "_i23", "_i24", "_t8", "_i25", "_i26", "S", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sequence1", "sequence2", "match_score", "mismatch_penalty", "gap_penalty", "iMax", "jMax", "tracebackGrid", "alignments", "score", "computeGrids", "getScore", "getAlignments", "diag", "up", "maxOf", "indices", "arrayAllMaxIndexes", "includes", "alignmentTraceback", "inProcessAlignments", "pos", "seq1", "seq2", "getAllIndexes", "indexes", "apply", "layoutBase", "listeners", "addListener", "event", "callback", "removeListener", "emit", "data"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/cytoscape-fcose/node_modules/layout-base/layout-base.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"layoutBase\"] = factory();\n\telse\n\t\troot[\"layoutBase\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 28);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar RectangleD = __webpack_require__(13);\nvar LayoutConstants = __webpack_require__(0);\nvar RandomSeed = __webpack_require__(16);\nvar PointD = __webpack_require__(5);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth) {\n        if (this.labelPosHorizontal == \"left\") {\n          this.rect.x -= this.labelWidth;\n          this.setWidth(width + this.labelWidth);\n        } else if (this.labelPosHorizontal == \"center\" && this.labelWidth > width) {\n          this.rect.x -= (this.labelWidth - width) / 2;\n          this.setWidth(this.labelWidth);\n        } else if (this.labelPosHorizontal == \"right\") {\n          this.setWidth(width + this.labelWidth);\n        }\n      }\n\n      if (this.labelHeight) {\n        if (this.labelPosVertical == \"top\") {\n          this.rect.y -= this.labelHeight;\n          this.setHeight(height + this.labelHeight);\n        } else if (this.labelPosVertical == \"center\" && this.labelHeight > height) {\n          this.rect.y -= (this.labelHeight - height) / 2;\n          this.setHeight(this.labelHeight);\n        } else if (this.labelPosVertical == \"bottom\") {\n          this.setHeight(height + this.labelHeight);\n        }\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(7);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar RectangleD = __webpack_require__(13);\nvar Point = __webpack_require__(12);\nvar LinkedList = __webpack_require__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __webpack_require__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __webpack_require__(6); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n  var edgesToRemove = [];\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      edgesToRemove.push(edge);\n    }\n  }\n\n  // Remove invalid edges from graph manager\n  for (var i = 0; i < edgesToRemove.length; i++) {\n    this.remove(edgesToRemove[i]);\n  }\n\n  // Invalid edges are cleared, so return false\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __webpack_require__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n/**\n * This method checks and calculates the intersection of \n * a line segment and a circle.\n */\nIGeometry.findCircleLineIntersections = function (Ex, Ey, Lx, Ly, Cx, Cy, r) {\n\n  // E is the starting point of the ray,\n  // L is the end point of the ray,\n  // C is the center of sphere you're testing against\n  // r is the radius of that sphere\n\n  // Compute:\n  // d = L - E ( Direction vector of ray, from start to end )\n  // f = E - C ( Vector from center sphere to ray start )\n\n  // Then the intersection is found by..\n  // P = E + t * d\n  // This is a parametric equation:\n  // Px = Ex + tdx\n  // Py = Ey + tdy\n\n  // get a, b, c values\n  var a = (Lx - Ex) * (Lx - Ex) + (Ly - Ey) * (Ly - Ey);\n  var b = 2 * ((Ex - Cx) * (Lx - Ex) + (Ey - Cy) * (Ly - Ey));\n  var c = (Ex - Cx) * (Ex - Cx) + (Ey - Cy) * (Ey - Cy) - r * r;\n\n  // get discriminant\n  var disc = b * b - 4 * a * c;\n  if (disc >= 0) {\n    // insert into quadratic formula\n    var t1 = (-b + Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n    var t2 = (-b - Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n    var intersections = null;\n    if (t1 >= 0 && t1 <= 1) {\n      // t1 is the intersection, and it's closer than t2\n      // (since t1 uses -b - discriminant)\n      // Impale, Poke\n      return [t1];\n    }\n\n    // here t1 didn't intersect so we are either started\n    // inside the sphere or completely past it\n    if (t2 >= 0 && t2 <= 1) {\n      // ExitWound\n      return [t2];\n    }\n\n    return intersections;\n  } else return null;\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(7);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar LGraph = __webpack_require__(6);\nvar PointD = __webpack_require__(5);\nvar Transform = __webpack_require__(17);\nvar Emitter = __webpack_require__(29);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar PointD = __webpack_require__(5);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __webpack_require__(15);\nvar FDLayoutConstants = __webpack_require__(4);\nvar LayoutConstants = __webpack_require__(0);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var originalIdealLength;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    originalIdealLength = edge.idealLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += originalIdealLength * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  // Reassign this attribute by using new constant value\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = edge.edgeElasticity * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      // Here we use half of the nodes' repulsion values for backward compatibility\n      repulsionForce = (nodeA.nodeRepulsion / 2 + nodeB.nodeRepulsion / 2) * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LEdge = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(4);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n\n  // Ideal length and elasticity value for this edge\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.edgeElasticity = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LNode = __webpack_require__(3);\nvar FDLayoutConstants = __webpack_require__(4);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n\n  // Repulsion value of this node\n  this.nodeRepulsion = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n// Some matrix (1d and 2d array) operations\nfunction Matrix() {}\n\n/**\n * matrix multiplication\n * array1, array2 and result are 2d arrays\n */\nMatrix.multMat = function (array1, array2) {\n  var result = [];\n\n  for (var i = 0; i < array1.length; i++) {\n    result[i] = [];\n    for (var j = 0; j < array2[0].length; j++) {\n      result[i][j] = 0;\n      for (var k = 0; k < array1[0].length; k++) {\n        result[i][j] += array1[i][k] * array2[k][j];\n      }\n    }\n  }\n  return result;\n};\n\n/**\n * matrix transpose\n * array and result are 2d arrays\n */\nMatrix.transpose = function (array) {\n  var result = [];\n\n  for (var i = 0; i < array[0].length; i++) {\n    result[i] = [];\n    for (var j = 0; j < array.length; j++) {\n      result[i][j] = array[j][i];\n    }\n  }\n\n  return result;\n};\n\n/**\n * multiply array with constant\n * array and result are 1d arrays\n */\nMatrix.multCons = function (array, constant) {\n  var result = [];\n\n  for (var i = 0; i < array.length; i++) {\n    result[i] = array[i] * constant;\n  }\n\n  return result;\n};\n\n/**\n * substract two arrays\n * array1, array2 and result are 1d arrays\n */\nMatrix.minusOp = function (array1, array2) {\n  var result = [];\n\n  for (var i = 0; i < array1.length; i++) {\n    result[i] = array1[i] - array2[i];\n  }\n\n  return result;\n};\n\n/**\n * dot product of two arrays with same size\n * array1 and array2 are 1d arrays\n */\nMatrix.dotProduct = function (array1, array2) {\n  var product = 0;\n\n  for (var i = 0; i < array1.length; i++) {\n    product += array1[i] * array2[i];\n  }\n\n  return product;\n};\n\n/**\n * magnitude of an array\n * array is 1d array\n */\nMatrix.mag = function (array) {\n  return Math.sqrt(this.dotProduct(array, array));\n};\n\n/**\n * normalization of an array\n * array and result are 1d array\n */\nMatrix.normalize = function (array) {\n  var result = [];\n  var magnitude = this.mag(array);\n\n  for (var i = 0; i < array.length; i++) {\n    result[i] = array[i] / magnitude;\n  }\n\n  return result;\n};\n\n/**\n * multiply an array with centering matrix\n * array and result are 1d array\n */\nMatrix.multGamma = function (array) {\n  var result = [];\n  var sum = 0;\n\n  for (var i = 0; i < array.length; i++) {\n    sum += array[i];\n  }\n\n  sum *= -1 / array.length;\n\n  for (var _i = 0; _i < array.length; _i++) {\n    result[_i] = sum + array[_i];\n  }\n  return result;\n};\n\n/**\n * a special matrix multiplication\n * result = 0.5 * C * INV * C^T * array\n * array and result are 1d, C and INV are 2d arrays\n */\nMatrix.multL = function (array, C, INV) {\n  var result = [];\n  var temp1 = [];\n  var temp2 = [];\n\n  // multiply by C^T\n  for (var i = 0; i < C[0].length; i++) {\n    var sum = 0;\n    for (var j = 0; j < C.length; j++) {\n      sum += -0.5 * C[j][i] * array[j];\n    }\n    temp1[i] = sum;\n  }\n  // multiply the result by INV\n  for (var _i2 = 0; _i2 < INV.length; _i2++) {\n    var _sum = 0;\n    for (var _j = 0; _j < INV.length; _j++) {\n      _sum += INV[_i2][_j] * temp1[_j];\n    }\n    temp2[_i2] = _sum;\n  }\n  // multiply the result by C\n  for (var _i3 = 0; _i3 < C.length; _i3++) {\n    var _sum2 = 0;\n    for (var _j2 = 0; _j2 < C[0].length; _j2++) {\n      _sum2 += C[_i3][_j2] * temp2[_j2];\n    }\n    result[_i3] = _sum2;\n  }\n\n  return result;\n};\n\nmodule.exports = Matrix;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __webpack_require__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n// Singular Value Decomposition implementation\nfunction SVD() {};\n\n/* Below singular value decomposition (svd) code including hypot function is adopted from https://github.com/dragonfly-ai/JamaJS\n   Some changes are applied to make the code compatible with the fcose code and to make it independent from Jama.\n   Input matrix is changed to a 2D array instead of Jama matrix. Matrix dimensions are taken according to 2D array instead of using Jama functions.\n   An object that includes singular value components is created for return. \n   The types of input parameters of the hypot function are removed. \n   let is used instead of var for the variable initialization.\n*/\n/*\n                               Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright {yyyy} {name of copyright owner}\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\nSVD.svd = function (A) {\n  this.U = null;\n  this.V = null;\n  this.s = null;\n  this.m = 0;\n  this.n = 0;\n  this.m = A.length;\n  this.n = A[0].length;\n  var nu = Math.min(this.m, this.n);\n  this.s = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(Math.min(this.m + 1, this.n));\n  this.U = function (dims) {\n    var allocate = function allocate(dims) {\n      if (dims.length == 0) {\n        return 0;\n      } else {\n        var array = [];\n        for (var i = 0; i < dims[0]; i++) {\n          array.push(allocate(dims.slice(1)));\n        }\n        return array;\n      }\n    };\n    return allocate(dims);\n  }([this.m, nu]);\n  this.V = function (dims) {\n    var allocate = function allocate(dims) {\n      if (dims.length == 0) {\n        return 0;\n      } else {\n        var array = [];\n        for (var i = 0; i < dims[0]; i++) {\n          array.push(allocate(dims.slice(1)));\n        }\n        return array;\n      }\n    };\n    return allocate(dims);\n  }([this.n, this.n]);\n  var e = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(this.n);\n  var work = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(this.m);\n  var wantu = true;\n  var wantv = true;\n  var nct = Math.min(this.m - 1, this.n);\n  var nrt = Math.max(0, Math.min(this.n - 2, this.m));\n  for (var k = 0; k < Math.max(nct, nrt); k++) {\n    if (k < nct) {\n      this.s[k] = 0;\n      for (var i = k; i < this.m; i++) {\n        this.s[k] = SVD.hypot(this.s[k], A[i][k]);\n      }\n      ;\n      if (this.s[k] !== 0.0) {\n        if (A[k][k] < 0.0) {\n          this.s[k] = -this.s[k];\n        }\n        for (var _i = k; _i < this.m; _i++) {\n          A[_i][k] /= this.s[k];\n        }\n        ;\n        A[k][k] += 1.0;\n      }\n      this.s[k] = -this.s[k];\n    }\n    for (var j = k + 1; j < this.n; j++) {\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(k < nct, this.s[k] !== 0.0)) {\n        var t = 0;\n        for (var _i2 = k; _i2 < this.m; _i2++) {\n          t += A[_i2][k] * A[_i2][j];\n        }\n        ;\n        t = -t / A[k][k];\n        for (var _i3 = k; _i3 < this.m; _i3++) {\n          A[_i3][j] += t * A[_i3][k];\n        }\n        ;\n      }\n      e[j] = A[k][j];\n    }\n    ;\n    if (function (lhs, rhs) {\n      return lhs && rhs;\n    }(wantu, k < nct)) {\n      for (var _i4 = k; _i4 < this.m; _i4++) {\n        this.U[_i4][k] = A[_i4][k];\n      }\n      ;\n    }\n    if (k < nrt) {\n      e[k] = 0;\n      for (var _i5 = k + 1; _i5 < this.n; _i5++) {\n        e[k] = SVD.hypot(e[k], e[_i5]);\n      }\n      ;\n      if (e[k] !== 0.0) {\n        if (e[k + 1] < 0.0) {\n          e[k] = -e[k];\n        }\n        for (var _i6 = k + 1; _i6 < this.n; _i6++) {\n          e[_i6] /= e[k];\n        }\n        ;\n        e[k + 1] += 1.0;\n      }\n      e[k] = -e[k];\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(k + 1 < this.m, e[k] !== 0.0)) {\n        for (var _i7 = k + 1; _i7 < this.m; _i7++) {\n          work[_i7] = 0.0;\n        }\n        ;\n        for (var _j = k + 1; _j < this.n; _j++) {\n          for (var _i8 = k + 1; _i8 < this.m; _i8++) {\n            work[_i8] += e[_j] * A[_i8][_j];\n          }\n          ;\n        }\n        ;\n        for (var _j2 = k + 1; _j2 < this.n; _j2++) {\n          var _t = -e[_j2] / e[k + 1];\n          for (var _i9 = k + 1; _i9 < this.m; _i9++) {\n            A[_i9][_j2] += _t * work[_i9];\n          }\n          ;\n        }\n        ;\n      }\n      if (wantv) {\n        for (var _i10 = k + 1; _i10 < this.n; _i10++) {\n          this.V[_i10][k] = e[_i10];\n        };\n      }\n    }\n  };\n  var p = Math.min(this.n, this.m + 1);\n  if (nct < this.n) {\n    this.s[nct] = A[nct][nct];\n  }\n  if (this.m < p) {\n    this.s[p - 1] = 0.0;\n  }\n  if (nrt + 1 < p) {\n    e[nrt] = A[nrt][p - 1];\n  }\n  e[p - 1] = 0.0;\n  if (wantu) {\n    for (var _j3 = nct; _j3 < nu; _j3++) {\n      for (var _i11 = 0; _i11 < this.m; _i11++) {\n        this.U[_i11][_j3] = 0.0;\n      }\n      ;\n      this.U[_j3][_j3] = 1.0;\n    };\n    for (var _k = nct - 1; _k >= 0; _k--) {\n      if (this.s[_k] !== 0.0) {\n        for (var _j4 = _k + 1; _j4 < nu; _j4++) {\n          var _t2 = 0;\n          for (var _i12 = _k; _i12 < this.m; _i12++) {\n            _t2 += this.U[_i12][_k] * this.U[_i12][_j4];\n          };\n          _t2 = -_t2 / this.U[_k][_k];\n          for (var _i13 = _k; _i13 < this.m; _i13++) {\n            this.U[_i13][_j4] += _t2 * this.U[_i13][_k];\n          };\n        };\n        for (var _i14 = _k; _i14 < this.m; _i14++) {\n          this.U[_i14][_k] = -this.U[_i14][_k];\n        };\n        this.U[_k][_k] = 1.0 + this.U[_k][_k];\n        for (var _i15 = 0; _i15 < _k - 1; _i15++) {\n          this.U[_i15][_k] = 0.0;\n        };\n      } else {\n        for (var _i16 = 0; _i16 < this.m; _i16++) {\n          this.U[_i16][_k] = 0.0;\n        };\n        this.U[_k][_k] = 1.0;\n      }\n    };\n  }\n  if (wantv) {\n    for (var _k2 = this.n - 1; _k2 >= 0; _k2--) {\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(_k2 < nrt, e[_k2] !== 0.0)) {\n        for (var _j5 = _k2 + 1; _j5 < nu; _j5++) {\n          var _t3 = 0;\n          for (var _i17 = _k2 + 1; _i17 < this.n; _i17++) {\n            _t3 += this.V[_i17][_k2] * this.V[_i17][_j5];\n          };\n          _t3 = -_t3 / this.V[_k2 + 1][_k2];\n          for (var _i18 = _k2 + 1; _i18 < this.n; _i18++) {\n            this.V[_i18][_j5] += _t3 * this.V[_i18][_k2];\n          };\n        };\n      }\n      for (var _i19 = 0; _i19 < this.n; _i19++) {\n        this.V[_i19][_k2] = 0.0;\n      };\n      this.V[_k2][_k2] = 1.0;\n    };\n  }\n  var pp = p - 1;\n  var iter = 0;\n  var eps = Math.pow(2.0, -52.0);\n  var tiny = Math.pow(2.0, -966.0);\n  while (p > 0) {\n    var _k3 = void 0;\n    var kase = void 0;\n    for (_k3 = p - 2; _k3 >= -1; _k3--) {\n      if (_k3 === -1) {\n        break;\n      }\n      if (Math.abs(e[_k3]) <= tiny + eps * (Math.abs(this.s[_k3]) + Math.abs(this.s[_k3 + 1]))) {\n        e[_k3] = 0.0;\n        break;\n      }\n    };\n    if (_k3 === p - 2) {\n      kase = 4;\n    } else {\n      var ks = void 0;\n      for (ks = p - 1; ks >= _k3; ks--) {\n        if (ks === _k3) {\n          break;\n        }\n        var _t4 = (ks !== p ? Math.abs(e[ks]) : 0.0) + (ks !== _k3 + 1 ? Math.abs(e[ks - 1]) : 0.0);\n        if (Math.abs(this.s[ks]) <= tiny + eps * _t4) {\n          this.s[ks] = 0.0;\n          break;\n        }\n      };\n      if (ks === _k3) {\n        kase = 3;\n      } else if (ks === p - 1) {\n        kase = 1;\n      } else {\n        kase = 2;\n        _k3 = ks;\n      }\n    }\n    _k3++;\n    switch (kase) {\n      case 1:\n        {\n          var f = e[p - 2];\n          e[p - 2] = 0.0;\n          for (var _j6 = p - 2; _j6 >= _k3; _j6--) {\n            var _t5 = SVD.hypot(this.s[_j6], f);\n            var cs = this.s[_j6] / _t5;\n            var sn = f / _t5;\n            this.s[_j6] = _t5;\n            if (_j6 !== _k3) {\n              f = -sn * e[_j6 - 1];\n              e[_j6 - 1] = cs * e[_j6 - 1];\n            }\n            if (wantv) {\n              for (var _i20 = 0; _i20 < this.n; _i20++) {\n                _t5 = cs * this.V[_i20][_j6] + sn * this.V[_i20][p - 1];\n                this.V[_i20][p - 1] = -sn * this.V[_i20][_j6] + cs * this.V[_i20][p - 1];\n                this.V[_i20][_j6] = _t5;\n              };\n            }\n          };\n        };\n        break;\n      case 2:\n        {\n          var _f = e[_k3 - 1];\n          e[_k3 - 1] = 0.0;\n          for (var _j7 = _k3; _j7 < p; _j7++) {\n            var _t6 = SVD.hypot(this.s[_j7], _f);\n            var _cs = this.s[_j7] / _t6;\n            var _sn = _f / _t6;\n            this.s[_j7] = _t6;\n            _f = -_sn * e[_j7];\n            e[_j7] = _cs * e[_j7];\n            if (wantu) {\n              for (var _i21 = 0; _i21 < this.m; _i21++) {\n                _t6 = _cs * this.U[_i21][_j7] + _sn * this.U[_i21][_k3 - 1];\n                this.U[_i21][_k3 - 1] = -_sn * this.U[_i21][_j7] + _cs * this.U[_i21][_k3 - 1];\n                this.U[_i21][_j7] = _t6;\n              };\n            }\n          };\n        };\n        break;\n      case 3:\n        {\n          var scale = Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[p - 1]), Math.abs(this.s[p - 2])), Math.abs(e[p - 2])), Math.abs(this.s[_k3])), Math.abs(e[_k3]));\n          var sp = this.s[p - 1] / scale;\n          var spm1 = this.s[p - 2] / scale;\n          var epm1 = e[p - 2] / scale;\n          var sk = this.s[_k3] / scale;\n          var ek = e[_k3] / scale;\n          var b = ((spm1 + sp) * (spm1 - sp) + epm1 * epm1) / 2.0;\n          var c = sp * epm1 * (sp * epm1);\n          var shift = 0.0;\n          if (function (lhs, rhs) {\n            return lhs || rhs;\n          }(b !== 0.0, c !== 0.0)) {\n            shift = Math.sqrt(b * b + c);\n            if (b < 0.0) {\n              shift = -shift;\n            }\n            shift = c / (b + shift);\n          }\n          var _f2 = (sk + sp) * (sk - sp) + shift;\n          var g = sk * ek;\n          for (var _j8 = _k3; _j8 < p - 1; _j8++) {\n            var _t7 = SVD.hypot(_f2, g);\n            var _cs2 = _f2 / _t7;\n            var _sn2 = g / _t7;\n            if (_j8 !== _k3) {\n              e[_j8 - 1] = _t7;\n            }\n            _f2 = _cs2 * this.s[_j8] + _sn2 * e[_j8];\n            e[_j8] = _cs2 * e[_j8] - _sn2 * this.s[_j8];\n            g = _sn2 * this.s[_j8 + 1];\n            this.s[_j8 + 1] = _cs2 * this.s[_j8 + 1];\n            if (wantv) {\n              for (var _i22 = 0; _i22 < this.n; _i22++) {\n                _t7 = _cs2 * this.V[_i22][_j8] + _sn2 * this.V[_i22][_j8 + 1];\n                this.V[_i22][_j8 + 1] = -_sn2 * this.V[_i22][_j8] + _cs2 * this.V[_i22][_j8 + 1];\n                this.V[_i22][_j8] = _t7;\n              };\n            }\n            _t7 = SVD.hypot(_f2, g);\n            _cs2 = _f2 / _t7;\n            _sn2 = g / _t7;\n            this.s[_j8] = _t7;\n            _f2 = _cs2 * e[_j8] + _sn2 * this.s[_j8 + 1];\n            this.s[_j8 + 1] = -_sn2 * e[_j8] + _cs2 * this.s[_j8 + 1];\n            g = _sn2 * e[_j8 + 1];\n            e[_j8 + 1] = _cs2 * e[_j8 + 1];\n            if (wantu && _j8 < this.m - 1) {\n              for (var _i23 = 0; _i23 < this.m; _i23++) {\n                _t7 = _cs2 * this.U[_i23][_j8] + _sn2 * this.U[_i23][_j8 + 1];\n                this.U[_i23][_j8 + 1] = -_sn2 * this.U[_i23][_j8] + _cs2 * this.U[_i23][_j8 + 1];\n                this.U[_i23][_j8] = _t7;\n              };\n            }\n          };\n          e[p - 2] = _f2;\n          iter = iter + 1;\n        };\n        break;\n      case 4:\n        {\n          if (this.s[_k3] <= 0.0) {\n            this.s[_k3] = this.s[_k3] < 0.0 ? -this.s[_k3] : 0.0;\n            if (wantv) {\n              for (var _i24 = 0; _i24 <= pp; _i24++) {\n                this.V[_i24][_k3] = -this.V[_i24][_k3];\n              };\n            }\n          }\n          while (_k3 < pp) {\n            if (this.s[_k3] >= this.s[_k3 + 1]) {\n              break;\n            }\n            var _t8 = this.s[_k3];\n            this.s[_k3] = this.s[_k3 + 1];\n            this.s[_k3 + 1] = _t8;\n            if (wantv && _k3 < this.n - 1) {\n              for (var _i25 = 0; _i25 < this.n; _i25++) {\n                _t8 = this.V[_i25][_k3 + 1];\n                this.V[_i25][_k3 + 1] = this.V[_i25][_k3];\n                this.V[_i25][_k3] = _t8;\n              };\n            }\n            if (wantu && _k3 < this.m - 1) {\n              for (var _i26 = 0; _i26 < this.m; _i26++) {\n                _t8 = this.U[_i26][_k3 + 1];\n                this.U[_i26][_k3 + 1] = this.U[_i26][_k3];\n                this.U[_i26][_k3] = _t8;\n              };\n            }\n            _k3++;\n          };\n          iter = 0;\n          p--;\n        };\n        break;\n    }\n  };\n  var result = { U: this.U, V: this.V, S: this.s };\n  return result;\n};\n\n// sqrt(a^2 + b^2) without under/overflow.\nSVD.hypot = function (a, b) {\n  var r = void 0;\n  if (Math.abs(a) > Math.abs(b)) {\n    r = b / a;\n    r = Math.abs(a) * Math.sqrt(1 + r * r);\n  } else if (b != 0) {\n    r = a / b;\n    r = Math.abs(b) * Math.sqrt(1 + r * r);\n  } else {\n    r = 0.0;\n  }\n  return r;\n};\n\nmodule.exports = SVD;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __webpack_require__(18);\nlayoutBase.FDLayoutConstants = __webpack_require__(4);\nlayoutBase.FDLayoutEdge = __webpack_require__(19);\nlayoutBase.FDLayoutNode = __webpack_require__(20);\nlayoutBase.DimensionD = __webpack_require__(21);\nlayoutBase.HashMap = __webpack_require__(22);\nlayoutBase.HashSet = __webpack_require__(23);\nlayoutBase.IGeometry = __webpack_require__(8);\nlayoutBase.IMath = __webpack_require__(9);\nlayoutBase.Integer = __webpack_require__(10);\nlayoutBase.Point = __webpack_require__(12);\nlayoutBase.PointD = __webpack_require__(5);\nlayoutBase.RandomSeed = __webpack_require__(16);\nlayoutBase.RectangleD = __webpack_require__(13);\nlayoutBase.Transform = __webpack_require__(17);\nlayoutBase.UniqueIDGeneretor = __webpack_require__(14);\nlayoutBase.Quicksort = __webpack_require__(25);\nlayoutBase.LinkedList = __webpack_require__(11);\nlayoutBase.LGraphObject = __webpack_require__(2);\nlayoutBase.LGraph = __webpack_require__(6);\nlayoutBase.LEdge = __webpack_require__(1);\nlayoutBase.LGraphManager = __webpack_require__(7);\nlayoutBase.LNode = __webpack_require__(3);\nlayoutBase.Layout = __webpack_require__(15);\nlayoutBase.LayoutConstants = __webpack_require__(0);\nlayoutBase.NeedlemanWunsch = __webpack_require__(27);\nlayoutBase.Matrix = __webpack_require__(24);\nlayoutBase.SVD = __webpack_require__(26);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});"], "mappings": "AAAA,CAAC,SAASA,gCAAgCA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzD,IAAG,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAC3DA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,CAAC,KACvB,IAAG,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EACjDD,MAAM,CAAC,EAAE,EAAEH,OAAO,CAAC,CAAC,KAChB,IAAG,OAAOC,OAAO,KAAK,QAAQ,EAClCA,OAAO,CAAC,YAAY,CAAC,GAAGD,OAAO,CAAC,CAAC,CAAC,KAElCD,IAAI,CAAC,YAAY,CAAC,GAAGC,OAAO,CAAC,CAAC;AAChC,CAAC,EAAE,IAAI,EAAE,YAAW;EACpB,OAAO,QAAU,UAASK,OAAO,EAAE;IAAE;IACrC,SAAU;IACV;IAAU,IAAIC,gBAAgB,GAAG,CAAC,CAAC;IACnC;IACA,SAAU;IACV;IAAU,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;MACjD;MACA,SAAW;MACX,QAAW,IAAGF,gBAAgB,CAACE,QAAQ,CAAC,EAAE;QAC1C,QAAY,OAAOF,gBAAgB,CAACE,QAAQ,CAAC,CAACP,OAAO;QACrD;MAAW;MACX,SAAW;MACX;MAAW,IAAIC,MAAM,GAAGI,gBAAgB,CAACE,QAAQ,CAAC,GAAG;QACrD,QAAYC,CAAC,EAAED,QAAQ;QACvB,QAAYE,CAAC,EAAE,KAAK;QACpB,QAAYT,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAWI,OAAO,CAACG,QAAQ,CAAC,CAACG,IAAI,CAACT,MAAM,CAACD,OAAO,EAAEC,MAAM,EAAEA,MAAM,CAACD,OAAO,EAAEM,mBAAmB,CAAC;MAC9F;MACA,SAAW;MACX;MAAWL,MAAM,CAACQ,CAAC,GAAG,IAAI;MAC1B;MACA,SAAW;MACX;MAAW,OAAOR,MAAM,CAACD,OAAO;MAChC;IAAU;IACV;IACA;IACA,SAAU;IACV;IAAUM,mBAAmB,CAACK,CAAC,GAAGP,OAAO;IACzC;IACA,SAAU;IACV;IAAUE,mBAAmB,CAACM,CAAC,GAAGP,gBAAgB;IAClD;IACA,SAAU;IACV;IAAUC,mBAAmB,CAACE,CAAC,GAAG,UAASK,KAAK,EAAE;MAAE,OAAOA,KAAK;IAAE,CAAC;IACnE;IACA,SAAU;IACV;IAAUP,mBAAmB,CAACQ,CAAC,GAAG,UAASd,OAAO,EAAEe,IAAI,EAAEC,MAAM,EAAE;MAClE,QAAW,IAAG,CAACV,mBAAmB,CAACW,CAAC,CAACjB,OAAO,EAAEe,IAAI,CAAC,EAAE;QACrD,QAAYG,MAAM,CAACC,cAAc,CAACnB,OAAO,EAAEe,IAAI,EAAE;UACjD,QAAaK,YAAY,EAAE,KAAK;UAChC,QAAaC,UAAU,EAAE,IAAI;UAC7B,QAAaC,GAAG,EAAEN;UAClB;QAAY,CAAC,CAAC;QACd;MAAW;MACX;IAAU,CAAC;IACX;IACA,SAAU;IACV;IAAUV,mBAAmB,CAACiB,CAAC,GAAG,UAAStB,MAAM,EAAE;MACnD,QAAW,IAAIe,MAAM,GAAGf,MAAM,IAAIA,MAAM,CAACuB,UAAU,GACnD,QAAY,SAASC,UAAUA,CAAA,EAAG;QAAE,OAAOxB,MAAM,CAAC,SAAS,CAAC;MAAE,CAAC,GAC/D,QAAY,SAASyB,gBAAgBA,CAAA,EAAG;QAAE,OAAOzB,MAAM;MAAE,CAAC;MAC1D;MAAWK,mBAAmB,CAACQ,CAAC,CAACE,MAAM,EAAE,GAAG,EAAEA,MAAM,CAAC;MACrD;MAAW,OAAOA,MAAM;MACxB;IAAU,CAAC;IACX;IACA,SAAU;IACV;IAAUV,mBAAmB,CAACW,CAAC,GAAG,UAASU,MAAM,EAAEC,QAAQ,EAAE;MAAE,OAAOV,MAAM,CAACW,SAAS,CAACC,cAAc,CAACpB,IAAI,CAACiB,MAAM,EAAEC,QAAQ,CAAC;IAAE,CAAC;IAC/H;IACA,SAAU;IACV;IAAUtB,mBAAmB,CAACyB,CAAC,GAAG,EAAE;IACpC;IACA,SAAU;IACV;IAAU,OAAOzB,mBAAmB,CAACA,mBAAmB,CAAC0B,CAAC,GAAG,EAAE,CAAC;IAChE;EAAS;EACT;EACA,SAAU,EACV;EACA,KAAO,UAAS/B,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS2B,eAAeA,CAAA,EAAG,CAAC;;IAE5B;AACA;AACA;IACAA,eAAe,CAACC,OAAO,GAAG,CAAC;;IAE3B;AACA;AACA;IACAD,eAAe,CAACE,8BAA8B,GAAG,KAAK;IACtDF,eAAe,CAACG,mBAAmB,GAAG,KAAK;IAC3CH,eAAe,CAACI,2BAA2B,GAAG,IAAI;IAClDJ,eAAe,CAACK,+BAA+B,GAAG,KAAK;IACvDL,eAAe,CAACM,wBAAwB,GAAG,EAAE;IAC7CN,eAAe,CAACO,+BAA+B,GAAG,KAAK;;IAEvD;IACA;IACA;IACA;AACA;AACA;AACA;IACAP,eAAe,CAACQ,oBAAoB,GAAG,EAAE;;IAEzC;AACA;AACA;IACAR,eAAe,CAACS,8BAA8B,GAAG,KAAK;;IAEtD;AACA;AACA;IACAT,eAAe,CAACU,gBAAgB,GAAG,EAAE;;IAErC;AACA;AACA;IACAV,eAAe,CAACW,qBAAqB,GAAGX,eAAe,CAACU,gBAAgB,GAAG,CAAC;;IAE5E;AACA;AACA;AACA;IACAV,eAAe,CAACY,wBAAwB,GAAG,EAAE;;IAE7C;AACA;AACA;IACAZ,eAAe,CAACa,eAAe,GAAG,CAAC;;IAEnC;AACA;AACA;IACAb,eAAe,CAACc,cAAc,GAAG,OAAO;;IAExC;AACA;AACA;IACAd,eAAe,CAACe,sBAAsB,GAAGf,eAAe,CAACc,cAAc,GAAG,IAAI;;IAE9E;AACA;AACA;IACAd,eAAe,CAACgB,cAAc,GAAG,IAAI;IACrChB,eAAe,CAACiB,cAAc,GAAG,GAAG;IAEpCjD,MAAM,CAACD,OAAO,GAAGiC,eAAe;;IAEhC;EAAM,CAAC,IACP;EACA,KAAO,UAAShC,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI8C,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IACtC,IAAI+C,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAASgD,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;MACpCN,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAE+C,KAAK,CAAC;MAE9B,IAAI,CAACC,2BAA2B,GAAG,KAAK;MACxC,IAAI,CAACC,YAAY,GAAGF,KAAK;MACzB,IAAI,CAACG,UAAU,GAAG,EAAE;MACpB,IAAI,CAACL,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACtB;IAEAF,KAAK,CAACzB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7BG,KAAK,CAACQ,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IAClC;IAEAR,KAAK,CAACzB,SAAS,CAACkC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACR,MAAM;IACpB,CAAC;IAEDD,KAAK,CAACzB,SAAS,CAACmC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACR,MAAM;IACpB,CAAC;IAEDF,KAAK,CAACzB,SAAS,CAACoC,YAAY,GAAG,YAAY;MACzC,OAAO,IAAI,CAACA,YAAY;IAC1B,CAAC;IAEDX,KAAK,CAACzB,SAAS,CAACqC,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACC,MAAM;IACpB,CAAC;IAEDb,KAAK,CAACzB,SAAS,CAAC6B,2BAA2B,GAAG,YAAY;MACxD,OAAO,IAAI,CAACA,2BAA2B;IACzC,CAAC;IAEDJ,KAAK,CAACzB,SAAS,CAACuC,aAAa,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAEDN,KAAK,CAACzB,SAAS,CAACwC,MAAM,GAAG,YAAY;MACnC,OAAO,IAAI,CAACC,GAAG;IACjB,CAAC;IAEDhB,KAAK,CAACzB,SAAS,CAAC0C,cAAc,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACC,WAAW;IACzB,CAAC;IAEDlB,KAAK,CAACzB,SAAS,CAAC4C,cAAc,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACC,WAAW;IACzB,CAAC;IAEDpB,KAAK,CAACzB,SAAS,CAAC8C,WAAW,GAAG,UAAUC,IAAI,EAAE;MAC5C,IAAI,IAAI,CAACrB,MAAM,KAAKqB,IAAI,EAAE;QACxB,OAAO,IAAI,CAACpB,MAAM;MACpB,CAAC,MAAM,IAAI,IAAI,CAACA,MAAM,KAAKoB,IAAI,EAAE;QAC/B,OAAO,IAAI,CAACrB,MAAM;MACpB,CAAC,MAAM;QACL,MAAM,qCAAqC;MAC7C;IACF,CAAC;IAEDD,KAAK,CAACzB,SAAS,CAACgD,kBAAkB,GAAG,UAAUD,IAAI,EAAEE,KAAK,EAAE;MAC1D,IAAIC,QAAQ,GAAG,IAAI,CAACJ,WAAW,CAACC,IAAI,CAAC;MACrC,IAAI9E,IAAI,GAAGgF,KAAK,CAACE,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAE5C,OAAO,IAAI,EAAE;QACX,IAAIF,QAAQ,CAACG,QAAQ,CAAC,CAAC,IAAIJ,KAAK,EAAE;UAChC,OAAOC,QAAQ;QACjB;QAEA,IAAIA,QAAQ,CAACG,QAAQ,CAAC,CAAC,IAAIpF,IAAI,EAAE;UAC/B;QACF;QAEAiF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;MAC5C;MAEA,OAAO,IAAI;IACb,CAAC;IAED7B,KAAK,CAACzB,SAAS,CAACuD,YAAY,GAAG,YAAY;MACzC,IAAIC,oBAAoB,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;MAEvC,IAAI,CAAC5B,2BAA2B,GAAGN,SAAS,CAACmC,eAAe,CAAC,IAAI,CAAC/B,MAAM,CAACgC,OAAO,CAAC,CAAC,EAAE,IAAI,CAACjC,MAAM,CAACiC,OAAO,CAAC,CAAC,EAAEH,oBAAoB,CAAC;MAEhI,IAAI,CAAC,IAAI,CAAC3B,2BAA2B,EAAE;QACrC,IAAI,CAAC+B,OAAO,GAAGJ,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;QAChE,IAAI,CAACK,OAAO,GAAGL,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;QAEhE,IAAIM,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAC,GAAG,GAAG,EAAE;UAChC,IAAI,CAACA,OAAO,GAAGpC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;QACzC;QAEA,IAAIE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,OAAO,CAAC,GAAG,GAAG,EAAE;UAChC,IAAI,CAACA,OAAO,GAAGrC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC;QACzC;QAEA,IAAI,CAACvB,MAAM,GAAGwB,IAAI,CAACG,IAAI,CAAC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC;MACpF;IACF,CAAC;IAEDpC,KAAK,CAACzB,SAAS,CAACkE,kBAAkB,GAAG,YAAY;MAC/C,IAAI,CAACN,OAAO,GAAG,IAAI,CAACjC,MAAM,CAACwC,UAAU,CAAC,CAAC,GAAG,IAAI,CAACzC,MAAM,CAACyC,UAAU,CAAC,CAAC;MAClE,IAAI,CAACN,OAAO,GAAG,IAAI,CAAClC,MAAM,CAACyC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC1C,MAAM,CAAC0C,UAAU,CAAC,CAAC;MAElE,IAAIN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAC,GAAG,GAAG,EAAE;QAChC,IAAI,CAACA,OAAO,GAAGpC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACJ,OAAO,CAAC;MACzC;MAEA,IAAIE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,OAAO,CAAC,GAAG,GAAG,EAAE;QAChC,IAAI,CAACA,OAAO,GAAGrC,KAAK,CAACwC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC;MACzC;MAEA,IAAI,CAACvB,MAAM,GAAGwB,IAAI,CAACG,IAAI,CAAC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC;IACpF,CAAC;IAEDzF,MAAM,CAACD,OAAO,GAAGsD,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASrD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS6C,YAAYA,CAACQ,YAAY,EAAE;MAClC,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC;IAEA1D,MAAM,CAACD,OAAO,GAAGmD,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAASlD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI4F,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IACrC,IAAI6F,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAI8F,UAAU,GAAG9F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASgG,KAAKA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACnC;MACA,IAAID,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;QACjCA,KAAK,GAAGF,GAAG;MACb;MAEArD,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAEgG,KAAK,CAAC;;MAE9B;MACA,IAAIH,EAAE,CAACI,YAAY,IAAI,IAAI,EAAEJ,EAAE,GAAGA,EAAE,CAACI,YAAY;MAEjD,IAAI,CAACC,aAAa,GAAGV,OAAO,CAACW,SAAS;MACtC,IAAI,CAACC,kBAAkB,GAAGZ,OAAO,CAACa,SAAS;MAC3C,IAAI,CAACpD,YAAY,GAAG+C,KAAK;MACzB,IAAI,CAACM,KAAK,GAAG,EAAE;MACf,IAAI,CAACL,YAAY,GAAGJ,EAAE;MAEtB,IAAIE,IAAI,IAAI,IAAI,IAAID,GAAG,IAAI,IAAI,EAAE,IAAI,CAACS,IAAI,GAAG,IAAId,UAAU,CAACK,GAAG,CAACU,CAAC,EAAEV,GAAG,CAACW,CAAC,EAAEV,IAAI,CAACW,KAAK,EAAEX,IAAI,CAACY,MAAM,CAAC,CAAC,KAAK,IAAI,CAACJ,IAAI,GAAG,IAAId,UAAU,CAAC,CAAC;IACtI;IAEAG,KAAK,CAACzE,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IACvD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7BmD,KAAK,CAACxC,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IAClC;IAEAwC,KAAK,CAACzE,SAAS,CAACyF,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDV,KAAK,CAACzE,SAAS,CAAC0F,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACC,KAAK;IACnB,CAAC;IAEDlB,KAAK,CAACzE,SAAS,CAACqD,QAAQ,GAAG,YAAY;MACrC;MACA;MACA;MACA;MACA;;MAEA,OAAO,IAAI,CAACuC,KAAK;IACnB,CAAC;IAEDnB,KAAK,CAACzE,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAACT,IAAI,CAACG,KAAK;IACxB,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC1C,IAAI,CAACH,IAAI,CAACG,KAAK,GAAGA,KAAK;IACzB,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAAC+F,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAACX,IAAI,CAACI,MAAM;IACzB,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MAC5C,IAAI,CAACJ,IAAI,CAACI,MAAM,GAAGA,MAAM;IAC3B,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACmE,UAAU,GAAG,YAAY;MACvC,OAAO,IAAI,CAACiB,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK,GAAG,CAAC;IAC1C,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACoE,UAAU,GAAG,YAAY;MACvC,OAAO,IAAI,CAACgB,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM,GAAG,CAAC;IAC3C,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACiG,SAAS,GAAG,YAAY;MACtC,OAAO,IAAIzB,MAAM,CAAC,IAAI,CAACY,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK,GAAG,CAAC,EAAE,IAAI,CAACH,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;IAC1F,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACkG,WAAW,GAAG,YAAY;MACxC,OAAO,IAAI1B,MAAM,CAAC,IAAI,CAACY,IAAI,CAACC,CAAC,EAAE,IAAI,CAACD,IAAI,CAACE,CAAC,CAAC;IAC7C,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAAC2D,OAAO,GAAG,YAAY;MACpC,OAAO,IAAI,CAACyB,IAAI;IAClB,CAAC;IAEDX,KAAK,CAACzE,SAAS,CAACmG,WAAW,GAAG,YAAY;MACxC,OAAOrC,IAAI,CAACG,IAAI,CAAC,IAAI,CAACmB,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACI,MAAM,CAAC;IAC3F,CAAC;;IAED;AACA;AACA;IACAf,KAAK,CAACzE,SAAS,CAACoG,kBAAkB,GAAG,YAAY;MAC/C,OAAOtC,IAAI,CAACG,IAAI,CAAC,IAAI,CAACmB,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC/F,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACqG,OAAO,GAAG,UAAUC,SAAS,EAAEC,SAAS,EAAE;MACxD,IAAI,CAACnB,IAAI,CAACC,CAAC,GAAGiB,SAAS,CAACjB,CAAC;MACzB,IAAI,CAACD,IAAI,CAACE,CAAC,GAAGgB,SAAS,CAAChB,CAAC;MACzB,IAAI,CAACF,IAAI,CAACG,KAAK,GAAGgB,SAAS,CAAChB,KAAK;MACjC,IAAI,CAACH,IAAI,CAACI,MAAM,GAAGe,SAAS,CAACf,MAAM;IACrC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACwG,SAAS,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;MAC5C,IAAI,CAACtB,IAAI,CAACC,CAAC,GAAGoB,EAAE,GAAG,IAAI,CAACrB,IAAI,CAACG,KAAK,GAAG,CAAC;MACtC,IAAI,CAACH,IAAI,CAACE,CAAC,GAAGoB,EAAE,GAAG,IAAI,CAACtB,IAAI,CAACI,MAAM,GAAG,CAAC;IACzC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAAC2G,WAAW,GAAG,UAAUtB,CAAC,EAAEC,CAAC,EAAE;MAC5C,IAAI,CAACF,IAAI,CAACC,CAAC,GAAGA,CAAC;MACf,IAAI,CAACD,IAAI,CAACE,CAAC,GAAGA,CAAC;IACjB,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAAC4G,MAAM,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;MACzC,IAAI,CAAC1B,IAAI,CAACC,CAAC,IAAIwB,EAAE;MACjB,IAAI,CAACzB,IAAI,CAACE,CAAC,IAAIwB,EAAE;IACnB,CAAC;IAEDrC,KAAK,CAACzE,SAAS,CAAC+G,iBAAiB,GAAG,UAAUC,EAAE,EAAE;MAChD,IAAIC,QAAQ,GAAG,EAAE;MACjB,IAAIC,IAAI;MACR,IAAIC,IAAI,GAAG,IAAI;MAEfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAIA,IAAI,CAACvF,MAAM,IAAIqF,EAAE,EAAE;UACrB,IAAIE,IAAI,CAACxF,MAAM,IAAIyF,IAAI,EAAE,MAAM,wBAAwB;UAEvDF,QAAQ,CAACI,IAAI,CAACH,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;MAEF,OAAOD,QAAQ;IACjB,CAAC;IAEDxC,KAAK,CAACzE,SAAS,CAACsH,eAAe,GAAG,UAAUC,KAAK,EAAE;MACjD,IAAIN,QAAQ,GAAG,EAAE;MACjB,IAAIC,IAAI;MAER,IAAIC,IAAI,GAAG,IAAI;MACfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAI,EAAEA,IAAI,CAACxF,MAAM,IAAIyF,IAAI,IAAID,IAAI,CAACvF,MAAM,IAAIwF,IAAI,CAAC,EAAE,MAAM,qCAAqC;QAE9F,IAAID,IAAI,CAACvF,MAAM,IAAI4F,KAAK,IAAIL,IAAI,CAACxF,MAAM,IAAI6F,KAAK,EAAE;UAChDN,QAAQ,CAACI,IAAI,CAACH,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;MAEF,OAAOD,QAAQ;IACjB,CAAC;IAEDxC,KAAK,CAACzE,SAAS,CAACwH,gBAAgB,GAAG,YAAY;MAC7C,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;MAEzB,IAAIP,IAAI,GAAG,IAAI;MACfA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,UAAUF,IAAI,EAAE;QAEjC,IAAIA,IAAI,CAACxF,MAAM,IAAIyF,IAAI,EAAE;UACvBM,SAAS,CAACE,GAAG,CAACT,IAAI,CAACvF,MAAM,CAAC;QAC5B,CAAC,MAAM;UACL,IAAIuF,IAAI,CAACvF,MAAM,IAAIwF,IAAI,EAAE;YACvB,MAAM,sBAAsB;UAC9B;UAEAM,SAAS,CAACE,GAAG,CAACT,IAAI,CAACxF,MAAM,CAAC;QAC5B;MACF,CAAC,CAAC;MAEF,OAAO+F,SAAS;IAClB,CAAC;IAEDhD,KAAK,CAACzE,SAAS,CAAC4H,YAAY,GAAG,YAAY;MACzC,IAAIC,iBAAiB,GAAG,IAAIH,GAAG,CAAC,CAAC;MACjC,IAAII,SAAS;MACb,IAAIC,QAAQ;MAEZF,iBAAiB,CAACF,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI,IAAI,CAAChC,KAAK,IAAI,IAAI,EAAE;QACtB,IAAIqC,KAAK,GAAG,IAAI,CAACrC,KAAK,CAACsC,QAAQ,CAAC,CAAC;QACjC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCmJ,SAAS,GAAGE,KAAK,CAACrJ,CAAC,CAAC;UACpBoJ,QAAQ,GAAGD,SAAS,CAACF,YAAY,CAAC,CAAC;UACnCG,QAAQ,CAACX,OAAO,CAAC,UAAUrE,IAAI,EAAE;YAC/B8E,iBAAiB,CAACF,GAAG,CAAC5E,IAAI,CAAC;UAC7B,CAAC,CAAC;QACJ;MACF;MAEA,OAAO8E,iBAAiB;IAC1B,CAAC;IAEDpD,KAAK,CAACzE,SAAS,CAACkI,eAAe,GAAG,YAAY;MAC5C,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIL,SAAS;MAEb,IAAI,IAAI,CAACnC,KAAK,IAAI,IAAI,EAAE;QACtBwC,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACL,IAAIH,KAAK,GAAG,IAAI,CAACrC,KAAK,CAACsC,QAAQ,CAAC,CAAC;QACjC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCmJ,SAAS,GAAGE,KAAK,CAACrJ,CAAC,CAAC;UAEpBwJ,YAAY,IAAIL,SAAS,CAACI,eAAe,CAAC,CAAC;QAC7C;MACF;MAEA,IAAIC,YAAY,IAAI,CAAC,EAAE;QACrBA,YAAY,GAAG,CAAC;MAClB;MACA,OAAOA,YAAY;IACrB,CAAC;IAED1D,KAAK,CAACzE,SAAS,CAACoI,gBAAgB,GAAG,YAAY;MAC7C,IAAI,IAAI,CAACrD,aAAa,IAAIV,OAAO,CAACW,SAAS,EAAE;QAC3C,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,aAAa;IAC3B,CAAC;IAEDN,KAAK,CAACzE,SAAS,CAACqI,iBAAiB,GAAG,YAAY;MAC9C,IAAI,IAAI,CAAC1C,KAAK,IAAI,IAAI,EAAE;QACtB,OAAO,IAAI,CAACZ,aAAa,GAAG,CAAC,IAAI,CAACK,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,IAAI,CAAC;MACtE,CAAC,MAAM;QACL,IAAI,CAACT,aAAa,GAAG,IAAI,CAACY,KAAK,CAAC0C,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAACjD,IAAI,CAACG,KAAK,GAAG,IAAI,CAACR,aAAa;QACpC,IAAI,CAACK,IAAI,CAACI,MAAM,GAAG,IAAI,CAACT,aAAa;QAErC,OAAO,IAAI,CAACA,aAAa;MAC3B;IACF,CAAC;IAEDN,KAAK,CAACzE,SAAS,CAACsI,OAAO,GAAG,YAAY;MACpC,IAAIC,aAAa;MACjB,IAAIC,aAAa;MAEjB,IAAIC,IAAI,GAAG,CAACrI,eAAe,CAACe,sBAAsB;MAClD,IAAIuH,IAAI,GAAGtI,eAAe,CAACe,sBAAsB;MACjDoH,aAAa,GAAGnI,eAAe,CAACgB,cAAc,GAAGmD,UAAU,CAACoE,UAAU,CAAC,CAAC,IAAID,IAAI,GAAGD,IAAI,CAAC,GAAGA,IAAI;MAE/F,IAAIG,IAAI,GAAG,CAACxI,eAAe,CAACe,sBAAsB;MAClD,IAAI0H,IAAI,GAAGzI,eAAe,CAACe,sBAAsB;MACjDqH,aAAa,GAAGpI,eAAe,CAACiB,cAAc,GAAGkD,UAAU,CAACoE,UAAU,CAAC,CAAC,IAAIE,IAAI,GAAGD,IAAI,CAAC,GAAGA,IAAI;MAE/F,IAAI,CAACxD,IAAI,CAACC,CAAC,GAAGkD,aAAa;MAC3B,IAAI,CAACnD,IAAI,CAACE,CAAC,GAAGkD,aAAa;IAC7B,CAAC;IAED/D,KAAK,CAACzE,SAAS,CAAC8I,YAAY,GAAG,YAAY;MACzC,IAAI,IAAI,CAACpD,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;QAC3B,MAAM,eAAe;MACvB;MACA,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,EAAE;QAC1C;QACA,IAAIyG,UAAU,GAAG,IAAI,CAACrD,QAAQ,CAAC,CAAC;QAChCqD,UAAU,CAACD,YAAY,CAAC,IAAI,CAAC;QAE7B,IAAI,CAAC1D,IAAI,CAACC,CAAC,GAAG0D,UAAU,CAACC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC5D,IAAI,CAACE,CAAC,GAAGyD,UAAU,CAACE,MAAM,CAAC,CAAC;QAEjC,IAAI,CAACnD,QAAQ,CAACiD,UAAU,CAACG,QAAQ,CAAC,CAAC,GAAGH,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAChD,SAAS,CAAC+C,UAAU,CAACI,SAAS,CAAC,CAAC,GAAGJ,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAI7I,eAAe,CAACS,8BAA8B,EAAE;UAElD,IAAI0E,KAAK,GAAGwD,UAAU,CAACG,QAAQ,CAAC,CAAC,GAAGH,UAAU,CAACC,OAAO,CAAC,CAAC;UACxD,IAAIxD,MAAM,GAAGuD,UAAU,CAACI,SAAS,CAAC,CAAC,GAAGJ,UAAU,CAACE,MAAM,CAAC,CAAC;UAEzD,IAAI,IAAI,CAACG,UAAU,EAAE;YACnB,IAAI,IAAI,CAACC,kBAAkB,IAAI,MAAM,EAAE;cACrC,IAAI,CAACjE,IAAI,CAACC,CAAC,IAAI,IAAI,CAAC+D,UAAU;cAC9B,IAAI,CAACtD,QAAQ,CAACP,KAAK,GAAG,IAAI,CAAC6D,UAAU,CAAC;YACxC,CAAC,MAAM,IAAI,IAAI,CAACC,kBAAkB,IAAI,QAAQ,IAAI,IAAI,CAACD,UAAU,GAAG7D,KAAK,EAAE;cACzE,IAAI,CAACH,IAAI,CAACC,CAAC,IAAI,CAAC,IAAI,CAAC+D,UAAU,GAAG7D,KAAK,IAAI,CAAC;cAC5C,IAAI,CAACO,QAAQ,CAAC,IAAI,CAACsD,UAAU,CAAC;YAChC,CAAC,MAAM,IAAI,IAAI,CAACC,kBAAkB,IAAI,OAAO,EAAE;cAC7C,IAAI,CAACvD,QAAQ,CAACP,KAAK,GAAG,IAAI,CAAC6D,UAAU,CAAC;YACxC;UACF;UAEA,IAAI,IAAI,CAACE,WAAW,EAAE;YACpB,IAAI,IAAI,CAACC,gBAAgB,IAAI,KAAK,EAAE;cAClC,IAAI,CAACnE,IAAI,CAACE,CAAC,IAAI,IAAI,CAACgE,WAAW;cAC/B,IAAI,CAACtD,SAAS,CAACR,MAAM,GAAG,IAAI,CAAC8D,WAAW,CAAC;YAC3C,CAAC,MAAM,IAAI,IAAI,CAACC,gBAAgB,IAAI,QAAQ,IAAI,IAAI,CAACD,WAAW,GAAG9D,MAAM,EAAE;cACzE,IAAI,CAACJ,IAAI,CAACE,CAAC,IAAI,CAAC,IAAI,CAACgE,WAAW,GAAG9D,MAAM,IAAI,CAAC;cAC9C,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACsD,WAAW,CAAC;YAClC,CAAC,MAAM,IAAI,IAAI,CAACC,gBAAgB,IAAI,QAAQ,EAAE;cAC5C,IAAI,CAACvD,SAAS,CAACR,MAAM,GAAG,IAAI,CAAC8D,WAAW,CAAC;YAC3C;UACF;QACF;MACF;IACF,CAAC;IAED7E,KAAK,CAACzE,SAAS,CAACwJ,qBAAqB,GAAG,YAAY;MAClD,IAAI,IAAI,CAACvE,kBAAkB,IAAIZ,OAAO,CAACa,SAAS,EAAE;QAChD,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,kBAAkB;IAChC,CAAC;IAEDR,KAAK,CAACzE,SAAS,CAACyJ,SAAS,GAAG,UAAUC,KAAK,EAAE;MAC3C,IAAIC,IAAI,GAAG,IAAI,CAACvE,IAAI,CAACC,CAAC;MAEtB,IAAIsE,IAAI,GAAGvJ,eAAe,CAACc,cAAc,EAAE;QACzCyI,IAAI,GAAGvJ,eAAe,CAACc,cAAc;MACvC,CAAC,MAAM,IAAIyI,IAAI,GAAG,CAACvJ,eAAe,CAACc,cAAc,EAAE;QACjDyI,IAAI,GAAG,CAACvJ,eAAe,CAACc,cAAc;MACxC;MAEA,IAAI0I,GAAG,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC;MAErB,IAAIsE,GAAG,GAAGxJ,eAAe,CAACc,cAAc,EAAE;QACxC0I,GAAG,GAAGxJ,eAAe,CAACc,cAAc;MACtC,CAAC,MAAM,IAAI0I,GAAG,GAAG,CAACxJ,eAAe,CAACc,cAAc,EAAE;QAChD0I,GAAG,GAAG,CAACxJ,eAAe,CAACc,cAAc;MACvC;MAEA,IAAI2I,OAAO,GAAG,IAAIrF,MAAM,CAACmF,IAAI,EAAEC,GAAG,CAAC;MACnC,IAAIE,QAAQ,GAAGJ,KAAK,CAACK,qBAAqB,CAACF,OAAO,CAAC;MAEnD,IAAI,CAAClD,WAAW,CAACmD,QAAQ,CAACzE,CAAC,EAAEyE,QAAQ,CAACxE,CAAC,CAAC;IAC1C,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAACgJ,OAAO,GAAG,YAAY;MACpC,OAAO,IAAI,CAAC5D,IAAI,CAACC,CAAC;IACpB,CAAC;IAEDZ,KAAK,CAACzE,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAI,CAAC9D,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,IAAI,CAACG,KAAK;IACtC,CAAC;IAEDd,KAAK,CAACzE,SAAS,CAACiJ,MAAM,GAAG,YAAY;MACnC,OAAO,IAAI,CAAC7D,IAAI,CAACE,CAAC;IACpB,CAAC;IAEDb,KAAK,CAACzE,SAAS,CAACmJ,SAAS,GAAG,YAAY;MACtC,OAAO,IAAI,CAAC/D,IAAI,CAACE,CAAC,GAAG,IAAI,CAACF,IAAI,CAACI,MAAM;IACvC,CAAC;IAEDf,KAAK,CAACzE,SAAS,CAACsD,SAAS,GAAG,YAAY;MACtC,IAAI,IAAI,CAACsC,KAAK,IAAI,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,OAAO,IAAI,CAACA,KAAK,CAACtC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEDlF,MAAM,CAACD,OAAO,GAAGsG,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASrG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAE5C,SAASuL,iBAAiBA,CAAA,EAAG,CAAC;;IAE9B;IACA,KAAK,IAAI/H,IAAI,IAAI7B,eAAe,EAAE;MAChC4J,iBAAiB,CAAC/H,IAAI,CAAC,GAAG7B,eAAe,CAAC6B,IAAI,CAAC;IACjD;IAEA+H,iBAAiB,CAACC,cAAc,GAAG,IAAI;IAEvCD,iBAAiB,CAACE,mBAAmB,GAAG,EAAE;IAC1CF,iBAAiB,CAACG,uBAAuB,GAAG,IAAI;IAChDH,iBAAiB,CAACI,0BAA0B,GAAG,MAAM;IACrDJ,iBAAiB,CAACK,wBAAwB,GAAG,GAAG;IAChDL,iBAAiB,CAACM,iCAAiC,GAAG,GAAG;IACzDN,iBAAiB,CAACO,4BAA4B,GAAG,GAAG;IACpDP,iBAAiB,CAACQ,qCAAqC,GAAG,GAAG;IAC7DR,iBAAiB,CAACS,+CAA+C,GAAG,IAAI;IACxET,iBAAiB,CAACU,6CAA6C,GAAG,IAAI;IACtEV,iBAAiB,CAACW,kCAAkC,GAAG,GAAG;IAC1DX,iBAAiB,CAACY,yBAAyB,GAAG,IAAI;IAClDZ,iBAAiB,CAACa,2BAA2B,GAAG,IAAI;IACpDb,iBAAiB,CAACc,2BAA2B,GAAG,IAAI;IACpDd,iBAAiB,CAACe,iCAAiC,GAAG,KAAK;IAC3Df,iBAAiB,CAACgB,qBAAqB,GAAGhB,iBAAiB,CAACe,iCAAiC,GAAG,CAAC;IACjGf,iBAAiB,CAACiB,kBAAkB,GAAGjB,iBAAiB,CAACE,mBAAmB,GAAG,IAAI;IACnFF,iBAAiB,CAACkB,wBAAwB,GAAG,GAAG;IAChDlB,iBAAiB,CAACmB,kCAAkC,GAAG,GAAG;IAC1DnB,iBAAiB,CAAC/I,eAAe,GAAG,CAAC;IACrC+I,iBAAiB,CAACoB,6BAA6B,GAAG,EAAE;IAEpDhN,MAAM,CAACD,OAAO,GAAG6L,iBAAiB;;IAElC;EAAM,CAAC,IACP;EACA,KAAO,UAAS5L,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS+F,MAAMA,CAACa,CAAC,EAAEC,CAAC,EAAE;MACpB,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACD,CAAC,GAAG,CAAC;QACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACZ,CAAC,MAAM;QACL,IAAI,CAACD,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;MACZ;IACF;IAEAd,MAAM,CAACxE,SAAS,CAACqL,IAAI,GAAG,YAAY;MAClC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAEDb,MAAM,CAACxE,SAAS,CAACsL,IAAI,GAAG,YAAY;MAClC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACuL,IAAI,GAAG,UAAUlG,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDb,MAAM,CAACxE,SAAS,CAACwL,IAAI,GAAG,UAAUlG,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAACyL,aAAa,GAAG,UAAUC,EAAE,EAAE;MAC7C,OAAO,IAAIC,UAAU,CAAC,IAAI,CAACtG,CAAC,GAAGqG,EAAE,CAACrG,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC;IACrD,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAAC4L,OAAO,GAAG,YAAY;MACrC,OAAO,IAAIpH,MAAM,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IACnC,CAAC;IAEDd,MAAM,CAACxE,SAAS,CAAC6L,SAAS,GAAG,UAAUC,GAAG,EAAE;MAC1C,IAAI,CAACzG,CAAC,IAAIyG,GAAG,CAACvG,KAAK;MACnB,IAAI,CAACD,CAAC,IAAIwG,GAAG,CAACtG,MAAM;MACpB,OAAO,IAAI;IACb,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGqG,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAASpG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI6C,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IACzC,IAAI4F,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IACrC,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAIsN,aAAa,GAAGtN,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAI6F,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IACxC,IAAIuN,KAAK,GAAGvN,mBAAmB,CAAC,EAAE,CAAC;IACnC,IAAIwN,UAAU,GAAGxN,mBAAmB,CAAC,EAAE,CAAC;IAExC,SAASyN,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;MACpC/K,YAAY,CAACzC,IAAI,CAAC,IAAI,EAAEwN,MAAM,CAAC;MAC/B,IAAI,CAACtH,aAAa,GAAGV,OAAO,CAACW,SAAS;MACtC,IAAI,CAACsH,MAAM,GAAGlM,eAAe,CAACQ,oBAAoB;MAClD,IAAI,CAACuE,KAAK,GAAG,EAAE;MACf,IAAI,CAAC6C,KAAK,GAAG,EAAE;MACf,IAAI,CAACuE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACJ,MAAM,GAAGA,MAAM;MAEpB,IAAIC,IAAI,IAAI,IAAI,IAAIA,IAAI,YAAYL,aAAa,EAAE;QACjD,IAAI,CAACjH,YAAY,GAAGsH,IAAI;MAC1B,CAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,YAAYI,MAAM,EAAE;QACjD,IAAI,CAAC1H,YAAY,GAAGsH,IAAI,CAACtH,YAAY;MACvC;IACF;IAEAoH,MAAM,CAAClM,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACV,YAAY,CAACtB,SAAS,CAAC;IACxD,KAAK,IAAIiC,IAAI,IAAIX,YAAY,EAAE;MAC7B4K,MAAM,CAACjK,IAAI,CAAC,GAAGX,YAAY,CAACW,IAAI,CAAC;IACnC;IAEAiK,MAAM,CAAClM,SAAS,CAACiI,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACD,KAAK;IACnB,CAAC;IAEDkE,MAAM,CAAClM,SAAS,CAACyF,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAED+G,MAAM,CAAClM,SAAS,CAACmD,eAAe,GAAG,YAAY;MAC7C,OAAO,IAAI,CAAC2B,YAAY;IAC1B,CAAC;IAEDoH,MAAM,CAAClM,SAAS,CAACsD,SAAS,GAAG,YAAY;MACvC,OAAO,IAAI,CAAC6I,MAAM;IACpB,CAAC;IAEDD,MAAM,CAAClM,SAAS,CAACgJ,OAAO,GAAG,YAAY;MACrC,OAAO,IAAI,CAACW,IAAI;IAClB,CAAC;IAEDuC,MAAM,CAAClM,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MACtC,OAAO,IAAI,CAACuD,KAAK;IACnB,CAAC;IAEDP,MAAM,CAAClM,SAAS,CAACiJ,MAAM,GAAG,YAAY;MACpC,OAAO,IAAI,CAACW,GAAG;IACjB,CAAC;IAEDsC,MAAM,CAAClM,SAAS,CAACmJ,SAAS,GAAG,YAAY;MACvC,OAAO,IAAI,CAACuD,MAAM;IACpB,CAAC;IAEDR,MAAM,CAAClM,SAAS,CAACuM,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAACA,WAAW;IACzB,CAAC;IAEDL,MAAM,CAAClM,SAAS,CAAC2H,GAAG,GAAG,UAAUgF,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC7D,IAAID,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;QAC5C,IAAIC,OAAO,GAAGH,IAAI;QAClB,IAAI,IAAI,CAAC7H,YAAY,IAAI,IAAI,EAAE;UAC7B,MAAM,yBAAyB;QACjC;QACA,IAAI,IAAI,CAACmD,QAAQ,CAAC,CAAC,CAAC8E,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;UACzC,MAAM,wBAAwB;QAChC;QACAA,OAAO,CAAClH,KAAK,GAAG,IAAI;QACpB,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAACZ,IAAI,CAACyF,OAAO,CAAC;QAE7B,OAAOA,OAAO;MAChB,CAAC,MAAM;QACL,IAAIE,OAAO,GAAGL,IAAI;QAClB,IAAI,EAAE,IAAI,CAAC1E,QAAQ,CAAC,CAAC,CAAC8E,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC3E,QAAQ,CAAC,CAAC,CAAC8E,OAAO,CAACF,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAC3F,MAAM,gCAAgC;QACxC;QAEA,IAAI,EAAED,UAAU,CAAChH,KAAK,IAAIiH,UAAU,CAACjH,KAAK,IAAIgH,UAAU,CAAChH,KAAK,IAAI,IAAI,CAAC,EAAE;UACvE,MAAM,iCAAiC;QACzC;QAEA,IAAIgH,UAAU,CAAChH,KAAK,IAAIiH,UAAU,CAACjH,KAAK,EAAE;UACxC,OAAO,IAAI;QACb;;QAEA;QACAoH,OAAO,CAACtL,MAAM,GAAGkL,UAAU;QAC3BI,OAAO,CAACrL,MAAM,GAAGkL,UAAU;;QAE3B;QACAG,OAAO,CAAC5K,YAAY,GAAG,KAAK;;QAE5B;QACA,IAAI,CAACqD,QAAQ,CAAC,CAAC,CAAC4B,IAAI,CAAC2F,OAAO,CAAC;;QAE7B;QACAJ,UAAU,CAACzH,KAAK,CAACkC,IAAI,CAAC2F,OAAO,CAAC;QAE9B,IAAIH,UAAU,IAAID,UAAU,EAAE;UAC5BC,UAAU,CAAC1H,KAAK,CAACkC,IAAI,CAAC2F,OAAO,CAAC;QAChC;QAEA,OAAOA,OAAO;MAChB;IACF,CAAC;IAEDd,MAAM,CAAClM,SAAS,CAACiN,MAAM,GAAG,UAAUC,GAAG,EAAE;MACvC,IAAInK,IAAI,GAAGmK,GAAG;MACd,IAAIA,GAAG,YAAYzI,KAAK,EAAE;QACxB,IAAI1B,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,EAAEA,IAAI,CAAC6C,KAAK,IAAI,IAAI,IAAI7C,IAAI,CAAC6C,KAAK,IAAI,IAAI,CAAC,EAAE;UAC/C,MAAM,yBAAyB;QACjC;QACA,IAAI,IAAI,CAACd,YAAY,IAAI,IAAI,EAAE;UAC7B,MAAM,iCAAiC;QACzC;QACA;QACA,IAAIqI,gBAAgB,GAAGpK,IAAI,CAACoC,KAAK,CAACiI,KAAK,CAAC,CAAC;QACzC,IAAIlG,IAAI;QACR,IAAI/G,CAAC,GAAGgN,gBAAgB,CAAC7K,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BuI,IAAI,GAAGiG,gBAAgB,CAACxO,CAAC,CAAC;UAE1B,IAAIuI,IAAI,CAAC9E,YAAY,EAAE;YACrB,IAAI,CAAC0C,YAAY,CAACmI,MAAM,CAAC/F,IAAI,CAAC;UAChC,CAAC,MAAM;YACLA,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACqH,MAAM,CAAC/F,IAAI,CAAC;UAChC;QACF;;QAEA;QACA,IAAImG,KAAK,GAAG,IAAI,CAACrF,KAAK,CAAC+E,OAAO,CAAChK,IAAI,CAAC;QACpC,IAAIsK,KAAK,IAAI,CAAC,CAAC,EAAE;UACf,MAAM,8BAA8B;QACtC;QAEA,IAAI,CAACrF,KAAK,CAACsF,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAIH,GAAG,YAAYzL,KAAK,EAAE;QAC/B,IAAIyF,IAAI,GAAGgG,GAAG;QACd,IAAIhG,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,EAAEA,IAAI,CAACxF,MAAM,IAAI,IAAI,IAAIwF,IAAI,CAACvF,MAAM,IAAI,IAAI,CAAC,EAAE;UACjD,MAAM,+BAA+B;QACvC;QACA,IAAI,EAAEuF,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACvF,MAAM,CAACiE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACvF,MAAM,CAACiE,KAAK,IAAI,IAAI,CAAC,EAAE;UACvH,MAAM,wCAAwC;QAChD;QAEA,IAAI2H,WAAW,GAAGrG,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC;QACjD,IAAIsG,WAAW,GAAGtG,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC;QACjD,IAAI,EAAEqG,WAAW,GAAG,CAAC,CAAC,IAAIC,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE;UAC3C,MAAM,8CAA8C;QACtD;QAEAtG,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAACmI,MAAM,CAACC,WAAW,EAAE,CAAC,CAAC;QAExC,IAAIrG,IAAI,CAACvF,MAAM,IAAIuF,IAAI,CAACxF,MAAM,EAAE;UAC9BwF,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAACmI,MAAM,CAACE,WAAW,EAAE,CAAC,CAAC;QAC1C;QAEA,IAAIH,KAAK,GAAGnG,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACH,QAAQ,CAAC,CAAC,CAACsH,OAAO,CAAC7F,IAAI,CAAC;QACtD,IAAImG,KAAK,IAAI,CAAC,CAAC,EAAE;UACf,MAAM,2BAA2B;QACnC;QAEAnG,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC6H,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC;IAEDnB,MAAM,CAAClM,SAAS,CAACyN,aAAa,GAAG,YAAY;MAC3C,IAAI7D,GAAG,GAAGvF,OAAO,CAACa,SAAS;MAC3B,IAAIyE,IAAI,GAAGtF,OAAO,CAACa,SAAS;MAC5B,IAAIwI,OAAO;MACX,IAAIC,QAAQ;MACZ,IAAIrB,MAAM;MAEV,IAAItE,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC3B,IAAI9H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAIiP,KAAK,GAAG5F,KAAK,CAACrJ,CAAC,CAAC;QACpB+O,OAAO,GAAGE,KAAK,CAAC3E,MAAM,CAAC,CAAC;QACxB0E,QAAQ,GAAGC,KAAK,CAAC5E,OAAO,CAAC,CAAC;QAE1B,IAAIY,GAAG,GAAG8D,OAAO,EAAE;UACjB9D,GAAG,GAAG8D,OAAO;QACf;QAEA,IAAI/D,IAAI,GAAGgE,QAAQ,EAAE;UACnBhE,IAAI,GAAGgE,QAAQ;QACjB;MACF;;MAEA;MACA,IAAI/D,GAAG,IAAIvF,OAAO,CAACa,SAAS,EAAE;QAC5B,OAAO,IAAI;MACb;MAEA,IAAI8C,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACuK,WAAW,IAAIC,SAAS,EAAE;QACjDxB,MAAM,GAAGtE,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACuK,WAAW;MAC3C,CAAC,MAAM;QACLvB,MAAM,GAAG,IAAI,CAACA,MAAM;MACtB;MAEA,IAAI,CAAC3C,IAAI,GAAGA,IAAI,GAAG2C,MAAM;MACzB,IAAI,CAAC1C,GAAG,GAAGA,GAAG,GAAG0C,MAAM;;MAEvB;MACA,OAAO,IAAIN,KAAK,CAAC,IAAI,CAACrC,IAAI,EAAE,IAAI,CAACC,GAAG,CAAC;IACvC,CAAC;IAEDsC,MAAM,CAAClM,SAAS,CAAC8I,YAAY,GAAG,UAAUiF,SAAS,EAAE;MACnD;MACA,IAAIpE,IAAI,GAAGtF,OAAO,CAACa,SAAS;MAC5B,IAAIuH,KAAK,GAAG,CAACpI,OAAO,CAACa,SAAS;MAC9B,IAAI0E,GAAG,GAAGvF,OAAO,CAACa,SAAS;MAC3B,IAAIwH,MAAM,GAAG,CAACrI,OAAO,CAACa,SAAS;MAC/B,IAAIyI,QAAQ;MACZ,IAAIK,SAAS;MACb,IAAIN,OAAO;MACX,IAAIO,UAAU;MACd,IAAI3B,MAAM;MAEV,IAAItE,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI7H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAIiP,KAAK,GAAG5F,KAAK,CAACrJ,CAAC,CAAC;QAEpB,IAAIoP,SAAS,IAAIH,KAAK,CAACjI,KAAK,IAAI,IAAI,EAAE;UACpCiI,KAAK,CAAC9E,YAAY,CAAC,CAAC;QACtB;QACA6E,QAAQ,GAAGC,KAAK,CAAC5E,OAAO,CAAC,CAAC;QAC1BgF,SAAS,GAAGJ,KAAK,CAAC1E,QAAQ,CAAC,CAAC;QAC5BwE,OAAO,GAAGE,KAAK,CAAC3E,MAAM,CAAC,CAAC;QACxBgF,UAAU,GAAGL,KAAK,CAACzE,SAAS,CAAC,CAAC;QAE9B,IAAIQ,IAAI,GAAGgE,QAAQ,EAAE;UACnBhE,IAAI,GAAGgE,QAAQ;QACjB;QAEA,IAAIlB,KAAK,GAAGuB,SAAS,EAAE;UACrBvB,KAAK,GAAGuB,SAAS;QACnB;QAEA,IAAIpE,GAAG,GAAG8D,OAAO,EAAE;UACjB9D,GAAG,GAAG8D,OAAO;QACf;QAEA,IAAIhB,MAAM,GAAGuB,UAAU,EAAE;UACvBvB,MAAM,GAAGuB,UAAU;QACrB;MACF;MAEA,IAAIC,YAAY,GAAG,IAAI5J,UAAU,CAACqF,IAAI,EAAEC,GAAG,EAAE6C,KAAK,GAAG9C,IAAI,EAAE+C,MAAM,GAAG9C,GAAG,CAAC;MACxE,IAAID,IAAI,IAAItF,OAAO,CAACa,SAAS,EAAE;QAC7B,IAAI,CAACyE,IAAI,GAAG,IAAI,CAACwC,MAAM,CAACnD,OAAO,CAAC,CAAC;QACjC,IAAI,CAACyD,KAAK,GAAG,IAAI,CAACN,MAAM,CAACjD,QAAQ,CAAC,CAAC;QACnC,IAAI,CAACU,GAAG,GAAG,IAAI,CAACuC,MAAM,CAAClD,MAAM,CAAC,CAAC;QAC/B,IAAI,CAACyD,MAAM,GAAG,IAAI,CAACP,MAAM,CAAChD,SAAS,CAAC,CAAC;MACvC;MAEA,IAAInB,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACuK,WAAW,IAAIC,SAAS,EAAE;QACjDxB,MAAM,GAAGtE,KAAK,CAAC,CAAC,CAAC,CAAC1E,SAAS,CAAC,CAAC,CAACuK,WAAW;MAC3C,CAAC,MAAM;QACLvB,MAAM,GAAG,IAAI,CAACA,MAAM;MACtB;MAEA,IAAI,CAAC3C,IAAI,GAAGuE,YAAY,CAAC7I,CAAC,GAAGiH,MAAM;MACnC,IAAI,CAACG,KAAK,GAAGyB,YAAY,CAAC7I,CAAC,GAAG6I,YAAY,CAAC3I,KAAK,GAAG+G,MAAM;MACzD,IAAI,CAAC1C,GAAG,GAAGsE,YAAY,CAAC5I,CAAC,GAAGgH,MAAM;MAClC,IAAI,CAACI,MAAM,GAAGwB,YAAY,CAAC5I,CAAC,GAAG4I,YAAY,CAAC1I,MAAM,GAAG8G,MAAM;IAC7D,CAAC;IAEDJ,MAAM,CAACiC,eAAe,GAAG,UAAUnG,KAAK,EAAE;MACxC,IAAI2B,IAAI,GAAGtF,OAAO,CAACa,SAAS;MAC5B,IAAIuH,KAAK,GAAG,CAACpI,OAAO,CAACa,SAAS;MAC9B,IAAI0E,GAAG,GAAGvF,OAAO,CAACa,SAAS;MAC3B,IAAIwH,MAAM,GAAG,CAACrI,OAAO,CAACa,SAAS;MAC/B,IAAIyI,QAAQ;MACZ,IAAIK,SAAS;MACb,IAAIN,OAAO;MACX,IAAIO,UAAU;MAEd,IAAI9N,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAIiP,KAAK,GAAG5F,KAAK,CAACrJ,CAAC,CAAC;QACpBgP,QAAQ,GAAGC,KAAK,CAAC5E,OAAO,CAAC,CAAC;QAC1BgF,SAAS,GAAGJ,KAAK,CAAC1E,QAAQ,CAAC,CAAC;QAC5BwE,OAAO,GAAGE,KAAK,CAAC3E,MAAM,CAAC,CAAC;QACxBgF,UAAU,GAAGL,KAAK,CAACzE,SAAS,CAAC,CAAC;QAE9B,IAAIQ,IAAI,GAAGgE,QAAQ,EAAE;UACnBhE,IAAI,GAAGgE,QAAQ;QACjB;QAEA,IAAIlB,KAAK,GAAGuB,SAAS,EAAE;UACrBvB,KAAK,GAAGuB,SAAS;QACnB;QAEA,IAAIpE,GAAG,GAAG8D,OAAO,EAAE;UACjB9D,GAAG,GAAG8D,OAAO;QACf;QAEA,IAAIhB,MAAM,GAAGuB,UAAU,EAAE;UACvBvB,MAAM,GAAGuB,UAAU;QACrB;MACF;MAEA,IAAIC,YAAY,GAAG,IAAI5J,UAAU,CAACqF,IAAI,EAAEC,GAAG,EAAE6C,KAAK,GAAG9C,IAAI,EAAE+C,MAAM,GAAG9C,GAAG,CAAC;MAExE,OAAOsE,YAAY;IACrB,CAAC;IAEDhC,MAAM,CAAClM,SAAS,CAACwJ,qBAAqB,GAAG,YAAY;MACnD,IAAI,IAAI,IAAI,IAAI,CAAC1E,YAAY,CAAC1B,OAAO,CAAC,CAAC,EAAE;QACvC,OAAO,CAAC;MACV,CAAC,MAAM;QACL,OAAO,IAAI,CAAC+I,MAAM,CAAC3C,qBAAqB,CAAC,CAAC;MAC5C;IACF,CAAC;IAED0C,MAAM,CAAClM,SAAS,CAACoI,gBAAgB,GAAG,YAAY;MAC9C,IAAI,IAAI,CAACrD,aAAa,IAAIV,OAAO,CAACW,SAAS,EAAE;QAC3C,MAAM,eAAe;MACvB;MACA,OAAO,IAAI,CAACD,aAAa;IAC3B,CAAC;IAEDmH,MAAM,CAAClM,SAAS,CAACqI,iBAAiB,GAAG,YAAY;MAC/C,IAAIzD,IAAI,GAAG,CAAC;MACZ,IAAIoD,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI7H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MAEpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAIiP,KAAK,GAAG5F,KAAK,CAACrJ,CAAC,CAAC;QACpBiG,IAAI,IAAIgJ,KAAK,CAACvF,iBAAiB,CAAC,CAAC;MACnC;MAEA,IAAIzD,IAAI,IAAI,CAAC,EAAE;QACb,IAAI,CAACG,aAAa,GAAG3E,eAAe,CAACY,wBAAwB;MAC/D,CAAC,MAAM;QACL,IAAI,CAAC+D,aAAa,GAAGH,IAAI,GAAGd,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC+D,KAAK,CAAC1F,MAAM,CAAC;MAC1D;MAEA,OAAO,IAAI,CAACyC,aAAa;IAC3B,CAAC;IAEDmH,MAAM,CAAClM,SAAS,CAACoO,eAAe,GAAG,YAAY;MAC7C,IAAIjH,IAAI,GAAG,IAAI;MACf,IAAI,IAAI,CAACa,KAAK,CAAC1F,MAAM,IAAI,CAAC,EAAE;QAC1B,IAAI,CAACiK,WAAW,GAAG,IAAI;QACvB;MACF;MAEA,IAAI8B,KAAK,GAAG,IAAIpC,UAAU,CAAC,CAAC;MAC5B,IAAIqC,OAAO,GAAG,IAAI5G,GAAG,CAAC,CAAC;MACvB,IAAI6G,WAAW,GAAG,IAAI,CAACvG,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIwG,aAAa;MACjB,IAAIC,eAAe;MACnB,IAAIC,cAAc,GAAGH,WAAW,CAAC3G,YAAY,CAAC,CAAC;MAC/C8G,cAAc,CAACtH,OAAO,CAAC,UAAUrE,IAAI,EAAE;QACrCsL,KAAK,CAAChH,IAAI,CAACtE,IAAI,CAAC;QAChBuL,OAAO,CAAC3G,GAAG,CAAC5E,IAAI,CAAC;MACnB,CAAC,CAAC;MAEF,OAAOsL,KAAK,CAAC/L,MAAM,KAAK,CAAC,EAAE;QACzBiM,WAAW,GAAGF,KAAK,CAACM,KAAK,CAAC,CAAC;;QAE3B;QACAH,aAAa,GAAGD,WAAW,CAAC9I,QAAQ,CAAC,CAAC;QACtC,IAAIb,IAAI,GAAG4J,aAAa,CAAClM,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,IAAI,EAAEjG,CAAC,EAAE,EAAE;UAC7B,IAAIiQ,YAAY,GAAGJ,aAAa,CAAC7P,CAAC,CAAC;UACnC8P,eAAe,GAAGG,YAAY,CAAC5L,kBAAkB,CAACuL,WAAW,EAAE,IAAI,CAAC;;UAEpE;UACA,IAAIE,eAAe,IAAI,IAAI,IAAI,CAACH,OAAO,CAACO,GAAG,CAACJ,eAAe,CAAC,EAAE;YAC5D,IAAIK,kBAAkB,GAAGL,eAAe,CAAC7G,YAAY,CAAC,CAAC;YAEvDkH,kBAAkB,CAAC1H,OAAO,CAAC,UAAUrE,IAAI,EAAE;cACzCsL,KAAK,CAAChH,IAAI,CAACtE,IAAI,CAAC;cAChBuL,OAAO,CAAC3G,GAAG,CAAC5E,IAAI,CAAC;YACnB,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAI,CAACwJ,WAAW,GAAG,KAAK;MAExB,IAAI+B,OAAO,CAAC1J,IAAI,IAAI,IAAI,CAACoD,KAAK,CAAC1F,MAAM,EAAE;QACrC,IAAIyM,sBAAsB,GAAG,CAAC;QAE9BT,OAAO,CAAClH,OAAO,CAAC,UAAU4H,WAAW,EAAE;UACrC,IAAIA,WAAW,CAACpJ,KAAK,IAAIuB,IAAI,EAAE;YAC7B4H,sBAAsB,EAAE;UAC1B;QACF,CAAC,CAAC;QAEF,IAAIA,sBAAsB,IAAI,IAAI,CAAC/G,KAAK,CAAC1F,MAAM,EAAE;UAC/C,IAAI,CAACiK,WAAW,GAAG,IAAI;QACzB;MACF;IACF,CAAC;IAEDnO,MAAM,CAACD,OAAO,GAAG+N,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAAS9N,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIyN,MAAM;IACV,IAAIzK,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAASsN,aAAaA,CAACkD,MAAM,EAAE;MAC7B/C,MAAM,GAAGzN,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,IAAI,CAACwQ,MAAM,GAAGA,MAAM;MAEpB,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAAC/J,KAAK,GAAG,EAAE;IACjB;IAEA4G,aAAa,CAAC/L,SAAS,CAACmP,OAAO,GAAG,YAAY;MAC5C,IAAIC,MAAM,GAAG,IAAI,CAACH,MAAM,CAACI,QAAQ,CAAC,CAAC;MACnC,IAAIC,KAAK,GAAG,IAAI,CAACL,MAAM,CAACnC,OAAO,CAAC,IAAI,CAAC;MACrC,IAAI7O,IAAI,GAAG,IAAI,CAAC0J,GAAG,CAACyH,MAAM,EAAEE,KAAK,CAAC;MAClC,IAAI,CAACC,YAAY,CAACtR,IAAI,CAAC;MACvB,OAAO,IAAI,CAACuR,SAAS;IACvB,CAAC;IAEDzD,aAAa,CAAC/L,SAAS,CAAC2H,GAAG,GAAG,UAAU0H,QAAQ,EAAEI,UAAU,EAAEzC,OAAO,EAAEJ,UAAU,EAAEC,UAAU,EAAE;MAC7F;MACA,IAAIG,OAAO,IAAI,IAAI,IAAIJ,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;QAC/D,IAAIwC,QAAQ,IAAI,IAAI,EAAE;UACpB,MAAM,gBAAgB;QACxB;QACA,IAAII,UAAU,IAAI,IAAI,EAAE;UACtB,MAAM,sBAAsB;QAC9B;QACA,IAAI,IAAI,CAACP,MAAM,CAACnC,OAAO,CAACsC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;UACtC,MAAM,kCAAkC;QAC1C;QAEA,IAAI,CAACH,MAAM,CAAC7H,IAAI,CAACgI,QAAQ,CAAC;QAE1B,IAAIA,QAAQ,CAAClD,MAAM,IAAI,IAAI,EAAE;UAC3B,MAAM,uBAAuB;QAC/B;QACA,IAAIsD,UAAU,CAAC9J,KAAK,IAAI,IAAI,EAAE;UAC5B,MAAM,sBAAsB;QAC9B;QAEA0J,QAAQ,CAAClD,MAAM,GAAGsD,UAAU;QAC5BA,UAAU,CAAC9J,KAAK,GAAG0J,QAAQ;QAE3B,OAAOA,QAAQ;MACjB,CAAC,MAAM;QACL;QACAxC,UAAU,GAAGG,OAAO;QACpBJ,UAAU,GAAG6C,UAAU;QACvBzC,OAAO,GAAGqC,QAAQ;QAClB,IAAIK,WAAW,GAAG9C,UAAU,CAACvJ,QAAQ,CAAC,CAAC;QACvC,IAAIsM,WAAW,GAAG9C,UAAU,CAACxJ,QAAQ,CAAC,CAAC;QAEvC,IAAI,EAAEqM,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACvM,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UACnE,MAAM,+BAA+B;QACvC;QACA,IAAI,EAAEwM,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACxM,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UACnE,MAAM,+BAA+B;QACvC;QAEA,IAAIuM,WAAW,IAAIC,WAAW,EAAE;UAC9B3C,OAAO,CAAC5K,YAAY,GAAG,KAAK;UAC5B,OAAOsN,WAAW,CAAC/H,GAAG,CAACqF,OAAO,EAAEJ,UAAU,EAAEC,UAAU,CAAC;QACzD,CAAC,MAAM;UACLG,OAAO,CAAC5K,YAAY,GAAG,IAAI;;UAE3B;UACA4K,OAAO,CAACtL,MAAM,GAAGkL,UAAU;UAC3BI,OAAO,CAACrL,MAAM,GAAGkL,UAAU;;UAE3B;UACA,IAAI,IAAI,CAAC1H,KAAK,CAAC4H,OAAO,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;YACpC,MAAM,wCAAwC;UAChD;UAEA,IAAI,CAAC7H,KAAK,CAACkC,IAAI,CAAC2F,OAAO,CAAC;;UAExB;UACA,IAAI,EAAEA,OAAO,CAACtL,MAAM,IAAI,IAAI,IAAIsL,OAAO,CAACrL,MAAM,IAAI,IAAI,CAAC,EAAE;YACvD,MAAM,oCAAoC;UAC5C;UAEA,IAAI,EAAEqL,OAAO,CAACtL,MAAM,CAACyD,KAAK,CAAC4H,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAIA,OAAO,CAACrL,MAAM,CAACwD,KAAK,CAAC4H,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACjG,MAAM,sDAAsD;UAC9D;UAEAA,OAAO,CAACtL,MAAM,CAACyD,KAAK,CAACkC,IAAI,CAAC2F,OAAO,CAAC;UAClCA,OAAO,CAACrL,MAAM,CAACwD,KAAK,CAACkC,IAAI,CAAC2F,OAAO,CAAC;UAElC,OAAOA,OAAO;QAChB;MACF;IACF,CAAC;IAEDjB,aAAa,CAAC/L,SAAS,CAACiN,MAAM,GAAG,UAAU2C,IAAI,EAAE;MAC/C,IAAIA,IAAI,YAAY1D,MAAM,EAAE;QAC1B,IAAIjJ,KAAK,GAAG2M,IAAI;QAChB,IAAI3M,KAAK,CAACE,eAAe,CAAC,CAAC,IAAI,IAAI,EAAE;UACnC,MAAM,6BAA6B;QACrC;QACA,IAAI,EAAEF,KAAK,IAAI,IAAI,CAACuM,SAAS,IAAIvM,KAAK,CAACkJ,MAAM,IAAI,IAAI,IAAIlJ,KAAK,CAACkJ,MAAM,CAACrH,YAAY,IAAI,IAAI,CAAC,EAAE;UAC3F,MAAM,sBAAsB;QAC9B;;QAEA;QACA,IAAIqI,gBAAgB,GAAG,EAAE;QAEzBA,gBAAgB,GAAGA,gBAAgB,CAAC0C,MAAM,CAAC5M,KAAK,CAACwC,QAAQ,CAAC,CAAC,CAAC;QAE5D,IAAIyB,IAAI;QACR,IAAI/G,CAAC,GAAGgN,gBAAgB,CAAC7K,MAAM;QAC/B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BuI,IAAI,GAAGiG,gBAAgB,CAACxO,CAAC,CAAC;UAC1BsE,KAAK,CAACgK,MAAM,CAAC/F,IAAI,CAAC;QACpB;;QAEA;QACA,IAAI4I,gBAAgB,GAAG,EAAE;QAEzBA,gBAAgB,GAAGA,gBAAgB,CAACD,MAAM,CAAC5M,KAAK,CAACgF,QAAQ,CAAC,CAAC,CAAC;QAE5D,IAAIlF,IAAI;QACR5C,CAAC,GAAG2P,gBAAgB,CAACxN,MAAM;QAC3B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BoE,IAAI,GAAG+M,gBAAgB,CAACnR,CAAC,CAAC;UAC1BsE,KAAK,CAACgK,MAAM,CAAClK,IAAI,CAAC;QACpB;;QAEA;QACA,IAAIE,KAAK,IAAI,IAAI,CAACuM,SAAS,EAAE;UAC3B,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC;QACzB;;QAEA;QACA,IAAIlC,KAAK,GAAG,IAAI,CAAC6B,MAAM,CAACnC,OAAO,CAAC9J,KAAK,CAAC;QACtC,IAAI,CAACiM,MAAM,CAAC5B,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;QAE5B;QACApK,KAAK,CAACkJ,MAAM,GAAG,IAAI;MACrB,CAAC,MAAM,IAAIyD,IAAI,YAAYnO,KAAK,EAAE;QAChCyF,IAAI,GAAG0I,IAAI;QACX,IAAI1I,IAAI,IAAI,IAAI,EAAE;UAChB,MAAM,eAAe;QACvB;QACA,IAAI,CAACA,IAAI,CAAC9E,YAAY,EAAE;UACtB,MAAM,0BAA0B;QAClC;QACA,IAAI,EAAE8E,IAAI,CAACxF,MAAM,IAAI,IAAI,IAAIwF,IAAI,CAACvF,MAAM,IAAI,IAAI,CAAC,EAAE;UACjD,MAAM,+BAA+B;QACvC;;QAEA;;QAEA,IAAI,EAAEuF,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIA,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;UACrF,MAAM,8CAA8C;QACtD;QAEA,IAAImG,KAAK,GAAGnG,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC;QAC3CA,IAAI,CAACxF,MAAM,CAACyD,KAAK,CAACmI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QAClCA,KAAK,GAAGnG,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC;QACvCA,IAAI,CAACvF,MAAM,CAACwD,KAAK,CAACmI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;;QAElC;;QAEA,IAAI,EAAEnG,IAAI,CAACxF,MAAM,CAACkE,KAAK,IAAI,IAAI,IAAIsB,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;UAC/E,MAAM,kDAAkD;QAC1D;QACA,IAAI+D,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;UACjE,MAAM,yCAAyC;QACjD;QAEA,IAAImG,KAAK,GAAGnG,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAAC4H,OAAO,CAAC7F,IAAI,CAAC;QACnEA,IAAI,CAACxF,MAAM,CAACkE,KAAK,CAACzC,eAAe,CAAC,CAAC,CAACgC,KAAK,CAACmI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC5D;IACF,CAAC;IAEDtB,aAAa,CAAC/L,SAAS,CAAC8I,YAAY,GAAG,YAAY;MACjD,IAAI,CAAC0G,SAAS,CAAC1G,YAAY,CAAC,IAAI,CAAC;IACnC,CAAC;IAEDiD,aAAa,CAAC/L,SAAS,CAAC+P,SAAS,GAAG,YAAY;MAC9C,OAAO,IAAI,CAACb,MAAM;IACpB,CAAC;IAEDnD,aAAa,CAAC/L,SAAS,CAACgQ,WAAW,GAAG,YAAY;MAChD,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,EAAE;QACzB,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIhB,MAAM,GAAG,IAAI,CAACa,SAAS,CAAC,CAAC;QAC7B,IAAI5P,CAAC,GAAG+O,MAAM,CAAC5M,MAAM;QACrB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;UAC1BuR,QAAQ,GAAGA,QAAQ,CAACL,MAAM,CAACX,MAAM,CAACvQ,CAAC,CAAC,CAACsJ,QAAQ,CAAC,CAAC,CAAC;QAClD;QACA,IAAI,CAACgI,QAAQ,GAAGC,QAAQ;MAC1B;MACA,OAAO,IAAI,CAACD,QAAQ;IACtB,CAAC;IAEDlE,aAAa,CAAC/L,SAAS,CAACmQ,aAAa,GAAG,YAAY;MAClD,IAAI,CAACF,QAAQ,GAAG,IAAI;IACtB,CAAC;IAEDlE,aAAa,CAAC/L,SAAS,CAACoQ,aAAa,GAAG,YAAY;MAClD,IAAI,CAACC,QAAQ,GAAG,IAAI;IACtB,CAAC;IAEDtE,aAAa,CAAC/L,SAAS,CAACsQ,+BAA+B,GAAG,YAAY;MACpE,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACxC,CAAC;IAEDxE,aAAa,CAAC/L,SAAS,CAACwQ,WAAW,GAAG,YAAY;MAChD,IAAI,IAAI,CAACH,QAAQ,IAAI,IAAI,EAAE;QACzB,IAAIpJ,QAAQ,GAAG,EAAE;QACjB,IAAIiI,MAAM,GAAG,IAAI,CAACa,SAAS,CAAC,CAAC;QAC7B,IAAI5P,CAAC,GAAG+O,MAAM,CAAC5M,MAAM;QACrB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,MAAM,CAAC5M,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACtCsI,QAAQ,GAAGA,QAAQ,CAAC4I,MAAM,CAACX,MAAM,CAACvQ,CAAC,CAAC,CAAC8G,QAAQ,CAAC,CAAC,CAAC;QAClD;QAEAwB,QAAQ,GAAGA,QAAQ,CAAC4I,MAAM,CAAC,IAAI,CAAC1K,KAAK,CAAC;QAEtC,IAAI,CAACkL,QAAQ,GAAGpJ,QAAQ;MAC1B;MACA,OAAO,IAAI,CAACoJ,QAAQ;IACtB,CAAC;IAEDtE,aAAa,CAAC/L,SAAS,CAACyQ,6BAA6B,GAAG,YAAY;MAClE,OAAO,IAAI,CAACF,0BAA0B;IACxC,CAAC;IAEDxE,aAAa,CAAC/L,SAAS,CAAC0Q,6BAA6B,GAAG,UAAUR,QAAQ,EAAE;MAC1E,IAAI,IAAI,CAACK,0BAA0B,IAAI,IAAI,EAAE;QAC3C,MAAM,eAAe;MACvB;MAEA,IAAI,CAACA,0BAA0B,GAAGL,QAAQ;IAC5C,CAAC;IAEDnE,aAAa,CAAC/L,SAAS,CAACoD,OAAO,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACoM,SAAS;IACvB,CAAC;IAEDzD,aAAa,CAAC/L,SAAS,CAACuP,YAAY,GAAG,UAAUtM,KAAK,EAAE;MACtD,IAAIA,KAAK,CAACE,eAAe,CAAC,CAAC,IAAI,IAAI,EAAE;QACnC,MAAM,6BAA6B;MACrC;MAEA,IAAI,CAACqM,SAAS,GAAGvM,KAAK;MACtB;MACA,IAAIA,KAAK,CAACkJ,MAAM,IAAI,IAAI,EAAE;QACxBlJ,KAAK,CAACkJ,MAAM,GAAG,IAAI,CAAC8C,MAAM,CAACnC,OAAO,CAAC,WAAW,CAAC;MACjD;IACF,CAAC;IAEDf,aAAa,CAAC/L,SAAS,CAAC2Q,SAAS,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAC1B,MAAM;IACpB,CAAC;IAEDlD,aAAa,CAAC/L,SAAS,CAAC4Q,oBAAoB,GAAG,UAAUC,SAAS,EAAEC,UAAU,EAAE;MAC9E,IAAI,EAAED,SAAS,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,CAAC,EAAE;QAC9C,MAAM,eAAe;MACvB;MAEA,IAAID,SAAS,IAAIC,UAAU,EAAE;QAC3B,OAAO,IAAI;MACb;MACA;MACA,IAAIC,UAAU,GAAGF,SAAS,CAACxN,QAAQ,CAAC,CAAC;MACrC,IAAIoM,UAAU;MAEd,GAAG;QACDA,UAAU,GAAGsB,UAAU,CAACzN,SAAS,CAAC,CAAC;QAEnC,IAAImM,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;QAEA,IAAIA,UAAU,IAAIqB,UAAU,EAAE;UAC5B,OAAO,IAAI;QACb;QAEAC,UAAU,GAAGtB,UAAU,CAACpM,QAAQ,CAAC,CAAC;QAClC,IAAI0N,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;MACF,CAAC,QAAQ,IAAI;MACb;MACAA,UAAU,GAAGD,UAAU,CAACzN,QAAQ,CAAC,CAAC;MAElC,GAAG;QACDoM,UAAU,GAAGsB,UAAU,CAACzN,SAAS,CAAC,CAAC;QAEnC,IAAImM,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;QAEA,IAAIA,UAAU,IAAIoB,SAAS,EAAE;UAC3B,OAAO,IAAI;QACb;QAEAE,UAAU,GAAGtB,UAAU,CAACpM,QAAQ,CAAC,CAAC;QAClC,IAAI0N,UAAU,IAAI,IAAI,EAAE;UACtB;QACF;MACF,CAAC,QAAQ,IAAI;MAEb,OAAO,KAAK;IACd,CAAC;IAEDhF,aAAa,CAAC/L,SAAS,CAACgR,yBAAyB,GAAG,YAAY;MAC9D,IAAI9J,IAAI;MACR,IAAI0F,UAAU;MACd,IAAIC,UAAU;MACd,IAAIoE,mBAAmB;MACvB,IAAIC,mBAAmB;MAEvB,IAAI/L,KAAK,GAAG,IAAI,CAACqL,WAAW,CAAC,CAAC;MAC9B,IAAIrQ,CAAC,GAAGgF,KAAK,CAAC7C,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BuI,IAAI,GAAG/B,KAAK,CAACxG,CAAC,CAAC;QAEfiO,UAAU,GAAG1F,IAAI,CAACxF,MAAM;QACxBmL,UAAU,GAAG3F,IAAI,CAACvF,MAAM;QACxBuF,IAAI,CAACzE,GAAG,GAAG,IAAI;QACfyE,IAAI,CAACvE,WAAW,GAAGiK,UAAU;QAC7B1F,IAAI,CAACrE,WAAW,GAAGgK,UAAU;QAE7B,IAAID,UAAU,IAAIC,UAAU,EAAE;UAC5B3F,IAAI,CAACzE,GAAG,GAAGmK,UAAU,CAACvJ,QAAQ,CAAC,CAAC;UAChC;QACF;QAEA4N,mBAAmB,GAAGrE,UAAU,CAACvJ,QAAQ,CAAC,CAAC;QAE3C,OAAO6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;UACvByE,IAAI,CAACrE,WAAW,GAAGgK,UAAU;UAC7BqE,mBAAmB,GAAGrE,UAAU,CAACxJ,QAAQ,CAAC,CAAC;UAE3C,OAAO6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;YACvB,IAAIyO,mBAAmB,IAAID,mBAAmB,EAAE;cAC9C/J,IAAI,CAACzE,GAAG,GAAGyO,mBAAmB;cAC9B;YACF;YAEA,IAAIA,mBAAmB,IAAI,IAAI,CAAC1B,SAAS,EAAE;cACzC;YACF;YAEA,IAAItI,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;cACpB,MAAM,eAAe;YACvB;YACAyE,IAAI,CAACrE,WAAW,GAAGqO,mBAAmB,CAAC5N,SAAS,CAAC,CAAC;YAClD4N,mBAAmB,GAAGhK,IAAI,CAACrE,WAAW,CAACQ,QAAQ,CAAC,CAAC;UACnD;UAEA,IAAI4N,mBAAmB,IAAI,IAAI,CAACzB,SAAS,EAAE;YACzC;UACF;UAEA,IAAItI,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;YACpByE,IAAI,CAACvE,WAAW,GAAGsO,mBAAmB,CAAC3N,SAAS,CAAC,CAAC;YAClD2N,mBAAmB,GAAG/J,IAAI,CAACvE,WAAW,CAACU,QAAQ,CAAC,CAAC;UACnD;QACF;QAEA,IAAI6D,IAAI,CAACzE,GAAG,IAAI,IAAI,EAAE;UACpB,MAAM,eAAe;QACvB;MACF;IACF,CAAC;IAEDsJ,aAAa,CAAC/L,SAAS,CAACmR,wBAAwB,GAAG,UAAUN,SAAS,EAAEC,UAAU,EAAE;MAClF,IAAID,SAAS,IAAIC,UAAU,EAAE;QAC3B,OAAOD,SAAS,CAACxN,QAAQ,CAAC,CAAC;MAC7B;MACA,IAAI+N,eAAe,GAAGP,SAAS,CAACxN,QAAQ,CAAC,CAAC;MAE1C,GAAG;QACD,IAAI+N,eAAe,IAAI,IAAI,EAAE;UAC3B;QACF;QACA,IAAIC,gBAAgB,GAAGP,UAAU,CAACzN,QAAQ,CAAC,CAAC;QAE5C,GAAG;UACD,IAAIgO,gBAAgB,IAAI,IAAI,EAAE;YAC5B;UACF;UAEA,IAAIA,gBAAgB,IAAID,eAAe,EAAE;YACvC,OAAOC,gBAAgB;UACzB;UACAA,gBAAgB,GAAGA,gBAAgB,CAAC/N,SAAS,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC;QAC5D,CAAC,QAAQ,IAAI;QAEb+N,eAAe,GAAGA,eAAe,CAAC9N,SAAS,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC;MAC1D,CAAC,QAAQ,IAAI;MAEb,OAAO+N,eAAe;IACxB,CAAC;IAEDrF,aAAa,CAAC/L,SAAS,CAACsR,uBAAuB,GAAG,UAAUrO,KAAK,EAAEsO,KAAK,EAAE;MACxE,IAAItO,KAAK,IAAI,IAAI,IAAIsO,KAAK,IAAI,IAAI,EAAE;QAClCtO,KAAK,GAAG,IAAI,CAACuM,SAAS;QACtB+B,KAAK,GAAG,CAAC;MACX;MACA,IAAIxO,IAAI;MAER,IAAIiF,KAAK,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC;MAC5B,IAAI9H,CAAC,GAAG6H,KAAK,CAAC1F,MAAM;MACpB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;QACfoE,IAAI,CAACkC,kBAAkB,GAAGsM,KAAK;QAE/B,IAAIxO,IAAI,CAAC4C,KAAK,IAAI,IAAI,EAAE;UACtB,IAAI,CAAC2L,uBAAuB,CAACvO,IAAI,CAAC4C,KAAK,EAAE4L,KAAK,GAAG,CAAC,CAAC;QACrD;MACF;IACF,CAAC;IAEDxF,aAAa,CAAC/L,SAAS,CAACwR,mBAAmB,GAAG,YAAY;MACxD,IAAItK,IAAI;MACR,IAAIuK,aAAa,GAAG,EAAE;MAEtB,IAAItR,CAAC,GAAG,IAAI,CAACgF,KAAK,CAAC7C,MAAM;MACzB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1BuI,IAAI,GAAG,IAAI,CAAC/B,KAAK,CAACxG,CAAC,CAAC;QAEpB,IAAI,IAAI,CAACiS,oBAAoB,CAAC1J,IAAI,CAACxF,MAAM,EAAEwF,IAAI,CAACvF,MAAM,CAAC,EAAE;UACvD8P,aAAa,CAACpK,IAAI,CAACH,IAAI,CAAC;QAC1B;MACF;;MAEA;MACA,KAAK,IAAIvI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8S,aAAa,CAACnP,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC7C,IAAI,CAACsO,MAAM,CAACwE,aAAa,CAAC9S,CAAC,CAAC,CAAC;MAC/B;;MAEA;MACA,OAAO,KAAK;IACd,CAAC;IAEDP,MAAM,CAACD,OAAO,GAAG4N,aAAa;;IAE9B;EAAM,CAAC,IACP;EACA,KAAO,UAAS3N,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;AACA;AACA;AACA;AACA;AACA;IAEA,IAAIuN,KAAK,GAAGvN,mBAAmB,CAAC,EAAE,CAAC;IAEnC,SAAS8C,SAASA,CAAA,EAAG,CAAC;;IAEtB;AACA;AACA;AACA;AACA;AACA;AACA;IACAA,SAAS,CAACmQ,oBAAoB,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,gBAAgB,EAAE;MACxF,IAAI,CAACH,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC,EAAE;QAC5B,MAAM,eAAe;MACvB;MAEA,IAAII,UAAU,GAAG,IAAIvO,KAAK,CAAC,CAAC,CAAC;MAE7B,IAAI,CAACwO,mCAAmC,CAACN,KAAK,EAAEC,KAAK,EAAEI,UAAU,CAAC;MAElEH,aAAa,CAAC,CAAC,CAAC,GAAG/N,IAAI,CAACoO,GAAG,CAACP,KAAK,CAACzI,QAAQ,CAAC,CAAC,EAAE0I,KAAK,CAAC1I,QAAQ,CAAC,CAAC,CAAC,GAAGpF,IAAI,CAACqO,GAAG,CAACR,KAAK,CAACtM,CAAC,EAAEuM,KAAK,CAACvM,CAAC,CAAC;MAC5FwM,aAAa,CAAC,CAAC,CAAC,GAAG/N,IAAI,CAACoO,GAAG,CAACP,KAAK,CAACxI,SAAS,CAAC,CAAC,EAAEyI,KAAK,CAACzI,SAAS,CAAC,CAAC,CAAC,GAAGrF,IAAI,CAACqO,GAAG,CAACR,KAAK,CAACrM,CAAC,EAAEsM,KAAK,CAACtM,CAAC,CAAC;;MAE9F;MACA,IAAIqM,KAAK,CAACtG,IAAI,CAAC,CAAC,IAAIuG,KAAK,CAACvG,IAAI,CAAC,CAAC,IAAIsG,KAAK,CAACzI,QAAQ,CAAC,CAAC,IAAI0I,KAAK,CAAC1I,QAAQ,CAAC,CAAC,EAAE;QACxE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACI2I,aAAa,CAAC,CAAC,CAAC,IAAI/N,IAAI,CAACoO,GAAG,CAACN,KAAK,CAACvG,IAAI,CAAC,CAAC,GAAGsG,KAAK,CAACtG,IAAI,CAAC,CAAC,EAAEsG,KAAK,CAACzI,QAAQ,CAAC,CAAC,GAAG0I,KAAK,CAAC1I,QAAQ,CAAC,CAAC,CAAC;MAChG,CAAC,MAAM,IAAI0I,KAAK,CAACvG,IAAI,CAAC,CAAC,IAAIsG,KAAK,CAACtG,IAAI,CAAC,CAAC,IAAIuG,KAAK,CAAC1I,QAAQ,CAAC,CAAC,IAAIyI,KAAK,CAACzI,QAAQ,CAAC,CAAC,EAAE;QAC/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACI2I,aAAa,CAAC,CAAC,CAAC,IAAI/N,IAAI,CAACoO,GAAG,CAACP,KAAK,CAACtG,IAAI,CAAC,CAAC,GAAGuG,KAAK,CAACvG,IAAI,CAAC,CAAC,EAAEuG,KAAK,CAAC1I,QAAQ,CAAC,CAAC,GAAGyI,KAAK,CAACzI,QAAQ,CAAC,CAAC,CAAC;MAChG;MACA,IAAIyI,KAAK,CAACrG,IAAI,CAAC,CAAC,IAAIsG,KAAK,CAACtG,IAAI,CAAC,CAAC,IAAIqG,KAAK,CAACxI,SAAS,CAAC,CAAC,IAAIyI,KAAK,CAACzI,SAAS,CAAC,CAAC,EAAE;QAC1E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACI0I,aAAa,CAAC,CAAC,CAAC,IAAI/N,IAAI,CAACoO,GAAG,CAACN,KAAK,CAACtG,IAAI,CAAC,CAAC,GAAGqG,KAAK,CAACrG,IAAI,CAAC,CAAC,EAAEqG,KAAK,CAACxI,SAAS,CAAC,CAAC,GAAGyI,KAAK,CAACzI,SAAS,CAAC,CAAC,CAAC;MAClG,CAAC,MAAM,IAAIyI,KAAK,CAACtG,IAAI,CAAC,CAAC,IAAIqG,KAAK,CAACrG,IAAI,CAAC,CAAC,IAAIsG,KAAK,CAACzI,SAAS,CAAC,CAAC,IAAIwI,KAAK,CAACxI,SAAS,CAAC,CAAC,EAAE;QACjF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACI0I,aAAa,CAAC,CAAC,CAAC,IAAI/N,IAAI,CAACoO,GAAG,CAACP,KAAK,CAACrG,IAAI,CAAC,CAAC,GAAGsG,KAAK,CAACtG,IAAI,CAAC,CAAC,EAAEsG,KAAK,CAACzI,SAAS,CAAC,CAAC,GAAGwI,KAAK,CAACxI,SAAS,CAAC,CAAC,CAAC;MAClG;;MAEA;MACA,IAAIiJ,KAAK,GAAGtO,IAAI,CAACC,GAAG,CAAC,CAAC6N,KAAK,CAACxN,UAAU,CAAC,CAAC,GAAGuN,KAAK,CAACvN,UAAU,CAAC,CAAC,KAAKwN,KAAK,CAACzN,UAAU,CAAC,CAAC,GAAGwN,KAAK,CAACxN,UAAU,CAAC,CAAC,CAAC,CAAC;MAC3G;MACA,IAAIyN,KAAK,CAACxN,UAAU,CAAC,CAAC,KAAKuN,KAAK,CAACvN,UAAU,CAAC,CAAC,IAAIwN,KAAK,CAACzN,UAAU,CAAC,CAAC,KAAKwN,KAAK,CAACxN,UAAU,CAAC,CAAC,EAAE;QAC1F;QACAiO,KAAK,GAAG,GAAG;MACb;MAEA,IAAIC,OAAO,GAAGD,KAAK,GAAGP,aAAa,CAAC,CAAC,CAAC;MACtC,IAAIS,OAAO,GAAGT,aAAa,CAAC,CAAC,CAAC,GAAGO,KAAK;MACtC,IAAIP,aAAa,CAAC,CAAC,CAAC,GAAGS,OAAO,EAAE;QAC9BA,OAAO,GAAGT,aAAa,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLQ,OAAO,GAAGR,aAAa,CAAC,CAAC,CAAC;MAC5B;MACA;MACA;MACAA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC,IAAIM,OAAO,GAAG,CAAC,GAAGR,gBAAgB,CAAC;MACxED,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC,IAAIK,OAAO,GAAG,CAAC,GAAGP,gBAAgB,CAAC;IAC1E,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAvQ,SAAS,CAAC0Q,mCAAmC,GAAG,UAAUN,KAAK,EAAEC,KAAK,EAAEI,UAAU,EAAE;MAClF,IAAIL,KAAK,CAACxN,UAAU,CAAC,CAAC,GAAGyN,KAAK,CAACzN,UAAU,CAAC,CAAC,EAAE;QAC3C6N,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpB,CAAC,MAAM;QACLA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACnB;MAEA,IAAIL,KAAK,CAACvN,UAAU,CAAC,CAAC,GAAGwN,KAAK,CAACxN,UAAU,CAAC,CAAC,EAAE;QAC3C4N,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpB,CAAC,MAAM;QACLA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACnB;IACF,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACAzQ,SAAS,CAACgR,gBAAgB,GAAG,UAAUZ,KAAK,EAAEC,KAAK,EAAEY,MAAM,EAAE;MAC3D;MACA,IAAIC,GAAG,GAAGd,KAAK,CAACxN,UAAU,CAAC,CAAC;MAC5B,IAAIuO,GAAG,GAAGf,KAAK,CAACvN,UAAU,CAAC,CAAC;MAC5B,IAAIuO,GAAG,GAAGf,KAAK,CAACzN,UAAU,CAAC,CAAC;MAC5B,IAAIyO,GAAG,GAAGhB,KAAK,CAACxN,UAAU,CAAC,CAAC;;MAE5B;MACA,IAAIuN,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC,EAAE;QAC3BY,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;QACfD,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;QACfF,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;QACfH,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;QACf,OAAO,IAAI;MACb;MACA;MACA,IAAIC,SAAS,GAAGlB,KAAK,CAACtG,IAAI,CAAC,CAAC;MAC5B,IAAIyH,SAAS,GAAGnB,KAAK,CAACrG,IAAI,CAAC,CAAC;MAC5B,IAAIyH,UAAU,GAAGpB,KAAK,CAACzI,QAAQ,CAAC,CAAC;MACjC,IAAI8J,YAAY,GAAGrB,KAAK,CAACtG,IAAI,CAAC,CAAC;MAC/B,IAAI4H,YAAY,GAAGtB,KAAK,CAACxI,SAAS,CAAC,CAAC;MACpC,IAAI+J,aAAa,GAAGvB,KAAK,CAACzI,QAAQ,CAAC,CAAC;MACpC,IAAIiK,UAAU,GAAGxB,KAAK,CAACyB,YAAY,CAAC,CAAC;MACrC,IAAIC,WAAW,GAAG1B,KAAK,CAAC2B,aAAa,CAAC,CAAC;MACvC;MACA,IAAIC,SAAS,GAAG3B,KAAK,CAACvG,IAAI,CAAC,CAAC;MAC5B,IAAImI,SAAS,GAAG5B,KAAK,CAACtG,IAAI,CAAC,CAAC;MAC5B,IAAImI,UAAU,GAAG7B,KAAK,CAAC1I,QAAQ,CAAC,CAAC;MACjC,IAAIwK,YAAY,GAAG9B,KAAK,CAACvG,IAAI,CAAC,CAAC;MAC/B,IAAIsI,YAAY,GAAG/B,KAAK,CAACzI,SAAS,CAAC,CAAC;MACpC,IAAIyK,aAAa,GAAGhC,KAAK,CAAC1I,QAAQ,CAAC,CAAC;MACpC,IAAI2K,UAAU,GAAGjC,KAAK,CAACwB,YAAY,CAAC,CAAC;MACrC,IAAIU,WAAW,GAAGlC,KAAK,CAAC0B,aAAa,CAAC,CAAC;;MAEvC;MACA,IAAIS,eAAe,GAAG,KAAK;MAC3B,IAAIC,eAAe,GAAG,KAAK;;MAE3B;MACA,IAAIvB,GAAG,KAAKE,GAAG,EAAE;QACf,IAAID,GAAG,GAAGE,GAAG,EAAE;UACbJ,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;UACfD,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;UACrBN,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;UACfH,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;UACxB,OAAO,KAAK;QACd,CAAC,MAAM,IAAIjB,GAAG,GAAGE,GAAG,EAAE;UACpBJ,MAAM,CAAC,CAAC,CAAC,GAAGC,GAAG;UACfD,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;UACxBT,MAAM,CAAC,CAAC,CAAC,GAAGG,GAAG;UACfH,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;UACrB,OAAO,KAAK;QACd,CAAC,MAAM;UACL;QAAA;MAEJ;MACA;MAAA,KACK,IAAId,GAAG,KAAKE,GAAG,EAAE;QAClB,IAAIH,GAAG,GAAGE,GAAG,EAAE;UACbH,MAAM,CAAC,CAAC,CAAC,GAAGK,SAAS;UACrBL,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;UACfF,MAAM,CAAC,CAAC,CAAC,GAAGiB,UAAU;UACtBjB,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;UACf,OAAO,KAAK;QACd,CAAC,MAAM,IAAIH,GAAG,GAAGE,GAAG,EAAE;UACpBH,MAAM,CAAC,CAAC,CAAC,GAAGO,UAAU;UACtBP,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG;UACfF,MAAM,CAAC,CAAC,CAAC,GAAGe,SAAS;UACrBf,MAAM,CAAC,CAAC,CAAC,GAAGI,GAAG;UACf,OAAO,KAAK;QACd,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC,MAAM;QACL;QACA,IAAIqB,MAAM,GAAGtC,KAAK,CAACnM,MAAM,GAAGmM,KAAK,CAACpM,KAAK;QACvC,IAAI2O,MAAM,GAAGtC,KAAK,CAACpM,MAAM,GAAGoM,KAAK,CAACrM,KAAK;;QAEvC;QACA,IAAI4O,UAAU,GAAG,CAACvB,GAAG,GAAGF,GAAG,KAAKC,GAAG,GAAGF,GAAG,CAAC;QAC1C,IAAI2B,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAIC,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;;QAExB;QACA,IAAI,CAACR,MAAM,KAAKE,UAAU,EAAE;UAC1B,IAAI1B,GAAG,GAAGE,GAAG,EAAE;YACbH,MAAM,CAAC,CAAC,CAAC,GAAGQ,YAAY;YACxBR,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;YACxBc,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLvB,MAAM,CAAC,CAAC,CAAC,GAAGO,UAAU;YACtBP,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;YACrBiB,eAAe,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIE,MAAM,KAAKE,UAAU,EAAE;UAChC,IAAI1B,GAAG,GAAGE,GAAG,EAAE;YACbH,MAAM,CAAC,CAAC,CAAC,GAAGK,SAAS;YACrBL,MAAM,CAAC,CAAC,CAAC,GAAGM,SAAS;YACrBiB,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLvB,MAAM,CAAC,CAAC,CAAC,GAAGU,aAAa;YACzBV,MAAM,CAAC,CAAC,CAAC,GAAGS,YAAY;YACxBc,eAAe,GAAG,IAAI;UACxB;QACF;;QAEA;QACA,IAAI,CAACG,MAAM,KAAKC,UAAU,EAAE;UAC1B,IAAIxB,GAAG,GAAGF,GAAG,EAAE;YACbD,MAAM,CAAC,CAAC,CAAC,GAAGkB,YAAY;YACxBlB,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;YACxBK,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLxB,MAAM,CAAC,CAAC,CAAC,GAAGiB,UAAU;YACtBjB,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;YACrBQ,eAAe,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIE,MAAM,KAAKC,UAAU,EAAE;UAChC,IAAIxB,GAAG,GAAGF,GAAG,EAAE;YACbD,MAAM,CAAC,CAAC,CAAC,GAAGe,SAAS;YACrBf,MAAM,CAAC,CAAC,CAAC,GAAGgB,SAAS;YACrBQ,eAAe,GAAG,IAAI;UACxB,CAAC,MAAM;YACLxB,MAAM,CAAC,CAAC,CAAC,GAAGoB,aAAa;YACzBpB,MAAM,CAAC,CAAC,CAAC,GAAGmB,YAAY;YACxBK,eAAe,GAAG,IAAI;UACxB;QACF;;QAEA;QACA,IAAID,eAAe,IAAIC,eAAe,EAAE;UACtC,OAAO,KAAK;QACd;;QAEA;QACA,IAAIvB,GAAG,GAAGE,GAAG,EAAE;UACb,IAAID,GAAG,GAAGE,GAAG,EAAE;YACbwB,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACrEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACvE,CAAC,MAAM;YACLC,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAAC,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACtEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAAC,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACxE;QACF,CAAC,MAAM;UACL,IAAIzB,GAAG,GAAGE,GAAG,EAAE;YACbwB,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAAC,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACtEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAAC,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACxE,CAAC,MAAM;YACLC,kBAAkB,GAAG,IAAI,CAACM,oBAAoB,CAACT,MAAM,EAAEE,UAAU,EAAE,CAAC,CAAC;YACrEE,kBAAkB,GAAG,IAAI,CAACK,oBAAoB,CAACR,MAAM,EAAEC,UAAU,EAAE,CAAC,CAAC;UACvE;QACF;QACA;QACA,IAAI,CAACJ,eAAe,EAAE;UACpB,QAAQK,kBAAkB;YACxB,KAAK,CAAC;cACJG,WAAW,GAAGzB,SAAS;cACvBwB,WAAW,GAAG7B,GAAG,GAAG,CAACY,WAAW,GAAGc,UAAU;cAC7C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGpB,aAAa;cAC3BqB,WAAW,GAAG7B,GAAG,GAAGS,UAAU,GAAGgB,UAAU;cAC3C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJA,WAAW,GAAGtB,YAAY;cAC1BqB,WAAW,GAAG7B,GAAG,GAAGY,WAAW,GAAGc,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGtB,YAAY;cAC1BuB,WAAW,GAAG7B,GAAG,GAAG,CAACS,UAAU,GAAGgB,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAG8B,WAAW;cACvB9B,MAAM,CAAC,CAAC,CAAC,GAAG+B,WAAW;cACvB;UACJ;QACF;QACA,IAAI,CAACP,eAAe,EAAE;UACpB,QAAQK,kBAAkB;YACxB,KAAK,CAAC;cACJI,WAAW,GAAGjB,SAAS;cACvBgB,WAAW,GAAG7B,GAAG,GAAG,CAACmB,WAAW,GAAGK,UAAU;cAC7C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGZ,aAAa;cAC3Ba,WAAW,GAAG7B,GAAG,GAAGiB,UAAU,GAAGM,UAAU;cAC3C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJA,WAAW,GAAGd,YAAY;cAC1Ba,WAAW,GAAG7B,GAAG,GAAGmB,WAAW,GAAGK,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;YACF,KAAK,CAAC;cACJD,WAAW,GAAGd,YAAY;cAC1Be,WAAW,GAAG7B,GAAG,GAAG,CAACiB,UAAU,GAAGM,UAAU;cAC5C3B,MAAM,CAAC,CAAC,CAAC,GAAGgC,WAAW;cACvBhC,MAAM,CAAC,CAAC,CAAC,GAAGiC,WAAW;cACvB;UACJ;QACF;MACF;MACF,OAAO,KAAK;IACd,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;IACAlT,SAAS,CAACmT,oBAAoB,GAAG,UAAUtC,KAAK,EAAE+B,UAAU,EAAEQ,IAAI,EAAE;MAClE,IAAIvC,KAAK,GAAG+B,UAAU,EAAE;QACtB,OAAOQ,IAAI;MACb,CAAC,MAAM;QACL,OAAO,CAAC,GAAGA,IAAI,GAAG,CAAC;MACrB;IACF,CAAC;;IAED;AACA;AACA;AACA;IACApT,SAAS,CAACmC,eAAe,GAAG,UAAUkR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACpD,IAAIA,EAAE,IAAI,IAAI,EAAE;QACd,OAAO,IAAI,CAACxC,gBAAgB,CAACqC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC1C;MAEA,IAAIE,EAAE,GAAGJ,EAAE,CAACvP,CAAC;MACb,IAAI4P,EAAE,GAAGL,EAAE,CAACtP,CAAC;MACb,IAAI4P,EAAE,GAAGL,EAAE,CAACxP,CAAC;MACb,IAAI8P,EAAE,GAAGN,EAAE,CAACvP,CAAC;MACb,IAAI8P,EAAE,GAAGN,EAAE,CAACzP,CAAC;MACb,IAAIgQ,EAAE,GAAGP,EAAE,CAACxP,CAAC;MACb,IAAIgQ,EAAE,GAAGP,EAAE,CAAC1P,CAAC;MACb,IAAIkQ,EAAE,GAAGR,EAAE,CAACzP,CAAC;MACb,IAAID,CAAC,GAAG,KAAK,CAAC;QACVC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MAChB,IAAIkQ,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC;QACXC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;MACjB,IAAIC,KAAK,GAAG,KAAK,CAAC;MAElBN,EAAE,GAAGL,EAAE,GAAGF,EAAE;MACZS,EAAE,GAAGV,EAAE,GAAGE,EAAE;MACZU,EAAE,GAAGV,EAAE,GAAGD,EAAE,GAAGD,EAAE,GAAGG,EAAE,CAAC,CAAC;;MAExBM,EAAE,GAAGF,EAAE,GAAGF,EAAE;MACZM,EAAE,GAAGP,EAAE,GAAGE,EAAE;MACZO,EAAE,GAAGP,EAAE,GAAGD,EAAE,GAAGD,EAAE,GAAGG,EAAE,CAAC,CAAC;;MAExBO,KAAK,GAAGN,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE;MAEzB,IAAII,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,IAAI;MACb;MAEAzQ,CAAC,GAAG,CAACqQ,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE,IAAIE,KAAK;MAC/BxQ,CAAC,GAAG,CAACmQ,EAAE,GAAGG,EAAE,GAAGJ,EAAE,GAAGK,EAAE,IAAIC,KAAK;MAE/B,OAAO,IAAI9J,KAAK,CAAC3G,CAAC,EAAEC,CAAC,CAAC;IACxB,CAAC;;IAED;AACA;AACA;AACA;IACA/D,SAAS,CAACwU,aAAa,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAClD,IAAIC,OAAO,GAAG,KAAK,CAAC;MAEpB,IAAIJ,EAAE,KAAKE,EAAE,EAAE;QACbE,OAAO,GAAGtS,IAAI,CAACuS,IAAI,CAAC,CAACF,EAAE,GAAGF,EAAE,KAAKC,EAAE,GAAGF,EAAE,CAAC,CAAC;QAE1C,IAAIE,EAAE,GAAGF,EAAE,EAAE;UACXI,OAAO,IAAItS,IAAI,CAACwS,EAAE;QACpB,CAAC,MAAM,IAAIH,EAAE,GAAGF,EAAE,EAAE;UAClBG,OAAO,IAAI,IAAI,CAACG,MAAM;QACxB;MACF,CAAC,MAAM,IAAIJ,EAAE,GAAGF,EAAE,EAAE;QAClBG,OAAO,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC;MAClC,CAAC,MAAM;QACLJ,OAAO,GAAG,IAAI,CAACK,OAAO,CAAC,CAAC;MAC1B;MAEA,OAAOL,OAAO;IAChB,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA7U,SAAS,CAACmV,WAAW,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAChD,IAAIC,CAAC,GAAGJ,EAAE,CAACtR,CAAC;MACZ,IAAI2R,CAAC,GAAGL,EAAE,CAACrR,CAAC;MACZ,IAAIvG,CAAC,GAAG6X,EAAE,CAACvR,CAAC;MACZ,IAAIpG,CAAC,GAAG2X,EAAE,CAACtR,CAAC;MACZ,IAAIpF,CAAC,GAAG2W,EAAE,CAACxR,CAAC;MACZ,IAAI4R,CAAC,GAAGJ,EAAE,CAACvR,CAAC;MACZ,IAAI4R,CAAC,GAAGJ,EAAE,CAACzR,CAAC;MACZ,IAAIlF,CAAC,GAAG2W,EAAE,CAACxR,CAAC;MACZ,IAAI6R,GAAG,GAAG,CAACpY,CAAC,GAAGgY,CAAC,KAAK5W,CAAC,GAAG8W,CAAC,CAAC,GAAG,CAACC,CAAC,GAAGhX,CAAC,KAAKjB,CAAC,GAAG+X,CAAC,CAAC;MAE/C,IAAIG,GAAG,KAAK,CAAC,EAAE;QACb,OAAO,KAAK;MACd,CAAC,MAAM;QACL,IAAIC,MAAM,GAAG,CAAC,CAACjX,CAAC,GAAG8W,CAAC,KAAKC,CAAC,GAAGH,CAAC,CAAC,GAAG,CAAC7W,CAAC,GAAGgX,CAAC,KAAK/W,CAAC,GAAG6W,CAAC,CAAC,IAAIG,GAAG;QAC1D,IAAIE,KAAK,GAAG,CAAC,CAACL,CAAC,GAAG/X,CAAC,KAAKiY,CAAC,GAAGH,CAAC,CAAC,GAAG,CAAChY,CAAC,GAAGgY,CAAC,KAAK5W,CAAC,GAAG6W,CAAC,CAAC,IAAIG,GAAG;QACzD,OAAO,CAAC,GAAGC,MAAM,IAAIA,MAAM,GAAG,CAAC,IAAI,CAAC,GAAGC,KAAK,IAAIA,KAAK,GAAG,CAAC;MAC3D;IACF,CAAC;;IAED;AACA;AACA;AACA;IACA9V,SAAS,CAAC+V,2BAA2B,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE1B,EAAE,EAAEC,EAAE,EAAEiB,CAAC,EAAE;MAE3E;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAIH,CAAC,GAAG,CAACU,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC,GAAG,CAACG,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC;MACrD,IAAIR,CAAC,GAAG,CAAC,IAAI,CAACO,EAAE,GAAGvB,EAAE,KAAKyB,EAAE,GAAGF,EAAE,CAAC,GAAG,CAACC,EAAE,GAAGvB,EAAE,KAAKyB,EAAE,GAAGF,EAAE,CAAC,CAAC;MAC3D,IAAIzY,CAAC,GAAG,CAACwY,EAAE,GAAGvB,EAAE,KAAKuB,EAAE,GAAGvB,EAAE,CAAC,GAAG,CAACwB,EAAE,GAAGvB,EAAE,KAAKuB,EAAE,GAAGvB,EAAE,CAAC,GAAGiB,CAAC,GAAGA,CAAC;;MAE7D;MACA,IAAIS,IAAI,GAAGX,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGhY,CAAC;MAC5B,IAAI4Y,IAAI,IAAI,CAAC,EAAE;QACb;QACA,IAAIC,EAAE,GAAG,CAAC,CAACZ,CAAC,GAAGlT,IAAI,CAACG,IAAI,CAAC+S,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGhY,CAAC,CAAC,KAAK,CAAC,GAAGgY,CAAC,CAAC;QACtD,IAAIc,EAAE,GAAG,CAAC,CAACb,CAAC,GAAGlT,IAAI,CAACG,IAAI,CAAC+S,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGhY,CAAC,CAAC,KAAK,CAAC,GAAGgY,CAAC,CAAC;QACtD,IAAIe,aAAa,GAAG,IAAI;QACxB,IAAIF,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;UACtB;UACA;UACA;UACA,OAAO,CAACA,EAAE,CAAC;QACb;;QAEA;QACA;QACA,IAAIC,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;UACtB;UACA,OAAO,CAACA,EAAE,CAAC;QACb;QAEA,OAAOC,aAAa;MACtB,CAAC,MAAM,OAAO,IAAI;IACpB,CAAC;;IAED;IACA;IACA;IACA;AACA;AACA;IACAvW,SAAS,CAACkV,OAAO,GAAG,GAAG,GAAG3S,IAAI,CAACwS,EAAE;IACjC/U,SAAS,CAACiV,eAAe,GAAG,GAAG,GAAG1S,IAAI,CAACwS,EAAE;IACzC/U,SAAS,CAACgV,MAAM,GAAG,GAAG,GAAGzS,IAAI,CAACwS,EAAE;IAChC/U,SAAS,CAACwW,QAAQ,GAAG,GAAG,GAAGjU,IAAI,CAACwS,EAAE;IAElClY,MAAM,CAACD,OAAO,GAAGoD,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAASnD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS+C,KAAKA,CAAA,EAAG,CAAC;;IAElB;AACA;AACA;IACAA,KAAK,CAACwC,IAAI,GAAG,UAAUhF,KAAK,EAAE;MAC5B,IAAIA,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,CAAC;MACV,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC;IAEDwC,KAAK,CAACwW,KAAK,GAAG,UAAUhZ,KAAK,EAAE;MAC7B,OAAOA,KAAK,GAAG,CAAC,GAAG8E,IAAI,CAACmU,IAAI,CAACjZ,KAAK,CAAC,GAAG8E,IAAI,CAACkU,KAAK,CAAChZ,KAAK,CAAC;IACzD,CAAC;IAEDwC,KAAK,CAACyW,IAAI,GAAG,UAAUjZ,KAAK,EAAE;MAC5B,OAAOA,KAAK,GAAG,CAAC,GAAG8E,IAAI,CAACkU,KAAK,CAAChZ,KAAK,CAAC,GAAG8E,IAAI,CAACmU,IAAI,CAACjZ,KAAK,CAAC;IACzD,CAAC;IAEDZ,MAAM,CAACD,OAAO,GAAGqD,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAASpD,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS4F,OAAOA,CAAA,EAAG,CAAC;IAEpBA,OAAO,CAACa,SAAS,GAAG,UAAU;IAC9Bb,OAAO,CAACW,SAAS,GAAG,CAAC,UAAU;IAE/B5G,MAAM,CAACD,OAAO,GAAGkG,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAASjG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIyZ,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAACxW,MAAM,EAAEyW,KAAK,EAAE;QAAE,KAAK,IAAIzZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyZ,KAAK,CAAC9V,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI0Z,UAAU,GAAGD,KAAK,CAACzZ,CAAC,CAAC;UAAE0Z,UAAU,CAAC7Y,UAAU,GAAG6Y,UAAU,CAAC7Y,UAAU,IAAI,KAAK;UAAE6Y,UAAU,CAAC9Y,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAI8Y,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEjZ,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE0W,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAACxY,SAAS,EAAEyY,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;IAExJ,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAC9Z,KAAK,EAAE;MACtC,OAAO;QAAEA,KAAK,EAAEA,KAAK;QAAE+Z,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAK,CAAC;IACjD,CAAC;IAED,IAAIrR,GAAG,GAAG,SAASA,GAAGA,CAACqR,IAAI,EAAEjW,IAAI,EAAEgW,IAAI,EAAEE,IAAI,EAAE;MAC7C,IAAID,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACD,IAAI,GAAGhW,IAAI;MAClB,CAAC,MAAM;QACLkW,IAAI,CAACC,IAAI,GAAGnW,IAAI;MAClB;MAEA,IAAIgW,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACC,IAAI,GAAGjW,IAAI;MAClB,CAAC,MAAM;QACLkW,IAAI,CAACE,IAAI,GAAGpW,IAAI;MAClB;MAEAA,IAAI,CAACiW,IAAI,GAAGA,IAAI;MAChBjW,IAAI,CAACgW,IAAI,GAAGA,IAAI;MAEhBE,IAAI,CAAC3W,MAAM,EAAE;MAEb,OAAOS,IAAI;IACb,CAAC;IAED,IAAIqW,OAAO,GAAG,SAASA,OAAOA,CAACrW,IAAI,EAAEkW,IAAI,EAAE;MACzC,IAAID,IAAI,GAAGjW,IAAI,CAACiW,IAAI;QAChBD,IAAI,GAAGhW,IAAI,CAACgW,IAAI;MAGpB,IAAIC,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAClB,CAAC,MAAM;QACLE,IAAI,CAACC,IAAI,GAAGH,IAAI;MAClB;MAEA,IAAIA,IAAI,KAAK,IAAI,EAAE;QACjBA,IAAI,CAACC,IAAI,GAAGA,IAAI;MAClB,CAAC,MAAM;QACLC,IAAI,CAACE,IAAI,GAAGH,IAAI;MAClB;MAEAjW,IAAI,CAACiW,IAAI,GAAGjW,IAAI,CAACgW,IAAI,GAAG,IAAI;MAE5BE,IAAI,CAAC3W,MAAM,EAAE;MAEb,OAAOS,IAAI;IACb,CAAC;IAED,IAAIkJ,UAAU,GAAG,YAAY;MAC3B,SAASA,UAAUA,CAACoN,IAAI,EAAE;QACxB,IAAIC,KAAK,GAAG,IAAI;QAEhBX,eAAe,CAAC,IAAI,EAAE1M,UAAU,CAAC;QAEjC,IAAI,CAAC3J,MAAM,GAAG,CAAC;QACf,IAAI,CAAC4W,IAAI,GAAG,IAAI;QAChB,IAAI,CAACC,IAAI,GAAG,IAAI;QAEhB,IAAIE,IAAI,IAAI,IAAI,EAAE;UAChBA,IAAI,CAACjS,OAAO,CAAC,UAAUmS,CAAC,EAAE;YACxB,OAAOD,KAAK,CAACjS,IAAI,CAACkS,CAAC,CAAC;UACtB,CAAC,CAAC;QACJ;MACF;MAEArB,YAAY,CAACjM,UAAU,EAAE,CAAC;QACxBsM,GAAG,EAAE,MAAM;QACXvZ,KAAK,EAAE,SAAS4F,IAAIA,CAAA,EAAG;UACrB,OAAO,IAAI,CAACtC,MAAM;QACpB;MACF,CAAC,EAAE;QACDiW,GAAG,EAAE,cAAc;QACnBvZ,KAAK,EAAE,SAASwa,YAAYA,CAACC,GAAG,EAAEC,SAAS,EAAE;UAC3C,OAAO/R,GAAG,CAAC+R,SAAS,CAACV,IAAI,EAAEF,QAAQ,CAACW,GAAG,CAAC,EAAEC,SAAS,EAAE,IAAI,CAAC;QAC5D;MACF,CAAC,EAAE;QACDnB,GAAG,EAAE,aAAa;QAClBvZ,KAAK,EAAE,SAAS2a,WAAWA,CAACF,GAAG,EAAEC,SAAS,EAAE;UAC1C,OAAO/R,GAAG,CAAC+R,SAAS,EAAEZ,QAAQ,CAACW,GAAG,CAAC,EAAEC,SAAS,CAACX,IAAI,EAAE,IAAI,CAAC;QAC5D;MACF,CAAC,EAAE;QACDR,GAAG,EAAE,kBAAkB;QACvBvZ,KAAK,EAAE,SAAS4a,gBAAgBA,CAAC9M,OAAO,EAAE4M,SAAS,EAAE;UACnD,OAAO/R,GAAG,CAAC+R,SAAS,CAACV,IAAI,EAAElM,OAAO,EAAE4M,SAAS,EAAE,IAAI,CAAC;QACtD;MACF,CAAC,EAAE;QACDnB,GAAG,EAAE,iBAAiB;QACtBvZ,KAAK,EAAE,SAAS6a,eAAeA,CAAC/M,OAAO,EAAE4M,SAAS,EAAE;UAClD,OAAO/R,GAAG,CAAC+R,SAAS,EAAE5M,OAAO,EAAE4M,SAAS,CAACX,IAAI,EAAE,IAAI,CAAC;QACtD;MACF,CAAC,EAAE;QACDR,GAAG,EAAE,MAAM;QACXvZ,KAAK,EAAE,SAASqI,IAAIA,CAACoS,GAAG,EAAE;UACxB,OAAO9R,GAAG,CAAC,IAAI,CAACwR,IAAI,EAAEL,QAAQ,CAACW,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,EAAE;QACDlB,GAAG,EAAE,SAAS;QACdvZ,KAAK,EAAE,SAAS8a,OAAOA,CAACL,GAAG,EAAE;UAC3B,OAAO9R,GAAG,CAAC,IAAI,EAAEmR,QAAQ,CAACW,GAAG,CAAC,EAAE,IAAI,CAACP,IAAI,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,EAAE;QACDX,GAAG,EAAE,QAAQ;QACbvZ,KAAK,EAAE,SAASiO,MAAMA,CAAClK,IAAI,EAAE;UAC3B,OAAOqW,OAAO,CAACrW,IAAI,EAAE,IAAI,CAAC;QAC5B;MACF,CAAC,EAAE;QACDwV,GAAG,EAAE,KAAK;QACVvZ,KAAK,EAAE,SAAS+a,GAAGA,CAAA,EAAG;UACpB,OAAOX,OAAO,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAAC,CAACna,KAAK;QACvC;MACF,CAAC,EAAE;QACDuZ,GAAG,EAAE,SAAS;QACdvZ,KAAK,EAAE,SAASgb,OAAOA,CAAA,EAAG;UACxB,OAAOZ,OAAO,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAAC;QACjC;MACF,CAAC,EAAE;QACDZ,GAAG,EAAE,OAAO;QACZvZ,KAAK,EAAE,SAAS2P,KAAKA,CAAA,EAAG;UACtB,OAAOyK,OAAO,CAAC,IAAI,CAACF,IAAI,EAAE,IAAI,CAAC,CAACla,KAAK;QACvC;MACF,CAAC,EAAE;QACDuZ,GAAG,EAAE,WAAW;QAChBvZ,KAAK,EAAE,SAASib,SAASA,CAAA,EAAG;UAC1B,OAAOb,OAAO,CAAC,IAAI,CAACF,IAAI,EAAE,IAAI,CAAC;QACjC;MACF,CAAC,EAAE;QACDX,GAAG,EAAE,eAAe;QACpBvZ,KAAK,EAAE,SAASkb,aAAaA,CAAC7M,KAAK,EAAE;UACnC,IAAIA,KAAK,IAAI,IAAI,CAAC/K,MAAM,CAAC,CAAC,EAAE;YAC1B,IAAI3D,CAAC,GAAG,CAAC;YACT,IAAIwb,OAAO,GAAG,IAAI,CAACjB,IAAI;YACvB,OAAOva,CAAC,GAAG0O,KAAK,EAAE;cAChB8M,OAAO,GAAGA,OAAO,CAACpB,IAAI;cACtBpa,CAAC,EAAE;YACL;YACA,OAAOwb,OAAO,CAACnb,KAAK;UACtB;QACF;MACF,CAAC,EAAE;QACDuZ,GAAG,EAAE,eAAe;QACpBvZ,KAAK,EAAE,SAASob,aAAaA,CAAC/M,KAAK,EAAErO,KAAK,EAAE;UAC1C,IAAIqO,KAAK,IAAI,IAAI,CAAC/K,MAAM,CAAC,CAAC,EAAE;YAC1B,IAAI3D,CAAC,GAAG,CAAC;YACT,IAAIwb,OAAO,GAAG,IAAI,CAACjB,IAAI;YACvB,OAAOva,CAAC,GAAG0O,KAAK,EAAE;cAChB8M,OAAO,GAAGA,OAAO,CAACpB,IAAI;cACtBpa,CAAC,EAAE;YACL;YACAwb,OAAO,CAACnb,KAAK,GAAGA,KAAK;UACvB;QACF;MACF,CAAC,CAAC,CAAC;MAEH,OAAOiN,UAAU;IACnB,CAAC,CAAC,CAAC;IAEH7N,MAAM,CAACD,OAAO,GAAG8N,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAAS7N,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;AACA;AACA;IACA,SAASuN,KAAKA,CAAC3G,CAAC,EAAEC,CAAC,EAAEpF,CAAC,EAAE;MACtB,IAAI,CAACmF,CAAC,GAAG,IAAI;MACb,IAAI,CAACC,CAAC,GAAG,IAAI;MACb,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACvC,IAAI,CAACmF,CAAC,GAAG,CAAC;QACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACZ,CAAC,MAAM,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACpE,IAAI,CAACmF,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;MACZ,CAAC,MAAM,IAAID,CAAC,CAACgV,WAAW,CAACnb,IAAI,IAAI,OAAO,IAAIoG,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QAClEA,CAAC,GAAGmF,CAAC;QACL,IAAI,CAACA,CAAC,GAAGnF,CAAC,CAACmF,CAAC;QACZ,IAAI,CAACC,CAAC,GAAGpF,CAAC,CAACoF,CAAC;MACd;IACF;IAEA0G,KAAK,CAAChM,SAAS,CAACqL,IAAI,GAAG,YAAY;MACjC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAED2G,KAAK,CAAChM,SAAS,CAACsL,IAAI,GAAG,YAAY;MACjC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAED0G,KAAK,CAAChM,SAAS,CAACkG,WAAW,GAAG,YAAY;MACxC,OAAO,IAAI8F,KAAK,CAAC,IAAI,CAAC3G,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAClC,CAAC;IAED0G,KAAK,CAAChM,SAAS,CAAC2G,WAAW,GAAG,UAAUtB,CAAC,EAAEC,CAAC,EAAEpF,CAAC,EAAE;MAC/C,IAAImF,CAAC,CAACgV,WAAW,CAACnb,IAAI,IAAI,OAAO,IAAIoG,CAAC,IAAI,IAAI,IAAIpF,CAAC,IAAI,IAAI,EAAE;QAC3DA,CAAC,GAAGmF,CAAC;QACL,IAAI,CAACsB,WAAW,CAACzG,CAAC,CAACmF,CAAC,EAAEnF,CAAC,CAACoF,CAAC,CAAC;MAC5B,CAAC,MAAM,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,IAAIpF,CAAC,IAAI,IAAI,EAAE;QACpE;QACA,IAAIoa,QAAQ,CAACjV,CAAC,CAAC,IAAIA,CAAC,IAAIiV,QAAQ,CAAChV,CAAC,CAAC,IAAIA,CAAC,EAAE;UACxC,IAAI,CAACiV,IAAI,CAAClV,CAAC,EAAEC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL,IAAI,CAACD,CAAC,GAAGvB,IAAI,CAACkU,KAAK,CAAC3S,CAAC,GAAG,GAAG,CAAC;UAC5B,IAAI,CAACC,CAAC,GAAGxB,IAAI,CAACkU,KAAK,CAAC1S,CAAC,GAAG,GAAG,CAAC;QAC9B;MACF;IACF,CAAC;IAED0G,KAAK,CAAChM,SAAS,CAACua,IAAI,GAAG,UAAUlV,CAAC,EAAEC,CAAC,EAAE;MACrC,IAAI,CAACD,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACZ,CAAC;IAED0G,KAAK,CAAChM,SAAS,CAAC6L,SAAS,GAAG,UAAUhF,EAAE,EAAEC,EAAE,EAAE;MAC5C,IAAI,CAACzB,CAAC,IAAIwB,EAAE;MACZ,IAAI,CAACvB,CAAC,IAAIwB,EAAE;IACd,CAAC;IAEDkF,KAAK,CAAChM,SAAS,CAACwa,MAAM,GAAG,UAAUtN,GAAG,EAAE;MACtC,IAAIA,GAAG,CAACmN,WAAW,CAACnb,IAAI,IAAI,OAAO,EAAE;QACnC,IAAIwM,EAAE,GAAGwB,GAAG;QACZ,OAAO,IAAI,CAAC7H,CAAC,IAAIqG,EAAE,CAACrG,CAAC,IAAI,IAAI,CAACC,CAAC,IAAIoG,EAAE,CAACpG,CAAC;MACzC;MACA,OAAO,IAAI,IAAI4H,GAAG;IACpB,CAAC;IAEDlB,KAAK,CAAChM,SAAS,CAACya,QAAQ,GAAG,YAAY;MACrC,OAAO,IAAIzO,KAAK,CAAC,CAAC,CAACqO,WAAW,CAACnb,IAAI,GAAG,KAAK,GAAG,IAAI,CAACmG,CAAC,GAAG,KAAK,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG;IAC7E,CAAC;IAEDlH,MAAM,CAACD,OAAO,GAAG6N,KAAK;;IAEtB;EAAM,CAAC,IACP;EACA,KAAO,UAAS5N,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS6F,UAAUA,CAACe,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;MACvC,IAAI,CAACH,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,MAAM,GAAG,CAAC;MAEf,IAAIH,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;QAC7D,IAAI,CAACH,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;MACtB;IACF;IAEAlB,UAAU,CAACtE,SAAS,CAACqL,IAAI,GAAG,YAAY;MACtC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAEDf,UAAU,CAACtE,SAAS,CAACuL,IAAI,GAAG,UAAUlG,CAAC,EAAE;MACvC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDf,UAAU,CAACtE,SAAS,CAACsL,IAAI,GAAG,YAAY;MACtC,OAAO,IAAI,CAAChG,CAAC;IACf,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAACwL,IAAI,GAAG,UAAUlG,CAAC,EAAE;MACvC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACZ,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC/C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC+F,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACP,MAAM;IACpB,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MACjD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACkJ,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAAC7D,CAAC,GAAG,IAAI,CAACE,KAAK;IAC5B,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACmJ,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAAC7D,CAAC,GAAG,IAAI,CAACE,MAAM;IAC7B,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAAC+R,UAAU,GAAG,UAAUgF,CAAC,EAAE;MAC7C,IAAI,IAAI,CAAC7N,QAAQ,CAAC,CAAC,GAAG6N,CAAC,CAAC1R,CAAC,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI,IAAI,CAAC8D,SAAS,CAAC,CAAC,GAAG4N,CAAC,CAACzR,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MAEA,IAAIyR,CAAC,CAAC7N,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC7D,CAAC,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI0R,CAAC,CAAC5N,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC7D,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;IAEDhB,UAAU,CAACtE,SAAS,CAACmE,UAAU,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACkB,CAAC,GAAG,IAAI,CAACE,KAAK,GAAG,CAAC;IAChC,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAAC0a,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACrP,IAAI,CAAC,CAAC;IACpB,CAAC;IAED/G,UAAU,CAACtE,SAAS,CAAC2a,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACtP,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC9F,KAAK;IACjC,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACoE,UAAU,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACkB,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC;IACjC,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAAC4a,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACtP,IAAI,CAAC,CAAC;IACpB,CAAC;IAEDhH,UAAU,CAACtE,SAAS,CAAC6a,OAAO,GAAG,YAAY;MACzC,OAAO,IAAI,CAACvP,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC9F,MAAM;IAClC,CAAC;IAEDlB,UAAU,CAACtE,SAAS,CAACoT,YAAY,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAC7N,KAAK,GAAG,CAAC;IACvB,CAAC;IAEDjB,UAAU,CAACtE,SAAS,CAACsT,aAAa,GAAG,YAAY;MAC/C,OAAO,IAAI,CAAC9N,MAAM,GAAG,CAAC;IACxB,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGmG,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASlG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIqc,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAU9N,GAAG,EAAE;MAAE,OAAO,OAAOA,GAAG;IAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAI,OAAO6N,MAAM,KAAK,UAAU,IAAI7N,GAAG,CAACmN,WAAW,KAAKU,MAAM,IAAI7N,GAAG,KAAK6N,MAAM,CAAC/a,SAAS,GAAG,QAAQ,GAAG,OAAOkN,GAAG;IAAE,CAAC;IAE5Q,SAAS+N,iBAAiBA,CAAA,EAAG,CAAC;IAE9BA,iBAAiB,CAACC,MAAM,GAAG,CAAC;IAE5BD,iBAAiB,CAACE,QAAQ,GAAG,UAAUjO,GAAG,EAAE;MAC1C,IAAI+N,iBAAiB,CAACG,WAAW,CAAClO,GAAG,CAAC,EAAE;QACtC,OAAOA,GAAG;MACZ;MACA,IAAIA,GAAG,CAACmO,QAAQ,IAAI,IAAI,EAAE;QACxB,OAAOnO,GAAG,CAACmO,QAAQ;MACrB;MACAnO,GAAG,CAACmO,QAAQ,GAAGJ,iBAAiB,CAACK,SAAS,CAAC,CAAC;MAC5CL,iBAAiB,CAACC,MAAM,EAAE;MAC1B,OAAOhO,GAAG,CAACmO,QAAQ;IACrB,CAAC;IAEDJ,iBAAiB,CAACK,SAAS,GAAG,UAAUC,EAAE,EAAE;MAC1C,IAAIA,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGN,iBAAiB,CAACC,MAAM;MAC7C,OAAO,SAAS,GAAGK,EAAE,GAAG,EAAE;IAC5B,CAAC;IAEDN,iBAAiB,CAACG,WAAW,GAAG,UAAUI,GAAG,EAAE;MAC7C,IAAIC,IAAI,GAAG,OAAOD,GAAG,KAAK,WAAW,GAAG,WAAW,GAAGV,OAAO,CAACU,GAAG,CAAC;MAClE,OAAOA,GAAG,IAAI,IAAI,IAAIC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,UAAU;IAC9D,CAAC;IAEDrd,MAAM,CAACD,OAAO,GAAG8c,iBAAiB;;IAElC;EAAM,CAAC,IACP;EACA,KAAO,UAAS7c,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASid,kBAAkBA,CAACC,GAAG,EAAE;MAAE,IAAIlY,KAAK,CAACmY,OAAO,CAACD,GAAG,CAAC,EAAE;QAAE,KAAK,IAAIhd,CAAC,GAAG,CAAC,EAAEkd,IAAI,GAAGpY,KAAK,CAACkY,GAAG,CAACrZ,MAAM,CAAC,EAAE3D,CAAC,GAAGgd,GAAG,CAACrZ,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAEkd,IAAI,CAACld,CAAC,CAAC,GAAGgd,GAAG,CAAChd,CAAC,CAAC;QAAE;QAAE,OAAOkd,IAAI;MAAE,CAAC,MAAM;QAAE,OAAOpY,KAAK,CAACqY,IAAI,CAACH,GAAG,CAAC;MAAE;IAAE;IAElM,IAAIvb,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAIsN,aAAa,GAAGtN,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIyN,MAAM,GAAGzN,mBAAmB,CAAC,CAAC,CAAC;IACnC,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IACnC,IAAIsd,SAAS,GAAGtd,mBAAmB,CAAC,EAAE,CAAC;IACvC,IAAIud,OAAO,GAAGvd,mBAAmB,CAAC,EAAE,CAAC;IAErC,SAAS+N,MAAMA,CAACyP,WAAW,EAAE;MAC3BD,OAAO,CAACnd,IAAI,CAAC,IAAI,CAAC;;MAElB;MACA,IAAI,CAACqd,aAAa,GAAG9b,eAAe,CAACC,OAAO;MAC5C;MACA,IAAI,CAAC8b,mBAAmB,GAAG/b,eAAe,CAACE,8BAA8B;MACzE;MACA,IAAI,CAAC8b,WAAW,GAAGhc,eAAe,CAACG,mBAAmB;MACtD;MACA,IAAI,CAAC8b,iBAAiB,GAAGjc,eAAe,CAACI,2BAA2B;MACpE;MACA,IAAI,CAAC8b,qBAAqB,GAAGlc,eAAe,CAACK,+BAA+B;MAC5E;MACA,IAAI,CAAC8b,eAAe,GAAGnc,eAAe,CAACM,wBAAwB;MAC/D;AACF;AACA;AACA;AACA;AACA;MACE,IAAI,CAAC8b,oBAAoB,GAAGpc,eAAe,CAACO,+BAA+B;MAC3E;AACF;AACA;AACA;MACE,IAAI,CAAC8b,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjC,IAAI,CAAC5X,YAAY,GAAG,IAAIiH,aAAa,CAAC,IAAI,CAAC;MAC3C,IAAI,CAAC4Q,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACX,WAAW,GAAG,KAAK;MAExB,IAAIA,WAAW,IAAI,IAAI,EAAE;QACvB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAChC;IACF;IAEAzP,MAAM,CAACqQ,WAAW,GAAG,CAAC;IAEtBrQ,MAAM,CAACxM,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACga,OAAO,CAAChc,SAAS,CAAC;IAEnDwM,MAAM,CAACxM,SAAS,CAACmD,eAAe,GAAG,YAAY;MAC7C,OAAO,IAAI,CAAC2B,YAAY;IAC1B,CAAC;IAED0H,MAAM,CAACxM,SAAS,CAACgQ,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAAClL,YAAY,CAACkL,WAAW,CAAC,CAAC;IACxC,CAAC;IAEDxD,MAAM,CAACxM,SAAS,CAACwQ,WAAW,GAAG,YAAY;MACzC,OAAO,IAAI,CAAC1L,YAAY,CAAC0L,WAAW,CAAC,CAAC;IACxC,CAAC;IAEDhE,MAAM,CAACxM,SAAS,CAACyQ,6BAA6B,GAAG,YAAY;MAC3D,OAAO,IAAI,CAAC3L,YAAY,CAAC2L,6BAA6B,CAAC,CAAC;IAC1D,CAAC;IAEDjE,MAAM,CAACxM,SAAS,CAAC8c,eAAe,GAAG,YAAY;MAC7C,IAAIpY,EAAE,GAAG,IAAIqH,aAAa,CAAC,IAAI,CAAC;MAChC,IAAI,CAACjH,YAAY,GAAGJ,EAAE;MACtB,OAAOA,EAAE;IACX,CAAC;IAED8H,MAAM,CAACxM,SAAS,CAACqP,QAAQ,GAAG,UAAUhD,MAAM,EAAE;MAC5C,OAAO,IAAIH,MAAM,CAAC,IAAI,EAAE,IAAI,CAACpH,YAAY,EAAEuH,MAAM,CAAC;IACpD,CAAC;IAEDG,MAAM,CAACxM,SAAS,CAAC8M,OAAO,GAAG,UAAUjI,KAAK,EAAE;MAC1C,OAAO,IAAIJ,KAAK,CAAC,IAAI,CAACK,YAAY,EAAED,KAAK,CAAC;IAC5C,CAAC;IAED2H,MAAM,CAACxM,SAAS,CAACgN,OAAO,GAAG,UAAUpL,KAAK,EAAE;MAC1C,OAAO,IAAIH,KAAK,CAAC,IAAI,EAAE,IAAI,EAAEG,KAAK,CAAC;IACrC,CAAC;IAED4K,MAAM,CAACxM,SAAS,CAAC+c,kBAAkB,GAAG,YAAY;MAChD,OAAO,IAAI,CAACjY,YAAY,CAAC1B,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC0B,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,IAAI,IAAI,CAACwC,YAAY,CAAC0M,mBAAmB,CAAC,CAAC;IAC7I,CAAC;IAEDhF,MAAM,CAACxM,SAAS,CAACgd,SAAS,GAAG,YAAY;MACvC,IAAI,CAACL,gBAAgB,GAAG,KAAK;MAE7B,IAAI,IAAI,CAACM,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC,CAAC;MACxB;MAEA,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAIC,mBAAmB;MAEvB,IAAI,IAAI,CAACJ,kBAAkB,CAAC,CAAC,EAAE;QAC7BI,mBAAmB,GAAG,KAAK;MAC7B,CAAC,MAAM;QACLA,mBAAmB,GAAG,IAAI,CAAClO,MAAM,CAAC,CAAC;MACrC;MAEA,IAAI7O,eAAe,CAACgd,OAAO,KAAK,QAAQ,EAAE;QACxC;QACA;QACA,OAAO,KAAK;MACd;MAEA,IAAID,mBAAmB,EAAE;QACvB,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;UACrB,IAAI,CAACS,YAAY,CAAC,CAAC;QACrB;MACF;MAEA,IAAI,IAAI,CAACC,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC,CAAC;MACzB;MAEA,IAAI,CAACX,gBAAgB,GAAG,IAAI;MAE5B,OAAOQ,mBAAmB;IAC5B,CAAC;;IAED;AACA;AACA;IACA3Q,MAAM,CAACxM,SAAS,CAACqd,YAAY,GAAG,YAAY;MAC1C;MACA;MACA,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;QACrB,IAAI,CAAC3S,SAAS,CAAC,CAAC;MAClB;MACA,IAAI,CAAC8T,MAAM,CAAC,CAAC;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA/Q,MAAM,CAACxM,SAAS,CAACwd,OAAO,GAAG,YAAY;MACrC;MACA,IAAI,IAAI,CAACrB,mBAAmB,EAAE;QAC5B,IAAI,CAACsB,8BAA8B,CAAC,CAAC;;QAErC;QACA,IAAI,CAAC3Y,YAAY,CAACsL,aAAa,CAAC,CAAC;MACnC;;MAEA;MACA;MACA,IAAI,CAAC,IAAI,CAAC6L,WAAW,EAAE;QACrB;QACA,IAAI/U,IAAI;QACR,IAAImJ,QAAQ,GAAG,IAAI,CAACvL,YAAY,CAAC0L,WAAW,CAAC,CAAC;QAC9C,KAAK,IAAI7R,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0R,QAAQ,CAAC/N,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACxCuI,IAAI,GAAGmJ,QAAQ,CAAC1R,CAAC,CAAC;UAClB;QACF;;QAEA;QACA,IAAIoE,IAAI;QACR,IAAIiF,KAAK,GAAG,IAAI,CAAClD,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC;QAClD,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;UACf;QACF;;QAEA;QACA,IAAI,CAAC4e,MAAM,CAAC,IAAI,CAACzY,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC;MAC1C;IACF,CAAC;IAEDoJ,MAAM,CAACxM,SAAS,CAACud,MAAM,GAAG,UAAUrQ,GAAG,EAAE;MACvC,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,IAAI,CAACsQ,OAAO,CAAC,CAAC;MAChB,CAAC,MAAM,IAAItQ,GAAG,YAAYzI,KAAK,EAAE;QAC/B,IAAI1B,IAAI,GAAGmK,GAAG;QACd,IAAInK,IAAI,CAAC2C,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;UAC3B;UACA,IAAIsC,KAAK,GAAGjF,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC;UACtC,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACrC4e,MAAM,CAACvV,KAAK,CAACrJ,CAAC,CAAC,CAAC;UAClB;QACF;;QAEA;QACA;QACA;QACA,IAAIoE,IAAI,CAACjB,YAAY,IAAI,IAAI,EAAE;UAC7B;UACA,IAAI+C,KAAK,GAAG9B,IAAI,CAACjB,YAAY;;UAE7B;UACA+C,KAAK,CAAC0Y,MAAM,CAACxa,IAAI,CAAC;QACpB;MACF,CAAC,MAAM,IAAImK,GAAG,YAAYzL,KAAK,EAAE;QAC/B,IAAIyF,IAAI,GAAGgG,GAAG;QACd;QACA;QACA;;QAEA,IAAIhG,IAAI,CAACpF,YAAY,IAAI,IAAI,EAAE;UAC7B;UACA,IAAIF,KAAK,GAAGsF,IAAI,CAACpF,YAAY;;UAE7B;UACAF,KAAK,CAAC2b,MAAM,CAACrW,IAAI,CAAC;QACpB;MACF,CAAC,MAAM,IAAIgG,GAAG,YAAYhB,MAAM,EAAE;QAChC,IAAIjJ,KAAK,GAAGiK,GAAG;QACf;QACA;QACA;;QAEA,IAAIjK,KAAK,CAACnB,YAAY,IAAI,IAAI,EAAE;UAC9B;UACA,IAAIuK,MAAM,GAAGpJ,KAAK,CAACnB,YAAY;;UAE/B;UACAuK,MAAM,CAACkR,MAAM,CAACta,KAAK,CAAC;QACtB;MACF;IACF,CAAC;;IAED;AACA;AACA;AACA;IACAuJ,MAAM,CAACxM,SAAS,CAACkd,cAAc,GAAG,YAAY;MAC5C,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE;QACrB,IAAI,CAACV,aAAa,GAAG9b,eAAe,CAACC,OAAO;QAC5C,IAAI,CAACic,qBAAqB,GAAGlc,eAAe,CAACK,+BAA+B;QAC5E,IAAI,CAAC8b,eAAe,GAAGnc,eAAe,CAACM,wBAAwB;QAC/D,IAAI,CAAC2b,iBAAiB,GAAGjc,eAAe,CAACI,2BAA2B;QACpE,IAAI,CAAC4b,WAAW,GAAGhc,eAAe,CAACG,mBAAmB;QACtD,IAAI,CAAC4b,mBAAmB,GAAG/b,eAAe,CAACE,8BAA8B;QACzE,IAAI,CAACkc,oBAAoB,GAAGpc,eAAe,CAACO,+BAA+B;MAC7E;MAEA,IAAI,IAAI,CAAC2b,qBAAqB,EAAE;QAC9B,IAAI,CAACD,iBAAiB,GAAG,KAAK;MAChC;IACF,CAAC;IAED7P,MAAM,CAACxM,SAAS,CAACyJ,SAAS,GAAG,UAAUiU,UAAU,EAAE;MACjD,IAAIA,UAAU,IAAI5P,SAAS,EAAE;QAC3B,IAAI,CAACrE,SAAS,CAAC,IAAIjF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM;QACL;QACA;QACA;QACA;;QAEA,IAAIkF,KAAK,GAAG,IAAIqS,SAAS,CAAC,CAAC;QAC3B,IAAIlS,OAAO,GAAG,IAAI,CAAC/E,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAACqK,aAAa,CAAC,CAAC;QAEzD,IAAI5D,OAAO,IAAI,IAAI,EAAE;UACnBH,KAAK,CAACiU,YAAY,CAACD,UAAU,CAACrY,CAAC,CAAC;UAChCqE,KAAK,CAACkU,YAAY,CAACF,UAAU,CAACpY,CAAC,CAAC;UAEhCoE,KAAK,CAACmU,aAAa,CAAChU,OAAO,CAACxE,CAAC,CAAC;UAC9BqE,KAAK,CAACoU,aAAa,CAACjU,OAAO,CAACvE,CAAC,CAAC;UAE9B,IAAI0C,KAAK,GAAG,IAAI,CAACgI,WAAW,CAAC,CAAC;UAC9B,IAAIjN,IAAI;UAER,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACrCoE,IAAI,GAAGiF,KAAK,CAACrJ,CAAC,CAAC;YACfoE,IAAI,CAAC0G,SAAS,CAACC,KAAK,CAAC;UACvB;QACF;MACF;IACF,CAAC;IAED8C,MAAM,CAACxM,SAAS,CAAC+d,qBAAqB,GAAG,UAAU9a,KAAK,EAAE;MAExD,IAAIA,KAAK,IAAI6K,SAAS,EAAE;QACtB;QACA,IAAI,CAACiQ,qBAAqB,CAAC,IAAI,CAAC5a,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC0F,YAAY,CAAC,IAAI,CAAC;MACrD,CAAC,MAAM;QACL,IAAI8E,KAAK;QACT,IAAI7E,UAAU;QAEd,IAAIf,KAAK,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC;QAC5B,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,CAAC1F,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACrCiP,KAAK,GAAG5F,KAAK,CAACrJ,CAAC,CAAC;UAChBoK,UAAU,GAAG6E,KAAK,CAAClI,QAAQ,CAAC,CAAC;UAE7B,IAAIqD,UAAU,IAAI,IAAI,EAAE;YACtB6E,KAAK,CAACtF,OAAO,CAAC,CAAC;UACjB,CAAC,MAAM,IAAIS,UAAU,CAACd,QAAQ,CAAC,CAAC,CAAC3F,MAAM,IAAI,CAAC,EAAE;YAC5CsL,KAAK,CAACtF,OAAO,CAAC,CAAC;UACjB,CAAC,MAAM;YACL,IAAI,CAACyV,qBAAqB,CAAChV,UAAU,CAAC;YACtC6E,KAAK,CAAC9E,YAAY,CAAC,CAAC;UACtB;QACF;MACF;IACF,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACA0D,MAAM,CAACxM,SAAS,CAACge,aAAa,GAAG,YAAY;MAC3C,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,QAAQ,GAAG,IAAI;;MAEnB;MACA;MACA,IAAIjO,QAAQ,GAAG,IAAI,CAACnL,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6E,QAAQ,CAAC,CAAC;;MAErD;MACA,IAAIkW,MAAM,GAAG,IAAI;MAEjB,KAAK,IAAIxf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsR,QAAQ,CAAC3N,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxC,IAAIsR,QAAQ,CAACtR,CAAC,CAAC,CAAC+G,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;UAClCyY,MAAM,GAAG,KAAK;QAChB;MACF;;MAEA;MACA,IAAI,CAACA,MAAM,EAAE;QACX,OAAOF,UAAU;MACnB;;MAEA;;MAEA,IAAI3P,OAAO,GAAG,IAAI5G,GAAG,CAAC,CAAC;MACvB,IAAI0W,WAAW,GAAG,EAAE;MACpB,IAAIC,OAAO,GAAG,IAAI3B,GAAG,CAAC,CAAC;MACvB,IAAI4B,gBAAgB,GAAG,EAAE;MAEzBA,gBAAgB,GAAGA,gBAAgB,CAACzO,MAAM,CAACI,QAAQ,CAAC;;MAEpD;MACA;MACA;;MAEA,OAAOqO,gBAAgB,CAAChc,MAAM,GAAG,CAAC,IAAI4b,QAAQ,EAAE;QAC9CE,WAAW,CAAC/W,IAAI,CAACiX,gBAAgB,CAAC,CAAC,CAAC,CAAC;;QAErC;QACA;QACA,OAAOF,WAAW,CAAC9b,MAAM,GAAG,CAAC,IAAI4b,QAAQ,EAAE;UACzC;UACA,IAAI3P,WAAW,GAAG6P,WAAW,CAAC,CAAC,CAAC;UAChCA,WAAW,CAAC9Q,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UACxBgB,OAAO,CAAC3G,GAAG,CAAC4G,WAAW,CAAC;;UAExB;UACA,IAAIC,aAAa,GAAGD,WAAW,CAAC9I,QAAQ,CAAC,CAAC;UAE1C,KAAK,IAAI9G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6P,aAAa,CAAClM,MAAM,EAAE3D,CAAC,EAAE,EAAE;YAC7C,IAAI8P,eAAe,GAAGD,aAAa,CAAC7P,CAAC,CAAC,CAACmE,WAAW,CAACyL,WAAW,CAAC;;YAE/D;YACA,IAAI8P,OAAO,CAAC5e,GAAG,CAAC8O,WAAW,CAAC,IAAIE,eAAe,EAAE;cAC/C;cACA,IAAI,CAACH,OAAO,CAACO,GAAG,CAACJ,eAAe,CAAC,EAAE;gBACjC2P,WAAW,CAAC/W,IAAI,CAACoH,eAAe,CAAC;gBACjC4P,OAAO,CAACE,GAAG,CAAC9P,eAAe,EAAEF,WAAW,CAAC;cAC3C;cACA;cACA;cACA;cACA;cAAA,KACK;gBACD2P,QAAQ,GAAG,KAAK;gBAChB;cACF;YACJ;UACF;QACF;;QAEA;QACA;QACA,IAAI,CAACA,QAAQ,EAAE;UACbD,UAAU,GAAG,EAAE;QACjB;QACA;QACA;QACA;QAAA,KACK;UACD,IAAIO,IAAI,GAAG,EAAE,CAAC3O,MAAM,CAAC6L,kBAAkB,CAACpN,OAAO,CAAC,CAAC;UACjD2P,UAAU,CAAC5W,IAAI,CAACmX,IAAI,CAAC;UACrB;UACA;UACA,KAAK,IAAI7f,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6f,IAAI,CAAClc,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACpC,IAAIK,KAAK,GAAGwf,IAAI,CAAC7f,CAAC,CAAC;YACnB,IAAI0O,KAAK,GAAGiR,gBAAgB,CAACvR,OAAO,CAAC/N,KAAK,CAAC;YAC3C,IAAIqO,KAAK,GAAG,CAAC,CAAC,EAAE;cACdiR,gBAAgB,CAAChR,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;YACnC;UACF;UACAiB,OAAO,GAAG,IAAI5G,GAAG,CAAC,CAAC;UACnB2W,OAAO,GAAG,IAAI3B,GAAG,CAAC,CAAC;QACrB;MACJ;MAEA,OAAOuB,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;AACA;IACAzR,MAAM,CAACxM,SAAS,CAACye,6BAA6B,GAAG,UAAUvX,IAAI,EAAE;MAC/D,IAAIwX,UAAU,GAAG,EAAE;MACnB,IAAI1F,IAAI,GAAG9R,IAAI,CAACxF,MAAM;MAEtB,IAAIuB,KAAK,GAAG,IAAI,CAAC6B,YAAY,CAACqM,wBAAwB,CAACjK,IAAI,CAACxF,MAAM,EAAEwF,IAAI,CAACvF,MAAM,CAAC;MAEhF,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,IAAI,CAACnF,UAAU,CAACO,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC/C;QACA,IAAIggB,SAAS,GAAG,IAAI,CAAC7R,OAAO,CAAC,IAAI,CAAC;QAClC6R,SAAS,CAACtY,OAAO,CAAC,IAAI2F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI4S,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvD3b,KAAK,CAAC0E,GAAG,CAACgX,SAAS,CAAC;;QAEpB;QACA,IAAIE,SAAS,GAAG,IAAI,CAAC7R,OAAO,CAAC,IAAI,CAAC;QAClC,IAAI,CAAClI,YAAY,CAAC6C,GAAG,CAACkX,SAAS,EAAE7F,IAAI,EAAE2F,SAAS,CAAC;QAEjDD,UAAU,CAAC/W,GAAG,CAACgX,SAAS,CAAC;QACzB3F,IAAI,GAAG2F,SAAS;MAClB;MAEA,IAAIE,SAAS,GAAG,IAAI,CAAC7R,OAAO,CAAC,IAAI,CAAC;MAClC,IAAI,CAAClI,YAAY,CAAC6C,GAAG,CAACkX,SAAS,EAAE7F,IAAI,EAAE9R,IAAI,CAACvF,MAAM,CAAC;MAEnD,IAAI,CAAC8a,gBAAgB,CAAC8B,GAAG,CAACrX,IAAI,EAAEwX,UAAU,CAAC;;MAE3C;MACA,IAAIxX,IAAI,CAAC9E,YAAY,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC0C,YAAY,CAACmI,MAAM,CAAC/F,IAAI,CAAC;MAChC;MACA;MAAA,KACK;QACDjE,KAAK,CAACgK,MAAM,CAAC/F,IAAI,CAAC;MACpB;MAEF,OAAOwX,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;IACAlS,MAAM,CAACxM,SAAS,CAACyd,8BAA8B,GAAG,YAAY;MAC5D,IAAItY,KAAK,GAAG,EAAE;MACdA,KAAK,GAAGA,KAAK,CAAC0K,MAAM,CAAC,IAAI,CAAC/K,YAAY,CAAC0L,WAAW,CAAC,CAAC,CAAC;MACrDrL,KAAK,GAAG,EAAE,CAAC0K,MAAM,CAAC6L,kBAAkB,CAAC,IAAI,CAACe,gBAAgB,CAACqC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACjP,MAAM,CAAC1K,KAAK,CAAC;MAEjF,KAAK,IAAI4Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5Z,KAAK,CAAC7C,MAAM,EAAEyc,CAAC,EAAE,EAAE;QACrC,IAAIC,KAAK,GAAG7Z,KAAK,CAAC4Z,CAAC,CAAC;QAEpB,IAAIC,KAAK,CAACjd,UAAU,CAACO,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI2c,IAAI,GAAG,IAAI,CAACxC,gBAAgB,CAAChd,GAAG,CAACuf,KAAK,CAAC;UAE3C,KAAK,IAAIrgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsgB,IAAI,CAAC3c,MAAM,EAAE3D,CAAC,EAAE,EAAE;YACpC,IAAIggB,SAAS,GAAGM,IAAI,CAACtgB,CAAC,CAAC;YACvB,IAAIuB,CAAC,GAAG,IAAIsE,MAAM,CAACma,SAAS,CAACxa,UAAU,CAAC,CAAC,EAAEwa,SAAS,CAACva,UAAU,CAAC,CAAC,CAAC;;YAElE;YACA,IAAI8a,GAAG,GAAGF,KAAK,CAACjd,UAAU,CAACtC,GAAG,CAACd,CAAC,CAAC;YACjCugB,GAAG,CAAC7Z,CAAC,GAAGnF,CAAC,CAACmF,CAAC;YACX6Z,GAAG,CAAC5Z,CAAC,GAAGpF,CAAC,CAACoF,CAAC;;YAEX;YACA;YACAqZ,SAAS,CAACtb,QAAQ,CAAC,CAAC,CAAC4J,MAAM,CAAC0R,SAAS,CAAC;UACxC;;UAEA;UACA,IAAI,CAAC7Z,YAAY,CAAC6C,GAAG,CAACqX,KAAK,EAAEA,KAAK,CAACtd,MAAM,EAAEsd,KAAK,CAACrd,MAAM,CAAC;QAC1D;MACF;IACF,CAAC;IAED6K,MAAM,CAAC/C,SAAS,GAAG,UAAU0V,WAAW,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAE;MACtE,IAAID,MAAM,IAAIvR,SAAS,IAAIwR,MAAM,IAAIxR,SAAS,EAAE;QAC9C,IAAI9O,KAAK,GAAGogB,YAAY;QAExB,IAAID,WAAW,IAAI,EAAE,EAAE;UACrB,IAAII,QAAQ,GAAGH,YAAY,GAAGC,MAAM;UACpCrgB,KAAK,IAAI,CAACogB,YAAY,GAAGG,QAAQ,IAAI,EAAE,IAAI,EAAE,GAAGJ,WAAW,CAAC;QAC9D,CAAC,MAAM;UACL,IAAIK,QAAQ,GAAGJ,YAAY,GAAGE,MAAM;UACpCtgB,KAAK,IAAI,CAACwgB,QAAQ,GAAGJ,YAAY,IAAI,EAAE,IAAID,WAAW,GAAG,EAAE,CAAC;QAC9D;QAEA,OAAOngB,KAAK;MACd,CAAC,MAAM;QACL,IAAI+X,CAAC,EAAEC,CAAC;QAER,IAAImI,WAAW,IAAI,EAAE,EAAE;UACrBpI,CAAC,GAAG,GAAG,GAAGqI,YAAY,GAAG,KAAK;UAC9BpI,CAAC,GAAGoI,YAAY,GAAG,IAAI;QACzB,CAAC,MAAM;UACLrI,CAAC,GAAG,GAAG,GAAGqI,YAAY,GAAG,IAAI;UAC7BpI,CAAC,GAAG,CAAC,CAAC,GAAGoI,YAAY;QACvB;QAEA,OAAOrI,CAAC,GAAGoI,WAAW,GAAGnI,CAAC;MAC5B;IACF,CAAC;;IAED;AACA;AACA;AACA;IACAxK,MAAM,CAACiT,gBAAgB,GAAG,UAAUzX,KAAK,EAAE;MACzC,IAAIiR,IAAI,GAAG,EAAE;MACbA,IAAI,GAAGA,IAAI,CAACpJ,MAAM,CAAC7H,KAAK,CAAC;MAEzB,IAAI0X,YAAY,GAAG,EAAE;MACrB,IAAIC,gBAAgB,GAAG,IAAIjD,GAAG,CAAC,CAAC;MAChC,IAAIkD,WAAW,GAAG,KAAK;MACvB,IAAIC,UAAU,GAAG,IAAI;MAErB,IAAI5G,IAAI,CAAC3W,MAAM,IAAI,CAAC,IAAI2W,IAAI,CAAC3W,MAAM,IAAI,CAAC,EAAE;QACxCsd,WAAW,GAAG,IAAI;QAClBC,UAAU,GAAG5G,IAAI,CAAC,CAAC,CAAC;MACtB;MAEA,KAAK,IAAIta,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsa,IAAI,CAAC3W,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACpC,IAAIoE,IAAI,GAAGkW,IAAI,CAACta,CAAC,CAAC;QAClB,IAAImhB,MAAM,GAAG/c,IAAI,CAACyE,gBAAgB,CAAC,CAAC,CAAC5C,IAAI;QACzC+a,gBAAgB,CAACpB,GAAG,CAACxb,IAAI,EAAEA,IAAI,CAACyE,gBAAgB,CAAC,CAAC,CAAC5C,IAAI,CAAC;QAExD,IAAIkb,MAAM,IAAI,CAAC,EAAE;UACfJ,YAAY,CAACrY,IAAI,CAACtE,IAAI,CAAC;QACzB;MACF;MAEA,IAAIgd,QAAQ,GAAG,EAAE;MACjBA,QAAQ,GAAGA,QAAQ,CAAClQ,MAAM,CAAC6P,YAAY,CAAC;MAExC,OAAO,CAACE,WAAW,EAAE;QACnB,IAAII,SAAS,GAAG,EAAE;QAClBA,SAAS,GAAGA,SAAS,CAACnQ,MAAM,CAACkQ,QAAQ,CAAC;QACtCA,QAAQ,GAAG,EAAE;QAEb,KAAK,IAAIphB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsa,IAAI,CAAC3W,MAAM,EAAE3D,CAAC,EAAE,EAAE;UACpC,IAAIoE,IAAI,GAAGkW,IAAI,CAACta,CAAC,CAAC;UAElB,IAAI0O,KAAK,GAAG4L,IAAI,CAAClM,OAAO,CAAChK,IAAI,CAAC;UAC9B,IAAIsK,KAAK,IAAI,CAAC,EAAE;YACd4L,IAAI,CAAC3L,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;UACvB;UAEA,IAAI4S,UAAU,GAAGld,IAAI,CAACyE,gBAAgB,CAAC,CAAC;UAExCyY,UAAU,CAAC7Y,OAAO,CAAC,UAAU8Y,SAAS,EAAE;YACtC,IAAIR,YAAY,CAAC3S,OAAO,CAACmT,SAAS,CAAC,GAAG,CAAC,EAAE;cACvC,IAAIC,WAAW,GAAGR,gBAAgB,CAAClgB,GAAG,CAACygB,SAAS,CAAC;cACjD,IAAIE,SAAS,GAAGD,WAAW,GAAG,CAAC;cAE/B,IAAIC,SAAS,IAAI,CAAC,EAAE;gBAClBL,QAAQ,CAAC1Y,IAAI,CAAC6Y,SAAS,CAAC;cAC1B;cAEAP,gBAAgB,CAACpB,GAAG,CAAC2B,SAAS,EAAEE,SAAS,CAAC;YAC5C;UACF,CAAC,CAAC;QACJ;QAEAV,YAAY,GAAGA,YAAY,CAAC7P,MAAM,CAACkQ,QAAQ,CAAC;QAE5C,IAAI9G,IAAI,CAAC3W,MAAM,IAAI,CAAC,IAAI2W,IAAI,CAAC3W,MAAM,IAAI,CAAC,EAAE;UACxCsd,WAAW,GAAG,IAAI;UAClBC,UAAU,GAAG5G,IAAI,CAAC,CAAC,CAAC;QACtB;MACF;MAEA,OAAO4G,UAAU;IACnB,CAAC;;IAED;AACA;AACA;AACA;IACArT,MAAM,CAACxM,SAAS,CAACqgB,eAAe,GAAG,UAAU3b,EAAE,EAAE;MAC/C,IAAI,CAACI,YAAY,GAAGJ,EAAE;IACxB,CAAC;IAEDtG,MAAM,CAACD,OAAO,GAAGqO,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAASpO,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAAS8F,UAAUA,CAAA,EAAG,CAAC;IACvB;IACAA,UAAU,CAAC+b,IAAI,GAAG,CAAC;IACnB/b,UAAU,CAACc,CAAC,GAAG,CAAC;IAEhBd,UAAU,CAACoE,UAAU,GAAG,YAAY;MAClCpE,UAAU,CAACc,CAAC,GAAGvB,IAAI,CAACyc,GAAG,CAAChc,UAAU,CAAC+b,IAAI,EAAE,CAAC,GAAG,KAAK;MAClD,OAAO/b,UAAU,CAACc,CAAC,GAAGvB,IAAI,CAACkU,KAAK,CAACzT,UAAU,CAACc,CAAC,CAAC;IAChD,CAAC;IAEDjH,MAAM,CAACD,OAAO,GAAGoG,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASnG,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI+F,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAEnC,SAASsd,SAASA,CAAC1W,CAAC,EAAEC,CAAC,EAAE;MACvB,IAAI,CAACkb,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAI,CAACC,WAAW,GAAG,GAAG;MACtB,IAAI,CAACC,WAAW,GAAG,GAAG;IACxB;IAEAhF,SAAS,CAAC/b,SAAS,CAACghB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAEDzE,SAAS,CAAC/b,SAAS,CAAC2d,YAAY,GAAG,UAAUsD,GAAG,EAAE;MAChD,IAAI,CAACT,UAAU,GAAGS,GAAG;IACvB,CAAC;IAEDlF,SAAS,CAAC/b,SAAS,CAACkhB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACT,UAAU;IACxB,CAAC;IAED1E,SAAS,CAAC/b,SAAS,CAAC4d,YAAY,GAAG,UAAUuD,GAAG,EAAE;MAChD,IAAI,CAACV,UAAU,GAAGU,GAAG;IACvB,CAAC;IAEDpF,SAAS,CAAC/b,SAAS,CAACohB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACR,UAAU;IACxB,CAAC;IAED7E,SAAS,CAAC/b,SAAS,CAACqhB,YAAY,GAAG,UAAUC,GAAG,EAAE;MAChD,IAAI,CAACV,UAAU,GAAGU,GAAG;IACvB,CAAC;IAEDvF,SAAS,CAAC/b,SAAS,CAACuhB,YAAY,GAAG,YAAY;MAC7C,OAAO,IAAI,CAACV,UAAU;IACxB,CAAC;IAED9E,SAAS,CAAC/b,SAAS,CAACwhB,YAAY,GAAG,UAAUC,GAAG,EAAE;MAChD,IAAI,CAACZ,UAAU,GAAGY,GAAG;IACvB,CAAC;;IAED;;IAEA1F,SAAS,CAAC/b,SAAS,CAAC0hB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAChB,WAAW;IACzB,CAAC;IAED3E,SAAS,CAAC/b,SAAS,CAAC6d,aAAa,GAAG,UAAU8D,GAAG,EAAE;MACjD,IAAI,CAACjB,WAAW,GAAGiB,GAAG;IACxB,CAAC;IAED5F,SAAS,CAAC/b,SAAS,CAAC4hB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAACjB,WAAW;IACzB,CAAC;IAED5E,SAAS,CAAC/b,SAAS,CAAC8d,aAAa,GAAG,UAAU+D,GAAG,EAAE;MACjD,IAAI,CAAClB,WAAW,GAAGkB,GAAG;IACxB,CAAC;IAED9F,SAAS,CAAC/b,SAAS,CAAC8hB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAChB,WAAW;IACzB,CAAC;IAED/E,SAAS,CAAC/b,SAAS,CAAC+hB,aAAa,GAAG,UAAUC,GAAG,EAAE;MACjD,IAAI,CAAClB,WAAW,GAAGkB,GAAG;IACxB,CAAC;IAEDjG,SAAS,CAAC/b,SAAS,CAACiiB,aAAa,GAAG,YAAY;MAC9C,OAAO,IAAI,CAAClB,WAAW;IACzB,CAAC;IAEDhF,SAAS,CAAC/b,SAAS,CAACkiB,aAAa,GAAG,UAAUC,GAAG,EAAE;MACjD,IAAI,CAACpB,WAAW,GAAGoB,GAAG;IACxB,CAAC;IAEDpG,SAAS,CAAC/b,SAAS,CAACoiB,UAAU,GAAG,UAAU/c,CAAC,EAAE;MAC5C,IAAIgd,OAAO,GAAG,GAAG;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAC1B,UAAU;MAC/B,IAAI0B,SAAS,IAAI,GAAG,EAAE;QACpBD,OAAO,GAAG,IAAI,CAAC3B,WAAW,GAAG,CAACrb,CAAC,GAAG,IAAI,CAACmb,UAAU,IAAI,IAAI,CAACM,WAAW,GAAGwB,SAAS;MACnF;MAEA,OAAOD,OAAO;IAChB,CAAC;IAEDtG,SAAS,CAAC/b,SAAS,CAACuiB,UAAU,GAAG,UAAUjd,CAAC,EAAE;MAC5C,IAAIkd,OAAO,GAAG,GAAG;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAC5B,UAAU;MAC/B,IAAI4B,SAAS,IAAI,GAAG,EAAE;QACpBD,OAAO,GAAG,IAAI,CAAC7B,WAAW,GAAG,CAACrb,CAAC,GAAG,IAAI,CAACmb,UAAU,IAAI,IAAI,CAACM,WAAW,GAAG0B,SAAS;MACnF;MAEA,OAAOD,OAAO;IAChB,CAAC;IAEDzG,SAAS,CAAC/b,SAAS,CAAC0iB,iBAAiB,GAAG,UAAUrd,CAAC,EAAE;MACnD,IAAIsd,MAAM,GAAG,GAAG;MAChB,IAAIC,UAAU,GAAG,IAAI,CAAC9B,WAAW;MACjC,IAAI8B,UAAU,IAAI,GAAG,EAAE;QACrBD,MAAM,GAAG,IAAI,CAACnC,UAAU,GAAG,CAACnb,CAAC,GAAG,IAAI,CAACqb,WAAW,IAAI,IAAI,CAACE,UAAU,GAAGgC,UAAU;MAClF;MAEA,OAAOD,MAAM;IACf,CAAC;IAED5G,SAAS,CAAC/b,SAAS,CAAC6iB,iBAAiB,GAAG,UAAUvd,CAAC,EAAE;MACnD,IAAIwd,MAAM,GAAG,GAAG;MAChB,IAAIC,UAAU,GAAG,IAAI,CAAChC,WAAW;MACjC,IAAIgC,UAAU,IAAI,GAAG,EAAE;QACrBD,MAAM,GAAG,IAAI,CAACrC,UAAU,GAAG,CAACnb,CAAC,GAAG,IAAI,CAACqb,WAAW,IAAI,IAAI,CAACE,UAAU,GAAGkC,UAAU;MAClF;MACA,OAAOD,MAAM;IACf,CAAC;IAED/G,SAAS,CAAC/b,SAAS,CAAC+J,qBAAqB,GAAG,UAAUiZ,OAAO,EAAE;MAC7D,IAAIC,QAAQ,GAAG,IAAIze,MAAM,CAAC,IAAI,CAACke,iBAAiB,CAACM,OAAO,CAAC3d,CAAC,CAAC,EAAE,IAAI,CAACwd,iBAAiB,CAACG,OAAO,CAAC1d,CAAC,CAAC,CAAC;MAC/F,OAAO2d,QAAQ;IACjB,CAAC;IAED7kB,MAAM,CAACD,OAAO,GAAG4d,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAAS3d,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASid,kBAAkBA,CAACC,GAAG,EAAE;MAAE,IAAIlY,KAAK,CAACmY,OAAO,CAACD,GAAG,CAAC,EAAE;QAAE,KAAK,IAAIhd,CAAC,GAAG,CAAC,EAAEkd,IAAI,GAAGpY,KAAK,CAACkY,GAAG,CAACrZ,MAAM,CAAC,EAAE3D,CAAC,GAAGgd,GAAG,CAACrZ,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAEkd,IAAI,CAACld,CAAC,CAAC,GAAGgd,GAAG,CAAChd,CAAC,CAAC;QAAE;QAAE,OAAOkd,IAAI;MAAE,CAAC,MAAM;QAAE,OAAOpY,KAAK,CAACqY,IAAI,CAACH,GAAG,CAAC;MAAE;IAAE;IAElM,IAAInP,MAAM,GAAG/N,mBAAmB,CAAC,EAAE,CAAC;IACpC,IAAIuL,iBAAiB,GAAGvL,mBAAmB,CAAC,CAAC,CAAC;IAC9C,IAAI2B,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IAC5C,IAAI8C,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IACtC,IAAI+C,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IAElC,SAASykB,QAAQA,CAAA,EAAG;MAClB1W,MAAM,CAAC3N,IAAI,CAAC,IAAI,CAAC;MAEjB,IAAI,CAACskB,kCAAkC,GAAGnZ,iBAAiB,CAACS,+CAA+C;MAC3G,IAAI,CAAC2Y,eAAe,GAAGpZ,iBAAiB,CAACK,wBAAwB;MACjE,IAAI,CAACgZ,uBAAuB,GAAGrZ,iBAAiB,CAACM,iCAAiC;MAClF,IAAI,CAACgZ,kBAAkB,GAAGtZ,iBAAiB,CAACO,4BAA4B;MACxE,IAAI,CAACgZ,0BAA0B,GAAGvZ,iBAAiB,CAACQ,qCAAqC;MACzF,IAAI,CAACgZ,4BAA4B,GAAG,GAAG,GAAGxZ,iBAAiB,CAACE,mBAAmB,GAAG,GAAG;MACrF,IAAI,CAACuZ,aAAa,GAAGzZ,iBAAiB,CAACW,kCAAkC;MACzE,IAAI,CAAC+Y,oBAAoB,GAAG1Z,iBAAiB,CAACW,kCAAkC;MAChF,IAAI,CAACgZ,iBAAiB,GAAG,GAAG;MAC5B,IAAI,CAACC,oBAAoB,GAAG,GAAG;MAC/B,IAAI,CAACC,aAAa,GAAG7Z,iBAAiB,CAACC,cAAc;IACvD;IAEAiZ,QAAQ,CAACljB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACwK,MAAM,CAACxM,SAAS,CAAC;IAEpD,KAAK,IAAIiC,IAAI,IAAIuK,MAAM,EAAE;MACvB0W,QAAQ,CAACjhB,IAAI,CAAC,GAAGuK,MAAM,CAACvK,IAAI,CAAC;IAC/B;IAEAihB,QAAQ,CAACljB,SAAS,CAACkd,cAAc,GAAG,YAAY;MAC9C1Q,MAAM,CAACxM,SAAS,CAACkd,cAAc,CAACre,IAAI,CAAC,IAAI,EAAEilB,SAAS,CAAC;MAErD,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,qBAAqB,GAAG,CAAC;MAE9B,IAAI,CAACC,gBAAgB,GAAGja,iBAAiB,CAACU,6CAA6C;MAEvF,IAAI,CAACwZ,IAAI,GAAG,EAAE;IAChB,CAAC;IAEDhB,QAAQ,CAACljB,SAAS,CAACmkB,oBAAoB,GAAG,YAAY;MACpD,IAAIjd,IAAI;MACR,IAAIkd,mBAAmB;MACvB,IAAIC,QAAQ;MACZ,IAAI3iB,MAAM;MACV,IAAIC,MAAM;MACV,IAAI2iB,iBAAiB;MACrB,IAAIC,iBAAiB;MAErB,IAAIlU,QAAQ,GAAG,IAAI,CAAClN,eAAe,CAAC,CAAC,CAACqN,WAAW,CAAC,CAAC;MACnD,KAAK,IAAI7R,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0R,QAAQ,CAAC/N,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxCuI,IAAI,GAAGmJ,QAAQ,CAAC1R,CAAC,CAAC;QAElBylB,mBAAmB,GAAGld,IAAI,CAACsd,WAAW;QAEtC,IAAItd,IAAI,CAAC9E,YAAY,EAAE;UACrBV,MAAM,GAAGwF,IAAI,CAAChF,SAAS,CAAC,CAAC;UACzBP,MAAM,GAAGuF,IAAI,CAAC/E,SAAS,CAAC,CAAC;UAEzBmiB,iBAAiB,GAAGpd,IAAI,CAACxE,cAAc,CAAC,CAAC,CAAC0F,gBAAgB,CAAC,CAAC;UAC5Dmc,iBAAiB,GAAGrd,IAAI,CAACtE,cAAc,CAAC,CAAC,CAACwF,gBAAgB,CAAC,CAAC;UAE5D,IAAI,IAAI,CAAC+a,kCAAkC,EAAE;YAC3Cjc,IAAI,CAACsd,WAAW,IAAIF,iBAAiB,GAAGC,iBAAiB,GAAG,CAAC,GAAGnkB,eAAe,CAACU,gBAAgB;UAClG;UAEAujB,QAAQ,GAAGnd,IAAI,CAAC1E,MAAM,CAAC,CAAC,CAACgH,qBAAqB,CAAC,CAAC;UAEhDtC,IAAI,CAACsd,WAAW,IAAIJ,mBAAmB,GAAGpa,iBAAiB,CAACmB,kCAAkC,IAAIzJ,MAAM,CAAC8H,qBAAqB,CAAC,CAAC,GAAG7H,MAAM,CAAC6H,qBAAqB,CAAC,CAAC,GAAG,CAAC,GAAG6a,QAAQ,CAAC;QACnL;MACF;IACF,CAAC;IAEDnB,QAAQ,CAACljB,SAAS,CAACykB,kBAAkB,GAAG,YAAY;MAElD,IAAItkB,CAAC,GAAG,IAAI,CAAC6P,WAAW,CAAC,CAAC,CAAC1N,MAAM;MACjC,IAAI,IAAI,CAAC8Z,WAAW,EAAE;QACpB,IAAIjc,CAAC,GAAG6J,iBAAiB,CAACa,2BAA2B,EAAE;UACrD,IAAI,CAAC4Y,aAAa,GAAG3f,IAAI,CAACqO,GAAG,CAAC,IAAI,CAACsR,aAAa,GAAGzZ,iBAAiB,CAACY,yBAAyB,EAAE,IAAI,CAAC6Y,aAAa,GAAG,CAACtjB,CAAC,GAAG6J,iBAAiB,CAACa,2BAA2B,KAAKb,iBAAiB,CAACc,2BAA2B,GAAGd,iBAAiB,CAACa,2BAA2B,CAAC,GAAG,IAAI,CAAC4Y,aAAa,IAAI,CAAC,GAAGzZ,iBAAiB,CAACY,yBAAyB,CAAC,CAAC;QACtV;QACA,IAAI,CAAC8Z,mBAAmB,GAAG1a,iBAAiB,CAACe,iCAAiC;MAChF,CAAC,MAAM;QACL,IAAI5K,CAAC,GAAG6J,iBAAiB,CAACa,2BAA2B,EAAE;UACrD,IAAI,CAAC4Y,aAAa,GAAG3f,IAAI,CAACqO,GAAG,CAACnI,iBAAiB,CAACY,yBAAyB,EAAE,GAAG,GAAG,CAACzK,CAAC,GAAG6J,iBAAiB,CAACa,2BAA2B,KAAKb,iBAAiB,CAACc,2BAA2B,GAAGd,iBAAiB,CAACa,2BAA2B,CAAC,IAAI,CAAC,GAAGb,iBAAiB,CAACY,yBAAyB,CAAC,CAAC;QAC7R,CAAC,MAAM;UACL,IAAI,CAAC6Y,aAAa,GAAG,GAAG;QAC1B;QACA,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACD,aAAa;QAC9C,IAAI,CAACiB,mBAAmB,GAAG1a,iBAAiB,CAACgB,qBAAqB;MACpE;MAEA,IAAI,CAAC6Y,aAAa,GAAG/f,IAAI,CAACqO,GAAG,CAAC,IAAI,CAACnC,WAAW,CAAC,CAAC,CAAC1N,MAAM,GAAG,CAAC,EAAE,IAAI,CAACuhB,aAAa,CAAC;;MAEhF;MACA,IAAI,CAACL,4BAA4B,GAAG,GAAG,GAAGxZ,iBAAiB,CAACE,mBAAmB,GAAG,GAAG;MACrF,IAAI,CAACya,0BAA0B,GAAG,IAAI,CAACnB,4BAA4B,GAAG,IAAI,CAACxT,WAAW,CAAC,CAAC,CAAC1N,MAAM;MAE/F,IAAI,CAACsiB,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,CAAC;IAED3B,QAAQ,CAACljB,SAAS,CAAC8kB,gBAAgB,GAAG,YAAY;MAChD,IAAIC,MAAM,GAAG,IAAI,CAACvU,WAAW,CAAC,CAAC;MAC/B,IAAItJ,IAAI;MAER,KAAK,IAAIvI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGomB,MAAM,CAACziB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCuI,IAAI,GAAG6d,MAAM,CAACpmB,CAAC,CAAC;QAEhB,IAAI,CAACqmB,eAAe,CAAC9d,IAAI,EAAEA,IAAI,CAACsd,WAAW,CAAC;MAC9C;IACF,CAAC;IAEDtB,QAAQ,CAACljB,SAAS,CAACilB,mBAAmB,GAAG,YAAY;MACnD,IAAIC,iBAAiB,GAAGpB,SAAS,CAACxhB,MAAM,GAAG,CAAC,IAAIwhB,SAAS,CAAC,CAAC,CAAC,KAAKhW,SAAS,GAAGgW,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAChG,IAAIqB,4BAA4B,GAAGrB,SAAS,CAACxhB,MAAM,GAAG,CAAC,IAAIwhB,SAAS,CAAC,CAAC,CAAC,KAAKhW,SAAS,GAAGgW,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAE5G,IAAInlB,CAAC,EAAEymB,CAAC;MACR,IAAIC,KAAK,EAAEC,KAAK;MAChB,IAAIC,MAAM,GAAG,IAAI,CAACvV,WAAW,CAAC,CAAC;MAC/B,IAAIwV,gBAAgB;MAEpB,IAAI,IAAI,CAACvB,gBAAgB,EAAE;QACzB,IAAI,IAAI,CAACF,eAAe,GAAG/Z,iBAAiB,CAACoB,6BAA6B,IAAI,CAAC,IAAI8Z,iBAAiB,EAAE;UACpG,IAAI,CAACO,UAAU,CAAC,CAAC;QACnB;QAEAD,gBAAgB,GAAG,IAAI9d,GAAG,CAAC,CAAC;;QAE5B;QACA,KAAK/I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,MAAM,CAACjjB,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAClC0mB,KAAK,GAAGE,MAAM,CAAC5mB,CAAC,CAAC;UACjB,IAAI,CAAC+mB,8BAA8B,CAACL,KAAK,EAAEG,gBAAgB,EAAEN,iBAAiB,EAAEC,4BAA4B,CAAC;UAC7GK,gBAAgB,CAAC7d,GAAG,CAAC0d,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QACL,KAAK1mB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,MAAM,CAACjjB,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAClC0mB,KAAK,GAAGE,MAAM,CAAC5mB,CAAC,CAAC;UAEjB,KAAKymB,CAAC,GAAGzmB,CAAC,GAAG,CAAC,EAAEymB,CAAC,GAAGG,MAAM,CAACjjB,MAAM,EAAE8iB,CAAC,EAAE,EAAE;YACtCE,KAAK,GAAGC,MAAM,CAACH,CAAC,CAAC;;YAEjB;YACA,IAAIC,KAAK,CAAChiB,QAAQ,CAAC,CAAC,IAAIiiB,KAAK,CAACjiB,QAAQ,CAAC,CAAC,EAAE;cACxC;YACF;YAEA,IAAI,CAACsiB,kBAAkB,CAACN,KAAK,EAAEC,KAAK,CAAC;UACvC;QACF;MACF;IACF,CAAC;IAEDpC,QAAQ,CAACljB,SAAS,CAAC4lB,uBAAuB,GAAG,YAAY;MACvD,IAAI7iB,IAAI;MACR,IAAIwiB,MAAM,GAAG,IAAI,CAAC9U,6BAA6B,CAAC,CAAC;MAEjD,KAAK,IAAI9R,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,MAAM,CAACjjB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCoE,IAAI,GAAGwiB,MAAM,CAAC5mB,CAAC,CAAC;QAChB,IAAI,CAACknB,sBAAsB,CAAC9iB,IAAI,CAAC;MACnC;IACF,CAAC;IAEDmgB,QAAQ,CAACljB,SAAS,CAAC8lB,SAAS,GAAG,YAAY;MACzC,IAAIP,MAAM,GAAG,IAAI,CAACvV,WAAW,CAAC,CAAC;MAC/B,IAAIjN,IAAI;MAER,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,MAAM,CAACjjB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCoE,IAAI,GAAGwiB,MAAM,CAAC5mB,CAAC,CAAC;QAChBoE,IAAI,CAACwX,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IAED2I,QAAQ,CAACljB,SAAS,CAACglB,eAAe,GAAG,UAAU9d,IAAI,EAAEsd,WAAW,EAAE;MAChE,IAAI5X,UAAU,GAAG1F,IAAI,CAAChF,SAAS,CAAC,CAAC;MACjC,IAAI2K,UAAU,GAAG3F,IAAI,CAAC/E,SAAS,CAAC,CAAC;MAEjC,IAAIG,MAAM;MACV,IAAIyjB,WAAW;MACf,IAAIC,YAAY;MAChB,IAAIC,YAAY;;MAEhB;MACA,IAAI,IAAI,CAACzJ,oBAAoB,IAAI5P,UAAU,CAAClH,QAAQ,CAAC,CAAC,IAAI,IAAI,IAAImH,UAAU,CAACnH,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;QAC/FwB,IAAI,CAAChD,kBAAkB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLgD,IAAI,CAAC3D,YAAY,CAAC,CAAC;QAEnB,IAAI2D,IAAI,CAACrF,2BAA2B,EAAE;UACpC;QACF;MACF;MAEAS,MAAM,GAAG4E,IAAI,CAAC7E,SAAS,CAAC,CAAC;MAEzB,IAAIC,MAAM,IAAI,CAAC,EAAE;;MAEjB;MACAyjB,WAAW,GAAG7e,IAAI,CAACgf,cAAc,IAAI5jB,MAAM,GAAGkiB,WAAW,CAAC;;MAE1D;MACAwB,YAAY,GAAGD,WAAW,IAAI7e,IAAI,CAACtD,OAAO,GAAGtB,MAAM,CAAC;MACpD2jB,YAAY,GAAGF,WAAW,IAAI7e,IAAI,CAACrD,OAAO,GAAGvB,MAAM,CAAC;;MAEpD;MACAsK,UAAU,CAACoZ,YAAY,IAAIA,YAAY;MACvCpZ,UAAU,CAACqZ,YAAY,IAAIA,YAAY;MACvCpZ,UAAU,CAACmZ,YAAY,IAAIA,YAAY;MACvCnZ,UAAU,CAACoZ,YAAY,IAAIA,YAAY;IACzC,CAAC;IAED/C,QAAQ,CAACljB,SAAS,CAAC2lB,kBAAkB,GAAG,UAAUN,KAAK,EAAEC,KAAK,EAAE;MAC9D,IAAI3T,KAAK,GAAG0T,KAAK,CAAC1hB,OAAO,CAAC,CAAC;MAC3B,IAAIiO,KAAK,GAAG0T,KAAK,CAAC3hB,OAAO,CAAC,CAAC;MAC3B,IAAIkO,aAAa,GAAG,IAAIpO,KAAK,CAAC,CAAC,CAAC;MAChC,IAAI0iB,UAAU,GAAG,IAAI1iB,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAI2iB,SAAS;MACb,IAAIC,SAAS;MACb,IAAIC,eAAe;MACnB,IAAIC,QAAQ;MACZ,IAAIC,cAAc;MAClB,IAAIC,eAAe;MACnB,IAAIC,eAAe;MAEnB,IAAI/U,KAAK,CAACI,UAAU,CAACH,KAAK,CAAC;QAAE;QAC3B;UACE;UACArQ,SAAS,CAACmQ,oBAAoB,CAACC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAE7H,iBAAiB,CAACE,mBAAmB,GAAG,GAAG,CAAC;UAExGuc,eAAe,GAAG,CAAC,GAAG5U,aAAa,CAAC,CAAC,CAAC;UACtC6U,eAAe,GAAG,CAAC,GAAG7U,aAAa,CAAC,CAAC,CAAC;UAEtC,IAAI8U,gBAAgB,GAAGtB,KAAK,CAACld,YAAY,GAAGmd,KAAK,CAACnd,YAAY,IAAIkd,KAAK,CAACld,YAAY,GAAGmd,KAAK,CAACnd,YAAY,CAAC;;UAE1G;UACAkd,KAAK,CAACoB,eAAe,IAAIE,gBAAgB,GAAGF,eAAe;UAC3DpB,KAAK,CAACqB,eAAe,IAAIC,gBAAgB,GAAGD,eAAe;UAC3DpB,KAAK,CAACmB,eAAe,IAAIE,gBAAgB,GAAGF,eAAe;UAC3DnB,KAAK,CAACoB,eAAe,IAAIC,gBAAgB,GAAGD,eAAe;QAC7D,CAAC;QAAM;QACP;UACE;;UAEA,IAAI,IAAI,CAAClK,oBAAoB,IAAI6I,KAAK,CAAC3f,QAAQ,CAAC,CAAC,IAAI,IAAI,IAAI4f,KAAK,CAAC5f,QAAQ,CAAC,CAAC,IAAI,IAAI;YAAE;YACrF;cACE0gB,SAAS,GAAGxU,KAAK,CAACzN,UAAU,CAAC,CAAC,GAAGwN,KAAK,CAACxN,UAAU,CAAC,CAAC;cACnDkiB,SAAS,GAAGzU,KAAK,CAACxN,UAAU,CAAC,CAAC,GAAGuN,KAAK,CAACvN,UAAU,CAAC,CAAC;YACrD,CAAC;YAAM;YACP;cACE7C,SAAS,CAACmC,eAAe,CAACiO,KAAK,EAAEC,KAAK,EAAEuU,UAAU,CAAC;cAEnDC,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;cACzCE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;YAC3C;;UAEF;UACA,IAAIriB,IAAI,CAACC,GAAG,CAACqiB,SAAS,CAAC,GAAGpc,iBAAiB,CAACiB,kBAAkB,EAAE;YAC9Dmb,SAAS,GAAG5kB,KAAK,CAACwC,IAAI,CAACoiB,SAAS,CAAC,GAAGpc,iBAAiB,CAACiB,kBAAkB;UAC1E;UAEA,IAAInH,IAAI,CAACC,GAAG,CAACsiB,SAAS,CAAC,GAAGrc,iBAAiB,CAACiB,kBAAkB,EAAE;YAC9Dob,SAAS,GAAG7kB,KAAK,CAACwC,IAAI,CAACqiB,SAAS,CAAC,GAAGrc,iBAAiB,CAACiB,kBAAkB;UAC1E;UAEAqb,eAAe,GAAGF,SAAS,GAAGA,SAAS,GAAGC,SAAS,GAAGA,SAAS;UAC/DE,QAAQ,GAAGziB,IAAI,CAACG,IAAI,CAACqiB,eAAe,CAAC;;UAErC;UACAE,cAAc,GAAG,CAACnB,KAAK,CAACuB,aAAa,GAAG,CAAC,GAAGtB,KAAK,CAACsB,aAAa,GAAG,CAAC,IAAIvB,KAAK,CAACld,YAAY,GAAGmd,KAAK,CAACnd,YAAY,GAAGme,eAAe;;UAEhI;UACAG,eAAe,GAAGD,cAAc,GAAGJ,SAAS,GAAGG,QAAQ;UACvDG,eAAe,GAAGF,cAAc,GAAGH,SAAS,GAAGE,QAAQ;;UAEvD;UACAlB,KAAK,CAACoB,eAAe,IAAIA,eAAe;UACxCpB,KAAK,CAACqB,eAAe,IAAIA,eAAe;UACxCpB,KAAK,CAACmB,eAAe,IAAIA,eAAe;UACxCnB,KAAK,CAACoB,eAAe,IAAIA,eAAe;QAC1C;IACJ,CAAC;IAEDxD,QAAQ,CAACljB,SAAS,CAAC6lB,sBAAsB,GAAG,UAAU9iB,IAAI,EAAE;MAC1D,IAAIgO,UAAU;MACd,IAAI8V,YAAY;MAChB,IAAIC,YAAY;MAChB,IAAIV,SAAS;MACb,IAAIC,SAAS;MACb,IAAIU,YAAY;MAChB,IAAIC,YAAY;MAChB,IAAIjiB,aAAa;MACjBgM,UAAU,GAAGhO,IAAI,CAACM,QAAQ,CAAC,CAAC;MAE5BwjB,YAAY,GAAG,CAAC9V,UAAU,CAAC7H,QAAQ,CAAC,CAAC,GAAG6H,UAAU,CAAC/H,OAAO,CAAC,CAAC,IAAI,CAAC;MACjE8d,YAAY,GAAG,CAAC/V,UAAU,CAAC9H,MAAM,CAAC,CAAC,GAAG8H,UAAU,CAAC5H,SAAS,CAAC,CAAC,IAAI,CAAC;MACjEid,SAAS,GAAGrjB,IAAI,CAACoB,UAAU,CAAC,CAAC,GAAG0iB,YAAY;MAC5CR,SAAS,GAAGtjB,IAAI,CAACqB,UAAU,CAAC,CAAC,GAAG0iB,YAAY;MAC5CC,YAAY,GAAGjjB,IAAI,CAACC,GAAG,CAACqiB,SAAS,CAAC,GAAGrjB,IAAI,CAAC8C,QAAQ,CAAC,CAAC,GAAG,CAAC;MACxDmhB,YAAY,GAAGljB,IAAI,CAACC,GAAG,CAACsiB,SAAS,CAAC,GAAGtjB,IAAI,CAACgD,SAAS,CAAC,CAAC,GAAG,CAAC;MAEzD,IAAIhD,IAAI,CAACM,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACyB,YAAY,CAAC1B,OAAO,CAAC,CAAC;QAAE;QAClD;UACE2B,aAAa,GAAGgM,UAAU,CAAC3I,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACkb,kBAAkB;UAEvE,IAAIyD,YAAY,GAAGhiB,aAAa,IAAIiiB,YAAY,GAAGjiB,aAAa,EAAE;YAChEhC,IAAI,CAACkkB,iBAAiB,GAAG,CAAC,IAAI,CAAC7D,eAAe,GAAGgD,SAAS;YAC1DrjB,IAAI,CAACmkB,iBAAiB,GAAG,CAAC,IAAI,CAAC9D,eAAe,GAAGiD,SAAS;UAC5D;QACF,CAAC;QAAM;QACP;UACEthB,aAAa,GAAGgM,UAAU,CAAC3I,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACmb,0BAA0B;UAE/E,IAAIwD,YAAY,GAAGhiB,aAAa,IAAIiiB,YAAY,GAAGjiB,aAAa,EAAE;YAChEhC,IAAI,CAACkkB,iBAAiB,GAAG,CAAC,IAAI,CAAC7D,eAAe,GAAGgD,SAAS,GAAG,IAAI,CAAC/C,uBAAuB;YACzFtgB,IAAI,CAACmkB,iBAAiB,GAAG,CAAC,IAAI,CAAC9D,eAAe,GAAGiD,SAAS,GAAG,IAAI,CAAChD,uBAAuB;UAC3F;QACF;IACJ,CAAC;IAEDH,QAAQ,CAACljB,SAAS,CAACmnB,WAAW,GAAG,YAAY;MAC3C,IAAIC,SAAS;MACb,IAAIC,UAAU,GAAG,KAAK;MAEtB,IAAI,IAAI,CAACtD,eAAe,GAAG,IAAI,CAACF,aAAa,GAAG,CAAC,EAAE;QACjDwD,UAAU,GAAGvjB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC4f,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,GAAG,CAAC;MAC/E;MAEAwD,SAAS,GAAG,IAAI,CAACzD,iBAAiB,GAAG,IAAI,CAACgB,0BAA0B;MAEpE,IAAI,CAACf,oBAAoB,GAAG,IAAI,CAACD,iBAAiB;MAElD,OAAOyD,SAAS,IAAIC,UAAU;IAChC,CAAC;IAEDnE,QAAQ,CAACljB,SAAS,CAACsnB,OAAO,GAAG,YAAY;MACvC,IAAI,IAAI,CAAChL,qBAAqB,IAAI,CAAC,IAAI,CAACM,WAAW,EAAE;QACnD,IAAI,IAAI,CAACoH,qBAAqB,IAAI,IAAI,CAACzH,eAAe,EAAE;UACtD,IAAI,CAACgB,MAAM,CAAC,CAAC;UACb,IAAI,CAACyG,qBAAqB,GAAG,CAAC;QAChC,CAAC,MAAM;UACL,IAAI,CAACA,qBAAqB,EAAE;QAC9B;MACF;IACF,CAAC;;IAED;IACAd,QAAQ,CAACljB,SAAS,CAACunB,2BAA2B,GAAG,YAAY;MAC3D,IAAIxkB,IAAI;MACR,IAAIkN,QAAQ,GAAG,IAAI,CAACnL,YAAY,CAACkL,WAAW,CAAC,CAAC;MAE9C,KAAK,IAAIrR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsR,QAAQ,CAAC3N,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxCoE,IAAI,GAAGkN,QAAQ,CAACtR,CAAC,CAAC;QAClBoE,IAAI,CAACoF,YAAY,GAAGpF,IAAI,CAACmF,eAAe,CAAC,CAAC;MAC5C;IACF,CAAC;;IAED;IACA;IACA;;IAEAgb,QAAQ,CAACljB,SAAS,CAACwnB,QAAQ,GAAG,UAAUvkB,KAAK,EAAE;MAE7C,IAAIwkB,KAAK,GAAG,CAAC;MACb,IAAIC,KAAK,GAAG,CAAC;MAEbD,KAAK,GAAGnN,QAAQ,CAACxW,IAAI,CAACmU,IAAI,CAAC,CAAChV,KAAK,CAACiG,QAAQ,CAAC,CAAC,GAAGjG,KAAK,CAAC+F,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC4b,cAAc,CAAC,CAAC;MACvF8C,KAAK,GAAGpN,QAAQ,CAACxW,IAAI,CAACmU,IAAI,CAAC,CAAChV,KAAK,CAACkG,SAAS,CAAC,CAAC,GAAGlG,KAAK,CAACgG,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC2b,cAAc,CAAC,CAAC;MAEvF,IAAIV,IAAI,GAAG,IAAIzgB,KAAK,CAACgkB,KAAK,CAAC;MAE3B,KAAK,IAAI9oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8oB,KAAK,EAAE9oB,CAAC,EAAE,EAAE;QAC9BulB,IAAI,CAACvlB,CAAC,CAAC,GAAG,IAAI8E,KAAK,CAACikB,KAAK,CAAC;MAC5B;MAEA,KAAK,IAAI/oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8oB,KAAK,EAAE9oB,CAAC,EAAE,EAAE;QAC9B,KAAK,IAAIymB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,EAAE,EAAE;UAC9BlB,IAAI,CAACvlB,CAAC,CAAC,CAACymB,CAAC,CAAC,GAAG,IAAI3hB,KAAK,CAAC,CAAC;QAC1B;MACF;MAEA,OAAOygB,IAAI;IACb,CAAC;IAEDhB,QAAQ,CAACljB,SAAS,CAAC2nB,aAAa,GAAG,UAAUpO,CAAC,EAAE5P,IAAI,EAAEC,GAAG,EAAE;MAEzD,IAAIge,MAAM,GAAG,CAAC;MACd,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIC,OAAO,GAAG,CAAC;MAEfH,MAAM,GAAGtN,QAAQ,CAACxW,IAAI,CAACkU,KAAK,CAAC,CAACuB,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC0B,CAAC,GAAGsE,IAAI,IAAI,IAAI,CAACib,cAAc,CAAC,CAAC;MAC3EiD,OAAO,GAAGvN,QAAQ,CAACxW,IAAI,CAACkU,KAAK,CAAC,CAACuB,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC4B,KAAK,GAAGgU,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC0B,CAAC,GAAGsE,IAAI,IAAI,IAAI,CAACib,cAAc,CAAC,CAAC;MAChGkD,MAAM,GAAGxN,QAAQ,CAACxW,IAAI,CAACkU,KAAK,CAAC,CAACuB,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC2B,CAAC,GAAGsE,GAAG,IAAI,IAAI,CAACgb,cAAc,CAAC,CAAC;MAC1EmD,OAAO,GAAGzN,QAAQ,CAACxW,IAAI,CAACkU,KAAK,CAAC,CAACuB,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC6B,MAAM,GAAG+T,CAAC,CAAC5V,OAAO,CAAC,CAAC,CAAC2B,CAAC,GAAGsE,GAAG,IAAI,IAAI,CAACgb,cAAc,CAAC,CAAC;MAEhG,KAAK,IAAIjmB,CAAC,GAAGipB,MAAM,EAAEjpB,CAAC,IAAIkpB,OAAO,EAAElpB,CAAC,EAAE,EAAE;QACtC,KAAK,IAAIymB,CAAC,GAAG0C,MAAM,EAAE1C,CAAC,IAAI2C,OAAO,EAAE3C,CAAC,EAAE,EAAE;UACtC,IAAI,CAAClB,IAAI,CAACvlB,CAAC,CAAC,CAACymB,CAAC,CAAC,CAAC/d,IAAI,CAACkS,CAAC,CAAC;UACvBA,CAAC,CAACyO,kBAAkB,CAACJ,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,CAAC;QACxD;MACF;IACF,CAAC;IAED7E,QAAQ,CAACljB,SAAS,CAACylB,UAAU,GAAG,YAAY;MAC1C,IAAI9mB,CAAC;MACL,IAAI0mB,KAAK;MACT,IAAIE,MAAM,GAAG,IAAI,CAACvV,WAAW,CAAC,CAAC;MAE/B,IAAI,CAACkU,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,IAAI,CAAC1iB,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC;;MAEtD;MACA,KAAKzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4mB,MAAM,CAACjjB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAClC0mB,KAAK,GAAGE,MAAM,CAAC5mB,CAAC,CAAC;QACjB,IAAI,CAACgpB,aAAa,CAACtC,KAAK,EAAE,IAAI,CAACvgB,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC4F,OAAO,CAAC,CAAC,EAAE,IAAI,CAAClE,YAAY,CAAC1B,OAAO,CAAC,CAAC,CAAC6F,MAAM,CAAC,CAAC,CAAC;MACxG;IACF,CAAC;IAEDia,QAAQ,CAACljB,SAAS,CAAC0lB,8BAA8B,GAAG,UAAUL,KAAK,EAAEG,gBAAgB,EAAEN,iBAAiB,EAAEC,4BAA4B,EAAE;MAEtI,IAAI,IAAI,CAACpB,eAAe,GAAG/Z,iBAAiB,CAACoB,6BAA6B,IAAI,CAAC,IAAI8Z,iBAAiB,IAAIC,4BAA4B,EAAE;QACpI,IAAI8C,WAAW,GAAG,IAAIvgB,GAAG,CAAC,CAAC;QAC3B2d,KAAK,CAAC4C,WAAW,GAAG,IAAIxkB,KAAK,CAAC,CAAC;QAC/B,IAAI6hB,KAAK;QACT,IAAIpB,IAAI,GAAG,IAAI,CAACA,IAAI;QAEpB,KAAK,IAAIvlB,CAAC,GAAG0mB,KAAK,CAACuC,MAAM,GAAG,CAAC,EAAEjpB,CAAC,GAAG0mB,KAAK,CAACwC,OAAO,GAAG,CAAC,EAAElpB,CAAC,EAAE,EAAE;UACzD,KAAK,IAAIymB,CAAC,GAAGC,KAAK,CAACyC,MAAM,GAAG,CAAC,EAAE1C,CAAC,GAAGC,KAAK,CAAC0C,OAAO,GAAG,CAAC,EAAE3C,CAAC,EAAE,EAAE;YACzD,IAAI,EAAEzmB,CAAC,GAAG,CAAC,IAAIymB,CAAC,GAAG,CAAC,IAAIzmB,CAAC,IAAIulB,IAAI,CAAC5hB,MAAM,IAAI8iB,CAAC,IAAIlB,IAAI,CAAC,CAAC,CAAC,CAAC5hB,MAAM,CAAC,EAAE;cAChE,KAAK,IAAIyc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,IAAI,CAACvlB,CAAC,CAAC,CAACymB,CAAC,CAAC,CAAC9iB,MAAM,EAAEyc,CAAC,EAAE,EAAE;gBAC1CuG,KAAK,GAAGpB,IAAI,CAACvlB,CAAC,CAAC,CAACymB,CAAC,CAAC,CAACrG,CAAC,CAAC;;gBAErB;gBACA;gBACA,IAAIsG,KAAK,CAAChiB,QAAQ,CAAC,CAAC,IAAIiiB,KAAK,CAACjiB,QAAQ,CAAC,CAAC,IAAIgiB,KAAK,IAAIC,KAAK,EAAE;kBAC1D;gBACF;;gBAEA;gBACA;gBACA,IAAI,CAACE,gBAAgB,CAAC3W,GAAG,CAACyW,KAAK,CAAC,IAAI,CAAC2C,WAAW,CAACpZ,GAAG,CAACyW,KAAK,CAAC,EAAE;kBAC3D,IAAIc,SAAS,GAAGtiB,IAAI,CAACC,GAAG,CAACshB,KAAK,CAAClhB,UAAU,CAAC,CAAC,GAAGmhB,KAAK,CAACnhB,UAAU,CAAC,CAAC,CAAC,IAAIkhB,KAAK,CAACxf,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGyf,KAAK,CAACzf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;kBACjH,IAAIwgB,SAAS,GAAGviB,IAAI,CAACC,GAAG,CAACshB,KAAK,CAACjhB,UAAU,CAAC,CAAC,GAAGkhB,KAAK,CAAClhB,UAAU,CAAC,CAAC,CAAC,IAAIihB,KAAK,CAACtf,SAAS,CAAC,CAAC,GAAG,CAAC,GAAGuf,KAAK,CAACvf,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;;kBAEnH;kBACA;kBACA,IAAIqgB,SAAS,IAAI,IAAI,CAACxB,cAAc,IAAIyB,SAAS,IAAI,IAAI,CAACzB,cAAc,EAAE;oBACxE;oBACAqD,WAAW,CAACtgB,GAAG,CAAC2d,KAAK,CAAC;kBACxB;gBACF;cACF;YACF;UACF;QACF;QAEAD,KAAK,CAAC4C,WAAW,GAAG,EAAE,CAACpY,MAAM,CAAC6L,kBAAkB,CAACuM,WAAW,CAAC,CAAC;MAChE;MACA,KAAKtpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0mB,KAAK,CAAC4C,WAAW,CAAC3lB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC7C,IAAI,CAACgnB,kBAAkB,CAACN,KAAK,EAAEA,KAAK,CAAC4C,WAAW,CAACtpB,CAAC,CAAC,CAAC;MACtD;IACF,CAAC;IAEDukB,QAAQ,CAACljB,SAAS,CAAC6kB,kBAAkB,GAAG,YAAY;MAClD,OAAO,GAAG;IACZ,CAAC;IAEDzmB,MAAM,CAACD,OAAO,GAAG+kB,QAAQ;;IAEzB;EAAM,CAAC,IACP;EACA,KAAO,UAAS9kB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIgD,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIuL,iBAAiB,GAAGvL,mBAAmB,CAAC,CAAC,CAAC;IAE9C,SAASypB,YAAYA,CAACxmB,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;MAC3CH,KAAK,CAAC5C,IAAI,CAAC,IAAI,EAAE6C,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;;MAEvC;MACA,IAAI,CAAC4iB,WAAW,GAAGxa,iBAAiB,CAACE,mBAAmB;MACxD,IAAI,CAACgc,cAAc,GAAGlc,iBAAiB,CAACG,uBAAuB;IACjE;IAEA+d,YAAY,CAACloB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACP,KAAK,CAACzB,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIR,KAAK,EAAE;MACtBymB,YAAY,CAACjmB,IAAI,CAAC,GAAGR,KAAK,CAACQ,IAAI,CAAC;IAClC;IAEA7D,MAAM,CAACD,OAAO,GAAG+pB,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAAS9pB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIgG,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IAClC,IAAIuL,iBAAiB,GAAGvL,mBAAmB,CAAC,CAAC,CAAC;IAE9C,SAAS0pB,YAAYA,CAACzjB,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;MAC1C;MACAJ,KAAK,CAAC5F,IAAI,CAAC,IAAI,EAAE6F,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,CAAC;;MAEtC;MACA,IAAI,CAAC+hB,aAAa,GAAG5c,iBAAiB,CAACI,0BAA0B;;MAEjE;MACA,IAAI,CAAC4b,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACQ,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACO,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;MACA,IAAI,CAACkB,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,aAAa,GAAG,CAAC;;MAEtB;MACA,IAAI,CAACT,MAAM,GAAG,CAAC;MACf,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,MAAM,GAAG,CAAC;MACf,IAAI,CAACC,OAAO,GAAG,CAAC;;MAEhB;MACA,IAAI,CAACE,WAAW,GAAG,EAAE;IACvB;IAEAE,YAAY,CAACnoB,SAAS,GAAGX,MAAM,CAAC2C,MAAM,CAACyC,KAAK,CAACzE,SAAS,CAAC;IAEvD,KAAK,IAAIiC,IAAI,IAAIwC,KAAK,EAAE;MACtB0jB,YAAY,CAAClmB,IAAI,CAAC,GAAGwC,KAAK,CAACxC,IAAI,CAAC;IAClC;IAEAkmB,YAAY,CAACnoB,SAAS,CAACgoB,kBAAkB,GAAG,UAAUM,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAE;MAC1F,IAAI,CAACb,MAAM,GAAGU,OAAO;MACrB,IAAI,CAACT,OAAO,GAAGU,QAAQ;MACvB,IAAI,CAACT,MAAM,GAAGU,OAAO;MACrB,IAAI,CAACT,OAAO,GAAGU,QAAQ;IACzB,CAAC;IAEDrqB,MAAM,CAACD,OAAO,GAAGgqB,YAAY;;IAE7B;EAAM,CAAC,IACP;EACA,KAAO,UAAS/pB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASkN,UAAUA,CAACpG,KAAK,EAAEC,MAAM,EAAE;MACjC,IAAI,CAACD,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,MAAM,GAAG,CAAC;MACf,IAAID,KAAK,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;QACrC,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;MACpB;IACF;IAEAoG,UAAU,CAAC3L,SAAS,CAAC6F,QAAQ,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACN,KAAK;IACnB,CAAC;IAEDoG,UAAU,CAAC3L,SAAS,CAAC8F,QAAQ,GAAG,UAAUP,KAAK,EAAE;MAC/C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC;IAEDoG,UAAU,CAAC3L,SAAS,CAAC+F,SAAS,GAAG,YAAY;MAC3C,OAAO,IAAI,CAACP,MAAM;IACpB,CAAC;IAEDmG,UAAU,CAAC3L,SAAS,CAACgG,SAAS,GAAG,UAAUR,MAAM,EAAE;MACjD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC;IAEDpH,MAAM,CAACD,OAAO,GAAGwN,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASvN,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIwc,iBAAiB,GAAGxc,mBAAmB,CAAC,EAAE,CAAC;IAE/C,SAASiqB,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;MACb,IAAI,CAAC7J,IAAI,GAAG,EAAE;IAChB;IAEA4J,OAAO,CAAC1oB,SAAS,CAAC4oB,GAAG,GAAG,UAAUrQ,GAAG,EAAEvZ,KAAK,EAAE;MAC5C,IAAI6pB,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAI,CAACuQ,QAAQ,CAACD,KAAK,CAAC,EAAE;QACzB,IAAI,CAACF,GAAG,CAACE,KAAK,CAAC,GAAG7pB,KAAK;QACvB,IAAI,CAAC8f,IAAI,CAACzX,IAAI,CAACkR,GAAG,CAAC;MACrB;IACF,CAAC;IAEDmQ,OAAO,CAAC1oB,SAAS,CAAC8oB,QAAQ,GAAG,UAAUvQ,GAAG,EAAE;MAC1C,IAAIsQ,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,OAAO,IAAI,CAACoQ,GAAG,CAACpQ,GAAG,CAAC,IAAI,IAAI;IAC9B,CAAC;IAEDmQ,OAAO,CAAC1oB,SAAS,CAACP,GAAG,GAAG,UAAU8Y,GAAG,EAAE;MACrC,IAAIsQ,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAAC5C,GAAG,CAAC;MAC3C,OAAO,IAAI,CAACoQ,GAAG,CAACE,KAAK,CAAC;IACxB,CAAC;IAEDH,OAAO,CAAC1oB,SAAS,CAAC+oB,MAAM,GAAG,YAAY;MACrC,OAAO,IAAI,CAACjK,IAAI;IAClB,CAAC;IAED1gB,MAAM,CAACD,OAAO,GAAGuqB,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAAStqB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIwc,iBAAiB,GAAGxc,mBAAmB,CAAC,EAAE,CAAC;IAE/C,SAASuqB,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACzK,GAAG,GAAG,CAAC,CAAC;IACf;IACA;IAEAyK,OAAO,CAAChpB,SAAS,CAAC2H,GAAG,GAAG,UAAUuF,GAAG,EAAE;MACrC,IAAI2b,KAAK,GAAG5N,iBAAiB,CAACE,QAAQ,CAACjO,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAI,CAAC4b,QAAQ,CAACD,KAAK,CAAC,EAAE,IAAI,CAACtK,GAAG,CAACsK,KAAK,CAAC,GAAG3b,GAAG;IAClD,CAAC;IAED8b,OAAO,CAAChpB,SAAS,CAACiN,MAAM,GAAG,UAAUC,GAAG,EAAE;MACxC,OAAO,IAAI,CAACqR,GAAG,CAACtD,iBAAiB,CAACE,QAAQ,CAACjO,GAAG,CAAC,CAAC;IAClD,CAAC;IAED8b,OAAO,CAAChpB,SAAS,CAACipB,KAAK,GAAG,YAAY;MACpC,IAAI,CAAC1K,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAEDyK,OAAO,CAAChpB,SAAS,CAAC8oB,QAAQ,GAAG,UAAU5b,GAAG,EAAE;MAC1C,OAAO,IAAI,CAACqR,GAAG,CAACtD,iBAAiB,CAACE,QAAQ,CAACjO,GAAG,CAAC,CAAC,IAAIA,GAAG;IACzD,CAAC;IAED8b,OAAO,CAAChpB,SAAS,CAACkpB,OAAO,GAAG,YAAY;MACtC,OAAO,IAAI,CAACtkB,IAAI,CAAC,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEDokB,OAAO,CAAChpB,SAAS,CAAC4E,IAAI,GAAG,YAAY;MACnC,OAAOvF,MAAM,CAACyf,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC,CAACjc,MAAM;IACrC,CAAC;;IAED;IACA0mB,OAAO,CAAChpB,SAAS,CAACmpB,QAAQ,GAAG,UAAUlQ,IAAI,EAAE;MAC3C,IAAI6F,IAAI,GAAGzf,MAAM,CAACyf,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC;MAChC,IAAIjc,MAAM,GAAGwc,IAAI,CAACxc,MAAM;MACxB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC/Bsa,IAAI,CAAC5R,IAAI,CAAC,IAAI,CAACkX,GAAG,CAACO,IAAI,CAACngB,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC;IAEDqqB,OAAO,CAAChpB,SAAS,CAAC4E,IAAI,GAAG,YAAY;MACnC,OAAOvF,MAAM,CAACyf,IAAI,CAAC,IAAI,CAACP,GAAG,CAAC,CAACjc,MAAM;IACrC,CAAC;IAED0mB,OAAO,CAAChpB,SAAS,CAACopB,MAAM,GAAG,UAAUnQ,IAAI,EAAE;MACzC,IAAI9Y,CAAC,GAAG8Y,IAAI,CAAC3W,MAAM;MACnB,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,CAAC,EAAExB,CAAC,EAAE,EAAE;QAC1B,IAAI4a,CAAC,GAAGN,IAAI,CAACta,CAAC,CAAC;QACf,IAAI,CAACgJ,GAAG,CAAC4R,CAAC,CAAC;MACb;IACF,CAAC;IAEDnb,MAAM,CAACD,OAAO,GAAG6qB,OAAO;;IAExB;EAAM,CAAC,IACP;EACA,KAAO,UAAS5qB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;IACA,SAAS4qB,MAAMA,CAAA,EAAG,CAAC;;IAEnB;AACA;AACA;AACA;IACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAE;MACzC,IAAIhX,MAAM,GAAG,EAAE;MAEf,KAAK,IAAI7T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4qB,MAAM,CAACjnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtC6T,MAAM,CAAC7T,CAAC,CAAC,GAAG,EAAE;QACd,KAAK,IAAIymB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,MAAM,CAAC,CAAC,CAAC,CAAClnB,MAAM,EAAE8iB,CAAC,EAAE,EAAE;UACzC5S,MAAM,CAAC7T,CAAC,CAAC,CAACymB,CAAC,CAAC,GAAG,CAAC;UAChB,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwK,MAAM,CAAC,CAAC,CAAC,CAACjnB,MAAM,EAAEyc,CAAC,EAAE,EAAE;YACzCvM,MAAM,CAAC7T,CAAC,CAAC,CAACymB,CAAC,CAAC,IAAImE,MAAM,CAAC5qB,CAAC,CAAC,CAACogB,CAAC,CAAC,GAAGyK,MAAM,CAACzK,CAAC,CAAC,CAACqG,CAAC,CAAC;UAC7C;QACF;MACF;MACA,OAAO5S,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA6W,MAAM,CAACI,SAAS,GAAG,UAAUC,KAAK,EAAE;MAClC,IAAIlX,MAAM,GAAG,EAAE;MAEf,KAAK,IAAI7T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+qB,KAAK,CAAC,CAAC,CAAC,CAACpnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACxC6T,MAAM,CAAC7T,CAAC,CAAC,GAAG,EAAE;QACd,KAAK,IAAIymB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,KAAK,CAACpnB,MAAM,EAAE8iB,CAAC,EAAE,EAAE;UACrC5S,MAAM,CAAC7T,CAAC,CAAC,CAACymB,CAAC,CAAC,GAAGsE,KAAK,CAACtE,CAAC,CAAC,CAACzmB,CAAC,CAAC;QAC5B;MACF;MAEA,OAAO6T,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA6W,MAAM,CAACM,QAAQ,GAAG,UAAUD,KAAK,EAAEE,QAAQ,EAAE;MAC3C,IAAIpX,MAAM,GAAG,EAAE;MAEf,KAAK,IAAI7T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+qB,KAAK,CAACpnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACrC6T,MAAM,CAAC7T,CAAC,CAAC,GAAG+qB,KAAK,CAAC/qB,CAAC,CAAC,GAAGirB,QAAQ;MACjC;MAEA,OAAOpX,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA6W,MAAM,CAACQ,OAAO,GAAG,UAAUN,MAAM,EAAEC,MAAM,EAAE;MACzC,IAAIhX,MAAM,GAAG,EAAE;MAEf,KAAK,IAAI7T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4qB,MAAM,CAACjnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtC6T,MAAM,CAAC7T,CAAC,CAAC,GAAG4qB,MAAM,CAAC5qB,CAAC,CAAC,GAAG6qB,MAAM,CAAC7qB,CAAC,CAAC;MACnC;MAEA,OAAO6T,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA6W,MAAM,CAACS,UAAU,GAAG,UAAUP,MAAM,EAAEC,MAAM,EAAE;MAC5C,IAAIO,OAAO,GAAG,CAAC;MAEf,KAAK,IAAIprB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4qB,MAAM,CAACjnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACtCorB,OAAO,IAAIR,MAAM,CAAC5qB,CAAC,CAAC,GAAG6qB,MAAM,CAAC7qB,CAAC,CAAC;MAClC;MAEA,OAAOorB,OAAO;IAChB,CAAC;;IAED;AACA;AACA;AACA;IACAV,MAAM,CAACW,GAAG,GAAG,UAAUN,KAAK,EAAE;MAC5B,OAAO5lB,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC6lB,UAAU,CAACJ,KAAK,EAAEA,KAAK,CAAC,CAAC;IACjD,CAAC;;IAED;AACA;AACA;AACA;IACAL,MAAM,CAACY,SAAS,GAAG,UAAUP,KAAK,EAAE;MAClC,IAAIlX,MAAM,GAAG,EAAE;MACf,IAAI0X,SAAS,GAAG,IAAI,CAACF,GAAG,CAACN,KAAK,CAAC;MAE/B,KAAK,IAAI/qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+qB,KAAK,CAACpnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACrC6T,MAAM,CAAC7T,CAAC,CAAC,GAAG+qB,KAAK,CAAC/qB,CAAC,CAAC,GAAGurB,SAAS;MAClC;MAEA,OAAO1X,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;IACA6W,MAAM,CAACc,SAAS,GAAG,UAAUT,KAAK,EAAE;MAClC,IAAIlX,MAAM,GAAG,EAAE;MACf,IAAI4X,GAAG,GAAG,CAAC;MAEX,KAAK,IAAIzrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+qB,KAAK,CAACpnB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACrCyrB,GAAG,IAAIV,KAAK,CAAC/qB,CAAC,CAAC;MACjB;MAEAyrB,GAAG,IAAI,CAAC,CAAC,GAAGV,KAAK,CAACpnB,MAAM;MAExB,KAAK,IAAI+nB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGX,KAAK,CAACpnB,MAAM,EAAE+nB,EAAE,EAAE,EAAE;QACxC7X,MAAM,CAAC6X,EAAE,CAAC,GAAGD,GAAG,GAAGV,KAAK,CAACW,EAAE,CAAC;MAC9B;MACA,OAAO7X,MAAM;IACf,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA6W,MAAM,CAACiB,KAAK,GAAG,UAAUZ,KAAK,EAAEa,CAAC,EAAEC,GAAG,EAAE;MACtC,IAAIhY,MAAM,GAAG,EAAE;MACf,IAAIiY,KAAK,GAAG,EAAE;MACd,IAAIC,KAAK,GAAG,EAAE;;MAEd;MACA,KAAK,IAAI/rB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4rB,CAAC,CAAC,CAAC,CAAC,CAACjoB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QACpC,IAAIyrB,GAAG,GAAG,CAAC;QACX,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,CAAC,CAACjoB,MAAM,EAAE8iB,CAAC,EAAE,EAAE;UACjCgF,GAAG,IAAI,CAAC,GAAG,GAAGG,CAAC,CAACnF,CAAC,CAAC,CAACzmB,CAAC,CAAC,GAAG+qB,KAAK,CAACtE,CAAC,CAAC;QAClC;QACAqF,KAAK,CAAC9rB,CAAC,CAAC,GAAGyrB,GAAG;MAChB;MACA;MACA,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,GAAG,CAACloB,MAAM,EAAEqoB,GAAG,EAAE,EAAE;QACzC,IAAIC,IAAI,GAAG,CAAC;QACZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGL,GAAG,CAACloB,MAAM,EAAEuoB,EAAE,EAAE,EAAE;UACtCD,IAAI,IAAIJ,GAAG,CAACG,GAAG,CAAC,CAACE,EAAE,CAAC,GAAGJ,KAAK,CAACI,EAAE,CAAC;QAClC;QACAH,KAAK,CAACC,GAAG,CAAC,GAAGC,IAAI;MACnB;MACA;MACA,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,CAAC,CAACjoB,MAAM,EAAEwoB,GAAG,EAAE,EAAE;QACvC,IAAIC,KAAK,GAAG,CAAC;QACb,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,CAAC,CAAC,CAAC,CAAC,CAACjoB,MAAM,EAAE0oB,GAAG,EAAE,EAAE;UAC1CD,KAAK,IAAIR,CAAC,CAACO,GAAG,CAAC,CAACE,GAAG,CAAC,GAAGN,KAAK,CAACM,GAAG,CAAC;QACnC;QACAxY,MAAM,CAACsY,GAAG,CAAC,GAAGC,KAAK;MACrB;MAEA,OAAOvY,MAAM;IACf,CAAC;IAEDpU,MAAM,CAACD,OAAO,GAAGkrB,MAAM;;IAEvB;EAAM,CAAC,IACP;EACA,KAAO,UAASjrB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIyZ,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAACxW,MAAM,EAAEyW,KAAK,EAAE;QAAE,KAAK,IAAIzZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyZ,KAAK,CAAC9V,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI0Z,UAAU,GAAGD,KAAK,CAACzZ,CAAC,CAAC;UAAE0Z,UAAU,CAAC7Y,UAAU,GAAG6Y,UAAU,CAAC7Y,UAAU,IAAI,KAAK;UAAE6Y,UAAU,CAAC9Y,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAI8Y,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEjZ,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE0W,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAACxY,SAAS,EAAEyY,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;;IAExJ;AACA;AACA;AACA;AACA;AACA;;IAEA,IAAI5M,UAAU,GAAGxN,mBAAmB,CAAC,EAAE,CAAC;IAExC,IAAIwsB,SAAS,GAAG,YAAY;MACxB,SAASA,SAASA,CAACC,CAAC,EAAEC,eAAe,EAAE;QACnCxS,eAAe,CAAC,IAAI,EAAEsS,SAAS,CAAC;QAEhC,IAAIE,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKrd,SAAS,EAAE,IAAI,CAACqd,eAAe,GAAG,IAAI,CAACC,uBAAuB;QAElH,IAAI9oB,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI4oB,CAAC,YAAYjf,UAAU,EAAE3J,MAAM,GAAG4oB,CAAC,CAACtmB,IAAI,CAAC,CAAC,CAAC,KAAKtC,MAAM,GAAG4oB,CAAC,CAAC5oB,MAAM;QAErE,IAAI,CAAC+oB,UAAU,CAACH,CAAC,EAAE,CAAC,EAAE5oB,MAAM,GAAG,CAAC,CAAC;MACrC;MAEA4V,YAAY,CAAC+S,SAAS,EAAE,CAAC;QACrB1S,GAAG,EAAE,YAAY;QACjBvZ,KAAK,EAAE,SAASqsB,UAAUA,CAACH,CAAC,EAAEhrB,CAAC,EAAEgX,CAAC,EAAE;UAChC,IAAIhX,CAAC,GAAGgX,CAAC,EAAE;YACP,IAAID,CAAC,GAAG,IAAI,CAACqU,UAAU,CAACJ,CAAC,EAAEhrB,CAAC,EAAEgX,CAAC,CAAC;YAChC,IAAI,CAACmU,UAAU,CAACH,CAAC,EAAEhrB,CAAC,EAAE+W,CAAC,CAAC;YACxB,IAAI,CAACoU,UAAU,CAACH,CAAC,EAAEjU,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;UAChC;QACJ;MACJ,CAAC,EAAE;QACCqB,GAAG,EAAE,YAAY;QACjBvZ,KAAK,EAAE,SAASssB,UAAUA,CAACJ,CAAC,EAAEhrB,CAAC,EAAEgX,CAAC,EAAE;UAChC,IAAI7R,CAAC,GAAG,IAAI,CAACkmB,IAAI,CAACL,CAAC,EAAEhrB,CAAC,CAAC;UACvB,IAAIvB,CAAC,GAAGuB,CAAC;UACT,IAAIklB,CAAC,GAAGlO,CAAC;UACT,OAAO,IAAI,EAAE;YACT,OAAO,IAAI,CAACiU,eAAe,CAAC9lB,CAAC,EAAE,IAAI,CAACkmB,IAAI,CAACL,CAAC,EAAE9F,CAAC,CAAC,CAAC,EAAE;cAC7CA,CAAC,EAAE;YACP;YAAC,OAAO,IAAI,CAAC+F,eAAe,CAAC,IAAI,CAACI,IAAI,CAACL,CAAC,EAAEvsB,CAAC,CAAC,EAAE0G,CAAC,CAAC,EAAE;cAC9C1G,CAAC,EAAE;YACP;YAAC,IAAIA,CAAC,GAAGymB,CAAC,EAAE;cACR,IAAI,CAACoG,KAAK,CAACN,CAAC,EAAEvsB,CAAC,EAAEymB,CAAC,CAAC;cACnBzmB,CAAC,EAAE;cACHymB,CAAC,EAAE;YACP,CAAC,MAAM,OAAOA,CAAC;UACnB;QACJ;MACJ,CAAC,EAAE;QACC7M,GAAG,EAAE,MAAM;QACXvZ,KAAK,EAAE,SAASusB,IAAIA,CAACzrB,MAAM,EAAEuN,KAAK,EAAE;UAChC,IAAIvN,MAAM,YAAYmM,UAAU,EAAE,OAAOnM,MAAM,CAACoa,aAAa,CAAC7M,KAAK,CAAC,CAAC,KAAK,OAAOvN,MAAM,CAACuN,KAAK,CAAC;QAClG;MACJ,CAAC,EAAE;QACCkL,GAAG,EAAE,MAAM;QACXvZ,KAAK,EAAE,SAASysB,IAAIA,CAAC3rB,MAAM,EAAEuN,KAAK,EAAErO,KAAK,EAAE;UACvC,IAAIc,MAAM,YAAYmM,UAAU,EAAEnM,MAAM,CAACsa,aAAa,CAAC/M,KAAK,EAAErO,KAAK,CAAC,CAAC,KAAKc,MAAM,CAACuN,KAAK,CAAC,GAAGrO,KAAK;QACnG;MACJ,CAAC,EAAE;QACCuZ,GAAG,EAAE,OAAO;QACZvZ,KAAK,EAAE,SAASwsB,KAAKA,CAACN,CAAC,EAAEvsB,CAAC,EAAEymB,CAAC,EAAE;UAC3B,IAAI5G,IAAI,GAAG,IAAI,CAAC+M,IAAI,CAACL,CAAC,EAAEvsB,CAAC,CAAC;UAC1B,IAAI,CAAC8sB,IAAI,CAACP,CAAC,EAAEvsB,CAAC,EAAE,IAAI,CAAC4sB,IAAI,CAACL,CAAC,EAAE9F,CAAC,CAAC,CAAC;UAChC,IAAI,CAACqG,IAAI,CAACP,CAAC,EAAE9F,CAAC,EAAE5G,IAAI,CAAC;QACzB;MACJ,CAAC,EAAE;QACCjG,GAAG,EAAE,yBAAyB;QAC9BvZ,KAAK,EAAE,SAASosB,uBAAuBA,CAACrU,CAAC,EAAEC,CAAC,EAAE;UAC1C,OAAOA,CAAC,GAAGD,CAAC;QAChB;MACJ,CAAC,CAAC,CAAC;MAEH,OAAOkU,SAAS;IACpB,CAAC,CAAC,CAAC;IAEH7sB,MAAM,CAACD,OAAO,GAAG8sB,SAAS;;IAE1B;EAAM,CAAC,IACP;EACA,KAAO,UAAS7sB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ;IACA,SAASitB,GAAGA,CAAA,EAAG,CAAC;IAAC;;IAEjB;AACA;AACA;AACA;AACA;AACA;AACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEAA,GAAG,CAACC,GAAG,GAAG,UAAUT,CAAC,EAAE;MACrB,IAAI,CAACU,CAAC,GAAG,IAAI;MACb,IAAI,CAACC,CAAC,GAAG,IAAI;MACb,IAAI,CAAC1rB,CAAC,GAAG,IAAI;MACb,IAAI,CAACrB,CAAC,GAAG,CAAC;MACV,IAAI,CAACY,CAAC,GAAG,CAAC;MACV,IAAI,CAACZ,CAAC,GAAGosB,CAAC,CAAC5oB,MAAM;MACjB,IAAI,CAAC5C,CAAC,GAAGwrB,CAAC,CAAC,CAAC,CAAC,CAAC5oB,MAAM;MACpB,IAAIwpB,EAAE,GAAGhoB,IAAI,CAACoO,GAAG,CAAC,IAAI,CAACpT,CAAC,EAAE,IAAI,CAACY,CAAC,CAAC;MACjC,IAAI,CAACS,CAAC,GAAG,UAAUA,CAAC,EAAE;QACpB,IAAI4W,CAAC,GAAG,EAAE;QACV,OAAO5W,CAAC,EAAE,GAAG,CAAC,EAAE;UACd4W,CAAC,CAAC1P,IAAI,CAAC,CAAC,CAAC;QACX;QAAC,OAAO0P,CAAC;MACX,CAAC,CAACjT,IAAI,CAACoO,GAAG,CAAC,IAAI,CAACpT,CAAC,GAAG,CAAC,EAAE,IAAI,CAACY,CAAC,CAAC,CAAC;MAC/B,IAAI,CAACksB,CAAC,GAAG,UAAUG,IAAI,EAAE;QACvB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACD,IAAI,EAAE;UACrC,IAAIA,IAAI,CAACzpB,MAAM,IAAI,CAAC,EAAE;YACpB,OAAO,CAAC;UACV,CAAC,MAAM;YACL,IAAIonB,KAAK,GAAG,EAAE;YACd,KAAK,IAAI/qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGotB,IAAI,CAAC,CAAC,CAAC,EAAEptB,CAAC,EAAE,EAAE;cAChC+qB,KAAK,CAACriB,IAAI,CAAC2kB,QAAQ,CAACD,IAAI,CAAC3e,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC;YACA,OAAOsc,KAAK;UACd;QACF,CAAC;QACD,OAAOsC,QAAQ,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC,CAAC,IAAI,CAACjtB,CAAC,EAAEgtB,EAAE,CAAC,CAAC;MACf,IAAI,CAACD,CAAC,GAAG,UAAUE,IAAI,EAAE;QACvB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACD,IAAI,EAAE;UACrC,IAAIA,IAAI,CAACzpB,MAAM,IAAI,CAAC,EAAE;YACpB,OAAO,CAAC;UACV,CAAC,MAAM;YACL,IAAIonB,KAAK,GAAG,EAAE;YACd,KAAK,IAAI/qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGotB,IAAI,CAAC,CAAC,CAAC,EAAEptB,CAAC,EAAE,EAAE;cAChC+qB,KAAK,CAACriB,IAAI,CAAC2kB,QAAQ,CAACD,IAAI,CAAC3e,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC;YACA,OAAOsc,KAAK;UACd;QACF,CAAC;QACD,OAAOsC,QAAQ,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC,CAAC,IAAI,CAACrsB,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC,CAAC;MACnB,IAAIusB,CAAC,GAAG,UAAU9rB,CAAC,EAAE;QACnB,IAAI4W,CAAC,GAAG,EAAE;QACV,OAAO5W,CAAC,EAAE,GAAG,CAAC,EAAE;UACd4W,CAAC,CAAC1P,IAAI,CAAC,CAAC,CAAC;QACX;QAAC,OAAO0P,CAAC;MACX,CAAC,CAAC,IAAI,CAACrX,CAAC,CAAC;MACT,IAAIwsB,IAAI,GAAG,UAAU/rB,CAAC,EAAE;QACtB,IAAI4W,CAAC,GAAG,EAAE;QACV,OAAO5W,CAAC,EAAE,GAAG,CAAC,EAAE;UACd4W,CAAC,CAAC1P,IAAI,CAAC,CAAC,CAAC;QACX;QAAC,OAAO0P,CAAC;MACX,CAAC,CAAC,IAAI,CAACjY,CAAC,CAAC;MACT,IAAIqtB,KAAK,GAAG,IAAI;MAChB,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAIC,GAAG,GAAGvoB,IAAI,CAACoO,GAAG,CAAC,IAAI,CAACpT,CAAC,GAAG,CAAC,EAAE,IAAI,CAACY,CAAC,CAAC;MACtC,IAAI4sB,GAAG,GAAGxoB,IAAI,CAACqO,GAAG,CAAC,CAAC,EAAErO,IAAI,CAACoO,GAAG,CAAC,IAAI,CAACxS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACZ,CAAC,CAAC,CAAC;MACnD,KAAK,IAAIigB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjb,IAAI,CAACqO,GAAG,CAACka,GAAG,EAAEC,GAAG,CAAC,EAAEvN,CAAC,EAAE,EAAE;QAC3C,IAAIA,CAAC,GAAGsN,GAAG,EAAE;UACX,IAAI,CAAClsB,CAAC,CAAC4e,CAAC,CAAC,GAAG,CAAC;UACb,KAAK,IAAIpgB,CAAC,GAAGogB,CAAC,EAAEpgB,CAAC,GAAG,IAAI,CAACG,CAAC,EAAEH,CAAC,EAAE,EAAE;YAC/B,IAAI,CAACwB,CAAC,CAAC4e,CAAC,CAAC,GAAG2M,GAAG,CAACa,KAAK,CAAC,IAAI,CAACpsB,CAAC,CAAC4e,CAAC,CAAC,EAAEmM,CAAC,CAACvsB,CAAC,CAAC,CAACogB,CAAC,CAAC,CAAC;UAC3C;UACA;UACA,IAAI,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC,KAAK,GAAG,EAAE;YACrB,IAAImM,CAAC,CAACnM,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG,GAAG,EAAE;cACjB,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC;YACxB;YACA,KAAK,IAAIsL,EAAE,GAAGtL,CAAC,EAAEsL,EAAE,GAAG,IAAI,CAACvrB,CAAC,EAAEurB,EAAE,EAAE,EAAE;cAClCa,CAAC,CAACb,EAAE,CAAC,CAACtL,CAAC,CAAC,IAAI,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC;YACvB;YACA;YACAmM,CAAC,CAACnM,CAAC,CAAC,CAACA,CAAC,CAAC,IAAI,GAAG;UAChB;UACA,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC5e,CAAC,CAAC4e,CAAC,CAAC;QACxB;QACA,KAAK,IAAIqG,CAAC,GAAGrG,CAAC,GAAG,CAAC,EAAEqG,CAAC,GAAG,IAAI,CAAC1lB,CAAC,EAAE0lB,CAAC,EAAE,EAAE;UACnC,IAAI,UAAUoH,GAAG,EAAEC,GAAG,EAAE;YACtB,OAAOD,GAAG,IAAIC,GAAG;UACnB,CAAC,CAAC1N,CAAC,GAAGsN,GAAG,EAAE,IAAI,CAAClsB,CAAC,CAAC4e,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;YAC7B,IAAI2N,CAAC,GAAG,CAAC;YACT,KAAK,IAAI/B,GAAG,GAAG5L,CAAC,EAAE4L,GAAG,GAAG,IAAI,CAAC7rB,CAAC,EAAE6rB,GAAG,EAAE,EAAE;cACrC+B,CAAC,IAAIxB,CAAC,CAACP,GAAG,CAAC,CAAC5L,CAAC,CAAC,GAAGmM,CAAC,CAACP,GAAG,CAAC,CAACvF,CAAC,CAAC;YAC5B;YACA;YACAsH,CAAC,GAAG,CAACA,CAAC,GAAGxB,CAAC,CAACnM,CAAC,CAAC,CAACA,CAAC,CAAC;YAChB,KAAK,IAAI+L,GAAG,GAAG/L,CAAC,EAAE+L,GAAG,GAAG,IAAI,CAAChsB,CAAC,EAAEgsB,GAAG,EAAE,EAAE;cACrCI,CAAC,CAACJ,GAAG,CAAC,CAAC1F,CAAC,CAAC,IAAIsH,CAAC,GAAGxB,CAAC,CAACJ,GAAG,CAAC,CAAC/L,CAAC,CAAC;YAC5B;YACA;UACF;UACAkN,CAAC,CAAC7G,CAAC,CAAC,GAAG8F,CAAC,CAACnM,CAAC,CAAC,CAACqG,CAAC,CAAC;QAChB;QACA;QACA,IAAI,UAAUoH,GAAG,EAAEC,GAAG,EAAE;UACtB,OAAOD,GAAG,IAAIC,GAAG;QACnB,CAAC,CAACN,KAAK,EAAEpN,CAAC,GAAGsN,GAAG,CAAC,EAAE;UACjB,KAAK,IAAIM,GAAG,GAAG5N,CAAC,EAAE4N,GAAG,GAAG,IAAI,CAAC7tB,CAAC,EAAE6tB,GAAG,EAAE,EAAE;YACrC,IAAI,CAACf,CAAC,CAACe,GAAG,CAAC,CAAC5N,CAAC,CAAC,GAAGmM,CAAC,CAACyB,GAAG,CAAC,CAAC5N,CAAC,CAAC;UAC5B;UACA;QACF;QACA,IAAIA,CAAC,GAAGuN,GAAG,EAAE;UACXL,CAAC,CAAClN,CAAC,CAAC,GAAG,CAAC;UACR,KAAK,IAAI6N,GAAG,GAAG7N,CAAC,GAAG,CAAC,EAAE6N,GAAG,GAAG,IAAI,CAACltB,CAAC,EAAEktB,GAAG,EAAE,EAAE;YACzCX,CAAC,CAAClN,CAAC,CAAC,GAAG2M,GAAG,CAACa,KAAK,CAACN,CAAC,CAAClN,CAAC,CAAC,EAAEkN,CAAC,CAACW,GAAG,CAAC,CAAC;UAChC;UACA;UACA,IAAIX,CAAC,CAAClN,CAAC,CAAC,KAAK,GAAG,EAAE;YAChB,IAAIkN,CAAC,CAAClN,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;cAClBkN,CAAC,CAAClN,CAAC,CAAC,GAAG,CAACkN,CAAC,CAAClN,CAAC,CAAC;YACd;YACA,KAAK,IAAI8N,GAAG,GAAG9N,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAG,IAAI,CAACntB,CAAC,EAAEmtB,GAAG,EAAE,EAAE;cACzCZ,CAAC,CAACY,GAAG,CAAC,IAAIZ,CAAC,CAAClN,CAAC,CAAC;YAChB;YACA;YACAkN,CAAC,CAAClN,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG;UACjB;UACAkN,CAAC,CAAClN,CAAC,CAAC,GAAG,CAACkN,CAAC,CAAClN,CAAC,CAAC;UACZ,IAAI,UAAUyN,GAAG,EAAEC,GAAG,EAAE;YACtB,OAAOD,GAAG,IAAIC,GAAG;UACnB,CAAC,CAAC1N,CAAC,GAAG,CAAC,GAAG,IAAI,CAACjgB,CAAC,EAAEmtB,CAAC,CAAClN,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;YAC/B,KAAK,IAAI+N,GAAG,GAAG/N,CAAC,GAAG,CAAC,EAAE+N,GAAG,GAAG,IAAI,CAAChuB,CAAC,EAAEguB,GAAG,EAAE,EAAE;cACzCZ,IAAI,CAACY,GAAG,CAAC,GAAG,GAAG;YACjB;YACA;YACA,KAAK,IAAIjC,EAAE,GAAG9L,CAAC,GAAG,CAAC,EAAE8L,EAAE,GAAG,IAAI,CAACnrB,CAAC,EAAEmrB,EAAE,EAAE,EAAE;cACtC,KAAK,IAAIkC,GAAG,GAAGhO,CAAC,GAAG,CAAC,EAAEgO,GAAG,GAAG,IAAI,CAACjuB,CAAC,EAAEiuB,GAAG,EAAE,EAAE;gBACzCb,IAAI,CAACa,GAAG,CAAC,IAAId,CAAC,CAACpB,EAAE,CAAC,GAAGK,CAAC,CAAC6B,GAAG,CAAC,CAAClC,EAAE,CAAC;cACjC;cACA;YACF;YACA;YACA,KAAK,IAAIG,GAAG,GAAGjM,CAAC,GAAG,CAAC,EAAEiM,GAAG,GAAG,IAAI,CAACtrB,CAAC,EAAEsrB,GAAG,EAAE,EAAE;cACzC,IAAIgC,EAAE,GAAG,CAACf,CAAC,CAACjB,GAAG,CAAC,GAAGiB,CAAC,CAAClN,CAAC,GAAG,CAAC,CAAC;cAC3B,KAAK,IAAIkO,GAAG,GAAGlO,CAAC,GAAG,CAAC,EAAEkO,GAAG,GAAG,IAAI,CAACnuB,CAAC,EAAEmuB,GAAG,EAAE,EAAE;gBACzC/B,CAAC,CAAC+B,GAAG,CAAC,CAACjC,GAAG,CAAC,IAAIgC,EAAE,GAAGd,IAAI,CAACe,GAAG,CAAC;cAC/B;cACA;YACF;YACA;UACF;UACA,IAAIb,KAAK,EAAE;YACT,KAAK,IAAIc,IAAI,GAAGnO,CAAC,GAAG,CAAC,EAAEmO,IAAI,GAAG,IAAI,CAACxtB,CAAC,EAAEwtB,IAAI,EAAE,EAAE;cAC5C,IAAI,CAACrB,CAAC,CAACqB,IAAI,CAAC,CAACnO,CAAC,CAAC,GAAGkN,CAAC,CAACiB,IAAI,CAAC;YAC3B;YAAC;UACH;QACF;MACF;MAAC;MACD,IAAIhtB,CAAC,GAAG4D,IAAI,CAACoO,GAAG,CAAC,IAAI,CAACxS,CAAC,EAAE,IAAI,CAACZ,CAAC,GAAG,CAAC,CAAC;MACpC,IAAIutB,GAAG,GAAG,IAAI,CAAC3sB,CAAC,EAAE;QAChB,IAAI,CAACS,CAAC,CAACksB,GAAG,CAAC,GAAGnB,CAAC,CAACmB,GAAG,CAAC,CAACA,GAAG,CAAC;MAC3B;MACA,IAAI,IAAI,CAACvtB,CAAC,GAAGoB,CAAC,EAAE;QACd,IAAI,CAACC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;MACrB;MACA,IAAIosB,GAAG,GAAG,CAAC,GAAGpsB,CAAC,EAAE;QACf+rB,CAAC,CAACK,GAAG,CAAC,GAAGpB,CAAC,CAACoB,GAAG,CAAC,CAACpsB,CAAC,GAAG,CAAC,CAAC;MACxB;MACA+rB,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;MACd,IAAIisB,KAAK,EAAE;QACT,KAAK,IAAIgB,GAAG,GAAGd,GAAG,EAAEc,GAAG,GAAGrB,EAAE,EAAEqB,GAAG,EAAE,EAAE;UACnC,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACtuB,CAAC,EAAEsuB,IAAI,EAAE,EAAE;YACxC,IAAI,CAACxB,CAAC,CAACwB,IAAI,CAAC,CAACD,GAAG,CAAC,GAAG,GAAG;UACzB;UACA;UACA,IAAI,CAACvB,CAAC,CAACuB,GAAG,CAAC,CAACA,GAAG,CAAC,GAAG,GAAG;QACxB;QAAC;QACD,KAAK,IAAIE,EAAE,GAAGhB,GAAG,GAAG,CAAC,EAAEgB,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;UACpC,IAAI,IAAI,CAACltB,CAAC,CAACktB,EAAE,CAAC,KAAK,GAAG,EAAE;YACtB,KAAK,IAAIC,GAAG,GAAGD,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAGxB,EAAE,EAAEwB,GAAG,EAAE,EAAE;cACtC,IAAIC,GAAG,GAAG,CAAC;cACX,KAAK,IAAIC,IAAI,GAAGH,EAAE,EAAEG,IAAI,GAAG,IAAI,CAAC1uB,CAAC,EAAE0uB,IAAI,EAAE,EAAE;gBACzCD,GAAG,IAAI,IAAI,CAAC3B,CAAC,CAAC4B,IAAI,CAAC,CAACH,EAAE,CAAC,GAAG,IAAI,CAACzB,CAAC,CAAC4B,IAAI,CAAC,CAACF,GAAG,CAAC;cAC7C;cAAC;cACDC,GAAG,GAAG,CAACA,GAAG,GAAG,IAAI,CAAC3B,CAAC,CAACyB,EAAE,CAAC,CAACA,EAAE,CAAC;cAC3B,KAAK,IAAII,IAAI,GAAGJ,EAAE,EAAEI,IAAI,GAAG,IAAI,CAAC3uB,CAAC,EAAE2uB,IAAI,EAAE,EAAE;gBACzC,IAAI,CAAC7B,CAAC,CAAC6B,IAAI,CAAC,CAACH,GAAG,CAAC,IAAIC,GAAG,GAAG,IAAI,CAAC3B,CAAC,CAAC6B,IAAI,CAAC,CAACJ,EAAE,CAAC;cAC7C;cAAC;YACH;YAAC;YACD,KAAK,IAAIK,IAAI,GAAGL,EAAE,EAAEK,IAAI,GAAG,IAAI,CAAC5uB,CAAC,EAAE4uB,IAAI,EAAE,EAAE;cACzC,IAAI,CAAC9B,CAAC,CAAC8B,IAAI,CAAC,CAACL,EAAE,CAAC,GAAG,CAAC,IAAI,CAACzB,CAAC,CAAC8B,IAAI,CAAC,CAACL,EAAE,CAAC;YACtC;YAAC;YACD,IAAI,CAACzB,CAAC,CAACyB,EAAE,CAAC,CAACA,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAACzB,CAAC,CAACyB,EAAE,CAAC,CAACA,EAAE,CAAC;YACrC,KAAK,IAAIM,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGN,EAAE,GAAG,CAAC,EAAEM,IAAI,EAAE,EAAE;cACxC,IAAI,CAAC/B,CAAC,CAAC+B,IAAI,CAAC,CAACN,EAAE,CAAC,GAAG,GAAG;YACxB;YAAC;UACH,CAAC,MAAM;YACL,KAAK,IAAIO,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC9uB,CAAC,EAAE8uB,IAAI,EAAE,EAAE;cACxC,IAAI,CAAChC,CAAC,CAACgC,IAAI,CAAC,CAACP,EAAE,CAAC,GAAG,GAAG;YACxB;YAAC;YACD,IAAI,CAACzB,CAAC,CAACyB,EAAE,CAAC,CAACA,EAAE,CAAC,GAAG,GAAG;UACtB;QACF;QAAC;MACH;MACA,IAAIjB,KAAK,EAAE;QACT,KAAK,IAAIyB,GAAG,GAAG,IAAI,CAACnuB,CAAC,GAAG,CAAC,EAAEmuB,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;UAC1C,IAAI,UAAUrB,GAAG,EAAEC,GAAG,EAAE;YACtB,OAAOD,GAAG,IAAIC,GAAG;UACnB,CAAC,CAACoB,GAAG,GAAGvB,GAAG,EAAEL,CAAC,CAAC4B,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE;YAC5B,KAAK,IAAIC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAGhC,EAAE,EAAEgC,GAAG,EAAE,EAAE;cACvC,IAAIC,GAAG,GAAG,CAAC;cACX,KAAK,IAAIC,IAAI,GAAGH,GAAG,GAAG,CAAC,EAAEG,IAAI,GAAG,IAAI,CAACtuB,CAAC,EAAEsuB,IAAI,EAAE,EAAE;gBAC9CD,GAAG,IAAI,IAAI,CAAClC,CAAC,CAACmC,IAAI,CAAC,CAACH,GAAG,CAAC,GAAG,IAAI,CAAChC,CAAC,CAACmC,IAAI,CAAC,CAACF,GAAG,CAAC;cAC9C;cAAC;cACDC,GAAG,GAAG,CAACA,GAAG,GAAG,IAAI,CAAClC,CAAC,CAACgC,GAAG,GAAG,CAAC,CAAC,CAACA,GAAG,CAAC;cACjC,KAAK,IAAII,IAAI,GAAGJ,GAAG,GAAG,CAAC,EAAEI,IAAI,GAAG,IAAI,CAACvuB,CAAC,EAAEuuB,IAAI,EAAE,EAAE;gBAC9C,IAAI,CAACpC,CAAC,CAACoC,IAAI,CAAC,CAACH,GAAG,CAAC,IAAIC,GAAG,GAAG,IAAI,CAAClC,CAAC,CAACoC,IAAI,CAAC,CAACJ,GAAG,CAAC;cAC9C;cAAC;YACH;YAAC;UACH;UACA,KAAK,IAAIK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACxuB,CAAC,EAAEwuB,IAAI,EAAE,EAAE;YACxC,IAAI,CAACrC,CAAC,CAACqC,IAAI,CAAC,CAACL,GAAG,CAAC,GAAG,GAAG;UACzB;UAAC;UACD,IAAI,CAAChC,CAAC,CAACgC,GAAG,CAAC,CAACA,GAAG,CAAC,GAAG,GAAG;QACxB;QAAC;MACH;MACA,IAAIM,EAAE,GAAGjuB,CAAC,GAAG,CAAC;MACd,IAAIkuB,IAAI,GAAG,CAAC;MACZ,IAAIC,GAAG,GAAGvqB,IAAI,CAACwqB,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;MAC9B,IAAIC,IAAI,GAAGzqB,IAAI,CAACwqB,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;MAChC,OAAOpuB,CAAC,GAAG,CAAC,EAAE;QACZ,IAAIsuB,GAAG,GAAG,KAAK,CAAC;QAChB,IAAIC,IAAI,GAAG,KAAK,CAAC;QACjB,KAAKD,GAAG,GAAGtuB,CAAC,GAAG,CAAC,EAAEsuB,GAAG,IAAI,CAAC,CAAC,EAAEA,GAAG,EAAE,EAAE;UAClC,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;YACd;UACF;UACA,IAAI1qB,IAAI,CAACC,GAAG,CAACkoB,CAAC,CAACuC,GAAG,CAAC,CAAC,IAAID,IAAI,GAAGF,GAAG,IAAIvqB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACquB,GAAG,CAAC,CAAC,GAAG1qB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACquB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACxFvC,CAAC,CAACuC,GAAG,CAAC,GAAG,GAAG;YACZ;UACF;QACF;QAAC;QACD,IAAIA,GAAG,KAAKtuB,CAAC,GAAG,CAAC,EAAE;UACjBuuB,IAAI,GAAG,CAAC;QACV,CAAC,MAAM;UACL,IAAIC,EAAE,GAAG,KAAK,CAAC;UACf,KAAKA,EAAE,GAAGxuB,CAAC,GAAG,CAAC,EAAEwuB,EAAE,IAAIF,GAAG,EAAEE,EAAE,EAAE,EAAE;YAChC,IAAIA,EAAE,KAAKF,GAAG,EAAE;cACd;YACF;YACA,IAAIG,GAAG,GAAG,CAACD,EAAE,KAAKxuB,CAAC,GAAG4D,IAAI,CAACC,GAAG,CAACkoB,CAAC,CAACyC,EAAE,CAAC,CAAC,GAAG,GAAG,KAAKA,EAAE,KAAKF,GAAG,GAAG,CAAC,GAAG1qB,IAAI,CAACC,GAAG,CAACkoB,CAAC,CAACyC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3F,IAAI5qB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACuuB,EAAE,CAAC,CAAC,IAAIH,IAAI,GAAGF,GAAG,GAAGM,GAAG,EAAE;cAC5C,IAAI,CAACxuB,CAAC,CAACuuB,EAAE,CAAC,GAAG,GAAG;cAChB;YACF;UACF;UAAC;UACD,IAAIA,EAAE,KAAKF,GAAG,EAAE;YACdC,IAAI,GAAG,CAAC;UACV,CAAC,MAAM,IAAIC,EAAE,KAAKxuB,CAAC,GAAG,CAAC,EAAE;YACvBuuB,IAAI,GAAG,CAAC;UACV,CAAC,MAAM;YACLA,IAAI,GAAG,CAAC;YACRD,GAAG,GAAGE,EAAE;UACV;QACF;QACAF,GAAG,EAAE;QACL,QAAQC,IAAI;UACV,KAAK,CAAC;YACJ;cACE,IAAIG,CAAC,GAAG3C,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC;cAChB+rB,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;cACd,KAAK,IAAI2uB,GAAG,GAAG3uB,CAAC,GAAG,CAAC,EAAE2uB,GAAG,IAAIL,GAAG,EAAEK,GAAG,EAAE,EAAE;gBACvC,IAAIC,GAAG,GAAGpD,GAAG,CAACa,KAAK,CAAC,IAAI,CAACpsB,CAAC,CAAC0uB,GAAG,CAAC,EAAED,CAAC,CAAC;gBACnC,IAAIG,EAAE,GAAG,IAAI,CAAC5uB,CAAC,CAAC0uB,GAAG,CAAC,GAAGC,GAAG;gBAC1B,IAAIE,EAAE,GAAGJ,CAAC,GAAGE,GAAG;gBAChB,IAAI,CAAC3uB,CAAC,CAAC0uB,GAAG,CAAC,GAAGC,GAAG;gBACjB,IAAID,GAAG,KAAKL,GAAG,EAAE;kBACfI,CAAC,GAAG,CAACI,EAAE,GAAG/C,CAAC,CAAC4C,GAAG,GAAG,CAAC,CAAC;kBACpB5C,CAAC,CAAC4C,GAAG,GAAG,CAAC,CAAC,GAAGE,EAAE,GAAG9C,CAAC,CAAC4C,GAAG,GAAG,CAAC,CAAC;gBAC9B;gBACA,IAAIzC,KAAK,EAAE;kBACT,KAAK,IAAI6C,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACvvB,CAAC,EAAEuvB,IAAI,EAAE,EAAE;oBACxCH,GAAG,GAAGC,EAAE,GAAG,IAAI,CAAClD,CAAC,CAACoD,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGG,EAAE,GAAG,IAAI,CAACnD,CAAC,CAACoD,IAAI,CAAC,CAAC/uB,CAAC,GAAG,CAAC,CAAC;oBACvD,IAAI,CAAC2rB,CAAC,CAACoD,IAAI,CAAC,CAAC/uB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC8uB,EAAE,GAAG,IAAI,CAACnD,CAAC,CAACoD,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGE,EAAE,GAAG,IAAI,CAAClD,CAAC,CAACoD,IAAI,CAAC,CAAC/uB,CAAC,GAAG,CAAC,CAAC;oBACxE,IAAI,CAAC2rB,CAAC,CAACoD,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGC,GAAG;kBACzB;kBAAC;gBACH;cACF;cAAC;YACH;YAAC;YACD;UACF,KAAK,CAAC;YACJ;cACE,IAAII,EAAE,GAAGjD,CAAC,CAACuC,GAAG,GAAG,CAAC,CAAC;cACnBvC,CAAC,CAACuC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;cAChB,KAAK,IAAIW,GAAG,GAAGX,GAAG,EAAEW,GAAG,GAAGjvB,CAAC,EAAEivB,GAAG,EAAE,EAAE;gBAClC,IAAIC,GAAG,GAAG1D,GAAG,CAACa,KAAK,CAAC,IAAI,CAACpsB,CAAC,CAACgvB,GAAG,CAAC,EAAED,EAAE,CAAC;gBACpC,IAAIG,GAAG,GAAG,IAAI,CAAClvB,CAAC,CAACgvB,GAAG,CAAC,GAAGC,GAAG;gBAC3B,IAAIE,GAAG,GAAGJ,EAAE,GAAGE,GAAG;gBAClB,IAAI,CAACjvB,CAAC,CAACgvB,GAAG,CAAC,GAAGC,GAAG;gBACjBF,EAAE,GAAG,CAACI,GAAG,GAAGrD,CAAC,CAACkD,GAAG,CAAC;gBAClBlD,CAAC,CAACkD,GAAG,CAAC,GAAGE,GAAG,GAAGpD,CAAC,CAACkD,GAAG,CAAC;gBACrB,IAAIhD,KAAK,EAAE;kBACT,KAAK,IAAIoD,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACzwB,CAAC,EAAEywB,IAAI,EAAE,EAAE;oBACxCH,GAAG,GAAGC,GAAG,GAAG,IAAI,CAACzD,CAAC,CAAC2D,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGG,GAAG,GAAG,IAAI,CAAC1D,CAAC,CAAC2D,IAAI,CAAC,CAACf,GAAG,GAAG,CAAC,CAAC;oBAC3D,IAAI,CAAC5C,CAAC,CAAC2D,IAAI,CAAC,CAACf,GAAG,GAAG,CAAC,CAAC,GAAG,CAACc,GAAG,GAAG,IAAI,CAAC1D,CAAC,CAAC2D,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGE,GAAG,GAAG,IAAI,CAACzD,CAAC,CAAC2D,IAAI,CAAC,CAACf,GAAG,GAAG,CAAC,CAAC;oBAC9E,IAAI,CAAC5C,CAAC,CAAC2D,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGC,GAAG;kBACzB;kBAAC;gBACH;cACF;cAAC;YACH;YAAC;YACD;UACF,KAAK,CAAC;YACJ;cACE,IAAII,KAAK,GAAG1rB,IAAI,CAACqO,GAAG,CAACrO,IAAI,CAACqO,GAAG,CAACrO,IAAI,CAACqO,GAAG,CAACrO,IAAI,CAACqO,GAAG,CAACrO,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAACC,GAAG,CAACkoB,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,CAAC,CAACquB,GAAG,CAAC,CAAC,CAAC,EAAE1qB,IAAI,CAACC,GAAG,CAACkoB,CAAC,CAACuC,GAAG,CAAC,CAAC,CAAC;cACjK,IAAIiB,EAAE,GAAG,IAAI,CAACtvB,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,GAAGsvB,KAAK;cAC9B,IAAIE,IAAI,GAAG,IAAI,CAACvvB,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,GAAGsvB,KAAK;cAChC,IAAIG,IAAI,GAAG1D,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC,GAAGsvB,KAAK;cAC3B,IAAII,EAAE,GAAG,IAAI,CAACzvB,CAAC,CAACquB,GAAG,CAAC,GAAGgB,KAAK;cAC5B,IAAIK,EAAE,GAAG5D,CAAC,CAACuC,GAAG,CAAC,GAAGgB,KAAK;cACvB,IAAIxY,CAAC,GAAG,CAAC,CAAC0Y,IAAI,GAAGD,EAAE,KAAKC,IAAI,GAAGD,EAAE,CAAC,GAAGE,IAAI,GAAGA,IAAI,IAAI,GAAG;cACvD,IAAI5wB,CAAC,GAAG0wB,EAAE,GAAGE,IAAI,IAAIF,EAAE,GAAGE,IAAI,CAAC;cAC/B,IAAIhhB,KAAK,GAAG,GAAG;cACf,IAAI,UAAU6d,GAAG,EAAEC,GAAG,EAAE;gBACtB,OAAOD,GAAG,IAAIC,GAAG;cACnB,CAAC,CAACzV,CAAC,KAAK,GAAG,EAAEjY,CAAC,KAAK,GAAG,CAAC,EAAE;gBACvB4P,KAAK,GAAG7K,IAAI,CAACG,IAAI,CAAC+S,CAAC,GAAGA,CAAC,GAAGjY,CAAC,CAAC;gBAC5B,IAAIiY,CAAC,GAAG,GAAG,EAAE;kBACXrI,KAAK,GAAG,CAACA,KAAK;gBAChB;gBACAA,KAAK,GAAG5P,CAAC,IAAIiY,CAAC,GAAGrI,KAAK,CAAC;cACzB;cACA,IAAImhB,GAAG,GAAG,CAACF,EAAE,GAAGH,EAAE,KAAKG,EAAE,GAAGH,EAAE,CAAC,GAAG9gB,KAAK;cACvC,IAAIohB,CAAC,GAAGH,EAAE,GAAGC,EAAE;cACf,KAAK,IAAIG,GAAG,GAAGxB,GAAG,EAAEwB,GAAG,GAAG9vB,CAAC,GAAG,CAAC,EAAE8vB,GAAG,EAAE,EAAE;gBACtC,IAAIC,GAAG,GAAGvE,GAAG,CAACa,KAAK,CAACuD,GAAG,EAAEC,CAAC,CAAC;gBAC3B,IAAIG,IAAI,GAAGJ,GAAG,GAAGG,GAAG;gBACpB,IAAIE,IAAI,GAAGJ,CAAC,GAAGE,GAAG;gBAClB,IAAID,GAAG,KAAKxB,GAAG,EAAE;kBACfvC,CAAC,CAAC+D,GAAG,GAAG,CAAC,CAAC,GAAGC,GAAG;gBAClB;gBACAH,GAAG,GAAGI,IAAI,GAAG,IAAI,CAAC/vB,CAAC,CAAC6vB,GAAG,CAAC,GAAGG,IAAI,GAAGlE,CAAC,CAAC+D,GAAG,CAAC;gBACxC/D,CAAC,CAAC+D,GAAG,CAAC,GAAGE,IAAI,GAAGjE,CAAC,CAAC+D,GAAG,CAAC,GAAGG,IAAI,GAAG,IAAI,CAAChwB,CAAC,CAAC6vB,GAAG,CAAC;gBAC3CD,CAAC,GAAGI,IAAI,GAAG,IAAI,CAAChwB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC7vB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC,GAAGE,IAAI,GAAG,IAAI,CAAC/vB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC;gBACxC,IAAI5D,KAAK,EAAE;kBACT,KAAK,IAAIgE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC1wB,CAAC,EAAE0wB,IAAI,EAAE,EAAE;oBACxCH,GAAG,GAAGC,IAAI,GAAG,IAAI,CAACrE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGG,IAAI,GAAG,IAAI,CAACtE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,GAAG,CAAC,CAAC;oBAC7D,IAAI,CAACnE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,GAAG,CAAC,CAAC,GAAG,CAACG,IAAI,GAAG,IAAI,CAACtE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGE,IAAI,GAAG,IAAI,CAACrE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,GAAG,CAAC,CAAC;oBAChF,IAAI,CAACnE,CAAC,CAACuE,IAAI,CAAC,CAACJ,GAAG,CAAC,GAAGC,GAAG;kBACzB;kBAAC;gBACH;gBACAA,GAAG,GAAGvE,GAAG,CAACa,KAAK,CAACuD,GAAG,EAAEC,CAAC,CAAC;gBACvBG,IAAI,GAAGJ,GAAG,GAAGG,GAAG;gBAChBE,IAAI,GAAGJ,CAAC,GAAGE,GAAG;gBACd,IAAI,CAAC9vB,CAAC,CAAC6vB,GAAG,CAAC,GAAGC,GAAG;gBACjBH,GAAG,GAAGI,IAAI,GAAGjE,CAAC,CAAC+D,GAAG,CAAC,GAAGG,IAAI,GAAG,IAAI,CAAChwB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC;gBAC5C,IAAI,CAAC7vB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC,GAAG,CAACG,IAAI,GAAGlE,CAAC,CAAC+D,GAAG,CAAC,GAAGE,IAAI,GAAG,IAAI,CAAC/vB,CAAC,CAAC6vB,GAAG,GAAG,CAAC,CAAC;gBACzDD,CAAC,GAAGI,IAAI,GAAGlE,CAAC,CAAC+D,GAAG,GAAG,CAAC,CAAC;gBACrB/D,CAAC,CAAC+D,GAAG,GAAG,CAAC,CAAC,GAAGE,IAAI,GAAGjE,CAAC,CAAC+D,GAAG,GAAG,CAAC,CAAC;gBAC9B,IAAI7D,KAAK,IAAI6D,GAAG,GAAG,IAAI,CAAClxB,CAAC,GAAG,CAAC,EAAE;kBAC7B,KAAK,IAAIuxB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACvxB,CAAC,EAAEuxB,IAAI,EAAE,EAAE;oBACxCJ,GAAG,GAAGC,IAAI,GAAG,IAAI,CAACtE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,CAAC,GAAGG,IAAI,GAAG,IAAI,CAACvE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,GAAG,CAAC,CAAC;oBAC7D,IAAI,CAACpE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,GAAG,CAAC,CAAC,GAAG,CAACG,IAAI,GAAG,IAAI,CAACvE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,CAAC,GAAGE,IAAI,GAAG,IAAI,CAACtE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,GAAG,CAAC,CAAC;oBAChF,IAAI,CAACpE,CAAC,CAACyE,IAAI,CAAC,CAACL,GAAG,CAAC,GAAGC,GAAG;kBACzB;kBAAC;gBACH;cACF;cAAC;cACDhE,CAAC,CAAC/rB,CAAC,GAAG,CAAC,CAAC,GAAG4vB,GAAG;cACd1B,IAAI,GAAGA,IAAI,GAAG,CAAC;YACjB;YAAC;YACD;UACF,KAAK,CAAC;YACJ;cACE,IAAI,IAAI,CAACjuB,CAAC,CAACquB,GAAG,CAAC,IAAI,GAAG,EAAE;gBACtB,IAAI,CAACruB,CAAC,CAACquB,GAAG,CAAC,GAAG,IAAI,CAACruB,CAAC,CAACquB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAACruB,CAAC,CAACquB,GAAG,CAAC,GAAG,GAAG;gBACpD,IAAIpC,KAAK,EAAE;kBACT,KAAK,IAAIkE,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAInC,EAAE,EAAEmC,IAAI,EAAE,EAAE;oBACrC,IAAI,CAACzE,CAAC,CAACyE,IAAI,CAAC,CAAC9B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC3C,CAAC,CAACyE,IAAI,CAAC,CAAC9B,GAAG,CAAC;kBACxC;kBAAC;gBACH;cACF;cACA,OAAOA,GAAG,GAAGL,EAAE,EAAE;gBACf,IAAI,IAAI,CAAChuB,CAAC,CAACquB,GAAG,CAAC,IAAI,IAAI,CAACruB,CAAC,CAACquB,GAAG,GAAG,CAAC,CAAC,EAAE;kBAClC;gBACF;gBACA,IAAI+B,GAAG,GAAG,IAAI,CAACpwB,CAAC,CAACquB,GAAG,CAAC;gBACrB,IAAI,CAACruB,CAAC,CAACquB,GAAG,CAAC,GAAG,IAAI,CAACruB,CAAC,CAACquB,GAAG,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAACruB,CAAC,CAACquB,GAAG,GAAG,CAAC,CAAC,GAAG+B,GAAG;gBACrB,IAAInE,KAAK,IAAIoC,GAAG,GAAG,IAAI,CAAC9uB,CAAC,GAAG,CAAC,EAAE;kBAC7B,KAAK,IAAI8wB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC9wB,CAAC,EAAE8wB,IAAI,EAAE,EAAE;oBACxCD,GAAG,GAAG,IAAI,CAAC1E,CAAC,CAAC2E,IAAI,CAAC,CAAChC,GAAG,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC3C,CAAC,CAAC2E,IAAI,CAAC,CAAChC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC3C,CAAC,CAAC2E,IAAI,CAAC,CAAChC,GAAG,CAAC;oBACzC,IAAI,CAAC3C,CAAC,CAAC2E,IAAI,CAAC,CAAChC,GAAG,CAAC,GAAG+B,GAAG;kBACzB;kBAAC;gBACH;gBACA,IAAIpE,KAAK,IAAIqC,GAAG,GAAG,IAAI,CAAC1vB,CAAC,GAAG,CAAC,EAAE;kBAC7B,KAAK,IAAI2xB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC3xB,CAAC,EAAE2xB,IAAI,EAAE,EAAE;oBACxCF,GAAG,GAAG,IAAI,CAAC3E,CAAC,CAAC6E,IAAI,CAAC,CAACjC,GAAG,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC5C,CAAC,CAAC6E,IAAI,CAAC,CAACjC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC5C,CAAC,CAAC6E,IAAI,CAAC,CAACjC,GAAG,CAAC;oBACzC,IAAI,CAAC5C,CAAC,CAAC6E,IAAI,CAAC,CAACjC,GAAG,CAAC,GAAG+B,GAAG;kBACzB;kBAAC;gBACH;gBACA/B,GAAG,EAAE;cACP;cAAC;cACDJ,IAAI,GAAG,CAAC;cACRluB,CAAC,EAAE;YACL;YAAC;YACD;QACJ;MACF;MAAC;MACD,IAAIsS,MAAM,GAAG;QAAEoZ,CAAC,EAAE,IAAI,CAACA,CAAC;QAAEC,CAAC,EAAE,IAAI,CAACA,CAAC;QAAE6E,CAAC,EAAE,IAAI,CAACvwB;MAAE,CAAC;MAChD,OAAOqS,MAAM;IACf,CAAC;;IAED;IACAkZ,GAAG,CAACa,KAAK,GAAG,UAAUxV,CAAC,EAAEC,CAAC,EAAE;MAC1B,IAAIE,CAAC,GAAG,KAAK,CAAC;MACd,IAAIpT,IAAI,CAACC,GAAG,CAACgT,CAAC,CAAC,GAAGjT,IAAI,CAACC,GAAG,CAACiT,CAAC,CAAC,EAAE;QAC7BE,CAAC,GAAGF,CAAC,GAAGD,CAAC;QACTG,CAAC,GAAGpT,IAAI,CAACC,GAAG,CAACgT,CAAC,CAAC,GAAGjT,IAAI,CAACG,IAAI,CAAC,CAAC,GAAGiT,CAAC,GAAGA,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIF,CAAC,IAAI,CAAC,EAAE;QACjBE,CAAC,GAAGH,CAAC,GAAGC,CAAC;QACTE,CAAC,GAAGpT,IAAI,CAACC,GAAG,CAACiT,CAAC,CAAC,GAAGlT,IAAI,CAACG,IAAI,CAAC,CAAC,GAAGiT,CAAC,GAAGA,CAAC,CAAC;MACxC,CAAC,MAAM;QACLA,CAAC,GAAG,GAAG;MACT;MACA,OAAOA,CAAC;IACV,CAAC;IAED9Y,MAAM,CAACD,OAAO,GAAGutB,GAAG;;IAEpB;EAAM,CAAC,IACP;EACA,KAAO,UAASttB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAIyZ,YAAY,GAAG,YAAY;MAAE,SAASC,gBAAgBA,CAACxW,MAAM,EAAEyW,KAAK,EAAE;QAAE,KAAK,IAAIzZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyZ,KAAK,CAAC9V,MAAM,EAAE3D,CAAC,EAAE,EAAE;UAAE,IAAI0Z,UAAU,GAAGD,KAAK,CAACzZ,CAAC,CAAC;UAAE0Z,UAAU,CAAC7Y,UAAU,GAAG6Y,UAAU,CAAC7Y,UAAU,IAAI,KAAK;UAAE6Y,UAAU,CAAC9Y,YAAY,GAAG,IAAI;UAAE,IAAI,OAAO,IAAI8Y,UAAU,EAAEA,UAAU,CAACC,QAAQ,GAAG,IAAI;UAAEjZ,MAAM,CAACC,cAAc,CAACqC,MAAM,EAAE0W,UAAU,CAACE,GAAG,EAAEF,UAAU,CAAC;QAAE;MAAE;MAAE,OAAO,UAAUG,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;QAAE,IAAID,UAAU,EAAEN,gBAAgB,CAACK,WAAW,CAACxY,SAAS,EAAEyY,UAAU,CAAC;QAAE,IAAIC,WAAW,EAAEP,gBAAgB,CAACK,WAAW,EAAEE,WAAW,CAAC;QAAE,OAAOF,WAAW;MAAE,CAAC;IAAE,CAAC,CAAC,CAAC;IAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;MAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;QAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;MAAE;IAAE;;IAExJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,IAAI8X,eAAe,GAAG,YAAY;MAC9B,SAASA,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAE;QAC3C,IAAIC,WAAW,GAAGhN,SAAS,CAACxhB,MAAM,GAAG,CAAC,IAAIwhB,SAAS,CAAC,CAAC,CAAC,KAAKhW,SAAS,GAAGgW,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QACvF,IAAIiN,gBAAgB,GAAGjN,SAAS,CAACxhB,MAAM,GAAG,CAAC,IAAIwhB,SAAS,CAAC,CAAC,CAAC,KAAKhW,SAAS,GAAGgW,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7F,IAAIkN,WAAW,GAAGlN,SAAS,CAACxhB,MAAM,GAAG,CAAC,IAAIwhB,SAAS,CAAC,CAAC,CAAC,KAAKhW,SAAS,GAAGgW,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAExFnL,eAAe,CAAC,IAAI,EAAEgY,eAAe,CAAC;QAEtC,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;;QAE9B;QACA,IAAI,CAACC,IAAI,GAAGL,SAAS,CAACtuB,MAAM,GAAG,CAAC;QAChC,IAAI,CAAC4uB,IAAI,GAAGL,SAAS,CAACvuB,MAAM,GAAG,CAAC;;QAEhC;QACA,IAAI,CAAC4hB,IAAI,GAAG,IAAIzgB,KAAK,CAAC,IAAI,CAACwtB,IAAI,CAAC;QAChC,KAAK,IAAItyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsyB,IAAI,EAAEtyB,CAAC,EAAE,EAAE;UAChC,IAAI,CAACulB,IAAI,CAACvlB,CAAC,CAAC,GAAG,IAAI8E,KAAK,CAAC,IAAI,CAACytB,IAAI,CAAC;UAEnC,KAAK,IAAI9L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8L,IAAI,EAAE9L,CAAC,EAAE,EAAE;YAChC,IAAI,CAAClB,IAAI,CAACvlB,CAAC,CAAC,CAACymB,CAAC,CAAC,GAAG,CAAC;UACvB;QACJ;;QAEA;QACA,IAAI,CAAC+L,aAAa,GAAG,IAAI1tB,KAAK,CAAC,IAAI,CAACwtB,IAAI,CAAC;QACzC,KAAK,IAAI5G,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAAC4G,IAAI,EAAE5G,EAAE,EAAE,EAAE;UACnC,IAAI,CAAC8G,aAAa,CAAC9G,EAAE,CAAC,GAAG,IAAI5mB,KAAK,CAAC,IAAI,CAACytB,IAAI,CAAC;UAE7C,KAAK,IAAIrG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACqG,IAAI,EAAErG,EAAE,EAAE,EAAE;YACnC,IAAI,CAACsG,aAAa,CAAC9G,EAAE,CAAC,CAACQ,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACJ;;QAEA;QACA,IAAI,CAACuG,UAAU,GAAG,EAAE;;QAEpB;QACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;QAEf;QACA,IAAI,CAACC,YAAY,CAAC,CAAC;MACvB;MAEApZ,YAAY,CAACyY,eAAe,EAAE,CAAC;QAC3BpY,GAAG,EAAE,UAAU;QACfvZ,KAAK,EAAE,SAASuyB,QAAQA,CAAA,EAAG;UACvB,OAAO,IAAI,CAACF,KAAK;QACrB;MACJ,CAAC,EAAE;QACC9Y,GAAG,EAAE,eAAe;QACpBvZ,KAAK,EAAE,SAASwyB,aAAaA,CAAA,EAAG;UAC5B,OAAO,IAAI,CAACJ,UAAU;QAC1B;;QAEA;MAEJ,CAAC,EAAE;QACC7Y,GAAG,EAAE,cAAc;QACnBvZ,KAAK,EAAE,SAASsyB,YAAYA,CAAA,EAAG;UAC3B;UACA,KAAK,IAAIlM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8L,IAAI,EAAE9L,CAAC,EAAE,EAAE;YAChC,IAAI,CAAClB,IAAI,CAAC,CAAC,CAAC,CAACkB,CAAC,CAAC,GAAG,IAAI,CAAClB,IAAI,CAAC,CAAC,CAAC,CAACkB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC4L,WAAW;YACxD,IAAI,CAACG,aAAa,CAAC,CAAC,CAAC,CAAC/L,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;UACnD;;UAEA;UACA,KAAK,IAAIzmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsyB,IAAI,EAAEtyB,CAAC,EAAE,EAAE;YAChC,IAAI,CAACulB,IAAI,CAACvlB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACulB,IAAI,CAACvlB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACqyB,WAAW;YACxD,IAAI,CAACG,aAAa,CAACxyB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;UACnD;;UAEA;UACA,KAAK,IAAIgsB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACsG,IAAI,EAAEtG,GAAG,EAAE,EAAE;YACtC,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACkG,IAAI,EAAElG,GAAG,EAAE,EAAE;cACtC;cACA,IAAIyG,IAAI,GAAG,KAAK,CAAC;cACjB,IAAI,IAAI,CAACb,SAAS,CAACjG,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAACkG,SAAS,CAAC7F,GAAG,GAAG,CAAC,CAAC,EAAEyG,IAAI,GAAG,IAAI,CAACvN,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACK,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC8F,WAAW,CAAC,KAAKW,IAAI,GAAG,IAAI,CAACvN,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACK,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC+F,gBAAgB;cAE9K,IAAIW,EAAE,GAAG,IAAI,CAACxN,IAAI,CAACyG,GAAG,GAAG,CAAC,CAAC,CAACK,GAAG,CAAC,GAAG,IAAI,CAACgG,WAAW;cACnD,IAAIrnB,IAAI,GAAG,IAAI,CAACua,IAAI,CAACyG,GAAG,CAAC,CAACK,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACgG,WAAW;;cAErD;cACA,IAAIW,KAAK,GAAG,CAACF,IAAI,EAAEC,EAAE,EAAE/nB,IAAI,CAAC;cAC5B,IAAIioB,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAAC;;cAE5C;cACA,IAAI,CAACzN,IAAI,CAACyG,GAAG,CAAC,CAACK,GAAG,CAAC,GAAG2G,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;cACvC,IAAI,CAACT,aAAa,CAACxG,GAAG,CAAC,CAACK,GAAG,CAAC,GAAG,CAAC4G,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClG;UACJ;;UAEA;UACA,IAAI,CAACT,KAAK,GAAG,IAAI,CAACnN,IAAI,CAAC,IAAI,CAAC+M,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;QACxD;;QAEA;MAEJ,CAAC,EAAE;QACC3Y,GAAG,EAAE,oBAAoB;QACzBvZ,KAAK,EAAE,SAAS+yB,kBAAkBA,CAAA,EAAG;UACjC,IAAIC,mBAAmB,GAAG,EAAE;UAE5BA,mBAAmB,CAAC3qB,IAAI,CAAC;YAAE4qB,GAAG,EAAE,CAAC,IAAI,CAACrB,SAAS,CAACtuB,MAAM,EAAE,IAAI,CAACuuB,SAAS,CAACvuB,MAAM,CAAC;YAC1E4vB,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE;UACV,CAAC,CAAC;UAEF,OAAOH,mBAAmB,CAAC,CAAC,CAAC,EAAE;YAC3B,IAAI7X,OAAO,GAAG6X,mBAAmB,CAAC,CAAC,CAAC;YACpC,IAAIhgB,UAAU,GAAG,IAAI,CAACmf,aAAa,CAAChX,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,CAAC;YAEnE,IAAIjgB,UAAU,CAAC,CAAC,CAAC,EAAE;cACfggB,mBAAmB,CAAC3qB,IAAI,CAAC;gBAAE4qB,GAAG,EAAE,CAAC9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACpEC,IAAI,EAAE,IAAI,CAACtB,SAAS,CAACzW,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9X,OAAO,CAAC+X,IAAI;gBACvDC,IAAI,EAAE,IAAI,CAACtB,SAAS,CAAC1W,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9X,OAAO,CAACgY;cACvD,CAAC,CAAC;YACN;YACA,IAAIngB,UAAU,CAAC,CAAC,CAAC,EAAE;cACfggB,mBAAmB,CAAC3qB,IAAI,CAAC;gBAAE4qB,GAAG,EAAE,CAAC9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChEC,IAAI,EAAE,IAAI,CAACtB,SAAS,CAACzW,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9X,OAAO,CAAC+X,IAAI;gBACvDC,IAAI,EAAE,GAAG,GAAGhY,OAAO,CAACgY;cACxB,CAAC,CAAC;YACN;YACA,IAAIngB,UAAU,CAAC,CAAC,CAAC,EAAE;cACfggB,mBAAmB,CAAC3qB,IAAI,CAAC;gBAAE4qB,GAAG,EAAE,CAAC9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,EAAE9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAChEC,IAAI,EAAE,GAAG,GAAG/X,OAAO,CAAC+X,IAAI;gBACxBC,IAAI,EAAE,IAAI,CAACtB,SAAS,CAAC1W,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9X,OAAO,CAACgY;cACvD,CAAC,CAAC;YACN;YAEA,IAAIhY,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI9X,OAAO,CAAC8X,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,CAACb,UAAU,CAAC/pB,IAAI,CAAC;cAAEupB,SAAS,EAAEzW,OAAO,CAAC+X,IAAI;cAC5FrB,SAAS,EAAE1W,OAAO,CAACgY;YACvB,CAAC,CAAC;YAEFH,mBAAmB,CAACrjB,KAAK,CAAC,CAAC;UAC/B;UAEA,OAAO,IAAI,CAACyiB,UAAU;QAC1B;;QAEA;MAEJ,CAAC,EAAE;QACC7Y,GAAG,EAAE,eAAe;QACpBvZ,KAAK,EAAE,SAASozB,aAAaA,CAACzW,GAAG,EAAElC,GAAG,EAAE;UACpC,IAAI4Y,OAAO,GAAG,EAAE;YACZ1zB,CAAC,GAAG,CAAC,CAAC;UACV,OAAO,CAACA,CAAC,GAAGgd,GAAG,CAAC5O,OAAO,CAAC0M,GAAG,EAAE9a,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;YACzC0zB,OAAO,CAAChrB,IAAI,CAAC1I,CAAC,CAAC;UACnB;UACA,OAAO0zB,OAAO;QAClB;MACJ,CAAC,EAAE;QACC9Z,GAAG,EAAE,oBAAoB;QACzBvZ,KAAK,EAAE,SAAS6yB,kBAAkBA,CAACnI,KAAK,EAAE;UACtC,OAAO,IAAI,CAAC0I,aAAa,CAAC1I,KAAK,EAAE5lB,IAAI,CAACqO,GAAG,CAACmgB,KAAK,CAAC,IAAI,EAAE5I,KAAK,CAAC,CAAC;QACjE;MACJ,CAAC,CAAC,CAAC;MAEH,OAAOiH,eAAe;IAC1B,CAAC,CAAC,CAAC;IAEHvyB,MAAM,CAACD,OAAO,GAAGwyB,eAAe;;IAEhC;EAAM,CAAC,IACP;EACA,KAAO,UAASvyB,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,IAAI8zB,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;MACrC;IACF,CAAC;IAEDA,UAAU,CAACrP,QAAQ,GAAGzkB,mBAAmB,CAAC,EAAE,CAAC;IAC7C8zB,UAAU,CAACvoB,iBAAiB,GAAGvL,mBAAmB,CAAC,CAAC,CAAC;IACrD8zB,UAAU,CAACrK,YAAY,GAAGzpB,mBAAmB,CAAC,EAAE,CAAC;IACjD8zB,UAAU,CAACpK,YAAY,GAAG1pB,mBAAmB,CAAC,EAAE,CAAC;IACjD8zB,UAAU,CAAC5mB,UAAU,GAAGlN,mBAAmB,CAAC,EAAE,CAAC;IAC/C8zB,UAAU,CAAC7J,OAAO,GAAGjqB,mBAAmB,CAAC,EAAE,CAAC;IAC5C8zB,UAAU,CAACvJ,OAAO,GAAGvqB,mBAAmB,CAAC,EAAE,CAAC;IAC5C8zB,UAAU,CAAChxB,SAAS,GAAG9C,mBAAmB,CAAC,CAAC,CAAC;IAC7C8zB,UAAU,CAAC/wB,KAAK,GAAG/C,mBAAmB,CAAC,CAAC,CAAC;IACzC8zB,UAAU,CAACluB,OAAO,GAAG5F,mBAAmB,CAAC,EAAE,CAAC;IAC5C8zB,UAAU,CAACvmB,KAAK,GAAGvN,mBAAmB,CAAC,EAAE,CAAC;IAC1C8zB,UAAU,CAAC/tB,MAAM,GAAG/F,mBAAmB,CAAC,CAAC,CAAC;IAC1C8zB,UAAU,CAAChuB,UAAU,GAAG9F,mBAAmB,CAAC,EAAE,CAAC;IAC/C8zB,UAAU,CAACjuB,UAAU,GAAG7F,mBAAmB,CAAC,EAAE,CAAC;IAC/C8zB,UAAU,CAACxW,SAAS,GAAGtd,mBAAmB,CAAC,EAAE,CAAC;IAC9C8zB,UAAU,CAACtX,iBAAiB,GAAGxc,mBAAmB,CAAC,EAAE,CAAC;IACtD8zB,UAAU,CAACtH,SAAS,GAAGxsB,mBAAmB,CAAC,EAAE,CAAC;IAC9C8zB,UAAU,CAACtmB,UAAU,GAAGxN,mBAAmB,CAAC,EAAE,CAAC;IAC/C8zB,UAAU,CAACjxB,YAAY,GAAG7C,mBAAmB,CAAC,CAAC,CAAC;IAChD8zB,UAAU,CAACrmB,MAAM,GAAGzN,mBAAmB,CAAC,CAAC,CAAC;IAC1C8zB,UAAU,CAAC9wB,KAAK,GAAGhD,mBAAmB,CAAC,CAAC,CAAC;IACzC8zB,UAAU,CAACxmB,aAAa,GAAGtN,mBAAmB,CAAC,CAAC,CAAC;IACjD8zB,UAAU,CAAC9tB,KAAK,GAAGhG,mBAAmB,CAAC,CAAC,CAAC;IACzC8zB,UAAU,CAAC/lB,MAAM,GAAG/N,mBAAmB,CAAC,EAAE,CAAC;IAC3C8zB,UAAU,CAACnyB,eAAe,GAAG3B,mBAAmB,CAAC,CAAC,CAAC;IACnD8zB,UAAU,CAAC5B,eAAe,GAAGlyB,mBAAmB,CAAC,EAAE,CAAC;IACpD8zB,UAAU,CAAClJ,MAAM,GAAG5qB,mBAAmB,CAAC,EAAE,CAAC;IAC3C8zB,UAAU,CAAC7G,GAAG,GAAGjtB,mBAAmB,CAAC,EAAE,CAAC;IAExCL,MAAM,CAACD,OAAO,GAAGo0B,UAAU;;IAE3B;EAAM,CAAC,IACP;EACA,KAAO,UAASn0B,MAAM,EAAED,OAAO,EAAEM,mBAAmB,EAAE;IAEtD,YAAY;;IAGZ,SAASud,OAAOA,CAAA,EAAG;MACjB,IAAI,CAACwW,SAAS,GAAG,EAAE;IACrB;IAEA,IAAItyB,CAAC,GAAG8b,OAAO,CAAChc,SAAS;IAEzBE,CAAC,CAACuyB,WAAW,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;MACzC,IAAI,CAACH,SAAS,CAACnrB,IAAI,CAAC;QAClBqrB,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC;IAEDzyB,CAAC,CAAC0yB,cAAc,GAAG,UAAUF,KAAK,EAAEC,QAAQ,EAAE;MAC5C,KAAK,IAAIh0B,CAAC,GAAG,IAAI,CAAC6zB,SAAS,CAAClwB,MAAM,EAAE3D,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/C,IAAIC,CAAC,GAAG,IAAI,CAAC4zB,SAAS,CAAC7zB,CAAC,CAAC;QAEzB,IAAIC,CAAC,CAAC8zB,KAAK,KAAKA,KAAK,IAAI9zB,CAAC,CAAC+zB,QAAQ,KAAKA,QAAQ,EAAE;UAChD,IAAI,CAACH,SAAS,CAACllB,MAAM,CAAC3O,CAAC,EAAE,CAAC,CAAC;QAC7B;MACF;IACF,CAAC;IAEDuB,CAAC,CAAC2yB,IAAI,GAAG,UAAUH,KAAK,EAAEI,IAAI,EAAE;MAC9B,KAAK,IAAIn0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6zB,SAAS,CAAClwB,MAAM,EAAE3D,CAAC,EAAE,EAAE;QAC9C,IAAIC,CAAC,GAAG,IAAI,CAAC4zB,SAAS,CAAC7zB,CAAC,CAAC;QAEzB,IAAI+zB,KAAK,KAAK9zB,CAAC,CAAC8zB,KAAK,EAAE;UACrB9zB,CAAC,CAAC+zB,QAAQ,CAACG,IAAI,CAAC;QAClB;MACF;IACF,CAAC;IAED10B,MAAM,CAACD,OAAO,GAAG6d,OAAO;;IAExB;EAAM;EACN,UAAU,CAAC;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}