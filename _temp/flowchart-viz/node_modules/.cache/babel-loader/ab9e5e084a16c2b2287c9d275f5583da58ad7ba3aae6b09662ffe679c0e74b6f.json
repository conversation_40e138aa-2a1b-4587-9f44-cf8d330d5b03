{"ast": null, "code": "export default function difference(values, ...others) {\n  values = new Set(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}", "map": {"version": 3, "names": ["difference", "values", "others", "Set", "other", "value", "delete"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/d3-sankey/node_modules/d3-array/src/difference.js"], "sourcesContent": ["export default function difference(values, ...others) {\n  values = new Set(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,MAAM,EAAE,GAAGC,MAAM,EAAE;EACpDD,MAAM,GAAG,IAAIE,GAAG,CAACF,MAAM,CAAC;EACxB,KAAK,MAAMG,KAAK,IAAIF,MAAM,EAAE;IAC1B,KAAK,MAAMG,KAAK,IAAID,KAAK,EAAE;MACzBH,MAAM,CAACK,MAAM,CAACD,KAAK,CAAC;IACtB;EACF;EACA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}