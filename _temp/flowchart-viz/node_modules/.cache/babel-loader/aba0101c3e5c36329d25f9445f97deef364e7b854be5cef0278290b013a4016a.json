{"ast": null, "code": "export class BaseRegExpVisitor {\n  visitChildren(node) {\n    for (const key in node) {\n      const child = node[key];\n      /* istanbul ignore else */\n      if (node.hasOwnProperty(key)) {\n        if (child.type !== undefined) {\n          this.visit(child);\n        } else if (Array.isArray(child)) {\n          child.forEach(subChild => {\n            this.visit(subChild);\n          }, this);\n        }\n      }\n    }\n  }\n  visit(node) {\n    switch (node.type) {\n      case \"Pattern\":\n        this.visitPattern(node);\n        break;\n      case \"Flags\":\n        this.visitFlags(node);\n        break;\n      case \"Disjunction\":\n        this.visitDisjunction(node);\n        break;\n      case \"Alternative\":\n        this.visitAlternative(node);\n        break;\n      case \"StartAnchor\":\n        this.visitStartAnchor(node);\n        break;\n      case \"EndAnchor\":\n        this.visitEndAnchor(node);\n        break;\n      case \"WordBoundary\":\n        this.visitWordBoundary(node);\n        break;\n      case \"NonWordBoundary\":\n        this.visitNonWordBoundary(node);\n        break;\n      case \"Lookahead\":\n        this.visitLookahead(node);\n        break;\n      case \"NegativeLookahead\":\n        this.visitNegativeLookahead(node);\n        break;\n      case \"Character\":\n        this.visitCharacter(node);\n        break;\n      case \"Set\":\n        this.visitSet(node);\n        break;\n      case \"Group\":\n        this.visitGroup(node);\n        break;\n      case \"GroupBackReference\":\n        this.visitGroupBackReference(node);\n        break;\n      case \"Quantifier\":\n        this.visitQuantifier(node);\n        break;\n    }\n    this.visitChildren(node);\n  }\n  visitPattern(node) {}\n  visitFlags(node) {}\n  visitDisjunction(node) {}\n  visitAlternative(node) {}\n  // Assertion\n  visitStartAnchor(node) {}\n  visitEndAnchor(node) {}\n  visitWordBoundary(node) {}\n  visitNonWordBoundary(node) {}\n  visitLookahead(node) {}\n  visitNegativeLookahead(node) {}\n  // atoms\n  visitCharacter(node) {}\n  visitSet(node) {}\n  visitGroup(node) {}\n  visitGroupBackReference(node) {}\n  visitQuantifier(node) {}\n}", "map": {"version": 3, "names": ["BaseRegExpVisitor", "visit<PERSON><PERSON><PERSON><PERSON>", "node", "key", "child", "hasOwnProperty", "type", "undefined", "visit", "Array", "isArray", "for<PERSON>ach", "subChild", "visitPattern", "visitFlags", "visitDisjunction", "visitAlternative", "visitStartAnchor", "visitEndAnchor", "visitWordBoundary", "visitNonWordBoundary", "visitLookahead", "visitNegativeLookahead", "visitCharacter", "visitSet", "visitGroup", "visitGroupBackReference", "visitQuantifier"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/@chevrotain/regexp-to-ast/src/base-regexp-visitor.ts"], "sourcesContent": ["import type {\n  Alternative,\n  Assertion,\n  Character,\n  Disjunction,\n  Group,\n  GroupBackReference,\n  IRegExpAST,\n  Quantifier,\n  RegExpAstPart,\n  RegExpFlags,\n  RegExpPattern,\n  Set,\n} from \"../types\";\n\nexport class BaseRegExpVisitor {\n  public visitChildren(node: IRegExpAST) {\n    for (const key in node) {\n      const child = (node as any)[key];\n      /* istanbul ignore else */\n      if (node.hasOwnProperty(key)) {\n        if (child.type !== undefined) {\n          this.visit(child);\n        } else if (Array.isArray(child)) {\n          child.forEach((subChild) => {\n            this.visit(subChild);\n          }, this);\n        }\n      }\n    }\n  }\n\n  public visit(node: RegExpAstPart): void {\n    switch (node.type) {\n      case \"Pattern\":\n        this.visitPattern(node);\n        break;\n      case \"Flags\":\n        this.visitFlags(node);\n        break;\n      case \"Disjunction\":\n        this.visitDisjunction(node);\n        break;\n      case \"Alternative\":\n        this.visitAlternative(node);\n        break;\n      case \"StartAnchor\":\n        this.visitStartAnchor(node);\n        break;\n      case \"EndAnchor\":\n        this.visitEndAnchor(node);\n        break;\n      case \"WordBoundary\":\n        this.visitWordBoundary(node);\n        break;\n      case \"NonWordBoundary\":\n        this.visitNonWordBoundary(node);\n        break;\n      case \"Lookahead\":\n        this.visitLookahead(node);\n        break;\n      case \"NegativeLookahead\":\n        this.visitNegativeLookahead(node);\n        break;\n      case \"Character\":\n        this.visitCharacter(node);\n        break;\n      case \"Set\":\n        this.visitSet(node);\n        break;\n      case \"Group\":\n        this.visitGroup(node);\n        break;\n      case \"GroupBackReference\":\n        this.visitGroupBackReference(node);\n        break;\n      case \"Quantifier\":\n        this.visitQuantifier(node);\n        break;\n    }\n\n    this.visitChildren(node);\n  }\n\n  public visitPattern(node: RegExpPattern): void {}\n\n  public visitFlags(node: RegExpFlags): void {}\n\n  public visitDisjunction(node: Disjunction): void {}\n\n  public visitAlternative(node: Alternative): void {}\n\n  // Assertion\n  public visitStartAnchor(node: Assertion): void {}\n\n  public visitEndAnchor(node: Assertion): void {}\n\n  public visitWordBoundary(node: Assertion): void {}\n\n  public visitNonWordBoundary(node: Assertion): void {}\n\n  public visitLookahead(node: Assertion): void {}\n\n  public visitNegativeLookahead(node: Assertion): void {}\n\n  // atoms\n  public visitCharacter(node: Character): void {}\n\n  public visitSet(node: Set): void {}\n\n  public visitGroup(node: Group): void {}\n\n  public visitGroupBackReference(node: GroupBackReference): void {}\n\n  public visitQuantifier(node: Quantifier): void {}\n}\n"], "mappings": "AAeA,OAAM,MAAOA,iBAAiB;EACrBC,aAAaA,CAACC,IAAgB;IACnC,KAAK,MAAMC,GAAG,IAAID,IAAI,EAAE;MACtB,MAAME,KAAK,GAAIF,IAAY,CAACC,GAAG,CAAC;MAChC;MACA,IAAID,IAAI,CAACG,cAAc,CAACF,GAAG,CAAC,EAAE;QAC5B,IAAIC,KAAK,CAACE,IAAI,KAAKC,SAAS,EAAE;UAC5B,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;SAClB,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;UAC/BA,KAAK,CAACO,OAAO,CAAEC,QAAQ,IAAI;YACzB,IAAI,CAACJ,KAAK,CAACI,QAAQ,CAAC;UACtB,CAAC,EAAE,IAAI,CAAC;;;;EAIhB;EAEOJ,KAAKA,CAACN,IAAmB;IAC9B,QAAQA,IAAI,CAACI,IAAI;MACf,KAAK,SAAS;QACZ,IAAI,CAACO,YAAY,CAACX,IAAI,CAAC;QACvB;MACF,KAAK,OAAO;QACV,IAAI,CAACY,UAAU,CAACZ,IAAI,CAAC;QACrB;MACF,KAAK,aAAa;QAChB,IAAI,CAACa,gBAAgB,CAACb,IAAI,CAAC;QAC3B;MACF,KAAK,aAAa;QAChB,IAAI,CAACc,gBAAgB,CAACd,IAAI,CAAC;QAC3B;MACF,KAAK,aAAa;QAChB,IAAI,CAACe,gBAAgB,CAACf,IAAI,CAAC;QAC3B;MACF,KAAK,WAAW;QACd,IAAI,CAACgB,cAAc,CAAChB,IAAI,CAAC;QACzB;MACF,KAAK,cAAc;QACjB,IAAI,CAACiB,iBAAiB,CAACjB,IAAI,CAAC;QAC5B;MACF,KAAK,iBAAiB;QACpB,IAAI,CAACkB,oBAAoB,CAAClB,IAAI,CAAC;QAC/B;MACF,KAAK,WAAW;QACd,IAAI,CAACmB,cAAc,CAACnB,IAAI,CAAC;QACzB;MACF,KAAK,mBAAmB;QACtB,IAAI,CAACoB,sBAAsB,CAACpB,IAAI,CAAC;QACjC;MACF,KAAK,WAAW;QACd,IAAI,CAACqB,cAAc,CAACrB,IAAI,CAAC;QACzB;MACF,KAAK,KAAK;QACR,IAAI,CAACsB,QAAQ,CAACtB,IAAI,CAAC;QACnB;MACF,KAAK,OAAO;QACV,IAAI,CAACuB,UAAU,CAACvB,IAAI,CAAC;QACrB;MACF,KAAK,oBAAoB;QACvB,IAAI,CAACwB,uBAAuB,CAACxB,IAAI,CAAC;QAClC;MACF,KAAK,YAAY;QACf,IAAI,CAACyB,eAAe,CAACzB,IAAI,CAAC;QAC1B;;IAGJ,IAAI,CAACD,aAAa,CAACC,IAAI,CAAC;EAC1B;EAEOW,YAAYA,CAACX,IAAmB,GAAS;EAEzCY,UAAUA,CAACZ,IAAiB,GAAS;EAErCa,gBAAgBA,CAACb,IAAiB,GAAS;EAE3Cc,gBAAgBA,CAACd,IAAiB,GAAS;EAElD;EACOe,gBAAgBA,CAACf,IAAe,GAAS;EAEzCgB,cAAcA,CAAChB,IAAe,GAAS;EAEvCiB,iBAAiBA,CAACjB,IAAe,GAAS;EAE1CkB,oBAAoBA,CAAClB,IAAe,GAAS;EAE7CmB,cAAcA,CAACnB,IAAe,GAAS;EAEvCoB,sBAAsBA,CAACpB,IAAe,GAAS;EAEtD;EACOqB,cAAcA,CAACrB,IAAe,GAAS;EAEvCsB,QAAQA,CAACtB,IAAS,GAAS;EAE3BuB,UAAUA,CAACvB,IAAW,GAAS;EAE/BwB,uBAAuBA,CAACxB,IAAwB,GAAS;EAEzDyB,eAAeA,CAACzB,IAAgB,GAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}