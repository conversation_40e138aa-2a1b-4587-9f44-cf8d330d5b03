{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/languages/language-meta-data.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/**\r\n * Metadata of a language.\r\n */\r\nexport interface LanguageMetaData {\r\n    languageId: string;\r\n    fileExtensions: readonly string[];\r\n    caseInsensitive: boolean;\r\n    /**\r\n     * Mode used to optimize code for development or production environments.\r\n     *\r\n     * In production mode, all Chevrotain lexer/parser validations are disabled.\r\n     */\r\n    mode: 'development' | 'production';\r\n}\r\n"], "mappings": "AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}