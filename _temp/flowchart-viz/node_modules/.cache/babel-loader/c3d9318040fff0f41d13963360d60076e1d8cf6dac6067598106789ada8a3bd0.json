{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport class ErrorWithLocation extends Error {\n  constructor(node, message) {\n    super(node ? `${message} at ${node.range.start.line}:${node.range.start.character}` : message);\n  }\n}\nexport function assertUnreachable(_) {\n  throw new Error('Error! The input value was not handled.');\n}", "map": {"version": 3, "names": ["ErrorWithLocation", "Error", "constructor", "node", "message", "range", "start", "line", "character", "assertUnreachable", "_"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/utils/errors.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { CstNode } from '../syntax-tree.js';\r\n\r\nexport class ErrorWithLocation extends Error {\r\n    constructor(node: CstNode | undefined, message: string) {\r\n        super(node ? `${message} at ${node.range.start.line}:${node.range.start.character}` : message);\r\n    }\r\n}\r\n\r\nexport function assertUnreachable(_: never): never {\r\n    throw new Error('Error! The input value was not handled.');\r\n}\r\n"], "mappings": "AAAA;;;;;AAQA,OAAM,MAAOA,iBAAkB,SAAQC,KAAK;EACxCC,YAAYC,IAAyB,EAAEC,OAAe;IAClD,KAAK,CAACD,IAAI,GAAG,GAAGC,OAAO,OAAOD,IAAI,CAACE,KAAK,CAACC,KAAK,CAACC,IAAI,IAAIJ,IAAI,CAACE,KAAK,CAACC,KAAK,CAACE,SAAS,EAAE,GAAGJ,OAAO,CAAC;EAClG;;AAGJ,OAAM,SAAUK,iBAAiBA,CAACC,CAAQ;EACtC,MAAM,IAAIT,KAAK,CAAC,yCAAyC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}