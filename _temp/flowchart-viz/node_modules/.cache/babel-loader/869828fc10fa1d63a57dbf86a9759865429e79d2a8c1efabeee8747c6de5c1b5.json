{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { getDocument } from '../utils/ast-utils.js';\nimport { isJSDoc, parseJSDoc } from './jsdoc.js';\nexport class JSDocDocumentationProvider {\n  constructor(services) {\n    this.indexManager = services.shared.workspace.IndexManager;\n    this.commentProvider = services.documentation.CommentProvider;\n  }\n  getDocumentation(node) {\n    const comment = this.commentProvider.getComment(node);\n    if (comment && isJSDoc(comment)) {\n      const parsedJSDoc = parseJSDoc(comment);\n      return parsedJSDoc.toMarkdown({\n        renderLink: (link, display) => {\n          return this.documentationLinkRenderer(node, link, display);\n        },\n        renderTag: tag => {\n          return this.documentationTagRenderer(node, tag);\n        }\n      });\n    }\n    return undefined;\n  }\n  documentationLinkRenderer(node, name, display) {\n    var _a;\n    const description = (_a = this.findNameInPrecomputedScopes(node, name)) !== null && _a !== void 0 ? _a : this.findNameInGlobalScope(node, name);\n    if (description && description.nameSegment) {\n      const line = description.nameSegment.range.start.line + 1;\n      const character = description.nameSegment.range.start.character + 1;\n      const uri = description.documentUri.with({\n        fragment: `L${line},${character}`\n      });\n      return `[${display}](${uri.toString()})`;\n    } else {\n      return undefined;\n    }\n  }\n  documentationTagRenderer(_node, _tag) {\n    // Fall back to the default tag rendering\n    return undefined;\n  }\n  findNameInPrecomputedScopes(node, name) {\n    const document = getDocument(node);\n    const precomputed = document.precomputedScopes;\n    if (!precomputed) {\n      return undefined;\n    }\n    let currentNode = node;\n    do {\n      const allDescriptions = precomputed.get(currentNode);\n      const description = allDescriptions.find(e => e.name === name);\n      if (description) {\n        return description;\n      }\n      currentNode = currentNode.$container;\n    } while (currentNode);\n    return undefined;\n  }\n  findNameInGlobalScope(node, name) {\n    const description = this.indexManager.allElements().find(e => e.name === name);\n    return description;\n  }\n}", "map": {"version": 3, "names": ["getDocument", "isJSDoc", "parseJSDoc", "JSDocDocumentationProvider", "constructor", "services", "indexManager", "shared", "workspace", "IndexManager", "comment<PERSON><PERSON><PERSON>", "documentation", "CommentProvider", "getDocumentation", "node", "comment", "getComment", "parsedJSDoc", "toMarkdown", "renderLink", "link", "display", "documentationLinkRenderer", "renderTag", "tag", "documentationTag<PERSON><PERSON><PERSON>", "undefined", "name", "description", "_a", "findNameInPrecomputedScopes", "findNameInGlobalScope", "nameSegment", "line", "range", "start", "character", "uri", "documentUri", "with", "fragment", "toString", "_node", "_tag", "document", "precomputed", "precomputedScopes", "currentNode", "allDescriptions", "get", "find", "e", "$container", "allElements"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/documentation/documentation-provider.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, AstNodeDescription } from '../syntax-tree.js';\r\nimport type { IndexManager } from '../workspace/index-manager.js';\r\nimport type { CommentProvider } from './comment-provider.js';\r\nimport type { JSDocTag } from './jsdoc.js';\r\nimport { getDocument } from '../utils/ast-utils.js';\r\nimport { isJSDoc, parseJSDoc } from './jsdoc.js';\r\n\r\n/**\r\n * Provides documentation for AST nodes.\r\n */\r\nexport interface DocumentationProvider {\r\n    /**\r\n     * Returns a markdown documentation string for the specified AST node.\r\n     *\r\n     * The default implementation `JSDocDocumentationProvider` will inspect the comment associated with the specified node.\r\n     */\r\n    getDocumentation(node: AstNode): string | undefined;\r\n}\r\n\r\nexport class JSDocDocumentationProvider implements DocumentationProvider {\r\n\r\n    protected readonly indexManager: IndexManager;\r\n    protected readonly commentProvider: CommentProvider;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.indexManager = services.shared.workspace.IndexManager;\r\n        this.commentProvider = services.documentation.CommentProvider;\r\n    }\r\n\r\n    getDocumentation(node: AstNode): string | undefined {\r\n        const comment = this.commentProvider.getComment(node);\r\n        if (comment && isJSDoc(comment)) {\r\n            const parsedJSDoc = parseJSDoc(comment);\r\n            return parsedJSDoc.toMarkdown({\r\n                renderLink: (link, display) => {\r\n                    return this.documentationLinkRenderer(node, link, display);\r\n                },\r\n                renderTag: (tag) => {\r\n                    return this.documentationTagRenderer(node, tag);\r\n                }\r\n            });\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    protected documentationLinkRenderer(node: AstNode, name: string, display: string): string | undefined {\r\n        const description = this.findNameInPrecomputedScopes(node, name) ?? this.findNameInGlobalScope(node, name);\r\n        if (description && description.nameSegment) {\r\n            const line = description.nameSegment.range.start.line + 1;\r\n            const character = description.nameSegment.range.start.character + 1;\r\n            const uri = description.documentUri.with({ fragment: `L${line},${character}` });\r\n            return `[${display}](${uri.toString()})`;\r\n        } else {\r\n            return undefined;\r\n        }\r\n    }\r\n\r\n    protected documentationTagRenderer(_node: AstNode, _tag: JSDocTag): string | undefined {\r\n        // Fall back to the default tag rendering\r\n        return undefined;\r\n    }\r\n\r\n    protected findNameInPrecomputedScopes(node: AstNode, name: string): AstNodeDescription | undefined {\r\n        const document = getDocument(node);\r\n        const precomputed = document.precomputedScopes;\r\n        if (!precomputed) {\r\n            return undefined;\r\n        }\r\n        let currentNode: AstNode | undefined = node;\r\n        do {\r\n            const allDescriptions = precomputed.get(currentNode);\r\n            const description = allDescriptions.find(e => e.name === name);\r\n            if (description) {\r\n                return description;\r\n            }\r\n            currentNode = currentNode.$container;\r\n        } while (currentNode);\r\n\r\n        return undefined;\r\n    }\r\n\r\n    protected findNameInGlobalScope(node: AstNode, name: string): AstNodeDescription | undefined {\r\n        const description = this.indexManager.allElements().find(e => e.name === name);\r\n        return description;\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAWA,SAASA,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,EAAEC,UAAU,QAAQ,YAAY;AAchD,OAAM,MAAOC,0BAA0B;EAKnCC,YAAYC,QAA6B;IACrC,IAAI,CAACC,YAAY,GAAGD,QAAQ,CAACE,MAAM,CAACC,SAAS,CAACC,YAAY;IAC1D,IAAI,CAACC,eAAe,GAAGL,QAAQ,CAACM,aAAa,CAACC,eAAe;EACjE;EAEAC,gBAAgBA,CAACC,IAAa;IAC1B,MAAMC,OAAO,GAAG,IAAI,CAACL,eAAe,CAACM,UAAU,CAACF,IAAI,CAAC;IACrD,IAAIC,OAAO,IAAId,OAAO,CAACc,OAAO,CAAC,EAAE;MAC7B,MAAME,WAAW,GAAGf,UAAU,CAACa,OAAO,CAAC;MACvC,OAAOE,WAAW,CAACC,UAAU,CAAC;QAC1BC,UAAU,EAAEA,CAACC,IAAI,EAAEC,OAAO,KAAI;UAC1B,OAAO,IAAI,CAACC,yBAAyB,CAACR,IAAI,EAAEM,IAAI,EAAEC,OAAO,CAAC;QAC9D,CAAC;QACDE,SAAS,EAAGC,GAAG,IAAI;UACf,OAAO,IAAI,CAACC,wBAAwB,CAACX,IAAI,EAAEU,GAAG,CAAC;QACnD;OACH,CAAC;IACN;IACA,OAAOE,SAAS;EACpB;EAEUJ,yBAAyBA,CAACR,IAAa,EAAEa,IAAY,EAAEN,OAAe;;IAC5E,MAAMO,WAAW,GAAG,CAAAC,EAAA,OAAI,CAACC,2BAA2B,CAAChB,IAAI,EAAEa,IAAI,CAAC,cAAAE,EAAA,cAAAA,EAAA,GAAI,IAAI,CAACE,qBAAqB,CAACjB,IAAI,EAAEa,IAAI,CAAC;IAC1G,IAAIC,WAAW,IAAIA,WAAW,CAACI,WAAW,EAAE;MACxC,MAAMC,IAAI,GAAGL,WAAW,CAACI,WAAW,CAACE,KAAK,CAACC,KAAK,CAACF,IAAI,GAAG,CAAC;MACzD,MAAMG,SAAS,GAAGR,WAAW,CAACI,WAAW,CAACE,KAAK,CAACC,KAAK,CAACC,SAAS,GAAG,CAAC;MACnE,MAAMC,GAAG,GAAGT,WAAW,CAACU,WAAW,CAACC,IAAI,CAAC;QAAEC,QAAQ,EAAE,IAAIP,IAAI,IAAIG,SAAS;MAAE,CAAE,CAAC;MAC/E,OAAO,IAAIf,OAAO,KAAKgB,GAAG,CAACI,QAAQ,EAAE,GAAG;IAC5C,CAAC,MAAM;MACH,OAAOf,SAAS;IACpB;EACJ;EAEUD,wBAAwBA,CAACiB,KAAc,EAAEC,IAAc;IAC7D;IACA,OAAOjB,SAAS;EACpB;EAEUI,2BAA2BA,CAAChB,IAAa,EAAEa,IAAY;IAC7D,MAAMiB,QAAQ,GAAG5C,WAAW,CAACc,IAAI,CAAC;IAClC,MAAM+B,WAAW,GAAGD,QAAQ,CAACE,iBAAiB;IAC9C,IAAI,CAACD,WAAW,EAAE;MACd,OAAOnB,SAAS;IACpB;IACA,IAAIqB,WAAW,GAAwBjC,IAAI;IAC3C,GAAG;MACC,MAAMkC,eAAe,GAAGH,WAAW,CAACI,GAAG,CAACF,WAAW,CAAC;MACpD,MAAMnB,WAAW,GAAGoB,eAAe,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,IAAI,KAAKA,IAAI,CAAC;MAC9D,IAAIC,WAAW,EAAE;QACb,OAAOA,WAAW;MACtB;MACAmB,WAAW,GAAGA,WAAW,CAACK,UAAU;IACxC,CAAC,QAAQL,WAAW;IAEpB,OAAOrB,SAAS;EACpB;EAEUK,qBAAqBA,CAACjB,IAAa,EAAEa,IAAY;IACvD,MAAMC,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC+C,WAAW,EAAE,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,IAAI,KAAKA,IAAI,CAAC;IAC9E,OAAOC,WAAW;EACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}