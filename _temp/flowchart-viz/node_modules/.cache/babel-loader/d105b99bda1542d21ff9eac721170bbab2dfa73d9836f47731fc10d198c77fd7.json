{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { defaultParserErrorProvider, EmbeddedActionsParser, LLkLookaheadStrategy } from 'chevrotain';\nimport { LLStarLookaheadStrategy } from 'chevrotain-allstar';\nimport { isAssignment, isCrossReference, isKeyword } from '../languages/generated/ast.js';\nimport { getExplicitRuleType, isDataTypeRule } from '../utils/grammar-utils.js';\nimport { assignMandatoryProperties, getContainerOfType, linkContentToContainer } from '../utils/ast-utils.js';\nimport { CstNodeBuilder } from './cst-node-builder.js';\nexport const DatatypeSymbol = Symbol('Datatype');\nfunction isDataTypeNode(node) {\n  return node.$type === DatatypeSymbol;\n}\nconst ruleSuffix = '\\u200B';\nconst withRuleSuffix = name => name.endsWith(ruleSuffix) ? name : name + ruleSuffix;\nexport class AbstractLangiumParser {\n  constructor(services) {\n    this._unorderedGroups = new Map();\n    this.allRules = new Map();\n    this.lexer = services.parser.Lexer;\n    const tokens = this.lexer.definition;\n    const production = services.LanguageMetaData.mode === 'production';\n    this.wrapper = new ChevrotainWrapper(tokens, Object.assign(Object.assign({}, services.parser.ParserConfig), {\n      skipValidations: production,\n      errorMessageProvider: services.parser.ParserErrorMessageProvider\n    }));\n  }\n  alternatives(idx, choices) {\n    this.wrapper.wrapOr(idx, choices);\n  }\n  optional(idx, callback) {\n    this.wrapper.wrapOption(idx, callback);\n  }\n  many(idx, callback) {\n    this.wrapper.wrapMany(idx, callback);\n  }\n  atLeastOne(idx, callback) {\n    this.wrapper.wrapAtLeastOne(idx, callback);\n  }\n  getRule(name) {\n    return this.allRules.get(name);\n  }\n  isRecording() {\n    return this.wrapper.IS_RECORDING;\n  }\n  get unorderedGroups() {\n    return this._unorderedGroups;\n  }\n  getRuleStack() {\n    return this.wrapper.RULE_STACK;\n  }\n  finalize() {\n    this.wrapper.wrapSelfAnalysis();\n  }\n}\nexport class LangiumParser extends AbstractLangiumParser {\n  get current() {\n    return this.stack[this.stack.length - 1];\n  }\n  constructor(services) {\n    super(services);\n    this.nodeBuilder = new CstNodeBuilder();\n    this.stack = [];\n    this.assignmentMap = new Map();\n    this.linker = services.references.Linker;\n    this.converter = services.parser.ValueConverter;\n    this.astReflection = services.shared.AstReflection;\n  }\n  rule(rule, impl) {\n    const type = this.computeRuleType(rule);\n    const ruleMethod = this.wrapper.DEFINE_RULE(withRuleSuffix(rule.name), this.startImplementation(type, impl).bind(this));\n    this.allRules.set(rule.name, ruleMethod);\n    if (rule.entry) {\n      this.mainRule = ruleMethod;\n    }\n    return ruleMethod;\n  }\n  computeRuleType(rule) {\n    if (rule.fragment) {\n      return undefined;\n    } else if (isDataTypeRule(rule)) {\n      return DatatypeSymbol;\n    } else {\n      const explicit = getExplicitRuleType(rule);\n      return explicit !== null && explicit !== void 0 ? explicit : rule.name;\n    }\n  }\n  parse(input, options = {}) {\n    this.nodeBuilder.buildRootNode(input);\n    const lexerResult = this.lexerResult = this.lexer.tokenize(input);\n    this.wrapper.input = lexerResult.tokens;\n    const ruleMethod = options.rule ? this.allRules.get(options.rule) : this.mainRule;\n    if (!ruleMethod) {\n      throw new Error(options.rule ? `No rule found with name '${options.rule}'` : 'No main rule available.');\n    }\n    const result = ruleMethod.call(this.wrapper, {});\n    this.nodeBuilder.addHiddenNodes(lexerResult.hidden);\n    this.unorderedGroups.clear();\n    this.lexerResult = undefined;\n    return {\n      value: result,\n      lexerErrors: lexerResult.errors,\n      lexerReport: lexerResult.report,\n      parserErrors: this.wrapper.errors\n    };\n  }\n  startImplementation($type, implementation) {\n    return args => {\n      // Only create a new AST node in case the calling rule is not a fragment rule\n      const createNode = !this.isRecording() && $type !== undefined;\n      if (createNode) {\n        const node = {\n          $type\n        };\n        this.stack.push(node);\n        if ($type === DatatypeSymbol) {\n          node.value = '';\n        }\n      }\n      let result;\n      try {\n        result = implementation(args);\n      } catch (err) {\n        result = undefined;\n      }\n      if (result === undefined && createNode) {\n        result = this.construct();\n      }\n      return result;\n    };\n  }\n  extractHiddenTokens(token) {\n    const hiddenTokens = this.lexerResult.hidden;\n    if (!hiddenTokens.length) {\n      return [];\n    }\n    const offset = token.startOffset;\n    for (let i = 0; i < hiddenTokens.length; i++) {\n      const token = hiddenTokens[i];\n      if (token.startOffset > offset) {\n        return hiddenTokens.splice(0, i);\n      }\n    }\n    return hiddenTokens.splice(0, hiddenTokens.length);\n  }\n  consume(idx, tokenType, feature) {\n    const token = this.wrapper.wrapConsume(idx, tokenType);\n    if (!this.isRecording() && this.isValidToken(token)) {\n      const hiddenTokens = this.extractHiddenTokens(token);\n      this.nodeBuilder.addHiddenNodes(hiddenTokens);\n      const leafNode = this.nodeBuilder.buildLeafNode(token, feature);\n      const {\n        assignment,\n        isCrossRef\n      } = this.getAssignment(feature);\n      const current = this.current;\n      if (assignment) {\n        const convertedValue = isKeyword(feature) ? token.image : this.converter.convert(token.image, leafNode);\n        this.assign(assignment.operator, assignment.feature, convertedValue, leafNode, isCrossRef);\n      } else if (isDataTypeNode(current)) {\n        let text = token.image;\n        if (!isKeyword(feature)) {\n          text = this.converter.convert(text, leafNode).toString();\n        }\n        current.value += text;\n      }\n    }\n  }\n  /**\n   * Most consumed parser tokens are valid. However there are two cases in which they are not valid:\n   *\n   * 1. They were inserted during error recovery by the parser. These tokens don't really exist and should not be further processed\n   * 2. They contain invalid token ranges. This might include the special EOF token, or other tokens produced by invalid token builders.\n   */\n  isValidToken(token) {\n    return !token.isInsertedInRecovery && !isNaN(token.startOffset) && typeof token.endOffset === 'number' && !isNaN(token.endOffset);\n  }\n  subrule(idx, rule, fragment, feature, args) {\n    let cstNode;\n    if (!this.isRecording() && !fragment) {\n      // We only want to create a new CST node if the subrule actually creates a new AST node.\n      // In other cases like calls of fragment rules the current CST/AST is populated further.\n      // Note that skipping this initialization and leaving cstNode unassigned also skips the subrule assignment later on.\n      // This is intended, as fragment rules only enrich the current AST node\n      cstNode = this.nodeBuilder.buildCompositeNode(feature);\n    }\n    const subruleResult = this.wrapper.wrapSubrule(idx, rule, args);\n    if (!this.isRecording() && cstNode && cstNode.length > 0) {\n      this.performSubruleAssignment(subruleResult, feature, cstNode);\n    }\n  }\n  performSubruleAssignment(result, feature, cstNode) {\n    const {\n      assignment,\n      isCrossRef\n    } = this.getAssignment(feature);\n    if (assignment) {\n      this.assign(assignment.operator, assignment.feature, result, cstNode, isCrossRef);\n    } else if (!assignment) {\n      // If we call a subrule without an assignment we either:\n      // 1. append the result of the subrule (data type rule)\n      // 2. override the current object with the newly parsed object\n      // If the current element is an AST node and the result of the subrule\n      // is a data type rule, we can safely discard the results.\n      const current = this.current;\n      if (isDataTypeNode(current)) {\n        current.value += result.toString();\n      } else if (typeof result === 'object' && result) {\n        const object = this.assignWithoutOverride(result, current);\n        const newItem = object;\n        this.stack.pop();\n        this.stack.push(newItem);\n      }\n    }\n  }\n  action($type, action) {\n    if (!this.isRecording()) {\n      let last = this.current;\n      if (action.feature && action.operator) {\n        last = this.construct();\n        this.nodeBuilder.removeNode(last.$cstNode);\n        const node = this.nodeBuilder.buildCompositeNode(action);\n        node.content.push(last.$cstNode);\n        const newItem = {\n          $type\n        };\n        this.stack.push(newItem);\n        this.assign(action.operator, action.feature, last, last.$cstNode, false);\n      } else {\n        last.$type = $type;\n      }\n    }\n  }\n  construct() {\n    if (this.isRecording()) {\n      return undefined;\n    }\n    const obj = this.current;\n    linkContentToContainer(obj);\n    this.nodeBuilder.construct(obj);\n    this.stack.pop();\n    if (isDataTypeNode(obj)) {\n      return this.converter.convert(obj.value, obj.$cstNode);\n    } else {\n      assignMandatoryProperties(this.astReflection, obj);\n    }\n    return obj;\n  }\n  getAssignment(feature) {\n    if (!this.assignmentMap.has(feature)) {\n      const assignment = getContainerOfType(feature, isAssignment);\n      this.assignmentMap.set(feature, {\n        assignment: assignment,\n        isCrossRef: assignment ? isCrossReference(assignment.terminal) : false\n      });\n    }\n    return this.assignmentMap.get(feature);\n  }\n  assign(operator, feature, value, cstNode, isCrossRef) {\n    const obj = this.current;\n    let item;\n    if (isCrossRef && typeof value === 'string') {\n      item = this.linker.buildReference(obj, feature, cstNode, value);\n    } else {\n      item = value;\n    }\n    switch (operator) {\n      case '=':\n        {\n          obj[feature] = item;\n          break;\n        }\n      case '?=':\n        {\n          obj[feature] = true;\n          break;\n        }\n      case '+=':\n        {\n          if (!Array.isArray(obj[feature])) {\n            obj[feature] = [];\n          }\n          obj[feature].push(item);\n        }\n    }\n  }\n  assignWithoutOverride(target, source) {\n    for (const [name, existingValue] of Object.entries(source)) {\n      const newValue = target[name];\n      if (newValue === undefined) {\n        target[name] = existingValue;\n      } else if (Array.isArray(newValue) && Array.isArray(existingValue)) {\n        existingValue.push(...newValue);\n        target[name] = existingValue;\n      }\n    }\n    // The target was parsed from a unassigned subrule\n    // After the subrule construction, it received a cst node\n    // This CST node will later be overriden by the cst node builder\n    // To prevent references to stale AST nodes in the CST,\n    // we need to remove the reference here\n    const targetCstNode = target.$cstNode;\n    if (targetCstNode) {\n      targetCstNode.astNode = undefined;\n      target.$cstNode = undefined;\n    }\n    return target;\n  }\n  get definitionErrors() {\n    return this.wrapper.definitionErrors;\n  }\n}\nexport class AbstractParserErrorMessageProvider {\n  buildMismatchTokenMessage(options) {\n    return defaultParserErrorProvider.buildMismatchTokenMessage(options);\n  }\n  buildNotAllInputParsedMessage(options) {\n    return defaultParserErrorProvider.buildNotAllInputParsedMessage(options);\n  }\n  buildNoViableAltMessage(options) {\n    return defaultParserErrorProvider.buildNoViableAltMessage(options);\n  }\n  buildEarlyExitMessage(options) {\n    return defaultParserErrorProvider.buildEarlyExitMessage(options);\n  }\n}\nexport class LangiumParserErrorMessageProvider extends AbstractParserErrorMessageProvider {\n  buildMismatchTokenMessage({\n    expected,\n    actual\n  }) {\n    const expectedMsg = expected.LABEL ? '`' + expected.LABEL + '`' : expected.name.endsWith(':KW') ? `keyword '${expected.name.substring(0, expected.name.length - 3)}'` : `token of type '${expected.name}'`;\n    return `Expecting ${expectedMsg} but found \\`${actual.image}\\`.`;\n  }\n  buildNotAllInputParsedMessage({\n    firstRedundant\n  }) {\n    return `Expecting end of file but found \\`${firstRedundant.image}\\`.`;\n  }\n}\nexport class LangiumCompletionParser extends AbstractLangiumParser {\n  constructor() {\n    super(...arguments);\n    this.tokens = [];\n    this.elementStack = [];\n    this.lastElementStack = [];\n    this.nextTokenIndex = 0;\n    this.stackSize = 0;\n  }\n  action() {\n    // NOOP\n  }\n  construct() {\n    // NOOP\n    return undefined;\n  }\n  parse(input) {\n    this.resetState();\n    const tokens = this.lexer.tokenize(input, {\n      mode: 'partial'\n    });\n    this.tokens = tokens.tokens;\n    this.wrapper.input = [...this.tokens];\n    this.mainRule.call(this.wrapper, {});\n    this.unorderedGroups.clear();\n    return {\n      tokens: this.tokens,\n      elementStack: [...this.lastElementStack],\n      tokenIndex: this.nextTokenIndex\n    };\n  }\n  rule(rule, impl) {\n    const ruleMethod = this.wrapper.DEFINE_RULE(withRuleSuffix(rule.name), this.startImplementation(impl).bind(this));\n    this.allRules.set(rule.name, ruleMethod);\n    if (rule.entry) {\n      this.mainRule = ruleMethod;\n    }\n    return ruleMethod;\n  }\n  resetState() {\n    this.elementStack = [];\n    this.lastElementStack = [];\n    this.nextTokenIndex = 0;\n    this.stackSize = 0;\n  }\n  startImplementation(implementation) {\n    return args => {\n      const size = this.keepStackSize();\n      try {\n        implementation(args);\n      } finally {\n        this.resetStackSize(size);\n      }\n    };\n  }\n  removeUnexpectedElements() {\n    this.elementStack.splice(this.stackSize);\n  }\n  keepStackSize() {\n    const size = this.elementStack.length;\n    this.stackSize = size;\n    return size;\n  }\n  resetStackSize(size) {\n    this.removeUnexpectedElements();\n    this.stackSize = size;\n  }\n  consume(idx, tokenType, feature) {\n    this.wrapper.wrapConsume(idx, tokenType);\n    if (!this.isRecording()) {\n      this.lastElementStack = [...this.elementStack, feature];\n      this.nextTokenIndex = this.currIdx + 1;\n    }\n  }\n  subrule(idx, rule, fragment, feature, args) {\n    this.before(feature);\n    this.wrapper.wrapSubrule(idx, rule, args);\n    this.after(feature);\n  }\n  before(element) {\n    if (!this.isRecording()) {\n      this.elementStack.push(element);\n    }\n  }\n  after(element) {\n    if (!this.isRecording()) {\n      const index = this.elementStack.lastIndexOf(element);\n      if (index >= 0) {\n        this.elementStack.splice(index);\n      }\n    }\n  }\n  get currIdx() {\n    return this.wrapper.currIdx;\n  }\n}\nconst defaultConfig = {\n  recoveryEnabled: true,\n  nodeLocationTracking: 'full',\n  skipValidations: true,\n  errorMessageProvider: new LangiumParserErrorMessageProvider()\n};\n/**\n * This class wraps the embedded actions parser of chevrotain and exposes protected methods.\n * This way, we can build the `LangiumParser` as a composition.\n */\nclass ChevrotainWrapper extends EmbeddedActionsParser {\n  constructor(tokens, config) {\n    const useDefaultLookahead = config && 'maxLookahead' in config;\n    super(tokens, Object.assign(Object.assign(Object.assign({}, defaultConfig), {\n      lookaheadStrategy: useDefaultLookahead ? new LLkLookaheadStrategy({\n        maxLookahead: config.maxLookahead\n      }) : new LLStarLookaheadStrategy({\n        // If validations are skipped, don't log the lookahead warnings\n        logging: config.skipValidations ? () => {} : undefined\n      })\n    }), config));\n  }\n  get IS_RECORDING() {\n    return this.RECORDING_PHASE;\n  }\n  DEFINE_RULE(name, impl) {\n    return this.RULE(name, impl);\n  }\n  wrapSelfAnalysis() {\n    this.performSelfAnalysis();\n  }\n  wrapConsume(idx, tokenType) {\n    return this.consume(idx, tokenType);\n  }\n  wrapSubrule(idx, rule, args) {\n    return this.subrule(idx, rule, {\n      ARGS: [args]\n    });\n  }\n  wrapOr(idx, choices) {\n    this.or(idx, choices);\n  }\n  wrapOption(idx, callback) {\n    this.option(idx, callback);\n  }\n  wrapMany(idx, callback) {\n    this.many(idx, callback);\n  }\n  wrapAtLeastOne(idx, callback) {\n    this.atLeastOne(idx, callback);\n  }\n}", "map": {"version": 3, "names": ["defaultParserErrorProvider", "EmbeddedActions<PERSON><PERSON><PERSON>", "LLkLookaheadStrategy", "LLStarLookaheadStrategy", "isAssignment", "isCrossReference", "isKeyword", "getExplicitRuleType", "isDataTypeRule", "assignMandatoryProperties", "getContainerOfType", "linkContentToContainer", "CstNodeBuilder", "DatatypeSymbol", "Symbol", "isDataTypeNode", "node", "$type", "ruleSuffix", "withRuleSuffix", "name", "endsWith", "AbstractLangiumParser", "constructor", "services", "_unorderedGroups", "Map", "allRules", "lexer", "parser", "<PERSON><PERSON>", "tokens", "definition", "production", "LanguageMetaData", "mode", "wrapper", "ChevrotainWrapper", "Object", "assign", "ParserConfig", "skipValidations", "errorMessageProvider", "ParserErrorMessageProvider", "alternatives", "idx", "choices", "wrapOr", "optional", "callback", "wrapOption", "many", "wrapMany", "atLeastOne", "wrapAtLeastOne", "getRule", "get", "isRecording", "IS_RECORDING", "unorderedGroups", "getRuleStack", "RULE_STACK", "finalize", "wrapSelfAnalysis", "LangiumParser", "current", "stack", "length", "nodeBuilder", "assignmentMap", "linker", "references", "<PERSON><PERSON>", "converter", "ValueConverter", "astReflection", "shared", "AstReflection", "rule", "impl", "type", "computeRuleType", "rule<PERSON>ethod", "DEFINE_RULE", "startImplementation", "bind", "set", "entry", "mainRule", "fragment", "undefined", "explicit", "parse", "input", "options", "buildRootNode", "lexerResult", "tokenize", "Error", "result", "call", "addHiddenNodes", "hidden", "clear", "value", "lexerErrors", "errors", "lexerReport", "report", "parserErrors", "implementation", "args", "createNode", "push", "err", "construct", "extractHiddenTokens", "token", "hiddenTokens", "offset", "startOffset", "i", "splice", "consume", "tokenType", "feature", "wrapConsume", "isValidToken", "leafNode", "buildLeafNode", "assignment", "isCrossRef", "getAssignment", "convertedValue", "image", "convert", "operator", "text", "toString", "isInsertedInRecovery", "isNaN", "endOffset", "subrule", "cstNode", "buildCompositeNode", "subruleResult", "wrapSubrule", "performSubruleAssignment", "object", "assignWithoutOverride", "newItem", "pop", "action", "last", "removeNode", "$cstNode", "content", "obj", "has", "terminal", "item", "buildReference", "Array", "isArray", "target", "source", "existingValue", "entries", "newValue", "targetCstNode", "astNode", "definitionErrors", "AbstractParserErrorMessageProvider", "buildMismatchTokenMessage", "buildNotAllInputParsedMessage", "buildNoViableAltMessage", "buildEarlyExitMessage", "LangiumParserErrorMessageProvider", "expected", "actual", "expectedMsg", "LABEL", "substring", "firstRedundant", "LangiumCompletionParser", "elementStack", "lastElementStack", "nextTokenIndex", "stackSize", "resetState", "tokenIndex", "size", "keepStackSize", "resetStackSize", "removeUnexpectedElements", "currIdx", "before", "after", "element", "index", "lastIndexOf", "defaultConfig", "recoveryEnabled", "nodeLocationTracking", "config", "useDefaultLookahead", "lookaheadStrategy", "max<PERSON><PERSON><PERSON><PERSON>", "logging", "RECORDING_PHASE", "RULE", "performSelfAnalysis", "ARGS", "or", "option"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/langium/src/parser/langium-parser.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport type { DSLMethodOpts, ILexingError, IOrAlt, IParserErrorMessageProvider, IRecognitionException, IToken, TokenType, TokenVocabulary } from 'chevrotain';\r\nimport type { AbstractElement, Action, Assignment, ParserRule } from '../languages/generated/ast.js';\r\nimport type { Linker } from '../references/linker.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, AstReflection, CompositeCstNode, CstNode } from '../syntax-tree.js';\r\nimport type { Lexer, LexerResult } from './lexer.js';\r\nimport type { IParserConfig } from './parser-config.js';\r\nimport type { ValueConverter } from './value-converter.js';\r\nimport { defaultParserErrorProvider, EmbeddedActionsParser, LLkLookaheadStrategy } from 'chevrotain';\r\nimport { LLStarLookaheadStrategy } from 'chevrotain-allstar';\r\nimport { isAssignment, isCrossReference, isKeyword } from '../languages/generated/ast.js';\r\nimport { getExplicitRuleType, isDataTypeRule } from '../utils/grammar-utils.js';\r\nimport { assignMandatoryProperties, getContainerOfType, linkContentToContainer } from '../utils/ast-utils.js';\r\nimport { CstNodeBuilder } from './cst-node-builder.js';\r\nimport type { LexingReport } from './token-builder.js';\r\n\r\nexport type ParseResult<T = AstNode> = {\r\n    value: T,\r\n    parserErrors: IRecognitionException[],\r\n    lexerErrors: ILexingError[],\r\n    lexerReport?: LexingReport\r\n}\r\n\r\nexport const DatatypeSymbol = Symbol('Datatype');\r\n\r\ninterface DataTypeNode {\r\n    $cstNode: CompositeCstNode\r\n    /** Instead of a string, this node is uniquely identified by the `Datatype` symbol */\r\n    $type: symbol\r\n    /** Used as a storage for all parsed terminals, keywords and sub-datatype rules */\r\n    value: string\r\n}\r\n\r\nfunction isDataTypeNode(node: { $type: string | symbol | undefined }): node is DataTypeNode {\r\n    return node.$type === DatatypeSymbol;\r\n}\r\n\r\ntype RuleResult = (args: Args) => any;\r\n\r\ntype Args = Record<string, boolean>;\r\n\r\ntype RuleImpl = (args: Args) => any;\r\n\r\ninterface AssignmentElement {\r\n    assignment?: Assignment\r\n    isCrossRef: boolean\r\n}\r\n\r\n/**\r\n * Base interface for all parsers. Mainly used by the `parser-builder-base.ts` to perform work on different kinds of parsers.\r\n * The main use cases are:\r\n * * AST parser: Based on a string, create an AST for the current grammar\r\n * * Completion parser: Based on a partial string, identify the current position of the input within the grammar\r\n */\r\nexport interface BaseParser {\r\n    /**\r\n     * Adds a new parser rule to the parser\r\n     */\r\n    rule(rule: ParserRule, impl: RuleImpl): RuleResult;\r\n    /**\r\n     * Returns the executable rule function for the specified rule name\r\n     */\r\n    getRule(name: string): RuleResult | undefined;\r\n    /**\r\n     * Performs alternatives parsing (the `|` operation in EBNF/Langium)\r\n     */\r\n    alternatives(idx: number, choices: Array<IOrAlt<any>>): void;\r\n    /**\r\n     * Parses the callback as optional (the `?` operation in EBNF/Langium)\r\n     */\r\n    optional(idx: number, callback: DSLMethodOpts<unknown>): void;\r\n    /**\r\n     * Parses the callback 0 or more times (the `*` operation in EBNF/Langium)\r\n     */\r\n    many(idx: number, callback: DSLMethodOpts<unknown>): void;\r\n    /**\r\n     * Parses the callback 1 or more times (the `+` operation in EBNF/Langium)\r\n     */\r\n    atLeastOne(idx: number, callback: DSLMethodOpts<unknown>): void;\r\n    /**\r\n     * Consumes a specific token type from the token input stream.\r\n     * Requires a unique index within the rule for a specific token type.\r\n     */\r\n    consume(idx: number, tokenType: TokenType, feature: AbstractElement): void;\r\n    /**\r\n     * Invokes the executable function for a given parser rule.\r\n     * Requires a unique index within the rule for a specific sub rule.\r\n     * Arguments can be supplied to the rule invocation for semantic predicates\r\n     */\r\n    subrule(idx: number, rule: RuleResult, fragment: boolean, feature: AbstractElement, args: Args): void;\r\n    /**\r\n     * Executes a grammar action that modifies the currently active AST node\r\n     */\r\n    action($type: string, action: Action): void;\r\n    /**\r\n     * Finishes construction of the current AST node. Only used by the AST parser.\r\n     */\r\n    construct(): unknown;\r\n    /**\r\n     * Whether the parser is currently actually in use or in \"recording mode\".\r\n     * Recording mode is activated once when the parser is analyzing itself.\r\n     * During this phase, no input exists and therefore no AST should be constructed\r\n     */\r\n    isRecording(): boolean;\r\n    /**\r\n     * Current state of the unordered groups\r\n     */\r\n    get unorderedGroups(): Map<string, boolean[]>;\r\n    /**\r\n     * The rule stack indicates the indices of rules that are currently invoked,\r\n     * in order of their invocation.\r\n     */\r\n    getRuleStack(): number[];\r\n}\r\n\r\nconst ruleSuffix = '\\u200B';\r\nconst withRuleSuffix = (name: string): string => name.endsWith(ruleSuffix) ? name : name + ruleSuffix;\r\n\r\nexport abstract class AbstractLangiumParser implements BaseParser {\r\n\r\n    protected readonly lexer: Lexer;\r\n    protected readonly wrapper: ChevrotainWrapper;\r\n    protected _unorderedGroups: Map<string, boolean[]> = new Map<string, boolean[]>();\r\n\r\n    protected allRules = new Map<string, RuleResult>();\r\n    protected mainRule!: RuleResult;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.lexer = services.parser.Lexer;\r\n        const tokens = this.lexer.definition;\r\n        const production = services.LanguageMetaData.mode === 'production';\r\n        this.wrapper = new ChevrotainWrapper(tokens, {\r\n            ...services.parser.ParserConfig,\r\n            skipValidations: production,\r\n            errorMessageProvider: services.parser.ParserErrorMessageProvider\r\n        });\r\n    }\r\n\r\n    alternatives(idx: number, choices: Array<IOrAlt<any>>): void {\r\n        this.wrapper.wrapOr(idx, choices);\r\n    }\r\n\r\n    optional(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.wrapper.wrapOption(idx, callback);\r\n    }\r\n\r\n    many(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.wrapper.wrapMany(idx, callback);\r\n    }\r\n\r\n    atLeastOne(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.wrapper.wrapAtLeastOne(idx, callback);\r\n    }\r\n\r\n    abstract rule(rule: ParserRule, impl: RuleImpl): RuleResult;\r\n    abstract consume(idx: number, tokenType: TokenType, feature: AbstractElement): void;\r\n    abstract subrule(idx: number, rule: RuleResult, fragment: boolean, feature: AbstractElement, args: Args): void;\r\n    abstract action($type: string, action: Action): void;\r\n    abstract construct(): unknown;\r\n\r\n    getRule(name: string): RuleResult | undefined {\r\n        return this.allRules.get(name);\r\n    }\r\n\r\n    isRecording(): boolean {\r\n        return this.wrapper.IS_RECORDING;\r\n    }\r\n\r\n    get unorderedGroups(): Map<string, boolean[]> {\r\n        return this._unorderedGroups;\r\n    }\r\n\r\n    getRuleStack(): number[] {\r\n        return (this.wrapper as any).RULE_STACK;\r\n    }\r\n\r\n    finalize(): void {\r\n        this.wrapper.wrapSelfAnalysis();\r\n    }\r\n}\r\n\r\nexport interface ParserOptions {\r\n    rule?: string\r\n}\r\n\r\nexport class LangiumParser extends AbstractLangiumParser {\r\n    private readonly linker: Linker;\r\n    private readonly converter: ValueConverter;\r\n    private readonly astReflection: AstReflection;\r\n    private readonly nodeBuilder = new CstNodeBuilder();\r\n    private lexerResult?: LexerResult;\r\n    private stack: any[] = [];\r\n    private assignmentMap = new Map<AbstractElement, AssignmentElement | undefined>();\r\n\r\n    private get current(): any {\r\n        return this.stack[this.stack.length - 1];\r\n    }\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        super(services);\r\n        this.linker = services.references.Linker;\r\n        this.converter = services.parser.ValueConverter;\r\n        this.astReflection = services.shared.AstReflection;\r\n    }\r\n\r\n    rule(rule: ParserRule, impl: RuleImpl): RuleResult {\r\n        const type = this.computeRuleType(rule);\r\n        const ruleMethod = this.wrapper.DEFINE_RULE(withRuleSuffix(rule.name), this.startImplementation(type, impl).bind(this));\r\n        this.allRules.set(rule.name, ruleMethod);\r\n        if (rule.entry) {\r\n            this.mainRule = ruleMethod;\r\n        }\r\n        return ruleMethod;\r\n    }\r\n\r\n    private computeRuleType(rule: ParserRule): string | symbol | undefined {\r\n        if (rule.fragment) {\r\n            return undefined;\r\n        } else if (isDataTypeRule(rule)) {\r\n            return DatatypeSymbol;\r\n        } else {\r\n            const explicit = getExplicitRuleType(rule);\r\n            return explicit ?? rule.name;\r\n        }\r\n    }\r\n\r\n    parse<T extends AstNode = AstNode>(input: string, options: ParserOptions = {}): ParseResult<T> {\r\n        this.nodeBuilder.buildRootNode(input);\r\n        const lexerResult = this.lexerResult = this.lexer.tokenize(input);\r\n        this.wrapper.input = lexerResult.tokens;\r\n        const ruleMethod = options.rule ? this.allRules.get(options.rule) : this.mainRule;\r\n        if (!ruleMethod) {\r\n            throw new Error(options.rule ? `No rule found with name '${options.rule}'` : 'No main rule available.');\r\n        }\r\n        const result = ruleMethod.call(this.wrapper, {});\r\n        this.nodeBuilder.addHiddenNodes(lexerResult.hidden);\r\n        this.unorderedGroups.clear();\r\n        this.lexerResult = undefined;\r\n        return {\r\n            value: result,\r\n            lexerErrors: lexerResult.errors,\r\n            lexerReport: lexerResult.report,\r\n            parserErrors: this.wrapper.errors\r\n        };\r\n    }\r\n\r\n    private startImplementation($type: string | symbol | undefined, implementation: RuleImpl): RuleImpl {\r\n        return (args) => {\r\n            // Only create a new AST node in case the calling rule is not a fragment rule\r\n            const createNode = !this.isRecording() && $type !== undefined;\r\n            if (createNode) {\r\n                const node: any = { $type };\r\n                this.stack.push(node);\r\n                if ($type === DatatypeSymbol) {\r\n                    node.value = '';\r\n                }\r\n            }\r\n            let result: unknown;\r\n            try {\r\n                result = implementation(args);\r\n            } catch (err) {\r\n                result = undefined;\r\n            }\r\n            if (result === undefined && createNode) {\r\n                result = this.construct();\r\n            }\r\n            return result;\r\n        };\r\n    }\r\n\r\n    private extractHiddenTokens(token: IToken): IToken[] {\r\n        const hiddenTokens = this.lexerResult!.hidden;\r\n        if (!hiddenTokens.length) {\r\n            return [];\r\n        }\r\n        const offset = token.startOffset;\r\n        for (let i = 0; i < hiddenTokens.length; i++) {\r\n            const token = hiddenTokens[i];\r\n            if (token.startOffset > offset) {\r\n                return hiddenTokens.splice(0, i);\r\n            }\r\n        }\r\n        return hiddenTokens.splice(0, hiddenTokens.length);\r\n    }\r\n\r\n    consume(idx: number, tokenType: TokenType, feature: AbstractElement): void {\r\n        const token = this.wrapper.wrapConsume(idx, tokenType);\r\n        if (!this.isRecording() && this.isValidToken(token)) {\r\n            const hiddenTokens = this.extractHiddenTokens(token);\r\n            this.nodeBuilder.addHiddenNodes(hiddenTokens);\r\n            const leafNode = this.nodeBuilder.buildLeafNode(token, feature);\r\n            const { assignment, isCrossRef } = this.getAssignment(feature);\r\n            const current = this.current;\r\n            if (assignment) {\r\n                const convertedValue = isKeyword(feature) ? token.image : this.converter.convert(token.image, leafNode);\r\n                this.assign(assignment.operator, assignment.feature, convertedValue, leafNode, isCrossRef);\r\n            } else if (isDataTypeNode(current)) {\r\n                let text = token.image;\r\n                if (!isKeyword(feature)) {\r\n                    text = this.converter.convert(text, leafNode).toString();\r\n                }\r\n                current.value += text;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Most consumed parser tokens are valid. However there are two cases in which they are not valid:\r\n     *\r\n     * 1. They were inserted during error recovery by the parser. These tokens don't really exist and should not be further processed\r\n     * 2. They contain invalid token ranges. This might include the special EOF token, or other tokens produced by invalid token builders.\r\n     */\r\n    private isValidToken(token: IToken): boolean {\r\n        return !token.isInsertedInRecovery && !isNaN(token.startOffset) && typeof token.endOffset === 'number' && !isNaN(token.endOffset);\r\n    }\r\n\r\n    subrule(idx: number, rule: RuleResult, fragment: boolean, feature: AbstractElement, args: Args): void {\r\n        let cstNode: CompositeCstNode | undefined;\r\n        if (!this.isRecording() && !fragment) {\r\n            // We only want to create a new CST node if the subrule actually creates a new AST node.\r\n            // In other cases like calls of fragment rules the current CST/AST is populated further.\r\n            // Note that skipping this initialization and leaving cstNode unassigned also skips the subrule assignment later on.\r\n            // This is intended, as fragment rules only enrich the current AST node\r\n            cstNode = this.nodeBuilder.buildCompositeNode(feature);\r\n        }\r\n        const subruleResult = this.wrapper.wrapSubrule(idx, rule, args) as any;\r\n        if (!this.isRecording() && cstNode && cstNode.length > 0) {\r\n            this.performSubruleAssignment(subruleResult, feature, cstNode);\r\n        }\r\n    }\r\n\r\n    private performSubruleAssignment(result: any, feature: AbstractElement, cstNode: CompositeCstNode): void {\r\n        const { assignment, isCrossRef } = this.getAssignment(feature);\r\n        if (assignment) {\r\n            this.assign(assignment.operator, assignment.feature, result, cstNode, isCrossRef);\r\n        } else if (!assignment) {\r\n            // If we call a subrule without an assignment we either:\r\n            // 1. append the result of the subrule (data type rule)\r\n            // 2. override the current object with the newly parsed object\r\n            // If the current element is an AST node and the result of the subrule\r\n            // is a data type rule, we can safely discard the results.\r\n            const current = this.current;\r\n            if (isDataTypeNode(current)) {\r\n                current.value += result.toString();\r\n            } else if (typeof result === 'object' && result) {\r\n                const object = this.assignWithoutOverride(result, current);\r\n                const newItem = object;\r\n                this.stack.pop();\r\n                this.stack.push(newItem);\r\n            }\r\n        }\r\n    }\r\n\r\n    action($type: string, action: Action): void {\r\n        if (!this.isRecording()) {\r\n            let last = this.current;\r\n            if (action.feature && action.operator) {\r\n                last = this.construct();\r\n                this.nodeBuilder.removeNode(last.$cstNode);\r\n                const node = this.nodeBuilder.buildCompositeNode(action);\r\n                node.content.push(last.$cstNode);\r\n                const newItem = { $type };\r\n                this.stack.push(newItem);\r\n                this.assign(action.operator, action.feature, last, last.$cstNode, false);\r\n            } else {\r\n                last.$type = $type;\r\n            }\r\n        }\r\n    }\r\n\r\n    construct(): unknown {\r\n        if (this.isRecording()) {\r\n            return undefined;\r\n        }\r\n        const obj = this.current;\r\n        linkContentToContainer(obj);\r\n        this.nodeBuilder.construct(obj);\r\n        this.stack.pop();\r\n        if (isDataTypeNode(obj)) {\r\n            return this.converter.convert(obj.value, obj.$cstNode);\r\n        } else {\r\n            assignMandatoryProperties(this.astReflection, obj);\r\n        }\r\n        return obj;\r\n    }\r\n\r\n    private getAssignment(feature: AbstractElement): AssignmentElement {\r\n        if (!this.assignmentMap.has(feature)) {\r\n            const assignment = getContainerOfType(feature, isAssignment);\r\n            this.assignmentMap.set(feature, {\r\n                assignment: assignment,\r\n                isCrossRef: assignment ? isCrossReference(assignment.terminal) : false\r\n            });\r\n        }\r\n        return this.assignmentMap.get(feature)!;\r\n    }\r\n\r\n    private assign(operator: string, feature: string, value: unknown, cstNode: CstNode, isCrossRef: boolean): void {\r\n        const obj = this.current;\r\n        let item: unknown;\r\n        if (isCrossRef && typeof value === 'string') {\r\n            item = this.linker.buildReference(obj, feature, cstNode, value);\r\n        } else {\r\n            item = value;\r\n        }\r\n        switch (operator) {\r\n            case '=': {\r\n                obj[feature] = item;\r\n                break;\r\n            }\r\n            case '?=': {\r\n                obj[feature] = true;\r\n                break;\r\n            }\r\n            case '+=': {\r\n                if (!Array.isArray(obj[feature])) {\r\n                    obj[feature] = [];\r\n                }\r\n                obj[feature].push(item);\r\n            }\r\n        }\r\n    }\r\n\r\n    private assignWithoutOverride(target: any, source: any): any {\r\n        for (const [name, existingValue] of Object.entries(source)) {\r\n            const newValue = target[name];\r\n            if (newValue === undefined) {\r\n                target[name] = existingValue;\r\n            } else if (Array.isArray(newValue) && Array.isArray(existingValue)) {\r\n                existingValue.push(...newValue);\r\n                target[name] = existingValue;\r\n            }\r\n        }\r\n        // The target was parsed from a unassigned subrule\r\n        // After the subrule construction, it received a cst node\r\n        // This CST node will later be overriden by the cst node builder\r\n        // To prevent references to stale AST nodes in the CST,\r\n        // we need to remove the reference here\r\n        const targetCstNode = target.$cstNode;\r\n        if (targetCstNode) {\r\n            targetCstNode.astNode = undefined;\r\n            target.$cstNode = undefined;\r\n        }\r\n        return target;\r\n    }\r\n\r\n    get definitionErrors(): IParserDefinitionError[] {\r\n        return this.wrapper.definitionErrors;\r\n    }\r\n}\r\n\r\nexport interface IParserDefinitionError {\r\n    message: string\r\n    type: number\r\n    ruleName?: string\r\n}\r\n\r\nexport abstract class AbstractParserErrorMessageProvider implements IParserErrorMessageProvider {\r\n\r\n    buildMismatchTokenMessage(options: {\r\n        expected: TokenType\r\n        actual: IToken\r\n        previous: IToken\r\n        ruleName: string\r\n    }): string {\r\n        return defaultParserErrorProvider.buildMismatchTokenMessage(options);\r\n    }\r\n\r\n    buildNotAllInputParsedMessage(options: {\r\n        firstRedundant: IToken\r\n        ruleName: string\r\n    }): string {\r\n        return defaultParserErrorProvider.buildNotAllInputParsedMessage(options);\r\n    }\r\n\r\n    buildNoViableAltMessage(options: {\r\n        expectedPathsPerAlt: TokenType[][][]\r\n        actual: IToken[]\r\n        previous: IToken\r\n        customUserDescription: string\r\n        ruleName: string\r\n    }): string {\r\n        return defaultParserErrorProvider.buildNoViableAltMessage(options);\r\n    }\r\n\r\n    buildEarlyExitMessage(options: {\r\n        expectedIterationPaths: TokenType[][]\r\n        actual: IToken[]\r\n        previous: IToken\r\n        customUserDescription: string\r\n        ruleName: string\r\n    }): string {\r\n        return defaultParserErrorProvider.buildEarlyExitMessage(options);\r\n    }\r\n\r\n}\r\n\r\nexport class LangiumParserErrorMessageProvider extends AbstractParserErrorMessageProvider {\r\n\r\n    override buildMismatchTokenMessage({ expected, actual }: {\r\n        expected: TokenType\r\n        actual: IToken\r\n        previous: IToken\r\n        ruleName: string\r\n    }): string {\r\n        const expectedMsg = expected.LABEL\r\n            ? '`' + expected.LABEL + '`'\r\n            : expected.name.endsWith(':KW')\r\n                ? `keyword '${expected.name.substring(0, expected.name.length - 3)}'`\r\n                : `token of type '${expected.name}'`;\r\n        return `Expecting ${expectedMsg} but found \\`${actual.image}\\`.`;\r\n    }\r\n\r\n    override buildNotAllInputParsedMessage({ firstRedundant }: {\r\n        firstRedundant: IToken\r\n        ruleName: string\r\n    }): string {\r\n        return `Expecting end of file but found \\`${firstRedundant.image}\\`.`;\r\n    }\r\n}\r\n\r\nexport interface CompletionParserResult {\r\n    tokens: IToken[]\r\n    elementStack: AbstractElement[]\r\n    tokenIndex: number\r\n}\r\n\r\nexport class LangiumCompletionParser extends AbstractLangiumParser {\r\n    private tokens: IToken[] = [];\r\n\r\n    private elementStack: AbstractElement[] = [];\r\n    private lastElementStack: AbstractElement[] = [];\r\n    private nextTokenIndex = 0;\r\n    private stackSize = 0;\r\n\r\n    action(): void {\r\n        // NOOP\r\n    }\r\n\r\n    construct(): unknown {\r\n        // NOOP\r\n        return undefined;\r\n    }\r\n\r\n    parse(input: string): CompletionParserResult {\r\n        this.resetState();\r\n        const tokens = this.lexer.tokenize(input, { mode: 'partial' });\r\n        this.tokens = tokens.tokens;\r\n        this.wrapper.input = [...this.tokens];\r\n        this.mainRule.call(this.wrapper, {});\r\n        this.unorderedGroups.clear();\r\n        return {\r\n            tokens: this.tokens,\r\n            elementStack: [...this.lastElementStack],\r\n            tokenIndex: this.nextTokenIndex\r\n        };\r\n    }\r\n\r\n    rule(rule: ParserRule, impl: RuleImpl): RuleResult {\r\n        const ruleMethod = this.wrapper.DEFINE_RULE(withRuleSuffix(rule.name), this.startImplementation(impl).bind(this));\r\n        this.allRules.set(rule.name, ruleMethod);\r\n        if (rule.entry) {\r\n            this.mainRule = ruleMethod;\r\n        }\r\n        return ruleMethod;\r\n    }\r\n\r\n    private resetState(): void {\r\n        this.elementStack = [];\r\n        this.lastElementStack = [];\r\n        this.nextTokenIndex = 0;\r\n        this.stackSize = 0;\r\n    }\r\n\r\n    private startImplementation(implementation: RuleImpl): RuleImpl {\r\n        return (args) => {\r\n            const size = this.keepStackSize();\r\n            try {\r\n                implementation(args);\r\n            } finally {\r\n                this.resetStackSize(size);\r\n            }\r\n        };\r\n    }\r\n\r\n    private removeUnexpectedElements(): void {\r\n        this.elementStack.splice(this.stackSize);\r\n    }\r\n\r\n    keepStackSize(): number {\r\n        const size = this.elementStack.length;\r\n        this.stackSize = size;\r\n        return size;\r\n    }\r\n\r\n    resetStackSize(size: number): void {\r\n        this.removeUnexpectedElements();\r\n        this.stackSize = size;\r\n    }\r\n\r\n    consume(idx: number, tokenType: TokenType, feature: AbstractElement): void {\r\n        this.wrapper.wrapConsume(idx, tokenType);\r\n        if (!this.isRecording()) {\r\n            this.lastElementStack = [...this.elementStack, feature];\r\n            this.nextTokenIndex = this.currIdx + 1;\r\n        }\r\n    }\r\n\r\n    subrule(idx: number, rule: RuleResult, fragment: boolean, feature: AbstractElement, args: Args): void {\r\n        this.before(feature);\r\n        this.wrapper.wrapSubrule(idx, rule, args);\r\n        this.after(feature);\r\n    }\r\n\r\n    before(element: AbstractElement): void {\r\n        if (!this.isRecording()) {\r\n            this.elementStack.push(element);\r\n        }\r\n    }\r\n\r\n    after(element: AbstractElement): void {\r\n        if (!this.isRecording()) {\r\n            const index = this.elementStack.lastIndexOf(element);\r\n            if (index >= 0) {\r\n                this.elementStack.splice(index);\r\n            }\r\n        }\r\n    }\r\n\r\n    get currIdx(): number {\r\n        return (this.wrapper as any).currIdx;\r\n    }\r\n}\r\n\r\nconst defaultConfig: IParserConfig = {\r\n    recoveryEnabled: true,\r\n    nodeLocationTracking: 'full',\r\n    skipValidations: true,\r\n    errorMessageProvider: new LangiumParserErrorMessageProvider()\r\n};\r\n\r\n/**\r\n * This class wraps the embedded actions parser of chevrotain and exposes protected methods.\r\n * This way, we can build the `LangiumParser` as a composition.\r\n */\r\nclass ChevrotainWrapper extends EmbeddedActionsParser {\r\n\r\n    // This array is set in the base implementation of Chevrotain.\r\n    definitionErrors: IParserDefinitionError[];\r\n\r\n    constructor(tokens: TokenVocabulary, config: IParserConfig) {\r\n        const useDefaultLookahead = config && 'maxLookahead' in config;\r\n        super(tokens, {\r\n            ...defaultConfig,\r\n            lookaheadStrategy: useDefaultLookahead\r\n                ? new LLkLookaheadStrategy({ maxLookahead: config.maxLookahead })\r\n                : new LLStarLookaheadStrategy({\r\n                    // If validations are skipped, don't log the lookahead warnings\r\n                    logging: config.skipValidations ? () => { } : undefined\r\n                }),\r\n            ...config,\r\n        });\r\n    }\r\n\r\n    get IS_RECORDING(): boolean {\r\n        return this.RECORDING_PHASE;\r\n    }\r\n\r\n    DEFINE_RULE(name: string, impl: RuleImpl): RuleResult {\r\n        return this.RULE(name, impl);\r\n    }\r\n\r\n    wrapSelfAnalysis(): void {\r\n        this.performSelfAnalysis();\r\n    }\r\n\r\n    wrapConsume(idx: number, tokenType: TokenType): IToken {\r\n        return this.consume(idx, tokenType);\r\n    }\r\n\r\n    wrapSubrule(idx: number, rule: RuleResult, args: Args): unknown {\r\n        return this.subrule(idx, rule, {\r\n            ARGS: [args]\r\n        });\r\n    }\r\n\r\n    wrapOr(idx: number, choices: Array<IOrAlt<any>>): void {\r\n        this.or(idx, choices);\r\n    }\r\n\r\n    wrapOption(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.option(idx, callback);\r\n    }\r\n\r\n    wrapMany(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.many(idx, callback);\r\n    }\r\n\r\n    wrapAtLeastOne(idx: number, callback: DSLMethodOpts<unknown>): void {\r\n        this.atLeastOne(idx, callback);\r\n    }\r\n}\r\n"], "mappings": "AAAA;;;;;AAeA,SAASA,0BAA0B,EAAEC,qBAAqB,EAAEC,oBAAoB,QAAQ,YAAY;AACpG,SAASC,uBAAuB,QAAQ,oBAAoB;AAC5D,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,QAAQ,+BAA+B;AACzF,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,2BAA2B;AAC/E,SAASC,yBAAyB,EAAEC,kBAAkB,EAAEC,sBAAsB,QAAQ,uBAAuB;AAC7G,SAASC,cAAc,QAAQ,uBAAuB;AAUtD,OAAO,MAAMC,cAAc,GAAGC,MAAM,CAAC,UAAU,CAAC;AAUhD,SAASC,cAAcA,CAACC,IAA4C;EAChE,OAAOA,IAAI,CAACC,KAAK,KAAKJ,cAAc;AACxC;AAgFA,MAAMK,UAAU,GAAG,QAAQ;AAC3B,MAAMC,cAAc,GAAIC,IAAY,IAAaA,IAAI,CAACC,QAAQ,CAACH,UAAU,CAAC,GAAGE,IAAI,GAAGA,IAAI,GAAGF,UAAU;AAErG,OAAM,MAAgBI,qBAAqB;EASvCC,YAAYC,QAA6B;IAL/B,KAAAC,gBAAgB,GAA2B,IAAIC,GAAG,EAAqB;IAEvE,KAAAC,QAAQ,GAAG,IAAID,GAAG,EAAsB;IAI9C,IAAI,CAACE,KAAK,GAAGJ,QAAQ,CAACK,MAAM,CAACC,KAAK;IAClC,MAAMC,MAAM,GAAG,IAAI,CAACH,KAAK,CAACI,UAAU;IACpC,MAAMC,UAAU,GAAGT,QAAQ,CAACU,gBAAgB,CAACC,IAAI,KAAK,YAAY;IAClE,IAAI,CAACC,OAAO,GAAG,IAAIC,iBAAiB,CAACN,MAAM,EAAAO,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACpCf,QAAQ,CAACK,MAAM,CAACW,YAAY;MAC/BC,eAAe,EAAER,UAAU;MAC3BS,oBAAoB,EAAElB,QAAQ,CAACK,MAAM,CAACc;IAA0B,GAClE;EACN;EAEAC,YAAYA,CAACC,GAAW,EAAEC,OAA2B;IACjD,IAAI,CAACV,OAAO,CAACW,MAAM,CAACF,GAAG,EAAEC,OAAO,CAAC;EACrC;EAEAE,QAAQA,CAACH,GAAW,EAAEI,QAAgC;IAClD,IAAI,CAACb,OAAO,CAACc,UAAU,CAACL,GAAG,EAAEI,QAAQ,CAAC;EAC1C;EAEAE,IAAIA,CAACN,GAAW,EAAEI,QAAgC;IAC9C,IAAI,CAACb,OAAO,CAACgB,QAAQ,CAACP,GAAG,EAAEI,QAAQ,CAAC;EACxC;EAEAI,UAAUA,CAACR,GAAW,EAAEI,QAAgC;IACpD,IAAI,CAACb,OAAO,CAACkB,cAAc,CAACT,GAAG,EAAEI,QAAQ,CAAC;EAC9C;EAQAM,OAAOA,CAACnC,IAAY;IAChB,OAAO,IAAI,CAACO,QAAQ,CAAC6B,GAAG,CAACpC,IAAI,CAAC;EAClC;EAEAqC,WAAWA,CAAA;IACP,OAAO,IAAI,CAACrB,OAAO,CAACsB,YAAY;EACpC;EAEA,IAAIC,eAAeA,CAAA;IACf,OAAO,IAAI,CAAClC,gBAAgB;EAChC;EAEAmC,YAAYA,CAAA;IACR,OAAQ,IAAI,CAACxB,OAAe,CAACyB,UAAU;EAC3C;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAAC1B,OAAO,CAAC2B,gBAAgB,EAAE;EACnC;;AAOJ,OAAM,MAAOC,aAAc,SAAQ1C,qBAAqB;EASpD,IAAY2C,OAAOA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EAC5C;EAEA5C,YAAYC,QAA6B;IACrC,KAAK,CAACA,QAAQ,CAAC;IAVF,KAAA4C,WAAW,GAAG,IAAIxD,cAAc,EAAE;IAE3C,KAAAsD,KAAK,GAAU,EAAE;IACjB,KAAAG,aAAa,GAAG,IAAI3C,GAAG,EAAkD;IAQ7E,IAAI,CAAC4C,MAAM,GAAG9C,QAAQ,CAAC+C,UAAU,CAACC,MAAM;IACxC,IAAI,CAACC,SAAS,GAAGjD,QAAQ,CAACK,MAAM,CAAC6C,cAAc;IAC/C,IAAI,CAACC,aAAa,GAAGnD,QAAQ,CAACoD,MAAM,CAACC,aAAa;EACtD;EAEAC,IAAIA,CAACA,IAAgB,EAAEC,IAAc;IACjC,MAAMC,IAAI,GAAG,IAAI,CAACC,eAAe,CAACH,IAAI,CAAC;IACvC,MAAMI,UAAU,GAAG,IAAI,CAAC9C,OAAO,CAAC+C,WAAW,CAAChE,cAAc,CAAC2D,IAAI,CAAC1D,IAAI,CAAC,EAAE,IAAI,CAACgE,mBAAmB,CAACJ,IAAI,EAAED,IAAI,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;IACvH,IAAI,CAAC1D,QAAQ,CAAC2D,GAAG,CAACR,IAAI,CAAC1D,IAAI,EAAE8D,UAAU,CAAC;IACxC,IAAIJ,IAAI,CAACS,KAAK,EAAE;MACZ,IAAI,CAACC,QAAQ,GAAGN,UAAU;IAC9B;IACA,OAAOA,UAAU;EACrB;EAEQD,eAAeA,CAACH,IAAgB;IACpC,IAAIA,IAAI,CAACW,QAAQ,EAAE;MACf,OAAOC,SAAS;IACpB,CAAC,MAAM,IAAIlF,cAAc,CAACsE,IAAI,CAAC,EAAE;MAC7B,OAAOjE,cAAc;IACzB,CAAC,MAAM;MACH,MAAM8E,QAAQ,GAAGpF,mBAAmB,CAACuE,IAAI,CAAC;MAC1C,OAAOa,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAIb,IAAI,CAAC1D,IAAI;IAChC;EACJ;EAEAwE,KAAKA,CAA8BC,KAAa,EAAEC,OAAA,GAAyB,EAAE;IACzE,IAAI,CAAC1B,WAAW,CAAC2B,aAAa,CAACF,KAAK,CAAC;IACrC,MAAMG,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI,CAACpE,KAAK,CAACqE,QAAQ,CAACJ,KAAK,CAAC;IACjE,IAAI,CAACzD,OAAO,CAACyD,KAAK,GAAGG,WAAW,CAACjE,MAAM;IACvC,MAAMmD,UAAU,GAAGY,OAAO,CAAChB,IAAI,GAAG,IAAI,CAACnD,QAAQ,CAAC6B,GAAG,CAACsC,OAAO,CAAChB,IAAI,CAAC,GAAG,IAAI,CAACU,QAAQ;IACjF,IAAI,CAACN,UAAU,EAAE;MACb,MAAM,IAAIgB,KAAK,CAACJ,OAAO,CAAChB,IAAI,GAAG,4BAA4BgB,OAAO,CAAChB,IAAI,GAAG,GAAG,yBAAyB,CAAC;IAC3G;IACA,MAAMqB,MAAM,GAAGjB,UAAU,CAACkB,IAAI,CAAC,IAAI,CAAChE,OAAO,EAAE,EAAE,CAAC;IAChD,IAAI,CAACgC,WAAW,CAACiC,cAAc,CAACL,WAAW,CAACM,MAAM,CAAC;IACnD,IAAI,CAAC3C,eAAe,CAAC4C,KAAK,EAAE;IAC5B,IAAI,CAACP,WAAW,GAAGN,SAAS;IAC5B,OAAO;MACHc,KAAK,EAAEL,MAAM;MACbM,WAAW,EAAET,WAAW,CAACU,MAAM;MAC/BC,WAAW,EAAEX,WAAW,CAACY,MAAM;MAC/BC,YAAY,EAAE,IAAI,CAACzE,OAAO,CAACsE;KAC9B;EACL;EAEQtB,mBAAmBA,CAACnE,KAAkC,EAAE6F,cAAwB;IACpF,OAAQC,IAAI,IAAI;MACZ;MACA,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACvD,WAAW,EAAE,IAAIxC,KAAK,KAAKyE,SAAS;MAC7D,IAAIsB,UAAU,EAAE;QACZ,MAAMhG,IAAI,GAAQ;UAAEC;QAAK,CAAE;QAC3B,IAAI,CAACiD,KAAK,CAAC+C,IAAI,CAACjG,IAAI,CAAC;QACrB,IAAIC,KAAK,KAAKJ,cAAc,EAAE;UAC1BG,IAAI,CAACwF,KAAK,GAAG,EAAE;QACnB;MACJ;MACA,IAAIL,MAAe;MACnB,IAAI;QACAA,MAAM,GAAGW,cAAc,CAACC,IAAI,CAAC;MACjC,CAAC,CAAC,OAAOG,GAAG,EAAE;QACVf,MAAM,GAAGT,SAAS;MACtB;MACA,IAAIS,MAAM,KAAKT,SAAS,IAAIsB,UAAU,EAAE;QACpCb,MAAM,GAAG,IAAI,CAACgB,SAAS,EAAE;MAC7B;MACA,OAAOhB,MAAM;IACjB,CAAC;EACL;EAEQiB,mBAAmBA,CAACC,KAAa;IACrC,MAAMC,YAAY,GAAG,IAAI,CAACtB,WAAY,CAACM,MAAM;IAC7C,IAAI,CAACgB,YAAY,CAACnD,MAAM,EAAE;MACtB,OAAO,EAAE;IACb;IACA,MAAMoD,MAAM,GAAGF,KAAK,CAACG,WAAW;IAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACnD,MAAM,EAAEsD,CAAC,EAAE,EAAE;MAC1C,MAAMJ,KAAK,GAAGC,YAAY,CAACG,CAAC,CAAC;MAC7B,IAAIJ,KAAK,CAACG,WAAW,GAAGD,MAAM,EAAE;QAC5B,OAAOD,YAAY,CAACI,MAAM,CAAC,CAAC,EAAED,CAAC,CAAC;MACpC;IACJ;IACA,OAAOH,YAAY,CAACI,MAAM,CAAC,CAAC,EAAEJ,YAAY,CAACnD,MAAM,CAAC;EACtD;EAEAwD,OAAOA,CAAC9E,GAAW,EAAE+E,SAAoB,EAAEC,OAAwB;IAC/D,MAAMR,KAAK,GAAG,IAAI,CAACjF,OAAO,CAAC0F,WAAW,CAACjF,GAAG,EAAE+E,SAAS,CAAC;IACtD,IAAI,CAAC,IAAI,CAACnE,WAAW,EAAE,IAAI,IAAI,CAACsE,YAAY,CAACV,KAAK,CAAC,EAAE;MACjD,MAAMC,YAAY,GAAG,IAAI,CAACF,mBAAmB,CAACC,KAAK,CAAC;MACpD,IAAI,CAACjD,WAAW,CAACiC,cAAc,CAACiB,YAAY,CAAC;MAC7C,MAAMU,QAAQ,GAAG,IAAI,CAAC5D,WAAW,CAAC6D,aAAa,CAACZ,KAAK,EAAEQ,OAAO,CAAC;MAC/D,MAAM;QAAEK,UAAU;QAAEC;MAAU,CAAE,GAAG,IAAI,CAACC,aAAa,CAACP,OAAO,CAAC;MAC9D,MAAM5D,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIiE,UAAU,EAAE;QACZ,MAAMG,cAAc,GAAG/H,SAAS,CAACuH,OAAO,CAAC,GAAGR,KAAK,CAACiB,KAAK,GAAG,IAAI,CAAC7D,SAAS,CAAC8D,OAAO,CAAClB,KAAK,CAACiB,KAAK,EAAEN,QAAQ,CAAC;QACvG,IAAI,CAACzF,MAAM,CAAC2F,UAAU,CAACM,QAAQ,EAAEN,UAAU,CAACL,OAAO,EAAEQ,cAAc,EAAEL,QAAQ,EAAEG,UAAU,CAAC;MAC9F,CAAC,MAAM,IAAIpH,cAAc,CAACkD,OAAO,CAAC,EAAE;QAChC,IAAIwE,IAAI,GAAGpB,KAAK,CAACiB,KAAK;QACtB,IAAI,CAAChI,SAAS,CAACuH,OAAO,CAAC,EAAE;UACrBY,IAAI,GAAG,IAAI,CAAChE,SAAS,CAAC8D,OAAO,CAACE,IAAI,EAAET,QAAQ,CAAC,CAACU,QAAQ,EAAE;QAC5D;QACAzE,OAAO,CAACuC,KAAK,IAAIiC,IAAI;MACzB;IACJ;EACJ;EAEA;;;;;;EAMQV,YAAYA,CAACV,KAAa;IAC9B,OAAO,CAACA,KAAK,CAACsB,oBAAoB,IAAI,CAACC,KAAK,CAACvB,KAAK,CAACG,WAAW,CAAC,IAAI,OAAOH,KAAK,CAACwB,SAAS,KAAK,QAAQ,IAAI,CAACD,KAAK,CAACvB,KAAK,CAACwB,SAAS,CAAC;EACrI;EAEAC,OAAOA,CAACjG,GAAW,EAAEiC,IAAgB,EAAEW,QAAiB,EAAEoC,OAAwB,EAAEd,IAAU;IAC1F,IAAIgC,OAAqC;IACzC,IAAI,CAAC,IAAI,CAACtF,WAAW,EAAE,IAAI,CAACgC,QAAQ,EAAE;MAClC;MACA;MACA;MACA;MACAsD,OAAO,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,kBAAkB,CAACnB,OAAO,CAAC;IAC1D;IACA,MAAMoB,aAAa,GAAG,IAAI,CAAC7G,OAAO,CAAC8G,WAAW,CAACrG,GAAG,EAAEiC,IAAI,EAAEiC,IAAI,CAAQ;IACtE,IAAI,CAAC,IAAI,CAACtD,WAAW,EAAE,IAAIsF,OAAO,IAAIA,OAAO,CAAC5E,MAAM,GAAG,CAAC,EAAE;MACtD,IAAI,CAACgF,wBAAwB,CAACF,aAAa,EAAEpB,OAAO,EAAEkB,OAAO,CAAC;IAClE;EACJ;EAEQI,wBAAwBA,CAAChD,MAAW,EAAE0B,OAAwB,EAAEkB,OAAyB;IAC7F,MAAM;MAAEb,UAAU;MAAEC;IAAU,CAAE,GAAG,IAAI,CAACC,aAAa,CAACP,OAAO,CAAC;IAC9D,IAAIK,UAAU,EAAE;MACZ,IAAI,CAAC3F,MAAM,CAAC2F,UAAU,CAACM,QAAQ,EAAEN,UAAU,CAACL,OAAO,EAAE1B,MAAM,EAAE4C,OAAO,EAAEZ,UAAU,CAAC;IACrF,CAAC,MAAM,IAAI,CAACD,UAAU,EAAE;MACpB;MACA;MACA;MACA;MACA;MACA,MAAMjE,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIlD,cAAc,CAACkD,OAAO,CAAC,EAAE;QACzBA,OAAO,CAACuC,KAAK,IAAIL,MAAM,CAACuC,QAAQ,EAAE;MACtC,CAAC,MAAM,IAAI,OAAOvC,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;QAC7C,MAAMiD,MAAM,GAAG,IAAI,CAACC,qBAAqB,CAAClD,MAAM,EAAElC,OAAO,CAAC;QAC1D,MAAMqF,OAAO,GAAGF,MAAM;QACtB,IAAI,CAAClF,KAAK,CAACqF,GAAG,EAAE;QAChB,IAAI,CAACrF,KAAK,CAAC+C,IAAI,CAACqC,OAAO,CAAC;MAC5B;IACJ;EACJ;EAEAE,MAAMA,CAACvI,KAAa,EAAEuI,MAAc;IAChC,IAAI,CAAC,IAAI,CAAC/F,WAAW,EAAE,EAAE;MACrB,IAAIgG,IAAI,GAAG,IAAI,CAACxF,OAAO;MACvB,IAAIuF,MAAM,CAAC3B,OAAO,IAAI2B,MAAM,CAAChB,QAAQ,EAAE;QACnCiB,IAAI,GAAG,IAAI,CAACtC,SAAS,EAAE;QACvB,IAAI,CAAC/C,WAAW,CAACsF,UAAU,CAACD,IAAI,CAACE,QAAQ,CAAC;QAC1C,MAAM3I,IAAI,GAAG,IAAI,CAACoD,WAAW,CAAC4E,kBAAkB,CAACQ,MAAM,CAAC;QACxDxI,IAAI,CAAC4I,OAAO,CAAC3C,IAAI,CAACwC,IAAI,CAACE,QAAQ,CAAC;QAChC,MAAML,OAAO,GAAG;UAAErI;QAAK,CAAE;QACzB,IAAI,CAACiD,KAAK,CAAC+C,IAAI,CAACqC,OAAO,CAAC;QACxB,IAAI,CAAC/G,MAAM,CAACiH,MAAM,CAAChB,QAAQ,EAAEgB,MAAM,CAAC3B,OAAO,EAAE4B,IAAI,EAAEA,IAAI,CAACE,QAAQ,EAAE,KAAK,CAAC;MAC5E,CAAC,MAAM;QACHF,IAAI,CAACxI,KAAK,GAAGA,KAAK;MACtB;IACJ;EACJ;EAEAkG,SAASA,CAAA;IACL,IAAI,IAAI,CAAC1D,WAAW,EAAE,EAAE;MACpB,OAAOiC,SAAS;IACpB;IACA,MAAMmE,GAAG,GAAG,IAAI,CAAC5F,OAAO;IACxBtD,sBAAsB,CAACkJ,GAAG,CAAC;IAC3B,IAAI,CAACzF,WAAW,CAAC+C,SAAS,CAAC0C,GAAG,CAAC;IAC/B,IAAI,CAAC3F,KAAK,CAACqF,GAAG,EAAE;IAChB,IAAIxI,cAAc,CAAC8I,GAAG,CAAC,EAAE;MACrB,OAAO,IAAI,CAACpF,SAAS,CAAC8D,OAAO,CAACsB,GAAG,CAACrD,KAAK,EAAEqD,GAAG,CAACF,QAAQ,CAAC;IAC1D,CAAC,MAAM;MACHlJ,yBAAyB,CAAC,IAAI,CAACkE,aAAa,EAAEkF,GAAG,CAAC;IACtD;IACA,OAAOA,GAAG;EACd;EAEQzB,aAAaA,CAACP,OAAwB;IAC1C,IAAI,CAAC,IAAI,CAACxD,aAAa,CAACyF,GAAG,CAACjC,OAAO,CAAC,EAAE;MAClC,MAAMK,UAAU,GAAGxH,kBAAkB,CAACmH,OAAO,EAAEzH,YAAY,CAAC;MAC5D,IAAI,CAACiE,aAAa,CAACiB,GAAG,CAACuC,OAAO,EAAE;QAC5BK,UAAU,EAAEA,UAAU;QACtBC,UAAU,EAAED,UAAU,GAAG7H,gBAAgB,CAAC6H,UAAU,CAAC6B,QAAQ,CAAC,GAAG;OACpE,CAAC;IACN;IACA,OAAO,IAAI,CAAC1F,aAAa,CAACb,GAAG,CAACqE,OAAO,CAAE;EAC3C;EAEQtF,MAAMA,CAACiG,QAAgB,EAAEX,OAAe,EAAErB,KAAc,EAAEuC,OAAgB,EAAEZ,UAAmB;IACnG,MAAM0B,GAAG,GAAG,IAAI,CAAC5F,OAAO;IACxB,IAAI+F,IAAa;IACjB,IAAI7B,UAAU,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;MACzCwD,IAAI,GAAG,IAAI,CAAC1F,MAAM,CAAC2F,cAAc,CAACJ,GAAG,EAAEhC,OAAO,EAAEkB,OAAO,EAAEvC,KAAK,CAAC;IACnE,CAAC,MAAM;MACHwD,IAAI,GAAGxD,KAAK;IAChB;IACA,QAAQgC,QAAQ;MACZ,KAAK,GAAG;QAAE;UACNqB,GAAG,CAAChC,OAAO,CAAC,GAAGmC,IAAI;UACnB;QACJ;MACA,KAAK,IAAI;QAAE;UACPH,GAAG,CAAChC,OAAO,CAAC,GAAG,IAAI;UACnB;QACJ;MACA,KAAK,IAAI;QAAE;UACP,IAAI,CAACqC,KAAK,CAACC,OAAO,CAACN,GAAG,CAAChC,OAAO,CAAC,CAAC,EAAE;YAC9BgC,GAAG,CAAChC,OAAO,CAAC,GAAG,EAAE;UACrB;UACAgC,GAAG,CAAChC,OAAO,CAAC,CAACZ,IAAI,CAAC+C,IAAI,CAAC;QAC3B;IACJ;EACJ;EAEQX,qBAAqBA,CAACe,MAAW,EAAEC,MAAW;IAClD,KAAK,MAAM,CAACjJ,IAAI,EAAEkJ,aAAa,CAAC,IAAIhI,MAAM,CAACiI,OAAO,CAACF,MAAM,CAAC,EAAE;MACxD,MAAMG,QAAQ,GAAGJ,MAAM,CAAChJ,IAAI,CAAC;MAC7B,IAAIoJ,QAAQ,KAAK9E,SAAS,EAAE;QACxB0E,MAAM,CAAChJ,IAAI,CAAC,GAAGkJ,aAAa;MAChC,CAAC,MAAM,IAAIJ,KAAK,CAACC,OAAO,CAACK,QAAQ,CAAC,IAAIN,KAAK,CAACC,OAAO,CAACG,aAAa,CAAC,EAAE;QAChEA,aAAa,CAACrD,IAAI,CAAC,GAAGuD,QAAQ,CAAC;QAC/BJ,MAAM,CAAChJ,IAAI,CAAC,GAAGkJ,aAAa;MAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,MAAMG,aAAa,GAAGL,MAAM,CAACT,QAAQ;IACrC,IAAIc,aAAa,EAAE;MACfA,aAAa,CAACC,OAAO,GAAGhF,SAAS;MACjC0E,MAAM,CAACT,QAAQ,GAAGjE,SAAS;IAC/B;IACA,OAAO0E,MAAM;EACjB;EAEA,IAAIO,gBAAgBA,CAAA;IAChB,OAAO,IAAI,CAACvI,OAAO,CAACuI,gBAAgB;EACxC;;AASJ,OAAM,MAAgBC,kCAAkC;EAEpDC,yBAAyBA,CAAC/E,OAKzB;IACG,OAAO9F,0BAA0B,CAAC6K,yBAAyB,CAAC/E,OAAO,CAAC;EACxE;EAEAgF,6BAA6BA,CAAChF,OAG7B;IACG,OAAO9F,0BAA0B,CAAC8K,6BAA6B,CAAChF,OAAO,CAAC;EAC5E;EAEAiF,uBAAuBA,CAACjF,OAMvB;IACG,OAAO9F,0BAA0B,CAAC+K,uBAAuB,CAACjF,OAAO,CAAC;EACtE;EAEAkF,qBAAqBA,CAAClF,OAMrB;IACG,OAAO9F,0BAA0B,CAACgL,qBAAqB,CAAClF,OAAO,CAAC;EACpE;;AAIJ,OAAM,MAAOmF,iCAAkC,SAAQL,kCAAkC;EAE5EC,yBAAyBA,CAAC;IAAEK,QAAQ;IAAEC;EAAM,CAKpD;IACG,MAAMC,WAAW,GAAGF,QAAQ,CAACG,KAAK,GAC5B,GAAG,GAAGH,QAAQ,CAACG,KAAK,GAAG,GAAG,GAC1BH,QAAQ,CAAC9J,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,GACzB,YAAY6J,QAAQ,CAAC9J,IAAI,CAACkK,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC9J,IAAI,CAAC+C,MAAM,GAAG,CAAC,CAAC,GAAG,GACnE,kBAAkB+G,QAAQ,CAAC9J,IAAI,GAAG;IAC5C,OAAO,aAAagK,WAAW,gBAAgBD,MAAM,CAAC7C,KAAK,KAAK;EACpE;EAESwC,6BAA6BA,CAAC;IAAES;EAAc,CAGtD;IACG,OAAO,qCAAqCA,cAAc,CAACjD,KAAK,KAAK;EACzE;;AASJ,OAAM,MAAOkD,uBAAwB,SAAQlK,qBAAqB;EAAlEC,YAAA;;IACY,KAAAQ,MAAM,GAAa,EAAE;IAErB,KAAA0J,YAAY,GAAsB,EAAE;IACpC,KAAAC,gBAAgB,GAAsB,EAAE;IACxC,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,SAAS,GAAG,CAAC;EAmGzB;EAjGIpC,MAAMA,CAAA;IACF;EAAA;EAGJrC,SAASA,CAAA;IACL;IACA,OAAOzB,SAAS;EACpB;EAEAE,KAAKA,CAACC,KAAa;IACf,IAAI,CAACgG,UAAU,EAAE;IACjB,MAAM9J,MAAM,GAAG,IAAI,CAACH,KAAK,CAACqE,QAAQ,CAACJ,KAAK,EAAE;MAAE1D,IAAI,EAAE;IAAS,CAAE,CAAC;IAC9D,IAAI,CAACJ,MAAM,GAAGA,MAAM,CAACA,MAAM;IAC3B,IAAI,CAACK,OAAO,CAACyD,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC9D,MAAM,CAAC;IACrC,IAAI,CAACyD,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAChE,OAAO,EAAE,EAAE,CAAC;IACpC,IAAI,CAACuB,eAAe,CAAC4C,KAAK,EAAE;IAC5B,OAAO;MACHxE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB0J,YAAY,EAAE,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAAC;MACxCI,UAAU,EAAE,IAAI,CAACH;KACpB;EACL;EAEA7G,IAAIA,CAACA,IAAgB,EAAEC,IAAc;IACjC,MAAMG,UAAU,GAAG,IAAI,CAAC9C,OAAO,CAAC+C,WAAW,CAAChE,cAAc,CAAC2D,IAAI,CAAC1D,IAAI,CAAC,EAAE,IAAI,CAACgE,mBAAmB,CAACL,IAAI,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;IACjH,IAAI,CAAC1D,QAAQ,CAAC2D,GAAG,CAACR,IAAI,CAAC1D,IAAI,EAAE8D,UAAU,CAAC;IACxC,IAAIJ,IAAI,CAACS,KAAK,EAAE;MACZ,IAAI,CAACC,QAAQ,GAAGN,UAAU;IAC9B;IACA,OAAOA,UAAU;EACrB;EAEQ2G,UAAUA,CAAA;IACd,IAAI,CAACJ,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;EAEQxG,mBAAmBA,CAAC0B,cAAwB;IAChD,OAAQC,IAAI,IAAI;MACZ,MAAMgF,IAAI,GAAG,IAAI,CAACC,aAAa,EAAE;MACjC,IAAI;QACAlF,cAAc,CAACC,IAAI,CAAC;MACxB,CAAC,SAAS;QACN,IAAI,CAACkF,cAAc,CAACF,IAAI,CAAC;MAC7B;IACJ,CAAC;EACL;EAEQG,wBAAwBA,CAAA;IAC5B,IAAI,CAACT,YAAY,CAAC/D,MAAM,CAAC,IAAI,CAACkE,SAAS,CAAC;EAC5C;EAEAI,aAAaA,CAAA;IACT,MAAMD,IAAI,GAAG,IAAI,CAACN,YAAY,CAACtH,MAAM;IACrC,IAAI,CAACyH,SAAS,GAAGG,IAAI;IACrB,OAAOA,IAAI;EACf;EAEAE,cAAcA,CAACF,IAAY;IACvB,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACN,SAAS,GAAGG,IAAI;EACzB;EAEApE,OAAOA,CAAC9E,GAAW,EAAE+E,SAAoB,EAAEC,OAAwB;IAC/D,IAAI,CAACzF,OAAO,CAAC0F,WAAW,CAACjF,GAAG,EAAE+E,SAAS,CAAC;IACxC,IAAI,CAAC,IAAI,CAACnE,WAAW,EAAE,EAAE;MACrB,IAAI,CAACiI,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,YAAY,EAAE5D,OAAO,CAAC;MACvD,IAAI,CAAC8D,cAAc,GAAG,IAAI,CAACQ,OAAO,GAAG,CAAC;IAC1C;EACJ;EAEArD,OAAOA,CAACjG,GAAW,EAAEiC,IAAgB,EAAEW,QAAiB,EAAEoC,OAAwB,EAAEd,IAAU;IAC1F,IAAI,CAACqF,MAAM,CAACvE,OAAO,CAAC;IACpB,IAAI,CAACzF,OAAO,CAAC8G,WAAW,CAACrG,GAAG,EAAEiC,IAAI,EAAEiC,IAAI,CAAC;IACzC,IAAI,CAACsF,KAAK,CAACxE,OAAO,CAAC;EACvB;EAEAuE,MAAMA,CAACE,OAAwB;IAC3B,IAAI,CAAC,IAAI,CAAC7I,WAAW,EAAE,EAAE;MACrB,IAAI,CAACgI,YAAY,CAACxE,IAAI,CAACqF,OAAO,CAAC;IACnC;EACJ;EAEAD,KAAKA,CAACC,OAAwB;IAC1B,IAAI,CAAC,IAAI,CAAC7I,WAAW,EAAE,EAAE;MACrB,MAAM8I,KAAK,GAAG,IAAI,CAACd,YAAY,CAACe,WAAW,CAACF,OAAO,CAAC;MACpD,IAAIC,KAAK,IAAI,CAAC,EAAE;QACZ,IAAI,CAACd,YAAY,CAAC/D,MAAM,CAAC6E,KAAK,CAAC;MACnC;IACJ;EACJ;EAEA,IAAIJ,OAAOA,CAAA;IACP,OAAQ,IAAI,CAAC/J,OAAe,CAAC+J,OAAO;EACxC;;AAGJ,MAAMM,aAAa,GAAkB;EACjCC,eAAe,EAAE,IAAI;EACrBC,oBAAoB,EAAE,MAAM;EAC5BlK,eAAe,EAAE,IAAI;EACrBC,oBAAoB,EAAE,IAAIuI,iCAAiC;CAC9D;AAED;;;;AAIA,MAAM5I,iBAAkB,SAAQpC,qBAAqB;EAKjDsB,YAAYQ,MAAuB,EAAE6K,MAAqB;IACtD,MAAMC,mBAAmB,GAAGD,MAAM,IAAI,cAAc,IAAIA,MAAM;IAC9D,KAAK,CAAC7K,MAAM,EAAAO,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACLkK,aAAa;MAChBK,iBAAiB,EAAED,mBAAmB,GAChC,IAAI3M,oBAAoB,CAAC;QAAE6M,YAAY,EAAEH,MAAM,CAACG;MAAY,CAAE,CAAC,GAC/D,IAAI5M,uBAAuB,CAAC;QAC1B;QACA6M,OAAO,EAAEJ,MAAM,CAACnK,eAAe,GAAG,MAAK,CAAG,CAAC,GAAGiD;OACjD;IAAC,IACHkH,MAAM,EACX;EACN;EAEA,IAAIlJ,YAAYA,CAAA;IACZ,OAAO,IAAI,CAACuJ,eAAe;EAC/B;EAEA9H,WAAWA,CAAC/D,IAAY,EAAE2D,IAAc;IACpC,OAAO,IAAI,CAACmI,IAAI,CAAC9L,IAAI,EAAE2D,IAAI,CAAC;EAChC;EAEAhB,gBAAgBA,CAAA;IACZ,IAAI,CAACoJ,mBAAmB,EAAE;EAC9B;EAEArF,WAAWA,CAACjF,GAAW,EAAE+E,SAAoB;IACzC,OAAO,IAAI,CAACD,OAAO,CAAC9E,GAAG,EAAE+E,SAAS,CAAC;EACvC;EAEAsB,WAAWA,CAACrG,GAAW,EAAEiC,IAAgB,EAAEiC,IAAU;IACjD,OAAO,IAAI,CAAC+B,OAAO,CAACjG,GAAG,EAAEiC,IAAI,EAAE;MAC3BsI,IAAI,EAAE,CAACrG,IAAI;KACd,CAAC;EACN;EAEAhE,MAAMA,CAACF,GAAW,EAAEC,OAA2B;IAC3C,IAAI,CAACuK,EAAE,CAACxK,GAAG,EAAEC,OAAO,CAAC;EACzB;EAEAI,UAAUA,CAACL,GAAW,EAAEI,QAAgC;IACpD,IAAI,CAACqK,MAAM,CAACzK,GAAG,EAAEI,QAAQ,CAAC;EAC9B;EAEAG,QAAQA,CAACP,GAAW,EAAEI,QAAgC;IAClD,IAAI,CAACE,IAAI,CAACN,GAAG,EAAEI,QAAQ,CAAC;EAC5B;EAEAK,cAAcA,CAACT,GAAW,EAAEI,QAAgC;IACxD,IAAI,CAACI,UAAU,CAACR,GAAG,EAAEI,QAAQ,CAAC;EAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}