{"ast": null, "code": "\"use strict\";\n\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nlet _ral;\nfunction RAL() {\n  if (_ral === undefined) {\n    throw new Error(`No runtime abstraction layer installed`);\n  }\n  return _ral;\n}\n(function (RAL) {\n  function install(ral) {\n    if (ral === undefined) {\n      throw new Error(`No runtime abstraction layer provided`);\n    }\n    _ral = ral;\n  }\n  RAL.install = install;\n})(RAL || (RAL = {}));\nexports.default = RAL;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_ral", "RAL", "undefined", "Error", "install", "ral", "default"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/vscode-jsonrpc/lib/common/ral.js"], "sourcesContent": ["\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nlet _ral;\nfunction RAL() {\n    if (_ral === undefined) {\n        throw new Error(`No runtime abstraction layer installed`);\n    }\n    return _ral;\n}\n(function (RAL) {\n    function install(ral) {\n        if (ral === undefined) {\n            throw new Error(`No runtime abstraction layer provided`);\n        }\n        _ral = ral;\n    }\n    RAL.install = install;\n})(RAL || (RAL = {}));\nexports.default = RAL;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACAA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,IAAIC,IAAI;AACR,SAASC,GAAGA,CAAA,EAAG;EACX,IAAID,IAAI,KAAKE,SAAS,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC7D;EACA,OAAOH,IAAI;AACf;AACA,CAAC,UAAUC,GAAG,EAAE;EACZ,SAASG,OAAOA,CAACC,GAAG,EAAE;IAClB,IAAIA,GAAG,KAAKH,SAAS,EAAE;MACnB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;IAC5D;IACAH,IAAI,GAAGK,GAAG;EACd;EACAJ,GAAG,CAACG,OAAO,GAAGA,OAAO;AACzB,CAAC,EAAEH,GAAG,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrBH,OAAO,CAACQ,OAAO,GAAGL,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}