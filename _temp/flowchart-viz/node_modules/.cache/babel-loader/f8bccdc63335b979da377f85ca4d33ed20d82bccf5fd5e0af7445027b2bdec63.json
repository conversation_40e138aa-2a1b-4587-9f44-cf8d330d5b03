{"ast": null, "code": "import { has } from \"lodash-es\";\nimport { timer } from \"@chevrotain/utils\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n/**\n * Trait responsible for runtime parsing errors.\n */\nexport class PerformanceTracer {\n  initPerformanceTracer(config) {\n    if (has(config, \"traceInitPerf\")) {\n      const userTraceInitPerf = config.traceInitPerf;\n      const traceIsNumber = typeof userTraceInitPerf === \"number\";\n      this.traceInitMaxIdent = traceIsNumber ? userTraceInitPerf : Infinity;\n      this.traceInitPerf = traceIsNumber ? userTraceInitPerf > 0 : userTraceInitPerf; // assumes end user provides the correct config value/type\n    } else {\n      this.traceInitMaxIdent = 0;\n      this.traceInitPerf = DEFAULT_PARSER_CONFIG.traceInitPerf;\n    }\n    this.traceInitIndent = -1;\n  }\n  TRACE_INIT(phaseDesc, phaseImpl) {\n    // No need to optimize this using NOOP pattern because\n    // It is not called in a hot spot...\n    if (this.traceInitPerf === true) {\n      this.traceInitIndent++;\n      const indent = new Array(this.traceInitIndent + 1).join(\"\\t\");\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        console.log(`${indent}--> <${phaseDesc}>`);\n      }\n      const {\n        time,\n        value\n      } = timer(phaseImpl);\n      /* istanbul ignore next - Difficult to reproduce specific performance behavior (>10ms) in tests */\n      const traceMethod = time > 10 ? console.warn : console.log;\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        traceMethod(`${indent}<-- <${phaseDesc}> time: ${time}ms`);\n      }\n      this.traceInitIndent--;\n      return value;\n    } else {\n      return phaseImpl();\n    }\n  }\n}", "map": {"version": 3, "names": ["has", "timer", "DEFAULT_PARSER_CONFIG", "PerformanceTracer", "initPerformanceTracer", "config", "userTraceInitPerf", "traceInitPerf", "traceIsNumber", "traceInitMaxIdent", "Infinity", "traceInitIndent", "TRACE_INIT", "phaseDesc", "phaseImpl", "indent", "Array", "join", "console", "log", "time", "value", "traceMethod", "warn"], "sources": ["/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/flowchart-viz/node_modules/chevrotain/src/parse/parser/traits/perf_tracer.ts"], "sourcesContent": ["import { IParserConfig } from \"@chevrotain/types\";\nimport { has } from \"lodash-es\";\nimport { timer } from \"@chevrotain/utils\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n\n/**\n * Trait responsible for runtime parsing errors.\n */\nexport class PerformanceTracer {\n  traceInitPerf: boolean | number;\n  traceInitMaxIdent: number;\n  traceInitIndent: number;\n\n  initPerformanceTracer(config: IParserConfig) {\n    if (has(config, \"traceInitPerf\")) {\n      const userTraceInitPerf = config.traceInitPerf;\n      const traceIsNumber = typeof userTraceInitPerf === \"number\";\n      this.traceInitMaxIdent = traceIsNumber\n        ? <number>userTraceInitPerf\n        : Infinity;\n      this.traceInitPerf = traceIsNumber\n        ? userTraceInitPerf > 0\n        : (userTraceInitPerf as boolean); // assumes end user provides the correct config value/type\n    } else {\n      this.traceInitMaxIdent = 0;\n      this.traceInitPerf = DEFAULT_PARSER_CONFIG.traceInitPerf;\n    }\n\n    this.traceInitIndent = -1;\n  }\n\n  TRACE_INIT<T>(this: MixedInParser, phaseDesc: string, phaseImpl: () => T): T {\n    // No need to optimize this using NOOP pattern because\n    // It is not called in a hot spot...\n    if (this.traceInitPerf === true) {\n      this.traceInitIndent++;\n      const indent = new Array(this.traceInitIndent + 1).join(\"\\t\");\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        console.log(`${indent}--> <${phaseDesc}>`);\n      }\n      const { time, value } = timer(phaseImpl);\n      /* istanbul ignore next - Difficult to reproduce specific performance behavior (>10ms) in tests */\n      const traceMethod = time > 10 ? console.warn : console.log;\n      if (this.traceInitIndent < this.traceInitMaxIdent) {\n        traceMethod(`${indent}<-- <${phaseDesc}> time: ${time}ms`);\n      }\n      this.traceInitIndent--;\n      return value;\n    } else {\n      return phaseImpl();\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,KAAK,QAAQ,mBAAmB;AAEzC,SAASC,qBAAqB,QAAQ,cAAc;AAEpD;;;AAGA,OAAM,MAAOC,iBAAiB;EAK5BC,qBAAqBA,CAACC,MAAqB;IACzC,IAAIL,GAAG,CAACK,MAAM,EAAE,eAAe,CAAC,EAAE;MAChC,MAAMC,iBAAiB,GAAGD,MAAM,CAACE,aAAa;MAC9C,MAAMC,aAAa,GAAG,OAAOF,iBAAiB,KAAK,QAAQ;MAC3D,IAAI,CAACG,iBAAiB,GAAGD,aAAa,GAC1BF,iBAAiB,GACzBI,QAAQ;MACZ,IAAI,CAACH,aAAa,GAAGC,aAAa,GAC9BF,iBAAiB,GAAG,CAAC,GACpBA,iBAA6B,CAAC,CAAC;KACrC,MAAM;MACL,IAAI,CAACG,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACF,aAAa,GAAGL,qBAAqB,CAACK,aAAa;;IAG1D,IAAI,CAACI,eAAe,GAAG,CAAC,CAAC;EAC3B;EAEAC,UAAUA,CAAyBC,SAAiB,EAAEC,SAAkB;IACtE;IACA;IACA,IAAI,IAAI,CAACP,aAAa,KAAK,IAAI,EAAE;MAC/B,IAAI,CAACI,eAAe,EAAE;MACtB,MAAMI,MAAM,GAAG,IAAIC,KAAK,CAAC,IAAI,CAACL,eAAe,GAAG,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MAC7D,IAAI,IAAI,CAACN,eAAe,GAAG,IAAI,CAACF,iBAAiB,EAAE;QACjDS,OAAO,CAACC,GAAG,CAAC,GAAGJ,MAAM,QAAQF,SAAS,GAAG,CAAC;;MAE5C,MAAM;QAAEO,IAAI;QAAEC;MAAK,CAAE,GAAGpB,KAAK,CAACa,SAAS,CAAC;MACxC;MACA,MAAMQ,WAAW,GAAGF,IAAI,GAAG,EAAE,GAAGF,OAAO,CAACK,IAAI,GAAGL,OAAO,CAACC,GAAG;MAC1D,IAAI,IAAI,CAACR,eAAe,GAAG,IAAI,CAACF,iBAAiB,EAAE;QACjDa,WAAW,CAAC,GAAGP,MAAM,QAAQF,SAAS,WAAWO,IAAI,IAAI,CAAC;;MAE5D,IAAI,CAACT,eAAe,EAAE;MACtB,OAAOU,KAAK;KACb,MAAM;MACL,OAAOP,SAAS,EAAE;;EAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}